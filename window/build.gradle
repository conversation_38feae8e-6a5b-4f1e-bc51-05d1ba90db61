apply plugin: 'com.android.library'

android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 14
        versionCode 90
        versionName "9.0"
    }

    // 支持 JDK 1.8
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }

    android.libraryVariants.all { variant ->
        // aar 输出文件名配置
        variant.outputs.all { output ->
            outputFileName = "${rootProject.name}-${android.defaultConfig.versionName}.aar"
        }
    }
}

afterEvaluate {
    // 排除 BuildConfig.class 和 R.class
    generateReleaseBuildConfig.enabled = false
    generateDebugBuildConfig.enabled = false
    generateReleaseResValues.enabled = false
    generateDebugResValues.enabled = false
}

tasks.withType(Javadoc) {
    options.addStringOption('Xdoclint:none', '-quiet')
    options.addStringOption('encoding', 'UTF-8')
    options.addStringOption('charSet', 'UTF-8')
}

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier = 'sources'
}

task javadoc(type: Javadoc) {
    source = android.sourceSets.main.java.srcDirs
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
}

task javadocJar(type: Jar, dependsOn: javadoc) {
    classifier = 'javadoc'
    from javadoc.destinationDir
}

artifacts {
    archives javadocJar
    archives sourcesJar
}