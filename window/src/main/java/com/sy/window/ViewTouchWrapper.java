package com.sy.window;

import android.annotation.SuppressLint;
import android.view.MotionEvent;
import android.view.View;

/**
 *    author : 黄锦群
 *    time   : 2023/03/06
 *    desc   : {@link View.OnTouchListener} 包装类
 */
@SuppressWarnings("rawtypes")
final class ViewTouchWrapper implements View.OnTouchListener {

    private final WindowX<?> mWindow;
    private final WindowX.OnTouchListener mListener;

    ViewTouchWrapper(WindowX<?> window, WindowX.OnTouchListener listener) {
        mWindow = window;
        mListener = listener;
    }

    @SuppressLint("ClickableViewAccessibility")
    @SuppressWarnings("unchecked")
    @Override
    public boolean onTouch(View view, MotionEvent event) {
        if (mListener == null) {
            return false;
        }
        return mListener.onTouch(mWindow, view, event);
    }
}