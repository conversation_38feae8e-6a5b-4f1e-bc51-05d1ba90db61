package com.sy.window;

import android.view.View;

/**
 *    author : 黄锦群
 *    time   : 2023/03/06
 *    desc   : {@link View.OnLongClickListener} 包装类
 */
@SuppressWarnings("rawtypes")
final class ViewLongClickWrapper implements View.OnLongClickListener {

    private final WindowX<?> mWindow;
    private final WindowX.OnLongClickListener mListener;

    ViewLongClickWrapper(WindowX<?> window, WindowX.OnLongClickListener listener) {
        mWindow = window;
        mListener = listener;
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean onLongClick(View view) {
        if (mListener == null) {
            return false;
        }
        return mListener.onLongClick(mWindow, view);
    }
}