package com.sy.window;

import android.app.Activity;
import android.app.Application;
import android.os.Build;
import android.os.Bundle;

/**
 *    author : 黄锦群
 *    time   : 2023/03/06
 *    desc   : 悬浮窗生命周期管理，防止内存泄露
 */
final class ActivityLifecycle implements Application.ActivityLifecycleCallbacks {

    private Activity mActivity;
    private WindowX<?> mWindow;

    ActivityLifecycle(WindowX<?> window, Activity activity) {
        mActivity = activity;
        mWindow = window;
    }

    /**
     * 注册监听
     */
    void register() {
        if (mActivity == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mActivity.registerActivityLifecycleCallbacks(this);
        } else {
            mActivity.getApplication().registerActivityLifecycleCallbacks(this);
        }
    }

    /**
     * 取消监听
     */
    void unregister() {
        if (mActivity == null) {
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mActivity.unregisterActivityLifecycleCallbacks(this);
        } else {
            mActivity.getApplication().unregisterActivityLifecycleCallbacks(this);
        }
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        // default implementation ignored
    }

    @Override
    public void onActivityStarted(Activity activity) {
        // default implementation ignored
    }

    @Override
    public void onActivityResumed(Activity activity) {
        // default implementation ignored
    }

    @Override
    public void onActivityPaused(Activity activity) {
        // 一定要在 onPaused 方法中销毁掉，如果放在 onDestroyed 方法中还是有一定几率会导致内存泄露
        if (mActivity != activity || !mActivity.isFinishing() || mWindow == null || !mWindow.isShowing()) {
            return;
        }
        mWindow.cancel();
    }

    @Override
    public void onActivityStopped(Activity activity) {
        // default implementation ignored
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        // default implementation ignored
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        if (mActivity != activity) {
            return;
        }
        // 释放 Activity 的引用
        mActivity = null;

        if (mWindow == null) {
            return;
        }
        mWindow.recycle();
        mWindow = null;
    }
}