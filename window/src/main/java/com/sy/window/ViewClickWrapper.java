package com.sy.window;

import android.view.View;

/**
 *    author : 黄锦群
 *    time   : 2023/03/06
 *    desc   : {@link View.OnClickListener} 包装类
 */
@SuppressWarnings("rawtypes")
final class ViewClickWrapper implements View.OnClickListener {

    private final WindowX<?> mWindow;
    private final WindowX.OnClickListener mListener;

    ViewClickWrapper(WindowX<?> window, WindowX.OnClickListener listener) {
        mWindow = window;
        mListener = listener;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onClick(View view) {
        if (mListener == null) {
            return;
        }
        mListener.onClick(mWindow, view);
    }
}