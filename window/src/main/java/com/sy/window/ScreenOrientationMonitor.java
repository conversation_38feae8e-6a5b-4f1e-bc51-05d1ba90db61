package com.sy.window;

import android.content.ComponentCallbacks;
import android.content.Context;
import android.content.res.Configuration;

/**
 *    author : 黄锦群
 *    time   : 2023/03/06
 *    desc   : 屏幕方向旋转监听
 */
final class ScreenOrientationMonitor implements ComponentCallbacks {

   /** 当前屏幕的方向 */
   private int mScreenOrientation;

   /** 上下文对象 */
   private Context mContext;

   /** 屏幕旋转回调 */
   private OnScreenOrientationCallback mCallback;

   public ScreenOrientationMonitor(Configuration configuration) {
      mScreenOrientation = configuration.orientation;
   }

   /**
    * 注册监听
    */
   void registerCallback(Context context, OnScreenOrientationCallback callback) {
      context.registerComponentCallbacks(this);
      mCallback = callback;
      mContext = context;
   }

   /**
    * 取消监听
    */
   void unregisterCallback(Context context) {
      context.unregisterComponentCallbacks(this);
      mCallback = null;
      mContext = null;
   }

   @Override
   public void onConfigurationChanged(Configuration newConfig) {
      // 为什么不用 newConfig.orientation，而是用 mContext.getResources().getConfiguration().orientation？
      // 这是因为如果是 Activity 自旋转的情况下，那么一切正常，如果是用户从横屏的 Activity 切换到竖版的 Activity
      // 那么就会导致回调这个监听，但是实际上面如果是附着在 Activity 上面的 Window，压根就不需要关心用户切换到桌面是横屏的还是竖屏
      // 所以不能拿 newConfig.orientation 来做判断，而是应该拿原本 Context 对象来做比较
      if (mScreenOrientation == mContext.getResources().getConfiguration().orientation) {
         return;
      }
      mScreenOrientation = mContext.getResources().getConfiguration().orientation;

      if (mCallback == null) {
         return;
      }
      mCallback.onScreenOrientationChange(mScreenOrientation);
   }

   @Override
   public void onLowMemory() {
      // default implementation ignored
   }

   /**
    * 屏幕方向监听器
    */
   interface OnScreenOrientationCallback {

      /**
       * 监听屏幕旋转了
       *
       * @param newOrientation         最新的屏幕方向
       */
      default void onScreenOrientationChange(int newOrientation) {}
   }
}