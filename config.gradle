import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.sq.gradle.fix.common.FileUtil

import java.text.SimpleDateFormat

apply from: 'common.gradle'
apply from: 'multi.gradle'

ext {
    /**
     * common
     */

    getCurrentTime = this.&getCurrentTime
    getSigningFile = this.&getSigningFile
    rootProjectPath = {
        rootProject.getProjectDir().getAbsolutePath()
    }

    /**
     * common.gradle wrapper
     */
    get_isMModuleDebug = this.&get_isMModuleDebug
    get_isPluginMode = this.&get_isPluginMode
    get_isMModuleRelease = this.&get_isMModuleRelease
    get_libraryVersion = this.&get_libraryVersion
    get_isMultiAAR = this.&get_isMultiAAR
    get_isPluginHostAar = this.&get_isPluginHostAar
}

def get_cmb_build_sdk(){
    return cmb_build_sdk
}
def get_config_build_sdk(){
    def build_sdk_config_json = "build_sdk_config.json"
    return new File(getRootDir(),build_sdk_config_json).getAbsolutePath()
}
def get_isMModuleDebug() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        return buildConfigBean.isMModuleDebug
    }else{
        return rootProject.ext.isMModuleDebug
    }
}

def get_isPluginMode() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        return buildConfigBean.isPluginMode
    }else{
        return rootProject.ext.isPluginMode
    }
}

def get_isMModuleRelease() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        return buildConfigBean.isMModuleRelease
    }else{
        return rootProject.ext.isMModuleRelease
    }
}

def get_libraryVersion() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        if(buildConfigBean.libraryVersion==""){
            return rootProject.ext.libraryVersion
        }
        return buildConfigBean.libraryVersion
    }else{
        return rootProject.ext.libraryVersion
    }
}
def get_isMultiAAR() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        return buildConfigBean.isMultiAAR
    }else{
        return rootProject.ext.isMultiAAR
    }
}

def get_isPluginHostAar() {
    BuildConfigBean buildConfigBean = BuildConfigBeanHandler.getBuildConfigBean(get_config_build_sdk(),get_cmb_build_sdk())
    if(buildConfigBean!=null){
        return buildConfigBean.isPluginHostAar
    }else{
        return rootProject.ext.isPluginHostAar
    }
}

//获取当前时间
def getCurrentTime() {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss")
    dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"))
    return dateFormat.format(new Date())
}
//获取签名信息
def getSigningFile() {
    return file('./BuildSystem/sq_signing.properties')
}


def isLiveshowTypeNone() {
    return checkLiveshowType("none")
}

def isLiveshowTypeAudio() {
    if (isLiveshowTypeNone()) {
        return false
    }
    return checkLiveshowType("audio")
}

def isLiveshowTypeHy() {
    if (isLiveshowTypeNone()) {
        return false
    }
    return checkLiveshowType("hy")
}

def checkLiveshowType(def type) {
    return type == rootProject.ext.liveshowType
}

class BuildConfigBean{
    String cmd = ""
    boolean isMModuleDebug=false
    boolean isPluginMode=false
    boolean isSQSkin=true
    boolean isMModuleRelease=false
    boolean isMultiAAR=false
    String libraryVersion=""
    boolean isPluginHostAar=true
    boolean isPluginHostApk=false
    String sdkType="sq"

    BuildConfigBean() {
    }


    @Override
    public String toString() {
        return "BuildConfigBean{" +
                "cmd='" + cmd + '\'' +
                ", isMModuleDebug=" + isMModuleDebug +
                ", isPluginMode=" + isPluginMode +
                ", isSQSkin=" + isSQSkin +
                ", isMModuleRelease=" + isMModuleRelease +
                ", isMultiAAR=" + isMultiAAR +
                ", libraryVersion='" + libraryVersion + '\'' +
                ", isPluginHostAar=" + isPluginHostAar +
                ", isPluginHostApk=" + isPluginHostApk +
                ", sdkType='" + sdkType + '\'' +
                '}';
    }
}
class BuildConfigBeanHandler{
    static Map<String,BuildConfigBean> buildConfigBeanMap
    static BuildConfigBean getBuildConfigBean(String jsonPath,String cmd){
        println("getBuildConfigBean cmd:$cmd")

        if("none"==cmd){
            println("return")
            return null
        }

        if(buildConfigBeanMap==null){
            String json = FileUtil.read(jsonPath)
            buildConfigBeanMap= new HashMap<>()
            List<BuildConfigBean> buildConfigBeanList = new Gson().fromJson(json,new TypeToken<List<BuildConfigBean>>() {}.getType())
            buildConfigBeanList.forEach{
                buildConfigBeanMap.put(it.cmd,it)
            }
            println("size:${buildConfigBeanMap.size()}")

        }
        BuildConfigBean buildConfigBean =  buildConfigBeanMap.get(cmd)
        if(buildConfigBean==null){
            throw Exception("BuildConfigBeanHandler 配置异常")
        }
        println("buildConfigBean:${buildConfigBean.toString()}")
        return buildConfigBean
    }
}




