if (isHyLiveShow) {
    android {
        defaultConfig {
            ndk {
//                abiFilters "armeabi-v7a,arm64-v8a"
                setAbiFilters(['armeabi-v7a', 'arm64-v8a', 'x86'])
            }
        }
        packagingOptions {
            pickFirst 'lib/armeabi-v7a/libhyaudioengine.so'
            pickFirst 'lib/arm64-v8a/libhyaudioengine.so'
            pickFirst 'lib/x86/libhyaudioengine.so'
        }
        dexOptions {
            javaMaxHeapSize "2g"
            preDexLibraries = true
            dexInProcess = true
        }
    }


}


