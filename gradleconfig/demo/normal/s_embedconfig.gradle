dependencies {
    embed project(path: ':module_business:order', configuration: 'default')
    embed project(path: ':module_business:account', configuration: 'default')
    embed project(path: ':module_business:share', configuration: 'default')
    embed project(path: ':37sdkcommon', configuration: 'default')
    embed project(path: ':37SDKLibrarys', configuration: 'default')
    embed project(path: ':notchtools', configuration: 'default')
    embed project(path: ':module_business:sdk_plugin', configuration: 'default')
    embed project(path: ':module_ui:sq', configuration: 'default')
    embed project(path: ':module_ui:base_new', configuration: 'default')
    embed project(path: ':module_plugin:plugin_standard', configuration: 'default')
    embed project(path: ':websocket:websocket_engine', configuration: 'default')
    embed project(path: ':websocket:websocketlib', configuration: 'default')
    embed project(path: ':websocket:Java-WebSocket', configuration: 'default')
    embed 'org.slf4j:slf4j-api:1.7.25'
    embed(rootProject.ext.sqsdk.volly) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    embed project(path: ':gson', configuration: 'default')
    embed rootProject.ext.sqsdk.bugless
    embed 'com.37sy.android:tool:2.0.5'
    embed 'com.37sy.android:sqinject:1.0.0'
    embed 'com.37sy.android:sqeventbus:1.0.0'
    embed 'com.37sy.android:sqeventbus-annotation:1.0.0'
    embed 'com.37sy.android:social_sdk:2.0.8'
    // 第三方登录和分享
    embed project(path: ':module_third:social_sdk', configuration: 'default')
    embed project(path: ':module_third:ali_face:ali_face_sdk', configuration: 'default')

//宿主跟插件都需要的的资源
    embed project(':module_ui_common:base')
    embed project(":module_ui_common:sq")
}