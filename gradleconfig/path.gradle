ext{
    s_embendconfig = this.&s_embendconfig
    liveshow_config = this.&liveshow_config
    liveshow_embed_config = this.&liveshow_embed_config
    liveshow_so_config = this.&liveshow_so_config
    libconfig = this.&libconfig
}
def _pathRootDir(){
    return "${rootProjectPath()}/gradleconfig"
}
def s_embendconfig(){
    return "${_pathRootDir()}/demo/normal/s_embedconfig.gradle"
}
def liveshowconfig(){
    return "${rootProjectPath()}/moduleconfig"
}
def liveshow_config(){
    return "${liveshowconfig()}/hy_liveshow_config.gradle"
}
def liveshow_embed_config(){
    return "${liveshowconfig()}/hy_liveshow_embed_config.gradle"
}
def liveshow_so_config(){
    return "${liveshowconfig()}/hy_liveshow_so_config.gradle"
}

def libconfig(){
    return "${_pathRootDir()}/libconfig.gradle"
}
