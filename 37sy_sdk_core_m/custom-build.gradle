
def isWindowSystem() {
    String system = System.properties['os.name']
    return (system.contains("windows") || system.contains("Windows"))
}


task cleanRootProject(type: Exec) {
    group 'custom'
    workingDir '../'
    if (!isWindowSystem()) {
        commandLine './gradlew', 'clean'
    } else {
        commandLine 'cmd', '/c', 'gradlew :clean'
    }
}

task buildMModule(type: Exec) {
    group 'custom'
    workingDir '../'
    dependsOn cleanRootProject
    if (!isWindowSystem()) {
        commandLine './gradlew', ':37sy_sdk_core_m:build'
    } else {
        commandLine 'cmd', '/c', 'gradlew :37sy_sdk_core_m:build'
    }
}

// Task to delete old jar
task deleteOldJar(type: Delete) {
    group 'custom'
    delete "build/libs/37SDK_release_v${libraryVersion}.jar"
}

// task to export contents as jar
task exportJar(type: Copy) {
    group 'custom'
    dependsOn deleteOldJar
    from('build/intermediates/packaged-classes/release/')
    into('build/libs/')
    include('classes.jar')
    rename('classes.jar', "37SDK_release_v${libraryVersion}.jar")
}

task buildMRelease(type: Exec) {
    group 'custom'
    dependsOn cleanRootProject
    workingDir '../'
    if (!isWindowSystem()) {
        commandLine './gradlew', ':37sy_sdk_core_m:assemble'
    } else {
        commandLine 'cmd', '/c', 'gradlew :37sy_sdk_core_m:assemble'
    }
}