<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.sy.yxjun">


  <supports-screens
    android:anyDensity="true"
    android:largeScreens="true"
    android:normalScreens="true"
    android:smallScreens="true" />

  <!-- 易盾 SDK 需要的权限 -->
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission
    android:name="android.permission.WRITE_SETTINGS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

  <!-- sqwan Start -->

  <!-- 支付宝 权限 start -->
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <!-- 支付宝 权限 end -->

  <!--  V2.1新增，用于短信注册账号 -->
  <uses-permission android:name="android.permission.SEND_SMS" />

  <!--  target26 更新包需要 -->
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

  <!--必要权限，解决安全风险漏洞，发送和注册广播事件需要调用带有传递权限的接口-->
  <permission      android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN"
    android:protectionLevel="signature" />

  <uses-permission android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN" />

  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

  <!-- sqwan End -->
  <uses-permission
    android:name="android.permission.QUERY_ALL_PACKAGES"
    tools:ignore="QueryAllPackagesPermission" />

  <application
    android:name="com.sqwan.msdk.SQApplication"
    android:allowBackup="true"
    android:icon="@drawable/sy37_sdk_launcher"
    android:label="sq-union:3.7.7.8.2"
    android:usesCleartextTraffic="true"
    tools:replace="android:label,android:allowBackup">
    <activity
      android:name="com.sqwan.m.MainActivity"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:launchMode="singleTask"
      android:resizeableActivity="true"
      android:screenOrientation="landscape"
      android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen"
      android:windowSoftInputMode="adjustPan">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
        <action android:name="android.intent.action.VIEW" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.BROWSABLE" />
        <category android:name="android.intent.category.DEFAULT" />

        <data
          android:host="com.sy.yxjun"
          android:scheme="sq" />
      </intent-filter>
    </activity>

    <!--Weixin start-->
    <activity android:name="com.social.sdk.sso.wechat.WXCallbackActivity" />
    <activity-alias
      android:name="${applicationId}.wxapi.WXEntryActivity"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:exported="true"
      android:launchMode="singleTask"
      android:targetActivity="com.social.sdk.sso.wechat.WXCallbackActivity"
      android:taskAffinity="com.sy37.sdk.demo.diff"
      android:theme="@android:style/Theme.Translucent.NoTitleBar" />
    <activity
      android:name="com.social.sdk.sso.wechat.WXQRActivity"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
    <!--Weixin end-->

    <!-- 第三方登录和分享用到, 原本属于share/account的业务模块, 但是重构前产物有, 所以保留 -->
    <meta-data
      android:name="wx_appid"
      android:value="wx2d611131c6591acd" />
    <meta-data
      android:name="wx_appkey"
      android:value="aeced70482e18d2575da3fc0e513eb55" />
    <meta-data
      android:name="qq_appid"
      android:value="tencent101495075" />
    <!-- end -->

    <meta-data
      android:name="android.max_aspect"
      android:value="2.4" />
    <meta-data
      android:name="android.notch_support"
      android:value="true" />

    <meta-data
      android:name="BUGLESS_APP_SECRET"
      android:value="jdjfqwec-xr5g-zgfr-fdvb-zelplvzo" />
    <meta-data
      android:name="BUGLESS_APP_ID"
      android:value="40d4a67cfe" />

    <!--  apk install      -->
    <provider
      android:name="com.sqwan.msdk.provider.SqFileProvider"
      android:authorities="${applicationId}.provider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/provider_file" />
    </provider>
    <activity
      android:name="com.sqwan.common.dialog.PlatformAnnouncementActivity"
      android:theme="@style/Transparent" />

    <activity
      android:name="com.sqwan.m.WebDemoActivity"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:launchMode="singleTop"
      android:resizeableActivity="true"
      android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen"
      android:windowSoftInputMode="adjustPan" />

    <activity
      android:name="com.sqwan.m.ShareDemoActivity"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:launchMode="singleTop"
      android:resizeableActivity="true"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen"
      android:windowSoftInputMode="adjustPan" />

    <activity
      android:name="com.sqwan.common.net.risk.RiskWebActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="stateVisible|adjustResize" />

    <activity
      android:name="com.sqwan.common.net.risk.RiskWebPortraitActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="stateVisible|adjustResize" />

    <activity
      android:name="com.sqwan.common.net.risk.RiskWebLandscapeActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="landscape"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="stateVisible|adjustResize" />

    <activity
      android:name="com.sqwan.common.net.risk.RiskWebBehindActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="behind"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="stateVisible|adjustResize" />

    <provider
      android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
      android:authorities="${applicationId}.TTFileProvider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
    </provider>
  </application>

</manifest>