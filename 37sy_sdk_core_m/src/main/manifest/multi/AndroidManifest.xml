<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.sy37.sdk.demo"
  android:versionCode="14"
  android:versionName="1.2.6">

    <uses-sdk
        android:minSdkVersion="9"
        android:targetSdkVersion="30" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true" />

    <!-- sqwan Start -->

    <!-- 支付宝 权限 start -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <!-- 支付宝 权限 end -->

    <!--  V2.1新增，用于短信注册账号 -->
    <uses-permission android:name="android.permission.SEND_SMS"/>
    <uses-permission android:name="android.permission.READ_SMS"/>

    <!--  target26 华为要求这个权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <!-- sqwan End -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
      tools:ignore="QueryAllPackagesPermission" />


    <application
        android:name="com.sqwan.msdk.SQApplication"
        android:allowBackup="true"
        android:icon="@drawable/sy37_sdk_launcher">

        <!-- 悬浮窗的切换账号和主动切换账号，是否跳过登录界面  -->
        <!-- yes：不弹出登录框，直接返回成功，没有用户信息；no：弹出登录框，成功后返回用户信息 -->
        <meta-data  android:name="SQwanSkipSwitchLogin"  android:value="no" />

        <!-- SQwan End -->
        <meta-data android:name="android.max_aspect" android:value="2.4" />
        <meta-data android:name="android.notch_support" android:value="true"/>

        <meta-data android:name="BUGLESS_APP_SECRET" android:value="jdjfqwec-xr5g-zgfr-fdvb-zelplvzo"/>
        <meta-data android:name="BUGLESS_APP_ID" android:value="40d4a67cfe" />

    </application>

</manifest>