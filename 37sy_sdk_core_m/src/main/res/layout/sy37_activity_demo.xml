<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/demo_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <EditText
                android:id="@+id/etWeburl"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="请大佬输入链接" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="goWebUrl"
                android:text="跳转" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <Button
                    android:id="@+id/turn_on"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="收听上报"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/turn_off"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="退出收听上报"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/loginBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Login(登录)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/changeAccountBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="ChangeAccount(切换账号)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/payBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Pay(定额)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/payBtn2"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Pay(不定额)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/logoutBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Logout(不再使用)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/showExit"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showExitDailog(退出游戏对话框)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/showSQWebBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showSQWeb"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/btn154"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="1.5.4"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/btn156"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="1.5.6"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/wx_auth"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="微信授权"
                    android:textSize="12dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <Button
                    android:id="@+id/creatRole"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="creatRole(不再使用)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/creatRoleBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="creatRoleInfo(创建角色-提交信息)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/submitDataBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="submitRoleInfo(角色进服-提交信息)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/upgradeDataBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="upgradeRoleInfo(角色升级-提交信息)"
                    android:textSize="12dp"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/submitStatisticsInfo"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="submitStatisticsInfo(提交统计数据)"
                    android:textSize="12dp"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/getappconfigBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="getConfig(获取配置)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/wxShareBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="wxShare"
                    android:textSize="12dp"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/showPersonalBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showPersonalWeb"
                    android:textSize="12dp" />


                <Button
                    android:id="@+id/btn_ua"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="用户协议"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/share_img"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="研发直接分享图片"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/age_appropriate"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="适龄提醒"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="test"
                    android:text="test"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="joinRoom"
                    android:text="joinRoom"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="leaveRoom"
                    android:text="leaveRoom"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="joinRadioRoom"
                    android:text="joinRadioRoom"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="leaveRadioRoom"
                    android:text="leaveRadioRoom"
                    android:textSize="12dp" />

                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="chatRoom"
                    android:text="聊天页面"
                    android:textSize="12dp" />
                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="closeLiveshowVoice"
                    android:text="关闭直播声音"
                    android:textSize="12dp" />
                <Button
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:onClick="openLiveshowVoice"
                    android:text="开启直播声音"
                    android:textSize="12dp" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="订单金额："
                    android:textColor="#121212"
                    android:textSize="18dp" />

                <EditText
                    android:id="@+id/et_money"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:inputType="number"
                    android:text="100" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/iv_age_appropriate"
                    android:layout_width="48dp"
                    android:layout_height="62dp"
                    android:visibility="gone" />
            </LinearLayout>
        </LinearLayout>

        <EditText
            android:id="@+id/et_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="输入url" />

        <Button
            android:id="@+id/btn_open_url"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="打开webveiw" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:onClick="url1"
                android:text="带工具栏webview"
                android:textSize="12dp" />

            <Button
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:onClick="url2"
                android:text="普通webview"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/share_h5_wechat"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="js分享h5至好友"
                android:textSize="12dp" />

            <Button
                android:id="@+id/share_h5_moment"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="js分享h5至朋友圈"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/share_url_title_edt"
                android:layout_weight="1"
                android:hint="输入分享标题"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <EditText
                android:id="@+id/share_url_des_edt"
                android:layout_weight="1"
                android:hint="输入分享描述"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/share_url_edt"
                android:layout_weight="1"
                android:hint="输入分享url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <CheckBox
                android:id="@+id/share_is_preview"
                android:text="是否预览"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/share_url"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="分享链接"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <Button
                android:id="@+id/add_share"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="20dp"
                android:text="添加分享配置"
                android:textColor="#121212"
                android:textSize="18dp" />

            <ListView
                android:id="@+id/lv_share"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cp_track_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/cp_track_report"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:text="上报CP事件" />

                <Button
                    android:id="@+id/cp_track_add"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:text="添加事件字段" />

            </LinearLayout>

            <EditText
                android:id="@+id/cp_track_event"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:hint="CP事件名称" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>