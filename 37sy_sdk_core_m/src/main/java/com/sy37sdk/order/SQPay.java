package com.sy37sdk.order;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.order.IOrderMod;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.popup.BeforePayPopupDialogManager;
import com.sqwan.order.base.IPay;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.account.auth.AuthBean;
import com.sy37sdk.account.auth.AuthManager;
import org.json.JSONObject;

/**
 * 37支付
 *
 * <AUTHOR>
 * @since 2024/7/30
 */
public class SQPay implements IPay {

    private static final String TAG = "【Pay 37】";
    /**
     * 是否触发检查实名认证
     */
    public static final String EXTRA_CHECK_AUTH = "check_auth";

    @Override
    public void init(@NonNull Context context) {
        /* no-op */
    }

    /**
     * @param checkAuth 是否触发实名认证检查
     */
    public void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, boolean checkAuth, @Nullable PayCallback callback) {
        if (extra == null) {
            extra = new Bundle();
        }
        extra.putBoolean(EXTRA_CHECK_AUTH, checkAuth);
        pay(activity, payContext, payInfo, extra, callback);
    }

    /**
     * 默认会触发实名认证检查
     *
     * @see #EXTRA_CHECK_AUTH
     */
    @Override
    public void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @Nullable PayCallback callback) {
        SQLog.d(TAG + "37支付, extra=" + extra);
        // 外部可以控制是否需要检查实名认证
        boolean needCheckAuth = true;
        if (extra != null) {
            needCheckAuth = extra.getBoolean(EXTRA_CHECK_AUTH, true);
        }
        PayCallback uiCallback = UIPayCallback.wrap(callback);
        if (needCheckAuth) {
            SQLog.d(TAG + "检查实名认证");
            AuthBean authBean = parseAuthBean(payContext);
            // 检查是否需要实名认证
            boolean needAuth = authBean != null && authBean.needAuth() && !TextUtils.isEmpty(authBean.getUrl());
            if (needAuth) {
                SQLog.d(TAG + "需要实名认证");
                // 展示实名认证弹窗
                showAuthDialog(activity, authBean, payContext, payInfo, extra, uiCallback);
            } else {
                SQLog.d(TAG + "无需实名认证, 检查防沉迷配置");
                // 检查防沉迷配置是否允许支付
                PayAddictionBean addictionBean = PayAddictionBean.parse(payContext);
                // 没有配置也可以支付
                boolean allowPay = addictionBean == null || addictionBean.allowRecharge;
                if (allowPay) {
                    SQLog.d(TAG + "防沉迷允许支付, 继续支付");
                    payInternal(activity, payContext, payInfo, extra, uiCallback);
                } else {
                    SQLog.e(TAG + "防沉迷配置限制支付, 回调支付失败");
                    uiCallback.onFailed(payContext, new SqPayError(SqPayError.ERROR_SDK_ADDITION,
                        "防沉迷配置限制支付", -1, addictionBean.toastContent));
                }
            }
        } else {
            SQLog.d(TAG + "无需检查实名认证, 继续支付");
            payInternal(activity, payContext, payInfo, extra, uiCallback);
        }
    }

    @Nullable
    private AuthBean parseAuthBean(PayContext payContext) {
        JSONObject orderJson = payContext.getOrderDataJson();
        if (orderJson == null) {
            return null;
        }
        try {
            JSONObject authJson = orderJson.optJSONObject("auth");
            SQLog.d(TAG + "auth=" + authJson);
            if (authJson != null) {
                return AuthBean.parseFromJson(authJson);
            }
        } catch (Exception e) {
            SQLog.w(TAG + "解析auth配置异常", e);
        }
        return null;
    }

    private void showAuthDialog(@NonNull Activity activity, @NonNull AuthBean authBean, @NonNull PayContext payContext,
        @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @NonNull PayCallback callback) {
        String authUrl = authBean.getUrl();
        SQLog.d(TAG + "显示实名认证弹窗: " + authUrl);
        AuthManager.getInstance(activity).showAuthDialog(authUrl, authBean.isFocus(),
            false, new AuthManager.AuthCallbackAdapter() {
                @Override
                public void onSuccess() {
                    SQLog.e(TAG + "实名认证成功, 需要重新下单, 回调支付失败");
                    callback.onFailed(payContext,
                        new SqPayError(SqPayError.ERROR_SDK_AUTH, "实名认证成功，需要重新下单", -1));
                }

                @Override
                public void onFailure() {
                    // 判断是否可以继续往下走支付流程
                    if (authBean.getAllowRecharge()) {
                        SQLog.w(TAG + "实名认证失败, 但允许支付, 继续支付");
                        payInternal(activity, payContext, payInfo, extra, callback);
                    } else {
                        SQLog.e(TAG + "实名认证失败, 回调支付失败");
                        callback.onFailed(payContext,
                            new SqPayError(SqPayError.ERROR_SDK_AUTH, "未实名认证", -2));
                    }
                }
            });
    }

    private void payInternal(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @NonNull PayCallback callback) {
        // 无token表示用户尚未登录
        boolean hasLogin = !TextUtils.isEmpty(MultiSDKUtils.getToken(activity));
        if (!hasLogin) {
            SQLog.e(TAG + "未登录, 回调支付失败");
            callback.onFailed(payContext,
                new SqPayError(SqPayError.ERROR_SDK_USER_ERROR, "用户信息已过期，请重新登录", -1));
            return;
        }
        if (TextUtils.isEmpty(payInfo.getServerId())) {
            SQLog.e(TAG + "区服ID为空, 回调支付失败");
            callback.onFailed(payContext,
                new SqPayError(SqPayError.ERROR_SDK_USER_ERROR, "区服ID不能为空", -2));
            return;
        }

        String moid = payContext.getMoid();
        SQLog.d(TAG + "moid=" + moid);
        IOrderMod orderMod = ModHelper.get(IOrderMod.class);
        if (orderMod == null) {
            SQLog.e(TAG + "缺少支付模块, 回调支付失败");
            callback.onFailed(payContext, new SqPayError(
                SqPayError.ERROR_SDK_UNSUPPORTED, "无法支付", -3, "缺少支付模块"));
            return;
        }
        orderMod.pay(activity, payContext, payInfo, extra, callback);
        SQLog.d(TAG + "触发支付前弹窗");
        BeforePayPopupDialogManager.getInstance().handlePopup(
            activity, moid, payInfo.getOrderId(),
            String.valueOf(payInfo.getMoney()),
            String.valueOf(payInfo.getRoleLevel()),
            payInfo.getProductName());
    }

    /**
     * 防沉迷配置
     */
    private static class PayAddictionBean {

        /**
         * 提示文案
         */
        final String toastContent;

        /**
         * 是否继续调起充值界面
         */
        final boolean allowRecharge;

        public PayAddictionBean(boolean allowRecharge, String toastContent) {
            this.toastContent = toastContent;
            this.allowRecharge = allowRecharge;
        }

        @Nullable
        static PayAddictionBean parse(PayContext payContext) {
            JSONObject orderJson = payContext.getOrderDataJson();
            if (orderJson == null) {
                return null;
            }
            try {
                JSONObject addictionJson = orderJson.optJSONObject("addiction");
                SQLog.d(TAG + "addiction=" + addictionJson);
                if (addictionJson != null) {
                    String msg = addictionJson.optString("toastContent");
                    // 是否继续调起充值界面 （1是 0否）
                    boolean allowRecharge = addictionJson.optInt("allowRecharge") == 1;
                    return new PayAddictionBean(allowRecharge, msg);
                }
            } catch (Exception e) {
                SQLog.w(TAG + "解析addiction配置异常", e);
            }
            return null;
        }
    }
}
