package com.sqwan.afinal.download;

import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;

import com.sqwan.common.util.LogUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 * @date 2019/3/20
 */
public class DownloadTask extends AsyncTask<String, Long, Long> {

    private static final String TAG = "DownloadTask";
    private Handler mainHandler;

    private DownloadListener mListener;
    private String mUrl;
    private String downloadFileName;
    private String downloadPath;

    public static final int CODE_URL_NULL = 1;
    public static final int CODE_APK_NOT_EXIST = 2;
    public static final int CODE_EXCEPTION = 3;
    public static final int CODE_DID_NOT_FINISH = 4;

    /**
     * 服务器不能满足客户在请求中指定的Range
     */
    public static final int CODE_RANGE_NOT_SATIS = 416;

    private long total;

    public DownloadTask(String url, String fileName, String target, DownloadListener listener) {
        mUrl = url;
        downloadFileName = fileName;
        downloadPath = target;
        mListener = listener;
        mainHandler = new Handler(Looper.getMainLooper());
    }


    @Override
    protected Long doInBackground(String... strings) {
        if (mUrl == null) {
            postFailureResult(null, CODE_URL_NULL, "url 为 null");
            return null;
        }
        HttpURLConnection connection = null;
        InputStream is = null;
        OutputStream os = null;
        RandomAccessFile raf = null;
        try {
            File dir = new File(downloadPath);
            if (!dir.exists()) {
                dir.mkdir();
            }
            File file = new File(downloadPath, downloadFileName);
            URL url = new URL(mUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");
            int inputSize;
            long count = 0;
            if(!file.exists()) {
                file.createNewFile();
                os = new FileOutputStream(file);
                is = connection.getInputStream();
                total = connection.getContentLength();
                if (!handleResponseCode(connection.getResponseCode())) {
                    return null;
                }
                byte[] buffer = new byte[1024];
                while ((inputSize = is.read(buffer)) != -1) {
                    os.write(buffer, 0, inputSize);
                    count += inputSize;
                    publishProgress(count);
                    if (isCancelled()) {
                        os.flush();
                        return null;
                    }
                }
                os.flush();
            } else {
                long readSize = file.length();
                LogUtil.i("read size is " + readSize);
                connection.setRequestProperty("Range", "bytes=" + readSize + "-");
                if (!handleResponseCode(connection.getResponseCode())) {
                    return null;
                }
                total = readSize + connection.getContentLength();
                is = connection.getInputStream();
                raf = new RandomAccessFile(file, "rwd");
                raf.seek(readSize);
                byte[] buffer = new byte[1024];
                count = readSize;
                while ((inputSize = is.read(buffer)) != -1) {
                    raf.write(buffer, 0, inputSize);
                    count += inputSize;
                    publishProgress(count);
                    if (isCancelled()) {
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            postFailureResult(e, CODE_EXCEPTION, "下载出错");
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
                if (raf != null) {
                    raf.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private boolean handleResponseCode(int code) {
        LogUtil.i(TAG, "download response code is " + code);
        boolean result = true;
        switch (code) {
            case HttpURLConnection.HTTP_NOT_FOUND:
                postFailureResult(null, HttpURLConnection.HTTP_NOT_FOUND, "返回404");
                result = false;
                break;
            case CODE_RANGE_NOT_SATIS:
                postFailureResult(null, CODE_RANGE_NOT_SATIS, "服务器不能满足客户在请求中指定的Range");
                result = false;
                break;
            default:
                break;
        }
        return result;
    }

    private void postFailureResult(final Throwable t, final int code, final String msg) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                if(mListener != null) {
                    mListener.onFailure(t, code, msg);
                }
            }
        });
    }

    @Override
    protected void onCancelled() {
        super.onCancelled();
        LogUtil.i(TAG, "task is canceled");
    }

    @Override
    protected void onProgressUpdate(Long... values) {
        super.onProgressUpdate(values);
        Long current = values[0];
        mListener.onUpdate(total, current);
    }

    @Override
    protected void onPostExecute(Long aLong) {
        super.onPostExecute(aLong);
        LogUtil.i(TAG, "task is success");
        File file = new File(downloadPath, downloadFileName);
        if (file.exists()) {
            if(file.length() == total) {
                mListener.onSuccess(file);
            } else if(file.length() < total) {
                mListener.onFailure(null, CODE_DID_NOT_FINISH, "下载未完成");
            }
        } else {
            mListener.onFailure(null, CODE_APK_NOT_EXIST, "下载结束， apk文件没找到");
        }
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        LogUtil.i(TAG, "task is start");
    }
}
