package com.sqwan.push;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.push.service.PushLog;
import com.sq.push.service.SqPushService;
import com.sq.push.service.SqPushService.GameConfig;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.mod.push.IPushMod;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.utils.PushHttpRequester;
import java.util.Map;

/**
 * <AUTHOR>
 * @see com.sqwan.common.mod.ModManager
 * @since 2024/6/6
 */
public class PushModImpl implements IPushMod {

    @Override
    public void onCreate(@NonNull Activity activity, @Nullable Bundle bundle) {
        try {
            SqPushService.getInstance().onCreate(activity, bundle);
        } catch (Throwable e) {
            // 推送sdk代码在宿主, 插件调用有可能失败
        }
    }

    @Override
    public void onNewIntent(@NonNull Activity activity, @Nullable Intent intent) {
        try {
            SqPushService.getInstance().onNewIntent(activity, intent);
        } catch (Throwable e) {
            // 推送sdk代码在宿主, 插件调用有可能失败
        }
    }

    @Override
    public void init(@NonNull Context context) {
        try {
            PushLog.setTag("sqsdk");
            PushLog.setLogger(SQLog::log);
            SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
            SqPushService.getInstance().init(context,
                new GameConfig(config.getPartner(), config.getGameid()), new PushHttpRequester());
        } catch (Throwable e) {
            // 推送sdk有部分代码在宿主, 插件调用有可能失败
            SQLog.w("【Push】不支持推送");
        }
    }

    @Override
    public void setUserInfo(String uid, String roleId) {
        try {
            SqPushService.getInstance().setUserInfo(uid, roleId);
        } catch (Throwable e) {
            // 推送sdk代码在宿主, 插件调用有可能失败
        }
    }

    @Override
    public void setTransmitMessageListener(TransmitMessageListenerInternal listener) {
        try {
            SqPushService.getInstance().setTransmitMessageListener(json -> {
                if (listener != null) {
                    listener.onReceiveTransmitMessage(json);
                }
            });
        } catch (Throwable e) {
            // 推送sdk代码在宿主, 插件调用有可能失败
        }
    }

    @Override
    public void sendFeedback(@NonNull Context context, @Nullable Map<String, String> params) {
        try {
            SqPushService.getInstance().sendFeedback(context, params);
        } catch (Throwable e) {
            // 推送sdk代码在宿主, 插件调用有可能失败
        }
    }
}
