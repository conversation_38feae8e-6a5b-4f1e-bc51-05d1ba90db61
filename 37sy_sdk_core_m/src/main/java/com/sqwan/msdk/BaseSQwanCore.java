package com.sqwan.msdk;

import static com.sqwan.common.util.PermissionHelper.SETTING_REQUEST_CODE;
import static com.sqwan.msdk.SQEngineHandler.CONFIG_FILE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;
import com.m37.dtszj.sy37.wxapi.WXEntryActivity;
import com.parameters.performfeatureconfig.PerformFeature;
import com.parameters.share.DefaultShareInfo;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.utils.ClassCheckUtils;
import com.plugin.sdk.BasePluginInterface;
import com.sq.diagnostic.assistant.DiagnosticAssistant;
import com.sq.diagnostic.assistant.other.LogConfig;
import com.sq.eventbus.core.EventBus;
import com.sq.oaid.sq_oaid.IdsBean;
import com.sq.oaid.sq_oaid.SqOAIDHelper;
import com.sq.oaid.sq_oaid.SqOAIDHelper.Callback;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.EventReporter;
import com.sq.tool.network.ExceptionReporter;
import com.sq.tool.network.GateWayManager;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpClient;
import com.sq.tools.Logger;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sq.tools.network.httpdns.SqHttpDns;
import com.sq.tools.network.httpdns.SqHttpDnsConfig;
import com.sq.tools.network.httpdns.constant.HttpDnsRequestConstant;
import com.sq.tools.network.httpdns.util.VolleyRequest;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.TestConst;
import com.sqwan.base.L;
import com.sqwan.bugless.core.Bugless;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.annotation.ProtectUrlManager;
import com.sqwan.common.annotation.UrlUpdateUtils;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.dialog.LoadingExDialog;
import com.sqwan.common.dialog.PlatformAnnouncementActivity;
import com.sqwan.common.dialog.pop.OnDialogFinishListener;
import com.sqwan.common.eventbus.PreInitEvent;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.advertise.IAdvertiseMod;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.mod.liveshow.ILiveshowManager;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.mod.push.IPushMod;
import com.sqwan.common.mod.share.IShareMod;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.mod.track.TrackModManager;
import com.sqwan.common.mod.track.TrackModManager2;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MMOBindingUtils;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.PermissionSimpleHelper;
import com.sqwan.common.util.PermissionSimpleHelper.OnPermissionCallback;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SdkVersionUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.common.webview.SQWeb;
import com.sqwan.common.webview.WebRequestProxy;
import com.sqwan.msdk.api.BusinessParameters;
import com.sqwan.msdk.api.DiagnosticSensorManager;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.InitBeanUtil;
import com.sqwan.msdk.api.MRequestCallBack;
import com.sqwan.msdk.api.MRequestManager;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.PluginContext;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQAppConfigImpl;
import com.sqwan.msdk.api.SQPushTransmitMessageListener;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.SQSdkApi;
import com.sqwan.msdk.api.SQSdkInterface;
import com.sqwan.msdk.api.SQUpdateManager;
import com.sqwan.msdk.api.popup.ActivePopupDialogManager;
import com.sqwan.msdk.api.popup.SubmitRolePopupDialogManager;
import com.sqwan.msdk.api.sdk.Platform;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sqwan.msdk.utils.AlarmUtil;
import com.sqwan.msdk.utils.AppSigning;
import com.sqwan.msdk.utils.MUtil;
import com.sqwan.msdk.utils.TimeUtils;
import com.sqwan.msdk.utils.ZipString;
import com.sqwan.msdk.views.SQSplashDialog;
import com.sqwan.msdk.views.SQSplashDialog.SplashListener;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.PlatformAnnouncement.PlatformAnnouncementManager;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.sy37sdk.account.device.DevicesInfo;
import com.sy37sdk.account.policy.AuthHandler;
import com.sy37sdk.account.update.UpdateUrlManager;
import com.sy37sdk.utils.SettingHelper;
import com.sy37sdk.utils.Util;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public abstract class BaseSQwanCore extends BasePluginInterface implements SQSdkApi {

    protected static final String TAG = "【SQCore】";
    // 角色提交相关
    public static final String INFO_SERVERID = "serverId";
    public static final String INFO_SERVERNAME = "serverName";
    // 3.7.0新增开服时间
    public static final String INFO_SERVERTIME = "serverTime";
    public static final String INFO_ROLEID = "roleId";
    public static final String INFO_ROLENAME = "roleName";
    public static final String INFO_ROLELEVEL = "roleLevel";
    public static final String INFO_BALANCE = "balance";
    public static final String INFO_PARTYNAME = "partyName";
    public static final String INFO_VIPLEVEL = "vipLevel";
    public static final String INFO_ROLE_TIME_CREATE = "roleCTime";
    public static final String INFO_ROLE_TIME_LEVEL = "roleLevelMTime";

    // 用户信息
    public static final String LOGIN_KEY_TOKEN = "token";
    public static final String LOGIN_KEY_USERID = "userid";
    public static final String LOGIN_KEY_USERNAME = "username";
    public static final String LOGIN_KEY_GID = "gid";
    public static final String LOGIN_KEY_PID = "pid";
    // 公告弹窗s
    public static final String LOGIN_KEY_NURL = "nurl";
    // 激活码
    public static final String LOGIN_KEY_BETA = "beta";
    public static final String TOKEN = "token";

    /** 日志等级：调试 */
    public static final int LOG_LEVEL_DEBUG = 0;
    /** 日志等级：信息 */
    public static final int LOG_LEVEL_INFO = 1;
    /** 日志等级：警告 */
    public static final int LOG_LEVEL_WARN = 2;
    /** 日志等级：错误 */
    public static final int LOG_LEVEL_ERROR = 3;

    //初始化回调参数key
    private static final String IMEI = "imei";
    private static final String IS_UPDATE = "is_update";
    private static final String UPDATE_TYPE = "update_type";

    // 平台
    public static final int Platform_SQwan = 1;


    // 其他
    public static SQwanCoreImpl instance;
    public static byte[] lock = new byte[0];

    public static InitBean initBean;

    public static Platform sdkapi;

    /**
     * 上下文对象（可能为空）
     */
    public Context context;
    public SQResultListener switchAccountListener;
    public SQResultListener baseInitListener;

    // 回到游戏登录界面
    public SQResultListener back2GameListener;

    // 是否显示debug
    public static String debug4cp;

    private boolean isCheckOn = false;
    public static boolean isInitSqwan = false;
    public static boolean isPlatformInitRunning = false;
    private static boolean isInitSuccess = false;// 初始化成功的标识
    public boolean isLogin = false;
    public boolean isSubmit = false;

    protected MRequestManager requestManager = null;
    protected LoadingExDialog initLoading = null;// 初始化loading图

    private IScreenshotListener mScreenshotListener;//截图监听

    public static WeakReference<Activity> sContextReference;

    public static final int SQ_REQUEST_PERMISSION_CODE = PermissionHelper.SQ_REQUEST_PERMISSION_CODE;
    private String appKey;
    private SQResultListener mInitListener;

    private Handler mHandler;
    public boolean isAuthCheck = false;

    public abstract SQSdkInterface getPlatform(Context context, InitBean bean,
                                               SQResultListener initListener);

    public BaseSQwanCore() {
    }

    /**
     * 37平台初始化
     *
     * @param cxt
     * @param appkey
     * @param listener
     */
    public void init(final Context cxt, final String appkey,
                     final SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.init");
        // 调用初始化，将初始化标识为false
        isInitSuccess = false;
        sContextReference = new WeakReference<Activity>((Activity) cxt);
        SQContextWrapper.init((Activity) cxt);
        L.init(cxt);
        mInitListener = listener;
        appKey = appkey;
        context = cxt;
        mHandler = new Handler(Looper.getMainLooper());
        setAuthCheck(false);
        SQEngineHandler.getInstance().init(context);
        // 获取参数
        initConfig(appkey);

        EventBus.getDefault().post(new PreInitEvent(""));

        //初始化dns配置
        initHttpDns();

        // 初始化 SDK 诊断助手
        initDiagnosticAssistant();

        //离线加载初始化
        initLocalH5();
        SqHttpClient.getInstance().setIpController(() -> {
            //应用在前台才获取
            return ActivityLifeCycleUtils.getInstance().isForeground();
        });
        UpdateUrlManager updateUrlManager = new UpdateUrlManager();
        updateUrlManager.reqUrlUpdateManager(new SqHttpCallback<JSONObject>() {

            @Override
            public void onSuccess(JSONObject dataJson) {
                sendLog("接口动态下发请求成功！");
                HashMap<String, String> map = updateUrlManager.parse(dataJson);
                if (GateWayManager.getWhiteList().contains(UrlConstant.UPDATE_URL)) { //统一网关逻辑
                    GateWayManager.updateWhiteList(map);
                    GateWayManager.updateKeySet(dataJson);
                }
                UrlUpdateUtils.setUrlData(map, context);
                //接口下发成功 设置dns保护域名列表为接口下发的
                ProtectUrlManager.getInstance().setProtectUrls(updateUrlManager.getApiUrls());
                afterApiUpdate(cxt);
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                onFailure(msg);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                onFailure(errorMsg);
            }

            private void onFailure(String msg) {
                sendLog("接口动态下发请求失败，继续后续操作, " + msg);
                //接口下发请求失败,设置dns保护域名列表为项目中默认的
                ProtectUrlManager.getInstance().setProtectUrls(UrlUpdateUtils.getUrls());
                afterApiUpdate(cxt);
            }
        });
        initToken();
        resetRoleInfo();
    }

    /*
     * 初始化穿山甲
     * */
    private void initAds() {
        IAdvertiseMod mod = ModHelper.get(IAdvertiseMod.class);
        if (mod != null) {
            try {
                mod.init(context);
            } catch (Error e) {
                LogUtil.e("广告模块初始化异常 " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void checkPackageParams() {
        String md5 = AppSigning.getMD5(context);
        String sha256 = AppSigning.getSHA256(context);
        String sha1 = AppSigning.getSha1(context);

        Map<String, String> map = new HashMap<>();
        map.put("md5", md5);
        map.put("sha256", sha256);
        map.put("sha1", sha1);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PACKAGE_PARAMS, map);
    }

    private void initHttpDns() {
        // 是否开启dns dns-close不存在（默认），走dns，当dns-close
        boolean enable = !EnvironmentUtils.isFileExits(context, "dns-close");
        // 设置全局开关
        SqHttpDns.getInstance().setGlobalEnable(enable);
        //拉取dns配置服务所需参数，必传
        Map<String, String> dnsRequestMap = new HashMap<>();
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        dnsRequestMap.put("pid", config.getPartner());
        dnsRequestMap.put("gid", config.getGameid());
        dnsRequestMap.put("refer", config.getRefer());
        dnsRequestMap.put("dev", DevLogic.getInstance(context).getValue());
        dnsRequestMap.put("sversion", VersionUtil.sdkVersion);
        dnsRequestMap.put("gwversion", VersionUtil.gwversion);

        SqHttpDnsConfig httpDnsConfig = new SqHttpDnsConfig.Builder()
            .setSecretInfo(HttpDnsRequestConstant.APP_ID, HttpDnsRequestConstant.APP_KEY)
            .setTimeOut(5 * 1000)
            .setDnsServerIps(HttpDnsRequestConstant.REQUEST_IPS)
            .setDnsServerHost(HttpDnsRequestConstant.DNS_HOST)
            .enable(false)// 无配置时, 默认关闭dns
            .build();
        SqHttpDns.getInstance().init(context, httpDnsConfig, dnsRequestMap,
            new VolleyRequest(SqHttpClient.getInstance()));
        SqHttpDns.getInstance().setExceptionReporter(new ExceptionReporter());

        //添加主域保护列表
        ProtectUrlManager.getInstance().addProtectMainHost(MultiSdkManager.APP_HOST);
        //添加域名保护列表
        ProtectUrlManager.getInstance().addProtectHosts(ProtectUrlManager.defaultProtectHosts);
        //当sdk版本更新时清除dns服务缓存
        if (SdkVersionUtil.isNewVersion(context)) {
            SdkVersionUtil.updateVersion(context);
            SqHttpDns.getInstance().clearDnsServer();
        }
    }


    private void initLocalH5() {
        try {

            SQAppConfig appConfig = ConfigManager.getInstance(context).getSQAppConfig();
            SQWeb.getInstance().localH5Builder()
                .setGid(appConfig.getGameid())
                .setPid(appConfig.getPartner())
                .setSversion(VersionUtil.sdkVersion)
                .setRequestProxy(new WebRequestProxy())
                .build(SQContextWrapper.getActivity().getApplication());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initDiagnosticAssistant() {
        if (context == null) {
            return;
        }

        if (initBean == null) {
            return;
        }

        LogConfig logConfig = new LogConfig(context);
        logConfig.logPrintEnable = initBean.getDebug() == 1;

        DiagnosticAssistant.getInstance()
                .setLogConfig(logConfig)
                .setServerHost(DiagnosticAssistant.SERVER_HOST_CHINA)
                // 国内域名
                .addDefaultTcpingConfig("37.com.cn", 80)
                .addDefaultTcpingConfig("37.com", 80)
                .addDefaultTcpingConfig("bugless.shan-yu-tech.com", 80)
                .addDefaultTcpingConfig("baidu.com", 80)
                .addDefaultMtrConfig("37.com.cn", 31)
                .addDefaultMtrConfig("37.com", 31)
                .setBusinessParameters(new BusinessParameters(context))
                .setCallBack(new DiagnosticAssistant.CallBack() {

                    @Override
                    public void onHomePageShow() {
                        LogUtil.i("诊断助手显示了");
                        DiagnosticSensorManager.getInstance().onPause();
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_HOME_SHOW);
                    }

                    @Override
                    public void onHomePageClose() {
                        LogUtil.i("诊断助手关闭了");
                        DiagnosticSensorManager.getInstance().onResume();
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_HOME_CLOSE);
                    }

                    @Override
                    public void onUpdateLogSuccess() {
                        LogUtil.i("日志上传成功");
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_UPDATE_LOG_SUCCESS);
                    }

                    @Override
                    public void onUpdateLogFail(int errorCode, String errorMsg) {
                        LogUtil.i("日志上传失败");

                        HashMap<String, String> extraPram = new HashMap<>();
                        extraPram.put(SqTrackKey.fail_code, String.valueOf(errorCode));
                        extraPram.put(SqTrackKey.reason_fail, errorMsg);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_UPDATE_LOG_FAIL, extraPram);
                    }

                    @Override
                    public void onLogRetrievalSuccess() {
                        LogUtil.i("日志回捞成功");

                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_LOG_RETRIEVAL_SUCCESS);
                    }

                    @Override
                    public void onLogRetrievalFail(int errorCode, String errorMsg) {
                        LogUtil.i("日志回捞失败");

                        HashMap<String, String> extraPram = new HashMap<>();
                        extraPram.put(SqTrackKey.fail_code, String.valueOf(errorCode));
                        extraPram.put(SqTrackKey.reason_fail, errorMsg);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.DIAGNOSTIC_ASSISTANT_LOG_RETRIEVAL_FAIL, extraPram);
                    }
                })
                .init(context);
    }

    private void initPushService() {
        IPushMod pushMod = ModHelper.get(IPushMod.class);
        if (pushMod != null) {
            pushMod.init(context);
        }
    }

    private void afterApiUpdate(Context cxt) {
        //设置dns预解析域名
        SqHttpDns.getInstance().setPreResolveHosts(ProtectUrlManager.getInstance().getProtectHosts());
        //拉取dns服务配置
        SqHttpDns.getInstance().pullDnsConfig();
        UrlUpdateUtils.urlUpdate(cxt);
        PlatformAnnouncementManager.getInstance().getPlatformAnnouncementRequest(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                String content = bundle.getString("content");
                String title = bundle.getString("title");
                boolean is_show = bundle.getBoolean("is_show");
                if (is_show) {
                    showDialog((Activity) cxt, title, content);
                }
            }

            @Override
            public void onFailture(int code, String msg) {
                LogUtil.i(code + "_" + msg);
            }
        });
        ActiveBeforeManager.getInstance().updateData(new ActiveBeforeManager.UpdateDataCallback() {
            @Override
            public void invoke() {
                AuthHandler.getInstance().checkPermission(new AuthHandler.PermissionCallback() {
                    @Override
                    public void invoke() {
                        Map<String, Boolean> consent = new HashMap<>();
                        consent.put("all", true);
                        SqHttpClient.getInstance().setUserConsent(consent);
                        SqHttpDns.getInstance().setUserConsent(consent);
                        setAuthCheck(true);
                        AuthHandler.getInstance().setAuthHandle();
                        //确保政策合规，第三方sdk初始化放在权限申请之后再进行
                        TrackModManager.init(context);
                        TrackModManager2.init(context);
                        trackPlugin();
                        registerActivityLife();
                        initSelf();

                        LogConfig logConfig = DiagnosticAssistant.getInstance().getLogConfig();
                        if (initBean != null && logConfig != null) {
                            // 更新日志的打印开关
                            logConfig.logPrintEnable = initBean.getDebug() == 1;
                        }

                        // 请求游戏域名
                        ActiveBeforeManager.getInstance().requestGameUrlList();

                        // 触发日志回捞操作
                        DiagnosticAssistant.logRetrieval();

                        // 如果是在模拟器上面运行，则不监听摇一摇，因为有用户反馈在雷电模拟器上面 sdk 初始化会弹出诊断助手
                        // 但是我们自己人去复现的时候没有办法复现，这就很诡异，然后用户也是，有时候会，有时候不会
                        // 所以最终敲定的解决方案是先判断当前设备是否是模拟器，如果是则不去通过摇一摇开启诊断助手
                        if (!DeviceUtils.isSimulator(context)) {
                            // 初始化诊断助手摇一摇监听
                            DiagnosticSensorManager.getInstance().init(cxt);
                            // 监听摇一摇，如果触发了摇一摇就打开诊断助手
                            DiagnosticSensorManager.getInstance().registerSensorChangedCallback(event -> {
                                Logger.info("通过摇一摇开启了诊断助手");
                                // 如果已经开启过一次诊断助手了，那么不再监听摇一摇了
                                DiagnosticSensorManager.getInstance().unregisterSensorChangedCallback();
                                DiagnosticAssistant.show(context);
                            });
                        } else {
                            Logger.info("不初始化诊断助手");
                        }
                    }
                });
            }
        });
    }

    private void reportOaid() {
        SqOAIDHelper helper = new SqOAIDHelper(new Callback() {
            @Override
            public void onIdsValid(IdsBean idsBean) {
                reportMDev(idsBean.toString());
            }
        }, new EventReporter(), new ExceptionReporter());
        helper.getDeviceIds(new PluginContext(context, context.getResources()));
    }

    /**
     * 上报插件加载生效埋点
     */
    private void trackPlugin() {
        //热更插件加载生效
        String KEY_HOTTER_EFFECT = "hotter_hotter_effect";
        SharedPreferences sp = context.getSharedPreferences("sq_plugin_config", Context.MODE_PRIVATE);
        //获取加载新插件之前的旧插件版本号
        String hotterEffectBeforeVersion = sp.getString(KEY_HOTTER_EFFECT, "");
        Logger.info("读取到hotterEffectBeforeVersion " + hotterEffectBeforeVersion);
        if (!TextUtils.isEmpty(hotterEffectBeforeVersion)) {
            HashMap<String, String> trackMap = new HashMap<>();
            trackMap.put(SqTrackKey.plug_latest_version, hotterEffectBeforeVersion);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update_effect, trackMap);
            //上报完后置空
            sp.edit().putString(KEY_HOTTER_EFFECT, "").apply();
            Logger.info("热更加载生效上报成功，置空 ");
        }

        //热更回滚生效
        String KEY_HOTTER_ROLLBACK_EFFECT = "hotter_rollback_effect";
        //获取回滚插件之前的旧插件版本号
        String hotterEffectRollbackBeforeVersion = sp.getString(KEY_HOTTER_ROLLBACK_EFFECT, "");
        Logger.info("hotterEffectRollbackBeforeVersion " + hotterEffectRollbackBeforeVersion);
        if (!TextUtils.isEmpty(hotterEffectRollbackBeforeVersion)) {
            HashMap<String, String> trackMap = new HashMap<>();
            trackMap.put(SqTrackKey.plug_latest_version, hotterEffectBeforeVersion);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update_rollback_effect, trackMap);
            //上报完后置空
            sp.edit().putString(KEY_HOTTER_ROLLBACK_EFFECT, "").apply();
            Logger.info("回滚加载生效上报成功，置空 ");
        }

        // 热更耗时统计
        String keyHotterConsuming = "hotter_consuming";
        String pluginConsumingTime = sp.getString(keyHotterConsuming, "");
        Logger.info("pluginConsumingTime " + hotterEffectRollbackBeforeVersion);
        if (!TextUtils.isEmpty(pluginConsumingTime)) {
            HashMap<String, String> trackMap = new HashMap<>();
            trackMap.put(SqTrackKey.PLUG_CONSUMING_TIME, String.valueOf(pluginConsumingTime));
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.HOTTER_UPDATE_CONSUMING, trackMap);
            //上报完后置空
            sp.edit().putString(keyHotterConsuming, "").apply();
            Logger.info("插件 apk 加载耗时上报成功，置空");
        }
    }

    private void registerActivityLife() {
        ActivityLifeCycleUtils.getInstance().registerActivityListener(new ActivityLifeCycleUtils.AppVisibilityCallback() {
            @Override
            public void onBackground() {
                LogUtil.i("切换到了后台");
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.backstage_succ);
            }

            @Override
            public void onForeground() {
                LogUtil.i("切换到了前台");
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.resume_succ);
            }
        });
    }

    private void showDialog(Activity context, String title, String content) {
        context.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent(context, PlatformAnnouncementActivity.class);
                intent.putExtra("title", title);
                intent.putExtra("content", content);
                context.startActivity(intent);
                context.overridePendingTransition(0, 0);
            }
        });
    }


    private void initSelf() {
        // 对初始化接口进行保护，在未完成之前，不可以进行第二次的操作。
        if (!isPlatformInitRunning) {
            isPlatformInitRunning = true;
            initCore(context, appKey, new SQResultListener() {
                @Override
                public void onSuccess(final Bundle bundle) {
                    boolean debug = initBean != null && initBean.getDebug() == 1;
                    showTestToast("init 接口调用 初始化成功 debug模式为" + debug);
                    isPlatformInitRunning = false;
                    // 初始化成功标识为true
                    isInitSuccess = true;
                    // 初始化成功后, 隐私协议通过后再初始化个推
                    initPushService();
                    // 使用延迟回调是为了，避免在渠道对接中，在initPlatform中，如果直接回调初始化成功
                    // ，此时，研发如果直接调用登录的话，会导致此时具体的Platform还没完成实例，导致sdkapi为空，导致无法调起SDK服务
                    post(new Runnable() {
                        @Override
                        public void run() {
                            mInitListener.onSuccess(fillInitData(bundle));
                            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_succ);
                            SqTrackActionManager2.getInstance().flush();
                            ActivePopupDialogManager.getInstance().handlePopup(context);
                        }
                    });
                }

                @Override
                public void onFailture(final int code, final String msg) {
                    isPlatformInitRunning = false;
                    post(new Runnable() {
                        @Override
                        public void run() {
                            mInitListener.onFailture(code, msg);
                        }
                    });
                }
            });
        } else {
            sendLog("初始化进行中，多次调用");
            ViewUtils.showToast(context, "初始化进行中，请勿多次调用..");
        }
    }

    private Bundle fillInitData(Bundle bundle) {
        bundle.putString(IMEI, ImeiLogic.getInstance(context).getValue());
        String updateType = SQUpdateManager.updateType;
        bundle.putBoolean(IS_UPDATE, updateType.equals(SQUpdateManager.TYPE_UPDATE) || updateType.equals(SQUpdateManager.TYPE_FORCE));
        bundle.putString(UPDATE_TYPE, updateType);
        LogUtil.i("初始化回调数据: " + bundle.toString());
        return bundle;
    }

    public void initCore(final Context cxt, final String appkey,
                         final SQResultListener listener) {

        this.context = cxt;
        sendLogNoDebug("SDK开始初始化");
        // 获取参数
        initConfig(appkey);
        // 初始化 bugless 应用参数
        BuglessAction.setAppExtension(ConfigManager.getInstance(context).getSQAppConfig());
        // M层初始化，获取dev，拦截，更新等。
        initSQPlatformRequest(listener);
        // 20151110原来的平台初始化，放到M层初始化之后了
        this.baseInitListener = listener;
        initEngine(cxt);
        initAds();
        checkPackageParams();
    }

    private void initEngine(Context cxt) {
        AuthCountDownManager.getInstance().init(cxt);

    }

    /**
     * 初始化appConfig（包括37wan_config.xml和multicofig）
     *
     * @param appkey
     */
    private void initConfig(final String appkey) {
        sendLogNoDebug("获取游戏参数");
        String mac = MultiSDKUtils.getDevMac(context);
        String imei = MultiSDKUtils.getDevImei(context);
        sendLogNoDebug("缓存前 mac=" + mac + "|imei=" + imei);
        if (TextUtils.isEmpty(mac)) {
            MultiSDKUtils.setDevMac(context, MultiSDKUtils.getMac(context, isAuthCheck));
        }
        if (TextUtils.isEmpty(imei)) {
            MultiSDKUtils.setDevImei(context, MultiSDKUtils.getIMEI(context, isAuthCheck));
        }
        // -----------------------读取37配置文件-------------------------------
        ConfigManager.getInstance(context).initConfig();
        ConfigManager.getInstance(context).setAppKey(appkey);
        MultiSDKUtils.setKey(context, ZipString.json2ZipString(appkey));

        //解析 multiconfig 配置文件
        MultiConfigManager.getInstance().initMultiConfig(context);

        // -------------------------读取多平台配置文件-----------------------------
        Properties prop = MultiSDKUtils.readPropertites(context, CONFIG_FILE);
        initBean = InitBeanUtil.inflactBean(context, prop);
        if (initBean == null) {
            // 没有multiconfig就自动生成一个
            initBean = new InitBean();
            initBean.setUsesdk(Platform_SQwan);
        }
        if (initBean.getUsesdk() == Platform_SQwan) {
            // 不管是自动生成的，还是为了方便测试，手动在母包添加了multiconfig文件的。
            initBean.setAppkey(appkey);
            initBean.setAppid(MultiSDKUtils.getGID(context));
            // 不显示闪屏，闪屏在s层中进行显示
            initBean.setIsSplashShow(0);
            // 强制使用渠道的退出。
            initBean.setUsePlatformExit(1);
        }

        MultiSDKUtils.setPushIsDelay(context, initBean.getIsPushDelay() == 1);

        // /控制单平台debug
        // 设置是否开启push的延迟
        Util.setLogPrintEnable(initBean.getDebug() == 1);
        Logger.setTag("sqsdk");

        //是否开启动态日志检测
        if (initBean.isLogDetect == 1) {
            sendLog("开启动态日志检测");
        }

        sendLog("37wan_config:" + getAppConfig().toString() + " | key:"
                + ZipString.zipString2Json(MultiSDKUtils.getKey(context)) + "|");
        sendLog("multiconfig:" + initBean.toString());

        // -----------------------------判断SDK版本-----------------------------
        // infoName 为原先的37wan_info，多SDK可能会将其重新命名
        String infoName = MultiSdkManager.getInstance().getInfo();
        Properties sdkInfo = MultiSDKUtils.readPropertites(context, MultiSdkManager.getInstance().getInfo());
        if (sdkInfo != null) {
            debug4cp = sdkInfo.getProperty("debug") == null ? "0" : sdkInfo.getProperty("debug");
            //设置出包时候的sdk版本号
            String originalVersion = sdkInfo.getProperty("sdkverion") == null ? "" : sdkInfo.getProperty("sdkverion");
            VersionUtil.setOriginalVersion(originalVersion);
        } else {
            ViewUtils.showToast(context, "assets缺少" + infoName + "文件");
            sendLogBase4CP("assets缺少" + infoName + "文件");
        }
    }

    /**
     * 显示初始化请求的loading，黑色背景
     */
    private void showInitLoading() {
        if (initLoading == null && context != null) {
            sendLog("showInitLoading");
            initLoading = new LoadingExDialog(context);
            initLoading.show();
            initLoading.setMessage("");
        }
    }

    private void hideInitLoading() {
        if (TestConst.isTestInitLoadingDialog) {
            Task.postDelay(5000, new Runnable() {
                @Override
                public void run() {
                    if (initLoading != null && initLoading.isShowing() && context != null) {
                        initLoading.dismiss();
                    }
                }
            });
        } else {
            if (initLoading != null && initLoading.isShowing() && context != null) {
                initLoading.dismiss();
            }
        }


    }

    private void onActiveResSData(JSONObject data) throws Exception {
        String dev = data.getString("dev");
        MultiSDKUtils.setDevID(context, dev);
        DevLogic.getInstance(context).saveToCache(dev);
        // 是否截流
        if (data.has("sdata")) {
            String sdata = data.getString("sdata");
            String sdataUnzip = MUtil.sqUnZip(context, sdata);
            sendLog("解密内容=" + sdataUnzip);
            JSONObject sdataJson = new JSONObject(sdataUnzip);
            int loginCode = sdataJson.getInt("code");
            sendLog("code of login" + loginCode);
            MultiSDKUtils.setCodeOfLogin(context, ""
                    + loginCode);
            ConfigManager.getInstance(context).setLoginCode(loginCode);
        } else {
            ConfigManager.getInstance(context).setLoginCode(0);
//			MultiSDKUtils.setCodeOfLogin(context, "1");
        }
    }

    private void onActiveResUpdate(JSONObject data) throws Exception {
        SQUpdateManager.checkUpdateConfig(context, data);
    }

    private void onActivityResLibs(JSONObject data) throws Exception {
        if (!data.isNull("libs")) {
            String libsStr = data.getString("libs");
            JSONArray libs = new JSONArray(libsStr);
            if (libsStr == null ||
                    "".equals(libsStr) || libs.length() == 0) {
                sendLog("onActivityResLibs : libs empty");
                return;
            }

            int length = libs.length();
            for (int i = 0; i < length; i++) {
                String className = (String) libs.get(i);
                try {
                    boolean exit = null != Class.forName(className);
                    if (exit) {
                        sendLog("onActivityResLibs : exit  : " + className);
                        System.exit(0);
                    }
                } catch (Throwable t) {
                    sendLog("onActivityResLibs : not exit  : " + t.toString());
                }
            }
        } else {
            sendLog("onActivityResLibs : service not libs");
        }
    }

    private void onActiveResOther(JSONObject data) throws Exception {
        //判断是否开启按压上报
        if (!data.isNull("isTouch")) {
            String isTouch = data.getString("isTouch");
            AccountCache.setTouch(context, isTouch.equals("1"));
        }
    }

    private void onActiveAntiAddiction(JSONObject data) {
        if (data.has("anti_addiction")) {
            try {
                JSONObject antiAddiction = data.getJSONObject("anti_addiction");
                int state = antiAddiction.has("state") ? antiAddiction.getInt("state") : 0;
                if (state != 0) {
                    sendLog("防沉迷提示状态为开");
                    String splitHourStr = antiAddiction.has("ejection_time") ? antiAddiction.getString("ejection_time") : "";
                    String exitTimeStr = antiAddiction.has("exit_time") ? antiAddiction.getString("exit_time") : "";
                    String message = antiAddiction.has("message") ? antiAddiction.getString("message") : "";
                    sendLog(splitHourStr + "小时之后显示");
                    sendLog("显示" + exitTimeStr + "分钟");
                    sendLog("显示信息：" + message);
                    if (!TextUtils.isEmpty(splitHourStr) && !TextUtils.isEmpty(exitTimeStr) && !TextUtils.isEmpty(message)) {
                        int splitHour = Integer.valueOf(splitHourStr);
                        int exitTime = Integer.valueOf(exitTimeStr);
                        Intent intent = new Intent();
                        intent.setAction("com.game.tip.alarm");
                        intent.putExtra("tips", message);
                        intent.putExtra("showTime", exitTime * TimeUtils.MINUTE);
                        sendLog("启动闹钟");
                        AlarmUtil.setAlarm(context, splitHour * TimeUtils.HOUR, 1001, intent);
                    }
                } else {
                    sendLog("防沉迷开关为关");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            sendLog("防沉迷开关为关");
        }
    }

    private void onActiveNotice(JSONObject data) throws Exception {
        // 显示公告。
        if (!data.isNull("nurl")) {
            String url = data.getString("nurl");
            LogUtil.i("onActiveNotice show notice dialog, " + url);
            //MultiSDKUtils.showNoticeDialog(context, "", url);
        }
    }

    /**
     * 解析pinfo,是否上报采集设备信息
     * <p>
     * 数据上报接口需求 ：
     * 已经在m层激活添加  data.pInfo字段，当字段非空时则上报信息。上报信息需要进行加密
     * ，加密算法和密钥参考s层接口的apps字段。上报结果中，若state返回1
     * ，则为解密成功，并且在data中给出解密后的内容（调试用，调试完成后会去除，请勿读取data字段）
     *
     * @param data
     */
    private void onActivePInfo(JSONObject data) {
        if (data.has("pInfo")) {
            try {
                String pinfo = data.getString("pInfo");
                sendLog("激活返回数据中读取到pInfo信息-->" + pinfo);
                String deviceInfo = DeviceUtils.loadInfo(context);
                //用于设备评分，统一收集数据
                DevicesInfo.setDeviceInfoFromJson(deviceInfo);
                requestManager.uploadDeviceInfo(deviceInfo, pinfo);
            } catch (JSONException e) {
                sendLog("激活返回数据中解析异常");
                e.printStackTrace();
            }
        } else {
            sendLog("激活返回数据中没有读取到pInfo信息");
        }
    }

    private void onActiveRequestSuccess(JSONObject data, SQResultListener initListener) {
        LogUtil.d("active Response: " + data);
        try {
                onActiveResSData(data);
                onActiveResUpdate(data);
                // 更新push时间
                if (!data.isNull("ph")) {
                    int delay = data.getInt("ph");
                    MultiSDKUtils.setPushDelay(context,
                            delay * 60 * 60 * 1000);
                }
                onActivityResLibs(data);
                onActiveResOther(data);
                onActiveAntiAddiction(data);
                onActiveNotice(data);
                onActivePInfo(data);
                hideInitLoading();
                showSplashAndInitPlatform();
                onActiveLiveshowInfo(data);
                DevicesInfo.initAndDoDeviceCollect(context);//设备评分
                com.sy37sdk.account.config.ConfigManager.getInstance().initConfigInfo(context);
        } catch (Exception e) {
            e.printStackTrace();
            ViewUtils.showToast(context, "初始化解析过程－异常");
            sendLog("初始化解析过程－异常 ： " + e.toString(), 1);
            BuglessAction.reportCatchException(e, data.toString(), BuglessAction.M_INIT);
            initListener.onFailture(204, "init sdk parse data fail.");
            hideInitLoading();
            HashMap<String, String> extraMap = new HashMap<>();
            extraMap.put(SqTrackKey.reason_fail, "m层解析错误 " + e.getMessage());
            extraMap.put(SqTrackKey.fail_code, "204");
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_fail, extraMap);
        }
    }

    private void onActiveResponseStateError(int state, String msg, SQResultListener initListener) {
        sendLog("初始化请求失败：" + msg, 1);
        initListener.onFailture(204, "init sdk request fail, state is not 1");
        HashMap<String, String> extraMap = new HashMap<>();
        extraMap.put(SqTrackKey.reason_fail, "m层初始化返回错误：" + msg);
        extraMap.put(SqTrackKey.fail_code, String.valueOf(state));
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_fail, extraMap);
        MultiSDKUtils.showTips(context, "初始化请求失败：" + msg);
        hideInitLoading();
    }

    private void onActiveLiveshowInfo(JSONObject data) {
        ILiveshowManager liveshowEngine = LiveshowEngine.getInstance().getLiveshowManager();
        ILiveshowManager liveRadioEngine = LiveRadioEngine.getInstance().getLiveshowManager();
        if (liveshowEngine != null) {
            liveshowEngine.onActive(data);
        }
        if (liveRadioEngine != null) {
            liveRadioEngine.onActive(data);
        }
    }

    /**
     * 初始化SQ平台request 得到平台初始化数据(urls，判断是否更新)
     */
    private void initSQPlatformRequest(final SQResultListener initListener) {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init);
        sendLog("平台初始化请求");
        showInitLoading();

        // 初始化网络请求manager
        requestManager = new MRequestManager(context);
        requestManager.active(new SqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject data) {
                onActiveRequestSuccess(data, initListener);
                SQReportCore.getInstance().afterPermission(context);
                reportOaid();
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                onActiveResponseStateError(state, msg, initListener);
                SQReportCore.getInstance().afterPermission(context);
                reportOaid();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                ViewUtils.showToast(context, "网络请求失败，请重试");
                sendLog("初始化请求异常", 1);
                initListener.onFailture(204, "网络请求失败，请重试");
                hideInitLoading();
            }
        });


    }


    private void showSplashAndInitPlatform() {
        String code = MultiSDKUtils.getCodeOfLogin(context);
        sendLog("showSplashAndInitPlatform code is " + code);
        if (!TextUtils.isEmpty(code) && "1".equals(code)) {
            // 截流不显示闪屏，直接初始化
            initPaltform(baseInitListener);
        } else {
            // 新闪屏
            if (initBean != null && initBean.getIsSplashShow() == 1) {
                SQSplashDialog splash = new SQSplashDialog(context, 3);
                splash.setSplashListener(new SplashListener() {

                    @Override
                    public void afterSplash() {
                        // 闪屏结束初始化
                        sendLog("splash end ...");
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.open_img);
                        initPaltform(baseInitListener);
                    }
                });
                splash.show();
            } else {
                initPaltform(baseInitListener);
            }
        }
    }

    private synchronized void initPaltform(SQResultListener initListener) {
        sendLog("初始化平台代码");
        // 无论是单平台还是多请平台，全部走Platform
        sdkapi = (Platform) getPlatform(context, initBean, initListener);
        sdkapi.init(context);
        LiveshowEngine.getInstance().setHasInited(true);
        LiveRadioEngine.getInstance().setHasInited(true);
        // 防止CP调用的顺序问题，多次设置
        if (switchAccountListener != null)
            sdkapi.setSwitchAccountListener(switchAccountListener);
        if (back2GameListener != null)
            sdkapi.setBackToGameLoginListener(back2GameListener);
        if (mScreenshotListener != null) {
            sdkapi.setScreenshotListener(mScreenshotListener);
        }

        handleInitPerformFeatureListeners();
        handleCallback();
    }


    /**
     * 获取游戏配置信息 包括：gameid
     *
     * @return
     */
    public SQAppConfig getAppConfig() {
        // 规避旧宿主加载新插件的类型转换问题, 所以封装一层, 确保返回的类继承ISQAppConfig
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        return new SQAppConfigImpl(config);
    }


    /**
     * @param event    事件名称，需要是英文缩写，请勿使用汉字，如: active
     * @param describe 事件描述说明，可以是UTF-8编码的任意字符，如"调用初始化"
     * @param params   事件参数，HashMap 的 key 即是事件 key, value 是 key 对应的值
     */
    public void track(String event, String describe, HashMap<String, String> params) {
        if (TextUtils.isEmpty(event)) return;
        if (TextUtils.isEmpty(describe)) {
            describe = "CP未命名自定义事件";
        }
        SqTrackAction2.sdk_expand.construct(event, describe);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_expand, params);
        LogUtil.i("调用上报事件: " + event);
    }

    /**
     * 登录接口
     *
     * @param loginListener
     */
    @Override
    public void login(final Context context,
                      final SQResultListener loginListener) {
        SQLog.d(TAG + "调用login");
        post(new Runnable() {
            @Override
            public void run() {
                if (!isInitSuccess) {
                    SQLog.e(TAG + "初始化成功之后，才能调用登录");
                    loginListener.onFailture(204, "初始化成功之后，才能调用登录");
                    return;
                }
                SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.DO_LOGIN);
                boolean needShowPopup = ActivePopupDialogManager.getInstance().needShowPopup();
                if (needShowPopup) {
                    SQLog.i(TAG + "需要显示登录弹窗");
                    ActivePopupDialogManager.getInstance().showPopupDialog(new OnDialogFinishListener() {
                        @Override
                        public void onFinishPopup() {
                            doLogin(context, loginListener);
                        }
                    });
                } else {
                    doLogin(context, loginListener);
                }
            }
        });

    }

    private void doLogin(final Context context,
                         final SQResultListener loginListener) {
        SQLog.d(TAG + "调用" + sdkapi.getClass().getSimpleName() + "#login");
        sdkapi.login(context, new SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                loginListener.onSuccess(bundle);
                MultiSDKUtils.setLogined(context, true);
                initBuglessUser();
                isLogin = true;
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.cp_login_succ);
                // 登录成功后清空之前的角色信息
                resetRoleInfo();
            }

            @Override
            public void onFailture(int code, String msg) {
                loginListener.onFailture(code, msg);
                HashMap<String, String> loginFailParams = new HashMap<>();
                loginFailParams.put("code", code + "");
                loginFailParams.put("msg", msg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.cp_login_fail, loginFailParams);
            }
        });
    }

    private void initBuglessUser() {
        com.sqwan.bugless.model.UserInfo userInfo = new com.sqwan.bugless.model.UserInfo();
        userInfo.setName(MultiSDKUtils.getUsername(context));
        userInfo.setId(MultiSDKUtils.getUserid(context));
        Bugless.getInstance().setUserInfo(userInfo);
    }

    /**
     * 详细支付接口(供CP调用)
     *
     * @param doid    CP订单ID (*必传)
     * @param dpt     CP商品名 (*必传)
     * @param dcn     CP货币名称 (*必传)
     * @param dsid    CP游戏服ID (*必传)
     * @param dsname  CP游戏服名称 (*必传)
     * @param dext    CP扩展回调参数 (*必传)
     * @param drid    CP角色ID (*必传)
     * @param drname  CP角色名 (*必传)
     * @param drlevel CP角色等级 (*必传)
     * @param dmoney  CP金额(定额) (*必传)
     * @param dradio  CP兑换比率(1元兑换率默认1:10) (*必传)
     */
    public void pay(final Context context, final String doid, final String dpt,
                    final String dcn, final String dsid, final String dsname,
                    final String dext, final String drid, final String drname,
                    final int drlevel, final float dmoney, final int dradio,
                    final SQResultListener payListener) {
        sendLog("调用了 BaseSQwanCore.pay");
        post(new Runnable() {
            @Override
            public void run() {
                StringBuilder payParams = new StringBuilder();
                payParams.append("  doid=" + doid + "\n");
                payParams.append("  dpt=" + dpt + "\n");
                payParams.append("  dcn=" + dcn + "\n");
                payParams.append("  dsid=" + dsid + "\n");
                payParams.append("  dsname=" + dsname + "\n");
                payParams.append("  dext=" + dext + "\n");
                payParams.append("  drid=" + drid + "\n");
                payParams.append("  drname=" + drname + "\n");
                payParams.append("  drlevel=" + drlevel + "\n");
                payParams.append("  dmoney=" + dmoney + "\n");
                payParams.append("  dradio=" + dradio + "\n");
                sendLogBase4CP("支付接口:" + payParams.toString().replace("\n", ""));
                sendLogBase4CP("请CP检查参数是否为空:\n" + payParams.toString());
                showTestToast("pay 接口调用 \n" + "请检查参数正确性：\n" + payParams.toString());
                if (sdkapi == null) {
                    sendLogBase4CP("初始化还未完成");
                    payListener.onFailture(205, "初始化还未完成");
                    return;
                }
                // 如果2.0.2或者2.1.2就过掉。
                if (initBean != null
                        && (initBean.getIsSDK202() == 1 || initBean
                        .getIsSDK210() == 1)) {
                    sendLog("当期版本不需要验证支付参数");
                } else {
                    // 检查参数是否为空
                    if (TextUtils.isEmpty(doid)
                            || TextUtils.isEmpty(dpt)
                            || TextUtils.isEmpty(dcn)
                            || TextUtils.isEmpty(dsid)
                            || TextUtils.isEmpty(dsname)
                            || TextUtils.isEmpty(drid)
                            || TextUtils.isEmpty(drname)
                            || TextUtils.isEmpty("" + drlevel)
                            || TextUtils.isEmpty("" + dmoney)
                            || TextUtils.isEmpty("" + dradio)) {
                        sendLog("pay方法提交的参数不能为空");
                        MultiSDKUtils.showTips(context, "pay方法提交的参数不能为空");
                        payListener.onFailture(205, "pay方法提交的参数不能为空");
                        return;
                    }
                }

                sdkapi.pay(context, doid, dpt, dcn, dsid, dsname, dext,
                        drid, drname, drlevel, dmoney, dradio,
                        new SQResultListener() {

                            @Override
                            public void onSuccess(Bundle bundle) {
                                LogUtil.i("收到了支付成功的回调：" + bundle);
                                // 传递成功
                                payListener.onSuccess(bundle);
                            }

                            @Override
                            public void onFailture(int code, String msg) {
                                LogUtil.w("收到了支付失败的回调：code=" + code + "， msg=" + msg);
                                payListener.onFailture(code, msg);
                            }
                        });
            }
        });

    }

    /**
     * 切换账号监听，返回与登录回调一样的用户信息 必须先调用初始化
     *
     * @param listener
     */
    public void setSwitchAccountListener(SQResultListener listener) {
        sendLogBase4CP("设置切换账号监听");
        sendLog("调用了 BaseSQwanCore.setSwitchAccountListener");
        showTestToast("悬浮窗-切换账号 \n setSwitchAccountListener 调用成功");
        switchAccountListener = listener;
        if (sdkapi != null) {
            sdkapi.setSwitchAccountListener(listener);
        }
    }

    /**
     * 回到游戏登录界面窗口
     *
     * @param listener
     */
    public void setBackToGameLoginListener(SQResultListener listener) {
        sendLogBase4CP("设置回到游戏登录监听");
        sendLog("调用了 BaseSQwanCore.setBackToGameLoginListener");
        showTestToast("悬浮窗-账户-注销登录 \n setBackToGameLoginListener 调用成功");
        back2GameListener = listener;
        if (sdkapi != null) {
            sdkapi.setBackToGameLoginListener(listener);
        }
    }

    public void showSQWebDialog(final String url) {
        sendLogBase4CP("打开37Web容器，url --> " + url);
        if (sdkapi != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    sdkapi.showSQWebDialog(url);
                }
            });
        }
    }


    public void showSQPersonalDialog(final Context context) {
        sendLogBase4CP("打开37实名制窗口");
        if (sdkapi != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    sdkapi.showSQPersonalDialog(context);
                }
            });
        }
    }

    /**
     * 游戏方主动切换账号，回调信息与登录回调一致
     *
     * @param context
     * @param listener
     */
    public void changeAccount(final Context context,
                              final SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.changeAccount");
        post(new Runnable() {
            @Override
            public void run() {
                sendLogBase4CP("主动调用切换账号");
                showTestToast("游戏内切换账号：changeAccount接口调用成功");
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_CP);
                HashMap<String, String> logoutMap = new HashMap<>();
                logoutMap.put(SqTrackKey.logout_type, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_CP);
                String loginType = AccountCache.getLoginType(context);
                logoutMap.put(SqTrackKey.login_type, loginType);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC, logoutMap);
                if (sdkapi != null) {
                    // 是否跳过登录框
                    if (Util.isSkipSQChangeAccountLogin(context)) {
                        listener.onSuccess(new Bundle());

                    } else {
                        sdkapi.changeAccount(context,
                            new SQResultListener() {

                                @Override
                                public void onSuccess(Bundle bundle) {
                                    listener.onSuccess(bundle);
                                    initBuglessUser();
                                    // 切换账号后清除以前的角色信息
                                    resetRoleInfo();
                                }

                                @Override
                                public void onFailture(int code,
                                    String msg) {
                                    listener.onFailture(code, msg);
                                }
                            });
                    }
                } else {
                    listener.onFailture(203, "初始化成功之后，才能调用登录");
                }
            }
        });


    }

    /**
     * 分享到微信
     * todo 插件sdk的SQWanCore中没有这个方法, 所以不放到分享api中
     *
     * @param context
     * @param title        分享的标题
     * @param description  分享的内容
     * @param shareUrl     跳转到WEB的网络URL地址
     * @param thumbUrl     分享的图片资源URL地址
     * @param resourceType 1:分享图片 0:分享WEB链接
     */
    public void shareToWX(Context context, String title, String description,
                          String shareUrl, String thumbUrl, int resourceType,
                          SQResultListener shareListener) {
        sendLog("调用了 BaseSQwanCore.shareToWX");
        Platform.shareListener = shareListener;
        Bundle shareBundle = new Bundle();
        Intent shareIntent = new Intent(context, WXEntryActivity.class);
        shareBundle.putString("title", title);
        shareBundle.putString("description", description);
        shareBundle.putString("shareUrl", shareUrl);
        shareBundle.putString("thumbUrl", thumbUrl);
        shareBundle.putInt("resourceType", resourceType);
        sendLog("分享参数为：" + shareBundle);
        shareIntent.putExtras(shareBundle);
        if (!(context instanceof Activity)) {
            shareIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(shareIntent);

    }

    /**
     * 分享，走后台配置
     *
     * @param inviteCode    邀请码
     * @param shareListener 分享回调
     */
    public void share(String inviteCode, SQResultListener shareListener) {
        share(inviteCode, "", shareListener);
    }

    /**
     * 分享，走后台配置，
     *
     * @param inviteCode    邀请码
     * @param img_id        图片id
     * @param shareListener 分享回调
     */
    public void share(String inviteCode, String img_id, SQResultListener shareListener) {
        DefaultShareInfo defaultShareInfo = new DefaultShareInfo();
        defaultShareInfo.setImgId(img_id);
        defaultShareInfo.setInviteCode(inviteCode);
        ShareMessage shareMessage = new ShareMessage();
        shareMessage.setShareMessage(defaultShareInfo);
        share(shareMessage, shareListener);
    }

    /**
     * 通用分享接口
     */
    public void share(final ShareMessage shareMessage, final SQResultListener shareListener) {
        sendLog("调用了 BaseSQwanCore.share");
        post(new Runnable() {
            @Override
            public void run() {
                IShareResultListener proxyShareResultListener = new IShareResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        if (shareListener == null) {
                            return;
                        }
                        shareListener.onSuccess(bundle);
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        if (shareListener == null) {
                            return;
                        }
                        shareListener.onFailture(code, msg);
                    }
                };

                if (!(shareMessage.getShareMessage() instanceof ShareImageInfo)) {
                    ModHelper.get(IShareMod.class).share(shareMessage, proxyShareResultListener);
                    return;
                }

                PermissionSimpleHelper.requestPermission(
                    PermissionSimpleHelper.STORAGE_PERMISSION,
                    PermissionSimpleHelper.STORAGE_PERMISSION_NAME,
                    PermissionSimpleHelper.STORAGE_PERMISSION_BY_SHARE, new OnPermissionCallback() {
                        @Override
                        public void onGranted() {
                            ModHelper.get(IShareMod.class).share(shareMessage, proxyShareResultListener);
                        }

                        @Override
                        public void onDenied() {
                            proxyShareResultListener.onFailture(1002, "分享图片失败，没有" + PermissionSimpleHelper.STORAGE_PERMISSION_NAME);
                        }
                    });
            }
        });
    }

    @Override
    public void log(int level, String msg) {
        SQLog.log(level, "sqsdk", "[LOG]" + msg, null);
    }

    /*
     * 退出游戏弹窗接口
     */
    public void showExitDailog(final Context context,
                               final SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.showExitDailog");
        post(new Runnable() {
            @Override
            public void run() {
                showTestToast("退出框接口showExitDialog调用成功");
                sendLogBase4CP("调用退出弹窗");
                logout(context, listener);
                SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.SHOW_EXIT_DIALOG);
            }
        });

    }

    /*
     * 退出游戏接口，有三种退出方式； 1.直接原生退出 2.平台有退出框 3.平台无退出框，调用安卓退出框
     */
    public void logout(final Context context,
                       final SQResultListener logoutListener) {
        sendLog("调用了 BaseSQwanCore.logout");
        post(new Runnable() {
            @Override
            public void run() {
                sendLog("退出框逻辑实现..logout");
                if (sdkapi != null) {
                    String code = MultiSDKUtils.getCodeOfLogin(context);
                    if (!TextUtils.isEmpty(code) && "1".equals(code)) {
                        // 截流就显示原生的退出框
                        showAndoridExit(context, logoutListener);
                    } else {
                        // 如果第三方有退出框，则直接使用
                        if (initBean != null && initBean.getUsePlatformExit() == 1) {
                            logoutPlatform(context, logoutListener);
                        } else {
                            // 如果第三方没有退出框，就调用原生的退出框
                            logoutPlatformWithAndroidExit(context, logoutListener);
                        }
                    }
                } else {
                    // 没有初始化完成，就调用原生退出框。
                    showAndoridExit(context, logoutListener);
                }
            }
        });

    }

    /**
     * 安卓原生的退出框
     *
     * @param logoutListener
     */
    private void showAndoridExit(final Context context,
                                 final SQResultListener logoutListener) {
        post(new Runnable() {
            @Override
            public void run() {
                new CommonAlertDialog.Builder(context)
                        .setTitle("您确定退出游戏吗？")
                        .setPositiveButton("确定", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                HashMap<String, String> logoutMap = new HashMap<>();
                                logoutMap.put(SqTrackKey.logout_type, "退出游戏");
                                String loginType = AccountCache.getLoginType(context);
                                logoutMap.put(SqTrackKey.login_type, loginType);
                                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC, logoutMap);
                                logoutListener.onSuccess(new Bundle());
                            }
                        })
                        .setNegativeButton("取消", null)
                        .show();
            }
        });
    }

    /**
     * 安卓原生退出，但是调用了第三方的logout
     *
     * @param logoutListener
     */
    private void logoutPlatformWithAndroidExit(final Context context,
                                               final SQResultListener logoutListener) {
        post(new Runnable() {
            @Override
            public void run() {
                new CommonAlertDialog.Builder(context)
                        .setTitle("您确定退出游戏吗？")
                        .setPositiveButton("确定", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                HashMap<String, String> logoutMap = new HashMap<>();
                                logoutMap.put(SqTrackKey.logout_type, "退出游戏");
                                String loginType = AccountCache.getLoginType(context);
                                logoutMap.put(SqTrackKey.login_type, loginType);
                                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC, logoutMap);
                                logoutPlatform(context, logoutListener);
                            }
                        })
                        .setNegativeButton("取消", null)
                        .show();
            }
        });

    }

    private void logoutPlatform(final Context context,
                                final SQResultListener logoutListener) {
        post(new Runnable() {

            @Override
            public void run() {
                sdkapi.logout(context, new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        sendLogBase4CP("调用退出游戏接口,退出成功..");
                        HashMap<String, String> logoutMap = new HashMap<>();
                        logoutMap.put(SqTrackKey.logout_type, "退出游戏");
                        String loginType = AccountCache.getLoginType(context);
                        logoutMap.put(SqTrackKey.login_type, loginType);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC, logoutMap);
                        logoutListener.onSuccess(bundle);
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        sendLogBase4CP("调用退出游戏接口,退出失败..");
                        logoutListener.onFailture(code, msg);
                    }
                });
            }
        });

    }

    // ——————————————————————————————————————工具相关————————————————————————————————

    /**
     * 检测请求的URL地址是否在服务器有更新
     *
     * @param urlString       服务器的URL地址
     * @param targetUrlString 本地端的URL地址
     */
    private void checkUrlNeedUpdate(String urlString, String targetUrlString) {

        // url不为空则需要更新请求的url
        if (!TextUtils.isEmpty(urlString)) {
            targetUrlString = urlString;
        }
    }

    // ——————————————————————————————————————提交角色信息————————————————————————————————

    @Override
    public void creatRoleInfo(final HashMap<String, String> infos) {
        sendLog("调用了 BaseSQwanCore.creatRoleInfo");
        sendLog("角色信息：" + infos);
        showRoleInfo(infos, 1);
        if (sdkapi != null) {
            sdkapi.creatRoleInfo(infos);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.role_add);
            SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.CREATE_ROLE_INFO, infos);
        }
    }

    /**
     * 升级角色接口
     */
    @Override
    public void upgradeRoleInfo(final HashMap<String, String> infos) {
        sendLog("调用了 BaseSQwanCore.upgradeRoleInfo");
        sendLog("角色信息：" + infos);
        showRoleInfo(infos, 3);
        if (sdkapi != null) {
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.role_level);
            SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.UPGRADE_ROLE_LEVEL, infos);
            sdkapi.upgradeRoleInfo(infos);
        }
    }

    private HashMap<String, String> roleTrackParam(HashMap<String, String> infos) {
        HashMap<String, String> roleParams = new HashMap<>();
        roleParams.put("role_id", infos.get(INFO_ROLEID));
        roleParams.put("role_name", infos.get(INFO_ROLENAME));
        roleParams.put("role_level", infos.get(INFO_ROLELEVEL));
        roleParams.put("vip_level", infos.get(INFO_VIPLEVEL));
        roleParams.put("server_id", infos.get(INFO_SERVERID));
        roleParams.put("server_name", infos.get(INFO_SERVERNAME));
        return roleParams;
    }

    private void setRoleInfos(final HashMap<String, String> infos) {
        BaseBean baseBean = new BaseBean();
        baseBean.roleId = infos.get(INFO_ROLEID);
        baseBean.roleLevel = infos.get(INFO_ROLELEVEL);
        baseBean.roleName = infos.get(INFO_ROLENAME);
        baseBean.serverId = infos.get(INFO_SERVERID);
        baseBean.serverName = infos.get(INFO_SERVERNAME);
        baseBean.vipLevel = infos.get(INFO_VIPLEVEL);
        CommonConfigs.getInstance().setBaseUserInfo(baseBean);
        ILiveshowManager liveshowEngine = LiveshowEngine.getInstance().getLiveshowManager();
        ILiveshowManager liveRadioEngine = LiveRadioEngine.getInstance().getLiveshowManager();
        if (liveshowEngine != null) {
            liveshowEngine.onSubmitRole();
        }
        if (liveRadioEngine != null) {
            liveRadioEngine.onSubmitRole();
        }
    }

    /**
     * 提交角色信息接口
     *
     * @param infos
     */
    public void submitRoleInfo(final HashMap<String, String> infos) {
        sendLog("调用了 BaseSQwanCore.submitRoleInfo");
        sendLog("角色信息：" + infos);
        showRoleInfo(infos, 2);
        //201709添加 在线客服
        Util.setRoleInfos(infos);
        ConfigManager.getInstance(context).setRoleInfo(infos);
        try {
            //2022/5/25 发现该方法异常，添加异常信息埋点用于线上用户分析
            setRoleInfos(infos);
        } catch (Throwable e) {
            e.printStackTrace();
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String errorInfo = sw.toString();
            HashMap<String, String> map = new HashMap<>();
            map.put(SqTrackKey.reason_fail, errorInfo);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.role_login_throwable, map);
        }
        if (sdkapi != null) {
            sdkapi.submitRoleInfo(infos);
            MultiSDKUtils.setServerid(context, infos.get(INFO_SERVERID));
            MultiSDKUtils.setRoleid(context, infos.get(INFO_ROLEID));
            MultiSDKUtils.setRolename(context, infos.get(INFO_ROLENAME));
            MultiSDKUtils.setRolelevel(context, infos.get(INFO_ROLELEVEL));
            MultiSDKUtils.setVipLevel(context, infos.get(INFO_VIPLEVEL));
            MultiSDKUtils.setServerName(context, infos.get(INFO_SERVERNAME));
            isSubmit = true;
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.role_login);
            SqTrackActionManager2.getInstance().flush();
        }
        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.SUBMIT_ROLE_INFO, infos);

        setUserInfoToPushService(infos.get(SQwanCore.INFO_ROLEID));
        sendLogBase4CP("调用提交角色信息接口");
        requestManager.submitRoleInfoRequst(infos,
                new MRequestCallBack() {

                    @Override
                    public void onRequestSuccess(String content) {
                        sendLog("37提交用户角色信息成功:" + content);
                        SubmitRolePopupDialogManager.getInstance().handlePopup(context, infos);
                    }

                    @Override
                    public void onRequestError(String errorMsg) {
                        sendLog("37提交用户角色信息失败:" + errorMsg);

                    }
                });

    }

    /**
     * @param info 角色数据
     * @param type 1 创建角色，2 进入服务器，3 角色升级
     */
    private void showRoleInfo(HashMap<String, String> info, int type) {
        if (initBean != null && initBean.getTestTag() == 1) {
            StringBuilder roleInfo = new StringBuilder();
            roleInfo.append(INFO_SERVERID + " = " + info.get(INFO_SERVERID) + "\n");
            roleInfo.append(INFO_SERVERNAME + " = " + info.get(INFO_SERVERNAME) + "\n");
            roleInfo.append(INFO_ROLEID + " = " + info.get(INFO_ROLEID) + "\n");
            roleInfo.append(INFO_ROLENAME + " = " + info.get(INFO_ROLENAME) + "\n");
            roleInfo.append(INFO_ROLELEVEL + " = " + info.get(INFO_ROLELEVEL) + "\n");
            roleInfo.append(INFO_BALANCE + " = " + info.get(INFO_BALANCE) + "\n");
            roleInfo.append(INFO_PARTYNAME + " = " + info.get(INFO_PARTYNAME) + "\n");
            roleInfo.append(INFO_VIPLEVEL + " = " + info.get(INFO_VIPLEVEL) + "\n");
            roleInfo.append(INFO_ROLE_TIME_CREATE + " = " + info.get(INFO_ROLE_TIME_CREATE) + "\n");
            roleInfo.append(INFO_ROLE_TIME_LEVEL + " = " + info.get(INFO_ROLE_TIME_LEVEL) + "\n");
            switch (type) {
                case 1:
                    showTestToast("创建角色 \n" + "请检查参数正确性：\n" + roleInfo.toString());
                    break;
                case 2:
                    showTestToast("进入服务器 \n" + "请检查参数正确性：\n" + roleInfo.toString());
                    break;
                case 3:
                    showTestToast("角色升级 \n" + "请检查参数正确性：\n" + roleInfo.toString());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 提交统计信息接口
     */
    public void submitStatisticsInfo(String key, String values) {
        sendLogBase4CP("调用提交统计信息接口");
        sendLog("调用了 BaseSQwanCore.submitStatisticsInfo");
        sendLog("key = " + key + ", values = " + values);
        requestManager.statisticsRequst(key, values, new MRequestCallBack() {

            @Override
            public void onRequestSuccess(String content) {

            }

            @Override
            public void onRequestError(String errorMsg) {

            }
        });

    }

    /**
     * 创建角色接口---不建议使用
     *
     * @param serverId 区服ID
     */
    public void creatRole(Context context, String serverId) {
        if (sdkapi != null) {
            sdkapi.creatRole(context, serverId);
        }
    }

    // ——————————————————————————————————————生命周期————————————————————————————————
    @Override
    public void onStart() {
        LogUtil.v("调用了 BaseSQwanCore.onStart");
        showTestToast("生命周期检查：\nonStart 调用成功");
        if (sdkapi != null) {
            sdkapi.onStart();
        }
    }

    @Override
    public void onRestart() {
        LogUtil.v("调用了 BaseSQwanCore.onRestart");
        showTestToast("生命周期检查：\nonRestart 调用成功");
        if (sdkapi != null) {
            sdkapi.onRestart();
        }
    }

    @Override
    public void onResume() {
        LogUtil.v("调用了 BaseSQwanCore.onResume");
        showTestToast("生命周期检查：\nonResume 调用成功");
        if (sdkapi != null) {
            sdkapi.onResume();
        }
        DiagnosticSensorManager.getInstance().onResume();
    }

    @Override
    public void onPause() {
        LogUtil.v("调用了 BaseonPause");
        showTestToast("生命周期检查：\nonPause 调用成功");
        if (sdkapi != null) {
            sdkapi.onPause();
        }
        DiagnosticSensorManager.getInstance().onPause();
    }

    @Override
    public void onStop() {
        LogUtil.v("调用了 BaseSQwanCore.onStop");
        showTestToast("生命周期检查：\nonStop 调用成功");
        if (sdkapi != null) {
            sdkapi.onStop();
        }
    }

    @Override
    public void onDestroy() {
        LogUtil.w("调用了 BaseSQwanCore.onDestroy");
        showTestToast("生命周期检查：\nonDestroy 调用成功");
        if (sdkapi != null) {
            sdkapi.onDestroy();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        LogUtil.d("调用了 BaseSQwanCore.onActivityResult");
        LogUtil.d("requestCode = " + requestCode + "，resultCode = " + resultCode + "，data" + data);
        showTestToast("生命周期检查：\nonActivityResult 调用成功");
        if (requestCode == SETTING_REQUEST_CODE) {
            SQwanCore.sendLogNoDebug("设置页回调");
            AuthHandler.getInstance().checkPermission(false, true);
        }
        if (requestCode == SettingHelper.SETTING_REQUEST_CODE_FLOAT) {
            SettingHelper.getInstance((Activity) context).onActivityResult(requestCode, resultCode, data);
        }
        if (sdkapi != null) {
            sdkapi.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onNewIntent(Intent intent) {
        sendLog("调用了 BaseonNewIntent");
        showTestToast("生命周期检查：\nonNewIntent 调用成功");
        if (sdkapi != null) {
            sdkapi.onNewIntent(intent);
        }
        IPushMod pushMod = ModHelper.get(IPushMod.class);
        if (context != null && pushMod != null) {
            pushMod.onNewIntent((Activity) context, intent);
        }
        if (context != null && intent != null) {
            MMOBindingUtils.handlerBinding((Activity) context, intent.getStringExtra(MMOBindingUtils.BINDING_PARAMS));
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        sendLog("调用了 BaseSQwanCore.onRequestPermissionsResult");
        sendLog("调用了 requestCode = " + requestCode + ", permissions = " +
            Arrays.toString(permissions) + "，grantResults = " + Arrays.toString(grantResults));

        sendLogNoDebug("权限申请回调：" + requestCode);
        showTestToast("权限申请回调接口接入");
        HashMap<String, String> permissionMap = new HashMap<>();
        permissionMap.put("requestCode", requestCode + "");
        permissionMap.put("permissions", Arrays.toString(permissions));
        permissionMap.put("grantResults", Arrays.toString(grantResults));
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.request_permission_result, permissionMap);
        if (requestCode != SQ_REQUEST_PERMISSION_CODE) {
            return;
        }
        PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        LogUtil.v("调用了 BaseSQwanCore.onConfigurationChanged");
    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        LogUtil.v("调用了 BaseSQwanCore.onWindowFocusChanged，hasFocus = " + hasFocus);
    }

    public void reportMDev(String identify) {
        HashMap<String, String> map = new HashMap<>();
        map.put("oaid", identify);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_oaid, map);
        try {
            JSONObject obj = new JSONObject(identify);
            String oaid = obj.optString("oaid");
            //oaid若为空则不上报
            if (TextUtils.isEmpty(oaid)) {
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.info("获取oaid出错");
        }
        String spIdentify = MultiSDKUtils.getMDevIds(context);
        if (!identify.equals(spIdentify)) {
            if (requestManager == null) {
                requestManager = new MRequestManager(context);
            }
            requestManager.reportDev(context, identify);
        }
    }

    /**
     * 设置当前context，解决游戏有2个activiyty的情况
     *
     * @param cxt
     */
    public void setContext(Context cxt) {

        this.context = cxt;
        if (sdkapi != null) {
            sdkapi.setContext(cxt);
        }

        if (requestManager != null) {
            requestManager.setContext(cxt);
        }

    }

    /**
     * 设置自检工具开关
     *
     * @param stat
     */
    public void setDebug(Boolean stat) {
        isCheckOn = stat;
    }

    /**
     * 显示测试提示信息
     *
     * @param msg
     */
    public void showTestToast(final String msg) {
        String toastMsg = "母包检查：\n" + msg;
        if (initBean != null && initBean.getTestTag() == 1) {
            if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
                ToastUtil.showToast(context, toastMsg);
            } else {
                ((Activity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.showToast(context, msg + " --> 非主线程调用");
                    }
                });
            }
        }
    }

    /**
     * 打印日志
     *
     * @param logString
     */
    public static void sendLog(String logString) {
        LogUtil.w(logString);
    }

    public static void sendLogNoDebug(String logString) {
        LogUtil.d(logString);
    }

    public static void sendLog(String logString, int ecode) {
        LogUtil.e("CODE:" + ecode + " " + logString);
    }

    public static void sendLogBase4CP(String log) {

        if (!TextUtils.isEmpty(debug4cp) && "1".equals(debug4cp)) {
            System.out.println("-->" + log);
        }
        sendLog("BaseSQwanCore-->" + log);
    }

    @Deprecated
    public static void sendLogPlat4CP(String log) {

        if (!TextUtils.isEmpty(debug4cp) && "1".equals(debug4cp)) {
            System.out.println("-->" + log);
        }
        sendLog("Platform-->" + log);
    }

    // ——————————————————————————————————————语音接口————————————————————————————————

    /**
     * 实例化语音接口
     */
    public void speechInit(Context context, final SQResultListener listener) {
        sendLogBase4CP("初始化语音接口");
        if (sdkapi != null) {
            sdkapi.speechInit(context, new SQResultListener() {

                @Override
                public void onSuccess(Bundle bundle) {
                    sendLogBase4CP("初始化语音接口成功");
                    listener.onSuccess(bundle);
                }

                @Override
                public void onFailture(int code, String msg) {
                    sendLogBase4CP("初始化语音接口失败");
                    listener.onFailture(code, msg);
                }
            });
        }
    }

    public void setServerInfo(String url) {
        sendLogBase4CP("设置海外服务器配置");
        if (sdkapi != null) {
            sdkapi.setServerInfo(url);
        }
    }

    public void poll() {
        sendLogBase4CP("回调函数驱动－主循环");
        if (sdkapi != null) {
            sdkapi.poll();
        }
    }

    public void joinNationalRoom(String roomName, int role, int msTimeout) {
        sendLogBase4CP("加入国战房间");
        if (sdkapi != null) {
            sdkapi.joinNationalRoom(roomName, role, msTimeout);
        }
    }

    public void joinTeamRoom(String roomName, int msTimeout) {
        sendLogBase4CP("加入小组房间");
        if (sdkapi != null) {
            sdkapi.joinTeamRoom(roomName, msTimeout);
        }
    }

    public void quitRoom(String roomName, int msTimeout) {
        sendLogBase4CP("退出房间");
        if (sdkapi != null) {
            sdkapi.quitRoom(roomName, msTimeout);
        }
    }

    public void openMic() {
        sendLogBase4CP("打开麦克风");
        if (sdkapi != null) {
            sdkapi.openMic();
        }
    }

    public void closeMic() {
        sendLogBase4CP("关闭麦克风");
        if (sdkapi != null) {
            sdkapi.closeMic();
        }
    }

    public void openSpeaker() {
        sendLogBase4CP("打开扬声器");
        if (sdkapi != null) {
            sdkapi.openSpeaker();
        }
    }

    public void closeSpeaker() {
        sendLogBase4CP("关闭扬声器");
        if (sdkapi != null) {
            sdkapi.closeSpeaker();
        }
    }

    public void setMicLevel(int paramInt) {
        sendLogBase4CP("设置麦克风音量");
        if (sdkapi != null) {
            sdkapi.setMicLevel(paramInt);
        }
    }

    public int getMicLevel() {
        sendLogBase4CP("获取麦克风音量");
        if (sdkapi != null) {
            return sdkapi.getMicLevel();
        }
        return 0;
    }

    public void setSpeakerVolume(int paramInt) {
        sendLogBase4CP("设置扬声器音量");
        if (sdkapi != null) {
            sdkapi.setSpeakerVolume(paramInt);
        }
    }

    public int getSpeakerVolume() {
        sendLogBase4CP("获取扬声器音量");
        if (sdkapi != null) {
            return sdkapi.getSpeakerVolume();
        }
        return 0;
    }

    public boolean testMic() {
        sendLogBase4CP("测试麦克风是否可用");
        if (sdkapi != null) {
            return sdkapi.testMic();
        }
        return false;
    }

    public void enableSpeakerOn(boolean paramBoolean) {
        sendLogBase4CP("是否打开扬声器");
        if (sdkapi != null) {
            sdkapi.enableSpeakerOn(paramBoolean);
        }
    }

    public void forbidMemberVoice(int paramInt, boolean paramBoolean) {
        sendLogBase4CP("禁止某成员语音");
        if (sdkapi != null) {
            sdkapi.forbidMemberVoice(paramInt, paramBoolean);
        }
    }

    /**
     * 进入房间回调接口
     */
    public void onJoinRoomListener(Context context, final SQResultListener listener) {
        sendLogBase4CP("进入房间回调接口");
        if (sdkapi != null) {
            sdkapi.onJoinRoomListener(context, listener);
        }
    }

    /**
     * 退出房间回调接口
     */
    public void onQuitRoomListener(Context context, final SQResultListener listener) {
        sendLogBase4CP("退出房间回调接口");
        if (sdkapi != null) {
            sdkapi.onQuitRoomListener(context, listener);
        }
    }

    /**
     * 成员状态回调-当房间中的其他成员开始说话或者停止说话的时候，
     * 通过该回调进行通知
     */
    public void onMemberVoiceListener(Context context, final SQResultListener listener) {
        sendLogBase4CP("成员状态回调接口");
        if (sdkapi != null) {
            sdkapi.onMemberVoiceListener(context, listener);
        }
    }

    /**
     * 断网之后的回调－断网三分钟之后触发
     */
    public void onStatusUpdateListener(Context context, final SQResultListener listener) {
        sendLogBase4CP("掉线回调接口");
        if (sdkapi != null) {
            sdkapi.onStatusUpdateListener(context, listener);
        }
    }


    @Override
    public void performFeatureBBS() {
        sendLog("调用了 BaseSQwanCore.performFeatureBBS");
        sendLogBase4CP("应用宝BBS论坛");
        if (sdkapi != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    sdkapi.performFeatureBBS();
                }
            });

        }
    }

    @Override
    public void performFeatureVPlayer() {
        sendLog("调用了 BaseSQwanCore.performFeatureVPlayer");
        sendLogBase4CP("应用宝V+特权");
        if (sdkapi != null) {
            sdkapi.performFeatureVPlayer();
        }
    }

    @Override
    public void performFeature(final Context context, final String type, final Object data, final SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.performFeature");
        sendLog("type = " + type + "，data = " + data);
        sendLogBase4CP("调用扩展接口");
        handleUninitPlatformPerformFeatureListeners(type, data, listener);
        if (sdkapi != null) {
            try {
                post(new Runnable() {
                    @Override
                    public void run() {
                        sdkapi.performFeature(context, type, data, listener);
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean isSupportPlugin() {
        sendLog("调用了 BaseSQwanCore.isSupportPlugin");
        return false;
    }

    /**
     * 设置截图监听
     *
     * @param listener
     */
    @Override
    public void setScreenshotListener(IScreenshotListener listener) {
        sendLog("调用了 BaseSQwanCore.setScreenshotListener");
        sendLogBase4CP("设置游戏中截图回调");
        mScreenshotListener = listener;
    }

    @Override
    public void showUAgreement(final Context context) {
        sendLog("调用了 BaseSQwanCore.showUAgreement");
        if (sdkapi != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    sdkapi.showUAgreement(context);
                }
            });

        }
        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.SHOW_USER_AGREEMENT);
    }

    @Override
    public void printLog(int level, String tag, String content) {
        String realLogTag = "[cp] " + tag;
        if (level == LOG_LEVEL_DEBUG) {
            SQLog.dt(realLogTag, content);
        } else if (level == LOG_LEVEL_INFO) {
            SQLog.it(realLogTag, content);
        } else if (level == LOG_LEVEL_WARN) {
            SQLog.wt(realLogTag, content);
        } else if (level == LOG_LEVEL_ERROR) {
            SQLog.et(realLogTag, content);
        }
    }

    private void handleCallback() {
        LiveshowEngine.getInstance().handleCallback();
        LiveRadioEngine.getInstance().handleCallback();
    }

    private void post(Runnable runnable) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            mHandler.post(runnable);
        }
    }

    private void handleUninitPlatformPerformFeatureListeners(String type, Object data, SQResultListener listener) {
        if (!ClassCheckUtils.isExistPerformFeatureConfig()) {
            return;
        }
        if (sdkapi == null) {
            if (listener != null) {
                PerformFeature.map.put(type, new PerformFeature(type, data, listener));
            }
        }

    }

    private void handleInitPerformFeatureListeners() {
        if (!ClassCheckUtils.isExistPerformFeatureConfig()) {
            sendLog("handleInitPerformFeatureListeners return");
            return;
        }
        Iterator<String> iterator = PerformFeature.map.keySet().iterator();
        while (iterator.hasNext()) {
            String type = iterator.next();
            PerformFeature performFeature = PerformFeature.map.get(type);
            if (performFeature.sqResultListener != null) {
                if (sdkapi != null) {
                    sdkapi.performFeature(context, performFeature.type, performFeature.data, performFeature.sqResultListener);
                }
            }

        }
        PerformFeature.map.clear();
    }

    private void setAuthCheck(boolean isAuthCheck) {
        this.isAuthCheck = isAuthCheck;
        SensitiveInfoManager.getInstance().setAuthCheck(isAuthCheck);
        MacLogic.getInstance(context).setAuthCheck(isAuthCheck);
        DevLogic.getInstance(context).setAuthCheck(isAuthCheck);
        ImeiLogic.getInstance(context).setAuthCheck(isAuthCheck);
        RootLogic.getInstance(context).setAuthCheck(isAuthCheck);
        SimulatorLogic.getInstance(context).setAuthCheck(isAuthCheck);
        SqTrackActionManager2.getInstance().setAuthCheck(isAuthCheck);
    }

    @Override
    public boolean isSupportLiveVideo() {
        sendLog("调用了 BaseSQwanCore.isSupportLiveVideo");
        return LiveshowEngine.getInstance().isSupportLiveVideo();
    }

    @Override
    public void joinLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.joinLiveshowRoom");
        LiveshowEngine.getInstance().joinLiveshowRoom(data, listener);
    }

    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.leaveLiveshowRoom");
        LiveshowEngine.getInstance().leaveLiveshowRoom(data, listener);
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.setLiveshowDestroyCallback");
        LiveshowEngine.getInstance().setLiveshowDestroyCallback(listener);
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.setLiveshowVoiceChangeCallback");
        LiveshowEngine.getInstance().setLiveshowVoiceChangeCallback(listener);
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.performLiveshowFeature");
        LiveshowEngine.getInstance().performLiveshowFeature(data, listener);
    }

    @Override
    public boolean isSupportLiveRadio() {
        sendLog("调用了 BaseSQwanCore.isSupportLiveRadio");
        return LiveRadioEngine.getInstance().isSupportLiveRadio();
    }

    @Override
    public void joinLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.joinLiveRadioRoom");
        LiveRadioEngine.getInstance().joinLiveRadioRoom(data, listener);
    }

    @Override
    public void leaveLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.leaveLiveRadioRoom");
        LiveRadioEngine.getInstance().leaveLiveRadioRoom(data, listener);
    }

    @Override
    public void setLiveRadioDestroyCallback(SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.setLiveRadioDestroyCallback");
        LiveRadioEngine.getInstance().setLiveRadioDestroyCallback(listener);
    }

    @Override
    public void setLiveRadioVoiceChangeCallback(SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.setLiveRadioVoiceChangeCallback");
        LiveRadioEngine.getInstance().setLiveRadioVoiceChangeCallback(listener);
    }

    @Override
    public void performLiveRadioFeature(Map<String, String> data, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.performLiveRadioFeature");
        LiveRadioEngine.getInstance().performLiveRadioFeature(data, listener);
    }

    @Override
    public void showAdReward(Context context, String params, SQResultListener listener) {
        sendLog("调用了 BaseSQwanCore.showAdReward");
        IAdvertiseMod adMod = ModHelper.get(IAdvertiseMod.class);
        if (adMod != null) {
            try {
                adMod.showAdvertiseReward(context, params, listener);
            } catch (Error e) {
                LogUtil.e("广告模块showAdReward异常 " + e.getMessage());
            }
        }
    }

    @Override
    public void setSQPushTransmitMessageListener(SQPushTransmitMessageListener listener) {
        IPushMod pushMod = ModHelper.get(IPushMod.class);
        if (pushMod != null) {
            pushMod.setTransmitMessageListener(json -> {
                if (listener != null) {
                    listener.onReceiveTransmitMessage(json);
                }
            });
        }
    }

    public void sendFeedbackSQPushMessage(@NonNull Context context, @Nullable Map<String, String> params) {
        IPushMod pushMod = ModHelper.get(IPushMod.class);
        if (pushMod != null) {
            pushMod.sendFeedback(context, params);
        }
    }

    private void setUserInfoToPushService(String roleId) {
        String uid = ModHelper.get(IAccountMod.class).getUid();
        IPushMod pushMod = ModHelper.get(IPushMod.class);
        if (pushMod != null) {
            pushMod.setUserInfo(uid, roleId);
        }
    }

    private void resetRoleInfo(){
        MultiSDKUtils.setServerid(context, "");
        MultiSDKUtils.setRoleid(context, "");
        MultiSDKUtils.setRolename(context,"");
        MultiSDKUtils.setRolelevel(context, "");
        MultiSDKUtils.setVipLevel(context, "");
        MultiSDKUtils.setServerName(context, "");
    }

    private void initToken(){
        AccountCache.setToken(context, "");
    }
}