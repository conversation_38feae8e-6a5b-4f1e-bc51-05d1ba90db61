package com.sqwan.msdk;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.Log;
import com.plugin.standard.BaseApplication;
import com.sq.oaid.sq_oaid.SqOAIDHelper;
import com.sq.sdk.tool.util.SpUtils;
import com.sq.tool.logger.AndroidLogAdapter;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.data.cache.SpRequestInfo;
import com.sqwan.common.track.SqTrackActionManager;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.SQAppApi;
import com.sqwan.msdk.api.SQMediaReportInterface;
import com.sqwan.msdk.api.SQReportInterface;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sqwan.msdk.utils.SqAtyLifecycle;
import com.sy37sdk.account.binding.GameBindingManager;
import com.sy37sdk.utils.Util;
import java.util.Properties;

/**
 * 注意: 这个类名不能改, 插件宿主通过这个类名来反射
 *
 * <AUTHOR>
 * @date 2019/3/25
 */
@SuppressWarnings("unused")
public class SQApplicationImpl extends BaseApplication implements SQAppApi {

    final String TAG = "【" + getClass().getSimpleName() + "】";

    @Override
    public void onCreate() {
        super.onCreate();
        SQLog.replaceLogAdapter(new AndroidLogAdapter("sqsdk") {
            @Override
            public boolean isLoggable(int priority, @Nullable String tag) {
                return Util.isLogPrintEnable();
            }
        }, AndroidLogAdapter.class);
        SQLog.i(TAG + "onCreate, " + getClass().getClassLoader());
        // 插件模式下, 要注入宿主context, 所以重新初始化一次
        SQContextWrapper.init(getApplicationContext());

        SpUtils.getInstance().setContext(getApplicationContext());
        SpRequestInfo.setRequestLiveId(getApplicationContext());
        SQReportCore.getInstance().init(getApplicationContext());
        BuglessAction.init(getApplicationContext());
        ActivityLifeCycleUtils.getInstance().init(getApplicationContext());
        SqAtyLifecycle.onApplication(getApplicationContext());
        MultiSDKUtils.setLogined(getApplicationContext(), false);
        SqTrackActionManager.getInstance().init(getApplicationContext(), VersionUtil.sdkVersion);
        SqTrackActionManager2.getInstance().init(getApplicationContext());
        GameBindingManager.getInstance().init(getApplicationContext());
    }

    @Override
    public void setReporter(SQReportInterface reporter) {
        SQReportCore.getInstance().setReporter(reporter);
    }

    @Override
    public void setMediaReporter(SQMediaReportInterface reporter) {
        SQReportCore.getInstance().setMediaReporter(reporter);
    }

    @Override
    public void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        Log.i("sqsdk", TAG + "attachBaseContext: " + base);
        //解析 multi_sdk 配置文件， 需要放在attachBaseContext 这个生命周期
        MultiSdkManager.getInstance().initMultiSdk(base);
        SQContextWrapper.init(base);
        initOAIDLib(base);
    }

    private void initOAIDLib(Context base) {
        try {
            Properties sdkInfo = MultiSDKUtils.readPropertites(base, MultiSdkManager.getInstance().getInfo());
            if (sdkInfo != null) {
                //设置出包时候的sdk版本号
                String originalVersion =
                    sdkInfo.getProperty("sdkverion") == null ? "" : sdkInfo.getProperty("sdkverion");
                String oriVersionReplace = originalVersion.replace(".", "");
                if (oriVersionReplace.length() > 4) {
                    //兼容小版本的版本号处理
                    oriVersionReplace = oriVersionReplace.substring(0, 4);
                }
                int originalVersionInt = Integer.parseInt(oriVersionReplace);
                if (originalVersionInt >= 3791) {
                    //3791以前的宿主会在多进程场景下的复制so环节，多次复制，导致OAID库崩溃，这里特殊兼容
                    //在宿主为3791版本及以上才初始化oaid
                    SqOAIDHelper.init();
                } else {
                    Log.e("sqsdk", TAG + "attachBaseContext: initOAIDLib 老版本不可以用新库获取OAID" );
                }
            }
        } catch (Exception e) {
            Log.e("sqsdk", TAG + "attachBaseContext: initOAIDLib " + e.getMessage());
        }

    }
}
