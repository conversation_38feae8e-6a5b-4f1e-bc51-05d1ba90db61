package com.sqwan.msdk.utils;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.sdk.Platform;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;


/**
 * Created by fengshan2030 on 16/11/23.
 * 管理社会化组件分享的功能类
 */

public class ShareUtil {

	private static final int TIMELINE_SUPPORTED_VERSION = 0x21020001;
	private static final int THUMB_SIZE = 150;

	private static Bitmap bmp;
	private static String handlerImageUrl;
	private static IWXAPI handlerWXAPI;
	private static int handlerWXType;
	private static int handlerResourceType;
	private static WXMediaMessage handlerMSG = null;
	private static String handlerTitle;
	private static String handlerDescription;
	private static String handlerWebUrl;
	private static  SendMessageToWX.Req handlerReq = new SendMessageToWX.Req();
	private static Context wxContext;

	private static ProgressDialog wxProgree = null;

	//IWXAPI 是第三方app和微信通信的openapi接口
	private static String wxAppid;
	private static String title,description,shareUrl,thumbUrl;
	private static int resourceType,wxType;
	private static Bundle shareBundle;

	private AlertDialog.Builder builder;
	private static boolean isFnish = false;
	private static  final int WX_RES_CODE_SUCCSE = 0;//发送成功
	private static final int WX_RES_CODE_FAILTERE = 1;//发送失败

	/**微信朋友圈*/
	private static final int WXSCEN_ETIMELINE = 1;
	/**微信好友*/
	private static final int WXSCENE_SESSION = 0;

	/**wxsharedialog*/
	private Dialog wxShareDialog;
	private static  boolean isHidingByMethod = false;//是否是系统调用隐藏

	private static PopupWindow wxPopupWindow;

	private static int share_wx_layout = 0;
	private static RelativeLayout fatherView;


	public static View onCreate(Context context,Bundle bundle ){
		wxContext = context;
		shareBundle = bundle;
		isFnish = false;
		ApplicationInfo info;
		View wxEntryView = null;
		try {
			info = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);

			wxAppid = info.metaData.getString("wxappid");
			// 通过WXAPIFactory工厂，获取IWXAPI的实例
			handlerWXAPI = WXAPIFactory.createWXAPI(context, wxAppid, false);
			handlerWXAPI.handleIntent(((Activity) context).getIntent(),(IWXAPIEventHandler) context);

			int wxEntryId = MultiSDKUtils.getIdByName("sy37_wxentry_main",
					"layout", context.getPackageName(), context);
			int wxEntryLayoutId = MultiSDKUtils.getIdByName("wxentry_main_id",
					"id", context.getPackageName(), context);

			LayoutInflater layoutInflater = (LayoutInflater) context
					.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
			wxEntryView = layoutInflater.inflate(wxEntryId, null);

			fatherView = (RelativeLayout) wxEntryView.findViewById(wxEntryLayoutId);

		} catch (NameNotFoundException e) {
			e.printStackTrace();
		}

		return wxEntryView;

	}

	public static void onWindowFocusChanged(Context context){
		wxContext = context;
		handler.sendEmptyMessageAtTime(0,200);
	}

	private static Handler handler = new Handler(){
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			switch (msg.what){
				case 0:
					showWXSharePopupWindow(wxContext);
					break;
				default:
					break;
			}
		}
	};


	/**
	 * 微信分享的选择弹窗
	 * @param context
	 */
	private static void showWXSharePopupWindow( final Context context){

		isHidingByMethod = false;
		LogUtil.w("PopupWindow为空，初始化");
		int styleID = MultiSDKUtils.getIdByName("sy37_mypopwindow_anim_style", "style", context.getPackageName(), context);
		int fatherID = MultiSDKUtils.getIdByName("sy37_share_wx_dialog", "layout", context.getPackageName(), context);
		int wxSessionID = MultiSDKUtils.getIdByName("share_wx_sessions", "id", context.getPackageName(), context);
		int wxOnlineID = MultiSDKUtils.getIdByName("share_wx_online", "id", context.getPackageName(), context);
		share_wx_layout = MultiSDKUtils.getIdByName("share_wx_view", "id", context.getPackageName(), context);

		final View popupView = ((Activity) context).getLayoutInflater().inflate(fatherID, null);

		wxPopupWindow = new PopupWindow(popupView, LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT, true);
		wxPopupWindow.setTouchable(true);
		wxPopupWindow.setOutsideTouchable(true);
		wxPopupWindow.setBackgroundDrawable(new BitmapDrawable(context.getResources(), (Bitmap) null));
		wxPopupWindow.getContentView().setFocusableInTouchMode(true);
		wxPopupWindow.getContentView().setFocusable(true);
		wxPopupWindow.setAnimationStyle(styleID);

		LinearLayout shareLayout = (LinearLayout) popupView.findViewById(share_wx_layout);
		shareLayout.getBackground().setAlpha(80);

		wxPopupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
			@Override
			public void onDismiss() {
				if(!isHidingByMethod) {
					Platform.shareListener.onFailture(203, "用户主动取消分享");
				}
				hideLoginDialog();
				finishActivity(context);
			}
		});

		popupView.setOnTouchListener(new View.OnTouchListener() {// 如果触摸位置在窗口外面则销毁

			@Override
			public boolean onTouch(View v, MotionEvent event) {
				int height = popupView.findViewById(share_wx_layout).getTop();
				int y = (int) event.getY();
				if (event.getAction() == MotionEvent.ACTION_UP) {
					if (y < height) {
						wxPopupWindow.dismiss();
					}
				}
				return true;
			}
		});

		ImageView wx_share_session = (ImageView)popupView.findViewById(wxSessionID);
		ImageView wx_share_online = (ImageView)popupView.findViewById(wxOnlineID);

		wx_share_online.setOnClickListener(new View.OnClickListener() {//微信朋友圈
			@Override
			public void onClick(View arg0) {
				hideLoginDialog();
				sendToWX(shareBundle,WXSCEN_ETIMELINE,context);
			}
		});
		wx_share_session.setOnClickListener(new View.OnClickListener() {//微信好友
			@Override
			public void onClick(View arg0) {
				hideLoginDialog();
				sendToWX(shareBundle,WXSCENE_SESSION,context);
			}
		});

		System.out.println("微信弹窗fatherView="+fatherView);
		System.out.println("isFnish="+isFnish);

		if(!isFnish){
			wxPopupWindow.showAtLocation(fatherView,Gravity.BOTTOM|Gravity.CENTER_HORIZONTAL, 0, 0);
		}

	}

	/**
	 * 获取微信分享的参数
	 * @param shareBundle
	 * @param wxType	1:微信朋友圈   0:微信好友
	 */
	private static void sendToWX(Bundle shareBundle ,int wxType,Context context){

		title = shareBundle.getString("title");
		description = shareBundle.getString("description");
		shareUrl = shareBundle.getString("shareUrl");
		thumbUrl = shareBundle.getString("thumbUrl");
		resourceType = shareBundle.getInt("resourceType");
		ShareUtil.shareWXLine(context,handlerWXAPI,title,description,shareUrl,thumbUrl,resourceType,wxType);

	}

	/**
	 * 取消微信分享的提示框
	 */
	private static void hideLoginDialog(){

		isHidingByMethod = true;
		if(wxPopupWindow != null && wxPopupWindow.isShowing()){
			wxPopupWindow.dismiss();
		}
	}

	private static  void finishActivity(Context context){
		isFnish = true;
		((Activity) context).finish();
	}

	public static void onResp(int code,Context context){

		switch (code) {
			case BaseResp.ErrCode.ERR_OK:
				onRespMessage(WX_RES_CODE_SUCCSE,"成功",context);
				break;
			case BaseResp.ErrCode.ERR_USER_CANCEL:
				onRespMessage(WX_RES_CODE_FAILTERE,"取消发送",context);
				break;
			case BaseResp.ErrCode.ERR_AUTH_DENIED:
				onRespMessage(WX_RES_CODE_FAILTERE,"发送被拒绝",context);
				break;
			default:
				onRespMessage(WX_RES_CODE_FAILTERE,"发送失败",context);
				break;

		}

	}


	/**
	 * 处理微信返回状态
	 * @param code
	 * @param message
	 */
	private static void onRespMessage(int code, String message,Context context){

		if(code == WX_RES_CODE_SUCCSE){
			if(null != Platform.shareListener){
				Platform.shareListener.onSuccess(new Bundle());
			}
		}else{
			if(null != Platform.shareListener){
				Platform.shareListener.onFailture(203,message);
			}
		}
		finishActivity(context);

	}


	/**
	 * 分享到微信
	 * @param context
	 * @param api
	 * @param title         分享的文字标题
	 * @param description   分享的文字内容
	 * @param webUrl        跳转到WEB的链接地址
	 * @param imageUrl      分享的图片URL地址
	 * @param resourceType  1:分享图片   0:分享WEB链接
	 * @param wxType        1:微信朋友圈   0:微信好友
	 */
	public static void shareWXLine(Context context,  IWXAPI api,  String title,  String description,
								   String webUrl,  String imageUrl,  int resourceType , int wxType){
		if(description.length()>1024){description = description.substring(0,1024);}
		if(title.length() > 512){title = title.substring(0,512);}

		wxContext = context;
		int wxSdkVersion = api.getWXAppSupportAPI();
		handlerImageUrl = imageUrl;
		handlerWXAPI = api;
		handlerWXType = wxType;
		handlerResourceType = resourceType;
		handlerDescription = description;
		handlerTitle = title;
		handlerWebUrl = webUrl;
		//支持朋友圈分享
		if (wxSdkVersion >= TIMELINE_SUPPORTED_VERSION) {
			try {

				if(!handlerImageUrl.startsWith("http")){//如果是本地地址
					sendToSDCardResource(api,imageUrl,wxType);
				}else{
					if(isValidContext(context)) {
						System.out.println("加载中。。。");
						wxProgree = new ProgressDialog(context);
						wxProgree.setMessage("加载中请稍后!");
						wxProgree.show();
					}
					new Thread(netRunnable).start();
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			ViewUtils.showToast(context,"当前微信版本不支持分享,请下载最新版本的微信");
		}

	}

	private static Handler wxHandler = new Handler(Looper.getMainLooper()){
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);

			if(msg.arg1==0){

				if(handlerResourceType==0){
					WXWebpageObject webpage = new WXWebpageObject();
					webpage.webpageUrl = handlerWebUrl;
					handlerMSG = new WXMediaMessage(webpage);
					handlerMSG.title = handlerTitle;
					handlerMSG.description = handlerDescription;
					handlerReq.transaction = buildTransaction("webpage");
				}else {
					WXImageObject imgObj = new WXImageObject(bmp);
					handlerMSG = new WXMediaMessage();
					handlerMSG.mediaObject = imgObj;
					handlerReq.transaction = buildTransaction("img");
				}


				Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
				bmp.recycle();
				handlerMSG.thumbData = bmpToByteArray(thumbBmp, true);
				System.out.println("handler...");
				if(isValidContext(wxContext)) {
					if (null != wxProgree && wxProgree.isShowing()) {
						wxProgree.dismiss();
					}
				}
				handlerReq.message = handlerMSG;
				handlerReq.scene =  handlerWXType == 1 ? SendMessageToWX.Req.WXSceneTimeline : SendMessageToWX.Req.WXSceneSession;
				handlerWXAPI.sendReq(handlerReq);


			}

		}
	};

	private static Runnable netRunnable = new Runnable() {
		@Override
		public void run() {

			try {
				bmp = BitmapFactory.decodeStream(new URL(handlerImageUrl).openStream());
			} catch (IOException e) {
				e.printStackTrace();
			}
			Message msg = new Message();
			msg.arg1=0;
			wxHandler.sendMessage(msg);

		}
	};

	/**
	 * 分享本地的图片到微信
	 * @param api
	 * @param path      本地的路径
	 * @param wxType    1:微信朋友圈   0:微信好友
	 */
	private static void sendToSDCardResource(IWXAPI api,String path,int wxType){

		File file = new File(path);
		if (!file.exists()) {
			ViewUtils.showToast(wxContext,"图片路径不存在");
			return ;
		}

		WXImageObject imgObj = new WXImageObject();
		imgObj.setImagePath(path);

		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = imgObj;

		Bitmap bmp = BitmapFactory.decodeFile(path);
		Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, THUMB_SIZE, THUMB_SIZE, true);
		bmp.recycle();
		msg.thumbData = bmpToByteArray(thumbBmp, true);

		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("img");
		req.message = msg;
		req.scene = wxType==1 ? SendMessageToWX.Req.WXSceneTimeline : SendMessageToWX.Req.WXSceneSession;
		api.sendReq(req);

	}

	/**
	 * 发送文字信息
	 * @param api
	 * @param wxType
	 */
	private static void sendTextToWx(IWXAPI api, int wxType,String description){
		System.out.println("wechat--text");

		// 初始化一个WXTextObject对象
		WXTextObject textObj = new WXTextObject();
		textObj.text = "永恒纪元开新服了";

		// 用WXTextObject对象初始化一个WXMediaMessage对象
		WXMediaMessage msg = new WXMediaMessage();
		msg.mediaObject = textObj;
		// 发送文本类型的消息时，title字段不起作用
		// msg.title = "Will be ignored";
		msg.description = description;

		// 构造一个Req
		SendMessageToWX.Req req = new SendMessageToWX.Req();
		req.transaction = buildTransaction("text"); // transaction字段用于唯一标识一个请求
		req.message = msg;
		req.scene = wxType ;

		// 调用api接口发送数据到微信
		api.sendReq(req);


	}


	private static String buildTransaction(final String type) {
		return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
	}


	/**
	 * 压缩图片的方案
	 * @param bmp
	 * @param needRecycle
	 * @return
	 */
	public static byte[] bmpToByteArray(final Bitmap bmp, final boolean needRecycle) {

		ByteArrayOutputStream output = new ByteArrayOutputStream();
		bmp.compress(Bitmap.CompressFormat.JPEG, 100, output);
		int options = 100;

		byte[] result = output.toByteArray();
		System.out.println("---------------------wx-result:"+result.length);
		System.out.println("---------------------wx-result-kb:"+result.length / 1024);
		while((result.length / 1024) > 32){
			output.reset();
			bmp.compress(Bitmap.CompressFormat.JPEG, options, output);
			if(options >= 10) {
				options -= 10;
			}else{
				ViewUtils.showToast(wxContext,"图片过大分享失败");
			}

		}

		if (needRecycle) {
			bmp.recycle();
		}

		try {
			output.close();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return result;
	}

	/**
	 * 判断当前的Activity是否结束
	 * @param c
	 * @return
	 */
	@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
	private static boolean isValidContext(Context c){

		System.out.println("is comeing");
		Activity a = (Activity)c;

		if (a.isDestroyed() || a.isFinishing()){
			System.out.println( "Activity is invalid." + " isDestoryed-->" + a.isDestroyed() + " isFinishing-->" + a.isFinishing());
			return false;
		}else{
			return true;
		}
	}

}
