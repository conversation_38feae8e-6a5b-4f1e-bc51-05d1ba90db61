package com.sqwan.msdk.utils;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.push.service.IHttpRequester;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpClient;
import com.sqnetwork.voly.Request.Method;
import com.sqnetwork.voly.toolbox.StringRequest;
import java.util.Collections;
import java.util.Map;
import okhttp3.HttpUrl;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @since 2024/6/27
 */
public class PushHttpRequester implements IHttpRequester {

    @Override
    public void get(@NonNull String url, @Nullable Map<String, String> headers, @Nullable Map<String, String> params,
        @NonNull IRequestCallback callback) {
        try {
            HttpUrl.Builder builder = HttpUrl.get(url).newBuilder();
            if (params != null) {
                for (String key : params.keySet()) {
                    builder.addQueryParameter(key, params.get(key));
                }
            }
            url = builder.build().toString();
        } catch (Exception e) {
            SQLog.w("【Push】构建url失败: url=" + url + ", param=" + params, e);
        }
        final StringRequest request = new StringRequest(Method.GET, url,
            (raw, response) -> callback.onSuccess(raw.getNetworkResponse().statusCode, response),
            error -> callback.onFailure(-1, error.getMessage())) {

            @Override
            public Map<String, String> getHeaders() {
                return headers == null ? Collections.emptyMap() : headers;
            }

            @Override
            public byte[] getBody() {
                return null;
            }

            @Override
            public String getBodyContentType() {
                return "application/json; charset=" + this.getParamsEncoding();
            }
        };

        SqHttpClient.getInstance().enqueue(request);
    }

    @Override
    public void postJson(@NonNull String url, @Nullable Map<String, String> headers,
        @Nullable Map<String, String> params, @NonNull IRequestCallback callback) {
        String temp = null;
        if (params != null) {
            try {
                JSONObject json = new JSONObject(params);
                temp = json.toString();
            } catch (Exception e) {
                SQLog.w("【Push】参数转json失败: " + params, e);
            }
        }
        final String body = temp;
        final StringRequest request = new StringRequest(Method.POST, url,
            (raw, response) -> callback.onSuccess(raw.getNetworkResponse().statusCode, response),
            error -> callback.onFailure(-1, error.getMessage())) {

            @Override
            public Map<String, String> getHeaders() {
                return headers == null ? Collections.emptyMap() : headers;
            }

            @Override
            public byte[] getBody() {
                return body != null ? body.getBytes() : null;
            }

            @Override
            public String getBodyContentType() {
                return "application/json; charset=" + this.getParamsEncoding();
            }
        };

        SqHttpClient.getInstance().enqueue(request);
    }
}
