package com.sqwan.msdk.utils;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import com.sq.push.service.SqPushService;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.util.ActivityLifecycleAdapter;

/**
 * <AUTHOR>
 * @since 2024/8/26
 */
public class SqAtyLifecycle {

    private SqAtyLifecycle() {
    }

    public static void onApplication(Context context) {
        // TODO: 2024/8/23 推送要监听页面onCreate, 暂时只能在这里特殊处理
        if (context instanceof Application) {
            ((Application) context).registerActivityLifecycleCallbacks(new ActivityLifecycleAdapter() {
                @Override
                public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                    // 这里不能用mod, 因为还没初始化
                    try {
                        SqPushService.getInstance().onCreate(activity, savedInstanceState);
                    } catch (Throwable e) {
                        // 避免旧版本热更崩溃
                    }
                }
            });
        } else {
            SQLog.w(context + "实例不是Application, 无法监听Activity");
        }
    }
}
