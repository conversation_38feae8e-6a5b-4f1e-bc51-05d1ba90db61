package com.sqwan.msdk.utils;

import com.sqwan.msdk.api.SQResultListener;


/**
 *
 * @ClassName: PayInfolUtil
 * @Description: TODO(支付参数信息)
 * <AUTHOR>
 * @date 2017-4-27 上午12:31:30
 *
 */

public class PayInfoUtil {

//	private static   String SQ_PREFS = "sq_payinfo_prefs";

	public static String pay_doid = "pay_doid";
	public static String pay_dpt = "pay_dpt";
	public static String pay_dcn = "pay_dcn";
	public static String pay_dsid = "pay_dsid";
	public static String pay_dsname = "pay_dsname";
	public static String pay_dext = "pay_dext";
	public static String pay_drid = "pay_drid";
	public static String pay_drname = "pay_drname";
	public static int pay_drlevel = 1;
	public static float pay_dmoney = 0;
	public static int pay_dradio = 0;
	public static String pay_moid = "pay_moid";
	public static String pay_data = "pay_data";
	public static SQResultListener  pay_listener = null;



//    public static void setPayDoid(Context context, String payDoid){
//    	SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,Context.MODE_PRIVATE);
//    	Editor editor = uiState.edit();
//    	editor.putString(PAY_DOID, payDoid);
//    	editor.commit();
//    }
//    
//    public static String getPayDoid(Context context){
//    	SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
//    	return uiState.getString(PAY_DOID, "");
//    }
//    
//    
//    
//    public static void setPayRoleLevel(Context context, int roleLevel) {
//		SharedPreferences sp = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
//		Editor editor = sp.edit();
//		editor.putInt(PAY_DRLEVEL, roleLevel);
//		editor.commit();
//	}
//
//	public static int getPayRoleLevel(Context context) {
//		SharedPreferences sp = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
//		return sp.getInt(PAY_DRLEVEL, 0);
//	}


}
