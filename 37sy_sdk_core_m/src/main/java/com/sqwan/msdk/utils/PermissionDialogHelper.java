package com.sqwan.msdk.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.view.View;

import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.util.SpanUtil;
import com.sy37sdk.utils.DensityUtil;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-01-21 16:06
 */
public class PermissionDialogHelper {
    public interface CheckPermissionCallback{
        void toCheck();
    }
    /**
     * 显示权限提示弹窗
     */
    public static void showPermissionPreviewDialog(final Context context, final CheckPermissionCallback checkPermissionCallback){
        post(new Runnable() {
            @Override
            public void run() {
                final int titleSize = DensityUtil.dip2px(context,18);
                final int messageSize = DensityUtil.dip2px(context,14);
                CharSequence title = SpanUtil.concat(
                        SpanUtil.getFontString("将向你申请以下权限和信息：\n\n",titleSize, Color.BLACK,true),
                        SpanUtil.getFontString("·IMEI权限：",messageSize,Color.BLACK,true),
                        SpanUtil.getFontString("手机标识用于保护账号安全\n",messageSize,Color.BLACK,false),
                        SpanUtil.getFontString("·存储权限：",messageSize,Color.BLACK,true),
                        SpanUtil.getFontString("实现账号、图片的缓存和使用，快速登录、降低流量消耗\n\n",messageSize,Color.BLACK,false),
                        SpanUtil.getFontString("·其他信息：Android ID、MAC地址、IMEI、IMSI",messageSize,Color.GRAY,false)
                );
                new CommonAlertDialog.Builder(context)
                        .setTitle(title)
                        .setNegativeButton(SpanUtil.getFontString("好的",messageSize,Color.parseColor("#2A72FF")), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (checkPermissionCallback!=null) {
                                    checkPermissionCallback.toCheck();
                                }

                            }
                        })
                        .setPositiveButton(SpanUtil.getFontString("退出游戏",messageSize,Color.BLACK), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                exit(context);
                            }
                        })
                        .setCancelable(false)
                        .show();
            }
        });

    }
    /**
     * 退出游戏
     */
    private static void exit(Context context){
        ((Activity)context).finish();
        System.exit(0);
    }
    private static void post(Runnable runnable){
        if (Looper.myLooper()==Looper.getMainLooper()) {
            runnable.run();
        }else{
            new Handler(Looper.getMainLooper()).post(runnable);
        }
    }
}
