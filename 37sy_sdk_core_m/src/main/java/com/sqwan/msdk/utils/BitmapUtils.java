package com.sqwan.msdk.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.sqwan.common.util.LogUtil;
import com.sy37sdk.utils.DisplayUtil;
import com.sy37sdk.utils.Util;

public class BitmapUtils {

    public static Bitmap decodeResource(Context context, String resourceName) {
        int resId = Util.getIdByName(resourceName, "drawable", context.getPackageName(), context);
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeResource(context.getResources(), resId, options);
        int sampleSize = calculateInSampleSize(options, DisplayUtil.getScreenWidth(context) / 2, DisplayUtil.getScreenHeight(context) / 2);
        options.inSampleSize = sampleSize;
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeResource(context.getResources(), resId, options);
    }

    /**
     * 计算采样率
     * @return
     */
    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        int height = options.outHeight;
        int width = options.outWidth;
        LogUtil.i("options:  width="+width+"   height="+height);
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            while (((halfHeight / inSampleSize) >= reqHeight) && ((halfWidth / inSampleSize) >= reqWidth)) {
                inSampleSize *= 2;
            }
        }
        LogUtil.i("计算得采样率："+inSampleSize);
        return inSampleSize;
    }

}
