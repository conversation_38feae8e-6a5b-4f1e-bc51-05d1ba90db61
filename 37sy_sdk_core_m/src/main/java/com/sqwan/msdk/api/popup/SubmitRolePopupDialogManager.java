package com.sqwan.msdk.api.popup;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.dialog.pop.BasePopupDialogManager;
import java.util.HashMap;

/**
 * 角色进服弹窗管理类
 */
public class SubmitRolePopupDialogManager extends BasePopupDialogManager {

    private static SubmitRolePopupDialogManager instance;

    private HashMap<String, String> infos;

    private SubmitRolePopupDialogManager() {

    }

    public static SubmitRolePopupDialogManager getInstance() {
        if (instance == null) {
            synchronized (SubmitRolePopupDialogManager.class) {
                if (instance == null) {
                    instance = new SubmitRolePopupDialogManager();
                }
            }
        }
        return instance;
    }

    public void handlePopup(Context context, HashMap<String, String> infos) {
        this.infos = infos;
        super.handlePopup(context);
    }

    @Override
    public void requestPopup() {
        PopupDialogHttpUtil.requestSubmitRolePopup(infos, new SimpleSqHttpCallback<String>() {

            @Override
            public void onSuccess(String data) {
                setPopupData(data);
                showPopupDialog(null);
            }
        });
    }

    @Override
    public String getDesc() {
        return "进服";
    }
}
