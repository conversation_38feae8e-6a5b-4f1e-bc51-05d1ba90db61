package com.sqwan.msdk.api.popup;

import android.content.Context;
import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.dialog.pop.BasePopupDialogManager;
import com.sqwan.common.dialog.pop.PopupDialogBean;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.LogUtil;
import java.util.ArrayList;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2023/10/31
 * @desc: 支付前弹窗
 */
public class BeforePayPopupDialogManager extends BasePopupDialogManager {

    private static BeforePayPopupDialogManager instance;
    private String doid, moid, dmoney, drlevel, dpt;

    public void handlePopup(Context context, String doid, String moid, String dmoney, String drlevel, String dpt) {
        this.doid = doid;
        this.moid = moid;
        this.dmoney = dmoney;
        this.drlevel = drlevel;
        this.dpt = dpt;
        super.handlePopup(context);
    }


    public static BeforePayPopupDialogManager getInstance() {
        if (instance == null) {
            instance = new BeforePayPopupDialogManager();
        }
        return instance;
    }

    @Override
    public void requestPopup() {
        PopupDialogHttpUtil.requestOrderPopups(moid, doid, dmoney, drlevel, dpt, new SimpleSqHttpCallback<String>() {
            @Override
            public void onSuccess(String data) {
                LogUtil.w("requestOrderPopups onSuccess " + data);
                setPopupData(data);
                showPopupDialog(ActivityLifeCycleUtils.getInstance().getResumedActivity(), null);
            }
        });
    }

    @Override
    public void setPopupData(String popupJsonStr) {
        try {
            if (popupDialogBeans == null) {
                popupDialogBeans = new ArrayList<>();
            }
            popupDialogBeans.clear();
            JSONObject popupJsonObj = new JSONObject(popupJsonStr);
            PopupDialogBean popupDialogBean = PopupDialogBean.decodeFromJson(popupJsonObj);
            if (!TextUtils.isEmpty(popupDialogBean.getUrl())) {
                popupDialogBeans.add(popupDialogBean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getDesc() {
        return "支付前弹窗";
    }
}
