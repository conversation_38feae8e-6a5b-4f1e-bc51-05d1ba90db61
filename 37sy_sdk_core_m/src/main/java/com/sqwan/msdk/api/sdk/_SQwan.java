package com.sqwan.msdk.api.sdk;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import com.sqwan.order.base.IPay;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.order.SQPay;

public class _SQwan extends Platform {

	private final SQPay mSQPay = new SQPay();

	public _SQwan(Context context, InitBean bean, SQResultListener initListener) {
		super(context, bean, initListener);
	}

	@Override
	public void init(Context context) {
		mSQPay.init(context);
		super.init(context);
	}

	@Override
	protected void initPlatform() {
		initSQ(false); // 默认加载显示37logo图
	}

	@Override
	protected void loginPlatform(final SQResultListener loginListener) {
		if (loginListener != null) {
			loginSQ(loginListener);
		}
	}

	@Override
	public void pay(Context context, String doid, String dpt, String dcn,
					String dsid, String dsname, String dext, String drid,
					String drname, int drlevel, float dmoney, int dradio,
					SQResultListener payListener) {
		// 37有不定额充值
		isNeedInputMoney = true;
		super.pay(context, doid, dpt, dcn, dsid, dsname, dext, drid, drname,
				drlevel, dmoney, dradio, payListener);
	}

	@Override
	protected void payPlatform(final Context context, final String doid,
							   final String dpt, final String dcn, final String dsid,
							   final String dsname, final String dext, final String drid,
							   final String drname, final int drlevel, final float dmoney,
							   final int dradio,final String moid,final String data,
							   final SQResultListener payListener) {
		// 返回了IPay, 那么不会走到这里
		SQLog.e("不支持payPlatform");
		if (payListener != null) {
			payListener.onFailture(SqPayError.ERROR_SDK_UNSUPPORTED, "不支持(10086)");
		}
	}

	@Override
	protected IPay getPlatformPay() {
		return mSQPay;
	}

	@Override
	public void changeAccount(final Context context,final SQResultListener listener) {
		LogUtil.w("37切换账号");
		super.changeAccount(context, listener);
		changeAccountSQ(context, listener);
	}

	@Override
	public void logout(Context context, final SQResultListener listener) {
		LogUtil.w("37退出框");
		super.logout(context, listener);
		logoutSQ(context, listener);
	}

	@Override
	public void setSwitchAccountListener(SQResultListener switchListener) {
		LogUtil.w("37设置悬浮窗切换账号监听");
		super.setSwitchAccountListener(switchListener);
		setSwitchAccountListenerSQ(switchListener);
	}

	@Override
	public void setBackToGameLoginListener(SQResultListener listener) {
		LogUtil.w("37设置返回游戏登录界面监听");
		super.setBackToGameLoginListener(listener);
		setBackToGameListenerSQ(listener);
	}

	@Override
	public void setScreenshotListener(IScreenshotListener mScreenshotListener) {
		LogUtil.w("37设置悬浮球截图监听");
		super.setScreenshotListener(mScreenshotListener);
		setScreenshotListenerSQ(mScreenshotListener);
	}

	@Override
	public void onResume() {
		super.onResume();
		if (sq != null) {
			sq.onResume();
		}
	}

	@Override
	public void onPause() {
		super.onPause();
		if (sq != null) {
			sq.onPause();
		}
	}

	@Override
	public void onStop() {
		super.onStop();
		if (sq != null) {
			sq.onStop();
		}
	}

	@Override
	public void onActivityResult(int requestCode, int resultCode, Intent data) {
		super.onActivityResult(requestCode, resultCode, data);
	}

	@Override
	public void showUAgreement(final Context context) {
		if(sq != null) {
			sq.showUAgreement(context);
		}
	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		if (sq != null) {
			sq.onConfigurationChanged(newConfig);
		}
	}

	@Override
	public void onWindowFocusChanged(boolean hasFocus) {
	}

	@Override
	public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {

	}


}
