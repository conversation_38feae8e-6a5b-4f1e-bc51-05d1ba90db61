package com.sqwan.msdk.api;

import android.content.Context;
import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Base64;
import com.sq.tool.network.SignInterceptor.SignVersion;
import com.sq.tool.network.SignV2Interceptor.SignExt;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tools.Logger;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.FakeActive;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.dialog.CommonProgressDialog;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqRequestCallBack;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.utils.ZipString;
import com.sqwan.order.base.PayInfoModel;
import com.sy37sdk.account.device.DevicesInfo;
import com.sy37sdk.utils.Util;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.TreeMap;
import org.json.JSONException;
import org.json.JSONObject;


/**
 * <AUTHOR>
 **/
public class MRequestManager {


    private Context mContext;
    private CommonProgressDialog waitDialog;

    private boolean isInitRequest = false;

    private static final String REQUEST_TRANS_INFO_KEY = "request_trans_info_key";

    public MRequestManager(Context context) {
        this.mContext = context;
    }

    /**
     * 激活接口
     */
    public void active(final SqHttpCallback<JSONObject> callback) {
        isInitRequest = true;
        //构建请求参数
        final HashMap<String, String> params = new HashMap<>();
        params.put("mac", MacLogic.getInstance(mContext).getValue());
        params.put("imei", ImeiLogic.getInstance(mContext).getValue());
        params.put("wpi", "" + MultiSDKUtils.getWpixels(mContext) + "");
        params.put("hpi", "" + MultiSDKUtils.getHpixels(mContext) + "");
        params.put("mode", Build.MODEL);
        params.put("os", IMUrl.OS + "");
        params.put("os_desc", DeviceUtils.getOs());
        params.put("over", IMUrl.OSVER + "");
        params.put("brand", Build.BRAND);
        params.put("phone", MultiSDKUtils.getNumber(mContext) + "");
        params.put("dpgn", mContext.getPackageName() + "");
        //2015年05月26日15:44:22 新增上传网络类型
        params.put("nwk", MultiSDKUtils.getNetType(mContext) + "");
        //( 1:安卓,    2:iOS越狱,      3:正版 )
        params.put("sua", "1");
        params.put("versionCode", Util.getVersionCode(mContext) + "");

        // 3.5.6 新增电量、WiFi信息
        // 当前电量百分比，及充电状态
        params.put("battery_level", String.valueOf(DeviceUtils.getBatteryLevel(mContext)));
        params.put("battery_status", String.valueOf(DeviceUtils.getBatteryStatus(mContext)));
        // wifi信息，包括WiFi名称SSID，和BSSID
        params.put("ssid", DeviceUtils.getWifiSSID(mContext));
        params.put("bssid", DeviceUtils.getWifiBSSID(mContext));
        params.put("display_name", AppUtils.getAppName(mContext));
        //插件版本号
        params.put("pluginVersion", String.valueOf(VersionUtil.getPluginVersion(mContext)));
        // 增加透传参数
        params.put(SqConstants.TRANS_INFO, getActiveTransInfo(mContext));
        //用于设备评分，统一收集数据
        DevicesInfo.setDeviceInfoFromMap(params);

        //url 新增时间戳，避免缓存
        String url = IMUrl.URL_M_INIT + "?t=" + System.currentTimeMillis() + "";

        SqRequest request = SqRequest.of(url)
            .signV5()
            .formParams(params)
            .addParamsTransformer(new MInitParams());
        request.post(new SqHttpCallback<JSONObject>() {

                @Override
                public void onSuccess(JSONObject dataJson) {
                    FakeActive.INSTANCE.saveContent(mContext, getResponseStr(), FakeActive.INSTANCE.M_FAKE_ACTIVE_KEY);
                    callback.onSuccess(dataJson);
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    callback.onResponseStateError(httpStatus, state, msg, data);
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    HashMap<String, String> extraMap = new HashMap<>();
                    extraMap.put(SqTrackKey.fail_code, code + "");
                    extraMap.put(SqTrackKey.reason_fail, errorMsg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_fail, extraMap);
                    String content = FakeActive.INSTANCE.requireContent(mContext,
                        FakeActive.INSTANCE.M_FAKE_ACTIVE_KEY, url, request.getRequestParams(), SignVersion.V1);
                    JSONObject dataJson = null;
                    try {
                        JSONObject contentJson = new JSONObject(content);
                        dataJson = contentJson.getJSONObject("data");
                    } catch (Exception e) {
                        /* no-op */
                    }
                    if (dataJson != null) {
                        Logger.info("本地m层激活有缓存, 本地激活成功");
                        callback.onSuccess(dataJson);
                    } else {
                        try {
                            Logger.warning("本地m层激活未读到缓存, 返回默认结果");
                            callback.onSuccess(buildFakeData());
                        } catch (Exception e) {
                            Logger.error("默认结果构建失败, m层激活失败");
                            callback.onFailure(code, "网络异常，请稍候再试", error);
                        }
                    }
                }

                private JSONObject buildFakeData() throws Exception {
                    JSONObject data = new JSONObject();
                    data.put("dev", DevLogic.getInstance(mContext).getValue());
                    return data;
                }
            });
    }

    @NonNull
    public String getActiveTransInfo(Context context) {
        String transInfo = MultiSDKUtils.getString(context, REQUEST_TRANS_INFO_KEY);
        MultiSDKUtils.removeString(context, REQUEST_TRANS_INFO_KEY);
        LogUtil.d("SqRequest#getTransInfo: " + transInfo);
        if (!TextUtils.isEmpty(transInfo)) {
            return Base64.encodeToString(transInfo.getBytes(), Base64.NO_WRAP);
        }
        return "";
    }

    /**
     * 验证激活码请求
     */
    public void checkActivationCodeRequest(String betaCode, final MRequestCallBack callBack) {
        HashMap<String, String> checkAccodeParams = new HashMap<>();
        checkAccodeParams.put("token", Util.getToken(mContext) + "");
        checkAccodeParams.put("mac", MacLogic.getInstance(mContext).getValue());
        checkAccodeParams.put("imei", ImeiLogic.getInstance(mContext).getValue());
        checkAccodeParams.put("idfa", "android");
        checkAccodeParams.put("betac", betaCode + "");
        post(IMUrl.URL_ACTIVATION_CODE_CHECK, checkAccodeParams, SignVersion.V2, new SignExt(betaCode),
            new SqRequestCallBack() {
                @Override
                public void onRequestSuccess(String content) {
                    callBack.onRequestSuccess(content);
                }

                @Override
                public void onRequestError(String errorMsg) {
                    callBack.onRequestError(errorMsg);
                }
            }, true, true);
    }


    public void orderRequest(PayInfoModel payInfo, String pdata, SqRequestCallBack callBack) {
        String uid = MultiSDKUtils.getUserid(mContext);
        String uname = MultiSDKUtils.getUsername(mContext);
        String token = MultiSDKUtils.getToken(mContext);
        HashMap<String, String> payReParams = new HashMap<>();
        payReParams.put("doid", payInfo.getOrderId());
        payReParams.put("dsid", payInfo.getServerId());
        payReParams.put("dsname", payInfo.getServerName());
        payReParams.put("dext", payInfo.getExtend());
        payReParams.put("drid", payInfo.getRoleId());
        payReParams.put("drname", payInfo.getRoleName());
        payReParams.put("drlevel", payInfo.getRoleLevel() + "");
        payReParams.put("dmoney", payInfo.getMoney() + "");
        payReParams.put("dradio", payInfo.getRadio() + "");
        payReParams.put("uid", uid);
        payReParams.put("uname", uname);
        payReParams.put("pdata", pdata);
        //( 1:安卓,    2:iOS越狱,      3:正版 )
        payReParams.put("sua", "1");
        //系统
        payReParams.put("os", "1");
        payReParams.put("os_desc", DeviceUtils.getOs());
        payReParams.put("token", token);
        String transInfo = SqRequest.getTransInfo();
        payReParams.put(SqConstants.TRANS_INFO, transInfo);
        String url = IMUrl.URL_M_ORDER;
        SignExt signExt = new SignExt(payInfo.getOrderId())
            .append(payInfo.getServerId())
            .append(uid)
            .append(uname)
            .append(token)
            .append(transInfo);
        post(url, payReParams, SignVersion.V2, signExt, callBack, true, false);
    }


    /**
     * 提交角色信息接口
     */
    public void submitRoleInfoRequst(HashMap<String, String> infos, final MRequestCallBack callBack) {
        //构建请求参数
        HashMap<String, String> params = new HashMap<>();

        if (null != infos) {
            params.put("dsid", infos.get(SQwanCore.INFO_SERVERID) + "");
            params.put("dsname", infos.get(SQwanCore.INFO_SERVERNAME) + "");
            params.put("drid", infos.get(SQwanCore.INFO_ROLEID) + "");
            params.put("drname", infos.get(SQwanCore.INFO_ROLENAME) + "");
            params.put("drlevel", infos.get(SQwanCore.INFO_ROLELEVEL) + "");
            params.put("drbalance", infos.get(SQwanCore.INFO_BALANCE) + "");
            params.put("dpname", infos.get(SQwanCore.INFO_PARTYNAME) + "");
            params.put("dviplevel", infos.get(SQwanCore.INFO_VIPLEVEL) + "");
            params.put("drctime", infos.get(SQwanCore.INFO_ROLE_TIME_CREATE) + "");
            params.put("drlevelmtime", infos.get(SQwanCore.INFO_ROLE_TIME_LEVEL) + "");
        }
        params.put("uid", MultiSDKUtils.getUserid(mContext) + "");
        params.put("uname", MultiSDKUtils.getUsername(mContext) + "");
        params.put("token", MultiSDKUtils.getToken(mContext) + "");
        params.put("display_name", AppUtils.getAppName(mContext));
        SignExt signExt = new SignExt("");
        if (infos != null) {
            signExt.append(infos.get(SQwanCore.INFO_SERVERID) + "");
        }
        signExt.append(VersionUtil.sdkVersion);

        String url = IMUrl.URL_M_ENTER;
        if ("".equals(url.trim())) {
            url = IMUrl.URL_M_ENTER;
        }
        post(url, params, SignVersion.V2, signExt, new SqRequestCallBack() {
            @Override
            public void onRequestSuccess(String content) {
                callBack.onRequestSuccess(content);
            }

            @Override
            public void onRequestError(String errorMsg) {
                callBack.onRequestError(errorMsg);
            }
        }, false, true);
    }


    /**
     * 提交统计数据接口
     *
     * @param key 统计项标识键
     * @param value 统计项数据( urlencode后的JSON格式数据 )
     */
    public void statisticsRequst(String key, String value, final MRequestCallBack callback) {
        //构建请求参数
        HashMap<String, String> params = new HashMap<>();
        params.put("k", key + "");
        params.put("v", value + "");
        params.put("uid", MultiSDKUtils.getUserid(mContext) + "");
        params.put("uname", MultiSDKUtils.getUsername(mContext) + "");
        String url = IMUrl.URL_M_SUBMIT;
        post(url, params, SignVersion.V2, null, new SqRequestCallBack() {
            @Override
            public void onRequestSuccess(String content) {
                callback.onRequestSuccess(content);
            }

            @Override
            public void onRequestError(String errorMsg) {
                callback.onRequestError(errorMsg);
            }
        }, false, true);

    }

    /**
     * 订单查询接口
     *
     * @param oid 订单号
     */
    public void payQueryRequst(String url, String oid, final MRequestCallBack callBack) {
        HashMap<String, String> params = new HashMap<>();
        params.put("oid", oid + "");
        params.put("puid", MultiSDKUtils.getPlatUserid(mContext) + "");
        params.put("puname", MultiSDKUtils.getPlatUsername(mContext) + "");
        params.put("uid", MultiSDKUtils.getUserid(mContext) + "");
        params.put("uname", MultiSDKUtils.getUsername(mContext) + "");
        post(url, params, SignVersion.V2, new SignExt(oid), new SqRequestCallBack() {
            @Override
            public void onRequestSuccess(String content) {
                callBack.onRequestSuccess(content);
            }

            @Override
            public void onRequestError(String errorMsg) {
                callBack.onRequestError(errorMsg);
            }
        }, false, true);
    }

    public void uploadDeviceInfo(String data, String url) {
        LogUtil.i("upload device info-->" + data);
        try {
            //加密
            byte[] aesBytes = AESUtil.encrypt(data);
            String encodeData = Base64.encodeToString(aesBytes, Base64.NO_WRAP);
            SqRequest.of(url)
                .signV3()
                .addParam("data", encodeData)
                .addParamsTransformer(new CommonParamsV3())
                .post(null, Void.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void post(final String requestUrl,
        HashMap<String, String> params,
        @Nullable SignVersion signVersion,
        @Nullable SignExt signExt,
        final SqRequestCallBack callBack,
        boolean isShowDialog, boolean errorTip) {
        if (isShowDialog) {
            waitDialog = new CommonProgressDialog(mContext);
            waitDialog.setCancelable(false);
            waitDialog.setMessage("加载中...");
            waitDialog.show();
        } else {
            if (waitDialog != null) {
                waitDialog.dismiss();
            }
        }

        SqRequest.of(requestUrl)
            .sign(signVersion, signExt)
            .formParams(params)
            .addParamsTransformer(new CommonParamsV1())
            .post(new SqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestSuccess(getResponseStr());
                    }
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestSuccess(getResponseStr());
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestError(code, "网络异常，请稍候再试");
                    }
                    if (errorTip) {
                        MultiSDKUtils.showTips(mContext, "网络请求失败，请重试");
                    }
                }
            });
    }

    public void reportDev(final Context context, final String identify) {
        HashMap<String, String> oaidMap = new HashMap<>();
        oaidMap.put("oaid", identify);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_oaid_request, oaidMap);
        String url = IMUrl.URL_REPORT_MDEV;
        try {
            JSONObject identifyObj = new JSONObject(identify);
            TreeMap<String, Object> treeMap = new TreeMap<>();
            treeMap.put("pid", Integer.parseInt(MultiSDKUtils.getPID(context)));
            treeMap.put("gid", Integer.parseInt(MultiSDKUtils.getGID(context)));
            treeMap.put("refer", MultiSDKUtils.getRefer(context));
            treeMap.put("dev", DevLogic.getInstance(mContext).getValue());
            treeMap.put("sversion", VersionUtil.sdkVersion);
            treeMap.put("version", AppUtils.getVersionName(context));
            treeMap.put("identify", identifyObj);
            String time = getSecondTimestampTwo(new Date()) + "";
            treeMap.put("time", time);
            String originSignStr = mapToJson(treeMap).toString() + ZipString.zipString2Json(MultiSDKUtils.getKey(context));
            LogUtil.w("originSign: " + originSignStr);
            String sign = Util.Md5(originSignStr);
            TreeMap<String, Object> map = new TreeMap<>();
            map.put("pid", Integer.parseInt(MultiSDKUtils.getPID(context)));
            map.put("gid", Integer.parseInt(MultiSDKUtils.getGID(context)));
            map.put("refer", MultiSDKUtils.getRefer(context));
            map.put("dev", DevLogic.getInstance(context).getValue());
            map.put("sversion", VersionUtil.sdkVersion);
            map.put("version", AppUtils.getVersionName(context));
            map.put("identify", identifyObj);
            map.put("time", time);
            map.put("sign", sign);
            SqRequest.of(url)
                .jsonParams(map)
                .post(new SqHttpCallback<Void>() {
                    @Override
                    public void onSuccess(Void noData) {
                        LogUtil.w("reportDev success");
                        MultiSDKUtils.setMDevIds(context, identify);
                    }

                    @Override
                    public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                        LogUtil.w("reportDev fail: " + errorMsg);
                    }

                    @Override
                    public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                        @Nullable String data) {
                        LogUtil.w("reportDev fail: " + msg);
                    }
                }, Void.class);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    /**
     * 把Map组装成Json格式
     *
     * @param map
     * @return
     */
    public static JSONObject mapToJson(TreeMap<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return new JSONObject();
        }
        Set<String> keys = map.keySet();
        Iterator<String> it = keys.iterator();
        JSONObject obj = new JSONObject();
        while (it.hasNext()) {
            try {
                String key = it.next();
                Object value = map.get(key);
                obj.put(key, value);
            } catch (JSONException e1) {
                e1.printStackTrace();
            }
        }
        return obj;
    }

    public void setContext(Context cxt) {
        mContext = cxt;
    }

    /**
     * 获取精确到秒的时间戳
     */
    public static int getSecondTimestampTwo(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime() / 1000);
        return Integer.valueOf(timestamp);
    }
}
