package com.sqwan.msdk.api.sdk;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.widget.Toast;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.JsonMap;
import com.sqwan.common.util.SqAtyRef;
import com.sqwan.common.util.SqRequestCallBack;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ZipUtil;
import com.sqwan.msdk.api.MRequestCallBack;
import com.sqwan.msdk.api.MRequestManager;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.popup.PayPopupDialogManager;
import com.sqwan.msdk.api.sdk.pay.PayReporter;
import com.sqwan.order.base.IPay;
import com.sqwan.order.base.IPay.PayCallback;
import com.sqwan.order.base.IPay.UIPayCallback;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.account.auth.AuthConfigCache;
import com.sy37sdk.order.OrderTrackManager;
import com.sy37sdk.order.SQPay;
import com.sy37sdk.order.third.ali.AliPayWay;
import com.sy37sdk.order.third.douyin.DouYinPayWay;
import com.sy37sdk.order.third.union.UnionPayWay;
import com.sy37sdk.order.third.wechat.WeChatPayWay;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 支付入口, 主要负责下单
 *
 * <AUTHOR>
 * @since 2024/7/30
 */
public class SqPayManager {

    private static final String TAG = "【Pay Manager】";
    /**
     * 下单接口的pdata参数, 一般是联运渠道下单需要传
     */
    public static final String EXTRA_PDATA = "extra_pdata";

    private static volatile SqPayManager sInstance;

    public static SqPayManager getInstance() {
        if (sInstance == null) {
            synchronized (SqPayManager.class) {
                if (sInstance == null) {
                    sInstance = new SqPayManager();
                }
            }
        }

        return sInstance;
    }

    private SqPayManager() {
    }

    @Nullable
    private Platform mPlatform;
    @Nullable
    private SQPay mSQPay;
    private IPay mPlatformPay;

    /**
     * 为了兼容旧代码
     */
    @Deprecated
    public void setPlatform(@Nullable Platform platform) {
        SQLog.w(TAG + "(兼容方式)设置平台支付实例: " + platform);
        mPlatform = platform;
    }

    public void setSQPay(@Nullable SQPay pay) {
        SQLog.d(TAG + "设置37支付实例: " + pay);
        mSQPay = pay;
    }

    public void setPlatformPay(IPay pay) {
        SQLog.d(TAG + "设置平台支付实例: " + pay);
        mPlatformPay = pay;
    }

    /**
     * @see #EXTRA_PDATA
     */
    public void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @Nullable PayCallback callback) {
        SQLog.d(TAG + "调起支付: \n" + payInfo);
        PayCallback uiCallback = UIPayCallback.wrap(new PayCallback() {
            @Override
            public void onSuccess(@NonNull PayContext payCtx) {
                SQLog.i(TAG + "支付成功, moid=" + payCtx.getMoid());
                showPayResultTip("支付成功");
                PayReporter.reportPaySuccess(payCtx);
                if (callback != null) {
                    callback.onSuccess(payCtx);
                }
                OrderTrackManager.reportToMedia(payInfo, payContext.getMoid(), true);
                SQLog.d(TAG + "支付成功, 检查弹窗");
                checkAfterPayPopup(activity, payCtx, 1);
            }

            @Override
            public void onCancel(@NonNull PayContext payCtx) {
                SQLog.w(TAG + "取消支付, moid=" + payCtx.getMoid());
                showPayResultTip("取消支付");
                PayReporter.reportPayCancel(payCtx);
                if (callback != null) {
                    callback.onCancel(payCtx);
                }
                OrderTrackManager.reportToMedia(payInfo, payContext.getMoid(), false);
                SQLog.d(TAG + "支付取消, 检查弹窗");
                checkAfterPayPopup(activity, payCtx, 3);
            }

            @Override
            public void onFailed(@NonNull PayContext payCtx, @NonNull SqPayError error) {
                SQLog.w(TAG + "支付失败, moid=" + payCtx.getMoid() + ", " + error);
                showPayFailTip(error);
                PayReporter.reportPayFail(payCtx, error);
                if (callback != null) {
                    callback.onFailed(payCtx, error);
                }
                OrderTrackManager.reportToMedia(payInfo, payContext.getMoid(), false);
                SQLog.d(TAG + "支付失败, 检查弹窗");
                checkAfterPayPopup(activity, payCtx, 2);
            }
        });
        // 下单
        createOrder(activity, payContext, payInfo, extra, new CreateOrderCallback() {
            @Override
            public void onSuccess(@NonNull PayContext payCtx, @NonNull OrderConfig config) {
                PayReporter.reportPayInvoke(payCtx, payInfo);
                if (config.disablePay) {
                    SQLog.w(TAG + "下单禁用支付");
                    // 未开启支付功能
                    uiCallback.onFailed(payCtx, new SqPayError(SqPayError.ERROR_SDK_DISABLE_PAY, "未开启支付功能"));
                } else if (config.cutTo37) {
                    SQLog.w(TAG + "下单转移到37支付");
                    // 截流到37
                    if (mSQPay != null) {
                        // 截流不触发实名认证
                        mSQPay.pay(activity, payCtx, payInfo, extra, false, uiCallback);
                    } else {
                        SQLog.e(TAG + "无法调用37支付");
                        uiCallback.onFailed(payCtx, new SqPayError(
                            SqPayError.ERROR_SDK_UNSUPPORTED, "无法支付", -1, "无法调用37支付"));
                    }
                } else {
                    SQLog.d(TAG + "走平台支付");
                    // 平台支付
                    if (mPlatformPay != null) {
                        // 优先使用新方式
                        mPlatformPay.pay(activity, payCtx, payInfo, extra, uiCallback);
                    } else if (mPlatform != null) {
                        SQLog.w(TAG + "使用兼容方式调用平台支付");
                        // 兼容旧代码
                        mPlatform.payPlatform(activity,
                            payInfo.getOrderId(), payInfo.getProductName(), payInfo.getCurrencyName(),
                            payInfo.getServerId(), payInfo.getServerName(), payInfo.getExtend(),
                            payInfo.getRoleId(), payInfo.getRoleName(), payInfo.getRoleLevel(),
                            payInfo.getMoney(), payInfo.getRadio(), payCtx.getMoid(), payCtx.getRawOrderData(),
                            new SQResultListener() {
                                @Override
                                public void onSuccess(Bundle bundle) {
                                    SQLog.i(TAG + "(兼容方式)平台支付成功");
                                    uiCallback.onSuccess(payCtx);
                                }

                                @Override
                                public void onFailture(int code, String msg) {
                                    SQLog.w(TAG + "(兼容方式)平台支付失败, code=" + code + ", msg=" + msg);
                                    if (code == 205) {
                                        uiCallback.onCancel(payCtx);
                                    } else {
                                        uiCallback.onFailed(payCtx, new SqPayError(
                                            SqPayError.ERROR_THIRD_PURCHASE, "平台支付失败", code, msg));
                                    }
                                }
                            });
                    } else {
                        SQLog.e(TAG + "无法调用平台支付");
                        uiCallback.onFailed(payCtx, new SqPayError(
                            SqPayError.ERROR_SDK_UNSUPPORTED, "无法支付", -2, "无法调用平台支付"));
                    }
                }
            }

            @Override
            public void onFailed(@NonNull PayContext payCtx, int code, String msg) {
                // 下单失败
                uiCallback.onFailed(payCtx, new SqPayError(SqPayError.ERROR_HTTP_CREATE_ORDER, "下单失败", code, msg));
            }
        });
    }

    /**
     * 创建订单
     */
    private void createOrder(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @NonNull CreateOrderCallback callback) {
        Context context = activity.getApplicationContext();
        // 渠道额外传参
        String pdata = "";
        if (extra != null) {
            pdata = extra.getString(EXTRA_PDATA, "");
        }
        SQLog.d(TAG + "请求下单接口, pdata=" + pdata);
        // TODO: 2024/7/31 有ui操作, 必须传Activity
        new MRequestManager(activity).orderRequest(payInfo, pdata, new SqRequestCallBack() {
            @Override
            public void onRequestSuccess(String content) {
                try {
                    JSONObject contentJson = new JSONObject(content);
                    int state = contentJson.getInt("state");
                    if (state == 1) {
                        String orderData = contentJson.getString("data");
                        SQLog.i(TAG + "下单成功, " + orderData);
                        JSONObject orderJson = new JSONObject(orderData);
                        updateAuthConfig(orderJson);

                        payContext.setRawOrderData(orderData);
                        payContext.setOrderDataJson(orderJson);

                        String moid = orderJson.getString("moid");
                        SQLog.i(TAG + "moid=" + moid);
                        if (!TextUtils.isEmpty(moid)) {
                            payContext.setMoid(moid);

                            callback.onSuccess(payContext, new OrderConfig(context, orderJson));
                        } else {
                            SQLog.e(TAG + "下单接口成功, 获取moid失败");
                            // moid异常
                            callback.onFailed(payContext, -2, "支付异常");
                        }
                    } else {
                        String msg = contentJson.optString("msg");
                        SQLog.e(TAG + "下单失败, state=" + state + ", msg=" + msg);
                        callback.onFailed(payContext, state, msg);
                    }
                } catch (Exception e) {
                    SQLog.e(TAG + "下单结果解析异常", e);
                    BuglessAction.reportCatchException(e, content, BuglessAction.M_ORDER);
                    callback.onFailed(payContext, -1, "支付异常");
                }
            }

            @Override
            public void onRequestError(int code, String errorMsg) {
                SQLog.e(TAG + "下单接口异常, " + errorMsg);
                JsonMap map = new JsonMap();
                map.put("cp_order_id", payInfo.getOrderId());
                map.put("pdata", extra == null ? "" : extra.getString(EXTRA_PDATA, ""));
                map.put("code", code);
                map.put("msg", errorMsg);
                BuglessAction.reportCatchException(new Exception(), map.toString(), BuglessAction.M_ORDER);
                callback.onFailed(payContext, code, errorMsg);
            }
        });
    }

    /**
     * 下单接口会返回认证信息, 尝试刷新
     */
    private void updateAuthConfig(JSONObject orderJson) {
        String authJson = orderJson.optString("auth");
        if (!TextUtils.isEmpty(authJson)) {
            SQLog.i(TAG + "刷新实名认证信息: " + authJson);
            AuthConfigCache.saveAuthConfig(authJson);
        } else {
            SQLog.w(TAG + "清空实名认证信息");
            AuthConfigCache.clearAuthConfig();
        }
    }

    /**
     * @param payResult 1:充值成功 2:充值失败 3:取消 4:未知
     */
    private void checkAfterPayPopup(Activity activity, PayContext payContext, int payResult) {
        String moid = payContext.getMoid();
        JSONObject orderJson = payContext.getOrderDataJson();
        boolean isShowNotice = false;
        // 充值查询接口
        String noticeUrl = null;
        if (orderJson != null) {
            isShowNotice = orderJson.optInt("pon", 0) == 1;
            noticeUrl = orderJson.optString("psapi");
        }
        // 进行充值查询
        if (isShowNotice && !TextUtils.isEmpty(moid) && !TextUtils.isEmpty(noticeUrl)) {
            SQLog.i(TAG + "显示支付公告框, moid=" + moid + ", url=" + noticeUrl);
            showNoticeAfterPay(activity, noticeUrl, moid, 1);
        } else {
            SQLog.d(TAG + "无需显示支付公告框, moid=" + moid);
        }
        PayInfoModel payInfo = payContext.getPayInfo();
        if (payInfo != null && !TextUtils.isEmpty(moid)) {
            SQLog.i(TAG + "检查支付后弹窗, moid=" + moid);
            PayPopupDialogManager.getInstance().handlePopup(
                activity, payInfo.getOrderId(), moid, String.valueOf(payInfo.getMoney()), String.valueOf(payResult));
        }
    }

    private interface CreateOrderCallback {

        void onSuccess(@NonNull PayContext payCtx, @NonNull OrderConfig config);

        void onFailed(@NonNull PayContext payCtx, int code, String msg);
    }

    private static class OrderConfig {

        /**
         * 禁用支付
         */
        boolean disablePay;
        /**
         * 转移到37支付
         */
        boolean cutTo37;

        public OrderConfig(Context context, JSONObject orderJson) {
            String encryptedData = orderJson.optString("sdata");
            if (!TextUtils.isEmpty(encryptedData)) {
                String data = ZipUtil.sqUnZip(context, encryptedData);
                SQLog.d(TAG + "sdata=" + data);
                JSONObject sdataJson;
                try {
                    sdataJson = new JSONObject(data);
                } catch (JSONException e) {
                    return;
                }
                int payCode = sdataJson.optInt("code", 0);

                if (payCode == -1) {
                    // -1:未开启充值
                    disablePay = true;
                    cutTo37 = false;
                } else if (payCode == 1) {
                    // 1:转移到37支付
                    disablePay = false;
                    cutTo37 = true;
                }
            }
        }
    }

    private void showPayFailTip(@NonNull SqPayError payError) {
        final int code = payError.code;
        final int subCode = payError.originCode;
        String tip = null;
        if (payError.getModule() == SqPayError.MODULE_HTTP
            && subCode > 0 // 大于0才认为是服务端返回的错误码
            && !TextUtils.isEmpty(payError.originMsg)) {
            // 后端的错误, 提示后端文案
            tip = payError.originMsg;
            // 后端文案不拼接错误码
            showPayResultTip(tip);
            return;
        } else if (code == SqPayError.ERROR_SDK_INLINE && subCode == -1) {
            // 跳转网页支付失败
            tip = "支付异常，请联系客服";
        } else if (code == SqPayError.ERROR_SDK_ADDITION) {
            // 防沉迷导致失败
            if (!TextUtils.isEmpty(payError.originMsg)) {
                // 提示后端文案
                tip = payError.originMsg;
                // 后端文案不拼接错误码
                showPayResultTip(tip);
                return;
            } else {
                tip = "实名信息不允许支付";
            }
        } else if (code == SqPayError.ERROR_SDK_NATIVE) {
            // 原生支付失败
            tip = "支付异常，请联系客服";
        } else if (code == SqPayError.ERROR_THIRD_PURCHASE) {
            if (subCode == WeChatPayWay.ERROR_UNSUPPORTED) {
                tip = "请先安装微信再进行支付";
            } else if (subCode == WeChatPayWay.ERROR_URL) {
                tip = "支付异常，请联系客服";
            } else if (subCode == WeChatPayWay.ERROR_GO_PAY_PAGE) {
                tip = "跳转微信失败，请重试或联系客服";
            } else if (subCode == AliPayWay.ERROR_INVALID_RESULT) {
                tip = "支付异常，请重试或联系客服";
            } else if (subCode == DouYinPayWay.ERROR_UNSUPPORTED) {
                tip = "请先安装抖音再进行支付";
            } else if (subCode == DouYinPayWay.ERROR_URL) {
                tip = "支付异常，请重试或联系客服";
            } else if (subCode == DouYinPayWay.ERROR_GO_PAY_PAGE) {
                tip = "跳转抖音失败，请重试或联系客服";
            } else if (subCode == UnionPayWay.ERROR_PAY) {
                tip = "支付失败，请重新下单";
            } else if (subCode == UnionPayWay.ERROR_GO_PAY_PAGE) {
                tip = "跳转云闪付失败，请重试或联系客服";
            } else if (subCode == UnionPayWay.ERROR_INVALID_RESULT) {
                tip = "支付失败，请重新下单";
            }
        }
        if (TextUtils.isEmpty(tip)) {
            // 默认提示语
            tip = "功能异常，请重试或联系客服";
        }
        // 统一拼接错误码后缀
        tip += "【" + code + "/" + subCode + "】";
        showPayResultTip(tip);
    }

    private void showPayResultTip(String tip) {
        SQLog.d(TAG + "支付结果提示语: " + tip);
        if (TextUtils.isEmpty(tip)) {
            return;
        }
        Activity activity = SqAtyRef.getInstance().getActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        ToastUtil.showToast(activity, tip);
    }

    /**
     * 根据订单号查询订单信息，并显示公告
     * todo 旧代码逻辑
     */
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private void showNoticeAfterPay(final Context context, String url, final String moid, final int times) {
        if (TextUtils.isEmpty(url)) {
            SQLog.w(TAG + "公告链接为空, moid=" + moid);
            return;
        }
        int delay = 10000;
        if (times == 1) {
            // 第一次仅延迟5秒，后续的都到3分钟
            delay = 5000;
        }
        MRequestManager requestManager = new MRequestManager(context);
        mHandler.postDelayed(() -> requestManager.payQueryRequst(url, moid, new MRequestCallBack() {

            @Override
            public void onRequestSuccess(String content) {
                try {
                    JSONObject jsonObj = new JSONObject(content);
                    JSONObject data = jsonObj.getJSONObject("data");
                    String url = data.optString("nurl");
                    if (!TextUtils.isEmpty(url)) {
                        SQLog.i(TAG + moid + " 显示公告: " + url);
                        // 如果存在公告地址的话
                        MultiSDKUtils.showNoticeDialog(context, "", url);
                    } else {
                        SQLog.d(TAG + moid + " 无需显示公告");
                    }
                } catch (JSONException e) {
                    SQLog.w(TAG + moid + " 查询订单解析失败, 不弹窗");
                }
            }

            @Override
            public void onRequestError(String errorMsg) {
                // 查询5次，每次
                if (times < 5) {
                    SQLog.w(TAG + moid + " 查询订单失败, " + errorMsg + ", 重试: " + times);
                    showNoticeAfterPay(context, url, moid, times + 1);
                } else {
                    SQLog.w(TAG + moid + " 查询订单失败, " + errorMsg + ", 不弹窗");
                }
            }
        }), delay);

    }
}
