package com.sqwan.msdk.api.popup;

import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.dialog.pop.BasePopupDialogManager;

/**
 * 激活弹窗管理类
 */
public class ActivePopupDialogManager extends BasePopupDialogManager {

    private static ActivePopupDialogManager instance;


    private ActivePopupDialogManager() {

    }

    public static ActivePopupDialogManager getInstance() {
        if (instance == null) {
            synchronized (ActivePopupDialogManager.class) {
                if (instance == null) {
                    instance = new ActivePopupDialogManager();
                }
            }
        }
        return instance;
    }


    @Override
    public void requestPopup() {
        PopupDialogHttpUtil.requestActivePopup(new SimpleSqHttpCallback<String>() {

            @Override
            public void onSuccess(String data) {
                setPopupData(data);
            }
        });
    }

    @Override
    public String getDesc() {
        return "激活";
    }
}
