package com.sqwan.msdk.api.popup;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.dialog.pop.BasePopupDialogManager;

/**
 * 支付弹窗管理类
 */
public class PayPopupDialogManager extends BasePopupDialogManager {

    private static PayPopupDialogManager instance;

    private String doid, moid, dmoney, operateType;

    private PayPopupDialogManager() {

    }

    public static PayPopupDialogManager getInstance() {
        if (instance == null) {
            synchronized (PayPopupDialogManager.class) {
                if (instance == null) {
                    instance = new PayPopupDialogManager();
                }
            }
        }
        return instance;
    }

    /**
     * 处理支付后弹窗
     *
     * @param context 上下文
     * @param doid 研发订单id
     * @param moid 37订单
     * @param dmoney 金额
     * @param operateType 1:充值成功 2:充值失败 3:取消 4:未知
     */
    public void handlePopup(Context context, String doid, String moid, String dmoney, String operateType) {
        this.doid = doid;
        this.moid = moid;
        this.dmoney = dmoney;
        this.operateType = operateType;
        super.handlePopup(context);
    }

    @Override
    public void requestPopup() {
        PopupDialogHttpUtil.requestPayPopup(moid, doid, dmoney, operateType, new SimpleSqHttpCallback<String>() {

            @Override
            public void onSuccess(String data) {
                setPopupData(data);
                showPopupDialog(null);
            }
        });
    }

    @Override
    public String getDesc() {
        return "支付后";
    }
}
