package com.sqwan.msdk.api.popup;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.IMUrl;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.core.INewUrl;
import java.util.HashMap;

public class PopupDialogHttpUtil {

    /**
     * 请求激活弹窗
     */
    public static void requestActivePopup(SqHttpCallback<String> callBack) {
        LogUtil.i("请求激活弹窗");
        SqRequest.of(IMUrl.URL_M_INIT_DIALOG)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .post(callBack, String.class);
    }


    /**
     * 进服弹窗
     */
    public static void requestSubmitRolePopup(HashMap<String, String> infos, SqHttpCallback<String> callback) {
        LogUtil.i("请求进服弹窗");
        Context context = SQContextWrapper.getActivity();
        if (context == null) {
            return;
        }
        SqRequest.of(IMUrl.URL_SUBMIT_ROLE_POPUP)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .addParam("dsid", infos.get(SQwanCore.INFO_SERVERID))
            .addParam("drid", infos.get(SQwanCore.INFO_ROLEID))
            .addParam("dsname", infos.get(SQwanCore.INFO_SERVERNAME))
            .addParam("drname", infos.get(SQwanCore.INFO_SERVERNAME))
            .addParam("drlevel", infos.get(SQwanCore.INFO_ROLELEVEL))
            .addParam("dviplevel", infos.get(SQwanCore.INFO_VIPLEVEL))
            .addParam("drctime", infos.get(SQwanCore.INFO_ROLE_TIME_CREATE))
            .addParam("drlevelmtime", infos.get(SQwanCore.INFO_ROLE_TIME_LEVEL))
            .addParam("dpname", infos.get(SQwanCore.INFO_PARTYNAME))
            .addParam("drbalance", infos.get(SQwanCore.INFO_BALANCE))
            .addParam("token", AccountCache.getToken(context))
            .post(callback, String.class);
    }

    /**
     * 请求支付弹窗
     */
    public static void requestPayPopup(String moid, String doid, String dmoney, String operateType,
        SqHttpCallback<String> callback) {
        LogUtil.i("请求支付弹窗");
        Context context = SQContextWrapper.getActivity();
        SqRequest.of(IMUrl.URL_PAY_POPUP)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .addParam("token", AccountCache.getToken(context))
            .addParam("moid", moid)
            .addParam("doid", doid)
            .addParam("dmoney", dmoney)
            .addParam("operate_type", operateType)
            .addParam("dsid", MultiSDKUtils.getServerid(context))
            .addParam("drid", MultiSDKUtils.getRoleid(context))
            .addParam("dsname", MultiSDKUtils.getServerName(context))
            .addParam("drname", MultiSDKUtils.getRolename(context))
            .post(callback, String.class);
    }


    public static void requestOrderPopups(String moid, String doid, String dmoney, String drlevel, String dpt,
        SqHttpCallback<String> callback) {
        LogUtil.i("请求支付前弹窗");
        Context context = SQContextWrapper.getActivity();
        SqRequest.of(INewUrl.URL_POPUPS_ORDER)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .addParam("token", AccountCache.getToken(context))
            .addParam("moid", moid)
            .addParam("doid", doid)
            .addParam("dmoney", dmoney)
            .addParam("dpt", dpt)
            .addParam("scene","sdk")
            .addParam("dsid", MultiSDKUtils.getServerid(context))
            .addParam("drid", MultiSDKUtils.getRoleid(context))
            .addParam("dsname", MultiSDKUtils.getServerName(context))
            .addParam("drname", MultiSDKUtils.getRolename(context))
            .addParam("drlevel", drlevel)
            .post(callback, String.class);
    }
}
