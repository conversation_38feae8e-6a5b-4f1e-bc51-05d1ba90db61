package com.sqwan.msdk.api;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.support.v4.content.FileProvider;
import android.util.Log;
import android.widget.Toast;

import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.views.SQConfirmDialog;
import com.sqwan.msdk.views.SQConfirmDialog.ConfirmListener;
import com.sqwan.msdk.views.SQUpdateDialog;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class SQUpdateManager {


	/**
	 * 更新类型:1, 忽略更新;2, 普通更新;3, 强制更新
	 */
	public static final String TYPE_NORMAL = "1";
	public static final String TYPE_UPDATE = "2";
	public static final String TYPE_FORCE = "3";

	public static String updateType = TYPE_NORMAL;

	public static void checkUpdateConfig(Context context, JSONObject data) throws Exception {
		if (!data.isNull("utype")) {
			// 是否需要更新
			updateType = data.getString("utype");
			String apkUrl = data.getString("uurl");
			//更新的url
			String updateContent = data.getString("uct");
			//更新的内容
			String version = "1.0";
			if (TYPE_NORMAL.equals(updateType)) {

			} else if (TYPE_UPDATE.equals(updateType)) {
				SQUpdateManager.checkUpdate(context, false,
						updateContent, apkUrl, version);
			} else if (TYPE_FORCE.equals(updateType)) {
				SQUpdateManager.checkUpdate(context, true,
						updateContent, apkUrl, version);
			}
		}
	}


	/**检查更新
	 * @param context
	 * @param isForce //是否强制更新
	 * @param content	//更新内容
	 * @param url	//更新地址
	 * @param version	//更新版本号
	 */
	public static void checkUpdate(Context context,boolean isForce,String content,String url,String version){
		String filename = getFileNameOfUrl(context, url, version);
		String filepath  = getSDPath(context)+filename;
		File file = new File(filepath);
		if (file.exists() && readFileLength(context, filename) == file.length()) {
			//文件存在且和记录的大小一致则直接安装。
			checkAndInstall(isForce,context, file);
		}else {
			//不存在文件后者文件大小不一致
			SQUpdateDialog  update_dialog = new SQUpdateDialog(context, isForce, content, url, version);
			update_dialog.show();
		}

	}


	//--------------------------------------一些方法类-----------------------------------
	/**
	 * 记录文件大小用来比对
	 */
	public static final String UPDATE_PREF = "update_pref";

	public static void saveFileLength(Context context,String filename,long size){
		SharedPreferences sp = context.getSharedPreferences(UPDATE_PREF, Context.MODE_PRIVATE);
		sp.edit().putLong(filename, size).commit();
	}

	public static long readFileLength(Context context,String filename){
		SharedPreferences sp = context.getSharedPreferences(UPDATE_PREF, 0);
		return sp.getLong(filename, 0);
	}

	public static String getFileNameOfUrl(Context context,String url,String version){
		//20151111单身节，加上“.apk”作为结尾，防止出现下载下来后不是apk无法被解析的情况。
		String fileName ;
		if (url !=null && !"".equals(url)) {

			String pre = url.substring(url.lastIndexOf("/")+1);

			if (!"".equals(pre) && pre !=null) {

				if (pre.contains(".apk")) {
					fileName = pre;
				}else {
					fileName  = pre+".apk";
				}

			}else {
				String gid =  MultiSDKUtils.getGID(context);
				String pid =  MultiSDKUtils.getPID(context);
				String refer =  MultiSDKUtils.getRefer(context);
				fileName =gid+"_"+pid+"_"+refer+".apk";
			}
			//加上版本号以示区别
			fileName = version+"_"+fileName;
			System.out.println("下载的文件名："+fileName);
			return fileName;

		}else {
			return null;
		}
	}

	/**获取sd中存储路径
	 * @param context
	 * @return
	 */
	public static String getSDPath(Context context) {
		return EnvironmentUtils.getCommonSubDirPath(context,"download");

	}

	/**安装apk文件
	 * @param context
	 * @param file
	 * @return
	 */
	public static boolean installApk(Context context, File file) {
		if(!file.exists()) {
			return false;
		}
		Intent intent = new Intent();
		intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		intent.setAction(Intent.ACTION_VIEW);
		String type = "application/vnd.android.package-archive";
		LogUtil.e("安装apk");
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			LogUtil.e( "安装apk，使用provider");
			Uri uri = FileProvider.getUriForFile(context, context.getPackageName() + ".provider", file);
			intent.setDataAndType(uri, type);
			intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
		} else {
			intent.setDataAndType(Uri.fromFile(file), type);
		}
		context.startActivity(intent);
		return true;
	}

	/**检查并安装app
	 * @param file
	 */
	public static void  checkAndInstall(final boolean isForce,final Context context,final File file){
		if (file != null ) {
			String path = file.getAbsolutePath();
			if (path.endsWith(".apk")) {
				//是安装文件
				String msg = "更新已完成,是否安装？";
				final SQConfirmDialog confirmDialog = new SQConfirmDialog(context,msg);
				confirmDialog.setConfirmListenr(new ConfirmListener() {

					@Override
					public void onConfirm() {
						if(!SQUpdateManager.installApk(context, file)) {
							showTips(context, "APK 文件不存在");
							confirmDialog.dismiss();
						}
					}

					@Override
					public void onCancel() {
						if (isForce) {
							showTips(context, "此次更新为强制更新。\n为了给您更好的游戏体验，更新后才能进入游戏。");
						}else {
							confirmDialog.dismiss();
						}
					}
				});
				if (isForce) {
					//强制更新不可取消。
					confirmDialog.setCancelable(false);
				}
				confirmDialog.show();
			}else {
				showTips(context, "安装失败：文件格式不对。");
			}
		}else {
			showTips(context, "安装失败：安装文件为空，请重新下载");
		}
	}

	/**显示提示和打印log
	 * @param context
	 * @param tips
	 */
	public static void showTips(Context context ,String tips){
		try{
			ToastUtil.showToast(context, tips);
		}catch (Exception e){
			e.printStackTrace();
		}
		System.out.println(tips);
	}


	/**
	 * @param name
	 * @param type
	 * @param packageName
	 * @param context
	 * @return
	 */
	@Deprecated
	public static int getIdByName(String name,String type,String packageName,Context context){
		int id = SqResUtils.getIdByName(name, type, context);
		return id;
	}

	@Deprecated
	public static String getCurrentDate(){
		DateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		String date = format.format(new Date());
		LogUtil.w("获取的时间是："+date);
		return date;
	}



}
