package com.sqwan.msdk.api;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.SystemClock;

import com.sq.diagnostic.assistant.log.SQLogUtils;

/**
 *    author : 黄锦群
 *    time   : 2022/10/11
 *    desc   : SDK 诊断助手摇一摇开启管理类
 */
public class DiagnosticSensorManager implements SensorEventListener {

    private static final String TAG = "DiagnosticSensorManager";

    private static final DiagnosticSensorManager INSTANCE = new DiagnosticSensorManager();

    /** 重心感应对象（可能为空） */
    private SensorManager mSensorManager;

    /** 注册监听标记 */
    private boolean mRegisterFlag;

    /** 速度阈值，当摇晃速度达到这值后产生作用 */
    private static final int SPEED_THRESHOLD = 25000;

    /** 两次检测的时间间隔 */
    private static final int UPDATE_INTERVAL_TIME = 70;

    /** 手机上一个位置时重力感应坐标 */
    private float mLastX;
    private float mLastY;
    private float mLastZ;

    /** 上次检测时间 */
    private long mLastUpdateTime;

    private SensorChangedCallback mCallback;

    private long mRegisterSensorTime;

    private long mTriggerCallbackTime;

    public static DiagnosticSensorManager getInstance() {
        return INSTANCE;
    }

    public void init(Context context) {
        if (context == null) {
            return;
        }

        Context applicationContext = context.getApplicationContext();
        if (applicationContext == null) {
            return;
        }

        mSensorManager = (SensorManager) applicationContext.getSystemService(Context.SENSOR_SERVICE);
    }

    public void registerSensorChangedCallback(SensorChangedCallback callback) {
        mCallback = callback;
        registerSensorListener();
    }

    public void unregisterSensorChangedCallback() {
        mCallback = null;
        unregisterSensorListener();
    }

    /**
     * {@link SensorEventListener}
     */

    @Override
    public void onSensorChanged(final SensorEvent event) {
        // 现在检测时间
        long currentUpdateTime = System.currentTimeMillis();
        // 两次检测的时间间隔
        long timeInterval = currentUpdateTime - mLastUpdateTime;

        // 判断是否达到了检测时间间隔
        if (timeInterval < UPDATE_INTERVAL_TIME) {
            return;
        }
        // 现在的时间变成last时间
        mLastUpdateTime = currentUpdateTime;

        // 获得x,y,z坐标
        float x = event.values[0];
        float y = event.values[1];
        float z = event.values[2];

        // 获得 x，y，z 的变化值
        float deltaX = x - mLastX;
        float deltaY = y - mLastY;
        float deltaZ = z - mLastZ;

        // 将现在的坐标变成 last 坐标
        mLastX = x;
        mLastY = y;
        mLastZ = z;

        double speed = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ) / timeInterval * 10000;
        // 达到速度阀值，发出提示
        if (speed < SPEED_THRESHOLD) {
            return;
        }

        long currentUptimeMillis = SystemClock.uptimeMillis();

        // 如果注册摇一摇监听在 5 秒前，则断定是系统触发了摇一摇，而不是用户主动触发的
        if (currentUptimeMillis - mRegisterSensorTime < 5000) {
            SQLogUtils.i(TAG, "The system triggered a shake, interval time = " + (currentUptimeMillis - mRegisterSensorTime));
            mTriggerCallbackTime = currentUptimeMillis;
            return;
        }

        // 如果是上次触发诊断助手的时间小于 10 秒，则这个手机很可能是传感器有问题，需要屏蔽此操作
        if (currentUptimeMillis - mTriggerCallbackTime < 10000) {
            SQLogUtils.i(TAG, "It could be that the system triggers a lot of shaking,  interval time = " + (mTriggerCallbackTime - currentUptimeMillis));
            mTriggerCallbackTime = currentUptimeMillis;
            return;
        }

        mTriggerCallbackTime = currentUptimeMillis;

        SQLogUtils.i(TAG, "Listen to shake shake, speed = " + speed);
        if (mCallback != null) {
            mCallback.onSensorChanged(event);
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        SQLogUtils.d(TAG,"onAccuracyChanged accuracy " + accuracy);
    }

    public void onResume() {
        if (mSensorManager == null) {
            return;
        }
        if (mCallback == null) {
            return;
        }
        if (mRegisterFlag) {
            return;
        }
        registerSensorListener();
    }

    public void onPause() {
        if (mSensorManager == null) {
            return;
        }
        if (mCallback == null) {
            return;
        }
        if (!mRegisterFlag) {
            return;
        }
        unregisterSensorListener();
    }

    private void registerSensorListener() {
        if (mSensorManager == null) {
            return;
        }
        mSensorManager.registerListener(this,
                mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER),
                SensorManager.SENSOR_DELAY_FASTEST);
        mRegisterFlag = true;
        mRegisterSensorTime = SystemClock.uptimeMillis();
    }

    private void unregisterSensorListener() {
        if (mSensorManager == null) {
            return;
        }
        mSensorManager.unregisterListener(this);
        mRegisterFlag = false;
    }

    public interface SensorChangedCallback {

        void onSensorChanged(SensorEvent event);
    }
}