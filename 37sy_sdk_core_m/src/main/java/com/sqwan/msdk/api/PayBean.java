package com.sqwan.msdk.api;

public class PayBean {
	public String change_id;				//订单号；360、91、anzhi、duoku、jifeng、xiaomi、oppo
	public String productId;			//商品ID；360、91
	public String productName;			//商品名称；360、91、dcn、duoku、oppo
	public String productDescription;	//商品描述；91、anzhi、duoku、oppo

	public String notifyUrl;			//充值回调地址;360、OPPO

	public int money;					//充值金额；360、5G、91、anzhi、dcn、duoku、jifeng、xiaomi、oppo、uc
	public String object;				//自定义字段；360、5G、anzhi、dcn、xiaomi、uc
	public String serverId;					//区服；5G,UC<UC的服务ID是整形>

	public String rate;					//1人民币与游戏币的比例；360,duoku

	public String access_token;			//access_token:360
	public String refresh_token;		//refresh_token; 360
	public String userid;				//用户ID；360
	public String userName;				//用户名;360
	public String gameName;				//游戏名称;360

	public int type;					//0-走默认的，1-走5Gwan


	/**以下参数为小米专用**/
	public String game_user_balance;
	public String game_user_gamer_vip;
	public String game_user_lv;
	public String game_user_party_name;
	public String game_user_role_name;
	public String game_user_roleid;
	public String game_user_server_name;

	/***vivo**/
	public String signature;
	public String transNo;

	//
	public String waresid;	//商品码

	public int getMoney() {
		return money;
	}
	public void setMoney(int money) {
		this.money = money;
	}
	public String getObject() {
		return object;
	}
	public void setObject(String object) {
		this.object = object;
	}
	public String getServerId() {
		return serverId;
	}
	public void setServerId(String sid) {
		this.serverId = sid;
	}
	public String getChange_id() {
		return change_id;
	}
	public void setChange_id(String orderId) {
		this.change_id = orderId;
	}
	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public String getProductDescription() {
		return productDescription;
	}
	public void setProductDescription(String productDescription) {
		this.productDescription = productDescription;
	}
	public String getNotifyUrl() {
		return notifyUrl;
	}
	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getRefresh_token() {
		return refresh_token;
	}
	public void setRefresh_token(String refresh_token) {
		this.refresh_token = refresh_token;
	}

	public String getAccess_token() {
		return access_token;
	}
	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	public String getGameName() {
		return gameName;
	}
	public void setGameName(String gameName) {
		this.gameName = gameName;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public String getGame_user_balance() {
		return game_user_balance;
	}
	public void setGame_user_balance(String game_user_balance) {
		this.game_user_balance = game_user_balance;
	}
	public String getGame_user_gamer_vip() {
		return game_user_gamer_vip;
	}
	public void setGame_user_gamer_vip(String game_user_gamer_vip) {
		this.game_user_gamer_vip = game_user_gamer_vip;
	}
	public String getGame_user_lv() {
		return game_user_lv;
	}
	public void setGame_user_lv(String game_user_lv) {
		this.game_user_lv = game_user_lv;
	}
	public String getGame_user_party_name() {
		return game_user_party_name;
	}
	public void setGame_user_party_name(String game_user_party_name) {
		this.game_user_party_name = game_user_party_name;
	}
	public String getGame_user_role_name() {
		return game_user_role_name;
	}
	public void setGame_user_role_name(String game_user_role_name) {
		this.game_user_role_name = game_user_role_name;
	}
	public String getGame_user_roleid() {
		return game_user_roleid;
	}
	public void setGame_user_roleid(String game_user_roleid) {
		this.game_user_roleid = game_user_roleid;
	}
	public String getGame_user_server_name() {
		return game_user_server_name;
	}
	public void setGame_user_server_name(String game_user_server_name) {
		this.game_user_server_name = game_user_server_name;
	}
	public String getSignature() {
		return signature;
	}
	public void setSignature(String signature) {
		this.signature = signature;
	}
	public String getTransNo() {
		return transNo;
	}
	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}
	public String getWaresid() {
		return waresid;
	}
	public void setWaresid(String waresid) {
		this.waresid = waresid;
	}



}
