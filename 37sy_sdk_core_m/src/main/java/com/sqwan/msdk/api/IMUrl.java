package com.sqwan.msdk.api;

import static com.sy37sdk.account.UrlConstant.URL_M_LOGIN_POPUP;

import android.util.Log;

import com.sqwan.base.L;
import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.common.track.TrackUrl;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sy37sdk.account.update.UpdateUrlManager;

import org.json.JSONObject;

public class IMUrl {
    public static final String MODE = android.os.Build.MODEL;
    public static final String OS = "android";
    public static final String OSVER = "android" + android.os.Build.VERSION.RELEASE;

    public static final String M_ACTIVE = "m_activate";
    public static final String POP_UPS_ACTIVE_API = "pop_ups_active_api";
    public static final String POP_UPS_ENTER_API = "pop_ups_enter_api";
    public static final String POP_UPS_RECHARGE_API = "pop_ups_recharge_api";
    public static final String KEY_M_VERIFY_TOKEN = "vptapi";
    public static final String KEY_M_ORDER = "oapi";
    public static final String KEY_M_SUBMIT = "lapi";
    public static final String KEY_M_ENTER = "eapi";
    public static final String KEY_ACTIVATION_CODE_CHECK = "vbcapi";
    public static final String KEY_PUSH = "push_url";
    public static final String KEY_REPORT_M_KEY = "report_m_dev";
    public static final String KEY_M_PAY = "pay";
    public static final String KEY_TRACK = "track";

    /*
     * 初始化接口
     */
    @UrlUpdate(value = M_ACTIVE, xValue = "x_activate_m")
    public static String URL_M_INIT = "https://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/active/";

    /**
     * 激活弹窗
     */
    @UrlUpdate(value = POP_UPS_ACTIVE_API, xValue = "x_popups_active")
    public static String URL_M_INIT_DIALOG = "https://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/popups/active";


    /**
     * 进服弹窗
     */
    @UrlUpdate(value = POP_UPS_ENTER_API, xValue = "x_popups_enter")
    public static String URL_SUBMIT_ROLE_POPUP = "https://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/popups/enter";

    /**
     * 支付弹窗
     */
    @UrlUpdate(value = POP_UPS_RECHARGE_API, xValue = "x_popups_recharge")
    public static String URL_PAY_POPUP = "https://m-api"  + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/popups/recharge";

    /*
     * 验证token接口
     */
    @UrlUpdate(KEY_M_VERIFY_TOKEN)
    public static String URL_M_VAREFY_TOKEN = "http://vt-api." + MultiSdkManager.APP_HOST + "/verify/ptoken/";

    /*
     * 生成订单接口
     */
    @UrlUpdate(value = KEY_M_ORDER,xValue = "x_order_m")
    public static String URL_M_ORDER = "http://mpay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/order/";

    /*
     * 提交统计数据接口
     */
    @UrlUpdate(KEY_M_SUBMIT)
    public static String URL_M_SUBMIT = "http://log.api." + MultiSdkManager.APP_HOST + "/sdk/tj/";


    /*
     * 新推送接口
     */
    @UrlUpdate(KEY_PUSH)
    public static String URL_PUSH = "http://push.api." + MultiSdkManager.APP_HOST + "/sdk/push/";


    /*
     * 激活码查询接口
     */
    @UrlUpdate(value = KEY_ACTIVATION_CODE_CHECK, xValue = "x_vbcapi")
    public static String URL_ACTIVATION_CODE_CHECK = "http://m-api" + MultiSdkManager.SECURE_SUFFIX  + MultiSdkManager.APP_HOST + "/sdk/vbetac/";

    /*
     * 用户角色信息接口
     */
    @UrlUpdate(value = KEY_M_ENTER, xValue = "x_enter")
    public static String URL_M_ENTER = "http://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/enter/";


    /**
     * 设备标识上报接口
     */
    @UrlUpdate(KEY_REPORT_M_KEY)
    public static String URL_REPORT_MDEV = "http://m-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportDev";


    /*
     * 默认充值接口
     */
    @UrlUpdate(KEY_M_PAY)
    public static String URL_PAY_DEFAULT = "http://" + MultiSdkManager.APP_HOST + "/sdk/pay/";

    /*
     * 语音信息接口
     */
    @Deprecated
    public static String URL_M_SPEECH = "https://pvt-api." + MultiSdkManager.APP_HOST + "/gvoice/getroomconf/";

    /*
     * 退出语音房间接口
     */
    @Deprecated
    public static String URL_M_SPEECH_EXITROOM = "https://pvt-api." + MultiSdkManager.APP_HOST + "/gvoice/exitroom/";

    /*
     * 订单查询接口,
     *  m层order 接口更新
     */
    public static String URL_PAY_QUERY = "http://mpay-api." + MultiSdkManager.APP_HOST + "/payment/state/";

    /**
     * 数据采集监控接口
     */
    @UrlUpdate(KEY_TRACK)
    public static String URL_DATA_TRACK = "https://track." + MultiSdkManager.APP_HOST + "/api/event/";

    /**
     * 获取分享物料
     */
    public static String GET_SHARE_SOURCE= "https://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/share";
//    public static String GET_SHARE_SOURCE= "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/share";

}