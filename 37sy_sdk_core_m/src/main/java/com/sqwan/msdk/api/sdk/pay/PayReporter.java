package com.sqwan.msdk.api.sdk.pay;

import android.support.annotation.Nullable;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.ReportDataBuilder;
import com.sqwan.order.base.SqPayError;

/**
 * <AUTHOR>
 * @since 2024/9/9
 */
public class PayReporter {

    private PayReporter() {
    }

    public static void reportPayInvoke(PayContext payCtx, PayInfoModel payInfo) {
        report(SqTrackAction2.pay_invoke, dataBuilder(payCtx).payInfo(payInfo));
    }

    public static void reportPaySuccess(PayContext payCtx) {
        report(SqTrackAction2.pay_succ, dataBuilder(payCtx));
    }

    public static void reportPayFail(PayContext payCtx, SqPayError error) {
        report(SqTrackAction2.pay_fail, dataBuilder(payCtx).error(error));
    }

    public static void reportPayCancel(PayContext payCtx) {
        report(SqTrackAction2.pay_cancel, dataBuilder(payCtx).put("pay_cancel_way", payCtx.getCancelWay()));
    }

    private static ReportDataBuilder dataBuilder(@Nullable PayContext payCtx) {
        return new ReportDataBuilder().payCtx(payCtx);
    }

    private static void report(SqTrackAction2 event, ReportDataBuilder data) {
        SqTrackActionManager2.getInstance().trackAction(event, data.map);
    }
}
