package com.sqwan.msdk.api;

/**
 * 注意: 这个类不能删, 因为SQAppConfig类在旧宿主中没有继承ISQAppConfig, 所以宿主加载新插件时, 不能直接返回新的SQAppConfig, 需要返回本类
 * 实际上类名可以随意, 但是因为以前的产物也有这个类, 所以保留类名
 */
public class SQAppConfigImpl extends SQAppConfig implements ISQAppConfig {

    private final String gameid;

    private final String partner;

    private final String refer;

    public SQAppConfigImpl(SQAppConfig config) {
        this.gameid = config.getGameid();
        this.partner = config.getPartner();
        this.refer = config.getRefer();
    }

    @Override
    public String getGameid() {
        return gameid;
    }

    @Override
    public String getPartner() {
        return partner;
    }

    @Override
    public String getRefer() {
        return refer;
    }
}
