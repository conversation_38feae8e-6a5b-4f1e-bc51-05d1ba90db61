package com.sqwan.msdk.api;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import android.text.method.NumberKeyListener;
import android.widget.EditText;
import com.parameters.bean.WebDialogBean;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.TelephonyInfoUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.common.web.SY37PortraitWebPage;
import com.sqwan.common.web.SY37web;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sqwan.msdk.views.SQActivationCodeDialog;
import com.sqwan.msdk.views.SQActivationCodeDialog.CheckActivationCodeCallback;
import com.sy37sdk.bean.DeviceInfo;
import com.sy37sdk.utils.DeviceTools;
import com.sy37sdk.utils.Util;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Properties;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.json.JSONObject;

public class MultiSDKUtils {

    private static final String SQ_PREFS = "sq_prefs";
    private static final String SQ_CHANNL_PREFS = "sq_channl_prefs";

    private static final String USERID = "userid";
    private static final String USERNAME = "username";
    // 20150615新增第三方用户名和name
    private static final String P_USERID = "puid";// 第三方平台UID
    private static final String P_USERNAME = "puname";// 第三方平台
    private static final String TOKEN = "token";
    private static final String PASSWORD = "pd";

    private static final String GID = "gid"; // appid
    private static final String PID = "pid";
    private static final String REFER = "refer";

    private static final String KEY = "multiAppkey"; // appkey
    private static final String DEV = "dev";
    private static final String CODE_LOGIN = "login_cut";
    private static final String CODE_PAY = "pay_cut";
    private static final String YYB_PARAM = "yyb";
    // 一些地址
    private static final String URL_PAY_NEW = "pay_url_new"; // 充值截流地址
    private static final String URL_PUSH = "push_url"; // 推送接口
    private static final String URL_PAY_QUERY = "pay_url_query"; // 订单查询
    private static final String URL_M_VERIFY_TOKEN = "vptapi"; // 验证37手游TOKEN接口
    private static final String URL_M_ORDER = "oapi"; // 游戏充值按钮调用的下单接口
    private static final String URL_M_SUBMIT = "lapi"; // 数据统计日志接口
    private static final String URL_M_ENTER = "eapi"; // 提交用户角色信息接口
    public static final String URL_M_POP_ACTIVE = "pop_ups_active_api"; // m层激活弹窗
    public static final String URL_M_POP_LOGIN = "pop_ups_login_api"; // m层请求登录弹窗
    public static final String URL_M_POP_SUBMIT_ROLE = "pop_ups_enter_api"; // 进服弹窗
    public static final String URL_M_POP_PAY = "pop_ups_recharge_api"; // 支付弹窗

    // push时间
    private static final String PUSH_DELAY = "push_delay";
    private static final String PUSH_IS_DELAY = "push_is_delay";// 配置是否延迟push，主要是方便测试

    // 新增缓存IMEI 和 MAC
    private static final String DEV_IMEI = "dev_imei";
    private static final String DEV_MAC = "dev_mac";
    private static DeviceInfo deviceInfo = new DeviceInfo();// 20161019增加管理mac和imei的设备类
    private static String devInfoImei = "";

    //201706 增加角色信息
    private static final String INFO_SERVERID = "dsid";
    private static final String INFO_ROLEID = "drid";
    private static final String INFO_ROLENAME = "drname";
    private static final String INFO_ROLELEVEL = "drlevel";
    private static final String INFO_VIP_LEVEL = "viplevel";
    private static final String INFO_SERVER_NAME = "serverName";

    //20191025 增加设备标识信息
    private static final String IDENTIFY = "identify";

    //是否登录过
    public static final String LOGINED = "logined";

    // -------------------------------------------------------------------------------------------

    public static void setString(Context context, String key, String value) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(key, value);
        editor.commit();
    }

    public static void removeString(Context context, String key) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.remove(key);
        editor.commit();
    }

    public static String getString(Context context, String key) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getString(key, "");
    }

    public static void setBoolean(Context context, String key, Boolean value) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putBoolean(key, value);
        editor.commit();
    }

    public static void removeBoolean(Context context, String key) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.remove(key);
        editor.commit();
    }

    public static boolean getBoolean(Context context, String key) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getBoolean(key, false);
    }

    // -------------------------------------------------------------------------------------------

    public static void setPushIsDelay(Context context, boolean isDelay) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putBoolean(PUSH_IS_DELAY, isDelay);
        editor.commit();
    }

    /**
     * 是否开启PUSH延迟,默认true，开启
     */
    public static boolean getPushIsDelay(Context context) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getBoolean(PUSH_IS_DELAY, true);
    }

    public static void setPushDelay(Context context, int time) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putInt(PUSH_DELAY, time);
        editor.commit();
    }

    public static int getPushDelay(Context context) {
        SharedPreferences sp = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getInt(PUSH_DELAY, 6 * 60 * 60 * 1000);
    }

    /**
     * 推送地址
     */
    public static void setPushUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_PUSH, url);
        editor.commit();
    }

    public static String getPushUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_PUSH, IMUrl.URL_PUSH);
    }

    /**
     * 充值查询接口
     */
    public static void setPayQueryUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_PAY_QUERY, url);
        editor.commit();
    }

    public static String getPayQueryUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_PAY_QUERY, IMUrl.URL_PAY_QUERY);
    }

    /**
     * 推送地址
     */
    public static void setPayOrderUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_ORDER, url);
        editor.commit();
    }

    public static String getPayOrderUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_ORDER, IMUrl.URL_M_ORDER);
    }

    /**
     * 验证token
     */
    public static void setVerifyTokenUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_VERIFY_TOKEN, url);
        editor.commit();
    }

    public static String getVerifyTokenUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_VERIFY_TOKEN, IMUrl.URL_M_VAREFY_TOKEN);
    }

    /**
     * 数据统计地址
     */
    public static void setSubmitUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_SUBMIT, url);
        editor.commit();
    }

    public static String getSubmitUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_SUBMIT, IMUrl.URL_M_SUBMIT);
    }

    // ------------------
    @Deprecated
    public static void setNewPayUrl(Context context, String newPayUrl) {
        SharedPreferences uiState = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(URL_PAY_NEW, newPayUrl);
        editor.commit();
    }

    @Deprecated
    public static String getNewPayUrl(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(
                MultiSDKUtils.SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(URL_PAY_NEW, IMUrl.URL_PAY_DEFAULT);
    }


    @Deprecated
    public static void setCodeOfPay(Context context, String payCode) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(CODE_PAY, payCode);
        editor.commit();
    }

    @Deprecated
    public static String getCodeOfPay(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(CODE_PAY, "0");
    }

    /**
     * 使用 ConfigManager
     */
    @Deprecated
    public static void setCodeOfLogin(Context context, String loginCode) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(CODE_LOGIN, loginCode);
        editor.commit();
    }

    /**
     * 使用 ConfigManager
     */
    @Deprecated
    public static String getCodeOfLogin(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(CODE_LOGIN, "0");
    }

    public static void setDevID(Context context, String dev) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(DEV, dev);
        editor.commit();
    }

    @Deprecated
    public static String getDevID(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(DEV, MultiSDKUtils.getLocalDev(context));
    }

    /**
     * 此方法的作用是判断是否有dev的缓存
     */
    private static String getDevCache(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(DEV, "");
    }

    /**
     * 判断系统接口能否获取imei
     */
    private static boolean canGetImeiFromApi(Context context) {
        return !TextUtils.isEmpty(Util.getIMEI(context));
    }

    public static void setGID(Context context, String gid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(GID, gid);
        editor.commit();
    }

    public static String getGID(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(GID, "1000001");
    }

    public static void setPID(Context context, String pid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(PID, pid);
        editor.commit();
    }

    public static String getPID(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(PID, "1");
    }

    public static void setYYB(Context context, String data) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(YYB_PARAM, data);
        editor.commit();
    }

    public static String getYYB(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(YYB_PARAM, "");
    }

    /**
     * 请使用 ConfigManager
     *
     * @link
     */
    @Deprecated
    public static void setKey(Context context, String key) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(KEY, key);
        editor.commit();
    }

    /**
     * 请使用 ConfigManager
     */
    @Deprecated
    public static String getKey(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(KEY, "");
    }

    public static void setRefer(Context context, String refer) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(REFER, refer);
        editor.commit();
    }

    public static String getRefer(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(REFER, "sy00000_1");
    }

    public static void setToken(Context context, String token) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(TOKEN, token);
        editor.commit();
    }

    public static String getToken(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(TOKEN, "");
    }

    public static void setPlatUsername(Context context, String username) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(P_USERNAME, username);
        editor.commit();
    }

    public static String getPlatUsername(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(P_USERNAME, "");
    }

    public static void setPlatUserid(Context context, String userid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(P_USERID, userid);
        editor.commit();
    }

    public static String getPlatUserid(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(P_USERID, "");
    }

    public static void setMDevIds(Context context, String identify) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(IDENTIFY, identify);
        editor.commit();
    }

    public static String getMDevIds(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(IDENTIFY, "");
    }

    public static void setUsername(Context context, String username) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERNAME, username);
        editor.commit();
    }

    public static String getUsername(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(USERNAME, "");
    }

    public static void setUserid(Context context, String userid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERID, userid);
        editor.commit();
    }

    public static String getUserid(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(USERID, "");
    }

    public static void setPassword(Context context, String password) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(PASSWORD, password);
        editor.commit();
    }

    public static String getPassword(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(PASSWORD, "");
    }

    public static void setLogined(Context context, boolean logined) {
        SpUtils.get(context, SQ_PREFS).put(LOGINED, logined);
    }

    public static boolean getLogined(Context context) {
        return SpUtils.get(context, SQ_PREFS).getBoolean(LOGINED, false);
    }

    /**
     * 取用户填入的支付金额--用于定额支付的SDK，在支付前让用户填入金额
     */
    public static void getPayMoney(final Context context,
                                   final SQResultListener listener) {
        final EditText money = new EditText(context);
        money.setKeyListener(new DigitsKeyListener(false, true));
        money.setFilters(new InputFilter[]{new InputFilter.LengthFilter(4)});
        money.setKeyListener(new NumberKeyListener() {

            @Override
            public int getInputType() {

                return android.text.InputType.TYPE_CLASS_PHONE;
            }

            @Override
            protected char[] getAcceptedChars() {

                return new char[]{'0', '1', '2', '3', '4', '5', '6', '7',
                        '8', '9'};
            }
        });
        money.setHint("请输入充值金额(不超过1万元)");

        Dialog alertDialog = new AlertDialog.Builder(context)
                .setTitle("请输入充值金额")
                .setView(money)
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        String moneyText = money.getText().toString();
                        if (moneyText.length() == 0
                                || Integer.parseInt(moneyText) == 0) {
                            showTips(context, "请输入大于0的金额");
                            // 不关闭对话框
                            try {
                                Field field = dialog.getClass().getSuperclass()
                                        .getDeclaredField("mShowing");
                                field.setAccessible(true);
                                field.set(dialog, false); // 设定为false,则不可以关闭对话框
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            // 关闭对话框
                            try {
                                Field field = dialog.getClass().getSuperclass()
                                        .getDeclaredField("mShowing");
                                field.setAccessible(true);
                                field.set(dialog, true); // 设定为false,则不可以关闭对话框
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            Bundle b = new Bundle();
                            b.putInt("money", Integer.parseInt(moneyText));
                            listener.onSuccess(b);
                            dialog.dismiss();
                        }
                    }
                })
                .setNegativeButton("取消", new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        // 关闭对话框
                        try {
                            Field field = dialog.getClass().getSuperclass()
                                    .getDeclaredField("mShowing");
                            field.setAccessible(true);
                            field.set(dialog, true); // 设定为false,则不可以关闭对话框
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        listener.onFailture(206, "用户取消支付【20005】");
                        dialog.dismiss();
                    }
                }).setCancelable(false).create();
        alertDialog.show();
    }

    /**
     * 取得asstes下的渠道号
     *
     * @param context
     * @return public static String getAssetsAdkey(Context context){ String
     *         adkey = ""; try { InputStreamReader inputReader = new
     *         InputStreamReader
     *         (context.getResources().getAssets().open("fg.dat"));
     *         BufferedReader bufReader = new BufferedReader(inputReader); adkey
     *         = bufReader.readLine(); bufReader.close(); adkey =
     *         adkey.trim().equals("")?"10000":adkey.trim(); } catch (Exception
     *         e) { adkey = "10000"; }
     *
     *         return adkey; }
     */

    /**
     * 取assets下的配置参数
     */
    public static Properties readPropertites(Context context, String file) {
        Properties p = null;
        try {
            InputStream in = context.getResources().getAssets().open(file);
            p = new Properties();
            p.load(in);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            p = null;
        }
        return p;
    }

    /**
     * MD5加密
     */
    public static String Md5(String string) {
        byte[] hash;
        try {
            hash = MessageDigest.getInstance("MD5").digest(
                    string.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Huh, MD5 should be supported?", e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Huh, UTF-8 should be supported?", e);
        }

        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }

    /**
     * 获取App安装包信息
     */
    public static PackageInfo getPackageInfo(Context context, String pack) {
        PackageInfo info = null;
        try {
            info = context.getPackageManager().getPackageInfo(pack, 0);
        } catch (NameNotFoundException e) {
            e.printStackTrace(System.err);
        }
        if (info == null) {
            info = new PackageInfo();
        }
        return info;
    }

    public static String getPackageInfos(Context context) {
        JSONObject jsonObject = new JSONObject();
        try {
            Process process = Runtime.getRuntime().exec("pm list package -3");
            BufferedReader bis = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            while ((line = bis.readLine()) != null) {
                line = line.replace("package:", "");
                String name =
                        (String) getPackageInfo(context, line).applicationInfo.loadLabel(context.getPackageManager());
                jsonObject.put(line, name);
            }
        } catch (Exception e) {
            System.out.println("MultiSDKUtils.runCommand,e=" + e);
        }

        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }

        return jsonObject.toString();
    }

    public static String getIMEI(Context context) {
        return getIMEI(context, true);
    }

    public static String getIMEI(Context context, boolean isAuthCheck) {
        if (!isAuthCheck) return "";
        String imei = "";
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P && TextUtils.isEmpty(getDevCache(context)) && !canGetImeiFromApi(context)) {
            imei = ImeiLogic.getInstance(context).getValue();
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                devInfoImei = Util.getIMEI(context);
                LogUtil.w("devInfoImei=" + devInfoImei);
                imei = !TextUtils.isEmpty(devInfoImei) ? devInfoImei : randomDeverceNum(
                        context, 15, deviceInfo);// 20160831增加如果没有获取到imei就传一个随机数
            } else {
                devInfoImei = Util.getIMEI(context);
                LogUtil.w("devInfoImei=" + devInfoImei);
                imei = !TextUtils.isEmpty(devInfoImei) ? devInfoImei : randomDeverceNum(
                        context, 15, deviceInfo);// 20160831增加如果没有获取到imei就传一个随机数
            }

        }
        if (TextUtils.isEmpty(imei)) {
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.imei_empty);
        }
        return imei;
    }


    private static String randomToString(Context context, int length) {
        Random random = new Random(System.currentTimeMillis());
        String randomNum = "";
        for (int i = 0; i < length; i++) {
            int subNum = random.nextInt(9);
            randomNum += subNum;
        }

        return randomNum;
    }

    /**
     * 随机生成imei码和mac地址
     */
    private static String randomDeverceNum(Context context, int length,
                                           DeviceInfo deviceInfo) {

        if (deviceInfo == null) {
            deviceInfo = new DeviceInfo();
        }

        String num = "";
        num = randomToString(context, length);

        List<DeviceInfo> deviceList = DeviceTools.getDeviceFromFile(context);

        if (length == 12) {
            if (deviceList != null && !deviceList.isEmpty()) {
                String mac = deviceList.get(0).getDevMac();
                if ("".equals(mac) || null == mac) {
                    deviceInfo.setDevMac(num);
                    DeviceTools.setDeviceToFile(context, deviceInfo);
                }
                return deviceList.get(0).getDevMac();
            } else {
                deviceInfo.setDevMac(num);
                if ("".equals(devInfoImei.trim()) || null == devInfoImei) {
                    deviceInfo.setDevImei(randomToString(context, 15));
                }
            }
        } else {
            if (deviceList != null && !deviceList.isEmpty()) {
                String imei = deviceList.get(0).getDevImei();
                if ("".equals(imei) || null == imei) {
                    deviceInfo.setDevImei(num);
                    DeviceTools.setDeviceToFile(context, deviceInfo);
                }
                return deviceList.get(0).getDevImei();
            } else {
                deviceInfo.setDevImei(num);
            }
        }
        DeviceTools.setDeviceToFile(context, deviceInfo);

        return num;
    }

    public static String getSIM(Context context) {
        return TelephonyInfoUtils.getSimSerialNumber(context);
    }

    public static String getIMSI(Context context) {
        return TelephonyInfoUtils.getSubscriberId(context);
    }

    public static String getMac(Context context) {
        return getMac(context, true);
    }

    public static String getMac(Context context, boolean isAuthCheck) {
        if (!isAuthCheck) {
            return "";
        }
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P && TextUtils.isEmpty(getDevCache(context)) && !canGetImeiFromApi(context)) {
            return MacLogic.getInstance(context).getValue();
        } else {
            String macAddress = SensitiveInfoManager.getInstance().getMacAddress(context);
            String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……& amp;*（）——+|{}【】‘；：”“’。，、？]";
            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(macAddress != null ? macAddress : "");
            return m != null ? m.replaceAll("").trim() : randomDeverceNum(context,
                    12, deviceInfo);
        }
    }

    public static String getLocalDev(Context context) {
        String key = "-:&d4@zXqm-pLgW";
        return Md5(getDevMac(context) + getDevImei(context) + key).toLowerCase();
    }

    /**
     * @return String 返?乩嘈?
     * @Title: getDisplayScreenResolution(手机的分辨率)
     * <AUTHOR>
     * @data 2013-8-10 下午3:41:29
     * @param: @param mContext
     * @param: @return
     */
    private static String getDisplayScreenResolution(Context mContext) {
        int screen_w = 0;
        int screen_h = 0;
        try {
            screen_h = mContext.getResources().getDisplayMetrics().heightPixels;
            screen_w = mContext.getResources().getDisplayMetrics().widthPixels;
        } catch (Exception e) {
            return screen_w + "*" + screen_h;
        }
        return screen_w + "*" + screen_h;
    }

    /**
     * @return int 返回类型
     * @Title: getWpixels(横屏的像素)
     * @param: @param mContext
     * @param: @return
     */
    public static int getWpixels(Context mContext) {

        String screenResolution = getDisplayScreenResolution(mContext);
        String wpixels = screenResolution.substring(0,
                screenResolution.indexOf("*"));
        return Integer.valueOf(wpixels);
    }

    /**
     * @return int 返回类型
     * @Title: getHpixels(竖屏的像素)
     * @param: @param mContext
     * @param: @return
     */
    public static int getHpixels(Context mContext) {
        String screenResolution = getDisplayScreenResolution(mContext);
        String hpixels = screenResolution.substring(screenResolution
                .indexOf("*") + 1);
        return Integer.valueOf(hpixels);
    }

    /**
     * 获取制造厂商
     */
    public static String getBrand() {
        return Build.MODEL;
    }

    /**
     * getNumber(获取手机号。注：有些手机卡获取不到手机号) (这里描述这个方法适用条件 – 可选)
     *
     * @return String
     * @throws
     */
    public static String getNumber(Context context) {
        return TelephonyInfoUtils.getLine1Number(context);
    }


    /**
     * 根据资源名字来反射出资源id author:chuan
     *
     * @param name        资源的名字
     * @param type        资源的类型
     * @param packageName 项目的报名
     * @return 资源的id
     */
    public static int getIdByName(String name, String type, String packageName, Context context) {
        int id = SqResUtils.getIdByName(name, type, context);
        return id;
    }

    /**
     * 检查是否安装某个apk
     */
    public static boolean checkPackInstalled(Context context, String pack) {
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(pack, 0);
        } catch (NameNotFoundException e) {
            packageInfo = null;
        }

        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 根据包名启动app
     */
    public static void launchApp(Context context, String packName) {
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(
                packName);
        context.startActivity(intent);
    }

    /**
     * 获取软件版本号
     */
    public static int getVersionCode(Context context) {
        int versionCode = 0;
        try {
            // 获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionCode = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), 0).versionCode;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionCode;
    }

    /**
     * 获取软件版本名称 用于更新
     */
    public static String getVersionName(Context context) {
        String versionName = "1.0.0.0";

        try {
            // 获取软件版本号，对应AndroidManifest.xml下android:versionName
            versionName = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), 0).versionName;
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

    /**
     * 获取软件版本名称 用于更新
     */
    public static Drawable getAppIcon(Context context) {
        Drawable icon = null;

        try {
            // 获取软件版本号，对应AndroidManifest.xml下android:icon
            PackageManager pm = context.getPackageManager();
            icon = pm.getPackageInfo(context.getPackageName(), 0).applicationInfo
                    .loadIcon(pm);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return icon;
    }

    /**
     * 显示toast提示
     */
    public static void showTips(Context context, String tips) {
        try {
            ToastUtil.showToast(context, tips);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检测网络是否可用
     */
    public static boolean isNetworkConnected(Context context) {

        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo ni = cm.getActiveNetworkInfo();
        return ni != null && ni.isConnectedOrConnecting();
    }

    /**
     * 获取当前网络类型
     *
     * @time 2015年05月26日15:46:42新增
     * <AUTHOR>
     */
    public static String getNetType(Context context) {

        String netType = "NULL"; // 默认为停网状态

        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo ni = cm.getActiveNetworkInfo();

        if (ni != null) {
            netType = ni.getTypeName();
        }

        return netType;
    }

    /**
     * 显示公告框
     */
    public static void showNoticeDialog(Context context, String title,
                                        String url) {

        if (url == null || "".equals(url)) {
            return;
        }

        SQWebViewDialog dialog = new SQWebViewDialog(context);
        dialog.setCancelable(false);
        dialog.setTitle(title);
        dialog.setUrl(AppUtils.constructWebUrlParam(context, url));
        dialog.show();
    }

    /**
     * 显示实名制页面
     */
    public static void showAuthDialog(Context context, String url) {

    }


    /**
     * 显示SQWebDialog
     *
     * @param url webview加载的地址
     */
    public static void showSQWebDialog(Context context, String url) {
        WebDialogBean webDialog = new WebDialogBean();
        webDialog.setShowToolBar(false);
        webDialog.setUrl(url);
        showSQWebDialog(context, webDialog);
    }

    public static void showSQWebDialog(Context context, WebDialogBean webDialog) {
        showSQWebDialog(context, webDialog, null);
    }

    public static void showSQWebDialog(Context context, WebDialogBean webDialog, DialogInterface.OnDismissListener listener) {
        if (webDialog == null || TextUtils.isEmpty(webDialog.getUrl())) {
            return;
        }
        String url = webDialog.getUrl();
        Uri uri = Uri.parse(url);
        String forceOrientation = uri.getQueryParameter("forceOrientation");
        if (TextUtils.equals(forceOrientation, "1") && !isScreenOriatationPortrait(context)) { // 强制竖屏
            boolean supportPlugin = isSupportPlugin();
            Intent intent = new Intent();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra(SY37web.WEB_URL, url);
            intent.setClass(context, supportPlugin ? SY37web.class : SY37PortraitWebPage.class);
            intent.putExtra("screenOrientation", "portrait");
            context.startActivity(intent);
        } else {
            String adaptNotchScreen = uri.getQueryParameter("adaptNotchScreen");
            if ((context instanceof Activity) && !((Activity) context).isFinishing()) {
                SQWebViewDialog dialog = new SQWebViewDialog(context);
                dialog.setUrl(webDialog.getUrl());
                dialog.setShowWebBar(webDialog.isShowToolBar());
                dialog.setOnDismissListener(listener);
                dialog.setCancelable(true);
                if (TextUtils.equals(adaptNotchScreen, "1") && isScreenOriatationPortrait(context)) {
                    dialog.setAdaptNotchScreen(true);
                }
                dialog.setAllowJumpURL(false);
                dialog.show();
            } else {
                LogUtil.i("context is not an Activity or Activity is finishing ");
            }
        }
    }


    private static boolean isSupportPlugin() {
        try {
            Class<?> sqwanCoreClass = Class.forName("com.sqwan.msdk.SQwanCore");
            Method getInstanceMethod = sqwanCoreClass.getMethod("getInstance");
            Object sqwanCoreInstance = getInstanceMethod.invoke(null);
            Method isSupportPluginMethod = sqwanCoreClass.getMethod("isSupportPlugin");
            boolean isSupported = (boolean) isSupportPluginMethod.invoke(sqwanCoreInstance);
            LogUtil.i("isSupportPlugin() 返回值：" + isSupported);
            return isSupported;
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            LogUtil.e("反射 isSupportPlugin 方法失败", e);
        }
        return false;
    }

    /**
     * 显示激活弹窗
     *
     * @param context
     * @param title
     * @param url
     */

    static SQActivationCodeDialog dialog;

    public static void showActivationCodeDialog(Context context, String beta,
                                                CheckActivationCodeCallback callback) {

        if (dialog == null) {

            dialog = new SQActivationCodeDialog(context);
        }
        dialog.setCancelable(false);
        dialog.setBetaData(beta);
        dialog.setCallback(callback);
        dialog.show();
    }

    public static void hideActivationCodeDialog() {

        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    /**
     * 网络下载图片
     *
     * @param link    下载地址
     * @param handler 1=传回bitmap，-1=失败
     */
    public static void downLoadBitmap(final String link, final Handler handler) {

        new Thread(new Runnable() {

            @Override
            public void run() {

                try {
                    URL url = new URL(link);
                    HttpURLConnection conn = (HttpURLConnection) url
                            .openConnection();
                    conn.setConnectTimeout(5000);
                    conn.setRequestMethod("GET");
                    if (conn.getResponseCode() == 200) {
                        InputStream inStream = conn.getInputStream();
                        Bitmap bitmap = BitmapFactory.decodeStream(inStream);
                        handler.obtainMessage(1, bitmap).sendToTarget();
                    } else {
                        handler.obtainMessage(-1).sendToTarget();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    handler.obtainMessage(-1).sendToTarget();
                }
            }
        }).start();
    }

    /**
     * 安装apk文件
     */
    public static boolean installApkByPath(Context context, String filePath) {
        try {
            Intent intent = new Intent();
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setAction(Intent.ACTION_VIEW);
            String type = "application/vnd.android.package-archive";

            File f = new File(filePath);
            if (f.exists() && filePath.endsWith(".apk")) {
                System.out.println("文件存在，开始安装" + f.getAbsolutePath());
                intent.setDataAndType(Uri.fromFile(f), type);
                context.startActivity(intent);
            } else {
                System.out.println("文件不存在,或者不是apk文件!");
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @return true 为竖屏，反之横屏
     */
    public static boolean isScreenOriatationPortrait(Context context) {
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.setPackage(context.getPackageName());


        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            for (ResolveInfo info : context.getPackageManager().queryIntentActivities(intent, PackageManager.MATCH_ALL)) {
                LogUtil.d("sqsdk", "[activtiy]" + info.activityInfo.name + " screenOrientation: " + info.activityInfo.screenOrientation);
                if (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_PORTRAIT || info.activityInfo.screenOrientation == SCREEN_ORIENTATION_SENSOR_PORTRAIT ||
                        info.activityInfo.screenOrientation == SCREEN_ORIENTATION_REVERSE_PORTRAIT || info.activityInfo.screenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_PORTRAIT) {
                    return true;
                }

            }
        } else {
            for (ResolveInfo info : context.getPackageManager().queryIntentActivities(intent, 0)) {
                LogUtil.d("sqsdk", "[activtiy]" + info.activityInfo.name + " screenOrientation: " + info.activityInfo.screenOrientation);
                if (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_PORTRAIT || info.activityInfo.screenOrientation == SCREEN_ORIENTATION_SENSOR_PORTRAIT ||
                        info.activityInfo.screenOrientation == SCREEN_ORIENTATION_REVERSE_PORTRAIT || info.activityInfo.screenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_PORTRAIT) {
                    return true;
                }
            }
        }
        return false;
    }

    // 保存mac到本地

    /**
     * @deprecated 请使用 DevLogic, MacLogic 来获取相关字段
     */
    @Deprecated
    public static void setDevMac(Context context, String mac) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(DEV_MAC, mac);
        editor.commit();
    }

    /**
     * @deprecated 请使用 DevLogic, MacLogic 来获取相关字段
     */
    @Deprecated
    public static String getDevMac(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(DEV_MAC, "");
    }

    // 保存imei到本地

    /**
     * @deprecated 请使用 DevLogic, ImeiLogic 来获取相关字段
     */
    @Deprecated
    public static void setDevImei(Context context, String imei) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(DEV_IMEI, imei);
        editor.commit();
    }

    /**
     * @deprecated 请使用 DevLogic, ImeiLogic 来获取相关字段
     */
    @Deprecated
    public static String getDevImei(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(DEV_IMEI, "");
    }

    /** 20160802 增加 */

    /**
     * 提交用户角色信息地址
     */
    public static void setEnterUrl(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_ENTER, url);
        editor.commit();
    }

    public static String getEnterUrl(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_ENTER, IMUrl.URL_M_ENTER);
    }

    /**
     * m层激活弹窗地址
     */
    public static void setUrlMPopActive(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_POP_ACTIVE, url);
        editor.commit();
    }

    public static String getUrlMPopActive(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_POP_ACTIVE, IMUrl.URL_M_INIT_DIALOG);
    }

    /**
     * m层登录弹窗地址
     */
    public static void setUrlMPopLogin(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_POP_LOGIN, url);
        editor.commit();
    }

    /**
     * 进服弹窗地址
     */
    public static void setUrlSubmitRolePop(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_POP_SUBMIT_ROLE, url);
        editor.commit();
    }

    public static String getUrlSubmitRolePop(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_POP_SUBMIT_ROLE, IMUrl.URL_SUBMIT_ROLE_POPUP);
    }


    /**
     * 支付弹窗地址
     */
    public static void setUrlPayPop(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(URL_M_POP_PAY, url);
        editor.commit();
    }

    public static String getUrlPayPop(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return sp.getString(URL_M_POP_PAY, IMUrl.URL_PAY_POPUP);
    }

    //201708添加
    //保存角色服务器id
    public static void setServerid(Context context, String serverid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_SERVERID, serverid);
        editor.commit();
    }

    public static String getServerid(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_SERVERID, "");
    }

    //保存角色id
    public static void setRoleid(Context context, String roleid) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_ROLEID, roleid);
        editor.commit();
    }

    public static String getRoleid(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_ROLEID, "");
    }

    //保存角色name
    public static void setRolename(Context context, String rolename) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_ROLENAME, rolename);
        editor.commit();
    }

    public static String getRolename(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_ROLENAME, "");
    }

    //保存角色的vip等级
    public static void setRolelevel(Context context, String rolelevel) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_ROLELEVEL, rolelevel);
        editor.commit();
    }

    public static String getRolelevel(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_ROLELEVEL, "");
    }

    //保存区服名称
    public static void setServerName(Context context, String severName) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_SERVER_NAME, severName);
        editor.commit();
    }

    public static String getServerName(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_SERVER_NAME, "");
    }

    //保存角色level
    public static void setVipLevel(Context context, String viplevel) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putString(INFO_VIP_LEVEL, viplevel);
        editor.commit();
    }

    public static String getVipLevel(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(INFO_VIP_LEVEL, "");
    }

    public static String constructCommonURL(Context context, String url) {
        String appendUrl = "?gid=" + MultiSDKUtils.getGID(context) +
                "&pid=" + MultiSDKUtils.getPID(context) +
                "&dev=" + DevLogic.getInstance(context).getValue() +
                "&token=" + MultiSDKUtils.getToken(context) +
                "&sversion=" + VersionUtil.sdkVersion +
                "&refer=" + MultiSDKUtils.getRefer(context) +
                "&gwversion=" + VersionUtil.gwversion;

        if (url.contains("?")) {

            appendUrl = appendUrl.replace("?", "&");
        }
        return url + appendUrl;
    }

    /**
     * 比较版本号的高低
     *
     * @return if version1 > version2, return 1, if equal, return 0, else return
     * -1
     */
    public static int VersionComparison(String versionServer, String versionLocal) {
        String version1 = versionServer;
        String version2 = versionLocal;
        if (version1 == null || version1.length() == 0 || version2 == null || version2.length() == 0) {
            return 0;
        }

        int index1 = 0;
        int index2 = 0;
        while (index1 < version1.length() && index2 < version2.length()) {
            int[] number1 = getValue(version1, index1);
            int[] number2 = getValue(version2, index2);

            if (number1[0] < number2[0]) {  //当服务器的版本<本地的版本
                return -1;
            } else if (number1[0] > number2[0]) {
                return 1;
            } else {
                index1 = number1[1] + 1;
                index2 = number2[1] + 1;
            }
        }
        if (index1 == version1.length() && index2 == version2.length()) {
            return 0;
        }
        if (index1 < version1.length()) {
            return 1;
        } else {
            return -1;
        }
    }

    /**
     * @param index the starting point
     * @return the number between two dots, and the index of the dot
     */
    public static int[] getValue(String version, int index) {
        int[] value_index = new int[2];
        StringBuilder sb = new StringBuilder();
        while (index < version.length() && version.charAt(index) != '.') {
            sb.append(version.charAt(index));
            index++;
        }
        value_index[0] = Integer.parseInt(sb.toString());
        value_index[1] = index;

        return value_index;
    }
}
