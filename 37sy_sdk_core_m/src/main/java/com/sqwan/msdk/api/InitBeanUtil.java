package com.sqwan.msdk.api;

import android.content.Context;
import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.utils.ZipString;
import java.io.File;
import java.util.Properties;


public class InitBeanUtil {
    /***
     * 开启调试文件
     * **/
    private static final String DEBUG_FILE = "sqwan-test";

    public static InitBean inflactBean(Context context, Properties prop) {
        InitBean bean = null;
        if (prop != null) {
            bean = new InitBean();
            if (prop.getProperty("usesdk") == null) {
                bean.setUsesdk(1);
            } else {
                bean.setUsesdk(Integer.parseInt(prop.getProperty("usesdk")));
            }

            bean.setAppid(prop.getProperty("appid") != null ? ZipString.zipString2Json(prop.getProperty("appid")) : "");
            bean.setAppkey(prop.getProperty("appkey") != null ? ZipString.zipString2Json(prop.getProperty("appkey")) : "");
            bean.setRefer(prop.getProperty("refer") != null ? prop.getProperty("refer") : "");

            bean.setMerchantId(prop.getProperty("merchantId"));
            bean.setServerSeqNum(prop.getProperty("serverSeqNum"));

            bean.setGameId(prop.getProperty("gameId"));
            bean.setServerId(prop.getProperty("serverId"));
            bean.setRate(prop.getProperty("rate"));
            bean.setGameName(prop.getProperty("gameName"));
            bean.setChannel(prop.getProperty("channel"));
            bean.setLandScape(Integer.parseInt(prop.getProperty("isLandScape") == null ? "0" : prop.getProperty("isLandScape")));

            bean.setIsFixed(Integer.parseInt(prop.getProperty("isFixed") == null ? "1" : prop.getProperty("isFixed")));
            //默认是1-定额
            bean.setIsTencentPayTest(Integer.parseInt(prop.getProperty("isTencentPayTest") == null ?
                    "0" : prop.getProperty("isTencentPayTest")));
            //默认不是沙箱测试环境

            //是否开启调试
            bean.setDebug(isDebug(context,prop));

            bean.setTestTag(Integer.parseInt(prop.getProperty("testTag") == null ? "0" : prop.getProperty("testTag")));
            bean.setUcDebug(Integer.parseInt(prop.getProperty("ucDebug") == null ? "0" : prop.getProperty("ucDebug")));

            bean.setPayid(prop.getProperty("payid"));
            bean.setPaykey(prop.getProperty("paykey"));

            bean.setWxAppid(prop.getProperty("wxAppid"));
            bean.setWxAppkey(prop.getProperty("wxAppkey"));

            bean.setIsSplashShow(Integer.parseInt(prop.getProperty("isSplashShow") == null ? "0" : prop.getProperty("isSplashShow")));
            bean.setUsePlatformExit(Integer.parseInt(prop.getProperty("usePlatformExit") == null ? "0" : prop.getProperty("usePlatformExit")));
            bean.setUseSQExit(Integer.parseInt(prop.getProperty("useSQExit") == null ? "1" : prop.getProperty("useSQExit")));
            bean.setIsPushDelay(Integer.parseInt(prop.getProperty("isPushDelay") == null ? "1" : prop.getProperty("isPushDelay")));

            bean.setIsSDK202(Integer.parseInt(prop.getProperty("isSDK202") == null ? "0" : prop.getProperty("isSDK202")));
            bean.setIsSDK210(Integer.parseInt(prop.getProperty("isSDK210") == null ? "0" : prop.getProperty("isSDK210")));

            bean.setIsLogDetect(Integer.parseInt(prop.getProperty("isLogDetect") == null ? "0" : prop.getProperty("isLogDetect")));
        } else {
            LogUtil.w("prop配置文件为null");
        }
        return bean;
    }

    /**
     * 读取测试机的文件，如果存在则开启调试模式。
     */
    private static boolean isDebugModeByFile(Context context) {
        String filePath = EnvironmentUtils.getCommonDirPath(context) + File.separator + DEBUG_FILE;
        LogUtil.i(filePath);
        File file = new File(filePath);
        if (file.exists() && file.isDirectory()) {
            LogUtil.i("sqwan-test directory is exists");
            return true;
        } else {
            LogUtil.i("sqwan-test directory is not exists");
        }
        return false;
    }

    private static int isDebug(Context context,Properties prop){
        return isDebugModeByFile(context) ? 1 : 0;
    }
}
