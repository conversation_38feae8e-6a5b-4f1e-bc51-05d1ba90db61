package com.sqwan.msdk.api;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.util.SQContextWrapper;
import java.util.HashMap;
import java.util.Map;

/**
 * 专门给m层激活用的通用参数
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
class MInitParams extends CommonParamsV1 {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        params = super.transform(params);
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();

        params.put("dev", MultiSDKUtils.getDevID(context) + "");
        params.put("dev2", DevLogic.getInstance(context).getValue());

        return params;
    }
}
