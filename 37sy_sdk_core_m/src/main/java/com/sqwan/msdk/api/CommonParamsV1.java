package com.sqwan.msdk.api;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.data.cache.DevManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/1
 */
class CommonParamsV1 implements ParamsTransformer {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();
        String gid = MultiSDKUtils.getGID(context);
        String pid = MultiSDKUtils.getPID(context);
        String refer = MultiSDKUtils.getRefer(context);
        String version = AppUtils.getVersionName(context);
        params.put("gid", gid);
        params.put("pid", pid);
        params.put("refer", refer);
        params.put("version", version);
        params.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("dev", DevLogic.getInstance(context).getValue());

        String androidId = DevManager.getAndroidId(DevLogic.getInstance(context).isAuthCheck());
        params.put("android_id", androidId == null ? "" : androidId);
        params.put("sversion", VersionUtil.sdkVersion);
        params.put("gwversion", VersionUtil.gwversion);
        // 宿主的版本号
        params.put("host_sdk_version", VersionUtil.getOriginalVersion());
        params.put(SqConstants.IS_ROOT, RootLogic.getInstance(context).getValue());
        params.put(SqConstants.IS_SIMULATOR, SimulatorLogic.getInstance(context).getValue());

        return params;
    }
}
