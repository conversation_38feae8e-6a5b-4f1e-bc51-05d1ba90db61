package com.sqwan.msdk.api.sdk;

import static com.sqwan.msdk.BaseSQwanCore.sendLog;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import com.parameters.bean.WebDialogBean;
import com.parameters.performfeatureconfig.PerformFeatureKey;
import com.parameters.performfeatureconfig.PerformFeatureType;
import com.parameters.utils.ClassCheckUtils;
import com.sq.eventbus.core.EventBus;
import com.sq.oaid.sq_oaid.SqOAIDHelper;
import com.sq.oaid.sq_oaid.SqOAIDHelper.Callback;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.EventReporter;
import com.sq.tool.network.ExceptionReporter;
import com.sq.tools.Logger;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.user.RoleInfo;
import com.sqwan.common.user.UserInfoManager;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.BaseSQwanCore;
import com.sqwan.msdk.SQReportCore;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.MRequestManager;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.PluginContext;
import com.sqwan.msdk.api.PurchaseReportBean;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.SQSdkInterface;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sqwan.msdk.utils.ViewUtils;
import com.sqwan.msdk.utils.ZipString;
import com.sqwan.msdk.views.SQActivationCodeDialog.CheckActivationCodeCallback;
import com.sqwan.order.base.IPay;
import com.sqwan.order.base.IPay.PayCallback;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.age.AppropriateAge;
import com.sy37sdk.account.age.AppropriateAgeCacheHelper;
import com.sy37sdk.account.age.AppropriateAgeManager;
import com.sy37sdk.account.auth.AuthConfigCache;
import com.sy37sdk.account.auth.AuthManager;
import com.sy37sdk.account.config.ConfigManager;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.scanCode.ScanCodeCameraActivity;
import com.sy37sdk.core.SQScreenshotListener;
import com.sy37sdk.core.SQwan;
import com.sy37sdk.order.SQPay;
import com.sy37sdk.order.OrderTrackManager;
import com.sy37sdk.order.PayConstants;
import com.sy37sdk.order.PayVersionUtil;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import org.json.JSONException;
import org.json.JSONObject;

public abstract class Platform implements SQSdkInterface {

    protected static final String TAG = "【Platform】";
    protected static SQwan sq;
    protected static Context context;
    protected static InitBean init;
    protected String codeOfLogin;

    protected static SQResultListener listener;// 登录监听
    protected static SQResultListener initListener;
    protected static SQResultListener switchAccountListener;
    protected static SQResultListener back2GameListener;// 回到游戏登录界面
    public static SQResultListener shareListener; // 微信分享回调

    protected static SQResultListener speachInitListener;// 实例化语音接口的回调
    protected static SQResultListener joinToRoomListener;// 进入语音房间的回调
    protected static SQResultListener quitToRoomListener;// 退出语音房间的回调
    protected static SQResultListener statusUpdateListener;// 掉线的回调
    protected static SQResultListener memberVoiceListener;// 成员状态回调

    public static boolean upingData25g = false;

    protected HashMap<String, String> userMap = new HashMap<String, String>();

    protected MRequestManager requestManager = null;

    protected boolean isNeedInputMoney = true;// 如果第三方充值支持不定额支付，则设置为false；
    protected String pdata = "";

    protected HashMap<String, String> loginCallbackExtendParams = null;

    private IScreenshotListener mScreenshotListener;//截图监听

    private Handler handler = new Handler(Looper.getMainLooper());

    // 为了兼容旧版本的静态方法
    private static final SQPay sSQPay = new SQPay();


    // -----------------------------第三方渠道接口说明 start--------------------------
    // 必接的分别为【平台初始化】、【平台登录】、【平台支付】、【切换账号】和【退出游戏】接口
    // 其他均为按需选接。
    // 除了下述三个接口，其他接口必须先集成父类，super.XXX();
    protected abstract void initPlatform();

    protected abstract void loginPlatform(SQResultListener loginListener);

    protected abstract void payPlatform(Context context, String doid,
                                        String dpt, String dcn, String dsid, String dsname, String dext,
                                        String drid, String drname, int drlevel, float dmoney, int dradio,
                                        String moid, String data, SQResultListener payListener);

    /**
     * 为了兼容旧版本, 所以默认返回空
     */
    protected IPay getPlatformPay() {
        return null;
    }

    // public void changeAccount(final Context context,final SQResultListener
    // listener)
    // public void logout(Context context, final SQResultListener listener)
    // -----------------------------第三方渠道接口说明 end--------------------------

    public Platform(Context cxt, InitBean bean, SQResultListener initListener) {

        sendLogPlatform("Platform的初始化");

        init = bean;
        Platform.context = cxt;
        Platform.initListener = initListener;
        requestManager = new MRequestManager(context);
        codeOfLogin = MultiSDKUtils.getCodeOfLogin(context);

        sendLogPlatform("调用初始化信息, id:" + init.getAppid() + " key:"
                + init.getAppkey() + " codeOfLogin:" + codeOfLogin);
        EventDispatcher.getInstance().clear();
    }

    public void init(Context context) {
        if (isCut2SQ()) {
            sendLogPlatform("单平台初始化");
            initSQ(false);
        } else {
            sendLogPlatform("多平台初始化");
            initPlatform();
        }

        sSQPay.init(context);
        // 为了兼容旧版本
        SqPayManager.getInstance().setPlatform(this);
        SqPayManager.getInstance().setSQPay(sSQPay);
        SqPayManager.getInstance().setPlatformPay(getPlatformPay());
    }

    @Override
    public void login(Context context, final SQResultListener loginListener) {
        SQLog.d(TAG + "调用login");
        Platform.listener = loginListener;

        if (upingData25g) {
            // TODO: 2024/2/5 是什么逻辑?
            SQLog.w(TAG + "upingData25g, 忽略调用");
            ViewUtils.showToast(context, "处理中，请稍候.");
            return;
        }

        if (context == null) {
            SQLog.e(TAG + "context为空, 忽略调用");
            return;
        }

        upingData25g = true;

        if (isCut2SQ()) {// 1:转移调用37SDK( S )初始化方法,其它值不转移

            SQLog.d(TAG + "login-切37");

            upingData25g = false;

            loginSQ(loginListener);

        } else {
            SQLog.d(TAG + "login-Platform: " + getClass().getSimpleName());
            loginPlatform(loginListener);
        }

    }

    /**
     * 登陆成功返回数据通用回调 便于统一返回数据
     *
     * @param content
     * @param loginListerner
     */
    protected void loginSuccessCallBack(String content,
                                        final SQResultListener loginListerner) {

        try {

            JSONObject jsonObj = new JSONObject(content);
            int state = jsonObj.getInt("state");
            JSONObject data = jsonObj.getJSONObject("data");

            if (state == 1) {

                String token = data.getString("token");
                String uid = data.getString("uid");
                String uname = data.getString("uname");
                String puid = data.getString("puid");
                String puname = data.getString("puname");
                String beta = "";

                if (data.has("pwd")) {
                    sendLogPlatform("sq loginSuccessCallBack,has pwd!");
                    String pwd = data.getString("pwd");
                    if (!TextUtils.isEmpty(uname) && !TextUtils.isEmpty(pwd)) {
                        // ******** 修复多平台登录不再返回用户信息的bug
                        MultiSDKUtils.setPassword(context,
                                ZipString.json2ZipString(pwd));
                        // 同时存在uname和pwd，存储进文件中
                        // 广告线不会走到这里
//						UserInfo user = new UserInfo();
//						user.setUname(uname);
//						user.setUpwd(ZipString.json2ZipString(pwd));
//						AccountTools.setAccountToFile(context, user);
                    }

                }

                MultiSDKUtils.setToken(context, token);
                // MultiSDKUtils.setChannlToken(context, token);
                MultiSDKUtils.setUserid(context, uid);
                MultiSDKUtils.setUsername(context, uname);
                MultiSDKUtils.setPlatUserid(context, puid);
                MultiSDKUtils.setPlatUsername(context, puname);

                final Bundle bundle = new Bundle();
                bundle.putString(SQwanCore.TOKEN, token);

                // 登录成功后将 uid 和用户名返回给研发，这样研发会方便许多
                // 但是需要注意的一个点是，研发不能直接拿这个 uid 和用户名来做重要的事情，只能简单做一下界面展示
                bundle.putString(SQwanCore.LOGIN_KEY_USERID, uid);
                bundle.putString(SQwanCore.LOGIN_KEY_USERNAME, uname);

                SQAppConfig config = SQwanCore.getInstance().getAppConfig();
                bundle.putString(SQwanCore.LOGIN_KEY_GID, config.getGameid());
                bundle.putString(SQwanCore.LOGIN_KEY_PID, config.getPartner());

                // 20170326，新增登录返回扩展参数
                // 某些渠道业务需要，登录返回会带有扩展参数
                if (loginCallbackExtendParams != null) {
                    for (Entry<String, String> entry : loginCallbackExtendParams
                            .entrySet()) {
                        bundle.putString(entry.getKey(), entry.getValue());
                    }
                }

                sendLogPlatform("调用登录接口:与37交互成功，返回onSuccess信息:"
                        + bundle.toString());

                // 2015年07月27日 新加限号功能
                if (!data.isNull("beta")) { // 有相关激活码数据

                    beta = data.getJSONObject("beta").toString();

                    /*
                     * 激活码验证逻辑
                     */
                    if (!TextUtils.isEmpty(beta)) {

                        MultiSDKUtils.showActivationCodeDialog(context, beta,
                                new CheckActivationCodeCallback() {

                                    @Override
                                    public void onActiveSucecess() {

                                        loginListerner.onSuccess(bundle);
                                    }

                                });

                    } else {
                        MultiSDKUtils.hideActivationCodeDialog();
                        loginListerner.onSuccess(bundle);
                    }
                } else { // 无限号

                    MultiSDKUtils.hideActivationCodeDialog();
                    loginListerner.onSuccess(bundle);
                }

            } else if (state == 0) {

                String errInfo = jsonObj.getString("msg");
                loginListerner.onFailture(203, errInfo);
                // 20160708加入Toast显示用于uc平台限制制定用户登录用
                MultiSDKUtils.showTips(context, errInfo);
                sendLogPlatform("登录回调监听：37服务器返回错误信息：" + errInfo);
            }
            // 20160802多平台登录成功弹窗。
            if (!data.isNull("nurl")) {
                String noticeUrl = data.getString("nurl");
//                MultiSDKUtils.showLotteryDialog(context, noticeUrl);
                // MultiSDKUtils.showNoticeDialog(context, "",noticeUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
            BuglessAction.reportCatchException(e, content, BuglessAction.M_TOKEN_VERIFY);
            loginListerner.onFailture(203, "登录失败，请稍后重试");
            sendLogPlatform("登录回调监听：处理37交互得到信息出现异常：" + e.getMessage());
        }
    }

    @Override
    public void changeAccount(Context context, SQResultListener listener) {
        sendLogPlatform("changeAccount");
        // 是否切37
        // ********放到BaseSQwan层去处理。
        // if (isCut2SQ()) {
        // changeAccountSQ(context, listener);
        // return;
        // }

    }

    @Override
    public void setSwitchAccountListener(final SQResultListener switchListener) {
        sendLogPlatform("setSwitchAccountListener");
        switchAccountListener = switchListener;
    }

    @Override
    public void setBackToGameLoginListener(SQResultListener listener) {
        sendLogPlatform("设置回到游戏登录界面监听");
        back2GameListener = listener;
    }

    @Override
    public void pay(final Context context, final String doid, final String dpt,
                    final String dcn, final String dsid, final String dsname,
                    final String dext, final String drid, final String drname,
                    final int drlevel, final float dmoney, final int dradio,
                    final SQResultListener payListener) {
        PayContext payContext = new PayContext("pay");

        PayInfoModel info = new PayInfoModel();
        info.setOrderId(doid);
        info.setProductName(dpt);
        info.setCurrencyName(dcn);
        info.setServerId(dsid);
        info.setServerName(dsname);
        info.setExtend(dext);
        info.setRoleId(drid);
        info.setRoleName(drname);
        info.setRoleLevel(drlevel);
        info.setMoney(dmoney);
        info.setRadio(dradio);

        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.PAY_ORDER, info.getDataMap());

        payContext.setPayInfo(info);
        Bundle extra = new Bundle();
        extra.putString(SqPayManager.EXTRA_PDATA, pdata);
        SqPayManager.getInstance().pay((Activity) context, payContext, info, extra, new PayCallback() {
            @Override
            public void onSuccess(@NonNull PayContext payCtx) {
                Bundle bundle = new Bundle();
                bundle.putString("moid", payCtx.getMoid());
                payListener.onSuccess(bundle);
            }

            @Override
            public void onCancel(@NonNull PayContext payCtx) {
                payListener.onFailture(205, "取消支付【20005】");
            }

            @Override
            public void onFailed(@NonNull PayContext payCtx, @NonNull SqPayError error) {
                payListener.onFailture(error.code, error.msg);
            }
        });
    }

    @Override
    public void logout(Context context, SQResultListener listener) {
        // Platform层用不到，由第三方渠道调用。
    }

    @Override
    public void showExitDailog(Context context, SQResultListener listener) {
        // Platform层用不到，BaseSQwanCore中直接调用了logout
    }

    @Override
    public void setContext(Context cxt) {
        context = cxt;
    }

    // --------------------------------------生命周期---------------------------------------
    @Override
    public void onStart() {
        sendLogPlat4CP("onStart()");
    }

    @Override
    public void onRestart() {
        sendLogPlat4CP("onRestart()");
    }

    @Override
    public void onResume() {
        sendLogPlat4CP("onResume()");
    }

    @Override
    public void onPause() {
        sendLogPlat4CP("onPause()");
    }

    @Override
    public void onStop() {
        sendLogPlat4CP("onStop()");
    }

    @Override
    public void onDestroy() {
        sendLogPlat4CP("onDestroy()");
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        sendLogPlat4CP("onActivityResult()");
        OnActivityResultEvent onActivityResultEvent = new OnActivityResultEvent(requestCode, resultCode, data);
        EventBus.getDefault().post(onActivityResultEvent);
        EventDispatcher.getInstance().dispatcherActivityResultListener(onActivityResultEvent);
    }

    @Override
    public void onNewIntent(Intent intent) {
        sendLogPlat4CP("onNewIntent()");
    }

    // ----------------------------------提交角色相关----------------------------------------------------------

    @Override
    public void creatRoleInfo(HashMap<String, String> infos) {
        sendLogPlat4CP("创建角色信息接口:--->" + infos.toString());
        UserInfoManager.getInstance().setCurrentRoleInfo(RoleInfo.fromRoleMap(infos));
    }

    @Override
    public void upgradeRoleInfo(HashMap<String, String> infos) {
        sendLogPlat4CP("角色升级信息接口:--->" + infos.toString());
        UserInfoManager.getInstance().setCurrentRoleInfo(RoleInfo.fromRoleMap(infos));
    }

    @Override
    public void submitRoleInfo(HashMap<String, String> infos) {
        sendLogPlat4CP("提交角色信息接口:--->" + infos.toString());
        this.userMap = infos;
        UserInfoManager.getInstance().setCurrentRoleInfo(RoleInfo.fromRoleMap(infos));
        sq.submitRoleInfo(infos);
    }

    @Override
    public void creatRole(Context context, String serverId) {
        sendLogPlat4CP("不再使用！调用创建角色信息接口，serverId：" + serverId);
    }

    // ----------------------------------工具类----------------------------------------------------------

    private static void sendLogPlatform(String log) {
        LogUtil.w("Platform-->" + log);
    }

    /**
     * 把Map组装成Json格式
     *
     * @param map
     * @return
     */
    public static String mapToJson(HashMap<String, String> map) {

        if (map == null || map.isEmpty()) {

            return "";
        }

        Set<String> keys = map.keySet();
        Iterator<String> it = keys.iterator();

        String jsonStr = "";

        JSONObject obj = new JSONObject();

        while (it.hasNext()) {

            try {

                String key = it.next();
                String value = map.get(key);
                obj.put(key, value);

            } catch (JSONException e1) {

                e1.printStackTrace();
            }
        }

        jsonStr = obj.toString();

        return jsonStr;

    }

    // ---------------------------------37单平台相关的代码------------------------------------------------

    /**
     * 是否截流为37平台
     *
     * @return
     */
    public boolean isCut2SQ() {
        codeOfLogin = MultiSDKUtils.getCodeOfLogin(context);
        if ("1".equals(codeOfLogin)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 初始化37单平台，
     *
     * @param isLoad 是否显示过场图片
     */
    public static synchronized void initSQ(boolean isLoad) {

        sendLogPlatform("37是否初始化？=" + BaseSQwanCore.isInitSqwan);

        if (!BaseSQwanCore.isInitSqwan) {

            // 初始化
            sq = SQwan.getInstance();
            // 是否显示闪屏
            SQwan.isLoad = isLoad;

            String key = ZipString
                    .zipString2Json(MultiSDKUtils.getKey(context));
            sq.init(context, key, new com.sy37sdk.core.SQResultListener() {

                @Override
                public void onSuccess(Bundle bundle) {
                    // 记得添加init回调
                    if (initListener != null) {
                        initListener.onSuccess(bundle);
                        sendLogPlatform("初始化sq成功");
                        EntranceManager.getInstance().requestEntranceConfig(context);
                    } else {
                        System.err.println("SQ initListener is NULL!");
                    }
                }

                @Override
                public void onFailture(int code, String msg) {
                    // 记得添加init回调
                    if (initListener != null) {
                        initListener.onFailture(code, msg);
                    } else {
                        System.err.println("SQ initListener is NULL!");
                    }
                }
            });
            // 因为可能会出现，初始化还未完成，CP就设置了切换账号监听的情况，所以再设置一次
            if (switchAccountListener != null) {
                setSwitchAccountListenerSQ(switchAccountListener);
            }

            BaseSQwanCore.isInitSqwan = true;
        }
    }

    /**
     * 37单平台登录
     *
     * @param loginListener
     */
    public void loginSQ(final SQResultListener loginListener) {

        upingData25g = false;
        if (sq == null) {
            // 确保SQ不为空
            SQLog.e(TAG + "sq未初始化! 初始化之");
            initSQ(false);
        } else {
            SQLog.d(TAG + "37登录: " + sq.getClass().getSimpleName());
        }
        // 37单平台登录
        sq.login(context, new com.sy37sdk.core.SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                SQLog.i(TAG + "37登录成功, " + bundle);
                // 解析单平台代码
                sqLoginSuccess(context, loginListener, bundle);
            }

            @Override
            public void onFailture(int arg0, String message) {
                SQLog.e(TAG + "37登录失败, code=" + arg0 + ", msg=" + message);
                if (null != loginListener) {
                    loginListener.onFailture(arg0, message);
                }
                // String nurl = Util.getNurl(context);
                // MultiSDKUtils.showNoticeDialog(context, "", nurl);
                // 20161020加入Toast显示
                MultiSDKUtils.showTips(context, message);
            }
        });
    }

    /**
     * 37单平台支付
     *
     * @param context
     * @param doid
     * @param dpt
     * @param dcn
     * @param dsid
     * @param dsname
     * @param dext
     * @param drid
     * @param drname
     * @param drlevel
     * @param dmoney
     * @param dradio
     * @param moid
     * @param data
     * @param payListener
     */
    @Deprecated
    public static void paySQ(Context context, String doid, String dpt, String dcn,
                             String dsid, String dsname, String dext, String drid,
                             String drname, int drlevel, float dmoney, int dradio,
                             final String moid, String data, final SQResultListener payListener) {
        PayContext payContext = new PayContext("static pay");
        PayInfoModel info = new PayInfoModel();
        info.setOrderId(doid);
        info.setProductName(dpt);
        info.setCurrencyName(dcn);
        info.setServerId(dsid);
        info.setServerName(dsname);
        info.setExtend(dext);
        info.setRoleId(drid);
        info.setRoleName(drname);
        info.setRoleLevel(drlevel);
        info.setMoney(dmoney);
        info.setRadio(dradio);

        payContext.setPayInfo(info);
        payContext.setMoid(moid);
        payContext.setRawOrderData(data);

        // 通过静态方法调起支付, 不需要检查实名认证
        sSQPay.pay((Activity) context, payContext, info, null, false, new PayCallback() {
            @Override
            public void onSuccess(@NonNull PayContext payCtx) {
                Bundle bundle = new Bundle();
                bundle.putString("moid", payCtx.getMoid());
                payListener.onSuccess(bundle);
            }

            @Override
            public void onCancel(@NonNull PayContext payCtx) {
                payListener.onFailture(205, "取消支付【20005】");
            }

            @Override
            public void onFailed(@NonNull PayContext payCtx, @NonNull SqPayError error) {
                payListener.onFailture(error.code, error.msg);
            }
        });
    }

    public void changeAccountSQ(final Context context,
                                final SQResultListener listener) {

        if (sq == null) {
            // 确保SQ不为空
            sendLogPlatform("SQ未初始化!初始化之");
            initSQ(false);
        }

        upingData25g = false;

        sq.changeAccount(context, new com.sy37sdk.core.SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                sendLogPlatform("SQ主动切换账号成功");
                sqLoginSuccess(context, listener, bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                sendLogPlatform("SQ主动切换账号失败");
                listener.onFailture(code, msg);
            }

        });
    }

    public static void setSwitchAccountListenerSQ(final SQResultListener switchListener) {
        sendLogPlatform("37 setSwitchAccountListenerSQ 设置监听");
        if (sq != null) {
            if (switchListener != null) {
                sq.setSwitchAccountListener(new com.sy37sdk.core.SQResultListener() {

                    @Override
                    public void onSuccess(Bundle arg0) {
                        sendLogPlatform("SQ悬浮窗切换账号成功");
                        switchListener.onSuccess(arg0);
                    }

                    @Override
                    public void onFailture(int arg0, String arg1) {
                        sendLogPlatform("SQ悬浮窗切换账号失败");
                        switchListener.onFailture(arg0, arg1);
                    }
                });
            } else {
                sendLogPlatform("37切换账号监听为空");
            }
        }
    }

    public void setBackToGameListenerSQ(final SQResultListener back2GameListener) {
        sendLogPlatform("37 setBackToGameListenerSQ 设置监听");
        if (sq != null) {
            if (back2GameListener != null) {
                sq.setBackToGameLoginListener(new com.sy37sdk.core.SQResultListener() {

                    @Override
                    public void onSuccess(Bundle arg0) {
                        sendLogPlatform("sq回到游戏成功");
                        back2GameListener.onSuccess(arg0);
                    }

                    @Override
                    public void onFailture(int arg0, String arg1) {
                        sendLogPlatform("sq回到游戏失败");
                        back2GameListener.onFailture(arg0, arg1);
                    }
                });
            } else {
                sendLogPlatform("37回到游戏监听为空");
            }
        }
    }

    /**
     * 将m层的截图监听设置到s层中
     *
     * @param listener
     */
    public void setScreenshotListenerSQ(final IScreenshotListener listener) {
        sendLogPlatform("37 setScreenshotListenerSQ 设置监听");
        if (sq != null) {
            if (listener != null) {
                sq.setScreenshotListener(new SQScreenshotListener() {
                    @Override
                    public Bitmap createScreenshot() {
                        return listener.createScreenshot();
                    }
                });
            } else {
                sendLogPlatform("37悬浮窗截图监听为空");
            }
        }
    }

    @Override
    public void showSQWebDialog(final String url) {
        MultiSDKUtils.showSQWebDialog(context, transformURLForChannel(url));
    }

    @Override
    public void showSQPersonalDialog(final Context context) {
        LogUtil.w("Platform-->打开实名制弹窗");
        boolean isBand = AuthConfigCache.getIsAuth();
        if (isBand) {
            LogUtil.w("Platform-->已经实名制了");
            ViewUtils.showToast(context, "您已经注册了实名制了");
        } else {
            String url = AuthConfigCache.getPersonalDurl();
            LogUtil.w("Platform--->实名制url:" + url);
            AuthManager.getInstance(context).showAuthDialog(url, false, false, null);
        }
        ;


    }

    /**
     * 特殊渠道特殊业务需要对此URL进行特殊处理，重写此方法即可 例YDSK渠道QQ会员业务 不需要无视即可。
     */
    public String transformURLForChannel(String url) {
        if (MultiSDKUtils.getPID(context).endsWith("1")) {
            // 37平台添加通用参数
            return MultiSDKUtils.constructCommonURL(context, url);
        }
        return url;
    }

    public void logoutSQ(final Context context, final SQResultListener listener) {

        if (sq == null) {
            // 确保SQ不为空
            sendLogPlatform("SQ未初始化!初始化之");
            initSQ(false);
        }

        if (init != null && init.getUseSQExit() == 1 && null != sq) {
            // 调用37广告退出框
            sq.logout(context, new com.sy37sdk.core.SQResultListener() {

                @Override
                public void onSuccess(Bundle bundle) {
                    listener.onSuccess(bundle);
                }

                @Override
                public void onFailture(int code, String msg) {
                    listener.onFailture(code, msg);
                }
            });
        } else {
            // 不调用37广告退出框，原生一个
            new CommonAlertDialog.Builder(context)
                    .setTitle("您确定退出游戏吗？")
                    .setPositiveButton("确定", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            listener.onSuccess(new Bundle());
                        }
                    })
                    .setNegativeButton("取消", null).show();

        }

    }

    private void sqLoginSuccess(Context context,
                                final SQResultListener loginListener, Bundle bundle) {
        final Bundle cbBundle = new Bundle();

        // 所有平台登录后都需要存储一份映射后的用户信息（存储token、userid和username），用于pay的验证
        String userid = bundle.getString(SQwanCore.LOGIN_KEY_USERID);
        String username = bundle.getString(SQwanCore.LOGIN_KEY_USERNAME);
        String token = bundle.getString(SQwanCore.LOGIN_KEY_TOKEN);

        MultiSDKUtils.setUserid(context, userid);
        MultiSDKUtils.setUsername(context, username);
        MultiSDKUtils.setToken(context, token);

        SQAppConfig config = SQwanCore.getInstance().getAppConfig();
        cbBundle.putString(SQwanCore.LOGIN_KEY_TOKEN, token);
        cbBundle.putString(SQwanCore.LOGIN_KEY_GID, config.getGameid());
        cbBundle.putString(SQwanCore.LOGIN_KEY_PID, config.getPartner());

        // 登录成功后将 uid 和用户名返回给研发，这样研发会方便许多
        // 但是需要注意的一个点是，研发不能直接拿这个 uid 和用户名来做重要的事情，只能简单做一下界面展示
        cbBundle.putString(SQwanCore.LOGIN_KEY_USERID, userid);
        cbBundle.putString(SQwanCore.LOGIN_KEY_USERNAME, username);

        MultiSDKUtils.hideActivationCodeDialog();
        loginListener.onSuccess(cbBundle);
    }

    @Override
    public void speechInit(Context context, SQResultListener listener) {

        sendLogPlatform("语音接口实例化");
        speachInitListener = listener;

    }

    @Override
    public void setServerInfo(String url) {
        sendLogPlatform("服务器配置");


    }

    @Override
    public void poll() {
        sendLogPlatform("回调函数驱动");


    }

    @Override
    public void joinNationalRoom(String roomName, int role, int msTimeout) {

        sendLogPlatform("进入国战语音房间");

    }

    @Override
    public void joinTeamRoom(String roomName, int msTimeout) {

        sendLogPlatform("进入小组语音房间");

    }

    @Override
    public void quitRoom(String roomName, int msTimeout) {
        sendLogPlatform("退出语音房间");


    }

    @Override
    public void openMic() {
        sendLogPlatform("打开麦克风");


    }

    @Override
    public void closeMic() {
        sendLogPlatform("关闭麦克风");


    }

    @Override
    public void openSpeaker() {
        sendLogPlatform("打开扬声器");


    }

    @Override
    public void closeSpeaker() {
        sendLogPlatform("关闭扬声器");

    }

    @Override
    public void setMicLevel(int paramInt) {
        sendLogPlatform("设置麦克风音量");

    }

    @Override
    public int getMicLevel() {
        sendLogPlatform("获取麦克风音量");
        return 0;
    }

    @Override
    public void setSpeakerVolume(int paramInt) {
        sendLogPlatform("设置扬声器音量");

    }

    @Override
    public int getSpeakerVolume() {
        sendLogPlatform("获取扬声器音量");
        return 0;
    }

    @Override
    public boolean testMic() {
        sendLogPlatform("设置测试麦克风是否可用");
        return false;
    }

    @Override
    public void enableSpeakerOn(boolean paramBoolean) {
        sendLogPlatform("设置是否开启扬声器");
    }

    @Override
    public void forbidMemberVoice(int paramInt, boolean paramBoolean) {

    }

    @Override
    public void onJoinRoomListener(Context context, SQResultListener listener) {
        sendLogPlatform("设置进入语音房间监听");
        joinToRoomListener = listener;

    }

    @Override
    public void onQuitRoomListener(Context context, SQResultListener listener) {
        sendLogPlatform("设置退出语音房间监听");
        quitToRoomListener = listener;
    }

    @Override
    public void onMemberVoiceListener(Context context, SQResultListener listener) {
        sendLogPlatform("设置成员状态监听");
        memberVoiceListener = listener;

    }

    @Override
    public void onStatusUpdateListener(Context context,
                                       SQResultListener listener) {
        sendLogPlatform("设置掉线监听");
        statusUpdateListener = listener;

    }

    /**
     * 设置截图监听
     *
     * @param mScreenshotListener
     */
    @Override
    public void setScreenshotListener(IScreenshotListener mScreenshotListener) {
        this.mScreenshotListener = mScreenshotListener;
    }

    @Override
    public void performFeatureBBS() {
        sendLogPlatform("应用宝BBS论坛");
    }

    @Override
    public void performFeatureVPlayer() {
        sendLogPlatform("应用宝V+特权");
    }

    @Override
    public void performFeature(Context context, String type, Object data, SQResultListener listener) {
        sendLogPlatform("通用扩展接口" + "  type = " + type + " data = " + data);
        if (TextUtils.isEmpty(type)) {
            sendLogPlatform("type字段不能传空");
            listener.onFailture(203, "type不能传空");
            return;
        }
        if (!ClassCheckUtils.isExistPerformFeatureConfig()) {
            if ("showTransparentWebDialog".equals(type)) {
                sendLogPlatform("接收到显示透明web页面协议");
                String url = (String) data;
                MultiSDKUtils.showSQWebDialog(context, transformURLForChannel(url));
            }
            return;
        }

        switch (type) {
            case PerformFeatureType.TYPE_SHOWTRANSPARENTWEBDIALOG:
                sendLogPlatform("接收到显示透明web页面协议");
                if (data instanceof String) {
                    String url = (String) data;
                    MultiSDKUtils.showSQWebDialog(context, transformURLForChannel(url));
                }
                break;
            case PerformFeatureType.TYPE_AUTHRESULTCHECK:
                setAuthResultListener(listener);
                break;
            case PerformFeatureType.TYPE_AGE_APPROPRIATE_ICON:
                requestAgeAppropriate(listener);
                break;
            case PerformFeatureType.TYPE_SHOW_AGE_APPROPRIATE:
                AppropriateAgeManager.getInstance().showAppropriateAgeDialog(context);
                break;
            case PerformFeatureType.TYPE_SHOW_WEB_DIALOG:
                if (data instanceof String) {
                    WebDialogBean webDialogBean = WebDialogBean.parseToObject((String) data);
                    webDialogBean.setUrl(transformURLForChannel(webDialogBean.getUrl()));
                    MultiSDKUtils.showSQWebDialog(context, webDialogBean);
                }
                break;
            case PerformFeatureType.TYPE_GET_SUPPLIER_ID:
                getSupplierId(listener);
                break;
            case PerformFeatureType.TYPE_REPORT_PURCHASE_DATA:
                if (data instanceof String) {
                    reportPurchaseData((String) data);
                }
                break;
            case PerformFeatureType.TYPE_SCAN_LOGIN:
                doScanLogin();
                break;
            case PerformFeatureType.TYPE_SUPPORT_SCAN_LOGIN:
                if (listener != null) {
                    Bundle bundle = new Bundle();
                    String token = AccountCache.getToken(context);
                    //只有登录和配置打开才可显示
                    boolean canShow = !TextUtils.isEmpty(token) && EntranceManager.getInstance().isSupportScanLogin();
                    bundle.putBoolean("show_scan_login", canShow);
                    listener.onSuccess(bundle);
                }
                break;
            case PerformFeatureType.TYPE_SHOW_GOOD_REVIEW:
                if (listener != null) {
                    showGoodReview(context,listener);
                }
                break;
            case PerformFeatureType.TYPE_POLICY:
                if (listener != null) {
                    handlePolicy(context,data,listener);
                }
                break;
            default:
                listener.onFailture(203, "不支持的type类型");
                break;
        }

    }

    @Override
    public void printLog(int level, String tag, String content) {

    }


    /*
    * 上报购买数据
    * */
    private void reportPurchaseData(String jsonData) {
        try {
            SQReportCore.getInstance().eventCpPay(jsonData);
        } catch (Exception e) {
            e.printStackTrace();
            listener.onFailture(203, "reportPurchaseData处理错误");
        }
    }

    private void getSupplierId(SQResultListener listener) {
        //优先聪缓存中读取
        String mDevIds = MultiSDKUtils.getMDevIds(context);
        if (!TextUtils.isEmpty(mDevIds)) {
            Bundle bundle = new Bundle();
            try {
                JSONObject obj = new JSONObject(mDevIds);
                String oaid = obj.optString("oaid");
                String aaid = obj.optString("aaid");
                String vaid = obj.optString("vaid");
                if (!TextUtils.isEmpty(oaid)) {
                    bundle.putString(PerformFeatureKey.KEY_OAID, oaid);
                    bundle.putString(PerformFeatureKey.KEY_VAID, vaid);
                    bundle.putString(PerformFeatureKey.KEY_AAID, aaid);
                    listener.onSuccess(bundle);
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //若缓存获取为空，则通过api调用获取
        SqOAIDHelper helper = new SqOAIDHelper(new Callback() {
            @Override
            public void onIdsValid(com.sq.oaid.sq_oaid.IdsBean idsBean) {
                if (listener != null && idsBean != null) {
                    Bundle bundle = new Bundle();
                    bundle.putString(PerformFeatureKey.KEY_OAID, idsBean.getOaid());
                    bundle.putString(PerformFeatureKey.KEY_VAID, idsBean.getVaid());
                    bundle.putString(PerformFeatureKey.KEY_AAID, idsBean.getAaid());
                    listener.onSuccess(bundle);
                }
            }
        }, new EventReporter(), new ExceptionReporter());
        helper.getDeviceIds(new PluginContext(context, context.getResources()));
    }

    /**
     * 请求适龄提醒
     *
     * @param listener
     */
    private void requestAgeAppropriate(SQResultListener listener) {
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(context);
        if (appropriateAge == null) {
            //没有缓存则重新发起请求，走请求回调
            AppropriateAgeManager.getInstance().refreshConfig(listener);
            return;
        }
        //有缓存则直接从缓存中读取
        String appropriateIconUrl = "";
        if (appropriateAge.isStatus() && appropriateAge.getTiming() != null && appropriateAge.getTiming().contains(AppropriateAge.TIMING_ROLE)) {
            appropriateIconUrl = AppropriateAgeManager.getInstance().getAppropriateIconUrl();
        }
        Bundle bundle = new Bundle();
        bundle.putString(PerformFeatureKey.KEY_AGE_APPROPRIATE_ICON, appropriateIconUrl);
        LogUtil.i("通过缓存回调适龄提醒url：" + appropriateIconUrl);
        listener.onSuccess(bundle);
    }

    private void doScanLogin() {
        String token = AccountCache.getToken(context);
        if (TextUtils.isEmpty(token)) {
            MultiSDKUtils.showTips(context, "请先完成登录");
            return;
        }
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setClass(context, ScanCodeCameraActivity.class);
        intent.putExtra("screenOrientation", "portrait");
        context.startActivity(intent);
    }

    public void showGoodReview(Context context, SQResultListener listener) {
        LogUtil.i("调用了 Platform.showGoodReview");
        String comment_url = "";
        String jump_url = "";
        String status = "";
        String cp_control = "";
        String commentInfo = ConfigManager.getInstance().getCommentInfo(context);
        ConfigManager.getInstance().setCommentListener(listener);
        try {
            JSONObject data = new JSONObject(commentInfo);
            comment_url = data.optString("picture");
            status = data.optString("status");
            cp_control = data.optString("cp_control");
            jump_url = data.optString("url");
            ConfigManager.getInstance().setJumpUrl(jump_url);
            if (!status.equals("1")){
                listener.onFailture(3,"弹窗配置已关");
                return;
            }
            if(cp_control.equals("1")) {
                ConfigManager.getInstance().jumpComment();
            }else if(cp_control.equals("2")){
                LogUtil.i("调用了 showGoodReview打开弹窗url：" + comment_url);
                MultiSDKUtils.showSQWebDialog(context, transformURLForChannel(comment_url));
            }
        } catch (JSONException e) {
            e.printStackTrace();
            listener.onFailture(3,"打开弹窗异常:" + e.getMessage());
        }
    }

    public void handlePolicy(Context context, Object data, SQResultListener listener) {
        LogUtil.i("调用了 Platform.handlePolicy");
        String type = "";
        if(data instanceof String){
            LogUtil.i("handlePolicy传入参数为String类型");
            try {
                JSONObject json = new JSONObject((String) data);
                type = json.getString("type");
                LogUtil.i("handlePolicy type为："+type);
            } catch (JSONException e) {
                e.printStackTrace();
                ConfigManager.getInstance().handlePolicyFailure(101,"传入data参数类型有误，解析String类型data异常",SqTrackAction2.POLICY_ERROR,listener);
                return;
            }
        }else if (data instanceof JSONObject){
            LogUtil.i("handlePolicy传入参数为JSONObject类型");
            try {
                type = ((JSONObject) data).getString("type");
                LogUtil.i("handlePolicy type为："+type);
            } catch (JSONException e) {
                e.printStackTrace();
                ConfigManager.getInstance().handlePolicyFailure(102,"传入data参数类型有误，解析json类型data异常",SqTrackAction2.POLICY_ERROR,listener);
                return;
            }
        }else {
            ConfigManager.getInstance().handlePolicyFailure(103,"传入data参数类型有误，要求String或者json类型",SqTrackAction2.POLICY_ERROR,listener);
            return;
        }
        if(type.isEmpty()){
            ConfigManager.getInstance().handlePolicyFailure(104,"data获取异常,type为空",SqTrackAction2.POLICY_ERROR,listener);
            return;
        }

        String policyInfo = ConfigManager.getInstance().getPolicyInfo(context);
        ConfigManager.getInstance().setPolicyListener(listener);
        String share_url = "";
        String personal_url = "";
        String record_no = "";
        String record_url = "";
        try {
            JSONObject json = new JSONObject(policyInfo);
            share_url = json.optString("share_url");
            personal_url = json.optString("personal_url");
            record_no = json.optString("record_no");
            record_url = json.optString("record_url");
        } catch (Exception e) {
            e.printStackTrace();
            ConfigManager.getInstance().handlePolicyFailure(105,"获取配置异常:" + e.getMessage(),SqTrackAction2.POLICY_ERROR,listener);
            return;
        }

        String forceOrientation = "?forceOrientation=1";
        switch (type){
            case "show_third_list":
                try {
                    if(!share_url.isEmpty()){
                        showSQWebDialog(share_url + forceOrientation);
                        ConfigManager.getInstance().handlePolicySuccess(null,SqTrackAction2.POLICY_SHARE_LIST_SUCCESS,listener);
                    }else {
                        ConfigManager.getInstance().handlePolicyFailure(106,"个人信息第三方共享清单配置为空，请检查后台配置",SqTrackAction2.POLICY_SHARE_LIST_ERROR,listener);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    ConfigManager.getInstance().handlePolicyFailure(112,"个人信息第三方共享清单打开异常："+e.getMessage(),SqTrackAction2.POLICY_SHARE_LIST_ERROR,listener);
                }
                break;
            case "show_personal_list":
                try {
                    if(!personal_url.isEmpty()){
                        showSQWebDialog(personal_url + forceOrientation);
                        ConfigManager.getInstance().handlePolicySuccess(null,SqTrackAction2.POLICY_PERSONAL_LIST_SUCCESS,listener);
                    }else {
                        ConfigManager.getInstance().handlePolicyFailure(107,"个人信息收集与使用清单配置为空，请检查后台配置",SqTrackAction2.POLICY_PERSONAL_LIST_ERROR,listener);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    ConfigManager.getInstance().handlePolicyFailure(113,"个人信息收集与使用清单打开异常："+e.getMessage(),SqTrackAction2.POLICY_PERSONAL_LIST_ERROR,listener);
                }
                break;
            case "get_record_number":
                if(!personal_url.isEmpty()){
                    Bundle bundle = new Bundle();
                    bundle.putString("record_no", record_no);
                    ConfigManager.getInstance().handlePolicySuccess(bundle,SqTrackAction2.POLICY_RECORD_NUMBER_SUCCESS,listener);
                }else {
                    ConfigManager.getInstance().handlePolicyFailure(108,"备案号配置为空，请检查后台配置",SqTrackAction2.POLICY_RECORD_NUMBER_ERROR,listener);
                }
                break;
            case "show_miit":
                if(!record_url.isEmpty()){
                    try {
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.setData(Uri.parse(record_url));
                        context.startActivity(intent);
                        ConfigManager.getInstance().handlePolicySuccess(null,SqTrackAction2.POLICY_SHOW_MIIT_SUCCESS,listener);
                    } catch (Exception e) {
                        e.printStackTrace();
                        ConfigManager.getInstance().handlePolicyFailure(110,"跳转工信部网页异常",SqTrackAction2.POLICY_SHOW_MIIT_ERROR,listener);
                    }
                }else {
                    ConfigManager.getInstance().handlePolicyFailure(109,"工信部网址配置为空，请检查后台配置",SqTrackAction2.POLICY_SHOW_MIIT_ERROR,listener);
                }
                break;
            default:
                ConfigManager.getInstance().handlePolicyFailure(111,"type类型错误，请查看传入参数",SqTrackAction2.POLICY_ERROR,listener);
        }
    }

    private void setAuthResultListener(SQResultListener listener) {
        setAuthResultListenerSQ(listener);
    }

    private void setAuthResultListenerSQ(final SQResultListener listener) {
        if (sq != null) {
            sq.setAuthResultListener(new com.sy37sdk.core.SQResultListener() {
                @Override
                public void onSuccess(Bundle bundle) {
                    LogUtil.i("回调给研发setAuthResultListener onSuccess");
                    if (listener != null) {
                        listener.onSuccess(bundle);
                    }
                }

                @Override
                public void onFailture(int code, String msg) {
                    LogUtil.i("回调给研发setAuthResultListener onFailture");
                    if (listener != null) {
                        listener.onFailture(code, msg);
                    }
                }
            });
        }
    }

    private static String debug4cp = null;
    /**
     * 替换BaseSQWanCore中的同名方法, 打印日志不要依赖BaseSQWanCore的静态方法, 降低关系度
     */
    private static void sendLogPlat4CP(String log) {
        if (debug4cp == null) {
            Properties sdkInfo = MultiSDKUtils.readPropertites(context, MultiSdkManager.getInstance().getInfo());
            if (sdkInfo != null) {
                debug4cp = sdkInfo.getProperty("debug") == null ? "0" : sdkInfo.getProperty("debug");
            }
        }
        if (!TextUtils.isEmpty(debug4cp) && "1".equals(debug4cp)) {
            System.out.println("-->" + log);
        }
        LogUtil.w("Platform-->" + log);
    }
}
