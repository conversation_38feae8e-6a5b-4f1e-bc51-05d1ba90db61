package com.sqwan.msdk.api;

import android.app.Application;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.sq.diagnostic.assistant.other.IBusinessParameters;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.NetWorkUtils;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.SQwanCore;

/**
 *    author : 黄锦群
 *    time   : 2022/10/10
 *    desc   : SDK 诊断助手参数封装类
 */
public class BusinessParameters implements IBusinessParameters {

   private final Context mContext;

   public BusinessParameters(Context context) {
      if (context instanceof Application) {
         mContext = context;
         return;
      }
      Context applicationContext = context.getApplicationContext();
      if (applicationContext != null) {
         mContext = applicationContext;
      } else {
         mContext = context;
      }
   }

   @Override
   public String getGid() {
      SQAppConfig appConfig = SQwanCore.getInstance().getAppConfig();
      if (appConfig != null) {
         return appConfig.getGameid();
      }
      return "";
   }

   @Override
   public String getPid() {
      SQAppConfig appConfig = SQwanCore.getInstance().getAppConfig();
      if (appConfig != null) {
         return appConfig.getPartner();
      }
      return "";
   }

   @Override
   public String getReferer() {
      SQAppConfig appConfig = SQwanCore.getInstance().getAppConfig();
      if (appConfig != null) {
         return appConfig.getRefer();
      }
      return "";
   }

   @Override
   public String getDeviceId() {
      return DeviceUtils.getDev(mContext);
   }

   @Override
   public String getUserId() {
      return SqTrackUtil.getUserid(mContext);
   }

   @Override
   public String getUserName() {
      return SqTrackUtil.getUsername(mContext);
   }

   @Override
   public String getGwVersion() {
      return VersionUtil.gwversion;
   }

   @Override
   public String getSdkVersion() {
      return VersionUtil.getSdkVersion();
   }

   @Override
   public String getGameName() {
      try {
         PackageManager packageManager = mContext.getPackageManager();
         ApplicationInfo applicationInfo = packageManager.getApplicationInfo(getPackageName(), 0);
         return (String) packageManager.getApplicationLabel(applicationInfo);
      } catch (PackageManager.NameNotFoundException e) {
         e.printStackTrace();
      }
      return "";
   }

   @Override
   public String getPackageName() {
      return mContext.getPackageName();
   }

   @Override
   public String getVersionName() {
      try {
         PackageManager packageManager = mContext.getPackageManager();
         PackageInfo packageInfo = packageManager.getPackageInfo(mContext.getPackageName(), 0);
         return packageInfo.versionName;
      } catch (PackageManager.NameNotFoundException e) {
         e.printStackTrace();
      }
      return "";
   }

   @Override
   public String getVersionCode() {
      try {
         PackageManager packageManager = mContext.getPackageManager();
         PackageInfo packageInfo = packageManager.getPackageInfo(mContext.getPackageName(), 0);
         return String.valueOf(packageInfo.versionCode);
      } catch (PackageManager.NameNotFoundException e) {
         e.printStackTrace();
      }
      return "";
   }

   @Override
   public String getCountry() {
      return DeviceUtils.getCountry();
   }

   @Override
   public String getProvince() {
      return "";
   }

   @Override
   public String getCity() {
      return "";
   }

   @Override
   public String getNetworkType() {
      return NetWorkUtils.getNetworkType(mContext);
   }

   @Override
   public String getIpAddress() {
      return NetWorkUtils.getIpAddress(mContext);
   }

   @Override
   public String getCarrier() {
      return DeviceUtils.getCarrier(mContext);
   }

   @Override
   public String getServerId() {
      return MultiSDKUtils.getServerid(mContext);
   }

   @Override
   public String getServerName() {
      return MultiSDKUtils.getServerName(mContext);
   }

   @Override
   public String getRoleId() {
      return MultiSDKUtils.getRoleid(mContext);
   }

   @Override
   public String getRoleName() {
      return MultiSDKUtils.getRolename(mContext);
   }
}