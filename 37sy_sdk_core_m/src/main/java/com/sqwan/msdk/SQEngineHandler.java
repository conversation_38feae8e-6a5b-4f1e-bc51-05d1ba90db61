package com.sqwan.msdk;

import android.content.Context;
import com.sq.data.BuildConfig;
import com.sqwan.common.mod.ModManager;
import com.sqwan.common.mod.ModManager.ModConfig;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.advertise.IAdvertiseMod;
import com.sqwan.common.mod.comment.ICommentMod;
import com.sqwan.common.mod.config.IConfigMod;
import com.sqwan.common.mod.liveshow.IAudioLiveshowManager;
import com.sqwan.common.mod.liveshow.IAudioLiveshowTrackManager;
import com.sqwan.common.mod.liveshow.IHyLiveshowManager;
import com.sqwan.common.mod.liveshow.IHyLiveshowTrackManager;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.mod.order.IOrderMod;
import com.sqwan.common.mod.plugin.IPluginMod;
import com.sqwan.common.mod.push.IPushMod;
import com.sqwan.common.mod.share.IShareMod;
import com.sqwan.common.mod.track.ITrackMod;
import com.sqwan.common.mod.track.ITrackMod2;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.engine.CoreEngineHandler;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;
import com.sy37sdk.account.policy.AuthHandler;
import com.sy37sdk.account.uagree.UAgreeManager;
import java.util.Properties;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-16 10:19
 */
public class SQEngineHandler {

    public static final String CONFIG_FILE = "multiconfig";

    private static final SQEngineHandler ourInstance = new SQEngineHandler();

    public static SQEngineHandler getInstance() {
        return ourInstance;
    }

    private SQEngineHandler() {
    }

    public void init(Context context) {
        initMod(context);
        LiveshowEngine.getInstance().init(context);
        LiveRadioEngine.getInstance().init(context);
        UAgreeManager.getInstance().init(context);
        AuthHandler.getInstance().init(context);
        PermissionHelper.getInstance().init(context);
        CoreEngineHandler.getInstance().init(context);
        initConfigsBean(context);
        ActiveBeforeManager.getInstance().init(context);
    }

    private void initMod(Context context) {
        ModManager manager = ModManager.getInstance();
        manager.clearMod();
        manager.putMod(IOrderMod.class, new ModConfig("com.sy37sdk.order.OrderModImpl", true));
        manager.putMod(IAccountMod.class, new ModConfig("com.sy37sdk.account.AccountModImpl", true));
        manager.putMod(IPluginMod.class, new ModConfig("com.sy37sdk.plugin.PluginModImpl", true));
        manager.putMod(IShareMod.class, new ModConfig("com.sy37sdk.share.ShareModImpl", true));

        if (BuildConfig.isTrack) {
            manager.putMod(ITrackMod.class, new ModConfig("com.sq.track.TrackModImpl", true));
            manager.putMod(ITrackMod2.class, new ModConfig("com.sq.track.TrackModImpl2", true));
        }

        if (isLiveshowTypeAudio()) {
            manager.putMod(IAudioLiveshowManager.class, new ModConfig("com.sqwan.liveshow.LiveshowManager"));
            manager.putMod(IAudioLiveshowTrackManager.class,
                new ModConfig("com.sqwan.liveshow.trackaction.LiveshowTrackManager"));
        }

        if (isLiveshowTypeHy()) {
            manager.putMod(IHyLiveshowManager.class, new ModConfig("com.sqwan.liveshow.huya.engine.LiveshowManager"));
            manager.putMod(IHyLiveshowTrackManager.class,
                new ModConfig("com.sqwan.liveshow.huya.trackaction.LiveshowTrackManager"));
        }
        manager.putMod(IConfigMod.class, new ModConfig("com.sqwan.common.mod.config.ConfigModImpl"));
        manager.putMod(IPushMod.class, new ModConfig("com.sqwan.push.PushModImpl"));
        manager.putMod(IAdvertiseMod.class, new ModConfig("com.sy37sdk.advertise.AdvertiseImpl"));
        manager.putMod(ICommentMod.class, new ModConfig("com.sy37sdk.account.config.CommentModImpl"));
        manager.loadMod(context);
    }

    private void initConfigsBean(Context context) {
        if (ConfigsBean.configsBean == null) {
            Properties prop = MultiSDKUtils.readPropertites(context, CONFIG_FILE);
            ConfigsBean.configsBean = ConfigsBean.init(prop);
        }
    }

    private boolean isLiveshowTypeHy() {
        return BuildConfig.isHyLiveShow;

    }

    private boolean isLiveshowTypeAudio() {
        return BuildConfig.isYimAudio;
    }
}
