package com.sqwan.msdk.views;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.WindowManager;
import android.widget.TextView;

/**
 * <AUTHOR>
 */
public class CustomTextView extends TextView {
    public final static String TAG = CustomTextView.class.getSimpleName();

    private float textLength = 0f;
    private float viewWidth = 0f;
    private float y = 0f;
    private Paint paint = null;
    private String text = "";
    private float textSpace = 1200f;
    private float positionText1 = 0f;
    private float positionText2 = 0f;
    private float speed = 3;

    public CustomTextView(Context context) {
        super(context);
    }

    public CustomTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void setText(String text) {
        super.setText(text);
        this.text = text;
        init();
    }

    /** *//**
     * 文本初始化，每次更改文本内容或者文本效果等之后都需要重新初始化一下
     */
    public void init()
    {
        paint = getPaint();
        textLength = paint.measureText(text);
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        int screenWidth = windowManager.getDefaultDisplay().getWidth();

        if (getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            screenWidth = windowManager.getDefaultDisplay().getHeight();
            textSpace = screenWidth - textLength - 240;
        } else {
            textSpace = screenWidth - textLength - 200;
        }

        if (textSpace < 0) {
            textSpace = 200;
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        viewWidth = getWidth();
        if(viewWidth == 0)
        {
            viewWidth = textLength / 2;
        }
        positionText1 = viewWidth;
        positionText2 = viewWidth + textLength + textSpace;
        y = getBaseline(paint);

    }

    private static float getBaseline(Paint p) {
        Paint.FontMetrics fontMetrics = p.getFontMetrics();
        return (fontMetrics.descent - fontMetrics.ascent) / 2 -fontMetrics.descent;
    }

    @Override
    public void onDraw(Canvas canvas) {

        canvas.drawText(text, positionText1, getHeight() - y, paint);
        canvas.drawText(text, positionText2, getHeight() - y, paint);

        positionText1 -= speed;
        positionText2 -= speed;

        if (positionText1 < -textLength) {
            positionText1 = positionText2 + textSpace + textLength;
        }

        if (positionText2 < -textLength) {
            positionText2 = positionText1 + textSpace + textLength;
        }
        invalidate();
    }

}

