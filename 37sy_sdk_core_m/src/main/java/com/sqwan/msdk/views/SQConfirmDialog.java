package com.sqwan.msdk.views;


import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.sqwan.common.dialog.FullScreenDialog;
import com.sy37sdk.utils.Util;

public class SQConfirmDialog extends FullScreenDialog {

	private Context context;

	private Button cancel,confirm;//开始和停止下载，隐藏下载按钮
	private TextView msg_tv;//确认框的显示信息

	private ConfirmListener listener;//按键监听

	private String text;//确定框的内容

	public SQConfirmDialog(Context context) {
		super(context);
	}

	public SQConfirmDialog(Context context, String msg){
		super(context);
		this.context = context;
		this.text =  msg;
	}

	/**
	 * @param context
	 * @param theme
	 * @param msg 确定框内容
	 */
	public SQConfirmDialog(Context context,int theme,String msg) {
		super(context,theme);
		this.context = context;
		this.text =  msg;
	}

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		getContext().setTheme(Util.getIdByName("Mdialog", "style", context.getPackageName(), context));
		setContentView(Util.getIdByName("sy37_confirm_dialog", "layout", context.getPackageName(), context));
		//顶栏按钮
		msg_tv = (TextView) findViewById(Util.getIdByName("message", "id", context.getPackageName(), context));
		cancel = (Button) findViewById(Util.getIdByName("cancel", "id", context.getPackageName(), context));
		confirm = (Button) findViewById(Util.getIdByName("confirm", "id", context.getPackageName(), context));

		//赋值
		msg_tv.setText(text);
		//按钮确认
		cancel.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				if (listener!=null) {
					listener.onCancel();
				}
			}
		});

		confirm.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				if (listener!=null) {
					listener.onConfirm();
				}
			}
		});

		//设置对话框属性
		setCanceledOnTouchOutside(false);

	}


	/**确认框按钮监听器
	 * <AUTHOR>
	 *
	 */
	public interface ConfirmListener{
		void onCancel();
		void onConfirm();
	}

	public void setConfirmListenr(ConfirmListener listenr){
		if (listenr!=null) {
			this.listener  = listenr;
		}
	}

}
