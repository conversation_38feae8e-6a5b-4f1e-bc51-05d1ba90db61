package com.sqwan.msdk.views;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.MRequestCallBack;
import com.sqwan.msdk.api.MRequestManager;
import com.sqwan.msdk.utils.ViewUtils;
import com.sy37sdk.utils.AppUtils;
import com.sy37sdk.utils.Util;
import org.json.JSONObject;

public class SQActivationCodeDialog extends Dialog {

    private Context context;

    private String mBeta = "";
    private CharSequence mTitle = "";
    private String mDesc = "";
    private String mUrl = "";
    private CheckActivationCodeCallback mCallback;
    private EditText activationCodeEdit;
    private Button getActivationCodeBtn;
    private Button checkActivationCodeBtn;
    private ImageView cleanBtn;

    public SQActivationCodeDialog(Context context) {
        this(context, Util.getIdByName("Dialog", "style", context.getPackageName(), context));
        this.context = context;
    }

    SQActivationCodeDialog(Context context, int theme) {
        super(context, theme);
        this.context = context;
    }

    SQActivationCodeDialog(Context context, boolean cancelable,
                           OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Window dialogWindow = getWindow();
        WindowManager.LayoutParams p = getWindow().getAttributes(); // 获取对话框当前的参数值
        dialogWindow.setAttributes(p);
        super.onCreate(savedInstanceState);

        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(Util.getIdByName("sy37_m_activation_code_dialog", "layout", context.getPackageName(),context), null);

        activationCodeEdit = (EditText) view.findViewById(Util.getIdByName("sy37_m_active_edit_accode", "id", context.getPackageName(),context));
        cleanBtn = (ImageView) view.findViewById(Util.getIdByName("sy37_m_active_img_clean", "id", context.getPackageName(),context));
        getActivationCodeBtn = (Button) view.findViewById(Util.getIdByName("sy37_m_active_btn_get_code", "id", context.getPackageName(),context));
        checkActivationCodeBtn = (Button) view.findViewById(Util.getIdByName("sy37_m_active_btn_confirm", "id", context.getPackageName(),context));

        cleanBtn.setFocusable(true);

        if (!TextUtils.isEmpty(mTitle)) {

            TextView textView_title = (TextView) view.findViewById(Util.getIdByName("sy37_m_active_text_accode_title", "id", context.getPackageName(),context));
            textView_title.setText(mTitle);
        }

        if (!TextUtils.isEmpty(mDesc)) {

            TextView textView_desc = (TextView) view.findViewById(Util.getIdByName("sy37_m_active_text_accode_desc", "id", context.getPackageName(),context));
            textView_desc.setText(mDesc);
        }

        getActivationCodeBtn.setVisibility((TextUtils.isEmpty(mUrl) ? View.GONE : View.VISIBLE));


        //设置激活码输入框内容监听器
        activationCodeEdit.addTextChangedListener(new TextWatcher() {

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void afterTextChanged(Editable s) {

                cleanBtn.setVisibility((TextUtils.isEmpty(s.toString()))? View.GONE:View.VISIBLE);
            }
        });


        //清理激活码内容
        cleanBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                activationCodeEdit.setText("");
            }
        });

        //验证激活码
        checkActivationCodeBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                String betaCode = activationCodeEdit.getText().toString();

                if (TextUtils.isEmpty(betaCode)) {

                    ViewUtils.showToast(context, "请输入正确的激活码");
                    return;
                }

                MRequestManager manager = new MRequestManager(context);

                manager.checkActivationCodeRequest(betaCode,new MRequestCallBack() {

                    @Override
                    public void onRequestSuccess(String content) {

                        try {

                            JSONObject jsonObj = new JSONObject(content);
                            int state = jsonObj.getInt("state");
                            String msg = jsonObj.getString("msg");

                            if (state == 1) { //激活码验证通过

                                activationCodeEdit.setText("");
                                SQActivationCodeDialog.this.dismiss();
                                mCallback.onActiveSucecess();

                            } else {  //验证失败

                                ViewUtils.showToast(context, msg);

                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                            ViewUtils.showToast(context, "激活码验证失败，请检查激活码是否正确, 0xe");
                        }
                    }

                    @Override
                    public void onRequestError(String errorMsg) {

                        ViewUtils.showToast(context, errorMsg);
                    }
                });

            }
        });

        //获取激活码
        getActivationCodeBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                AppUtils.toSdkUrl(context, mUrl);

            }
        });

        setContentView(view, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }

    public static SQActivationCodeDialog show(Context context, CharSequence msg) {
        return show(context, msg, false);
    }

    public static SQActivationCodeDialog show(Context context, int id) {
        return show(context, context.getResources().getString(id), false);
    }

    public static SQActivationCodeDialog show(Context context, CharSequence title, boolean cancelable) {
        SQActivationCodeDialog dialog = new SQActivationCodeDialog(context);
        dialog.setTitle(title);
        dialog.setCancelable(cancelable);
        dialog.show();
        return dialog;
    }

    public void setTitle(CharSequence title) {
        mTitle = title;
    }


    public void setDesc(String desc) {
        mDesc = desc;
    }

    public void setUrl(String url) {
        mUrl = url;
    }

    public void setCallback(CheckActivationCodeCallback callback){

        mCallback = callback;

    }

    public void setBetaData(String beta) {

        mBeta = beta;
        LogUtil.w("SQActivationCodeDialog mBeta:"+mBeta);

        BetaInfo betaInfo = new BetaInfo();
        JSONObject jsonObject = new JSONObject();
        betaInfo.setDesc(jsonObject.optString("desc"));
        betaInfo.setTitle(jsonObject.optString("title"));
        betaInfo.setUrl(jsonObject.optString("url"));

        mTitle = betaInfo.getTitle();
        mDesc = betaInfo.getDesc();
        mUrl = betaInfo.getUrl();

        LogUtil.w("SQActivationCodeDialog mTitle:"+mTitle+" mDesc:"+mDesc+" mUrl:"+mUrl);

    }


    public interface CheckActivationCodeCallback{

        public void onActiveSucecess();

    }



    class BetaInfo {

        private String title;
        private String desc;
        private String url;
        public String getTitle() {
            return title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
        public String getDesc() {
            return desc;
        }
        public void setDesc(String desc) {
            this.desc = desc;
        }
        public String getUrl() {
            return url;
        }
        public void setUrl(String url) {
            this.url = url;
        }


    }

}
