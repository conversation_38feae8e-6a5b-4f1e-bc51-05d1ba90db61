package com.sqwan.msdk.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import com.sqwan.afinal.download.DownloadListener;
import com.sqwan.afinal.download.DownloadTask;
import com.sqwan.common.dialog.FullScreenDialog;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.SQUpdateManager;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.utils.Util;
import java.io.File;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class SQUpdateDialog extends FullScreenDialog {

    private Context context;
    private TextView update_notice;//更新通知
    private Button start, hide;//开始和停止下载，隐藏下载按钮
    private View progress_view;//隐藏按钮（可控制隐藏）
    private ImageView ivClose;

    private ProgressBar update_progress;//进度条
    private TextView update_size;//更新进度


    private String url, notice, version;//更新地址、提示和版本号
    private boolean isForceUpdate;//flag值识别：1=普通更新和2=强制更新。
    private boolean switch_on = false;//暂停结束标识

    private DownloadTask downloadTask;

    public SQUpdateDialog(Context context) {
        super(context);
    }

    /**
     * 加载更新进度框
     *
     * @param context       上下文
     * @param isForceUpdate 是否强制更新
     * @param notice        更新说明
     * @param url           更新地址
     */
    public SQUpdateDialog(Context context, boolean isForceUpdate, String notice, String url, String version) {
        super(context);
        this.context = context;
        this.isForceUpdate = isForceUpdate;
        this.notice = notice;
        this.url = url;
        this.version = version;
    }


    public SQUpdateDialog(Context context, int theme, boolean isForceUpdate, String notice, String url, String version) {
        super(context, theme);
        this.context = context;
        this.isForceUpdate = isForceUpdate;
        this.notice = notice;
        this.url = url;
        this.version = version;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getContext().setTheme(Util.getIdByName("Mdialog", "style", context.getPackageName(), context));
        setContentView(Util.getIdByName("sy37_update_layout", "layout", context.getPackageName(), context));
        update_notice = (TextView) findViewById(Util.getIdByName("update_notice", "id", context.getPackageName(), context));
        update_size = (TextView) findViewById(Util.getIdByName("update_size", "id", context.getPackageName(), context));
        start = (Button) findViewById(Util.getIdByName("stop_start_btn", "id", context.getPackageName(), context));
        hide = (Button) findViewById(Util.getIdByName("hide_btn", "id", context.getPackageName(), context));
        progress_view = findViewById(Util.getIdByName("progress_view", "id", context.getPackageName(), context));
        update_progress = (ProgressBar) findViewById(Util.getIdByName("progressbar", "id", context.getPackageName(), context));
        ivClose = findViewById(Util.getIdByName("iv_close", "id", context.getPackageName(), context));

        start.setText(isForceUpdate ? "开始下载" : "开始");

        //View的监听和赋值
        update_notice.setText(notice.replace("\\n", "\n").replace("\\space", " "));
        start.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                if (!Util.isNetworkConnected(getContext())) {
                    SQUpdateManager.showTips(context, "没有网络连接，请检查网络设置后重试");
                    return;
                }
                if (switch_on) {
                    //暂停
                    switch_on = false;
                    start.setText(isForceUpdate ? "继续下载" : "继续");
                    stopDownload();
                } else {
                    if (isForceUpdate) {
                        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.updateForceStart, SqTrackBtn.SqTrackBtnExt.updateForceStart);
                    } else {
                        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.updateStart, SqTrackBtn.SqTrackBtnExt.updateStart);
                    }
                    //开始
                    switch_on = true;
                    start.setText(isForceUpdate ? "暂停下载" : "暂停");
                    startDownload(url);
                    //显示进度条
                    if (progress_view.getVisibility() == View.INVISIBLE || progress_view.getVisibility() == View.GONE) {
                        progress_view.setVisibility(View.VISIBLE);
                    }
                }
            }
        });

        //隐藏按钮消失
        hide.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.updateCancel, SqTrackBtn.SqTrackBtnExt.updateCancel);
                //隐藏更新框，这是普通更新
                dismiss();
            }
        });
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.updateClose, SqTrackBtn.SqTrackBtnExt.updateClose);
                dismiss();
            }
        });

        //设置对话框属性
        setCanceledOnTouchOutside(false);
        //强制更新
        if (isForceUpdate) {
            setCancelable(false);
            ivClose.setVisibility(View.GONE);
            hide.setVisibility(View.GONE);
        }

        if (isForceUpdate) {
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.updateForce, SqTrackPage.SqTrackViewName.updateForce);
            HashMap<String,String> trackMap=new HashMap<>();
            trackMap.put(SqTrackKey.update_uurl,url);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.forced_update,trackMap);
        } else {
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.update, SqTrackPage.SqTrackViewName.update);
            HashMap<String,String> trackMap=new HashMap<>();
            trackMap.put(SqTrackKey.update_uurl,url);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.general_update,trackMap);
        }

    }


    /**
     * 下载
     *
     * @param downUrl
     */
    public void startDownload(String downUrl) {

        String targetPath = SQUpdateManager.getSDPath(context);

        if (targetPath != null && !"".endsWith(targetPath)) {
            //存在
            LogUtil.i("开始下载:" + downUrl);
            final String downName = SQUpdateManager.getFileNameOfUrl(context, downUrl, version);
            final String target = targetPath + downName;
            LogUtil.i("下载目录:" + target);
            downloadTask = new DownloadTask(downUrl, downName, targetPath, new DownloadListener() {

                private long lastCountMb;
                private int lastProgress;

                @SuppressLint("SetTextI18n")
                @Override
                public void onUpdate(long count, long current) {
                    if (count != 0) {
                        long delta = count / 100;
                        int progress = (int) (current / delta);
                        long currentMb = current / 1024L / 1024L;
                        if (current <= count && (currentMb > lastCountMb || progress > lastProgress)) {
                            LogUtil.i("下载进度：" + current + "/" + count
                                + ", " + currentMb + "MB, " + progress + "%");
                            update_size.setText(convert2MB(current) + "/" + convert2MB(count));
                            update_progress.setProgress(progress);
                        }
                        lastCountMb = currentMb;
                        lastProgress = progress;
                    }
                }

                @Override
                public void onSuccess(File file) {
                    //下载完成，弹出安装界面
                    if (file != null) {
                        HashMap<String,String> trackMap=new HashMap<>();
                        trackMap.put(SqTrackKey.update_uurl,url);
                        if(isForceUpdate){
                            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.forced_update_success,trackMap);
                        }else{
                            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.general_update_success,trackMap);
                        }
                        SQUpdateManager.showTips(context, "下载完成：" + file.getAbsoluteFile().toString());
                        LogUtil.i("apk file path is " + file.getAbsoluteFile().toString());
                        SQUpdateManager.saveFileLength(context, downName, file.length());
                        SQUpdateManager.checkAndInstall(isForceUpdate, context, file);
                        dismiss();
                    } else {
                        SQUpdateManager.showTips(context, "下载失败:建议在WIFI下重新启动游戏~");
                    }
                }

                @Override
                public void onFailure(Throwable t, int errorNo, String strMsg) {
                    HashMap<String,String> trackMap=new HashMap<>();
                    trackMap.put(SqTrackKey.update_uurl,url);
                    trackMap.put(SqTrackKey.fail_code,errorNo+"");
                    trackMap.put(SqTrackKey.reason_fail,strMsg+"");
                    if(isForceUpdate){
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.forced_update_fail,trackMap);
                    }else{
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.general_update_fail,trackMap);
                    }
                    if (t != null) {
                        t.printStackTrace();
                    }
                    LogUtil.d("errorNo: " + errorNo + ", strMsg: " + strMsg);
                    switch_on = false;
                    start.setText("开始下载");
                    stopDownload();
                    if (errorNo == DownloadTask.CODE_URL_NULL) {
                        SQUpdateManager.showTips(context, "下载失败:无效的下载链接~");
                    } else if (errorNo == DownloadTask.CODE_APK_NOT_EXIST) {
                        LogUtil.i("下载结束， apk文件没找到");
                    } else if (errorNo == DownloadTask.CODE_DID_NOT_FINISH) {
                        LogUtil.i("下载未完成");
                    } else if (errorNo == DownloadTask.CODE_EXCEPTION) {
                        LogUtil.i("下载出错");
                    } else if (errorNo == DownloadTask.CODE_RANGE_NOT_SATIS) {
                        LogUtil.i("返回 416 --> " + target);
                        File file = new File(target);
                        if (file.exists()) {
                            SQUpdateManager.checkAndInstall(isForceUpdate, context, file);
                            dismiss();
                        } else {
                            LogUtil.i("apk file is not exists");
                        }
                    } else {
                        SQUpdateManager.showTips(context, "下载失败:无效的下载链接~");
                    }
                }
            });
            downloadTask.execute();

        } else {
            SQUpdateManager.showTips(context, "下载失败:请您检查设备存储盘情况。");
        }

    }


    private void stopDownload() {
        System.out.println("请求暂停");
        if (downloadTask != null && downloadTask.getStatus() == AsyncTask.Status.RUNNING) {
            downloadTask.cancel(true);
        }
    }


    private String convert2MB(long size) {
        int result = (int) (size / 1024 / 1024);
        return result + "MB";
    }

    @Override
    public void dismiss() {
        stopDownload();
        super.dismiss();
    }
}
