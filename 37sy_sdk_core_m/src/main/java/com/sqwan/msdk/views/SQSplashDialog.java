package com.sqwan.msdk.views;


import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.utils.BitmapUtils;
import com.sqwan.msdk.utils.ViewUtils;
import com.sy37sdk.utils.Util;

import java.lang.reflect.Method;


public class SQSplashDialog extends Dialog {

	private Context mcontext;
	private int splashTime =3;
	private SplashListener splashCallback;
	private Bitmap bitmap;

	public SQSplashDialog(Context context, int time) {
		super(context, Util.getIdByName("Mdialog","style", context.getPackageName(), context));
		this.mcontext = context;
		this.splashTime = time;
	}

	public SQSplashDialog(Context context) {
		super(context, Util.getIdByName("Mdialog","style", context.getPackageName(), context));
		this.mcontext = context;
	}

	public void setSplashTime(int time){
		this.splashTime = time;
	}


	@SuppressWarnings("deprecation")
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		if (MultiSDKUtils.isScreenOriatationPortrait(mcontext)) {
			bitmap = BitmapUtils.decodeResource(mcontext, "sy37_splash_port");
		}else {
			bitmap = BitmapUtils.decodeResource(mcontext, "sy37_splash_land");
		}

		ImageView imageView = new ImageView(mcontext);
		LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
		imageView.setLayoutParams(params);
		imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
		imageView.setImageBitmap(bitmap);
		setContentView(imageView);
		imageView.postDelayed(new Runnable() {
			@Override
			public void run() {
				dismiss();
				bitmap.recycle();
				bitmap = null;
				//如果闪屏结束有需要做的事情
				if (splashCallback!=null) {
					splashCallback.afterSplash();
				}
			}
		}, splashTime * 1000);

		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
			WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
			layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
			getWindow().setAttributes(layoutParams);
		} else {
			hideMiuiNotch();
		}
	}

	private void hideMiuiNotch(){
		int flag = 0x00000100 | 0x00000200 | 0x00000400;
		try {
			Method method = Window.class.getMethod("addExtraFlags",
					int.class);
			method.invoke(getWindow(), flag);
		} catch (Exception e) {
			LogUtil.i("addExtraFlags not found.");		}
	}

	@Override
	public void show() {
		super.show();
		StatusBarUtil.hideSystemUI(getWindow());
	}

	public interface SplashListener {
		public void afterSplash();
	}

	public void setSplashListener(SplashListener listener){
		this.splashCallback = listener;
	}

}
