package com.sqwan.msdk;

import android.content.Context;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.SQSdkInterface;
import com.sqwan.msdk.api.sdk._SQwan;

/**
 * 注意: 这个类名不能改, 插件宿主通过这个类名来反射
 */
public class SQwanCoreImpl extends BaseSQwanCore {


    private SQwanCoreImpl() {
    }

    /**
     * 获取SQwanCore单例
     * 注意: 这个单例不能去掉, 插件宿主通过反射getInstance方法来获取实例
     */
    public static SQwanCoreImpl getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new SQwanCoreImpl();
                }
            }
        }
        return instance;
    }

    /**
     * 设置平台示例
     */
    @Override
    public SQSdkInterface getPlatform(Context context, InitBean bean, SQResultListener initListener) {
        LogUtil.w("SQ getPlatform --> userSdk: " + bean.getUsesdk());
        return new _SQwan(context, bean, initListener);
    }
}
