package com.sqwan.msdk.share.wx;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.utils.ShareUtil;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;

public class SQWXEntryActivity extends Activity implements IWXAPIEventHandler{

	private Bundle shareBundle;
	private boolean isCreate = false;

	private static boolean isSQWXShare = false;

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);

		requestWindowFeature(Window.FEATURE_NO_TITLE);
		try{
			shareBundle = getIntent().getExtras();

			if (null != shareBundle && shareBundle.containsKey("shareUrl")) {
				LogUtil.w("---onCreate share--- ture");

				View shareView = ShareUtil.onCreate(SQWXEntryActivity.this,shareBundle);
				setContentView(shareView);
				isCreate = true;
			}
		}catch (Exception ex){
			ex.printStackTrace();
		}

	}


	@Override
	public void onWindowFocusChanged(boolean hasFocus) {
		super.onWindowFocusChanged(hasFocus);
		if(hasFocus && isCreate){
			isCreate = false;
			ShareUtil.onWindowFocusChanged(SQWXEntryActivity.this);
		}
	}

	@Override
	protected void onNewIntent(Intent intent) {
		super.onNewIntent(intent);
		setIntent(intent);
	}

	// 微信发送请求到第三方应用时，会回调到该方法
	@Override
	public void onReq(BaseReq req) {
		switch (req.getType()) {
			case ConstantsAPI.COMMAND_GETMESSAGE_FROM_WX:
				break;
			case ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX:
				break;
			default:
				break;
		}
	}

	// 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
	@Override
	public void onResp(BaseResp resp) {
		LogUtil.w("---onResp share--- ture");
		ShareUtil.onResp(resp.errCode,SQWXEntryActivity.this);
		LogUtil.w("---onResp Login--- go");
		finish();
	}
}