package com.sqwan.m;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;
import com.demo.base.ToastUtil;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.share.SharePlatform;
import com.parameters.share.ShareTextInfo;
import com.parameters.share.ShareWebInfo;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.SQResultListener;
import com.sy.yxjun.R;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.json.JSONException;
import org.json.JSONObject;

public class ShareDemoActivity extends Activity implements View.OnClickListener {

   private EditText shareJsEditText;
   private EditText shareSystemEditText;

   @Override
   protected void onCreate(@Nullable Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);

      setContentView(R.layout.sy37_activity_share_demo);

      shareJsEditText = findViewById(R.id.et_share_h5_content);
      shareSystemEditText = findViewById(R.id.et_share_system_content);

      findViewById(R.id.btn_share_h5_way_wechat).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_way_h5_moment).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_way_h5_qq).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_type_picture).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_type_link).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_goto_h5).setOnClickListener(this);
      findViewById(R.id.btn_share_h5_goto_api).setOnClickListener(this);

      findViewById(R.id.btn_share_system_type_picture).setOnClickListener(this);
      findViewById(R.id.btn_share_system_type_link).setOnClickListener(this);
      findViewById(R.id.btn_share_system_type_text).setOnClickListener(this);
      findViewById(R.id.btn_share_system_goto_h5).setOnClickListener(this);
      findViewById(R.id.btn_share_system_goto_api).setOnClickListener(this);
   }

   @Override
   public void onClick(View v) {
      int viewId = v.getId();
      if (viewId == R.id.btn_share_h5_way_wechat) {
         setShareJsContent(shareJsEditText, "way", "wechat");
      } else if (viewId == R.id.btn_share_h5_way_h5_moment) {
         setShareJsContent(shareJsEditText, "way", "moments");
      } else if (viewId == R.id.btn_share_h5_way_h5_qq) {
         setShareJsContent(shareJsEditText, "way", "qq");
      } else if (viewId == R.id.btn_share_h5_type_picture) {
         setShareJsContent(shareJsEditText, "type", "1");
      } else if (viewId == R.id.btn_share_h5_type_link) {
         setShareJsContent(shareJsEditText, "type", "2");
      } else if (viewId == R.id.btn_share_h5_goto_h5) {
          invokeMethod(getSQCommonJsInterfaceInstance(), "share", String.class, shareJsEditText.getText().toString());
      } else if (viewId == R.id.btn_share_h5_goto_api) {
         try {
            JSONObject jsonObject = new JSONObject(shareJsEditText.getText().toString());
            String type = jsonObject.optString("type");

            ShareMessage shareMessage = new ShareMessage();
            if ("1".equals(type)) {
               ShareImageInfo shareImageInfo = new ShareImageInfo();
               shareImageInfo.setBitmap(readBitmap(R.drawable.sy37_splash_land));

               shareMessage.setShareMessage(shareImageInfo);
            } else if ("2".equals(type)) {
               ShareWebInfo shareWebInfo = new ShareWebInfo();
               shareWebInfo.setThumbBmp(readBitmap(R.drawable.sy37_splash_land));
               shareWebInfo.setTitle(jsonObject.optString("title"));
               shareWebInfo.setPageUrl(jsonObject.optString("landingPageUrl"));
               shareWebInfo.setDesc(jsonObject.optString("desc"));

               shareMessage.setShareMessage(shareWebInfo);
            } else {
               com.demo.base.ToastUtil.showToast(this, "暂不支持的分享类型：" + type);
               return;
            }

            String way = jsonObject.optString("way");
            if ("wechat".equals(way)) {
               shareMessage.setPlatform(SharePlatform.WECHAT);
            } else if ("moments".equals(way)) {
               shareMessage.setPlatform(SharePlatform.WECHAT_MOMENT);
            } else if ("qq".equals(way)) {
               shareMessage.setPlatform(SharePlatform.QQ);
            } else {
               com.demo.base.ToastUtil.showToast(this, "暂不支持的分享平台类型：" + type);
               return;
            }
            shareMessage.setSkipPreview(true);
            SQwanCore.getInstance().share(shareMessage, mShareListener);
         } catch (JSONException e) {
            e.printStackTrace();
            com.demo.base.ToastUtil.showToast(this, "Json 解析出错");
         }
      }

      if (viewId == R.id.btn_share_system_type_picture) {
         setShareJsContent(shareSystemEditText, "shareType", "1");
      } else if (viewId == R.id.btn_share_system_type_link) {
         setShareJsContent(shareSystemEditText, "shareType", "2");
      } else if (viewId == R.id.btn_share_system_type_text) {
         setShareJsContent(shareSystemEditText, "shareType", "3");
      } else if (viewId == R.id.btn_share_system_goto_h5) {
         invokeMethod(getSQCommonJsInterfaceInstance(), "shareToSystem", String.class, shareSystemEditText.getText().toString());
      } else if (viewId == R.id.btn_share_system_goto_api) {
         try {
            JSONObject jsonObject = new JSONObject(shareSystemEditText.getText().toString());
            String type = jsonObject.optString("shareType");

            ShareMessage shareMessage = new ShareMessage();
            if ("1".equals(type)) {
               ShareImageInfo shareImageInfo = new ShareImageInfo();
               shareImageInfo.setBitmap(readBitmap(R.drawable.sy37_splash_land));

               shareMessage.setShareMessage(shareImageInfo);
            } else if ("2".equals(type)) {
               ShareWebInfo shareWebInfo = new ShareWebInfo();
               shareWebInfo.setThumbBmp(readBitmap(R.drawable.sy37_splash_land));
               shareWebInfo.setTitle(jsonObject.optString("shareTitle"));
               shareWebInfo.setPageUrl(jsonObject.optString("shareLinkUrl"));
               shareWebInfo.setDesc(jsonObject.optString("shareText"));

               shareMessage.setShareMessage(shareWebInfo);
            } else if ("3".equals(type)) {
               ShareTextInfo shareTextInfo = new ShareTextInfo();
               shareTextInfo.setText(jsonObject.optString("shareText"));

               shareMessage.setShareMessage(shareTextInfo);
            } else {
               com.demo.base.ToastUtil.showToast(this, "暂不支持的分享类型：" + type);
               return;
            }

            shareMessage.setPlatform(SharePlatform.SYSTEM);
            shareMessage.setSkipPreview(true);
            SQwanCore.getInstance().share(shareMessage, mShareListener);
         } catch (JSONException e) {
            e.printStackTrace();
            com.demo.base.ToastUtil.showToast(this, "Json 解析出错");
         }
      }
   }

   private void setShareJsContent(EditText editText, String key, String value) {
      try {
         JSONObject jsonObject = new JSONObject(editText.getText().toString());
         jsonObject.putOpt(key, value);
         editText.setText(jsonObject.toString(2));
         com.demo.base.ToastUtil.showToast(this, "修改 " + key + " 值成功");
      } catch (JSONException e) {
         e.printStackTrace();
         com.demo.base.ToastUtil.showToast(this, "Json 解析出错");
      }
   }

   private Bitmap readBitmap(int resId) {
      BitmapFactory.Options opt = new BitmapFactory.Options();
      opt.inPreferredConfig = Bitmap.Config.RGB_565;
      opt.inPurgeable = true;
      opt.inInputShareable = true;
      InputStream is = this.getResources().openRawResource(resId);
      return BitmapFactory.decodeStream(is, null, opt);
   }

   private final SQResultListener mShareListener = new SQResultListener() {

      @Override
      public void onSuccess(Bundle bundle) {
         ToastUtil.showToast(ShareDemoActivity.this, "分享成功");
      }

      @Override
      public void onFailture(int code, String msg) {
         ToastUtil.showToast(ShareDemoActivity.this, msg);
      }
   };

   private Object sqCommonJsInterfaceInstance;

   private Object getSQCommonJsInterfaceInstance() {
      if (sqCommonJsInterfaceInstance == null) {
         try {
            Class<?> sqWebViewClazz = getClassLoader().loadClass("com.sqwan.common.webview.SQWebView");
            Constructor<?> sqWebViewConstructor = sqWebViewClazz.getConstructor(Context.class);
            Object sqWebViewInstance = sqWebViewConstructor.newInstance(this);

            Class<?> sqCommonJsInterfaceClazz = getClassLoader().loadClass("com.sqwan.common.webview.SQCommonJsInterface");
            Constructor<?> sqCommonJsInterfaceConstructor = sqCommonJsInterfaceClazz.getConstructor(sqWebViewClazz);
            sqCommonJsInterfaceInstance = sqCommonJsInterfaceConstructor.newInstance(sqWebViewInstance);
         } catch (ClassNotFoundException | IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
            com.demo.base.ToastUtil.showToast(this, "反射获取 SQCommonJsInterface 失败");
         }
      }
      return sqCommonJsInterfaceInstance;
   }

   private void invokeMethod(Object object, String methodName, Class<?> argsClazz, Object... argsValues) {
      try {
         Class<?> clazz = object.getClass();
         Method declaredMethod = clazz.getDeclaredMethod(methodName, argsClazz);
         declaredMethod.setAccessible(true);
         declaredMethod.invoke(object, argsValues);
         com.demo.base.ToastUtil.showToast(this, "反射执行方法成功");
      } catch (NullPointerException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
         e.printStackTrace();
         ToastUtil.showToast(this, "反射执行方法失败");
      }
   }

   @Override
   public Resources getResources() {
      return SQwanCore.getInstance().getResources(super.getResources());
   }

   @Override
   public ClassLoader getClassLoader() {
      return SQwanCore.getInstance().getClassLoader(super.getClassLoader());
   }

   @Override
   public void startActivity(Intent intent) {
      SQwanCore.getInstance().startActivity(intent);
      super.startActivity(intent);
   }

   @Override
   public void startActivityForResult(Intent intent, int requestCode) {
      SQwanCore.getInstance().startActivityForResult(intent, requestCode);
      super.startActivityForResult(intent, requestCode);
   }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SQwanCore.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SQwanCore.getInstance().onActivityResult(requestCode, resultCode, data);
    }
}