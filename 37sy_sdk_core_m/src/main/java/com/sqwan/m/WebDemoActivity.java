package com.sqwan.m;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;
import android.widget.Toast;
import com.parameters.performfeatureconfig.PerformFeatureType;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.MultiSDKUtils;
import com.sqwan.msdk.api.SQResultListener;
import com.sy.yxjun.R;
import com.sy37sdk.account.auth.AuthConfigCache;
import com.sy37sdk.account.auth.AuthManager;
import com.sy37sdk.account.captcha.CaptchaDialog;
import com.sy37sdk.account.captcha.VerifyPhoneDialog;
import com.sy37sdk.account.floatview.SqFloatViewManager;
import com.sy37sdk.account.floatview.redpacket.RedPacketInfo;
import com.sy37sdk.account.floatview.redpacket.RedPacketManager;
import com.sy37sdk.account.policy.PolicyManager;
import com.sy37sdk.account.uagree.UAgreeCacheHelper;

/**
 *    author : 黄锦群
 *    time   : 2022/04/15
 *    desc   : WebView 案例测试页面
 */
public class WebDemoActivity extends Activity {

   @Override
   protected void onCreate(@Nullable Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);
      setContentView(R.layout.sy37_web_demo_activity);
   }

   public void showSQNoticeDialog(View view) {
      String url;
      if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
         url = "https://37.com.cn/aicc/*************/vindex.html?pop_id=notice_2380";
      } else {
         url = "https://37.com.cn/aicc/*************/index.html?pop_id=notice_2380";
      }
      MultiSDKUtils.showNoticeDialog(this, "", url);
   }

   public void showAuthDialog(View view) {
      // AuthConfigCache.needAuth()
      String url= "http://37.com.cn/sdk/sdk-smrz/?authType=2&code=2&triggerType=reportDevDuration&operation=1&needCurfew=0";
      AuthManager.getInstance(this).showPersonalDialog(transformURLForChannel(url), AuthConfigCache.isAuthFocus(),
              AuthConfigCache.needAccumulateDuration(), false, null);
   }

   public void showPolicyDialog(View view) {
      String url = "https://37.com.cn/sdk/anti-addiction/vertical-indulge.html?indulgeType=3&code=2&ageCode=2";
      PolicyManager.getInstance().showPolicyDialog(this, url, false);
   }

   public void showRedPacketDialog(View view) {
      final RedPacketInfo redPacketInfo = new RedPacketInfo();
      RedPacketInfo.WebViewConfig webViewConfig = new RedPacketInfo.WebViewConfig();
      webViewConfig.pop_url = "https://liyuanhn.com/huodong/20201207_gd_red_envelopes_popup?id=11";
      webViewConfig.height = 589;
      webViewConfig.width = 691;
      redPacketInfo.webViewConfig = webViewConfig;
      String icon_url = "https://imgcs.s98s2.com/image/webSite/article/1607595787000/7658fb42-3529-449e-8557-3b02ec6d5074.gif";
      redPacketInfo.imgUrl = icon_url ;
      redPacketInfo.jumpLink = "https://liyuanhn.com/huodong/20201207_gd_red_envelopes/?actid=1190";

//      RedPacketManager.getInstance().downloadFloatViewGifImg(this, redPacketInfo, new RedPacketManager.DownloadGifCallback() {
//         @Override
//         public void getLocalGifPath(String localPath, boolean isCache) {
//            redPacketInfo.imgLocalPath = localPath;
//
//            final RedPacketDialog redPacketDialog = new RedPacketDialog(WebDemoActivity.this);
//            redPacketDialog.initData(SqFloatViewManager.getInstance().floatView, redPacketInfo, new RedPacketDialog.RedPacketDialogCallback() {
//               @Override
//               public void onClose() {
//                  redPacketDialog.dismiss();
//                  SqFloatViewManager.getInstance().showRedPacktFloatView();
//               }
//
//               @Override
//               public void onOpenUrl() {
//                  redPacketDialog.dismiss();
//                  SqTrackActionManager.getInstance().trackAction(SqTrackAction.RED_PACKAGE_FROM_DIALOG);
//                  RedPacketManager.getInstance().showTargetUrl(WebDemoActivity.this, redPacketInfo.jumpLink);
//                  SqFloatViewManager.getInstance().showRedPacktFloatView();
//               }
//            });
//            redPacketDialog.show();
//         }
//      });
      RedPacketManager.getInstance().downloadFloatViewGifImg(this, redPacketInfo, new RedPacketManager.DownloadGifCallback() {
         @Override
         public void getLocalGifPath(String localPath, boolean isCache) {
            redPacketInfo.imgLocalPath = localPath;
            SqFloatViewManager.getInstance().showRedPacketFloat(WebDemoActivity.this, redPacketInfo,true);
         }
      });
   }

   public void showSQWebContainerDialog(View view) {
//      String url = "http://37.com.cn/community/vertical/slg/51/home?popup_background=&pid=1&gid=1012532&uid=479276062&token=BASE64ZWJhZUFidnJpL0lLLzZHOVBNdmYvZ05JSEt5L0Q3VFdZaG1DKzh5WS8zeEZBY0NOSDhjcXdtaWlnT3U3SFBxVklsbjE0OE5wOHpabS81SXFKWUkzY1IxV2dYNzlKSWxsZDJLSzBkb0NFMzhJOXovRTZoQXN0Y1N0SHRWOXE4TElqVElsdlY0RE5oMUVKaHVXK3NjZ1lYZDBmc1dxRm5YbXNXd2tMZzEzekd2b3ZmbXB0Q1ZWSGhqMWZHRTRua2t4NXRJa1RTRTBFZw%3D%3D&dsid=37ios_1&dsname=ios1%E6%9C%8D&drid=3&drname=%E5%AE%89%E5%B0%BC%E6%96%AF%E7%AC%A8%E7%AC%A8&popup_id=45&scheme_id=67&drlevel=10&scut=0";
      String url = "http://37.com.cn/community/vertical/54/home";
//      String url = "https://www.huya.com/chenxi6";
      MultiSDKUtils.showSQWebDialog(this, transformURLForChannel(url));
   }

   public void showSQWebContainerDialog2(View view) {
      String url = "https://37.com.cn/service-system/accountappeal/accSubmit";
      MultiSDKUtils.showSQWebDialog(this, transformURLForChannel(url));
   }

   public void showUserProtocolDialog(View view) {
      SQWebViewDialog dialog = new SQWebViewDialog(this);
      dialog.setUrl(UAgreeCacheHelper.getUrlProtocol(this) + "&isAgree=true");
      dialog.show();
   }

   public void showPrivacyPolicyDialog(View view) {
      SQWebViewDialog dialog = new SQWebViewDialog(this);
      dialog.setUrl(UAgreeCacheHelper.getUrlPolicy(this) + "&isAgree=true");
      dialog.show();
   }

   public void showBaseNormalDialog(View view) {
      SQwanCore.getInstance().performFeature(WebDemoActivity.this, PerformFeatureType.TYPE_SHOW_AGE_APPROPRIATE, null, null);
   }

   public void showPayWebPage(View view) {
      float money = 1;
      SQwanCore.getInstance().pay(WebDemoActivity.this,
              "A" + System.currentTimeMillis(), "一堆金币", "金币", "S001",
              "铁马金戈", "CP扩展字段", "RID0001", "路人甲", 1, money, 10,
              new SQResultListener() {
                 @Override
                 public void onSuccess(Bundle bundle) {
                    ToastUtil.showToast(WebDemoActivity.this, "成功发起充值请求(充值结果以服务端为准)");
                 }

                 @Override
                 public void onFailture(int code, String msg) {
                    ToastUtil.showToast(WebDemoActivity.this, msg);
                 }
              });
   }

   public void showVerifyPhoneDialog(View view) {
      String url = "http://37.com.cn/mt/user/account/upgrade/sept1?mobile=***********";
      //String url = "http://37.com.cn/mt/user/account/upgrade/sept1";
      url = AppUtils.constructWebUrlParam(this, url);
      VerifyPhoneDialog verifyPhoneDialog = new VerifyPhoneDialog(this);
      verifyPhoneDialog.setUrl(url);
      verifyPhoneDialog.setVerifyListener(new VerifyPhoneDialog.VerifyListener() {
         @Override
         public void result(boolean success, String msg) {
            ToastUtil.showToast(WebDemoActivity.this, "result：" + success + ", msg = " + msg);
         }
      });
      verifyPhoneDialog.show();
   }

   public void showCaptchaDialog(View view) {
//      String url = "https://37.com.cn/sdk/security-verify/tx-reg.html?pid=1&gid=1000000&refer=1_1000000_1234_5678&uname=37zdbe6c4249&dev=a6d89466e0e27c65177d54f3f792928a";
      String url = "https://37.com.cn/sdk/security-verify/tx-reg.html";
      url = AppUtils.constructWebUrlParam(this, url);
      CaptchaDialog dialog = new CaptchaDialog(this);
      dialog.setUrl(url);
      dialog.setVerifyListener(new CaptchaDialog.VerifyListener() {
         @Override
         public void result(boolean success, String msg) {
            ToastUtil.showToast(WebDemoActivity.this, "result：" + success + ", msg = " + msg);
         }
      });
      dialog.show();
   }

   public void jsInterface(View view) {
      String url = "https://37.com.cn/freestyle/sdk_Interface/";
      MultiSDKUtils.showSQWebDialog(this, transformURLForChannel(url));
   }

   @Override
   protected void onActivityResult(int requestCode, int resultCode, Intent data) {
      super.onActivityResult(requestCode, resultCode, data);
      // 需要重写这个方法，否则 WebView 选择文件会没有回调
      SQwanCore.getInstance().onActivityResult(requestCode, resultCode, data);
   }

   public String transformURLForChannel(String url) {
      if (MultiSDKUtils.getPID(this).endsWith("1")) {
         // 37平台添加通用参数
         return MultiSDKUtils.constructCommonURL(this, url);
      }
      return url;
   }
}