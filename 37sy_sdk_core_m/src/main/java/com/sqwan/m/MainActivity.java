package com.sqwan.m;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.hardware.SensorManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Toast;

import com.demo.base.ScreenCaptureUtils;
import com.demo.base.ShareAdapter;
import com.parameters.performfeatureconfig.PerformFeatureKey;
import com.parameters.performfeatureconfig.PerformFeatureType;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.share.SharePlatform;
import com.parameters.share.ShareTextInfo;
import com.parameters.share.ShareWebInfo;
import com.sqwan.TestConst;
import com.sqwan.common.dialog.PlatformAnnouncementActivity;
import com.sqwan.common.mod.account.IBindWxListener;
import com.sqwan.common.util.ApkInfoUtil;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import com.sy.yxjun.R;
import com.sy37sdk.account.floatview.FloatViewUtils;
import com.sy37sdk.account.util.SocialAccountUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import notchtools.geek.com.notchtools.core.NotchProperty;
import notchtools.geek.com.notchtools.core.OnNotchCallBack;


public class MainActivity extends Activity implements View.OnClickListener , OnNotchCallBack {

    public static final Handler HANDLER = new Handler(Looper.getMainLooper());

    String TAG = this.getClass().getSimpleName();

    private String appkey = "";


    private SQAppConfig config;

    private ListView lvShare;

    private ImageView ivAgeAppropriate;

    private ShareAdapter shareAdapter;

    private List<String> imgs;

    private LinearLayout trackLayout;
    private EditText trackEventEdt;
    private List<EditText> keyEdts = new ArrayList<>();
    private List<EditText> valueEdts = new ArrayList<>();
    private EditText shareEdt, shareTitleEdt, shareDesEdt;
    private EditText etUrl;
    private CheckBox previewBox;
    private Task screenBrightnessTask = Task.create();

    float screenBrightness;
    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        StatusBarUtil.hideSystemUI(getWindow());
//		//隐藏导航栏
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                StatusBarUtil.hideSystemUI(getWindow());
                LogUtil.i(TAG,"onSystemUiVisibilityChange isUiFlagHideNavigation:"+ FloatViewUtils.isUiFlagHideNavigation(MainActivity.this));
            }
        });
        screenBrightness = getWindow().getAttributes().screenBrightness;
        Log.d(TAG, "screenBrightness: "+screenBrightness);
        handleScrrenBrightnessTask();
    }

    private void handleScrrenBrightnessTask() {
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.screenBrightness = screenBrightness;
        getWindow().setAttributes(params);
        screenBrightnessTask.oneShot(1000 * 5, new Runnable() {
            @Override
            public void run() {
                WindowManager.LayoutParams params = getWindow().getAttributes();
                params.screenBrightness = 0.1f;
                getWindow().setAttributes(params);
            }
        });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        appkey = AppkeyHelper.getAppkey(this);
        LogUtil.d("onCreate");
        String cpu_architect = ApkInfoUtil.chooseByX86andArm(this);
        LogUtil.i(TAG,"cpu_architect " + cpu_architect);
        super.onCreate(savedInstanceState);
        if (TestConst.isTestOrientation) {
//			setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
        }
        setContentView(R.layout.sy37_demo_main);
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }

        Uri data = getIntent().getData();
        if (data != null) {
            String mydata = data.getQuery();
            LogUtil.i("从浏览器启动收到的数据:" + mydata);
        }
        findViewById(R.id.loginBtn).setOnClickListener(this);
        findViewById(R.id.changeAccountBtn).setOnClickListener(this);
        findViewById(R.id.payBtn).setOnClickListener(this);
        findViewById(R.id.payBtn2).setOnClickListener(this);
        findViewById(R.id.logoutBtn).setOnClickListener(this);
        findViewById(R.id.showExit).setOnClickListener(this);
        findViewById(R.id.creatRole).setOnClickListener(this);
        findViewById(R.id.creatRoleBtn).setOnClickListener(this);
        findViewById(R.id.submitDataBtn).setOnClickListener(this);
        findViewById(R.id.upgradeDataBtn).setOnClickListener(this);
        findViewById(R.id.submitStatisticsInfo).setOnClickListener(this);
        findViewById(R.id.getappconfigBtn).setOnClickListener(this);
        findViewById(R.id.wxShareBtn).setOnClickListener(this);
        findViewById(R.id.showSQWebBtn).setOnClickListener(this);
        findViewById(R.id.showPersonalBtn).setOnClickListener(this);
        findViewById(R.id.btn_ua).setOnClickListener(this);
        findViewById(R.id.share_img).setOnClickListener(this);

        findViewById(R.id.btn_system_share_image).setOnClickListener(this);
        findViewById(R.id.btn_system_share_link).setOnClickListener(this);
        findViewById(R.id.btn_system_share_text).setOnClickListener(this);

        findViewById(R.id.share_url).setOnClickListener(this);
        findViewById(R.id.wx_auth).setOnClickListener(this);
        findViewById(R.id.add_share).setOnClickListener(this);
        findViewById(R.id.age_appropriate).setOnClickListener(this);
        findViewById(R.id.btn154).setOnClickListener(this);
        findViewById(R.id.btn156).setOnClickListener(this);
        findViewById(R.id.cp_track_add).setOnClickListener(this);
        findViewById(R.id.cp_track_report).setOnClickListener(this);
        findViewById(R.id.btn_open_url).setOnClickListener(this);
        findViewById(R.id.show_ad).setOnClickListener(this);
        findViewById(R.id.scan_code).setOnClickListener(this);
        findViewById(R.id.show_support_scan).setOnClickListener(this);
        findViewById(R.id.show_comment).setOnClickListener(this);
        findViewById(R.id.show_third_list).setOnClickListener(this);
        findViewById(R.id.show_personal_list).setOnClickListener(this);
        findViewById(R.id.get_record_number).setOnClickListener(this);
        findViewById(R.id.show_miit).setOnClickListener(this);
        etUrl=findViewById(R.id.et_url);
        trackLayout = findViewById(R.id.cp_track_layout);
        trackEventEdt = findViewById(R.id.cp_track_event);
        ivAgeAppropriate = findViewById(R.id.iv_age_appropriate);
        shareEdt = findViewById(R.id.share_url_edt);
        shareDesEdt = findViewById(R.id.share_url_des_edt);
        shareTitleEdt = findViewById(R.id.share_url_title_edt);
        previewBox = findViewById(R.id.share_is_preview);

        lvShare = findViewById(R.id.lv_share);

        imgs = new ArrayList<>();
        imgs.add("1608811521825");
        shareAdapter = new ShareAdapter(this, imgs);
        lvShare.setAdapter(shareAdapter);

        shareAdapter.setOnClickShareListener(new ShareAdapter.OnClickShareListener() {
            @Override
            public void clickShare(String imgId) {
                SQwanCore.getInstance().share("78278", imgId, mShareListener);
            }

            @Override
            public void clickDelete(int pos) {
                imgs.remove(pos);
                shareAdapter.setData(imgs);
            }
        });

        // 模拟 CP 延迟初始化 SDK，请注意不要改成立即的，否则某些场景可能会覆盖不到
        HANDLER.postDelayed(this::setSdkInit, 1000);

        /**
         * 切换账号监听 测试方法：悬浮窗-点击【切换账号】-可以弹出登录界面，登录成功后，会回到onSuccess的回调中。
         *
         * @param listener
         *            切换账号监听回调
         **/
        SQwanCore.getInstance().setSwitchAccountListener(
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        Toast.makeText(MainActivity.this, "悬浮窗切换账号成功：" + bundle, Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        Toast.makeText(MainActivity.this,
                                "悬浮窗切换账号失败:" + "\n msg=" + msg,
                                Toast.LENGTH_LONG).show();
                    }
                });
        /**
         * 回到游戏登录界面监听 测试方法：悬浮窗-点击【账户】-点击【注销登录】，将会回调到onSuccess中
         *
         * @param listener
         *            返回游戏登录界面回调
         **/
        SQwanCore.getInstance().setBackToGameLoginListener(
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        // 1.CP重置游戏状态，并回到游戏的登录界面
                        // 2.在游戏登录界面，用户可以通过点击[开始游戏]按钮，调用changeAccount进行登录 或者
                        // CP主动调用changeAccount进行登录
                        // 3.切记，不要调用login
                        Toast.makeText(MainActivity.this,
                                "重置游戏状态，回到游戏的登录界面，用户需要重新登录", Toast.LENGTH_LONG)
                                .show();
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        // 不做处理。
                    }
                });
        /**
         * 进入语音房间接口回调
         * 进入成功之后会返回：
         * 1.roomname:房间名称
         * 2.mermberid:成员id
         * 3.anchorname:主播名称
         * 4.进入房间超时时间
         */
        SQwanCore.getInstance().onJoinRoomListener(MainActivity.this, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                Toast.makeText(
                        MainActivity.this,
                        "进入房间成功:" + "\n roomname:"
                                + bundle.getString("roomname") + "\n memberid:"
                                + bundle.getString("memberid") + "\n anchorname:"
                                + bundle.getString("anchorname") + "\n timeout:"
                                + bundle.getString("timeout"),
                        Toast.LENGTH_LONG).show();
            }

            @Override
            public void onFailture(int code, String msg) {
                Toast.makeText(MainActivity.this,
                        "进入房间失败:" + "\n msg=" + msg,
                        Toast.LENGTH_LONG).show();
            }
        });
        /**
         * 退出语音房间接口回调
         */
        SQwanCore.getInstance().onQuitRoomListener(MainActivity.this, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                Toast.makeText(
                        MainActivity.this,
                        "退出房间成功，cp在此进行相应处理-更新播放按钮ui",
                        Toast.LENGTH_LONG).show();
            }

            @Override
            public void onFailture(int code, String msg) {
                Toast.makeText(MainActivity.this,
                        "退出房间失败:" + "\n msg=" + msg,
                        Toast.LENGTH_LONG).show();
            }
        });

        /**
         * 设置游戏中截图的监听，实现游戏中截图的逻辑，sdk在需要游戏中截图的时候会通过监听获取游戏中截图
         */
        SQwanCore.getInstance().setScreenshotListener(new IScreenshotListener() {
            @Override
            public Bitmap createScreenshot() {
                //实现游戏中截图功能，将截取的图片转为bitmap格式返回
                // 以下是模拟游戏中，对截图线程加锁，然后待游戏引擎获取到游戏截图后，再解锁返回截取的图片
                synchronized (mLock) {
                    testGameScreenCapture();
                    try {
                        // 对线程加锁，此线程为非UI线程，可以放心加锁
                        mLock.wait();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                return mScreenCaptureBitmap;
            }
        });

        mOrientationListener = new OrientationEventListener(this, SensorManager.SENSOR_DELAY_NORMAL) {
            @Override
            public void onOrientationChanged(int orientation) {
//				Log.e("sqsdk", "orientation --> " + orientation);
            }
        };
        _setAuthResultListener();

        //		AuthCountDownManager.getInstance().startTask(this,true,1000*20);
        notchConfigOnCreate();

        View decorView = getWindow().getDecorView();
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
            View.SYSTEM_UI_FLAG_FULLSCREEN |
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY |
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
        decorView.setSystemUiVisibility(uiOptions);
    }

    private void setSdkInit() {
        /**
         * 1.越早调用越好，放在调用游戏UI界面之前调用 2.初始化接口只需调用一次 3.测试方法：37闪屏在游戏的第一页。
         *
         * @param context
         *            上下文
         * @param appkey
         *            37提供的基础通信秘钥
         * @param listener
         *            初始化监听回调
         */
        SQwanCore.getInstance().init(MainActivity.this, appkey,
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        Toast.makeText(MainActivity.this, "初始化完成",
                                Toast.LENGTH_SHORT).show();
                        SQwanCore.getInstance().setSQPushTransmitMessageListener(
                            json -> Toast.makeText(MainActivity.this, "收到推送: " + json, Toast.LENGTH_LONG).show());

                        if (bundle != null) {
                            String imei = bundle.getString("imei");
                            boolean isUpdate = bundle.getBoolean("is_update");
                            String updateType = bundle.getString("update_type");
                            Log.i("MainActivity", "imei --> " + imei + ", isUpdate " + isUpdate + ", updateType " + updateType);
                        }
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        Toast.makeText(MainActivity.this, "初始化失败",
                                Toast.LENGTH_SHORT).show();
                    }

                });
    }

    //政策相关
    public void _setAuthResultListener() {
        /**
         * 实名认证回调情景：
         * onSuccess：
         *  实名成功
         *
         * onFailture：
         * 游戏方需要处理：
         * 1、回调游戏进服界面
         * 2、调用退服接口
         */
        SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_AUTHRESULTCHECK, null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                LogUtil.i("AuthResult onSuccess");
                ToastUtil.showToast(MainActivity.this, "AuthResult onSuccess");
            }

            @Override
            public void onFailture(int code, String msg) {
                LogUtil.e("AuthResult onFailture");
                ToastUtil.showToast(MainActivity.this, "AuthResult onFailture");
            }
        });
    }

    // ----------------------- 周期方法声明开始 ------------------------------
    @Override
    protected void onStart() {
        super.onStart();
        SQwanCore.getInstance().onStart();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        SQwanCore.getInstance().onRestart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        SQwanCore.getInstance().onResume();
        mOrientationListener.enable();
//		StatusBarUtil.hideSystemUI(getWindow());
    }

    @Override
    protected void onPause() {
        super.onPause();
        SQwanCore.getInstance().onPause();
        mOrientationListener.disable();
    }

    @Override
    protected void onStop() {
        super.onStop();
        SQwanCore.getInstance().onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SQwanCore.getInstance().onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        LogUtil.d("onActivityResult");
        SQwanCore.getInstance().onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        SQwanCore.getInstance().onNewIntent(intent);
    }

    // ----------------------- 周期方法声明结束 ------------------------------
    @Override
    public void onClick(View v) {
        final int i = v.getId();
        if (i == R.id.loginBtn) {/**
         * 登录接口，需要在UI线程中调用
         *
         * @param context
         *            上下文
         * @param loginListener
         *            登录回调
         */
            SQwanCore.getInstance().login(MainActivity.this,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            Log.i("sqsdk", "[Demo]登录成功: " + bundle);
                            // CP拿到token后，需要通过服务器进行token验证，拿到对应的uid和uname
                            Toast.makeText(MainActivity.this, "登录成功：" + bundle, Toast.LENGTH_LONG).show();
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this, "登录失败回调：" + msg,
                                    Toast.LENGTH_LONG).show();
                        }
                    });

        } else if (i == R.id.changeAccountBtn) {/**
         * 游戏内切换账号接口，需要在UI线程中调用 CP也可以通过回到游戏自己的登录界面重新发起登录，达到切换账号的目的。
         *
         * @param context
         *            上下文
         * @param loginListener
         *            登录回调
         */
            SQwanCore.getInstance().changeAccount(MainActivity.this,
                    new SQResultListener() {

                        @Override
                        public void onSuccess(Bundle bundle) {
                            Log.i("sqsdk", "[Demo]切换账号成功: " + bundle);
                            // CP拿到token后，需要通过服务器进行token验证，拿到对应的uid和uname
                            Toast.makeText(MainActivity.this, "主动切换账号成功：" + bundle, Toast.LENGTH_LONG).show();
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this, msg,
                                    Toast.LENGTH_LONG).show();
                        }
                    });

        } else if (i == R.id.payBtn) {/**
         * 支付接口(选接)参数说明 !!! 注意必传参数,不能为空，推荐所有参数都传值 !!!
         *
         * @param context
         *            上下文 (*必传)
         * @param doid
         *            CP订单ID (*必传)
         * @param dpt
         *            CP商品名(*必传)
         * @param dcn
         *            CP货币名称 (*必传)
         * @param dsid
         *            CP游戏服ID (*必传)
         * @param dsname
         *            CP游戏服名称(*必传)
         * @param dext
         *            CP扩展回调参数 (*必传)
         * @param drid
         *            CP角色ID(*必传)
         * @param drname
         *            CP角色名(*必传)
         * @param drlevel
         *            CP角色等级(*必传)
         * @param dmoney
         *            CP金额(定额) (*必传)
         * @param dradio
         *            CP兑换比率(1元兑换率默认1:10)(*必传)
         * @param payListener
         *            充值回调 (*必传)
         *
         */
            EditText etMoney = findViewById(R.id.et_money);
            float money = TextUtils.isEmpty(etMoney.getText().toString()) ? 0 : Float.parseFloat(etMoney.getText().toString());
            SQwanCore.getInstance().pay(MainActivity.this,
                    "A" + System.currentTimeMillis(), "一堆金币", "金币", "S001",
                    "铁马金戈", "CP扩展字段", "RID0001", "路人甲", 1, money, 10,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            Toast.makeText(MainActivity.this,
                                    "[Demo]成功发起充值请求(充值结果以服务端为准)", Toast.LENGTH_LONG)
                                    .show();
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this, "[Demo]" + msg,
                                    Toast.LENGTH_LONG).show();
                        }
                    });
        } else if (i == R.id.payBtn2) {
            SQwanCore.getInstance().pay(MainActivity.this,
                    "A" + System.currentTimeMillis(), "一堆金币", "金币", "S001",
                    "金戈铁马", "", "RID0001", "路人甲", 1, 0, 10,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            Toast.makeText(MainActivity.this,
                                    "成功发起充值请求(充值结果以服务端为准)", Toast.LENGTH_LONG)
                                    .show();
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this, msg,
                                    Toast.LENGTH_LONG).show();
                        }
                    });

        } else if (i == R.id.logoutBtn) {
            Toast.makeText(MainActivity.this, "该接口已作废，不建议使用", Toast.LENGTH_LONG)
                    .show();

        } else if (i == R.id.showExit) {/**
         * 退出游戏弹窗参数说明:
         *
         * @param context
         *            上下文
         * @param logoutListener
         *            登出游戏回调接口
         */
            SQwanCore.getInstance().showExitDailog(MainActivity.this,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            // 离开电台
                            leaveRadioRoom(null);
                            Toast.makeText(MainActivity.this,
                                    "登出完成，请处理游戏逻辑(例如清理资源、退出游戏等)",
                                    Toast.LENGTH_LONG).show();
                            // 在此做真正退出游戏逻辑，此处是模拟退出
                            System.exit(0);
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this,
                                    "取消登出，则不做退出处理，继续游戏", Toast.LENGTH_LONG)
                                    .show();
                        }
                    });

        } else if (i == R.id.creatRole) {
//			Toast.makeText(MainActivity.this, "该接口已作废，不建议使用", Toast.LENGTH_LONG)
//					.show();

            Intent intent = new Intent(this, PlatformAnnouncementActivity.class);
            intent.putExtra("title", "AAAAAAA");
            intent.putExtra("content", "BBBBBB");
            startActivity(intent);
            overridePendingTransition(0, 0);

        } else if (i == R.id.creatRoleBtn) {/**
         * 提交角色信息参数说明
         *
         * @param serverId
         *            当前玩家登录的区服ID
         * @param serverName
         *            当前玩家登录的区服名称
         * @param roleId
         *            当前玩家的角色ID
         * @param roleName
         *            当前玩家的角色名称
         * @param roleLevel
         *            当前玩家的角色等级
         * @param balance
         *            用户余额（RMB购买的游戏币）
         * @param partyName
         *            帮派、公会等，没有则填空字符串
         * @param vipLevel
         *            Vip等级,没有vip系统的传0
         * @param roleCTime
         *            角色创建时间，单位秒，必须真实，需要CP保存在服务器，每次从服务器进行获取。
         * @param roleLevelMTime
         *            角色升级的时间，单位：秒。获取不到请传-1
         */
            final HashMap<String, String> infos1 = new HashMap<String, String>();
            infos1.put(SQwanCore.INFO_SERVERID, "yourServerId");
            infos1.put(SQwanCore.INFO_SERVERNAME, "yourServerName");
            infos1.put(SQwanCore.INFO_ROLEID, (System.currentTimeMillis() / 1000) + "");
            infos1.put(SQwanCore.INFO_ROLENAME, "角色名");
            infos1.put(SQwanCore.INFO_ROLELEVEL, "yourRoleLevel");
            infos1.put(SQwanCore.INFO_BALANCE, "yourBalance");
            infos1.put(SQwanCore.INFO_PARTYNAME, "yourPartyName");
            infos1.put(SQwanCore.INFO_VIPLEVEL, "yourVipLevel");
            infos1.put(SQwanCore.INFO_ROLE_TIME_CREATE, "" + 1458542706);// 从服务器获取的真实创建角色时间
            infos1.put(SQwanCore.INFO_ROLE_TIME_LEVEL, "-1");// 第一次创建，没有升级时间，传-1
            SQwanCore.getInstance().creatRoleInfo(infos1);
//            Toast.makeText(getApplicationContext(), infos1.toString(),
//                    Toast.LENGTH_SHORT).show();
            ToastUtil.showToast(infos1.toString());

        } else if (i == R.id.submitDataBtn) {
            HashMap<String, String> infos2 = new HashMap<String, String>();
            infos2.put(SQwanCore.INFO_SERVERID, "99996");// 服务器id
            infos2.put(SQwanCore.INFO_SERVERNAME, "yourServerName");// 服务器名称
            infos2.put(SQwanCore.INFO_SERVERTIME, System.currentTimeMillis()/1000 + "");  //开服时间
            infos2.put(SQwanCore.INFO_ROLEID, (System.currentTimeMillis() / 1000) + "");// 角色id
            infos2.put(SQwanCore.INFO_ROLENAME, "夶夶夶"+System.currentTimeMillis());// 角色名称
            infos2.put(SQwanCore.INFO_ROLELEVEL, "1");// 角色等级
            infos2.put(SQwanCore.INFO_BALANCE, "12");// 角色余额
            infos2.put(SQwanCore.INFO_PARTYNAME, "yourPartyName");// 工会名称
            infos2.put(SQwanCore.INFO_VIPLEVEL, "10");// vip等级
            infos2.put(SQwanCore.INFO_ROLE_TIME_CREATE, "" + 1458542706);// 从服务器获取的真实创建角色时间
            infos2.put(SQwanCore.INFO_ROLE_TIME_LEVEL, "-1");// 第一次创建，没有升级时间，传-1
            SQwanCore.getInstance().submitRoleInfo(infos2);
            Toast.makeText(getApplicationContext(), infos2.toString(),
                    Toast.LENGTH_SHORT).show();


        } else if (i == R.id.upgradeDataBtn) {
            HashMap<String, String> info3 = new HashMap<String, String>();
            info3.put(SQwanCore.INFO_SERVERID, "yourServerId");
            info3.put(SQwanCore.INFO_SERVERNAME, "yourServerName");
            info3.put(SQwanCore.INFO_ROLEID, (System.currentTimeMillis() / 1000) + "");
            info3.put(SQwanCore.INFO_ROLENAME, "角色名");
            info3.put(SQwanCore.INFO_ROLELEVEL, "yourRoleLevel");
            info3.put(SQwanCore.INFO_BALANCE, "yourBalance");
            info3.put(SQwanCore.INFO_PARTYNAME, "yourPartyName");
            info3.put(SQwanCore.INFO_VIPLEVEL, "yourVipLevel");
            info3.put(SQwanCore.INFO_ROLE_TIME_CREATE, "" + 1458542706);// 从服务器获取的真实创建角色时间
            info3.put(SQwanCore.INFO_ROLE_TIME_LEVEL, "" + 145345667);// 当前角色升级时间
            SQwanCore.getInstance().upgradeRoleInfo(info3);
            Toast.makeText(getApplicationContext(), info3.toString(),
                    Toast.LENGTH_SHORT).show();

        } else if (i == R.id.submitStatisticsInfo) {/**
         * 提交统计数据接口参数说明:
         *
         * @param key
         *            所提交数据的键
         * @param values
         *            所提交数据的值
         */
            SQwanCore.getInstance().submitStatisticsInfo("consume", "特定数据格式");

        } else if (i == R.id.getappconfigBtn) {/**
         * 获取游戏配置信息(其实是解析assets/37wan_config.xml配置文件)
         *
         * @result gid 游戏id(appid)
         * @result pid 联运商id(pid)
         * @result refer 渠道号(refer)
         */
            config = SQwanCore.getInstance().getAppConfig();
            String gid = config.getGameid();
            String pid = config.getPartner();
            String refer = config.getRefer();
            Toast.makeText(MainActivity.this,
                    "gid:" + gid + " \npid:" + pid + "\nrefer:" + refer,
                    Toast.LENGTH_LONG).show();

        } else if (i == R.id.wxShareBtn) {
            String SDCARD_ROOT = Environment.getExternalStorageDirectory()
                    .getAbsolutePath();
            String wxImageUrl = "http://imgsrc.baidu.com/image/c0%3Dshijue1%2C0%2C0%2C294%2C40/sign=971de28cc1ef7609280691dc46b4c9b9/4a36acaf2edda3cce7305e310be93901203f92cf.jpg";
            String wxIconUrl = "http://image.37wan.cn/upload/image/20160909/1473410428292547.png";
            String sdImageUrl = SDCARD_ROOT + "/37shouyou/wxdts.jpg";
            /**
             * 分享到微信
             *
             * @param context
             * @param title
             *            分享的文字标题
             * @param description
             *            分享的文字内容
             * @param webUrl
             *            跳转到WEB的链接地址
             * @param imageUrl
             *            分享的图片URL地址
             * @param resourceType
             *            1:分享图片 0:分享WEB链接
             */
            SQwanCore.getInstance().shareToWX(MainActivity.this, "永恒纪元",
                    "这里是介绍内容", "http://www.37.com.cn/dsf/", wxImageUrl, 0,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            System.out.println("  恭喜你分享成功 ");
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            System.out.println(msg);
                        }
                    });

        } else if (i == R.id.showSQWebBtn) {
//			final String url = "http://37.com.cn/community/vertical/slg/51/home?popup_background=&pid=1&gid=1012532&uid=*********&token=BASE64ZWJhZUFidnJpL0lLLzZHOVBNdmYvZ05JSEt5L0Q3VFdZaG1DKzh5WS8zeEZBY0NOSDhjcXdtaWlnT3U3SFBxVklsbjE0OE5wOHpabS81SXFKWUkzY1IxV2dYNzlKSWxsZDJLSzBkb0NFMzhJOXovRTZoQXN0Y1N0SHRWOXE4TElqVElsdlY0RE5oMUVKaHVXK3NjZ1lYZDBmc1dxRm5YbXNXd2tMZzEzekd2b3ZmbXB0Q1ZWSGhqMWZHRTRua2t4NXRJa1RTRTBFZw%3D%3D&dsid=37ios_1&dsname=ios1%E6%9C%8D&drid=3&drname=%E5%AE%89%E5%B0%BC%E6%96%AF%E7%AC%A8%E7%AC%A8&popup_id=45&scheme_id=67&drlevel=10&scut=0";
//			final String url = "https://37.com.cn/service-system/accountappeal/accSubmit?locale=zh-cn&gn=%E6%96%B0%E5%AE%98%E6%96%97&uname=37zd87df6711&gid=1000000&pid=1&dev=937e4b62307e24d4311a3215040116c2&token=BASE64ODljNDFzVG9tR252cXF2Y1ppbGRERFd5TVpIdlRLVi9jQjgra0toaU9QRlNHeThhWEhIeVFYNWoySDdleUV2c3VzS1B5alkyMS9PR3cvS25mR2pMaWgxZS9aWVNHejRFSmJKcno3OWx6VHQrZ29WUmZERHZCTG52WFVock9JNkZ5SGo0Y2pOZlc3cysyZUpsdnFOT0poVTNtS0dBdmJXYWhTditpRUd2U29tZERBQ2ZPMGJ5NnJHWVN3WUtSM3FrVWFEUjl3VHRveFFPVzJpbDJTTkhPdUV0N0NR&sversion=3.7.0&refer=1_1012567_1234_1234&scut=0&dsid=0&os=1";
//			final String url = "http://37.com.cn/community/vertical/slg/51/home?popup_background=&pid=1&gid=1012532&uid=*********&token=BASE64ZWJhZUFidnJpL0lLLzZHOVBNdmYvZ05JSEt5L0Q3VFdZaG1DKzh5WS8zeEZBY0NOSDhjcXdtaWlnT3U3SFBxVklsbjE0OE5wOHpabS81SXFKWUkzY1IxV2dYNzlKSWxsZDJLSzBkb0NFMzhJOXovRTZoQXN0Y1N0SHRWOXE4TElqVElsdlY0RE5oMUVKaHVXK3NjZ1lYZDBmc1dxRm5YbXNXd2tMZzEzekd2b3ZmbXB0Q1ZWSGhqMWZHRTRua2t4NXRJa1RTRTBFZw%3D%3D&dsid=37ios_1&dsname=ios1%E6%9C%8D&drid=3&drname=%E5%AE%89%E5%B0%BC%E6%96%AF%E7%AC%A8%E7%AC%A8&popup_id=45&scheme_id=67&drlevel=10&scut=0";
            String url = "http://37.com.cn/community/vertical/54/home";
            url = "http://forum.odchqpto.com/?tgid=5636&&forceOrientation=1";
            url = "https://ai-agent-app.39on.com/voice";
//            url="http://37.com.cn/payment/?alipay=1&wechat=1&wallet=1&labor=1&ali-wap-balance=1&huabei=1&dmoney=100.0&dradio=10&drname=%E8%B7%AF%E4%BA%BA%E7%94%B2&os_desc=harmony&sversion=3.7.8&ig=1&os=1&dcn=%E4%B8%80%E5%A0%86%E9%87%91%E5%B8%81&dev=15cc11140e6fbb1c2593bb2c4aaeb60c&dpt=%E4%B8%80%E5%A0%86%E9%87%91%E5%B8%81&gid=1000000&pid=1&uid=907420775&code=0&dext=CP%E6%89%A9%E5%B1%95%E5%AD%97%E6%AE%B5&doid=A1638847470561&drid=RID0001&dsid=S001&moid=2FBBF9C2B6019E4019E1394FF1B163A4&pway=%7B%22wxpway%22%3A%22wxpay%22%7D&scut=0&sign=f8654a621b7c1ee7d95385bc5e0e33bc&time=1638847471&haswx=0&gwversion=4.1.0&refer=sy_00001&token=BASE64OWIxYmVPR2I1dDNMT2VDU0Y0ZlRjUG1rWGMyWmNJUU0xV1Y2T2xVejhsaWlvWWJMQ2RvYzlZdXh0YkIxWUxEVkJUeDJkWW44VG1ET1RvVFlUanBhckhBTE9PWkJZK0NRTFJMcGZlY0lEYThvYWF0TC9nQlgyVUJEZDdJUklwSkVJN2hPU1ZUbHIzWnNPMXZVc2Zqc2dOTS93RkhzcGlQdnNLa3dEekFyR2Z4d0FkYUZTK1IybWhaL1Z6YmlsSjI3T0RVYkVwaElTUHpNemljY2tNV2tQdXNKSWVzTXZlSmJlQjFSRnA0bkRmL2lNdFhYaEREcDdkeW1aL2lBWkNuM2dQMGpWaWpCL3o4Y0FMcUM2aHpqMFE%3D&uname=ligbee&version=3.7.8&drlevel=1";
            /**
             * 在SQWebDialog内打开指定URL
             *
             * @param url 指定URL
             */
            SQwanCore.getInstance().showSQWebDialog(url);

        } else if (i == R.id.btn154) {
            String json = "{ \"url\":\"http://37.com.cn/community/vertical/54/home?gid=1000000&pid=1&dev=c4c6f0a8e946b44a82f2b5e732a1d195&token=BASE64MTFkNmMxa0IwNDVRdG5LL1dudXhBNUc0bkpuaEJqWXV1SkZtR3FLL2cwMVVrZFYzMVJPNGJVK3I0TDdabVV4dCsvT2FjamF1T21JUTZZSmpJcGhQMndvVlcxUUoxcEd4WUFzU0prWjE1UDg3aHhKdEN5dXB6YTNHajBvQkh3Mkt3Y01YS1FXQ2dqNlpMSmpKd2t3YW1zdnZXcGRaRjd0Q1grdWhaYXA2TTdxMU1xVXhFSVFITFF5aDlDVjcySDJLWWFIelllaVNjVlFpbkExSDFFY2Uwb056S25J&sversion=3.7.5&refer=1_1000000_1234_1234&gwversion=1.5.4\",\"showToolBar\":true}";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SHOW_WEB_DIALOG, json, null);
        } else if (i == R.id.btn156) {
            String json = "{ \"url\":\"http://37.com.cn/community/vertical/54/home?gid=1000000&pid=1&dev=c4c6f0a8e946b44a82f2b5e732a1d195&token=BASE64MTFkNmMxa0IwNDVRdG5LL1dudXhBNUc0bkpuaEJqWXV1SkZtR3FLL2cwMVVrZFYzMVJPNGJVK3I0TDdabVV4dCsvT2FjamF1T21JUTZZSmpJcGhQMndvVlcxUUoxcEd4WUFzU0prWjE1UDg3aHhKdEN5dXB6YTNHajBvQkh3Mkt3Y01YS1FXQ2dqNlpMSmpKd2t3YW1zdnZXcGRaRjd0Q1grdWhaYXA2TTdxMU1xVXhFSVFITFF5aDlDVjcySDJLWWFIelllaVNjVlFpbkExSDFFY2Uwb056S25J&sversion=3.7.5&refer=1_1000000_1234_1234&gwversion=1.5.6\",\"showToolBar\":false}";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SHOW_WEB_DIALOG, json, null);

        } else if (i == R.id.showPersonalBtn) {/**
         * 打开实名制弹窗
         */
            SQwanCore.getInstance().showSQPersonalDialog(MainActivity.this);

        }  else if (i == R.id.share_img) {
            //1，构建分享源
            ShareImageInfo shareImageInfo = new ShareImageInfo();
            shareImageInfo.setBitmap(readBitmap(R.drawable.sy37_splash_port));
            //2，设置分享bean
            ShareMessage shareMessage = new ShareMessage();
            //2.1设置分享源
            shareMessage.setShareMessage(shareImageInfo);
            //2.2设置是否跳过分享预览页面(默认不跳过)
            //(如果为false则下面的分享平台设置失效)
            //(如果为true，则必须通过调用setPlatform来设置分享平台)
            shareMessage.setSkipPreview(!previewBox.isChecked());
            //2.3设置分享平台
            shareMessage.setPlatform(SharePlatform.WECHAT);

            SQwanCore.getInstance().share(shareMessage, mShareListener);
        } else if (i == R.id.btn_system_share_image) {
            ShareImageInfo shareImageInfo = new ShareImageInfo();
            shareImageInfo.setBitmap(readBitmap(R.drawable.sy37_splash_land));
            //2，设置分享bean
            ShareMessage shareMessage = new ShareMessage();
            //2.1设置分享源
            shareMessage.setShareMessage(shareImageInfo);
            //2.2设置是否跳过分享预览页面(默认不跳过)
            //(如果为false则下面的分享平台设置失效)
            //(如果为true，则必须通过调用setPlatform来设置分享平台)
            shareMessage.setSkipPreview(!previewBox.isChecked());
            //2.3设置分享平台
            shareMessage.setPlatform(SharePlatform.SYSTEM);
            SQwanCore.getInstance().share(shareMessage, mShareListener);
        } else if (i == R.id.btn_system_share_link) {
            ShareWebInfo shareWebInfo = new ShareWebInfo();
            shareWebInfo.setThumbBmp(readBitmap(R.drawable.sy37_splash_land));
            shareWebInfo.setTitle("测试标题");
            shareWebInfo.setPageUrl("http://www.baidu.com");
            ShareMessage shareMessage = new ShareMessage();
            shareMessage.setSkipPreview(true);
            shareMessage.setPlatform(SharePlatform.SYSTEM);
            shareMessage.setShareMessage(shareWebInfo);
            SQwanCore.getInstance().share(shareMessage, mShareListener);
        } else if (i == R.id.btn_system_share_text) {
            ShareTextInfo shareTextInfo = new ShareTextInfo();
            shareTextInfo.setText("我是被分享的文本");
            ShareMessage shareMessage = new ShareMessage();
            shareMessage.setSkipPreview(true);
            shareMessage.setPlatform(SharePlatform.SYSTEM);
            shareMessage.setShareMessage(shareTextInfo);
            SQwanCore.getInstance().share(shareMessage, mShareListener);
        } else if (i == R.id.share_url) {
            String url = shareEdt.getText().toString();
            if (TextUtils.isEmpty(url)) {
                Toast.makeText(this, "请输入分享链接再点击分享", Toast.LENGTH_SHORT).show();
                return;
            }
            ShareWebInfo info = new ShareWebInfo();
            info.setDesc(shareDesEdt.getText().toString());
            info.setPageUrl(url);
            info.setTitle(shareTitleEdt.getText().toString());
            info.setThumbBmp(readBitmap(R.drawable.sy37_splash_port));
            ShareMessage shareMessage = new ShareMessage();
            shareMessage.setShareMessage(info);
            shareMessage.setSkipPreview(!previewBox.isChecked());
            shareMessage.setPlatform(SharePlatform.WECHAT);
            SQwanCore.getInstance().share(shareMessage, mShareListener);

        } else if (i == R.id.add_share) {
            imgs.add("");
            shareAdapter.setData(imgs);
            setHeight();
        } else if (i == R.id.btn_ua) {
            SQwanCore.getInstance().showUAgreement(this);

        } else if (i == R.id.wx_auth) {
            SocialAccountUtil.socialBindAuthorize(this, new IBindWxListener() {
                @Override
                public void onSuccess(String authInfo) {
                    ToastUtil.showToast(MainActivity.this, "onSuccess " + authInfo);
                }

                @Override
                public void onFailure(int code, String msg) {
                    ToastUtil.showToast(MainActivity.this, "onFailure " + msg);
                }
            });

        } else if (i == R.id.age_appropriate) {
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_AGE_APPROPRIATE_ICON, null, new SQResultListener() {
                @Override
                public void onSuccess(Bundle bundle) {
                    String iconUrl = bundle.getString(PerformFeatureKey.KEY_AGE_APPROPRIATE_ICON);
                    LogUtil.i("适龄提醒url:" + iconUrl);
                    showAgeAppropriate(iconUrl);
                }

                @Override
                public void onFailture(int code, String msg) {
                    LogUtil.i("获取适龄提醒icon失败，msg: " + msg);
                    ivAgeAppropriate.setVisibility(View.GONE);
                    ToastUtil.showToast(MainActivity.this, "获取适龄提醒图标url失败，" + msg);
                }
            });
        } else if (i == R.id.cp_track_add) {
            addEdt();
        } else if (i == R.id.cp_track_report) {
            reportCpTrack();
        } else if(i==R.id.btn_open_url){
            SQwanCore.getInstance().showSQWebDialog(etUrl.getText().toString());
        } else if(i == R.id.show_ad) {
            String json = "{\n"
                + "    \"advertise_id\": \"960079766\",\n"
                + "    \"dsid\": \"服务器id\",\n"
                + "    \"dsname\": \"服务器名\",\n"
                + "    \"drid\": \"角色id\",\n"
                + "    \"drname\": \"角色名\"\n"
                + "}";
            SQwanCore.getInstance().showAdReward(this, json, new SQResultListener() {
                @Override
                public void onSuccess(Bundle bundle) {

                }

                @Override
                public void onFailture(int code, String msg) {

                }
            });
        } else if (i == R.id.scan_code) {
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SCAN_LOGIN, null, null);
        } else if (i == R.id.show_support_scan) {
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SUPPORT_SCAN_LOGIN, null,
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        boolean isSupport = bundle.getBoolean("show_scan_login");
                        ToastUtil.showToast("是否展示扫码按钮 : " + isSupport);
                    }

                    @Override
                    public void onFailture(int code, String msg) {

                    }
                });
        }else if (i == R.id.show_comment) {
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SHOW_GOOD_REVIEW, null,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            // code==1为用户取消好评跳转
                            // code==2为用户关闭好评跳转弹窗
                            // code==3为其他异常情况
                            ToastUtil.showToast("code:" + code + " ;msg:" + msg);
                        }
                    });
        }else if(i == R.id.show_third_list) {
            String json = "{ \"type\":\"show_third_list\" }";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_POLICY, json,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            ToastUtil.showToast("个人信息第三方共享清单打开成功");
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            ToastUtil.showToast("code:" + code + " ;msg:" + msg);
                        }
                    });
        }else if(i == R.id.show_personal_list) {
            String json = "{ \"type\":\"show_personal_list\" }";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_POLICY, json,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            ToastUtil.showToast("个人信息收集与使用清单打开成功");
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            ToastUtil.showToast("code:" + code + " ;msg:" + msg);
                        }
                    });
        }else if(i == R.id.get_record_number) {
            String json = "{ \"type\":\"get_record_number\" }";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_POLICY, json,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            String record_no = bundle.getString("record_no");
                            ToastUtil.showToast("获取备案号成功:" + record_no);
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            ToastUtil.showToast("code:" + code + " ;msg:" + msg);
                        }
                    });
        }else if(i == R.id.show_miit) {
            String json = "{ \"type\":\"show_miit\" }";
            SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_POLICY, json,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            ToastUtil.showToast("打开工信部url成功");
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            ToastUtil.showToast("code:" + code + " ;msg:" + msg);
                        }
                    });
        }

    }

    private void addEdt() {
        View layout = LayoutInflater.from(this).inflate(R.layout.sy37_demo_cp_filed, null);
        EditText key = layout.findViewById(R.id.cp_track_key);
        EditText value = layout.findViewById(R.id.cp_track_value);
        trackLayout.addView(layout);
        keyEdts.add(key);
        valueEdts.add(value);
    }

    private void reportCpTrack() {
        HashMap<String, String> fileds = new HashMap<>();
        for (int i = 0; i < keyEdts.size(); i++) {
            fileds.put(keyEdts.get(i).getText().toString(), valueEdts.get(i).getText().toString());
        }
        SQwanCore.getInstance().track(trackEventEdt.getText().toString(), "Demo 测试 CP事件", fileds);
    }

    private void showAgeAppropriate(String iconUrl) {
        if (TextUtils.isEmpty(iconUrl)) {
            ivAgeAppropriate.setVisibility(View.GONE);
            ToastUtil.showToast(MainActivity.this, "适龄提醒图标url为空");
        } else {
            ivAgeAppropriate.setVisibility(View.VISIBLE);
            AsyncImageLoader loader = new AsyncImageLoader(MainActivity.this);
            loader.loadDrawable(iconUrl, ivAgeAppropriate, new AsyncImageLoader.ImageCallback() {
                @Override
                public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                    imageView.setImageBitmap(imageDrawable);
                }
            });
            ivAgeAppropriate.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_SHOW_AGE_APPROPRIATE, null, null);
                }
            });
        }
    }

    private Bitmap readBitmap(int resId) {
        BitmapFactory.Options opt = new BitmapFactory.Options();
        opt.inPreferredConfig = Bitmap.Config.RGB_565;
        opt.inPurgeable = true;
        opt.inInputShareable = true;
        InputStream is = this.getResources().openRawResource(resId);
        return BitmapFactory.decodeStream(is, null, opt);
    }

    public void setHeight() {
        int height = 0;
        int count = shareAdapter.getCount();
        for (int i = 0; i < count; i++) {
            View temp = shareAdapter.getView(i, null, lvShare);
            temp.measure(0, 0);
            height += temp.getMeasuredHeight();
        }
        ViewGroup.LayoutParams params = lvShare.getLayoutParams();
        params.width = ViewGroup.LayoutParams.MATCH_PARENT;
        params.height = height;
        lvShare.setLayoutParams(params);
    }


    private OrientationEventListener mOrientationListener;

    private Object mLock = new Object();
    private Bitmap mScreenCaptureBitmap;
    private Handler mHandler = new Handler();

    /**
     * 模拟游戏中异步耗时的截图操作
     */
    private void testGameScreenCapture() {

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 模拟1s完成截图工作
                mScreenCaptureBitmap = ScreenCaptureUtils.captureScreen(MainActivity.this);
                // 解锁，唤醒线程
                synchronized (mLock) {
                    mLock.notifyAll();
                }
            }
        }, 1000);
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            SQwanCore.getInstance().showExitDailog(MainActivity.this,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            Toast.makeText(MainActivity.this,
                                    "登出完成，请处理游戏逻辑(例如清理资源、退出游戏等)",
                                    Toast.LENGTH_LONG).show();
                            // 在此做真正退出游戏逻辑，此处是模拟退出
                            System.exit(0);
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this,
                                    "取消登出，则不做退出处理，继续游戏", Toast.LENGTH_LONG)
                                    .show();
                        }
                    });
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SQwanCore.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.e("vivo_config", "onConfigurationChanged  isLandScape-->> " + (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE));
        int rotation = ((WindowManager) getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getRotation();
        Log.e("vivo_config", "rotation is " + rotation);
        SQwanCore.getInstance().onConfigurationChanged(newConfig);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        notchConfigOnWindowFocusChanged(hasFocus);
        super.onWindowFocusChanged(hasFocus);
        SQwanCore.getInstance().onWindowFocusChanged(hasFocus);

    }

    @Override
    public Resources getResources() {
        return SQwanCore.getInstance().getResources(super.getResources());
    }

    @Override
    public AssetManager getAssets() {
        return SQwanCore.getInstance().getAssets(super.getAssets());
    }

    @Override
    public ClassLoader getClassLoader() {
        return SQwanCore.getInstance().getClassLoader(super.getClassLoader());
    }

    @Override
    public void startActivity(Intent intent) {
        SQwanCore.getInstance().startActivity(intent);
        super.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        SQwanCore.getInstance().startActivityForResult(intent, requestCode);
        super.startActivityForResult(intent, requestCode);
    }

    public void url1(View view) {
//		String url = "http://37.com.cn/community/vertical/52/home";
//		WebDialog webDialog=new WebDialog();
//		webDialog.setUrl(url);
//		webDialog.setShowToolBar(true);
//		String json="{ \"url\":\"http://37.com.cn/community/vertical/54/home\",\"showToolBar\":false}";
        String json = "{ \"url\":\"http://37.com.cn/community/vertical/54/home\",\"showToolBar\":true}";

        SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_SHOW_WEB_DIALOG, json, null);

    }

    public void url2(View view) {
        startActivity(new Intent(this, WebDemoActivity.class));
    }

    private final SQResultListener mShareListener = new SQResultListener() {
        @Override
        public void onSuccess(Bundle bundle) {
            Toast.makeText(MainActivity.this,
                    "分享成功",
                    Toast.LENGTH_LONG).show();
            Log.i(TAG, "分享回调成功onSuccess: ");
            LogUtil.i("分享onSuccess");
        }

        @Override
        public void onFailture(int code, String msg) {
            Toast.makeText(MainActivity.this,
                    "分享失败",
                    Toast.LENGTH_LONG).show();
            Log.i(TAG, "分享回调失败onFailture: "+msg);
            LogUtil.i("分享onFailture");
        }
    };

    private void notchConfigOnWindowFocusChanged(boolean hasFocus) {
        if (hasFocus) {
//			NotchTools.getFullScreenTools().fullScreenDontUseStatusForOnWindowFocusChanged(this);
//			NotchTools.getFullScreenTools().fullScreenUseStatusForOnWindowFocusChanged(this);

        }
    }

    private void notchConfigOnCreate() {
//		NotchTools.getFullScreenTools().fullScreenDontUseStatusForActivityOnCreate(this);
//		NotchTools.getFullScreenTools().fullScreenUseStatusForActivityOnCreate(this, this);
    }

    @Override
    public void onNotchPropertyCallback(NotchProperty notchProperty) {
        int marginTop = notchProperty.getMarginTop();
    }


    public void test(View view) {
        setSdkInit();
    }

    public void leaveRoom(View view) {
        SQwanCore.getInstance().leaveLiveshowRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                Log.i(TAG, "leaveLiveshowRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
            }
        });
    }

    public void joinRoom(View view) {
        SQwanCore.getInstance().joinLiveshowRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                //开播成功
                Log.i(TAG, "joinRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(msg);
            }
        });
    }

    public void joinRadioRoom(View view) {
        // 进入电台
        SQwanCore.getInstance().joinLiveRadioRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                ToastUtil.showToast("进入电台成功");
                //开播成功
                Log.i(TAG, "joinRadioRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(msg);
            }
        });

        // 监听电台关闭
        SQwanCore.getInstance().setLiveRadioDestroyCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                // 电台关闭
                ToastUtil.showToast("监听到电台关闭了");
                Log.i(TAG, "setLiveshowDestroyCallback onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(msg);
            }
        });

        // 收听电台过程中监听电台声音变化
        SQwanCore.getInstance().setLiveRadioVoiceChangeCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                // 是否有播放声音
                boolean resume = bundle.getBoolean("isResume");
                ToastUtil.showToast("监听到电台声音变化了：" + resume);
                Log.i(TAG, "setLiveshowVoiceChangeCallback bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(msg);
            }
        });
    }

    public void leaveRadioRoom(View view) {
        SQwanCore.getInstance().leaveLiveRadioRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                ToastUtil.showToast("离开电台成功");
                Log.i(TAG, "leaveLiveRadioRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(msg);
            }
        });
    }

    private void handleAfterInited() {
        SQwanCore.getInstance().setLiveshowDestroyCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                //直播关闭
                Log.i(TAG, "setLiveshowDestroyCallback onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
        SQwanCore.getInstance().setLiveshowVoiceChangeCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                //是否有播放声音
                boolean isResume = bundle.getBoolean("isResume");
                Log.i(TAG, "setLiveshowVoiceChangeCallback bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        Log.d(TAG, "dispatchTouchEvent: ");
        if (ev.getAction()== MotionEvent.ACTION_UP) {
            handleScrrenBrightnessTask();
        }

        return super.dispatchTouchEvent(ev);
    }

    public void closeLiveshowVoice(View view) {
        Map<String,String> map = new HashMap<>();
        map.put("switchVoice","false");
        SQwanCore.getInstance().performLiveshowFeature(map, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                LogUtil.i(TAG,"closeLiveshowVoice");
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
    }

    public void openLiveshowVoice(View view) {
        Map<String,String> map = new HashMap<>();
        map.put("switchVoice","true");
        SQwanCore.getInstance().performLiveshowFeature(map, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                LogUtil.i(TAG,"openLiveshowVoice");
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
    }

    public void onShareDemo(View view) {
        startActivity(new Intent(this, ShareDemoActivity.class));
    }
}