def libraryVersion = get_libraryVersion()
def isMModuleDebug = get_isMModuleDebug()
def isMModuleRelease = get_isMModuleRelease()
def isPluginMode = get_isPluginMode()
def isNormalSdkAar = !isMModuleDebug&&!isMModuleRelease
def getCurrentTime = getCurrentTime()
println("isMModuleDebug:$isMModuleDebug isMModuleRelease:$isMModuleRelease isPluginMode:$isPluginMode sdkType:sq")
println("isNormalSdkAar $isNormalSdkAar isMModuleDebug:$isMModuleDebug isMModuleRelease:$isMModuleRelease isPluginMode:$isPluginMode sdkType:sq")
def isHyLiveShow = rootProject.ext.isHyLiveShow
if (isMModuleDebug || isMModuleRelease) {
    //生成apk
    apply plugin: 'com.android.application'
    apply from: 'custom-build.gradle'
} else {
    //生成aar
    apply plugin: 'com.android.library'
    apply from: 'multi-aar.gradle'
    apply plugin: "com.jfrog.artifactory"
    apply plugin: 'maven-publish'
    apply from: 'custom-build.gradle'
    apply plugin: 'com.kezong.fat-aar'
}

if (isPluginMode && isMModuleDebug) {
    apply from: 'sq-plugin-config.gradle'
}

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        //android 6.0(api 23) SDK以及以上，不再提供org.apache.http.*(只保留几个类).
        useLibrary 'org.apache.http.legacy'

        if (isMModuleDebug) {
            applicationId "com.sy.yxjun"

            // 编译apk需要指定个推参数
            manifestPlaceholders = [
                    // 个推app id
                    GETUI_APPID    : "xwdkpQLlfk8Azm2eErDqq9",
                    // 华为 相关应用参数
                    HUAWEI_APP_ID  : "",

                    // 小米相关应用参数
                    XIAOMI_APP_ID  : "",
                    XIAOMI_APP_KEY : "",

                    // OPPO 相关应用参数
                    OPPO_APP_KEY   : "",
                    OPPO_APP_SECRET: "",

                    // VIVO 相关应用参数
                    VIVO_APP_ID    : "",
                    VIVO_APP_KEY   : "",

                    // 魅族相关应用参数
                    MEIZU_APP_ID   : "",
                    MEIZU_APP_KEY  : "",

                    // 荣耀相关应用参数
                    HONOR_APP_ID   : "",
            ]
        }

        //sdk版本设定
        versionCode 24
        versionName libraryVersion




        multiDexEnabled true
    }

    // 配置结构
    sourceSets {
        main {

            //如果不是生成demo的话，不需要MainActivity类
            java {
                if (!isMModuleDebug) {
                    exclude 'com/sqwan/m/MainActivity.java'
                    exclude 'com/sqwan/m/WebDemoActivity.java'
                    exclude 'com/sqwan/m/ShareDemoActivity.java'
                }
            }

            if(isPluginMode) {
                assets.srcDirs = ['src/main/assets_plugin']
            } else {
                assets.srcDirs += "${rootProjectPath()}/module_liveshow/liveshowskin/assets/mdemo"
            }

            if(!isHyLiveShow){
                jniLibs.srcDirs =['libs']
            }
        }
    }

    //这个是解决lint报错的代码
    lintOptions {
        quiet true
        abortOnError false
        ignoreWarnings true
        checkReleaseBuilds false//方法过时警告的开关
        disable 'InvalidPackage' //Some libraries have issues with this.
        disable 'OldTargetApi' //Lint gives this warning but SDK 20 would be Android L Beta.
        disable 'IconDensities' //For testing purpose. This is safe to remove.
        disable 'IconMissingDensityFolder' //For testing purpose. This is safe to remove.
    }

    compileOptions {
        encoding "UTF-8"
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/dependencies.txt'
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/com.android.tools/proguard/coroutines.pro'
        //处理taptap的冲突问题
        exclude 'META-INF/kotlinx_coroutines_core.version'
        exclude 'kotlin/*.kotlin_metadata'
        exclude 'kotlin/**/*.kotlin_metadata'
        exclude 'kotlin/**/*.kotlin_builtins'
        // 排除 Kotlin 标准库和协程相关的模块文件
        exclude 'META-INF/*.kotlin_module'
        //账号体系宿主的配置
        if (isPluginMode) {
            exclude 'lib/armeabi-v7a/libalicomphonenumberauthsdk_core.so'
            exclude 'lib/armeabi-v7a/libpns-2.13.2.1-LogOnlineStandardCuumRelease_alijtca_plus.so'
            exclude 'lib/armeabi-v7a/libaliyunaf.so'
            exclude 'lib/armeabi-v7a/libdeviceid_607.so'
            exclude 'lib/armeabi-v7a/libsecuritydevice.so'
            exclude 'lib/armeabi-v7a/libtoyger.so'
            exclude 'lib/armeabi-v7a/libzkfv_ts_tj.so'
            exclude 'lib/armeabi-v7a/libentryexpro.so'

            exclude 'lib/arm64-v8a/libalicomphonenumberauthsdk_core.so'
            exclude 'lib/arm64-v8a/libpns-2.13.2.1-LogOnlineStandardCuumRelease_alijtca_plus.so'
            exclude 'lib/arm64-v8a/libaliyunaf.so.so'
            exclude 'lib/arm64-v8a/libdeviceid_607.so'
            exclude 'lib/arm64-v8a/libsecuritydevice.so'
            exclude 'lib/arm64-v8a/libtoyger.so'
            exclude 'lib/arm64-v8a/libzkfv_ts_tj.so'
            exclude 'lib/arm64-v8a/libentryexpro.so'

            exclude 'lib/armeabi/libaliyunaf.so.so'
            exclude 'lib/armeabi/libdeviceid_607.so'
            exclude 'lib/armeabi/libsecuritydevice.so'
            exclude 'lib/armeabi/libtoyger.so'
            exclude 'lib/armeabi/libzkfv_ts_tj.so'
            exclude 'lib/armeabi/libentryexpro.so'

            exclude 'lib/x86/libalicomphonenumberauthsdk_core.so'
            exclude 'lib/x86/libpns-2.13.2.1-LogOnlineStandardCuumRelease_alijtca_plus.so'
            exclude 'lib/x86/libaliyunaf.so.so'
            exclude 'lib/x86/libdeviceid_607.so'
            exclude 'lib/x86/libsecuritydevice.so'
            exclude 'lib/x86/libtoyger.so'
            exclude 'lib/x86/libzkfv_ts_tj.so'
            exclude 'lib/x86/libentryexpro.so'

        }

        // 解决遇到的 so 库冲突
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
    }

    /**
     * 签名设置
     */
    signingConfigs {
        release {
            File propFile = getSigningFile()
            if (propFile.exists()) {
                def Properties props = new Properties()
                props.load(new FileInputStream(propFile))
                if (props.containsKey('STORE_FILE') && props.containsKey('STORE_PASSWORD') &&
                        props.containsKey('KEY_ALIAS') &&
                        props.containsKey('KEY_PASSWORD')) {
                    storeFile file(props['STORE_FILE'])
                    storePassword props['STORE_PASSWORD']
                    keyAlias props['KEY_ALIAS']
                    keyPassword props['KEY_PASSWORD']
                }
            }
        }

        debug {
            //debug 版本签名使用android默认
            File propFile = getSigningFile()
            if (propFile.exists()) {
                def Properties props = new Properties()
                props.load(new FileInputStream(propFile))
                if (props.containsKey('STORE_FILE') && props.containsKey('STORE_PASSWORD') &&
                        props.containsKey('KEY_ALIAS') &&
                        props.containsKey('KEY_PASSWORD')) {
                    storeFile file(props['STORE_FILE'])
                    storePassword props['STORE_PASSWORD']
                    keyAlias props['KEY_ALIAS']
                    keyPassword props['KEY_PASSWORD']
                }
            }
        }
    }

    /**
     * 混淆设置
     */
    buildTypes {
        release {
            //执行proguard混淆
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_release.pro'
            //签名
            signingConfig signingConfigs.release
        }
        debug {
            //不执行proguard
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_release.pro'
            //签名
            signingConfig signingConfigs.debug
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    if (isMModuleDebug && !isPluginMode) {
        android.applicationVariants.all { variant ->
            //修改apk名称
            variant.outputs.all {
                def fileName = "sq_${variant.buildType.name}_${getCurrentTime}.apk"
                outputFileName = fileName
            }
        }
    }
}


//模块动态依赖
apply from: rootProjectPath() + '/moduleconfig/hy_liveshow_config.gradle'
apply from: rootProjectPath() + '/moduleconfig/hy_liveshow_so_config.gradle'
apply from: rootProjectPath() + '/moduleconfig/track.gradle'
apply from: rootProjectPath() + '/moduleconfig/yim_audio_config.gradle'

//构建非插件sdk
if(isNormalSdkAar){
    dependencies {
        embed project(':37sy_sdk_core_s')
    }
//    apply from: liveshow_embed_config() gradle plugin 版本太低，不支持，囧
//    apply from: s_embedconfig()
//    apply from: "${rootProject.getProjectDir().getAbsolutePath()}/gradleconfig/liveshowconfig/liveshow_config.gradle"
//    apply from: "${rootProject.getProjectDir().getAbsolutePath()}/gradleconfig/demo/normal/s_embedconfig.gradle"
    apply from: "${rootProject.getProjectDir().getAbsolutePath()}/gradleconfig/demo/normal/s_embedconfig.gradle"

    //模块动态依赖
    apply from: rootProjectPath() + '/moduleconfig/hy_liveshow_config.gradle'
    apply from: rootProjectPath() + '/moduleconfig/hy_liveshow_so_config.gradle'
    apply from: rootProjectPath() + '/moduleconfig/track.gradle'
    apply from: rootProjectPath() + '/moduleconfig/yim_audio_config.gradle'

    task buildSdk() {
        group 'custom'
        dependsOn 'assembleRelease'
        doLast{
            String aarSrcName = "${project.name}-release.aar"
            String aarSrcPath = "${project.buildDir}/outputs/aar/${aarSrcName}"
            String aarDstDir = "${rootProjectPath()}/archived/sdk/normal/"
            String aarDstName = "37sy_sdk_v${libraryVersion}_${getCurrentTime}.aar"
            println("aarSrc:${aarSrcPath}")
            println("aarDst:${aarDstDir}")
            delete(aarDstDir)
            copy {
                from(aarSrcPath)
                into(aarDstDir)
                rename(aarSrcName, aarDstName)
            }
        }
    }
}else{
    apply from: liveshow_config()
    dependencies {
        implementation project(':module_business:order')
        implementation project(':module_business:account')
        implementation project(':module_business:share')
        implementation project(':module_business:advertise')
        implementation project(':37sy_sdk_core_s')
        implementation project(':37sdkcommon')
        implementation project(':module_base:base_order')
        implementation project(':module_business:sdk_plugin')
        implementation(rootProject.ext.sqsdk.volly) {
            exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        }
        implementation rootProject.ext.sqsdk.bugless
        // 推送基础类, 移到宿主中
        implementation rootProject.ext.sqsdk.push_base
        // 推送实现类, 只在插件中
        implementation rootProject.ext.sqsdk.push
        implementation 'com.37sy.android:tool:2.0.5'
        implementation 'com.37sy.android:sqinject:1.0.0'
        implementation 'com.37sy.android:sqeventbus:1.0.0'
        implementation 'com.37sy.android:sqeventbus-annotation:1.0.0'
        implementation project(':module_plugin:plugin_standard')
        implementation project(':37SDKLibrarys')
        implementation project(':demo_base')
        if (isMModuleDebug) {
            // 个推依赖让研发主动引入, 所以插件和宿主都不需要
            // demo包带上个推依赖, 用于测试
            debugImplementation rootProject.ext.sqsdk.push_getui
        }
    }
    //构建demo
    if(!isPluginMode){
        task buildDemo() {
            group 'custom'
            dependsOn "assembleRelease"
            doFirst {
                println("buildDemo start")
            }
            doLast {
                String aarSrcPath = "${project.buildDir}/outputs/apk/release"
                String aarDstDir = "${rootProjectPath()}/archived/mdemo/sq"
                delete(aarDstDir)
                copy{
                    from(aarSrcPath){
                        include '*.apk'
                    }
                    into(aarDstDir)
                }
                println("buildDemo end")
            }
        }
    }
}

if (!isMModuleDebug) {
    publishing {
        publications {
            aar(MavenPublication) {
                groupId = rootProject.ext.artifactory_groupId
                artifactId "sySDKMultiLibrary"
                version = libraryVersion
                artifact "${project.buildDir}/outputs/aar/${project.name}-release.aar"
            }
        }
    }

    artifactory {
        contextUrl = rootProject.ext.artifactory_address
        publish {
            repository {
                repoKey = rootProject.ext.artifactory_repoKey
                username = rootProject.ext.artifactory_user
                password = rootProject.ext.artifactory_password
            }
            defaults {
                publishArtifacts = true
                publications('aar')
                publishPom = true //Publish generated POM files to Artifactory (true by default)
                publishIvy = true
                //Publish generated Ivy descriptor files to Artifactory (true by default)
            }
        }
    }
}

import com.sq.config.SignConfig
import com.sq.ResTool
import com.sq.config.Config
import com.sq.config.PropertiesUtils

if (isPluginMode && isMModuleDebug) {
    this.afterEvaluate {
        tasks['preBuild'].doFirst {
            println("清理缓存")
            delete(project.buildDir.absolutePath)
        }
        tasks['assembleRelease'].doLast {
            println("编译后的目录为: " + project.buildDir.absolutePath)
            String apkPath = project.buildDir.absolutePath + File.separator + "outputs" + File.separator + "apk" + File.separator + "release" + File.separator + project.name + "-release.apk"
            println("apk文件地址为: " + apkPath)
            String keystoreFilePath = project.rootProject.projectDir.absolutePath + File.separator + "BuildSystem" + File.separator + "37.keystore"
            SignConfig signConfig = new SignConfig("jarsigner", keystoreFilePath, "gz37wan", "37wan", "gz37wan")
            String tempApkPath = project.buildDir.absolutePath + File.separator + "temp" + File.separator
            File file = new File(tempApkPath)
            if (file.exists()) {
                file.delete()
            }
            file.mkdirs()
            String rootProjectPath = project.rootProject.projectDir.absolutePath
            String apktoolPath = rootProjectPath + File.separator + "apktool-2.7.0-fix.jar"
            String localPropertiesPath = rootProjectPath + File.separator + "local.properties"
            Properties localProperties = PropertiesUtils.getProperties(localPropertiesPath)
            String AndroidSdkDir = localProperties.get("sdk.dir")
            String zipAlignPath = AndroidSdkDir + File.separator + "build-tools/26.0.0/zipalign"
            Config config = new Config(apktoolPath, apkPath, tempApkPath, signConfig, zipAlignPath)
            println("aar config:${config.toString()}")
            // error: No resource identifier found for attribute 'keyboardNavigationCluster' in package 'android'
            // 如果这里报这个错，可以尝试执行一下这个命令后重试：java -jar apktool-2.7.0-fix.jar empty-framework-dir
            String resultApkPath = ResTool.handleResPkg(config)
            File resultFile = new File(resultApkPath)
            if (resultFile.exists()) {
                println("resultApkPath:${resultApkPath} size:${new File(resultApkPath).size()}")
                File resultDirFile = new File(project.buildDir.absolutePath + File.separator + "outputs" + File.separator + "plugin")
                if (resultDirFile.exists()) {
                    resultDirFile.deleteDir()
                }
                resultDirFile.mkdirs()
                def pluginApkName = "sq_plugin_${libraryVersion}_${getCurrentTime}.apk"
                copy {
                    from(resultApkPath)
                    into(resultDirFile.absolutePath)
                    rename(resultFile.name, pluginApkName)
                }
                file.deleteDir()
                println("插件生成成功，插件apk地址为: " + resultDirFile.absolutePath + File.separator + pluginApkName)
            } else {
                println("插件生成失败~")
            }
        }
    }

    task buildPlugin() {
        dependsOn "assembleRelease"
        group 'custom'
        doFirst {
            println("buildPlugin start")
        }
        doLast {
            String aarSrcPath = "${project.buildDir.getAbsolutePath()}/outputs/plugin"
            String aarDstDir = "${rootProjectPath()}/archived/plugin/sq"
            delete(aarDstDir)

            def pluginConfigPath = "${rootProjectPath()}/module_plugin/plugin_host/src/main/assets"
            def multilSdkConfigPath = new File(pluginConfigPath,"multi_sdk").getAbsolutePath()
            copy {
                from(aarSrcPath) {
                    include '*.apk'
                }
                into(aarDstDir)
            }
            copy {
                from(aarSrcPath) {
                    include '*.apk'
                }
                into(pluginConfigPath)
            }
            Properties properties = PropertiesUtils.getProperties(multilSdkConfigPath)
            String pluginName = new File(aarSrcPath).listFiles(new FileFilter() {
                @Override
                boolean accept(File pathname) {
                    pathname.getName().endsWith(".apk")
                    return true
                }
            })[0].getName().split(".apk")[0]
            println("buildPlugin pluginName:$pluginName")
            properties.setProperty("defaultPlugin",pluginName)
            def sqPluginVersion = sqPluginVersion()
            println("buildPlugin pluginVersion:$sqPluginVersion")
            properties.setProperty("defaultPluginVersion",sqPluginVersion)
            PropertiesUtils.save(properties, multilSdkConfigPath)
            println("buildPlugin end")

            copy {
                from(multilSdkConfigPath)
                into(aarDstDir)
            }

        }
    }
}

/***
 *  transformNativeLibsWithMergeJniLibsForDebug
 * */
tasks.whenTaskAdded { task ->
    if (task.name == 'mergeDebugNativeLibs'
            || task.name == 'transformNativeLibsWithMergeJniLibsForDebug') {
        task.doFirst {
            println("------------------- find so files start -------------------")
            it.inputs.files.each { file ->
                printDir(new File(file.absolutePath))
            }
            println("------------------- find so files end -------------------")
        }
    }
}

def printDir(File file) {
    if (file != null) {
        if (file.isDirectory()) {
            file.listFiles().each {
                printDir(it)
            }
        } else if (file.absolutePath.endsWith(".so")) {
            println "find so file: $file.absolutePath"
        }
    }
}

apply from: liveshow_so_config()

