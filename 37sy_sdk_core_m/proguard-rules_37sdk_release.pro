# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\ADT\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

#37sy_sdk_core_m混淆配置

-ignorewarnings
-dontoptimize
# 代码混淆压缩比，在0~7之间，默认为5,一般不下需要修改
-optimizationpasses 5

# 混淆时不使用大小写混合，混淆后的类名为小写
# windows下的同学还是加入这个选项吧(windows大小写不敏感)
-dontusemixedcaseclassnames

# 指定不去忽略非公共的库的类
# 默认跳过，有些情况下编写的代码与类库中的类在同一个包下，并且持有包中内容的引用，此时就需要加入此条声明
-dontskipnonpubliclibraryclasses

# 指定不去忽略非公共的库的类的成员
-dontskipnonpubliclibraryclassmembers

# 不做预检验，preverify是proguard的四个步骤之一
# Android不需要preverify，去掉这一步可以加快混淆速度
-dontpreverify

# 有了verbose这句话，混淆后就会生成映射文件
# 包含有类名->混淆后类名的映射关系
# 然后使用printmapping指定映射文件的名称
-verbose
-printmapping priguardMapping.txt

# 指定混淆时采用的算法，后面的参数是一个过滤器
# 这个过滤器是谷歌推荐的算法，一般不改变
-optimizations !code/simplification/artithmetic,!field/*,!class/merging/*

# 保护代码中的Annotation不被混淆
# 这在JSON实体映射时非常重要，比如fastJson
-keepattributes *Annotation*

# 避免混淆泛型
# 这在JSON实体映射时非常重要，比如fastJson
-keepattributes Signature

# 抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable

#混淆时不抛出警告
-dontwarn sun.misc.Unsafe,com.google.common.collect.MinMaxPriorityQueue,android.support.v4.**

# 保留所有的本地native方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

-keep class com.google.gson.** { *; }

# for 37shouyou release_m start
-keep public class com.sqwan.msdk.api.sdk._SQwan{*;}
-keep public class com.sqwan.msdk.api.sdk._SQwan$*{*;}

-keep public class com.sqwan.msdk.api.SQResultListener{*;}

-keep public class com.sqwan.msdk.SQwanCore{*;}
-keep public class com.sqwan.msdk.BaseSQwanCore{*;}
-keep public class com.sqwan.msdk.BaseSQReportCore{*;}
-keep public class com.sqwan.msdk.SQReportCore{*;}
-keep public class com.sqwan.msdk.ISQObservable{*;}
-keep public interface com.sqwan.msdk.ISQObserver{*;}
-keep public class com.sqwan.msdk.MDevObservable{*;}
-keep public class com.sqwan.msdk.api.sdk.Platform{*;}
-keep public class com.sqwan.msdk.api.sdk.Platform$*{*;}
-keep public class com.sqwan.msdk.api.PurchaseReportBean{*;}
-keep public class com.sqwan.msdk.api.RegisterReportBean{*;}
-keep public class com.sqwan.msdk.api.SQDefaultReporter{*;}
-keep public interface com.sqwan.msdk.api.SQReportInterface{*;}
-keep public class com.sqwan.msdk.api.SQDefaultMediaReport{*;}
-keep public interface com.sqwan.msdk.api.SQMediaReportInterface{*;}

-keep public class com.sqwan.msdk.api.SQAppConfig{*;}
-keep public class com.sqwan.msdk.receiver.** {*;}
-keep public class com.sqwan.afinal.** {*; }

-keep public class com.sqwan.msdk.api.InitBean{*;}
-keep public class com.sqwan.msdk.api.MRequestManager{*;}

-keep public class com.sqwan.msdk.api.MultiSDKUtils{*;}
-keep public class com.sqwan.msdk.utils.ZipString{*;}
-keep public class com.sqwan.msdk.http.RequestParams{*;}
-keep public class com.sqwan.msdk.utils.ShareUtil{*;}
-keep public class com.sqwan.msdk.utils.PayInfoUtil{*;}
-keep public class com.sqwan.msdk.share.wx.SQWXEntryActivity{*;}

-keep public interface com.sqwan.msdk.api.SQSdkInterface{*;}
-keep public interface com.sqwan.msdk.api.MRequestCallBack{*;}
-keep public interface com.sqwan.msdk.api.IMUrl{*;}

-keep class com.sqwan.msdk.views.** {*; }

-keep public interface com.sqwan.msdk.api.tool.**{*;}

# for 37shouyou release_m end


# for 37shouyou start--------
-keep class com.sy37sdk.*.** {*; }
-keep interface com.sy37sdk.*.**{*;}
-keep class com.social.*.**{*;}
-keep class com.sqwan.msdk.share.*.**{*;}
-keep class com.sy37sdk.otherlogin.** {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.AntiManager {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.views.SQAntiDialog {
    <fields>;
    <methods>;
}

-keep class com.tencent.mm.opensdk.** {
    *;
}

-keep class com.tencent.wxop.** {
    *;
}

-keep class com.tencent.mm.sdk.** {
    *;
}
-keep class com.sqwan.sdk.libs.** {
     *;
}
# for 37shouyou end--------
# for android-support-v4 start

-keep class android.support.** { *; }
-keep class android.support.v4.** { *; }
-keep public class android.support.v4.view.ViewPager{*;}
-keep interface android.support.v4.app.** { *; }
-keep public class * extends android.support.v4.**
-keep public class * extends android.app.Fragment

# for android-support-v4 end





# for ali pay start ---------------

-keep class com.alipay.**{*;}
-keep class org.json.**{*;}
-keep class com.ta.utdid2.**{*;}
-keep class com.json.alipay.**{*;}
-keep class com.ut.*

-keep class com.sy37sdk.alipay.** { *; }

-keep class android.util.** { *; }

-keep class com.tencent.mm.opensdk.** {
   *;
}

-keep class com.tencent.** {*;}
-keep class com.tencent.wxop.** {
   *;
}
-keep class com.tencent.mm.sdk.** {
   *;
}

# for ali pay end   ---------------

#-----------------------------------------------------------------------#
#----------------------------Defult block-------------------------------#
#-----------------------------------------------------------------------#

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService
-keep class * extends android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
-keepclasseswithmembers,allowshrinking class * {
    public <init>(android.content.Context,android.util.AttributeSet);
}
-keepclasseswithmembers,allowshrinking class * {
    public <init>(android.content.Context,android.util.AttributeSet,int);
}
-keepclassmembers enum  * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keepclasseswithmembers,allowshrinking class * {
    native <methods>;
}

-keep class com.sqwan.data.** { *; }
-keep class com.sdk.sq.net.** { *; }
-keep interface com.sdk.sq.net.** { *; }
-keep class com.sqnetwork.** { *; }
-keep interface com.sqnetwork.** { *; }
-keep class com.sqinject.*.** {*; }