{"nameMap": {}, "activityFilter": ["com.social.sdk.sso.wechat.WXCallbackActivity", "com.tencent.tauth.AuthActivity", "com.tencent.connect.common.AssistActivity", "com.social.sdk.sso.wechat.WXQRActivity", "com.sqwan.m.MainActivity", "com.huya.berry.sdkcamera.CameraEchoActivity", "com.huya.berry.sdkplayer.floats.view.PlayerActivity", "com.huya.berry.sdkplayer.floats.view.PortraitPlayerActivity", "com.huya.berry.webview.ImagePickerActivity", "com.huya.berry.webview.WebViewActivity"], "deleteClassList": ["com.plugin.standard.*", "com.sqwan.msdk.provider.SqFileProvider", "com.sqwan.msdk.api.PurchaseReportBean", "com.sqwan.msdk.api.RegisterReportBean", "com.sqwan.msdk.api.SQAppConfig", "com.sqwan.msdk.api.SQPushTransmitMessageListener", "com.sqwan.msdk.api.SQReportInterface", "com.sqwan.msdk.api.SQResultListener", "com.sqwan.msdk.api.SQSdkInterface", "com.sqwan.msdk.SQApplication", "com.sqwan.msdk.SQwanCore", "com.parameters.share.*", "com.parameters.performfeatureconfig.*", "com.sqwan.msdk.SQActivity"], "filterLocalJar": ["android-support-v4-26.0.0.jar", "wechat-sdk-android-without-mta-5.5.8.jar", "qqopensdk-3.53.0.jar", "libammsdk.jar", "push-base-1.1.0-alpha-runtime.jar", "social_sdk"], "applicationName": "", "receiverFilter": []}