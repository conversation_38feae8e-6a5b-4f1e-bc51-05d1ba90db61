> version.properties文件用来匹配OnekeyPlugin.jar版本<br>
onekeyplugin不能乱改名称，OnekeyPlugin.jar用到的路径

## 打插件
1. 修改env2.properties路径相关配置
2. 添加进程环境变量,执行jar

sq:
```
./gradlew --stop;cd onekeyplugin;export PARAM_SDK_USE_TYPE='sq';export PARAM_BUILD_SDK_VERSION="*******.1";java -classpath OnekeyPlugin.jar main.onekeyplugin.OnekeyPlugin;cd ..;
```

## 构建插件，然后构建资源
1. 修改env2.properties路径相关配置
2. 添加进程环境变量,执行jar

sq:
```
./gradlew --stop;cd onekeyplugin;export PARAM_SDK_USE_TYPE='sq';export PARAM_BUILD_SDK_VERSION="3.7.5";java -classpath PluginSdk.jar main.PluginSdk;cd ..;
```

## task构建
### sdk
##### psdk
sq:

```
isPluginHostApk=false
isMultiAAR=true
isPluginHostAar=true
./gradlew clean :module_plugin:plugin_host:buildSdk
```


##### msdk:
sq:

```
isMModuleRelease=false
isMModuleDebug=false
./gradlew clean :37sy_sdk_core_m:buildSdk -PPisMModuleRelease=false -PPisMModuleDebug=false
```

### demo
##### pdemo
sq:

```
sdkType='sq'
isPluginHostApk=false
isPluginHostAar=false
./gradlew clean :plugin_demo:buildDemo -PPisPluginHostApk=false -PPisPluginHostAar=false -PPsdkType=sq
```

##### mdemo
sq:

```
isPluginMode=false
isMModuleDebug=true
sdkType='sq'
isSQSkin=true
./gradlew clean :37sy_sdk_core_m:buildDemo -PPisMModuleDebug=true -PPisPluginMode=false -PPsdkType=sq PPisSQSkin=true
```

### plugin
sq:

```
isPluginMode=true
isMModuleDebug=true
isSQSkin=true
sdkType='sq'
./gradlew clean :37sy_sdk_core_m:buildPlugin -PPisMModuleDebug=true -PPisPluginMode=true -PPisSQSkin=true -PPsdkType=sq
```





