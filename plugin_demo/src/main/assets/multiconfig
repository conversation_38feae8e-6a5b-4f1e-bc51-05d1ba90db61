
isLandScape=0

debug=1

usesdk=0

#isPushDelay=1时强行开启push推送延迟，有时间限制。
#isPushDelay=0

#isSDK202=1时，登录成功后返回uid和uame/不做参数的验证。默认不开启
#isSDK202=1

#isSDK210=1时，不做支付参数的验证。默认不开启
#isSDK210=1

#useSQExit=0，关闭37自带的退出框
useSQExit=1

#isSplashShow=0，不显示闪屏。=1时显示，默认不显示，但是37渠道的强制打开了。
isSplashShow=0

#usePlatformExit=0，不使用渠道的退出框；=1时显示。但是37渠道的强制打开了。
usePlatformExit=0

#testTag 母包测试配置，=1 显示母包测试信息 =0 不显示
testTag=0

#是否开启截图测试，默认不开启，1：打开，其他值则不打开
isShowScreenshot=1

isShowSplashWhenSCut=0

#是否开启权限提示弹窗
isCheckPermissionPreview=1

#是否显示三七互娱logo
isSqUnion=1

#穿山甲app_id
chuanshanjia_app_id=5599625

#皮肤
skin=fx