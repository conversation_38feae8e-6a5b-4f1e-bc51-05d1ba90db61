<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.sy.plugindemo">

  <application
    android:screenOrientation="landscape"
    android:name="com.sq.plugin_demo.TestApplication"
    android:icon="@drawable/sy37_sdk_launcher"
    android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen"
    android:label="@string/demo_name">

    <activity
      android:name="com.sq.plugin_demo.MainActivity"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:screenOrientation="landscape">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    <activity
      android:name="com.sq.plugin_demo.ShareDemoActivity"
      android:exported="false"
      android:screenOrientation="landscape"
      android:theme="@style/ToygerAppTheme" />
  </application>
</manifest>