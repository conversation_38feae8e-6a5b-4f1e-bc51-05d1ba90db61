<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <Button
            android:id="@+id/login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="Sdk登录" />

        <Button
            android:id="@+id/change_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/login"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="Sdk切换账号" />

        <Button
            android:id="@+id/pay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/et_money"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="Sdk调用支付" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/change_account"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:onClick="submitRole"
            android:text="进服" />

        <Button
            android:id="@+id/role_report"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/pay"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="Sdk创建角色上报" />

        <Button
          android:id="@+id/scan_code"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_below="@id/role_report"
          android:layout_centerHorizontal="true"
          android:layout_marginTop="30dp"
          android:text="扫码登录" />

        <Button
          android:id="@+id/show_support_scan"
          android:layout_width="200dp"
          android:layout_height="wrap_content"
          android:text="是否可以展示扫码入口"
          android:textSize="12dp" />

        <Button
            android:id="@+id/app_config"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/role_report"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="获取配置文件信息" />

        <EditText
            android:id="@+id/et_input_url"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="http://37.com.cn/freestyle/sdk_Interface/"
            android:minWidth="300dp"
            android:maxWidth="300dp" />

        <Button
            android:id="@+id/open_external_web"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/app_config"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="15dp"
            android:text="打开网页" />

        <Button
            android:id="@+id/open_web_tool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/open_external_web"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="15dp"
            android:text="有工具栏网页" />

        <Button
            android:onClick="joinRoom"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="joinRoom"
            android:textSize="12dp" />
        <Button
            android:onClick="leaveRoom"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="leaveRoom"
            android:textSize="12dp" />

        <Button
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:onClick="joinRadioRoom"
            android:text="joinRadioRoom"
            android:textSize="12dp" />

        <Button
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:onClick="leaveRadioRoom"
            android:text="leaveRadioRoom"
            android:textSize="12dp" />

        <Button
            android:onClick="test"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="test"
            android:textSize="12dp" />

        <RelativeLayout
            android:id="@+id/rl_age_appropriate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/age_appropriate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30dp"
                android:text="适龄提醒" />

            <ImageView
                android:layout_marginLeft="20dp"
                android:layout_marginTop="30dp"
                android:id="@+id/iv_age_appropriate"
                android:layout_width="48dp"
                android:layout_height="62dp"
                android:layout_toRightOf="@+id/age_appropriate"
                android:src="@drawable/sy37_age_appropriate"
                android:visibility="gone" />
        </RelativeLayout>


        <Button
            android:id="@+id/quit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/rl_age_appropriate"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="退出游戏" />

        <Button
          android:id="@+id/show_ad"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_below="@id/rl_age_appropriate"
          android:layout_centerHorizontal="true"
          android:layout_marginTop="30dp"
          android:text="打开激励广告" />

        <Button
            android:id="@+id/btn_share_demo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/quit"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:onClick="onShareDemo"
            android:text="打开分享案例" />

        <Button
            android:id="@+id/btn_share_cp"
            android:layout_below="@id/quit"
            android:layout_marginTop="30dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="研发直接传图分享" />

        <EditText
            android:id="@+id/et_money"
            android:hint="请输入金额"
            android:text="0.01"
            android:layout_below="@id/change_account"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <EditText
                android:id="@+id/trackEdt"
                android:layout_weight="1"
                android:layout_margin="10dp"
                android:hint="事件名称"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                />

            <Button
                android:layout_weight="1"
                android:layout_margin="10dp"
                android:id="@+id/trackReport"
                android:text="立即上报"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/btn_share_cp"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <Button
                    android:id="@+id/add_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="20dp"
                    android:text="添加分享配置"
                    android:textColor="#121212"
                    android:textSize="18dp" />

                <CheckBox
                    android:id="@+id/share_is_preview"
                    android:text="是否预览"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

            </LinearLayout>

            <ListView
                android:id="@+id/lv_share"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/cp_track_layout"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <Button
                    android:id="@+id/cp_track_report"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:text="上报CP事件"
                    />

                <Button
                    android:id="@+id/cp_track_add"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:text="添加事件字段"
                    />

            </LinearLayout>

            <EditText
                android:id="@+id/cp_track_event"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:hint="CP事件名称"
                />

        </LinearLayout>
        <Button
            android:id="@+id/test"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/cp_track_layout"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:text="测试" />

    </LinearLayout>


</ScrollView>