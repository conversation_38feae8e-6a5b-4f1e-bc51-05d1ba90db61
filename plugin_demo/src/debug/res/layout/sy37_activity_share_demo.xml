<?xml version="1.0" encoding="utf-8"?>
<ScrollView  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:text="分享到微信或者朋友圈"
            android:textColor="#000000"
            android:textSize="16dp" />

        <EditText
            android:id="@+id/et_share_h5_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请编辑分享的内容"
            android:text="{\n  &quot;type&quot;: &quot;2&quot;,\n  &quot;way&quot;: &quot;wechat&quot;,\n  &quot;title&quot;: &quot;分享标题&quot;,\n  &quot;desc&quot;: &quot;这是一个副标题&quot;,\n  &quot;landingPageUrl&quot;: &quot;https://37.com.cn/m/&quot;,\n  &quot;img&quot;: &quot;https://37.com.cn/m/img/logo.95631587.png&quot;\n}" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <Button
                android:id="@+id/btn_share_h5_way_wechat"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享给微信好友"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_share_h5_way_h5_moment"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享给朋友圈"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_share_h5_way_h5_qq"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享给 QQ"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/btn_share_h5_type_picture"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享图片类型"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_share_h5_type_link"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享链接类型"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal" >

            <Button
                android:id="@+id/btn_share_h5_goto_h5"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="模拟 H5 调起分享"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_share_h5_goto_api"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="使用 API 调起分享"
                android:textSize="12dp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:background="#000000" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:text="分享到系统原生"
            android:textColor="#000000"
            android:textSize="16dp" />

        <EditText
            android:id="@+id/et_share_system_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请编辑分享的内容"
            android:text="{\n   &quot;shareType&quot;:  &quot;1&quot;,\n   &quot;shareTitle&quot;:  &quot;分享标题&quot;,\n   &quot;shareText&quot;:  &quot;这是一个副标题&quot;,\n   &quot;shareLinkUrl&quot;:  &quot;https://37.com.cn/m/ &quot;,\n   &quot;shareImageUrl&quot;:  &quot;https://37.com.cn/m/img/logo.95631587.png&quot;\n}" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <Button
                android:id="@+id/btn_share_system_type_picture"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享图片类型"
                android:textSize="11dp" />

            <Button
                android:id="@+id/btn_share_system_type_link"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享链接类型"
                android:textSize="11dp" />

            <Button
                android:id="@+id/btn_share_system_type_text"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="修改成分享文本类型"
                android:textSize="11dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal" >

            <Button
                android:id="@+id/btn_share_system_goto_h5"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="模拟 H5 调起分享"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_share_system_goto_api"
                android:layout_width="0px"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="使用 API 调起分享"
                android:textSize="12dp" />
        </LinearLayout>

    </LinearLayout>

</ScrollView>