package com.sq.plugin_demo;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Toast;

import com.demo.base.AppkeyHelper;
import com.demo.base.ScreenCaptureUtils;
import com.demo.base.ShareAdapter;
import com.demo.base.StatusBarUtil;
import com.demo.base.ToastUtil;
import com.parameters.performfeatureconfig.PerformFeatureKey;
import com.parameters.performfeatureconfig.PerformFeatureType;
import com.sqwan.msdk.SDKTrackUtils;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import com.sy.plugindemo.R;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MainActivity extends Activity {

    private String appkey = "";

    private ListView lvShare;

    private ShareAdapter shareAdapter;

    private List<String> imgs;

    private ImageView ivAgeAppropriate;
    private EditText trackEdt;
    private EditText etMoney;
    private CheckBox previewBox;
    private EditText inputUrl;

    private LinearLayout trackLayout;
    private EditText trackEventEdt;
    private List<EditText> keyEdts = new ArrayList<>();
    private List<EditText> valueEdts = new ArrayList<>();

    private static final String TAG = "MainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        appkey = AppkeyHelper.getAppkey(this);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.sy37_activity_demo);
        initView();
        ivAgeAppropriate = findViewById(R.id.iv_age_appropriate);
        trackLayout = findViewById(R.id.cp_track_layout);
        trackEventEdt = findViewById(R.id.cp_track_event);
        etMoney = findViewById(R.id.et_money);
        previewBox = findViewById(R.id.share_is_preview);
        inputUrl = findViewById(R.id.et_input_url);

        View decorView = getWindow().getDecorView();
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
            View.SYSTEM_UI_FLAG_FULLSCREEN |
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY |
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
        decorView.setSystemUiVisibility(uiOptions);

        SQwanCore.getInstance().setSwitchAccountListener(
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        ToastUtil.showToast(MainActivity.this, "悬浮窗切换账号成功：");
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        ToastUtil.showToast(MainActivity.this,
                            "悬浮窗切换账号失败:" + "\n msg=" + msg);
                    }
                });

        SQwanCore.getInstance().setBackToGameLoginListener(
                new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        // 1.CP重置游戏状态，并回到游戏的登录界面
                        // 2.在游戏登录界面，用户可以通过点击[开始游戏]按钮，调用changeAccount进行登录 或者
                        // CP主动调用changeAccount进行登录
                        // 3.切记，不要调用login
                        ToastUtil.showToast(MainActivity.this, "重置游戏状态，回到游戏的登录界面，用户需要重新登录");
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        // 不做处理。
                    }
                });

        SQwanCore.getInstance().init(MainActivity.this, appkey, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                Toast.makeText(MainActivity.this, "初始化完成",
                        Toast.LENGTH_SHORT).show();
                handleAfterInited();
            }

            @Override
            public void onFailture(int i, String s) {
                Toast.makeText(MainActivity.this, "初始化失败",
                        Toast.LENGTH_SHORT).show();
            }
        });

        SQwanCore.getInstance().setSQPushTransmitMessageListener(
            json -> Toast.makeText(MainActivity.this, "收到推送: " + json, Toast.LENGTH_LONG).show());
        findViewById(R.id.show_ad).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                String json = "{\"advertise_id\":\"960079766\"}";
                SQwanCore.getInstance().showAdReward(MainActivity.this, json, new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {

                    }

                    @Override
                    public void onFailture(int code, String msg) {

                    }
                });
            }
        });
        findViewById(R.id.login).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().login(MainActivity.this, new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        // CP拿到token后，需要通过服务器进行token验证，拿到对应的uid和uname
                        Toast.makeText(MainActivity.this, "登录成功：" + bundle, Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onFailture(int i, String msg) {
                        Toast.makeText(MainActivity.this, "登录失败回调：" + msg,
                                Toast.LENGTH_LONG).show();
                    }
                });
            }
        });

        findViewById(R.id.change_account).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().changeAccount(MainActivity.this, new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        // CP拿到token后，需要通过服务器进行token验证，拿到对应的uid和uname
                        Toast.makeText(MainActivity.this, "主动切换账号成功：" + bundle, Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        Toast.makeText(MainActivity.this, msg,
                                Toast.LENGTH_LONG).show();
                    }
                });
            }
        });

        findViewById(R.id.pay).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                float money = TextUtils.isEmpty(etMoney.getText().toString()) ? 1 : Float.parseFloat(etMoney.getText().toString());
                SQwanCore.getInstance().pay(MainActivity.this,
                        "A" + System.currentTimeMillis(), "一堆金币", "金币", "S001",
                        "铁马金戈", "CP扩展字段", "RID0001", "路人甲", 1, money, 10,
                        new SQResultListener() {
                            @Override
                            public void onSuccess(Bundle bundle) {
                                Toast.makeText(MainActivity.this,
                                                "成功发起充值请求(充值结果以服务端为准)", Toast.LENGTH_LONG)
                                        .show();
                            }

                            @Override
                            public void onFailture(int code, String msg) {
                                Toast.makeText(MainActivity.this, msg,
                                        Toast.LENGTH_LONG).show();
                            }
                        });
            }
        });

        findViewById(R.id.role_report).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final HashMap<String, String> infos1 = new HashMap<String, String>();
                infos1.put(SQwanCore.INFO_SERVERID, "yourServerId");
                infos1.put(SQwanCore.INFO_SERVERNAME, "yourServerName");
                infos1.put(SQwanCore.INFO_SERVERTIME, System.currentTimeMillis() + ""); //开服时间
                infos1.put(SQwanCore.INFO_ROLEID, (System.currentTimeMillis() / 1000) + "");
                infos1.put(SQwanCore.INFO_ROLENAME, "yourRoleName");
                infos1.put(SQwanCore.INFO_ROLELEVEL, "yourRoleLevel");
                infos1.put(SQwanCore.INFO_BALANCE, "yourBalance");
                infos1.put(SQwanCore.INFO_PARTYNAME, "yourPartyName");
                infos1.put(SQwanCore.INFO_VIPLEVEL, "yourVipLevel");
                infos1.put(SQwanCore.INFO_ROLE_TIME_CREATE, "" + 1458542706);// 从服务器获取的真实创建角色时间
                infos1.put(SQwanCore.INFO_ROLE_TIME_LEVEL, "-1");// 第一次创建，没有升级时间，传-1
                SQwanCore.getInstance().creatRoleInfo(infos1);
                Toast.makeText(getApplicationContext(), infos1.toString(),
                        Toast.LENGTH_SHORT).show();
            }
        });

        findViewById(R.id.app_config).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQAppConfig config = SQwanCore.getInstance().getAppConfig();
                String gid = config.getGameid();
                String pid = config.getPartner();
                String refer = config.getRefer();
                Toast.makeText(MainActivity.this,
                        "gid:" + gid + " \npid:" + pid + "\nrefer:" + refer,
                        Toast.LENGTH_LONG).show();
            }
        });

        findViewById(R.id.open_external_web).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().showSQWebDialog(inputUrl.getText().toString());
            }
        });

        findViewById(R.id.open_web_tool).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String url = inputUrl.getText().toString();
                String json = "{ \"url\":\"" + url + "\",\"showToolBar\":true}";
                SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_SHOW_WEB_DIALOG, json, null);
            }
        });

        findViewById(R.id.age_appropriate).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_AGE_APPROPRIATE_ICON, null, new SQResultListener() {
                    @Override
                    public void onSuccess(Bundle bundle) {
                        String iconUrl = bundle.getString(PerformFeatureKey.KEY_AGE_APPROPRIATE_ICON);
                        Log.i(TAG, "适龄提醒url:" + iconUrl);
                        if (!TextUtils.isEmpty(iconUrl)) {
                            ivAgeAppropriate.setVisibility(View.VISIBLE);
                            Toast.makeText(MainActivity.this, "适龄提醒url:" + iconUrl, Toast.LENGTH_SHORT).show();
                        } else {
                            ivAgeAppropriate.setVisibility(View.GONE);
                            Toast.makeText(MainActivity.this, "不需要配置适龄提醒", Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        Log.i(TAG, "获取适龄提醒icon失败，msg: " + msg);
                        ivAgeAppropriate.setVisibility(View.GONE);
                        Toast.makeText(MainActivity.this, "获取适龄提醒图标url失败，" + msg, Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });

        ivAgeAppropriate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_SHOW_AGE_APPROPRIATE, null, null);
            }
        });

        findViewById(R.id.quit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().showExitDailog(MainActivity.this,
                        new SQResultListener() {
                            @Override
                            public void onSuccess(Bundle bundle) {
                                // 离开电台
                                leaveRadioRoom(null);
                                Toast.makeText(MainActivity.this,
                                        "登出完成，请处理游戏逻辑(例如清理资源、退出游戏等)",
                                        Toast.LENGTH_LONG).show();
                                // 在此做真正退出游戏逻辑，此处是模拟退出
                                System.exit(0);
                            }

                            @Override
                            public void onFailture(int code, String msg) {
                                Toast.makeText(MainActivity.this,
                                                "取消登出，则不做退出处理，继续游戏", Toast.LENGTH_LONG)
                                        .show();
                            }
                        });
            }
        });

        //设置截图功能
        SQwanCore.getInstance().setScreenshotListener(new IScreenshotListener() {
            @Override
            public Bitmap createScreenshot() {
                //实现游戏中截图功能，将截取的图片转为bitmap格式返回
                // 以下是模拟游戏中，对截图线程加锁，然后待游戏引擎获取到游戏截图后，再解锁返回截取的图片
                // mLock是全局变量
                synchronized (mLock) {
                    // 测试游戏中异步截图，并且在截图完成后，将线程锁解锁，因为不是在UI线程，所以放心加锁
                    testGameScreenCapture();
                    try {
                        // 对线程加锁，此线程为非UI线程，可以放心加锁
                        mLock.wait();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                // mScreenCaptureBitmap是全局变量
                return mScreenCaptureBitmap;
            }
        });


        findViewById(R.id.btn_share_cp).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Gid 需要改成 1000000 才能分享成功
                SQwanCore.getInstance().share("7287", "", mShareListener);
            }
        });

        findViewById(R.id.test).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SDKTrackUtils.trackAction("test1", "describe1");
                HashMap<String, String> map = new HashMap<>();
                map.put("testkey1", "testvalue1");
                SDKTrackUtils.trackAction("test2", "describe2", map);
            }
        });


        findViewById(R.id.add_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                imgs.add("");
                shareAdapter.setData(imgs);
                setHeight();
            }
        });


        lvShare = findViewById(R.id.lv_share);

        imgs = new ArrayList<>();
        imgs.add("1608811521825");
        shareAdapter = new ShareAdapter(this, imgs);
        lvShare.setAdapter(shareAdapter);

        shareAdapter.setOnClickShareListener(new ShareAdapter.OnClickShareListener() {
            @Override
            public void clickShare(String imgId) {
                SQwanCore.getInstance().share("78278", imgId, mShareListener);
            }

            @Override
            public void clickDelete(int pos) {
                imgs.remove(pos);
                shareAdapter.setData(imgs);
            }
        });
        _setAuthResultListener();
        findViewById(R.id.cp_track_add).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addEdt();

            }
        });

        findViewById(R.id.cp_track_report).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                reportCpTrack();

            }
        });

        findViewById(R.id.scan_code).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_SCAN_LOGIN, null, null);
            }
        });
        findViewById(R.id.show_support_scan).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SQwanCore.getInstance().performFeature(MainActivity.this, PerformFeatureType.TYPE_SUPPORT_SCAN_LOGIN, null,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            boolean isSupport = bundle.getBoolean("show_scan_login");
                            ToastUtil.showToast(MainActivity.this,"是否展示扫码按钮 : " + isSupport);
                        }

                        @Override
                        public void onFailture(int code, String msg) {

                        }
                    });
            }
        });

    }

    private void addEdt() {
        View layout = LayoutInflater.from(this).inflate(R.layout.sy37_demo_cp_filed, null);
        EditText key = layout.findViewById(R.id.cp_track_key);
        EditText value = layout.findViewById(R.id.cp_track_value);
        trackLayout.addView(layout);
        keyEdts.add(key);
        valueEdts.add(value);
    }

    private void reportCpTrack() {
        HashMap<String, String> fileds = new HashMap<>();
        for (int i = 0; i < keyEdts.size(); i++) {
            fileds.put(keyEdts.get(i).getText().toString(), valueEdts.get(i).getText().toString());
        }
        SQwanCore.getInstance().track(trackEventEdt.getText().toString(), "Demo 测试 CP事件", fileds);
    }

    private void initView() {
        findViewById(R.id.trackReport).setOnClickListener(clickListener);
        trackEdt = findViewById(R.id.trackEdt);
    }

    private View.OnClickListener clickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v.getId() == R.id.trackReport) {
                SQwanCore.getInstance().track(trackEdt.getText().toString(), "测试事件", null);
            }
        }
    };

    private final SQResultListener mShareListener = new SQResultListener() {
        @Override
        public void onSuccess(Bundle bundle) {
            Toast.makeText(MainActivity.this,
                    "分享成功",
                    Toast.LENGTH_LONG).show();
            Log.i(TAG, "onSuccess: ");
        }

        @Override
        public void onFailture(int code, String msg) {
            Log.i(TAG, "onFailture: code" + code + " msg:" + msg);
        }
    };

    private Bitmap readBitmap(int resId) {
        BitmapFactory.Options opt = new BitmapFactory.Options();
        opt.inPreferredConfig = Bitmap.Config.RGB_565;
        opt.inPurgeable = true;
        opt.inInputShareable = true;
        InputStream is = this.getResources().openRawResource(resId);
        return BitmapFactory.decodeStream(is, null, opt);
    }

    @Override
    protected void onResume() {
        super.onResume();
        SQwanCore.getInstance().onResume();
        StatusBarUtil.hideSystemUI(getWindow());
    }

    @Override
    protected void onStart() {
        super.onStart();
        SQwanCore.getInstance().onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        SQwanCore.getInstance().onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SQwanCore.getInstance().onDestroy();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        SQwanCore.getInstance().onNewIntent(intent);
    }

    @Override
    protected void onPause() {
        super.onPause();
        SQwanCore.getInstance().onPause();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        SQwanCore.getInstance().onRestart();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SQwanCore.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SQwanCore.getInstance().onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            SQwanCore.getInstance().showExitDailog(MainActivity.this,
                    new SQResultListener() {
                        @Override
                        public void onSuccess(Bundle bundle) {
                            Toast.makeText(MainActivity.this,
                                    "登出完成，请处理游戏逻辑(例如清理资源、退出游戏等)",
                                    Toast.LENGTH_LONG).show();
                            // 在此做真正退出游戏逻辑，此处是模拟退出
                            System.exit(0);
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            Toast.makeText(MainActivity.this,
                                            "取消登出，则不做退出处理，继续游戏", Toast.LENGTH_LONG)
                                    .show();
                        }
                    });
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public Resources getResources() {
        return SQwanCore.getInstance().getResources(super.getResources());
    }

    @Override
    public ClassLoader getClassLoader() {
        return SQwanCore.getInstance().getClassLoader(super.getClassLoader());
    }

    @Override
    public void startActivity(Intent intent) {
        SQwanCore.getInstance().startActivity(intent);
        super.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        SQwanCore.getInstance().startActivityForResult(intent, requestCode);
        super.startActivityForResult(intent, requestCode);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        SQwanCore.getInstance().onWindowFocusChanged(hasFocus);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.e("Maintivity", "onConfigurationChanged");
        SQwanCore.getInstance().onConfigurationChanged(newConfig);
    }

    /**
     * 截图功能相关
     */
    private Object mLock = new Object();
    private Bitmap mScreenCaptureBitmap;
    private Handler mHandler = new Handler();

    /**
     * 模拟游戏中异步耗时的截图操作
     */
    private void testGameScreenCapture() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 模拟1s完成截图工作
                mScreenCaptureBitmap = ScreenCaptureUtils.captureScreen(MainActivity.this);
                // 解锁，唤醒线程
                synchronized (mLock) {
                    mLock.notifyAll();
                }
            }
        }, 1000);
    }


    public void setHeight() {
        int height = 0;
        int count = shareAdapter.getCount();
        for (int i = 0; i < count; i++) {
            View temp = shareAdapter.getView(i, null, lvShare);
            temp.measure(0, 0);
            height += temp.getMeasuredHeight();
        }
        ViewGroup.LayoutParams params = lvShare.getLayoutParams();
        params.width = ViewGroup.LayoutParams.MATCH_PARENT;
        params.height = height;
        lvShare.setLayoutParams(params);
    }

    public void submitRole(View view) {
        HashMap<String, String> infos2 = new HashMap<String, String>();
        infos2.put(SQwanCore.INFO_SERVERID, "99996");// 服务器id
        infos2.put(SQwanCore.INFO_SERVERNAME, "yourServerName");// 服务器名称
        infos2.put(SQwanCore.INFO_SERVERTIME, System.currentTimeMillis() / 1000 + "");  //开服时间
        infos2.put(SQwanCore.INFO_ROLEID, (System.currentTimeMillis() / 1000) + "");// 角色id
        infos2.put(SQwanCore.INFO_ROLENAME, "夶夶夶" + System.currentTimeMillis());// 角色名称
        infos2.put(SQwanCore.INFO_ROLELEVEL, "12");// 角色等级
        infos2.put(SQwanCore.INFO_BALANCE, "10");// 角色余额
        infos2.put(SQwanCore.INFO_PARTYNAME, "yourPartyName");// 工会名称
        infos2.put(SQwanCore.INFO_VIPLEVEL, "10");// vip等级
        SQwanCore.getInstance().submitRoleInfo(infos2);
        Toast.makeText(getApplicationContext(), infos2.toString(),
                Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        StatusBarUtil.hideSystemUI(getWindow());
        //隐藏导航栏
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                StatusBarUtil.hideSystemUI(getWindow());
            }
        });
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }
    }

    //政策相关
    public void _setAuthResultListener() {
        /**
         * 实名认证回调情景：
         * onSuccess：
         *  实名成功
         *
         * onFailture：
         * 游戏方需要处理：
         * 1、回调游戏进服界面
         * 2、调用退服接口
         */
        SQwanCore.getInstance().performFeature(this, PerformFeatureType.TYPE_AUTHRESULTCHECK, null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
            }

            @Override
            public void onFailture(int code, String msg) {
            }
        });
    }

    public void leaveRoom(View view) {
        Map<String, String> test = new HashMap<>();
        test.put("test", "test");
        SQwanCore.getInstance().leaveLiveshowRoom(test, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                ToastUtil.showToast(MainActivity.this, "leaveRoom onSuccess");
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, "leaveRoom onFailture");
            }
        });
    }

    public void joinRoom(View view) {
        Map<String, String> test = new HashMap<>();
        test.put("test", "test");
        SQwanCore.getInstance().joinLiveshowRoom(test, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
//                ToastUtil.showToast(MainActivity.this,"joinRoom onSuccess");
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, "joinRoom onFailture：" + msg);
            }
        });
    }

    private void handleAfterInited() {
        SQwanCore.getInstance().setLiveshowDestroyCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                //直播关闭
                Log.i(TAG, "setLiveshowDestroyCallback onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
        SQwanCore.getInstance().setLiveshowVoiceChangeCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                //是否有播放声音
                boolean isResume = bundle.getBoolean("isResume");
                Log.i(TAG, "setLiveshowVoiceChangeCallback bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {

            }
        });
    }

    public void joinRadioRoom(View view) {
        // 进入电台
        SQwanCore.getInstance().joinLiveRadioRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                ToastUtil.showToast(MainActivity.this, "进入电台成功");
                //开播成功
                Log.i(TAG, "joinRadioRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, msg);
            }
        });

        // 监听电台关闭
        SQwanCore.getInstance().setLiveRadioDestroyCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                // 电台关闭
                ToastUtil.showToast(MainActivity.this, "监听到电台关闭了");
                Log.i(TAG, "setLiveshowDestroyCallback onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, msg);
            }
        });

        // 收听电台过程中监听电台声音变化
        SQwanCore.getInstance().setLiveRadioVoiceChangeCallback(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                // 是否有播放声音
                boolean resume = bundle.getBoolean("isResume");
                ToastUtil.showToast(MainActivity.this, "监听到电台声音变化了：" + resume);
                Log.i(TAG, "setLiveshowVoiceChangeCallback bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, msg);
            }
        });
    }

    public void leaveRadioRoom(View view) {
        SQwanCore.getInstance().leaveLiveRadioRoom(null, new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                ToastUtil.showToast(MainActivity.this, "离开电台成功");
                Log.i(TAG, "leaveLiveRadioRoom onSuccess bundle " + bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                ToastUtil.showToast(MainActivity.this, msg);
            }
        });
    }

    public void test(View view) {
        String ACTION_DOWN_QUERY = "android.sq.down.query";
        Intent intent = new Intent();
        intent.setAction(ACTION_DOWN_QUERY);
        sendBroadcast(intent);
//        SQwanCore.getInstance().init(MainActivity.this, appkey, new SQResultListener() {
//            @Override
//            public void onSuccess(Bundle bundle) {
//                Toast.makeText(MainActivity.this, "初始化完成",
//                        Toast.LENGTH_SHORT).show();
//
//            }
//
//            @Override
//            public void onFailture(int i, String s) {
//                Toast.makeText(MainActivity.this, "初始化失败",
//                        Toast.LENGTH_SHORT).show();
//            }
//        });
    }

    public void onShareDemo(View view) {
        startActivity(new Intent(this, ShareDemoActivity.class));
    }
}
