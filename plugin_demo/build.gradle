apply plugin: 'com.android.application'

def libraryVersion = get_libraryVersion()
android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        //android 6.0(api 23) SDK以及以上，不再提供org.apache.http.*(只保留几个类).
        useLibrary 'org.apache.http.legacy'
        //sdk版本设定
        versionCode 24
        versionName libraryVersion
        applicationId "com.sy.yxjun"
    }

    // 配置结构
    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java']
            resources.srcDirs = ['src/main/resources']
            aidl.srcDirs = ['src/main/aidl']
            renderscript.srcDirs = ['src/maom']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDirs =['libs']
            assets.srcDirs += "${rootProjectPath()}/module_liveshow/liveshowskin/assets/mdemo"
        }
    }

    //这个是解决lint报错的代码
    lintOptions {
        quiet true
        abortOnError false
        ignoreWarnings true
        checkReleaseBuilds false//方法过时警告的开关
        disable 'InvalidPackage' //Some libraries have issues with this.
        disable 'OldTargetApi' //Lint gives this warning but SDK 20 would be Android L Beta.
        disable 'IconDensities' //For testing purpose. This is safe to remove.
        disable 'IconMissingDensityFolder' //For testing purpose. This is safe to remove.
    }

    compileOptions {
        encoding "UTF-8"
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        pickFirst 'lib/armeabi/libstlport_shared.so'
        pickFirst 'lib/armeabi-v7a/libstlport_shared.so'
        pickFirst 'lib/arm64-v8a/libstlport_shared.so'
        exclude 'META-INF/DEPENDENCIES.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/dependencies.txt'
        exclude 'META-INF/LGPL2.1'
    }

    /**
     * 签名设置
     */
    signingConfigs {
        release {
            File propFile = getSigningFile()
            if (propFile.exists()) {
                def Properties props = new Properties()
                props.load(new FileInputStream(propFile))
                if (props.containsKey('STORE_FILE') && props.containsKey('STORE_PASSWORD') &&
                        props.containsKey('KEY_ALIAS') &&
                        props.containsKey('KEY_PASSWORD')) {
                    storeFile file(props['STORE_FILE'])
                    storePassword props['STORE_PASSWORD']
                    keyAlias props['KEY_ALIAS']
                    keyPassword props['KEY_PASSWORD']
                }
            }
        }

        debug {
            //debug 版本签名使用android默认
            File propFile = getSigningFile()
            if (propFile.exists()) {
                def Properties props = new Properties()
                props.load(new FileInputStream(propFile))
                if (props.containsKey('STORE_FILE') && props.containsKey('STORE_PASSWORD') &&
                        props.containsKey('KEY_ALIAS') &&
                        props.containsKey('KEY_PASSWORD')) {
                    storeFile file(props['STORE_FILE'])
                    storePassword props['STORE_PASSWORD']
                    keyAlias props['KEY_ALIAS']
                    keyPassword props['KEY_PASSWORD']
                }
            }
        }
    }

    /**
     * 混淆设置
     */
    buildTypes {
        release {
            //执行proguard混淆
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources false
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_release.pro'
            //签名
            signingConfig signingConfigs.release
        }
        debug {
            //不执行proguard
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources false
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_release.pro'
            //签名
            signingConfig signingConfigs.debug


            // 指定个推参数
            manifestPlaceholders = [
                    // 个推app id
                    GETUI_APPID    : "xwdkpQLlfk8Azm2eErDqq9",
                    // 华为 相关应用参数
                    HUAWEI_APP_ID  : "",

                    // 小米相关应用参数
                    XIAOMI_APP_ID  : "",
                    XIAOMI_APP_KEY : "",

                    // OPPO 相关应用参数
                    OPPO_APP_KEY   : "",
                    OPPO_APP_SECRET: "",

                    // VIVO 相关应用参数
                    VIVO_APP_ID    : "",
                    VIVO_APP_KEY   : "",

                    // 魅族相关应用参数
                    MEIZU_APP_ID   : "",
                    MEIZU_APP_KEY  : "",

                    // 荣耀相关应用参数
                    HONOR_APP_ID   : "",
            ]
        }
    }
    android.applicationVariants.all { variant ->
        //修改apk名称
        variant.outputs.all {
            def fileName = "sq_demo_${variant.buildType.name}_${getCurrentTime()}.apk"
            outputFileName = fileName
        }
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    api project(':module_plugin:plugin_host')
    debugImplementation project(':demo_base')
    // demo带上个推, 方便测试
    debugImplementation(rootProject.ext.sqsdk.push_getui) {
        exclude group: 'com.android.support', module: 'support-annotations'
    }
}

task buildDemo() {
    group 'custom'
    dependsOn "assembleRelease"
    doFirst {
        println("buildDemo start")
    }
    doLast {
        String aarSrcPath = "${project.buildDir}/outputs/apk/release"
        String aarDstDir = "${rootProjectPath()}/archived/pdemo/sq"
        delete(aarDstDir)
        copy{
            from(aarSrcPath){
                include '*.apk'
            }
            into(aarDstDir)
        }
        println("buildDemo end")
    }
}
