package com.sqwan.liveshow.error;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-15 09:14
 */
public enum LiveshowResult {

    error_inited_youmemanager(10001,"YouMeManager init fail"),
    error_joinRoom(10002,"加入房间失败"),
    error_leaveRoom(10003,"离开房间失败"),
    error_inited_api(10004,"api init fail"),
    error_inited_reqeust(10005,"api request room fail"),
    error_inited_room_empty(10006,"error_inited_room_empty"),
    error_inited_not_submitrole(10007,"error_inited_not_submitrole"),
    error_inited_joining(10008,"error_inited_joining"),
    error_im_join(10009,"error_im_join"),
    error_im_login(10010,"error_im_login"),
    error_inited_sdkinit(10011,"error_inited_sdkinit"),
    error_sdkcode_support(10012,"直播功能不支持手机系统5.0以下"),
    error_cpu_supprot(10013,"直播功能不支持手机系统架构"),







    error_unknown(10000,"未知错误"),



    success_inited("初始化成功"),
    success_joinRoom("加入房间成功"),
    success_leaveRoom("离开房间成功"),
    success_voiceChange("频道状态切换"),
    success_joinRoomList("打开直播房间列表成功"),
    success_destroyRoom("直播销毁"),

    success_im_login("success_im_login"),
    success_im_join("success_im_login");



    private int error;
    private String msg;

    LiveshowResult(int error, String msg) {
        this.error = error;
        this.msg = msg;
    }

    LiveshowResult(String msg) {
        this.msg = msg;
    }

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "LiveshowResult{" +
                "error=" + error +
                ", msg='" + msg + '\'' +
                '}';
    }

}
