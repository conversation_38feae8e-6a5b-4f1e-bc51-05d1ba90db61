package com.sqwan.liveshow.trackaction;

import android.content.Context;

import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.mod.liveshow.ILiveshowTrackManager;
import com.sqwan.common.util.VersionUtil;

public class LiveshowTrackBaseManager extends BaseEnginHandler implements ILiveshowTrackManager {

    protected String mSdkVersion;

    protected Context mContext;

    protected  String TAG = this.getClass().getSimpleName();


    @Override
    public void init(Context _context) {
        super.init(_context);
        mContext = _context;
        mSdkVersion = VersionUtil.sdkVersion;
    }
    @Override
    public void init(BaseBean baseBean){

    }

    @Override
    public void initContext(Context context) {
        if (this.context==null) {
            init(context);
        }
    }

}
