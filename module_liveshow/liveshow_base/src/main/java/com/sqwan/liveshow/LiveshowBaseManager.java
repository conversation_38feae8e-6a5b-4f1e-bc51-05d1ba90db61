package com.sqwan.liveshow;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;

import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.common.LiveShowParamsKey;
import com.sqwan.liveshow.error.LiveshowResult;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;

import java.lang.ref.WeakReference;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-15 09:52
 */
public class LiveshowBaseManager extends BaseEnginHandler {
    public enum JoinRoomStatu{
        joined,unjoined,joining,liveshowlist
    }
    private ActivityLifeCycleUtils.AppVisibilityCallback innerAppVisibilityCallback;
    private ActivityLifeCycleUtils.AppVisibilityCallback visibilityCallback;
    public boolean isResume=true;
    public boolean isResumeLast=true;
    protected SQResultListener joinRoomListener,leaveRoomListener,destroyCallback,voiceChangeCallback;
    protected JoinRoomStatu joinRoomStatu=JoinRoomStatu.unjoined;
    protected WeakReference<Activity> activityWeakReferenceLiveShow;

    public boolean isJoined(){
        return joinRoomStatu==JoinRoomStatu.joined;
    }
    public Activity getLiveshowActivity(){
        if (activityWeakReferenceLiveShow!=null) {
            return activityWeakReferenceLiveShow.get();
        }
        return null;
    }

    public ActivityLifeCycleUtils.AppVisibilityCallback getVisibilityCallback() {
        return visibilityCallback;
    }

    public void setVisibilityCallback(ActivityLifeCycleUtils.AppVisibilityCallback visibilityCallback) {
        this.visibilityCallback = visibilityCallback;
    }

    public void initLiveshowActivity(Context _context){
        if (_context!=null && _context instanceof Activity && activityWeakReferenceLiveShow==null) {
            activityWeakReferenceLiveShow = new WeakReference<>((Activity)_context);
        }
    }
    protected void callbackInvokeSuccess(SQResultListener sqResultListener, Bundle data, LiveshowResult liveshowResult){
        callbackInvoke(true,sqResultListener,data, liveshowResult);

    }
    protected void callbackInvokeFail(SQResultListener sqResultListener, LiveshowResult liveshowResult){
        callbackInvoke(false,sqResultListener,null, liveshowResult);
    }
    protected void callbackInvoke(boolean isSuccess, SQResultListener sqResultListener, Bundle data, LiveshowResult liveshowResult){
            if (isSuccess) {
                if (data==null) {
                    data = new Bundle();
                }
                LogUtil.i(TAG,"callbackInvoke success bundle " + data.toString());
                if (liveshowResult !=null) {
                    LogUtil.i(TAG,"callbackInvoke success " + liveshowResult.toString());
                }
                if (sqResultListener!=null) {
                    sqResultListener.onSuccess(data);
                }
            }else{
                if (liveshowResult !=null) {
                    LogUtil.e(TAG,"callbackInvoke fail " + liveshowResult.toString());
                    if (sqResultListener!=null) {
                        sqResultListener.onFailture(liveshowResult.getError(), liveshowResult.getMsg());
                    }
                }
            }


    }
    public String getUsernick(){
        BaseBean baseBean = CommonConfigs.getInstance().getBaseUserInfo();
        if (baseBean!=null) {
            return baseBean.roleName;
        }
        return "";
    }
    public String getUserId() {
        return AccountCache.getUserid(context);
    }
    public String getUserName(){
        return AccountCache.getUsername(context);
    }
    public void resetData(){
    }
    protected void handleLeaveRoomSuccessCallback(){
        callbackInvokeSuccess(this.leaveRoomListener,null,LiveshowResult.success_leaveRoom);
        callbackInvokeSuccess(this.destroyCallback,null,LiveshowResult.success_destroyRoom);
        channelChangeInvokeCallback(false);
    }
    protected void channelChangeInvokeCallback(boolean resume){
        Bundle data = new Bundle();
        data.putBoolean(LiveShowParamsKey.isResume,resume);
        callbackInvokeSuccess(this.voiceChangeCallback,data,LiveshowResult.success_voiceChange);

    }
    protected void handleRepeatClickLiveshowIcon() {
        Task.post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showToast("请关闭当前直播间后再进入");
            }
        });
    }
    protected void initActivityLifeCycle(){
        isResume=true;
        if (innerAppVisibilityCallback ==null) {
            innerAppVisibilityCallback = new ActivityLifeCycleUtils.AppVisibilityCallbackAdapter(){
                @Override
                public void onBackground() {
                    if (isResume) {
                        isResumeLast=true;
                        LiveshowBaseManager.this.onBackground();
                    }
                }
                @Override
                public void onForeground() {
                    if (isResumeLast) {
                        isResumeLast=false;
                        LiveshowBaseManager.this.onForeground();
                    }

                }

                @Override
                public void onActivityStopped(Activity activity) {
                    LiveshowBaseManager.this.onActivityStopped(activity);
                }

                @Override
                public void onActivityStarted(Activity activity) {
                    LiveshowBaseManager.this.onActivityStarted(activity);
                }
            };
        }
        ActivityLifeCycleUtils.getInstance().registerActivityListener(innerAppVisibilityCallback);
    }
    protected void uninitActivityLifeCycle(){
        if (innerAppVisibilityCallback !=null) {
            ActivityLifeCycleUtils.getInstance().unRegisterActivityListener(innerAppVisibilityCallback);
        }
    }
    protected void onBackground(){
        if (visibilityCallback!=null) {
            visibilityCallback.onBackground();
        }
        LogUtil.i(TAG,"onBackground");

    }
    protected void onForeground(){
        if (visibilityCallback!=null) {
            visibilityCallback.onForeground();
        }
        LogUtil.i(TAG,"onForeground");
    }
    protected void onActivityStopped(Activity activity){
        LogUtil.i(TAG,"onActivityStopped");
        if (visibilityCallback!=null) {
            visibilityCallback.onActivityStopped(activity);
        }
    }
    protected void onActivityStarted(Activity activity){
        LogUtil.i(TAG,"onActivityStarted");
        if (visibilityCallback!=null) {
            visibilityCallback.onActivityStarted(activity);
        }
    }

}
