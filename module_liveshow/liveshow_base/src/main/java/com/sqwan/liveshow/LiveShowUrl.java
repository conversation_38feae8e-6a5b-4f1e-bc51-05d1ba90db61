package com.sqwan.liveshow;


import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.msdk.config.MultiSdkManager;

public class LiveShowUrl {

    private static String LIVE_ROOM_HOST = "https://live-api" + MultiSdkManager.SECURE_SUFFIX + "37.com.cn";

    //激活接口下发放的
    public static String rlapi = "";
    //激活接口下发放的
    public static String rlcapi = "";

    public static final String KEY_RADIO_CONFIG = "android_radio_config";

    @UrlUpdate(value = KEY_RADIO_CONFIG, xValue = "x_radio_radio_config")
    public static String radio_config = LIVE_ROOM_HOST + "/api/live-service/v1/radio/radio_config";

}