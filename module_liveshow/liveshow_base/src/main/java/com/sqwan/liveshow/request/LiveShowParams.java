package com.sqwan.liveshow.request;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.request.CommonParamsV2;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.account.AccountCache;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/1
 */
public class LiveShowParams extends CommonParamsV2 {

    protected final static String DRID = "drid";
    protected final static String DSID = "dsid";
    protected final static String TOKEN = "token";

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        params = super.transform(params);
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();
        BaseBean baseBean = CommonConfigs.getInstance().getBaseUserInfo();
        if (baseBean != null) {
            params.put(DRID, baseBean.roleId);
            params.put(DSID, baseBean.serverId);
        }
        params.put(TOKEN, AccountCache.getToken(context));
        return params;
    }
}
