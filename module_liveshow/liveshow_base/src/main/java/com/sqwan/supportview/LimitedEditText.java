package com.sqwan.supportview;

import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.widget.EditText;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-07 14:28
 */
public class LimitedEditText extends EditText {
    private int maxCharacters;
    private TextWatcher textWatcher;
    public LimitedEditText(Context context) {
        this(context,null);
    }

    public LimitedEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
//        textWatcher = new TextWatcherEx(this);
//        addTextChangedListener(textWatcher);
    }
    public void setNumberFilter(int number,TextWatcherAdapter textWatcher){
        this.setFilters(new InputFilter[]{new ExceedInputFilter(number).setListener(new ExceedInputFilter.OnTextExceedListener() {
            @Override
            public void onTextExceed() {
                if (textWatcherEx!=null) {
                    textWatcherEx.maxCharactersCallback();
                }
            }
        })});
        addTextChangedListenerEx(textWatcher);
        addTextChangedListener(textWatcher);
    }
    private TextWatcherAdapter textWatcherEx;
    private void addTextChangedListenerEx(TextWatcherAdapter textWatcher){
        this.textWatcherEx = textWatcher;
    }

    public static class TextWatcherAdapter implements TextWatcher{

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            emptyChange(TextUtils.isEmpty(s));
        }

        public void maxCharactersCallback(){

        }
        public void emptyChange(Boolean isEmpty){

        }

    }



    public class TextWatcherEx implements TextWatcher {
        private LimitedEditText limitedEditText;
        public TextWatcherEx(LimitedEditText limitedEditText){
            this.limitedEditText = limitedEditText;
        }
        public TextWatcherEx(){
        }
        private String text = null;
        private int beforeCursorPosition = 0;
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            text = s.toString();
            beforeCursorPosition = start;
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            /* turning off listener */
            limitedEditText.removeTextChangedListener(this);


            if (limitedEditText.getLineCount() > limitedEditText.getMaxLines()) {
                limitedEditText.setText(text);
                limitedEditText.setSelection(beforeCursorPosition);
            }

            /* handling character limit exceed */
            if (s.toString().length() > limitedEditText.maxCharacters) {
                limitedEditText.setText(text);
                limitedEditText.setSelection(beforeCursorPosition);
                if (textWatcherEx!=null) {
                    textWatcherEx.maxCharactersCallback();
                }
            }

            limitedEditText.addTextChangedListener(this);
            if (textWatcherEx!=null) {
                textWatcherEx.afterTextChanged(s);
            }
        }
    }
    /**
     * @see
     */
    public static class ExceedInputFilter extends InputFilter.LengthFilter {

        private int max;
        private OnTextExceedListener listener;

        public ExceedInputFilter setListener(OnTextExceedListener listener) {
            this.listener = listener;
            return this;
        }

        public interface OnTextExceedListener {
            void onTextExceed();
        }

        public ExceedInputFilter(int max) {
            super(max);
            this.max = max;
        }

        @Override
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
            boolean notified = false;
            int keep = max - (dest.length() - (dend - dstart));
            if (keep <= 0) {
                // 1：当前字数已经是max那么多个了
                if (null != listener) {
                    notified = true;
                    listener.onTextExceed();
                }
            }
            CharSequence tmp = super.filter(source, start, end, dest, dstart, dend);
            if (null != tmp) {
                // 中文键盘及字典模式特殊适配
                String result = tmp.toString();
                // 这里的判断条件：
                // 首先，这里得是一次性输入了超过15个字符（中文的情况）
                // 那么情况1：此键盘是这样的的 “n'a't's（那天是）”
                // 在这种情况下，显然 n'a't's 够15字时不该触发，那么做下contains判断，筛选掉
                // 情况2：此键盘是这样的 “（撒都库哈斯能尽快大数据和开发商看）”
                // 即EditText里面没有内容，内容都在键盘上
                // 那么直接判断
                // <p>
                // 总之result.length() < (end - start) 能hold住所有情况
                // 即表示结果相比你输入在键盘里的内容被截取了。
                if (result.length() < (end - start)
                        && !result.contains("'")
                        && !notified) {
                    if (null != listener) {
                        listener.onTextExceed();
                    }
                }
            }
            return tmp;
        }
    }

}
