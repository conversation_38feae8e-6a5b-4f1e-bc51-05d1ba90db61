<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="chatRoomDialog" parent="android:Theme.Black.NoTitleBar">
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="chatInputDialog" parent="android:Theme.Black.NoTitleBar">
        <item name="android:windowFrame">@android:color/transparent</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item><!--半透明-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item><!--背景透明-->
        <item name="android:backgroundDimAmount">0.3</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>
</resources>