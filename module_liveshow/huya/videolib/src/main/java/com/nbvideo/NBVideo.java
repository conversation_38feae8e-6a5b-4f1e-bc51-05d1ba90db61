package com.nbvideo;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.Surface;
import android.view.TextureView;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;

/**
 * Created by znb on 2021-07-23
 */

public class NBVideo extends FrameLayout implements TextureView.SurfaceTextureListener,
        MediaPlayer.OnPreparedListener, MediaPlayer.OnVideoSizeChangedListener, MediaPlayer.OnCompletionListener,
        MediaPlayer.OnErrorListener, MediaPlayer.OnInfoListener, MediaPlayer.OnBufferingUpdateListener {
    private static final String TAG = "NBVideo";
    private Context context;
    private int currStatus = PlayerStatus.STATE_IDLE;
    private FrameLayout mContainer;
    private NBTextureView mTextureView;
    private MediaPlayer mMediaPlayer;
    private Surface mSurface;
    private SurfaceTexture mSurfaceTexture;
    private AudioManager mAudioManager;
    private FrameLayout textureViewLayout;
    private VideoCallback videoCallback;
    private VideoInfo videoInfo;

    public VideoInfo getVideoInfo() {
        return videoInfo;
    }

    public void setVideoInfo(VideoInfo videoInfo) {
        this.videoInfo = videoInfo;
    }

    public void setVideoCallback(VideoCallback videoCallback) {
        this.videoCallback = videoCallback;
    }

    public NBVideo(Context context) {
        this(context, null, 0);
    }

    public NBVideo(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NBVideo(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        initVideo();
    }

    private void initVideo() {
        if (context instanceof Activity) {
            Window w = ((Activity) context).getWindow();
            w.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);
        }

        mContainer = new FrameLayout(context);
        mContainer.setBackgroundColor(Color.BLACK);
        LayoutParams params = new LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        this.addView(mContainer, params);
        params.gravity = Gravity.CENTER;
        textureViewLayout = new FrameLayout(context);
        textureViewLayout.setBackgroundColor(Color.BLACK);
        mContainer.addView(textureViewLayout, params);

    }

    public interface  LoadingResultListener{
        void loadFail();
    }

    private boolean isLoaded = false;

    private Task loadTask = Task.create();

    public void start(final LoadingResultListener listener) {
        isLoaded = false;
        if (currStatus == PlayerStatus.STATE_IDLE) {
            if (VideoManager.getInstance().getCurrPlayVideo() != null) {
                VideoManager.getInstance().getCurrPlayVideo().pause();
                VideoManager.getInstance().getCurrPlayVideo().releasePlayer();
            }
            VideoManager.getInstance().setCurrPlayVideo(this);
            initAudioManager();
            initMediaPlayer();
            initTextureView();
            addTextureView();
        }

        if (currStatus == PlayerStatus.STATE_IDLE || currStatus == PlayerStatus.STATE_PREPARING){
            loadTask.oneShot(6 * 1000, new Task.TaskFunc() {
                @Override
                public Task.Result exec() {
                    if (!isLoaded){
                        LogUtil.d(TAG,"加载视频流失败，回调重新加载新 url");
                        if (listener != null){
                            listener.loadFail();
                        }
                    }
                    return null;
                }
            });
        }
    }

    public void changeUrlMediaAndReset(final LoadingResultListener listener){
        isLoaded = false;
        if (mMediaPlayer != null){
            mMediaPlayer.reset();
        }
        openMediaPlayer(mSurfaceTexture);
        loadTask.oneShot(6 * 1000, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                if (!isLoaded){
                    LogUtil.d(TAG,"加载视频流失败，回调重新加载新 url");
                    if (listener != null){
                        listener.loadFail();
                    }
                }
                return null;
            }
        });
    }

    public void restart() {
        LogUtil.d(TAG,"restart: " + currStatus);
        if (currStatus == PlayerStatus.STATE_PAUSED) {
            mMediaPlayer.start();
            currStatus = PlayerStatus.STATE_PLAYING;
        } else if (currStatus == PlayerStatus.STATE_BUFFERING_PAUSED) {
            mMediaPlayer.start();
            currStatus = PlayerStatus.STATE_BUFFERING_PLAYING;
//            handleLoadingCallback();
        } else if (currStatus == PlayerStatus.STATE_COMPLETED) {
            mMediaPlayer.reset();
            openMediaPlayer(mSurfaceTexture);
            currStatus = PlayerStatus.STATE_RESTART;
        } else if (currStatus == PlayerStatus.STATE_ERROR) {
            mMediaPlayer.reset();
            openMediaPlayer(mSurfaceTexture);
            currStatus = PlayerStatus.STATE_RESTART;
        } else {
            mMediaPlayer.reset();
            openMediaPlayer(mSurfaceTexture);
            currStatus = PlayerStatus.STATE_CHANGE_LINE;
        }
    }


    public void pause() {
        if (currStatus == PlayerStatus.STATE_PLAYING ||
                currStatus == PlayerStatus.STATE_PREPARED ||
                currStatus == PlayerStatus.STATE_COMPLETED) {
            if (mMediaPlayer.isPlaying()) {
                LogUtil.i(TAG, "pause: ");
                mMediaPlayer.pause();
            }
            currStatus = PlayerStatus.STATE_PAUSED;
        }
        if (currStatus == PlayerStatus.STATE_BUFFERING_PLAYING) {
            if (mMediaPlayer.isPlaying()) {
                LogUtil.i(TAG, "pause: ");
                mMediaPlayer.pause();
            }
            currStatus = PlayerStatus.STATE_BUFFERING_PAUSED;
        }
    }

    public void releasePlayer() {
        LogUtil.i(TAG, "releasePlayer: ");
        if (mAudioManager != null) {
            mAudioManager.abandonAudioFocus(null);
            mAudioManager = null;
        }

        if (mMediaPlayer != null) {
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        textureViewLayout.removeView(mTextureView);
        if (null != mSurface) {
            mSurface.release();
            mSurface = null;
        }
        if (null != mSurfaceTexture) {
            mSurfaceTexture.release();
            mSurfaceTexture = null;
        }
        currStatus = PlayerStatus.STATE_IDLE;
//        Runtime.getRuntime().gc();
    }

    private void initTextureView() {
        if (mTextureView == null) {
            mTextureView = new NBTextureView(context);
            mTextureView.setSurfaceTextureListener(this);
        }
    }
    private void addTextureView() {
        textureViewLayout.removeView(mTextureView);
        LayoutParams textureView_lp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, Gravity.CENTER);
        textureViewLayout.addView(mTextureView, textureView_lp);
    }

    private void initMediaPlayer() {
        if (mMediaPlayer == null) {
            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            mMediaPlayer.setScreenOnWhilePlaying(true);
            mMediaPlayer.setOnPreparedListener(this);
            mMediaPlayer.setOnVideoSizeChangedListener(this);
            mMediaPlayer.setOnCompletionListener(this);
            mMediaPlayer.setOnErrorListener(this);
            mMediaPlayer.setOnInfoListener(this);
            mMediaPlayer.setOnBufferingUpdateListener(this);
        }
    }

    private void initAudioManager() {
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) getContext().getSystemService(Context.AUDIO_SERVICE);
            mAudioManager.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN);
        }
    }

    private void openMediaPlayer(SurfaceTexture surface) {
        try {
            if (videoInfo!=null) {
                mMediaPlayer.setDataSource(context, Uri.parse(videoInfo.getUrl()), videoInfo.getHeaders());
            }
            mSurface = new Surface(surface);
            mMediaPlayer.setSurface(mSurface);
            mMediaPlayer.prepareAsync();
            currStatus = PlayerStatus.STATE_PREPARING;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public int getCurrStatus() {
        return currStatus;
    }

    public int getMaxVolume() {
        if (mAudioManager != null) {
            return mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        }
        return 0;
    }

    public void setVolume(int volume) {
        if (mAudioManager != null) {
            mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);
        }
    }

    public int getVolume() {
        if (mAudioManager != null) {
            return mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        }
        return 0;
    }


    @Override
    public void onBufferingUpdate(MediaPlayer mediaPlayer, int i) {
        LogUtil.i(TAG, "onBufferingUpdate: ");
    }

    @Override
    public void onCompletion(MediaPlayer mediaPlayer) {
        currStatus = PlayerStatus.STATE_COMPLETED;
        // 清除屏幕常亮
        mContainer.setKeepScreenOn(false);
    }

    @Override
    public boolean onError(MediaPlayer mp, int what, int extra) {
        LogUtil.w(TAG, "MediaPlayer onError: " + String.format("what %d extra %d", what, extra));
        if (currStatus != PlayerStatus.STATE_CHANGE_LINE) {
            currStatus = PlayerStatus.STATE_ERROR;
        }
        handleErrorCallback();
        return true;
    }

    @Override
    public boolean onInfo(MediaPlayer mediaPlayer, int what, int extra) {
        if (what == MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START) {
            // 播放器开始渲染
            currStatus = PlayerStatus.STATE_PLAYING;
            LogUtil.d(TAG, "onInfo ——> MEDIA_INFO_VIDEO_RENDERING_START：STATE_PLAYING");
            handleSuccessCallback();
        } else if (what == MediaPlayer.MEDIA_INFO_BUFFERING_START) {
            //MEDIA_INFO_BUFFERING_START
            // MediaPlayer暂时不播放，以缓冲更多的数据
            if (currStatus == PlayerStatus.STATE_PAUSED || currStatus == PlayerStatus.STATE_BUFFERING_PAUSED) {
                currStatus = PlayerStatus.STATE_BUFFERING_PAUSED;
                LogUtil.d(TAG, "onInfo ——> MEDIA_INFO_BUFFERING_START：STATE_BUFFERING_PAUSED");

            } else {
                currStatus = PlayerStatus.STATE_BUFFERING_PLAYING;
                LogUtil.d(TAG, "onInfo ——> MEDIA_INFO_BUFFERING_START：STATE_BUFFERING_PLAYING");
                handleErrorCallback();
            }
        } else if (what == MediaPlayer.MEDIA_INFO_BUFFERING_END) {
            //MediaPlayer在缓冲完后继续播放
            // 填充缓冲区后，MediaPlayer恢复播放/暂停
            if (currStatus == PlayerStatus.STATE_BUFFERING_PLAYING) {
                currStatus = PlayerStatus.STATE_PLAYING;
                LogUtil.d(TAG, "onInfo ——> MEDIA_INFO_BUFFERING_END： STATE_PLAYING");
                handlePlayingCallback();
            }
            if (currStatus == PlayerStatus.STATE_BUFFERING_PAUSED) {
                currStatus = PlayerStatus.STATE_PAUSED;
                LogUtil.d(TAG, "onInfo ——> MEDIA_INFO_BUFFERING_END： STATE_PAUSED");
            }
        } else if (what == MediaPlayer.MEDIA_INFO_NOT_SEEKABLE) {
            //媒体不支持Seek
            LogUtil.d(TAG, "视频不能seekTo，为直播视频");
        } else if(what == MediaPlayer.MEDIA_INFO_AUDIO_NOT_PLAYING || what == MediaPlayer.MEDIA_INFO_VIDEO_NOT_PLAYING){
            LogUtil.w(TAG,"NOT_PLAYING:"+what);
            handleErrorCallback();
        }
        else {
            LogUtil.d(TAG, "onInfo ——> what：" + what);
        }
        return true;
    }

    @Override
    public void onPrepared(MediaPlayer mediaPlayer) {
        LogUtil.d(TAG, "onPrepared: ");
        if (null != mediaPlayer) {
            currStatus = PlayerStatus.STATE_PREPARED;
            mediaPlayer.start();
            isLoaded = true;
        }
    }

    @Override
    public void onVideoSizeChanged(MediaPlayer mediaPlayer, int i, int i1) {
        LogUtil.i(TAG, "onVideoSizeChanged: ");
    }

    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surfaceTexture, int i, int i1) {
        LogUtil.i(TAG, "onSurfaceTextureAvailable: ");
        if (mSurfaceTexture == null) {
            mSurfaceTexture = surfaceTexture;
            openMediaPlayer(mSurfaceTexture);
        } else {
            mTextureView.setSurfaceTexture(mSurfaceTexture);
        }
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surfaceTexture, int i, int i1) {
        LogUtil.i(TAG, "onSurfaceTextureSizeChanged: ");
    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surfaceTexture) {
        LogUtil.i(TAG, "onSurfaceTextureDestroyed: ");
        return false;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surfaceTexture) {
    }

    private void handleSuccessCallback() {
        LogUtil.i(TAG, "handleSuccessCallback: ");
        if (videoCallback != null) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    videoCallback.onSuccess();
                }
            });

        }

    }

    private void handleErrorCallback() {
        LogUtil.e(TAG, "handleErrorCallback: ");
        if (videoCallback != null) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    videoCallback.onError();
                }
            });

        }
    }

    private void handlePlayingCallback() {
        LogUtil.i(TAG, "handlePlayingCallback: ");
        if (videoCallback != null) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    videoCallback.onPlaying();
                }
            });
        }
    }

    private void handleLoadingCallback() {
        LogUtil.e(TAG, "handleLoadingCallback: ");
        if (videoCallback != null) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    videoCallback.onLoading();
                }
            });

        }
    }

    public void setOnlySoundEnable(boolean enable) {
        if (enable) {
            textureViewLayout.removeView(mTextureView);
        } else {
            textureViewLayout.addView(mTextureView);
        }
    }

    public boolean isOnlySoundEnable() {
        return textureViewLayout.indexOfChild(mTextureView) == -1;
    }
}