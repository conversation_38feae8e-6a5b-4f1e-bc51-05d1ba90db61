package com.nbvideo;

/**
 * Created by znb on 2021-07-23
 */
public class VideoManager {
    private static VideoManager manager;
    private NBVideo video;
    public static VideoManager getInstance(){
        if (null==manager){
            manager=new VideoManager();
        }
        return manager;
    }
    public void setCurrPlayVideo(NBVideo video){
        this.video=video;
    }
    public NBVideo getCurrPlayVideo(){
        return video;
    }
    public void release(){
        if (video!=null) {
            video.releasePlayer();
            video=null;
        }
    }
}
