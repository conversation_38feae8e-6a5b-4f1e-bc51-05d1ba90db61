package com.nbvideo;

import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-26 12:11
 */
public class VideoInfo {
    public String disPlayName;
    private String url;
    private Map<String, String> headers;

    public VideoInfo(String url, Map<String, String> headers) {
        this.url = url;
        this.headers = headers;
    }

    public VideoInfo(String disPlayName, String url, Map<String, String> headerMap) {
        this(url,headerMap);
        this.disPlayName = disPlayName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
}
