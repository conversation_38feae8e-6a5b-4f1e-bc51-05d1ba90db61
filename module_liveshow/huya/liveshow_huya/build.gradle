apply plugin: 'com.android.library'
apply plugin: 'com.sq.sqinject'
apply from: libconfig()

android {

    // 支持 JDK 1.8
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(":module_liveshow:liveshow_base")
    api project(':module_liveshow:huya:berry_lib')
    api project(':module_liveshow:huya:videolib')
    implementation 'io.github.happylishang:antifake:1.5.0'
    api project(':module_liveshow:huya:ndkbitmap-armv7a')
    api project(':module_liveshow:huya:DanmakuFlameMaster')
    api project(':window')
}
