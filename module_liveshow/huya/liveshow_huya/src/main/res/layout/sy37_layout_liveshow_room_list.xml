<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout
        android:layout_width="640dp"
        android:layout_height="367dp"
        android:layout_gravity="center"
        android:background="@drawable/sy37_liveshow_bg_room_list">

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/iv_room_title"
            android:layout_width="32dp"
            android:layout_height="16.5dp"
            android:layout_marginStart="57dp"
            android:layout_marginTop="10.5dp"
            android:src="@drawable/sy37_liveshow_icon_live" />

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/iv_sy37_liveshow_icon_close"
            android:layout_width="36dp"
            android:layout_height="45dp"
            android:layout_alignParentEnd="true" />

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_room_left_tab"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            tools:listitem="@layout/sy37_tab_item_room_left"
            tools:itemCount="2"
            android:layout_alignBottom="@id/rv_room_list"
            android:layout_marginStart="17.5dp"
            android:layout_marginTop="36dp" />

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_room_list"
            android:layout_width="503.5dp"
            android:layout_height="match_parent"
            android:layout_marginStart="120dp"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="15dp"
            tools:layoutManager="android.support.v7.widget.GridLayoutManager"
            tools:listitem="@layout/sy37_item_liveshow_room"
            tools:spanCount="3" />

        <LinearLayout
            android:id="@+id/ll_empty_layout"
            android:layout_width="503.5dp"
            android:layout_height="match_parent"
            android:layout_marginStart="120dp"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <com.sqwan.liveshow.huya.skin.view.SkinImageView
                android:id="@+id/iv_sy37_liveshow_list_empty"
                android:layout_width="136dp"
                android:layout_height="155dp"
                android:src="@drawable/sy37_liveshow_list_empty" />

        </LinearLayout>

    </com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout>

</FrameLayout>