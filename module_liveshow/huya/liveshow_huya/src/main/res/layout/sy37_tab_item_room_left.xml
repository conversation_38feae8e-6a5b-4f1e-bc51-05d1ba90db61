<?xml version="1.0" encoding="utf-8"?>
<com.sqwan.liveshow.huya.skin.view.SkinLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="25dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginBottom="2dp"
    android:gravity="center_vertical"
    android:id="@+id/ll_sy37_liveshow_bg_left_tab"
    tools:background="@drawable/sy37_liveshow_bg_left_tab_selector">

    <ImageView
        android:id="@+id/live_show_menu_icon_iv"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:scaleType="center" />

    <com.sqwan.liveshow.huya.skin.view.SkinTextView
        android:id="@+id/live_show_menu_platform_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="13dp"
        android:maxLines="1"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="2dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="热门推荐"
        android:textColor="@color/sy37_tab_item_room_left_live_show_menu_platform_name_tv_color"
        android:textSize="12dp" />
</com.sqwan.liveshow.huya.skin.view.SkinLinearLayout>
