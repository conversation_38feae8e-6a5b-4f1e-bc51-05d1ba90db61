<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/ll_chat_input"
    android:orientation="vertical"
    tools:context=".activity.ChatInputActivity">

    <Button
        android:id="@+id/btnInputCover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@null"
        android:visibility="invisible" />

    <com.sqwan.liveshow.huya.danmu.view.InputView
        android:id="@+id/inputview"
        android:layout_width="match_parent"
        android:layout_height="40dp" />

</LinearLayout>