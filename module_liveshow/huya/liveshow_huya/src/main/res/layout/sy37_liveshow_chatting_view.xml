<?xml version="1.0" encoding="utf-8"?>
<com.sqwan.liveshow.huya.skin.view.SkinLinearLayout
    android:background="@drawable/sy37_liveshow_bg_chat_room"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_weight="1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
    <android.support.v7.widget.RecyclerView
        android:id="@+id/rv_im_chat_message"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="50dp"
         />
    <com.sqwan.liveshow.huya.skin.view.SkinImageView
        android:visibility="invisible"
        android:id="@+id/iv_im_check_new_message"
        android:src="@drawable/sy37_liveshow_bt_check_new_message"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    </RelativeLayout>

    <com.sqwan.liveshow.huya.danmu.view.InputAndCountDownView
        android:background="@drawable/sy37_liveshow_bg_input"
        android:id="@+id/ip_input_view"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginBottom="15dp"
        android:layout_width="match_parent"
        android:layout_height="28dp"/>

</com.sqwan.liveshow.huya.skin.view.SkinLinearLayout>
