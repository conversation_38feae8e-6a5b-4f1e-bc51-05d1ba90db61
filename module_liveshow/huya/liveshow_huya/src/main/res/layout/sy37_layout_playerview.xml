<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@android:color/black">
    <com.nbvideo.NBVideo
        android:id="@+id/playerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>


        <LinearLayout
            android:gravity="center"
            android:background="@android:color/black"
            android:id="@+id/ll_live_network_error"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sy37_live_network_error_desc"
                android:textColor="#B4B4B4" />

            <com.sqwan.liveshow.huya.skin.view.SkinImageView
                android:id="@+id/iv_sy37_liveshow_icon_refresh"
                android:layout_width="39dp"
                android:layout_height="33dp"
                android:layout_gravity="center"
                android:layout_marginTop="5.5dp"
                android:scaleType="fitXY"
                android:src="@drawable/sy37_liveshow_icon_refresh" />

        </LinearLayout>

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/iv_sy37_liveshow_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:src="@drawable/sy37_liveshow_loading"
            android:visibility="gone"
            tools:visibility="visible"/>
</FrameLayout>
