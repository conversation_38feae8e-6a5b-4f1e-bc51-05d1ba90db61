<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/layout_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.sqwan.liveshow.huya.view.LiveshowFloatCoverView
                android:id="@+id/video_playerCover"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:layout_height="300dp" />

            <FrameLayout
                android:id="@+id/ftControlCorverView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@id/video_playerCover"
                android:layout_alignTop="@id/video_playerCover"
                android:layout_alignRight="@id/video_playerCover"
                android:layout_alignBottom="@id/video_playerCover">

                <FrameLayout
                    android:id="@+id/ftchooseView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone">

                    <com.sqwan.liveshow.huya.danmu.view.ChooseView
                        android:id="@+id/chooseView"
                        android:layout_width="134dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="right"
                        tools:visibility="visible" />
                </FrameLayout>

            </FrameLayout>

        </RelativeLayout>

        <com.sqwan.liveshow.huya.danmu.view.ChattingRoomView
            android:id="@+id/chattingRoomView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <com.sqwan.liveshow.huya.danmu.view.GuideView
        android:id="@+id/layout_guide_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:visibility="invisible" />

</RelativeLayout>