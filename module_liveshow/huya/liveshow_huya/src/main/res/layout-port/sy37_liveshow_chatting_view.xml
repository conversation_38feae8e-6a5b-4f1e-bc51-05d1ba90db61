<?xml version="1.0" encoding="utf-8"?>
<com.sqwan.liveshow.huya.skin.view.SkinFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/sy37_liveshow_bg_chat_room"
    android:orientation="vertical">

    <com.sqwan.liveshow.huya.skin.view.SkinImageView
        android:id="@+id/iv_im_chat_title"
        android:layout_width="162dp"
        android:layout_height="30dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/sy37_liveshow_title_chat_room" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_im_chat_message"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:paddingTop="30dp"
            android:paddingBottom="50dp"
            tools:listitem="@layout/sy37_item_im_chat" />

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/iv_im_check_new_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="50dp"
            android:src="@drawable/sy37_liveshow_bt_check_new_message"
            android:visibility="visible" />
    </FrameLayout>

    <com.sqwan.liveshow.huya.skin.view.SkinFrameLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom"
        android:background="@drawable/sy37_liveshow_room_bottom_bg" >

        <com.sqwan.liveshow.huya.danmu.view.InputAndCountDownView
            android:id="@+id/ip_input_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@drawable/sy37_liveshow_bg_input"/>

    </com.sqwan.liveshow.huya.skin.view.SkinFrameLayout>

</com.sqwan.liveshow.huya.skin.view.SkinFrameLayout>