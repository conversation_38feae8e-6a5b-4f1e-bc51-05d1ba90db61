<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000" >

    <com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout
        android:layout_width="345dp"
        android:layout_height="616dp"
        android:layout_gravity="center"
        android:background="@drawable/sy37_liveshow_bg_room_list">

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/iv_sy37_liveshow_icon_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:padding="12dp"
            android:src="@drawable/sy37_liveshow_ic_room_list_close" />

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_room_left_tab"
            android:layout_width="match_parent"
            android:layout_height="36.5dp"
            android:layout_marginLeft="1dp"
            android:layout_marginTop="42dp"
            android:layout_marginRight="1dp"
            tools:layoutManager="android.support.v7.widget.GridLayoutManager"
            tools:listitem="@layout/sy37_tab_item_room_left"
            tools:spanCount="2" />

        <com.sqwan.liveshow.huya.skin.view.SkinImageView
            android:id="@+id/tv_room_top_tab"
            android:layout_width="wrap_content"
            android:layout_height="25dp"
            android:layout_below="@id/rv_room_left_tab"
            android:layout_marginStart="13.5dp"
            android:layout_marginTop="15.5dp"
            android:layout_marginBottom="15.5dp"
            android:adjustViewBounds="true"
            android:gravity="center"
            android:src="@drawable/sy37_liveshow_ic_top_tabitem_all" />

        <FrameLayout
            android:id="@+id/ll_room_list"
            android:layout_width="match_parent"
            android:layout_height="0px"
            android:layout_below="@id/tv_room_top_tab"
            android:layout_alignParentBottom="true" >

            <android.support.v7.widget.RecyclerView
                android:id="@+id/rv_room_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginRight="5dp"
                android:paddingBottom="1dp"
                tools:layoutManager="android.support.v7.widget.GridLayoutManager"
                tools:listitem="@layout/sy37_item_liveshow_room"
                tools:spanCount="2" />

            <View
                android:id="@+id/v_room_list_bottom_obscuration"
                android:layout_width="match_parent"
                android:layout_height="41dp"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="1dp" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_empty_layout"
            android:layout_width="match_parent"
            android:layout_height="273dp"
            android:layout_alignStart="@id/ll_room_list"
            android:layout_alignTop="@id/ll_room_list"
            android:layout_alignEnd="@id/ll_room_list"
            android:layout_alignBottom="@id/ll_room_list"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <com.sqwan.liveshow.huya.skin.view.SkinImageView
                android:id="@+id/iv_sy37_liveshow_list_empty"
                android:layout_width="170dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/sy37_liveshow_list_empty" />

        </LinearLayout>

    </com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout>

</FrameLayout>