<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <com.sqwan.liveshow.huya.skin.view.SkinTextView
        android:id="@+id/tv_item_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/sy37_item_choose_view_tv_item_text_color"
        android:textSize="13dp"
        tools:text="高清" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="3dp"
        android:background="#33FFFFFF"/>

</RelativeLayout>