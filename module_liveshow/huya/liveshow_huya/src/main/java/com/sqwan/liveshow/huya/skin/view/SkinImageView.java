package com.sqwan.liveshow.huya.skin.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.sqwan.liveshow.huya.skin.attr.SkinDrawableAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/10/20 11:37
 */
public class SkinImageView extends ImageView implements SkinViewInterface {
    private SkinDrawableAttr skinDrawableAttr;
    public SkinImageView(Context context) {
        this(context,null);
    }

    public SkinImageView(Context context,  AttributeSet attrs) {
        this(context, attrs,0);
    }

    public SkinImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        skinDrawableAttr = new SkinDrawableAttr(this,attrs);
    }
    @Override
    public void applySkin() {
        if (skinDrawableAttr!=null) {
            skinDrawableAttr.applySkin();
        }
    }

    @Override
    public void setImageResource(int resId) {
        if (skinDrawableAttr!=null) {
            skinDrawableAttr.setImageResource(resId);
        }
    }
    public void setBackground(String stringSelected, String stringUnSelected){
        if (skinDrawableAttr!=null) {
            skinDrawableAttr.setBackground(stringSelected,stringUnSelected);
        }
    }
}
