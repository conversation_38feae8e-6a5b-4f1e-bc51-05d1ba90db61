package com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory;

import android.content.res.Configuration;
import android.graphics.Color;

import com.sqwan.base.L;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.SkinHelper;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/22 16:03
 */
public class ImNotification extends AImMsg {

    @Override
    protected int getUserColor() {
        return SkinHelper.getColorValue(L.getActivity(), SqR.color.sy37_item_im_chat_tv_msg_im_notification_user_color,
                Color.parseColor(L.getActivity().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ? "#A0A0A0" : "#888BAB"));
    }

    @Override
    protected int getContentColor() {
        return SkinHelper.getColorValue(L.getActivity(),SqR.color.sy37_item_im_chat_tv_msg_im_notification_content_color, Color.parseColor(
                L.getActivity().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ? "#9d96f3" : "#60C657"));
    }

    @Override
    protected int getUserSize() {
        if (L.getActivity().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            return 12;
        }
        return 9;
    }

    @Override
    protected int getContentSize() {
        if (L.getActivity().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            return 12;
        }
        return 9;
    }
}