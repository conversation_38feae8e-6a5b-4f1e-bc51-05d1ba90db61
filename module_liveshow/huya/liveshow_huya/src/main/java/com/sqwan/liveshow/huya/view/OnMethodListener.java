package com.sqwan.liveshow.huya.view;

import android.view.View;

public interface OnMethodListener {
    void addVideoFragmentContainer(View liveshowFloatView,View view);

    void setAnchorInfo(String anchorPortraitUrl, String anchorNickname, String audienceCount);

    void setOnBehaviorListener(OnBehaviorListener onBehaviorListener);

    void switchSmallLiveContainer();

    void switchFullLiveContainer();

    void loading();

    void networkError();

    void loadSuccess();


}