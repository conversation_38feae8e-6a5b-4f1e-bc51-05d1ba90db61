package com.sqwan.liveshow.huya.view;

import static com.sqwan.liveshow.huya.LiveRoomDataManager.PLATFORM_HUYA;
import static com.sqwan.liveshow.huya.LiveRoomDataManager.PLATFPRM_RECOMMENDED;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.huya.berry.client.customui.model.LiveListInfo;
import com.sq.websocket_engine.NetworkConst;
import com.sqwan.common.dialog.LoadingExDialog;
import com.sqwan.common.util.CheckNetwork;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.huya.LiveRoomDataManager;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.adapter.RoomListAdapter;
import com.sqwan.liveshow.huya.adapter.RoomTabAdapter;
import com.sqwan.liveshow.huya.bean.LiveMenuBean;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sy37sdk.account.floatview.CheckSystemUiViewBase;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-09 12:59
 */
public class RoomListView extends CheckSystemUiViewBase implements LiveRoomDataManager.RequestListener {

    private Dialog customDialog;
    private Typeface mFromAsset;
    private Shader selectShader;
    private Shader unSelectShader;
    public RoomListView(Context context) {
        super(context);
        initView();
    }

    @Override
    public boolean isScreenOnType() {
        return true;
    }

    private static final String TAG = "RoomListView";
    private RecyclerView mRvLeftTab;
    private RoomListAdapter roomListAdapter;
    private final HashMap<Integer, List<LiveListInfo>> dataMap = new HashMap<>();
    private View mLlEmptyLayout;

    private int mPlatformId;

    public void show() {
        if (this.isAttachedToWindow()) {
            ViewUtils.show(this);
            update();
        }else{
            addView();
            StatusBarUtil.hideSystemUI(mContext);
        }
    }

    @Override
    public void initLayoutParams() {
        super.initLayoutParams();
        floatLayoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        floatLayoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;

    }

    private void initView() {
        inflate(mContext, SqResUtils.getLayoutId(getContext(), SqR.layout.sy37_layout_liveshow_room_list), this);
        initRecyclerView();
        initEmptyView();
        showInitLoading();
        initLeftTab();
        handleClick();
    }

    private void initRecyclerView() {
        RecyclerView rvRoomList = findViewById(SqResUtils.getId(getContext(), SqR.id.rv_room_list));
        rvRoomList.setItemAnimator(null);
        rvRoomList.setOverScrollMode(View.OVER_SCROLL_NEVER);
        RecyclerView.LayoutManager layoutManager = new GridLayoutManager(getContext(),
                mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ? 2 : 3);
        rvRoomList.setLayoutManager(layoutManager);

        roomListAdapter = new RoomListAdapter(mContext, liveListInfo -> {
            if (!CheckNetwork.getInstance().hasNetwork()) {
                ToastUtil.showToast(NetworkConst.tips_no_network);
                return;
            }
            if (CheckNetwork.getInstance().isWifi()) {
                watchLive(mPlatformId, liveListInfo);
                return;
            }
            if (customDialog == null) {
                customDialog = new Dialog(mContext, SqResUtils.getStyleId(mContext, SqR.style.CustomDialog));
                customDialog.setContentView(SqResUtils.getLayoutId(getContext(), SqR.layout.sy37_dialog_network_desc));
                customDialog.setCancelable(true);
                customDialog.setCanceledOnTouchOutside(true);
                StatusBarUtil.hideSystemUI(customDialog.getWindow());
                if (mContext instanceof Activity && ((Activity) mContext).isFinishing()) {
                    return;
                }
                View closeView = customDialog.findViewById(SqResUtils.getId(mContext, SqR.id.iv_sy37_liveshow_icon_close));
                // 注意横版的直播提示对话框才有关闭按钮
                if (closeView != null) {
                    closeView.setOnClickListener(v -> {
                        if (customDialog != null && customDialog.isShowing()) {
                            customDialog.dismiss();
                        }
                    });
                }

                customDialog.findViewById(SqResUtils.getId(mContext, SqR.id.iv_sy37_liveshow_dialog_cancel)).setOnClickListener(v -> {
                    if (customDialog != null && customDialog.isShowing()) {
                        customDialog.dismiss();
                    }
                });
                customDialog.findViewById(SqResUtils.getId(mContext, SqR.id.iv_sy37_liveshow_dialog_join)).setOnClickListener(v -> {
                    if (customDialog != null && customDialog.isShowing()) {
                        customDialog.dismiss();
                    }
                    watchLive(mPlatformId, liveListInfo);
                });
            }
            customDialog.show();
        });
        rvRoomList.setAdapter(roomListAdapter);

        int obscurationEndColor = SkinHelper.getColorValue(mContext, SqR.color.sy37_liveshow_room_list_bottom_obscuration_color_end, Color.TRANSPARENT);
        View bottomObscurationView = findViewById(SqResUtils.getId(getContext(), SqR.id.v_room_list_bottom_obscuration));
        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT
                && obscurationEndColor != Color.TRANSPARENT && bottomObscurationView != null) {
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setShape(GradientDrawable.RECTANGLE);
            gradientDrawable.setColors(new int[] {Color.TRANSPARENT, obscurationEndColor});
            gradientDrawable.setGradientRadius(-45);
            gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
            bottomObscurationView.setBackground(gradientDrawable);
        }
    }

    private void initEmptyView() {
        mLlEmptyLayout = findViewById(SqResUtils.getId(getContext(), SqR.id.ll_empty_layout));
    }


    private void showEmptyView() {
        mLlEmptyLayout.setVisibility(View.VISIBLE);
    }

    private void hideEmptyView() {
        mLlEmptyLayout.setVisibility(View.GONE);
    }

    LoadingExDialog initLoading = null;

    /**
     * 显示初始化请求的loading，黑色背景
     */
    private void showInitLoading() {
        Task.post(() -> {
            if (initLoading == null && getContext() != null) {
                initLoading = new LoadingExDialog(getContext());
                initLoading.show();
                initLoading.setMessage("");
            }
        });
    }

    private void hideInitLoading() {
        Task.post(() -> {
            if (initLoading != null && initLoading.isShowing() && getContext() != null) {
                initLoading.dismiss();
            }
        });
    }

    private void handleClick() {
        findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_icon_close)).setOnClickListener(v -> handleFinish());
    }

    private void watchLive(int platformID, LiveListInfo liveListInfo) {
        LiveshowManager.getInstance().watchLive(platformID, liveListInfo, null);
    }

    private void initListInfo(Map<Integer, List<LiveListInfo>> map, final int platformID) {
        hideInitLoading();

        List<LiveListInfo> data = map.get(platformID);

        roomListAdapter.setLiveListInfoData(data);
        this.dataMap.put(platformID, data);

        if (data == null || data.size() == 0) {
            showEmptyView();
        } else {
            hideEmptyView();
        }
    }

    private void initLeftTab() {
        mRvLeftTab = findViewById(SqResUtils.getId(getContext(), SqR.id.rv_room_left_tab));
        mRvLeftTab.setItemAnimator(null);
        mRvLeftTab.setOverScrollMode(View.OVER_SCROLL_NEVER);
        RecyclerView.LayoutManager layoutManager;
        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        } else {
            layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false);
        }
        mRvLeftTab.setLayoutManager(layoutManager);
        LiveRoomDataManager.getInstance().getLiveMenuData(this);
    }

    private void initLeftTabView(final LiveMenuBean liveMenuBean) {

        hideInitLoading();

        RoomTabAdapter roomTabAdapter = new RoomTabAdapter(getContext(),
                SqResUtils.getLayoutId(getContext(), SqR.layout.sy37_tab_item_room_left),
                liveMenuBean.getItems(), (roomViewHolder, position, isSelect) -> {
                    TextView platformName = roomViewHolder.findViewById(SqResUtils.getId(getContext(), SqR.id.live_show_menu_platform_name_tv));
                    ImageView platformIcon = roomViewHolder.findViewById(SqResUtils.getId(getContext(), SqR.id.live_show_menu_icon_iv));
                    platformIcon.setAdjustViewBounds(true);
                    Typeface typeface = getTypeface();
                    if (typeface != null) {
                        platformName.setTypeface(typeface);
                    }
                    Glide.with(getContext()).load(liveMenuBean.getItems().get(position).getIcon_url()).into(platformIcon);

                    Shader shader = null;
                    String platformNameText = liveMenuBean.getItems().get(position).getPlatform_name();
                    try {
                        if (isSelect) {
                            int colorValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_icon_iv_select_color,
                                    mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                            Color.parseColor("#334060") : Color.parseColor("#857659"));
                            ColorStateList colorStateList = ColorStateList.valueOf(colorValue);
                            platformIcon.setImageTintList(colorStateList);
                            if (selectShader == null) {
                                int colorStartValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_platform_name_tv_color_select_start,
                                        mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                                Color.parseColor("#354A6D") : Color.parseColor("#746750"));
                                int colorEndValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_platform_name_tv_color_select_end,
                                        mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                        Color.parseColor("#354A6D") : Color.parseColor("#857659"));
                                selectShader = new LinearGradient(0, 0, 0, platformName.getLineHeight(),
                                        colorStartValue,colorEndValue, Shader.TileMode.REPEAT);
                            }
                            shader = selectShader;
                        } else {
                            int colorValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_icon_iv_not_select_color,
                                    mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                            Color.parseColor("#64728b") : Color.parseColor("#474b76"));
                            ColorStateList colorStateList = ColorStateList.valueOf(colorValue);
                            platformIcon.setImageTintList(colorStateList);
                            if (unSelectShader == null) {
                                int colorStartValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_platform_name_tv_color_not_select_start,
                                        mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                                Color.parseColor("#6D7E97") : Color.parseColor("#474b76"));
                                int colorEndValue = SkinHelper.getColorValue(mContext, SqR.color.sy37_tab_item_room_left_live_show_menu_platform_name_tv_color_not_select_end,
                                        mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ?
                                                Color.parseColor("#6D7E97") : Color.parseColor("#4b507e"));
                                unSelectShader = new LinearGradient(0, 0, 0, platformName.getLineHeight(),
                                        colorStartValue, colorEndValue, Shader.TileMode.REPEAT);
                            }
                            shader = unSelectShader;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // 报错直接设置文字
                    if (shader != null) {
                        platformName.getPaint().setShader(shader);
                    }
                    platformName.setText(platformNameText);
                    LogUtil.i("onBindView: 当前的position:" + position + "\t 当前选中的:" + isSelect);

                },

                (roomViewHolder, position) -> {
                    LogUtil.i("onItemClick: 点击了:" + position);
                    int platformId = liveMenuBean.getItems().get(position).getPlatform_id();
                    mPlatformId = platformId;

                    List<LiveListInfo> data = dataMap.get(platformId);
                    roomListAdapter.setLiveListInfoData(data);

                    if (!LiveRoomDataManager.getInstance().isHasplatformData(platformId)) {
                        loadAnchorMessage(platformId, liveMenuBean.getItems().get(position).getWhitelist());
                    } else {
                        List<LiveListInfo> platformData = LiveRoomDataManager.getInstance().getPlatformData(platformId);
                        if (platformData != null && platformData.size() > 0) {
                            hideEmptyView();
                        } else {
                            showEmptyView();
                        }
                    }
                });

        mRvLeftTab.setAdapter(roomTabAdapter);
    }

    private Typeface getTypeface() {
        if (mFromAsset != null) {
            return mFromAsset;
        }
        try {
            AssetManager assets = getContext().getResources().getAssets();
            return mFromAsset = Typeface.createFromAsset(assets, "fonts/fzcsf_37.TTF");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void loadAnchorMessage(int platformId, List<String> anchorWhiteList) {
        switch (platformId) {

            case PLATFPRM_RECOMMENDED:
                LiveRoomDataManager.getInstance().getRecommendedAnchorData(this);
                break;

            case PLATFORM_HUYA:
                LiveRoomDataManager.getInstance().getHuyaAnchorLiveInfo(anchorWhiteList, this, PLATFORM_HUYA);
                break;

            default:
                break;
        }
    }

    private void handleFinish() {
        LiveshowManager.getInstance().leaveLiveshowRoomWrapper(true);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        Log.d(TAG, "dispatchKeyEvent: ");
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            if (event.getAction() == KeyEvent.ACTION_UP) {   //按键  按下和移开会有两个不同的事件所以需要区分
                handleFinish();
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void onLiveMenuDataCallback(LiveMenuBean liveMenuBean) {
        initLeftTabView(liveMenuBean);
    }

    @Override
    public void onHuyaAnchorInfoCallback(Map<Integer, List<LiveListInfo>> data) {
        initListInfo(data, PLATFORM_HUYA);
    }

    @Override
    public void onRecommendedAnchorDataCallback(Map<Integer, List<LiveListInfo>> data) {
        initListInfo(data, PLATFPRM_RECOMMENDED);
    }

    @Override
    public void onFailure(int state, String msg) {
        LogUtil.e(TAG, "获取数据失败:" + msg);
        hideInitLoading();
        showEmptyView();
    }

    public void hide() {
        ViewUtils.gone(this);
        update();
    }
}