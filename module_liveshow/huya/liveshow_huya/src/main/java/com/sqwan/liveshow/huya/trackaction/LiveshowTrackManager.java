package com.sqwan.liveshow.huya.trackaction;

import android.os.SystemClock;

import com.sqwan.common.mod.liveshow.IAudioLiveshowTrackManager;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.liveshow.huya.trackaction.bean.BuiltinHalfOffTrackBean;
import com.sqwan.liveshow.huya.trackaction.bean.BuiltinHalfOnTrackBean;
import com.sqwan.liveshow.huya.trackaction.bean.BuiltinLiveListeningOffTrackBean;
import com.sqwan.liveshow.huya.trackaction.bean.BuiltinLiveListeningOnTrackBean;
import com.sqwan.liveshow.huya.trackaction.bean.BulletChatTrackBean;
import com.sqwan.liveshow.huya.trackaction.bean.LiveOffActtion;
import com.sqwan.liveshow.huya.trackaction.bean.LiveWatchAction;
import com.sqwan.liveshow.trackaction.LiveshowTrackBaseManager;

import java.util.HashMap;

public class LiveshowTrackManager extends LiveshowTrackBaseManager implements IAudioLiveshowTrackManager {

    public static LiveshowTrackManager getInstance() {
        return (LiveshowTrackManager) LiveshowEngine.getInstance().getLiveshowTrackManager();
    }

    /**
     * 开始收听上报
     */
    public void liveIconAction() {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_ICON);
    }

    private long mFullScreenOnTime;

    /**
     * 进入全屏直播间
     */
    public void trackFullScreenOnAction() {
        if (mFullScreenOnTime != 0) {
            return;
        }
        mFullScreenOnTime = SystemClock.uptimeMillis();

        HashMap<String, String> params = new LiveWatchAction().toMap();
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_WATCH, params);
    }

    /**
     * 退出全屏直播间
     */
    public void trackFullScreenOffAction() {
        if (mFullScreenOnTime == 0) {
            return;
        }
        long watchTime = (SystemClock.uptimeMillis() - mFullScreenOnTime) / 1000;
        mFullScreenOnTime = 0;

        LiveOffActtion liveOffActtion = new LiveOffActtion();
        liveOffActtion.watch_time = String.valueOf(watchTime);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_OFF, liveOffActtion.toMap());
    }

    private long mHalfScreenOnTime;

    /**
     * 进入半屏模式直播间
     */
    public void trackHalfScreenOnAction() {
        if (mHalfScreenOnTime != 0) {
            return;
        }
        mHalfScreenOnTime = SystemClock.uptimeMillis();

        BuiltinHalfOnTrackBean builtinHalfOnTrackBean = new BuiltinHalfOnTrackBean();
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_HALF_WATCH, builtinHalfOnTrackBean.toMap());
    }

    /**
     * 退出半屏模式直播间
     */
    public void trackHalfScreenOffAction() {
        if (mHalfScreenOnTime == 0) {
            return;
        }
        long watchTime = (SystemClock.uptimeMillis() - mHalfScreenOnTime) / 1000;
        mHalfScreenOnTime = 0;

        BuiltinHalfOffTrackBean builtinHalfOffTrackBean = new BuiltinHalfOffTrackBean();
        builtinHalfOffTrackBean.watch_time = String.valueOf(watchTime);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_HALF_OFF, builtinHalfOffTrackBean.toMap());
    }

    /**
     * 弹幕发言
     */
    public void bulletChatAction(String content, String forbidden_or_not, int state, boolean isShowImList) {
        BulletChatTrackBean bulletChatTrackBean = new BulletChatTrackBean();
        bulletChatTrackBean.content = content;
        bulletChatTrackBean.forbidden_or_not = forbidden_or_not;
        bulletChatTrackBean.state = state + "";
        if (isShowImList) {
            bulletChatTrackBean.speaking_position = "1";
        } else {
            bulletChatTrackBean.speaking_position = "2";
        }
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BULLET_CHAT, bulletChatTrackBean.toMap());
    }

    private long mListeningOnTime;

    /**
     * 进入仅听声音模式
     */
    public void trackListeningOnAction() {
        if (mListeningOnTime != 0) {
            return;
        }
        mListeningOnTime = SystemClock.uptimeMillis();

        BuiltinLiveListeningOnTrackBean trackBean = new BuiltinLiveListeningOnTrackBean();
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_LISTENING, trackBean.toMap());
    }

    /**
     * 退出仅听声音模式
     */
    public void trackListeningOffAction() {
        if (mListeningOnTime == 0) {
            return;
        }
        long listenTime = (SystemClock.uptimeMillis() - mListeningOnTime) / 1000;
        mListeningOnTime = 0;

        BuiltinLiveListeningOffTrackBean trackBean = new BuiltinLiveListeningOffTrackBean();
        trackBean.listen_time = String.valueOf(listenTime);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.BUILTIN_LIVE_LISTENING_OFF, trackBean.toMap());
    }
}