package com.sqwan.liveshow.huya.skin.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.widget.TextView;

import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sqwan.liveshow.huya.skin.attr.SkinTextColorAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;

public class SkinTextView extends TextView implements SkinViewInterface {

    private SkinTextColorAttr skinTextColorAttr;
    public SkinTextView(Context context) {
        this(context,null);
    }

    public SkinTextView(Context context,  AttributeSet attrs) {
        this(context, attrs,0);
    }

    public SkinTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        skinTextColorAttr = new SkinTextColorAttr(this,attrs);
    }


    @Override
    public void applySkin() {
        if (skinTextColorAttr != null){
            skinTextColorAttr.applySkin();
        }
    }

    public void setTextColor(Context context , String colorName){
        if (skinTextColorAttr != null){
            skinTextColorAttr.setTextColor(context,colorName);
        }
    }

}
