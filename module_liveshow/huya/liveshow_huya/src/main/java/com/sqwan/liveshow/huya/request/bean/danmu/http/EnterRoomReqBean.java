package com.sqwan.liveshow.huya.request.bean.danmu.http;

import com.sqwan.liveshow.huya.request.bean.danmu.LiveshowBaseRequest;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 11:25
 */
public class EnterRoomReqBean extends LiveshowBaseRequest {
    public String rid;

    public EnterRoomReqBean(String rid) {
        this.rid = rid;
    }

    /**
     * 描述:
     * 作者：znb
     * 时间：2021/11/19 15:51
     */
    public static class RspBeanWrapperBase {
        private int state;
        private String msg;

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
        public boolean isSuccess() {
            return state == 1;
        }
    }
}
