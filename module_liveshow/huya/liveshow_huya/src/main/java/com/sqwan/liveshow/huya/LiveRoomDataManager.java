package com.sqwan.liveshow.huya;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.huya.berry.client.HuyaBerry;
import com.huya.berry.client.customui.CustomUICallback;
import com.huya.berry.client.customui.model.LiveListInfo;
import com.huya.berry.gamesdk.base.BaseCallback;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.L;
import com.sqwan.common.util.LogUtil;
import com.sqwan.liveshow.huya.bean.ConfigBean;
import com.sqwan.liveshow.huya.bean.LiveMenuBean;
import com.sqwan.liveshow.huya.bean.RecommenedAnchorBean;
import com.sqwan.liveshow.huya.request.LiveRoomRequestManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LiveRoomDataManager {

    public final static int PLATFORM_HUYA = 1;

    public final static int PLATFPRM_RECOMMENDED = 0;

    private ConfigBean configBean;

    private Map<Integer, List<LiveListInfo>> data;

    public interface RequestListener {

        void onLiveMenuDataCallback(LiveMenuBean liveMenuBean);

        void onHuyaAnchorInfoCallback(Map<Integer, List<LiveListInfo>> data);

        void onRecommendedAnchorDataCallback(Map<Integer, List<LiveListInfo>> data);

        void onFailure(int state, String msg);
    }


    private static LiveRoomDataManager liveRoomDataManager = new LiveRoomDataManager();

    private LiveRoomRequestManager liveRoomRequestManager;

    private LiveRoomDataManager() {
        liveRoomRequestManager = new LiveRoomRequestManager(L.getApplicationContext());
        data = new HashMap<Integer, List<LiveListInfo>>();
    }

    public static LiveRoomDataManager getInstance() {
        return liveRoomDataManager;
    }

    public ConfigBean getConfigBean() {
        return configBean;
    }

    public void setConfigBean(ConfigBean configBean) {
        this.configBean = configBean;
    }

    /**
     * 获取配置数据
     * 初始化调用
     */
    public void getConfigData(final SqHttpCallback<ConfigBean> requestListener) {

        liveRoomRequestManager.ReqGetConfigData(new SqHttpCallback<ConfigBean>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (requestListener != null) {
                    requestListener.onResponseStateError(httpStatus, state, msg, data);
                }
            }

            @Override
            public void onSuccess(ConfigBean data) {
                if (requestListener != null) {
                    configBean = data;
                    requestListener.onSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (requestListener != null) {
                    requestListener.onFailure(code, errorMsg, error);
                }
            }
        });
    }

    /**
     * 给外部调用是否有平台数据
     */
    public boolean isHasplatformData(int platformID) {
        if (data == null) return false;
        return (data.get(platformID) != null);
    }

    public List<LiveListInfo> getPlatformData(int platformID) {
        if (data != null) {
            return data.get(platformID);
        } else {
            return null;
        }
    }

    /**
     * 清除平台数据
     */
    public void clearPlatformData() {
        if (data != null) {
            LogUtil.i("清除数据");
            data.clear();
        }
    }

    public void putPlatformData(int platformID, List<LiveListInfo> liveListInfoList) {
        if (data != null) {
            data.put(platformID, liveListInfoList);
        }
    }


    /**
     * 获取列表数据
     */
    public void getLiveMenuData(final RequestListener requestListener) {
        liveRoomRequestManager.ReqGetLiveMenuData(new SqHttpCallback<LiveMenuBean>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                if (requestListener != null) {
                    requestListener.onFailure(state, msg);
                }
            }

            @Override
            public void onSuccess(LiveMenuBean data) {
                if (requestListener != null) {
                    requestListener.onLiveMenuDataCallback(data);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (requestListener != null) {
                    requestListener.onFailure(code, errorMsg);
                }
            }
        });
    }


    /**
     * 获取推荐主播数据
     * 目前热门推荐的数据全部来自虎牙数据
     */
    public void getRecommendedAnchorData(final RequestListener requestListener) {
        liveRoomRequestManager.ReqGetRecommendedAnchorData(new SqHttpCallback<RecommenedAnchorBean>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (requestListener != null) {
                    requestListener.onFailure(state, msg);
                }
            }

            @Override
            public void onSuccess(RecommenedAnchorBean data) {

                List<String> anchorWhiteList = new ArrayList<>();

                for (RecommenedAnchorBean.ItemsBean dataBean : data.getItems()) {
                    if (dataBean.getPlatform_id() == PLATFORM_HUYA) {
                        anchorWhiteList.add(dataBean.getAnchor_id());
                    }
                }

                getHuyaAnchorLiveInfo(anchorWhiteList, requestListener, PLATFPRM_RECOMMENDED);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (requestListener != null) {
                    requestListener.onFailure(code, errorMsg);
                }
            }
        });
    }


    /**
     * 获取虎牙数据
     *
     * @param anchorWhiteList
     * @param requestListener
     */
    public void getHuyaAnchorLiveInfo(List<String> anchorWhiteList, RequestListener requestListener, int platform) {

        int times = 1;

        if (configBean != null && configBean.getItems() != null) {
            for (ConfigBean.ItemsBean dataBean : configBean.getItems()) {
                if (dataBean.getPlatform_id() == PLATFORM_HUYA) {
                    times = dataBean.getTimes();
                    break;
                }
            }
        } else {
            if (requestListener != null) {
                requestListener.onFailure(0, "获取直播接口初始化数据失败");
            }
        }

        ArrayList<LiveListInfo> anchorMessageBeanList = new ArrayList<>();
        ArrayList<LiveListInfo> anchorWhitePlatformList = new ArrayList<>();
        getanchorMessageFromPlatform(true, times, anchorWhiteList, anchorMessageBeanList, anchorWhitePlatformList, requestListener, platform);
    }


    /**
     * 将 anchorWhiteList 中的 uid 和 anchorMessageBeanList 中的uid 进行匹配
     * 并将匹配成功的 LiveListInfo 添加到 anchorWhitePlatformList 中
     *
     * @param isRefresh
     * @param times
     * @param anchorWhiteList
     * @param anchorMessageBeanList
     * @param anchorWhitePlatformList
     */
    private void getanchorMessageFromPlatform(boolean isRefresh,
                                              final int times, final List<String> anchorWhiteList,
                                              final ArrayList<LiveListInfo> anchorMessageBeanList,
                                              final ArrayList<LiveListInfo> anchorWhitePlatformList,
                                              final RequestListener requestListener,
                                              final int platform) {

        if (times == 0) {

            for (String uid : anchorWhiteList) {
                for (LiveListInfo liveListInfo : anchorMessageBeanList) {
                    try {
                        if (Long.parseLong(uid) != liveListInfo.uid) {
                            continue;
                        }
                        if (anchorWhitePlatformList.contains(liveListInfo)) {
                            continue;
                        }
                        anchorWhitePlatformList.add(liveListInfo);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            switch (platform) {

                case PLATFPRM_RECOMMENDED:
                    data.put(PLATFPRM_RECOMMENDED, anchorWhitePlatformList);
                    requestListener.onRecommendedAnchorDataCallback(data);
                    break;

                case PLATFORM_HUYA:
                    data.put(PLATFORM_HUYA, anchorWhitePlatformList);
                    requestListener.onHuyaAnchorInfoCallback(data);
                    break;

                default:
                    break;
            }

            return;
        }


        HuyaBerry.instance().getLiveListData(isRefresh, new CustomUICallback<LiveListInfo>() {
            @Override
            public void onResultCallback(int status, LiveListInfo liveListInfo) {
            }

            @Override
            public void onResultListCallback(int status, List<LiveListInfo> items) {
                if (status == BaseCallback.SUCCESS) {
                    anchorMessageBeanList.addAll(items);
                    getanchorMessageFromPlatform(false, times - 1, anchorWhiteList, anchorMessageBeanList, anchorWhitePlatformList, requestListener, platform);
                } else {
                    if (requestListener != null) {
                        requestListener.onFailure(status, "获取虎牙列表数据失败");
                    }
                }
            }
        });

    }

}
