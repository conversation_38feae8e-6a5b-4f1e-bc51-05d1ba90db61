package com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory;


import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImReqBean;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/22 16:13
 */
public class AImMsgFactory {
    public static int imType_normal = 1;
    public static int imType_notification = 0;
    public AImMsg convert(FetchImRspBean.ImMsg imMsg){
        AImMsg aImMsg = null;
        int type = imMsg.msg.type;
        if (type==imType_notification) {
            aImMsg = new ImNotification();
        }
        else{
            aImMsg = new ImNormal();
        }
        if (aImMsg!=null) {
            aImMsg.init(imMsg);
        }
        return aImMsg;
    }
    public FetchImRspBean.ImMsg buidNotification(){
        FetchImRspBean.ImMsg _imMsg = new FetchImRspBean.ImMsg();
        SendImReqBean.ImMsg imMsg = new SendImReqBean.ImMsg();
        imMsg.type = AImMsgFactory.imType_notification;
        imMsg.nickname = "系统消息";
        imMsg.content = "请各位用户文明发言，禁止传播任何违法违规、暴力血腥、低俗色情等不良信息；请勿轻信各类代练代抽、购买礼包码、游戏币等广告信息或私下交易信息，以免上当受骗。";
        _imMsg.msg = imMsg;
        return _imMsg;
    }
    public FetchImRspBean.ImMsg buildSelfMsg(SendImReqBean sendImReqBean){
        FetchImRspBean.ImMsg imMsg = new FetchImRspBean.ImMsg();
        imMsg.msg = sendImReqBean.msg;
        imMsg.uname = sendImReqBean.msg.nickname;
        try {
            imMsg.uid = Long.parseLong(LiveshowManager.getInstance().getUserId());
        }catch (Exception e){
            e.printStackTrace();
        }
        return imMsg;
    }
}
