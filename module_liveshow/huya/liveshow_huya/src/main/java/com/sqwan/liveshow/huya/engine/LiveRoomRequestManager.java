package com.sqwan.liveshow.huya.engine;

import com.huya.berry.client.HuyaBerry;
import com.huya.berry.client.customui.CustomUICallback;
import com.huya.berry.client.customui.model.LiveInfo;
import com.huya.berry.client.customui.model.LiveListInfo;
import com.huya.berry.gamesdk.base.BaseCallback;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.util.LogUtil;

import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-17 17:53
 */
public class LiveRoomRequestManager {

    private final String TAG = this.getClass().getSimpleName();

    public static class CustomUICallbackAdapter<T>{
        public void onResult(boolean success,T result){
            if (success) {
                onSuccess(result);
            }else{
                onFail();
            }
        }
        public void onSuccess(T result){

        }
        public void onFail(){

        }

    }

    public void getLiveListDataFirst(final CustomUICallbackAdapter<LiveListInfo> customUICallback) {
        HuyaBerry.instance().getLiveListData(true, new CustomUICallback<LiveListInfo>() {
            @Override
            public void onResultCallback(int status, LiveListInfo liveListInfo) {
                LogUtil.i(TAG,"onResultCallback");
            }

            @Override
            public void onResultListCallback(int status, List<LiveListInfo> items) {
                LogUtil.i(TAG,"onResultListCallback");
                if (status == BaseCallback.SUCCESS) {
                    if (items!=null && !items.isEmpty()) {
                        LiveListInfo item = items.get(0);
                        long channelId = item.uid;
                        LogUtil.i(TAG,"channelId:"+channelId);
                        customUICallback.onResult(true,item);
                        return;
                    }
                }
                customUICallback.onResult(false,null);

            }
        });
    }

    public void getLiveData(long uid, final CustomUICallbackAdapter<LiveInfo> customUICallback){
        HuyaBerry.instance().getLiveData(uid,new CustomUICallback<LiveInfo>() {
            @Override
            public void onResultCallback(int status, LiveInfo liveInfo) {
                if (status == BaseCallback.SUCCESS) {
                    if(liveInfo.roomId != 0){
                        customUICallback.onResult(true,liveInfo);
                        return;
                    }
                }
                customUICallback.onResult(false,null);
            }

            @Override
            public void onResultListCallback(int status, List vItems) {

            }
        });
    }


}
