package com.sqwan.liveshow.huya.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.huya.berry.GranularRoundedCorners;
import com.huya.berry.client.customui.model.LiveListInfo;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.SkinHelper;

import java.util.ArrayList;
import java.util.List;

public class RoomListAdapter extends RecyclerView.Adapter<RoomViewHolder> {

    private static final Handler HANDLER = new Handler(Looper.getMainLooper());

    private List<LiveListInfo> data;
    private final Context context;
    private final OnRoomClickListener mRoomClickListener;
    private RequestOptions requestOptions;

    public RoomListAdapter(@NonNull Context context, @NonNull OnRoomClickListener onRoomClickListener) {
        this.context = context;
        this.mRoomClickListener = onRoomClickListener;
    }

    @Override
    public RoomViewHolder onCreateViewHolder(ViewGroup parent, int position) {
        return new RoomViewHolder(LayoutInflater.from(this.context).inflate(SqResUtils.getLayoutId(this.context, SqR.layout.sy37_item_liveshow_room), parent, false));
    }

    @Override
    public void onBindViewHolder(final RoomViewHolder roomViewHolder, int position) {

        roomViewHolder.itemView.setOnClickListener(v -> mRoomClickListener.onRoomClick(data.get(roomViewHolder.getAdapterPosition())));

        ImageView ivCover = roomViewHolder.findViewById(SqResUtils.getId(this.context, SqR.id.iv_sy37_liveshow_icon_default_cover));
        TextView tvTitle = roomViewHolder.findViewById(SqResUtils.getId(this.context, SqR.id.tv_title));
        TextView tvNickname = roomViewHolder.findViewById(SqResUtils.getId(this.context, SqR.id.tv_nickname));
        TextView tvAudienceCount = roomViewHolder.findViewById(SqResUtils.getId(this.context, SqR.id.tv_audience_count));
        if (requestOptions == null) {
            requestOptions = RequestOptions
                    .placeholderOf(SkinHelper.getDrawable(this.context, SqR.drawable.sy37_liveshow_icon_default_cover))
                    .error(SkinHelper.getDrawable(this.context, SqR.drawable.sy37_liveshow_icon_default_cover));

            if (context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                requestOptions.transform(new GranularRoundedCorners(TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 4, context.getResources().getDisplayMetrics())
                        , TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 4, context.getResources().getDisplayMetrics()), 0, 0));
            }
        }
        Glide.with(this.context)
                .load(data.get(position).coverUrl)
                .apply(requestOptions)
                .into(ivCover);
        tvTitle.setText(data.get(position).title);
        tvNickname.setText(data.get(position).nickName);
        tvAudienceCount.setText(data.get(position).audienceCount);

        ViewGroup.LayoutParams layoutParams = roomViewHolder.itemView.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            if (context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT && position >= 2) {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin =
                        (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 9.5f, context.getResources().getDisplayMetrics());
            } else {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = 0;
            }
            roomViewHolder.itemView.setLayoutParams(layoutParams);
        }
    }

    @Override
    public int getItemCount() {
        return this.data == null ? 0 : data.size();
    }

    public void setLiveListInfoData(List<LiveListInfo> data) {
        if (data == null) {
            data = new ArrayList<>();
        }
        this.data = data;
        // 修复横版直播切换 TAB 后列表数据不显示的 Bug
        HANDLER.removeCallbacks(mRunnable);
        HANDLER.post(mRunnable);
    }

    public interface OnRoomClickListener {
        void onRoomClick(LiveListInfo liveListInfo);
    }

    @SuppressLint("NotifyDataSetChanged")
    private final Runnable mRunnable = () -> notifyDataSetChanged();
}