package com.sqwan.liveshow.huya.request.bean.danmu.websocket;

import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.parse.ResponseDataParse;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/12/20 12:15
 */
public class DanmuReqService extends Req<PERSON><PERSON><PERSON>Handler {
    public FailedHolder<ResponseDataParse> reqJoinRoom(JoinRoomMsgReq joinRoomMsgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(joinRoomMsgReq,successListener);
    }
    public FailedHolder<ResponseDataParse> reqleaveRoom(LeaveRoomMsgReq leaveRoomMsgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(leaveRoomMsgReq,successListener);
    }
}
