package com.sqwan.liveshow.huya.danmu;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.TaskSubThread;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 描述: 分发直播的收到的聊天信息
 * 作者：znb
 * 时间：2021/11/16 18:00
 */
public class LiveshowMsgDispatcher {
    private static final String TAG = "LiveshowMsgDispatcher";

    public interface ILiveshowMsgDispatch {
        void dispatchIm(FetchImRspBean.ImMsg imMsg);
        void dispatchDanmu(FetchImRspBean.ImMsg imMsg);

    }
    private static final LiveshowMsgDispatcher ourInstance = new LiveshowMsgDispatcher();

    public static LiveshowMsgDispatcher getInstance() {
        return ourInstance;
    }

    private LiveshowMsgDispatcher() {
    }
    private int duration_im = 50;
    private int duration_danmu = 50;
    private TaskSubThread task_im = TaskSubThread.create();
    private TaskSubThread task_danmu = TaskSubThread.create();


    private CopyOnWriteArrayList<FetchImRspBean.ImMsg> imMsgs = new CopyOnWriteArrayList<>();
    private CopyOnWriteArrayList<FetchImRspBean.ImMsg> danmuMsgs = new CopyOnWriteArrayList<>();
    private CopyOnWriteArrayList<ILiveshowMsgDispatch> danmuMsgDispatches = new CopyOnWriteArrayList<>();


    public void register(ILiveshowMsgDispatch iDanmuMsgDispatch){
        danmuMsgDispatches.add(iDanmuMsgDispatch);
    }
    public void unRegister(ILiveshowMsgDispatch iDanmuMsgDispatch){
        danmuMsgDispatches.remove(iDanmuMsgDispatch);
    }
    public void addLiveshowImMsgs(boolean flush,List<FetchImRspBean.ImMsg> imMsgs){
        boolean shouldAddDanmu = shouldAddDanmu();
        LogUtil.i(TAG,"addLiveshowImMsgs shouldAddDanmu:"+shouldAddDanmu);
        this.imMsgs.addAll(imMsgs);
        if (flush) {
            checkStartTaskIm();
        }
    }
    public void addLiveshoDanmuMsgs(boolean flush,List<FetchImRspBean.ImMsg> imMsgs){
        boolean shouldAddDanmu = shouldAddDanmu();
        LogUtil.i(TAG,"addLiveshoDanmuMsgs shouldAddDanmu:"+shouldAddDanmu);
        if (shouldAddDanmu) {
            this.danmuMsgs.addAll(imMsgs);
        }
        if (flush) {
            checkStartTaskDanmu();
        }
    }

    public void addLiveshowMsgs(boolean flush,List<FetchImRspBean.ImMsg> imMsgs){
        LogUtil.i(TAG,"addLiveshowMsgs");
        addLiveshowImMsgs(flush,imMsgs);
        addLiveshoDanmuMsgs(flush,imMsgs);
    }
    public void addLiveshowMsg(boolean self, boolean flush, FetchImRspBean.ImMsg imMsg){
       LogUtil.i(TAG,"addLiveshowMsg");
       addLiveshowDanmuMsg(self,flush,imMsg);
       addLiveshowImMsg(self,flush,imMsg);
    }
    public void addLiveshowDanmuMsg(boolean self, boolean flush, FetchImRspBean.ImMsg imMsg){
        boolean shouldAddDanmu = shouldAddDanmu();
        LogUtil.i(TAG,"addLiveshowDanmuMsg shouldAddDanmu:"+shouldAddDanmu);
        if (imMsg==null) {
            LogUtil.i(TAG,"return");
            return;
        }
        if (self) {
            if (shouldAddDanmu) {
                this.danmuMsgs.add(0,imMsg);
            }
        }else{
            if (shouldAddDanmu) {
                this.danmuMsgs.add(imMsg);
            }
        }
        if (flush) {
            checkStartTaskDanmu();
        }
    }
    public void addLiveshowImMsg(boolean self, boolean flush, FetchImRspBean.ImMsg imMsg){
        boolean shouldAddDanmu = shouldAddDanmu();
        LogUtil.i(TAG,"addLiveshowImMsg shouldAddDanmu:"+shouldAddDanmu);
        if (imMsg==null) {
            LogUtil.i(TAG,"return");
            return;
        }
        if (self) {
            this.imMsgs.add(0,imMsg);
        }else{
            this.imMsgs.add(imMsg);
        }
        if (flush) {
            checkStartTaskIm();
        }
    }
    private void checkStartTaskIm(){
        if (!task_im.isRunning()) {
            startTaskIm();
        }
    }
    private void checkStartTaskDanmu(){
        if (!task_danmu.isRunning()) {
            startTaskDanmu();
        }
    }
    private void checkStartTask(){
        checkStartTaskIm();
        checkStartTaskDanmu();

    }

    public void release(){
        imMsgs.clear();
        danmuMsgs.clear();
        stopTask();
    }
    private void stopTask(){
        task_im.stop();
        task_danmu.stop();
    }

    private void startTaskIm(){
        task_im.repeat(duration_im, new TaskSubThread.TaskFunc() {
            @Override
            public TaskSubThread.Result exec() {
                if (imMsgs.isEmpty()) {
                    return TaskSubThread.Result.Stop;
                }
                LogUtil.i(TAG,"danmuMsgDispatches size:"+danmuMsgDispatches.size());
                FetchImRspBean.ImMsg imMsg = imMsgs.remove(0);
                for (ILiveshowMsgDispatch danmuMsgDispatch : danmuMsgDispatches) {
                    LogUtil.i(TAG,"dispatch imMsg:"+imMsg);
                    danmuMsgDispatch.dispatchIm(imMsg);
                }
                return TaskSubThread.Result.Next;
            }
        });
    }
    private void startTaskDanmu(){
        task_danmu.repeat(duration_danmu, new TaskSubThread.TaskFunc() {
            @Override
            public TaskSubThread.Result exec() {
                if (danmuMsgs.isEmpty()) {
                    return TaskSubThread.Result.Stop;
                }
                LogUtil.i(TAG,"danmuMsgDispatches size:"+danmuMsgDispatches.size());
                FetchImRspBean.ImMsg imMsg = danmuMsgs.remove(0);
                for (ILiveshowMsgDispatch danmuMsgDispatch : danmuMsgDispatches) {
                    LogUtil.i(TAG,"dispatch danmuMsgs:"+imMsg);
                    danmuMsgDispatch.dispatchDanmu(imMsg);
                }
                return TaskSubThread.Result.Next;
            }
        });
    }
    public void init(){
        release();
    }
    public void flushIm(){
        startTaskIm();
    }
    public void flushDanmu(){
        startTaskDanmu();
    }
    public void flush(){
       flushIm();
       flushDanmu();

    }
    private boolean shouldAddDanmu(){
        if (LiveshowDanmuManager.getInstance().isDanmuShow()&& !LiveshowManager.getInstance().isMinimize()) {
            return true;
        }
        return false;
    }

}
