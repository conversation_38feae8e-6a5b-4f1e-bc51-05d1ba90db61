package com.sqwan.liveshow.huya.skin.attr;

import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import com.sqwan.common.util.SelectorUtil;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sqwan.liveshow.huya.skin.SkinSelectorState;


/**
 * Created by znb on 2021-10-20
 */
public abstract class SkinAttrEx extends AbstraceSkinAttr{
    public SkinAttrEx(View view, AttributeSet attrs) {
        super(view, attrs);
    }
    @Override
    protected Class<? extends View> getSkinViewClass() {
        return View.class;
    }

    public String getEntryNameByResId(){
        if (resourceId != INVALID_ID){
            String entryName = view.getContext().getResources().getResourceEntryName(resourceId);
            return  entryName;
        }
        return "";
    }
    public Drawable getDrawable(){
        String entryName = getEntryNameByResId();
        Drawable drawable = SkinHelper.getDrawable(view.getContext(),entryName);
        return drawable;
    }
    public void setBackground(SkinSelectorState... skinSelectorStates){
        Drawable[] drawables = new Drawable[skinSelectorStates.length];
        int[] stateids = new int[skinSelectorStates.length];
        for (int i = 0; i < skinSelectorStates.length; i++) {
            SkinSelectorState skinSelectorState = skinSelectorStates[i];
            drawables[i] = SkinHelper.getDrawable(view.getContext(),skinSelectorState.resName);
            stateids[i] = skinSelectorState.stateId;
        }
        SelectorUtil.addSelectorFromDrawable(view.getContext(),stateids,drawables,view);
    }
    public void setBackground(String stringSelected, String stringUnSelected){
        setBackground(new SkinSelectorState(android.R.attr.state_selected, stringSelected), new SkinSelectorState(-android.R.attr.state_selected, stringUnSelected));
    }

}
