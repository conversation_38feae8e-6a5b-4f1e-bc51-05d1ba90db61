package com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory;

import android.text.TextUtils;

import com.sq.websocket_engine.ARecInfMsg;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;

import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/24 12:32
 */
public class RoomRecInfMsg extends ARecInfMsg<List<FetchImRspBean.ImMsg>> {
    @Override
    public List<FetchImRspBean.ImMsg> getInf() {
        return inf;
    }

    @Override
    public boolean filter() {
        if (!checkRoomId(responseDataParse.body.ev,LiveshowManager.getInstance().getRoomId())) {
            return true;
        }
        return false;
    }
    public boolean checkRoomId(String ev,String currentRoomId){
        return TextUtils.equals(ev,String.format("lv.rm.%s",currentRoomId));
    }

}
