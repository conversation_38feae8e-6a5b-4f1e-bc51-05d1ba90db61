package com.sqwan.liveshow.huya.danmu;

import android.content.Context;
import android.graphics.Color;
import android.view.Display;
import android.view.WindowManager;

import com.sqwan.base.L;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.AImMsg;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.AImMsgFactory;
import com.sqwan.liveshow.huya.skin.SkinHelper;

import java.util.HashMap;
import java.util.List;

import master.flame.danmaku.controller.DrawHandler;
import master.flame.danmaku.danmaku.model.BaseDanmaku;
import master.flame.danmaku.danmaku.model.DanmakuTimer;
import master.flame.danmaku.danmaku.model.IDanmakus;
import master.flame.danmaku.danmaku.model.IDisplayer;
import master.flame.danmaku.danmaku.model.android.DanmakuContext;
import master.flame.danmaku.danmaku.model.android.Danmakus;
import master.flame.danmaku.danmaku.parser.BaseDanmakuParser;
import master.flame.danmaku.ui.widget.DanmakuView;

public class DanMuController {

    public DanmakuView mDanMuView;

    private DanmakuContext danmakuContext;

    private BaseDanmakuParser parser;


    public DanMuController(DanmakuView danmakuView){
        mDanMuView = danmakuView;
    }

    public void  init(){
        // 设置最大显示行数
        HashMap<Integer, Integer> maxLinesPair = new HashMap<Integer, Integer>();
        maxLinesPair.put(BaseDanmaku.TYPE_SCROLL_RL, 20); // 滚动弹幕最大显示20行
        // 设置是否禁止重叠
        HashMap<Integer, Boolean> overlappingEnablePair = new HashMap<Integer, Boolean>();
        overlappingEnablePair.put(BaseDanmaku.TYPE_SCROLL_RL, true);
        overlappingEnablePair.put(BaseDanmaku.TYPE_FIX_TOP, true);
        danmakuContext = DanmakuContext.create();
        WindowManager windowManager = (WindowManager) mDanMuView.getContext().getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        float refreshRate = display.getRefreshRate();
        int rate = (int)(1000/refreshRate);
        danmakuContext.setFrameUpateRate(rate);
        danmakuContext.setDanmakuStyle(IDisplayer.DANMAKU_STYLE_STROKEN, 3)
                .setDuplicateMergingEnabled(false)
                .setScrollSpeedFactor(2f)
                .setScaleTextSize(1.1f)
                .setMaximumLines(maxLinesPair)
                .preventOverlapping(overlappingEnablePair).setDanmakuMargin(40);

        parser = new BaseDanmakuParser() {
            @Override
            protected IDanmakus parse() {
                return new Danmakus();
            }
        };
        
        mDanMuView.prepare(parser,danmakuContext);
        mDanMuView.setCallback(new DrawHandler.Callback() {
            @Override
            public void prepared() {
                mDanMuView.start();
            }

            @Override
            public void updateTimer(DanmakuTimer timer) {

            }

            @Override
            public void danmakuShown(BaseDanmaku danmaku) {

            }

            @Override
            public void drawingFinished() {

            }
        });
    }

    private  long lastLiveDanMu = 0;
    private void addDanmaku(boolean islive,CharSequence text) {
        long time ;
        if (islive){
            lastLiveDanMu = 500 +  mDanMuView.getCurrentTime() + 500;
            time = lastLiveDanMu;
        }else{
            if (lastLiveDanMu  > mDanMuView.getCurrentTime()){
                time = lastLiveDanMu + 500;
            }else{
                time = mDanMuView.getCurrentTime() + 500;
            }
        }

        BaseDanmaku danmaku = danmakuContext.mDanmakuFactory.createDanmaku(BaseDanmaku.TYPE_SCROLL_RL);
        if (danmaku == null || mDanMuView == null) {
            return;
        }

        danmaku.text = text;
        danmaku.padding = 5;
        danmaku.isLive = islive;
        danmaku.textSize = 15f * (parser.getDisplayer().getDensity() - 0.6f);
        danmaku.textColor = SkinHelper.getColorValue(L.getActivity(), SqR.color.sy37_liveshow_float_cover_view_sv_danmaku_text_color, Color.WHITE);
        danmaku.textShadowColor = SkinHelper.getColorValue(L.getActivity(), SqR.color.sy37_liveshow_float_cover_view_sv_danmaku_stroke_color, Color.BLACK);
        danmaku.setTime(time);
        if(islive) {
            danmaku.priority = 1;  // 可能会被各种过滤器过滤并隐藏显示
            danmaku.borderColor = SkinHelper.getColorValue(L.getActivity(), SqR.color.sy37_liveshow_float_cover_view_sv_danmaku_border_color, Color.parseColor("#61C757"));
        }else{
            danmaku.priority = 0;
        }
        mDanMuView.addDanmaku(danmaku);
    }


    public void onPause() {
        if (mDanMuView != null && mDanMuView.isPrepared()) {
            mDanMuView.pause();
        }
    }


    public void onResume() {
        if (mDanMuView != null && mDanMuView.isPrepared() && mDanMuView.isPaused()){
            mDanMuView.resume();
        }
    }


    public void finish(){
        if (mDanMuView != null) {
            mDanMuView.release();
            mDanMuView = null;
        }
    }
    public void sendDanMuData(FetchImRspBean.ImMsg imMsg){
        AImMsg aImMsg = new AImMsgFactory().convert(imMsg);
        if (aImMsg!=null) {
            if (imMsg.isSelf()) {
                sendDanMuBySelf(aImMsg.getContent());
            }else{
                sendDanMuData(aImMsg.getContent());
            }
        }
    }
    /**
     * 用于发送别人的弹幕
     * @param msg
     */
    public void sendDanMuData(CharSequence msg) {
        addDanmaku(false,msg);
    }

    /**
     * 用于发送自己的弹幕
     * @param msg
     */
    public void sendDanMuBySelf(CharSequence msg){
        addDanmaku(true,msg);
    }


    /**
     * 用于发送别人的弹幕，列表形式
     * @param datalist
     */
    public void sendDanMuDataList(List<String> datalist) {
        for (String msg : datalist){
            addDanmaku(false,msg);
        }
    }

}
