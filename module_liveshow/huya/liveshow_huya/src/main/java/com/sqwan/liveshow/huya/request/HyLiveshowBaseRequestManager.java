package com.sqwan.liveshow.huya.request;

import android.content.Context;
import com.sdk.sq.net.RequestErrorCode;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.websocket_engine.NetworkConst;
import com.sq.websocket_engine.WebSocketCenter;
import com.sqnetwork.voly.NoConnectionError;
import com.sqwan.common.mod.CommonConfigs;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 10:27
 */
public class HyLiveshowBaseRequestManager {

    protected final Context mContext;

    public HyLiveshowBaseRequestManager(Context context) {
        mContext = context;
    }

    protected long getCurrentTime() {
        return CommonConfigs.getInstance().getCurrentTime();
    }

    protected <T> void get(SqRequest request, SqHttpCallback<T> callback, Class<T> clazz) {
        if (!WebSocketCenter.getInstance().isLocalConnected()) {
            if (callback != null) {
                callback.onFailure(
                    RequestErrorCode.ERROR_CONNECTION, NetworkConst.tips_no_network, new NoConnectionError());
            }
            return;
        }

        request.get(callback, clazz);
    }

    protected <T> void post(SqRequest request, SqHttpCallback<T> callback, Class<T> clazz) {
        if (!WebSocketCenter.getInstance().isLocalConnected()) {
            if (callback != null) {
                callback.onFailure(
                    RequestErrorCode.ERROR_CONNECTION, NetworkConst.tips_no_network, new NoConnectionError());
            }
            return;
        }

        request.post(callback, clazz);
    }
}
