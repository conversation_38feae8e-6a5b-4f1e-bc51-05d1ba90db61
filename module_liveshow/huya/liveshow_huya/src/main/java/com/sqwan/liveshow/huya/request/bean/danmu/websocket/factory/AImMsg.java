package com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory;

import android.content.Context;

import com.sqwan.base.L;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImReqBean;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/22 16:08
 */
public abstract class AImMsg extends SendImReqBean.ImMsg {
    private FetchImRspBean.ImMsg imMsg;
    private CharSequence user,content;
    public void init(FetchImRspBean.ImMsg imMsg){
        if (imMsg!=null) {
            this.imMsg = imMsg;
            this.user = SpanUtil.getFontString(imMsg.msg.nickname+"： ", dip2px(getUserSize()),getUserColor());
            this.content = SpanUtil.getFontString(imMsg.msg.content,dip2px(getContentSize()),getContentColor());
        }
    }

    abstract protected int getUserColor();
    abstract protected int getContentColor();
    abstract protected int getUserSize();
    abstract protected int getContentSize();
    protected CharSequence convertUser(String user){
        return user;
    }
    protected CharSequence converContent(String content){
        return content;
    }

    public CharSequence getUser() {
        return user;
    }

    public CharSequence getContent() {
        return content;
    }
    private int dip2px(int size){
        return DensityUtil.dip2px(L.getApplicationContext(), size);
    }
}
