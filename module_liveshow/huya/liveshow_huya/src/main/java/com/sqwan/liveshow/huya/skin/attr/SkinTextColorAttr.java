package com.sqwan.liveshow.huya.skin.attr;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.sqwan.liveshow.huya.skin.SkinHelper;

public class SkinTextColorAttr extends SkinAttrEx{

    public SkinTextColorAttr(View view, AttributeSet attrs) {
        super(view, attrs);
        resourceId = getAttributeValue(attrs,android.R.attr.textColor);
    }

    @Override
    protected Class<? extends View> getSkinViewClass() {
        return TextView.class;
    }

    @Override
    public void applySkinWithValid() {
        int colorId = SkinHelper.getColorValue(view.getContext(),getEntryNameByResId(),Color.parseColor("#090a0e"));
        ((TextView)view).setTextColor(colorId);
    }

    public void setTextColor(Context context , String colorName){
        int colorId = SkinHelper.getColorValue(context,colorName, Color.parseColor("#090a0e"));
        ((TextView)view).setTextColor(colorId);
    }

}
