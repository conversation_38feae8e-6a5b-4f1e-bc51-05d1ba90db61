package com.sqwan.liveshow.huya.skin.attr;

import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.liveshow.huya.skin.SkinHelper;

public class SkinTextColorHintAttr extends SkinAttrEx{

    public SkinTextColorHintAttr(View view, AttributeSet attrs) {
        super(view, attrs);
        resourceId = getAttributeValue(attrs,android.R.attr.textColorHint);
    }

    @Override
    protected Class<? extends View> getSkinViewClass() {
        return TextView.class;
    }

    @Override
    public void applySkinWithValid() {
        int colorId = SkinHelper.getColorValue(view.getContext(),getEntryNameByResId(),Color.parseColor("#090a0e"));
        LogUtil.d("xxxxxxxxx",getEntryNameByResId() + "_" + colorId);
        ((TextView)view).setHintTextColor(colorId);
    }
}
