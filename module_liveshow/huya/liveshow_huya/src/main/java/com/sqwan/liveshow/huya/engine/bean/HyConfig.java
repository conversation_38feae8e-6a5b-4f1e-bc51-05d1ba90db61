package com.sqwan.liveshow.huya.engine.bean;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-30 10:32
 */
public class HyConfig {
    public int gameId;
    public String appId="";
    public String appKey="";

    public HyUiConfig hyUiConfig;
    public static class HyUiConfig{
        public boolean landscapeMode=true;

        @Override
        public String toString() {
            return "HyUiConfig{" +
                    "landscapeMode=" + landscapeMode +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "HyConfig{" +
                "gameId=" + gameId +
                ", appId='" + appId + '\'' +
                ", appKey='" + appKey + '\'' +
                ", hyUiConfig=" + hyUiConfig +
                '}';
    }
}
