package com.sqwan.liveshow.huya.skin.attr;

import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;


/**
 * Created by znb on 2021-10-20
 */
public class SkinDrawableAttr extends SkinAttrEx {
    public SkinDrawableAttr(View view, AttributeSet attrs) {
        super(view, attrs);
        resourceId = getAttributeValue(attrs , android.R.attr.src);
    }

    @Override
    protected Class<? extends View> getSkinViewClass() {
        return ImageView.class;
    }

    @Override
    public void applySkinWithValid() {
        Drawable drawable = getDrawable();
        if (drawable!=null) {
            ((ImageView)view).setImageDrawable(drawable);
        }
    }
    public void setImageResource(int resId) {
        resourceId = checkResourceId(resId);
        applySkinWithValid();
    }

}
