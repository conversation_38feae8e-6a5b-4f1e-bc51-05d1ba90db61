package com.sqwan.liveshow.huya.trackaction.bean;

import com.google.sqgson.Gson;
import com.google.sqgson.GsonBuilder;
import com.google.sqgson.reflect.TypeToken;
import com.sqwan.liveshow.huya.engine.LiveshowManager;

import java.lang.reflect.Type;
import java.util.HashMap;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 11:20
 */
public class DanmuBaseTrackBean {
    public String channel_id;
    public String anchor_roomid;
    public String anchor_name;
    public String anchor_id;
    public HashMap<String,String> toMap(){
        Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();
        Type type = new TypeToken<HashMap<String, String>>() {}.getType();
        String jsonString = new Gson().toJson(this);
        HashMap<String, String> map = gson.fromJson(jsonString, type);
        return map;
    }
    public DanmuBaseTrackBean(){
        this.channel_id = LiveshowManager.getInstance().getPlatformID()+"";
        this.anchor_roomid = LiveshowManager.getInstance().getRoomId();
        this.anchor_name = LiveshowManager.getInstance().liveInfoEx.liveListInfo.nickName;
        this.anchor_id = LiveshowManager.getInstance().liveInfoEx.liveListInfo.uid+"";
    }
}
