package com.sqwan.liveshow.huya.skin.attr;

import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;


/**
 * Created by znb on 2021-10-20
 */
public class SkinBackgroundAttr extends SkinAttrEx {
    public SkinBackgroundAttr(View view, AttributeSet attrs) {
        super(view, attrs);
        resourceId = getAttributeValue(attrs , android.R.attr.background);
    }
    @Override
    protected Class<? extends View> getSkinViewClass() {
        return View.class;
    }

    @Override
    public void applySkinWithValid() {
        Drawable  drawable = getDrawable();
        if (drawable!=null) {
            view.setBackground(drawable);
        }
    }

    public void setBackgroundResource(int resId) {
        resourceId = checkResourceId(resId);
        applySkinWithValid();
    }

}
