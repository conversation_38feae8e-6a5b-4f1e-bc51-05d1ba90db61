package com.sqwan.liveshow.huya.bean;

import java.util.List;

public class ConfigBean {


    private List<ItemsBean> items;

    public List<ItemsBean> getItems() {
        return items;
    }

    public void setItems(List<ItemsBean> items) {
        this.items = items;
    }

    public static class ItemsBean {
        /**
         * platform_id : 1
         * game_id : 6745
         * app_id : huya_app_603
         * app_key : QD4VGDx33E+FhFIpD/bf2w==
         * times : 3
         */

        private int platform_id;
        private String game_id;
        private String app_id;
        private String app_key;
        private int times;

        public int getPlatform_id() {
            return platform_id;
        }

        public void setPlatform_id(int platform_id) {
            this.platform_id = platform_id;
        }

        public String getGame_id() {
            return game_id;
        }

        public void setGame_id(String game_id) {
            this.game_id = game_id;
        }

        public String getApp_id() {
            return app_id;
        }

        public void setApp_id(String app_id) {
            this.app_id = app_id;
        }

        public String getApp_key() {
            return app_key;
        }

        public void setApp_key(String app_key) {
            this.app_key = app_key;
        }

        public int getTimes() {
            return times;
        }

        public void setTimes(int times) {
            this.times = times;
        }


    }

    @Override
    public String toString() {
        return "ConfigBean{" +
                "items=" + items +
                '}';
    }
}
