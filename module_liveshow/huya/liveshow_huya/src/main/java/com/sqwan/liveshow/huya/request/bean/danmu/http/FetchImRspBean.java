package com.sqwan.liveshow.huya.request.bean.danmu.http;

import android.text.TextUtils;

import com.sqwan.liveshow.huya.engine.LiveshowManager;

import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 14:54
 */
public class FetchImRspBean {

        public List<ImMsg> items;


        public static class ImMsg {
            /**
             * uid : -43710492
             * uname : esse cupidatat amet in
             * rid : ipsum sed id
             * time : -48626737
             * msg : laborum
             */

            public long uid;
            public String uname;
            public String rid;
            public long time;
            public SendImReqBean.ImMsg msg;

            public boolean isSelf(){
                return TextUtils.equals(uid+"", LiveshowManager.getInstance().getUserId());
            }
        }

}
