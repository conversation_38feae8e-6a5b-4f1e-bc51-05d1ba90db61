package com.sqwan.liveshow.huya.engine;

import com.huya.berry.client.HuyaBerry;
import com.sqwan.common.util.LogUtil;

import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-21 14:16
 */
public class LiveshowEventManager {
    private final String TAG = this.getClass().getSimpleName();
    public void onEvent(Map<String, String> params) {
        String eventType = params.get(HuyaBerry.BerryEvent.BERRYEVENT_EVENTTYPE);
        final StringBuffer sb = new StringBuffer();
        sb.append("事件是").append(eventType).append(",");
        for (String key : params.keySet()) {
            String value = params.get(key);
            sb.append(key).append(":");
            sb.append(value).append(",");
        }
        LogUtil.i(TAG, sb.toString());
    }

}
