package com.sqwan.liveshow.huya.view;

import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.util.AttributeSet;
import android.widget.VideoView;

import com.huya.berry.sdkplayer.floats.view.PlayerView;
import com.sqwan.common.util.ReflectionUtils;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-22 10:46
 */
public class PlayerViewWrapper extends PlayerView {
    public PlayerViewWrapper(Context context) {
        super(context);
    }

    public PlayerViewWrapper(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PlayerViewWrapper(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

//    @Override
//    protected void configView(Context context) {
//        super.configView(context);
//            VideoView videoView = (VideoView) ReflectionUtils.getFieldValue(this,"mLivePlayerView");
//            videoView.setBackgroundColor(Color.BLACK);
////        if (isLowVideoViewSdkVersion()) {
//////            videoView.setZOrderOnTop(true);
////            videoView.setZOrderMediaOverlay(true);
////        }
//    }
    private boolean isLowVideoViewSdkVersion(){
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.M;
    }
}
