package com.sqwan.liveshow.huya.danmu.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.view.SkinLinearLayout;

public class InputAndCountDownView extends SkinLinearLayout {

    public OnClickListener onClickListener;

    private TextView tvCountDown;

    private TextView tvInputMessage;

    private LinearLayout linearLayout;

    public InputAndCountDownView(Context context) {
        this(context,null);
    }

    public InputAndCountDownView(Context context, @Nullable AttributeSet attrs) {
        this(context,attrs,0);
    }

    public InputAndCountDownView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public InputAndCountDownView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }

    private void initView(Context context){
        View.inflate(context, SqResUtils.getLayoutId(context,SqR.layout.sy37_item_roast_view),this);
        linearLayout = (LinearLayout) findViewById(SqResUtils.getId(context,SqR.id.ll_roast_view));
        tvCountDown = (TextView) findViewById(SqResUtils.getId(context,SqR.id.tv_countdown));
        tvInputMessage = (TextView) findViewById(SqResUtils.getId(context,SqR.id.tv_input_message));


    }

    public TextView getTvInputMessage(){
        return tvInputMessage;
    }

    public TextView getTvCountDown(){
        return tvCountDown;
    }

    public LinearLayout getLinearLayout(){
        return linearLayout;
    }

}