package com.sqwan.liveshow.huya.request.bean.danmu.http;

import com.google.sqgson.Gson;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.liveshow.huya.request.bean.danmu.LiveshowBaseRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 11:25
 */
public class SendImReqBean extends LiveshowBaseRequest {
    public ImMsg msg;
    public SendImReqBean(String nickname,String content){
        msg = new ImMsg();
        msg.nickname = nickname;
        msg.content = content;
    }
    public static class ImMsg{
        public String nickname;
        public int type = 1;
        public String content;

        @Override
        public String toString() {
            return "ImMsg{" +
                    "nickname='" + nickname + '\'' +
                    ", type=" + type +
                    ", content='" + content + '\'' +
                    '}';
        }
    }
    public Map<String,String> toMap(){
        Map<String,String> map = new HashMap<>();
        map.put("msg",new Gson().toJson(msg));
        map.put("actor_id", CommonConfigs.getInstance().getBaseUserInfo().roleId);
        return map;
    }


}
