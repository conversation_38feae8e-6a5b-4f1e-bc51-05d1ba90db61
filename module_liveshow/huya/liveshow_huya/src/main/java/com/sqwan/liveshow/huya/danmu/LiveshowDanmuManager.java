package com.sqwan.liveshow.huya.danmu;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.websocket_engine.ARecInfMsg;
import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.WebSocketEngine;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.L;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.TimeUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.DanmuImRequestManager;
import com.sqwan.liveshow.huya.request.DanmuRequestManager;
import com.sqwan.liveshow.huya.request.DanmuRoomRequestManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.AImMsgFactory;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.ARecInfMsgDanmuFactory;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.RoomRecInfMsg;
import com.sqwan.liveshow.huya.trackaction.LiveshowTrackManager;
import com.sqwan.liveshow.huya.view.LiveshowFloatView;
import com.sqwan.liveshow.huya.view.OnBehaviorListener;
import com.sqwan.liveshow.huya.view.OnBehaviorListenerAdapter;
import java.util.ArrayList;
import java.util.List;


/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/5 17:32
 */
public class LiveshowDanmuManager implements WebSocketEngine.WebSocketEngineCallback {

    private static final String TAG = "LiveshowDanmuManager";

    private static final LiveshowDanmuManager ourInstance = new LiveshowDanmuManager();

    public long firstJoinRoomTime;

    public DanmuRequestManager getDanmuRequestManager() {
        return danmuRequestManager;
    }

    public static LiveshowDanmuManager getInstance() {
        return ourInstance;
    }


    public DanmuRequestManager danmuRequestManager;
    public DanmuRoomRequestManager danmuRoomRequestManager;
    public DanmuImRequestManager danmuImRequestManager;

    private Task taskSendImLimit = Task.create();

    private String inputTxtTemp = "";

    public String getInputTxtTemp() {
        return inputTxtTemp;
    }

    public void setInputTxtTemp(String inputTxtTemp) {
        this.inputTxtTemp = inputTxtTemp;
    }

    private final long taskSendImLimitDuration = 1;//单位 s
    private final long taskSendImLimitCount = 3;//单位 s
    private long taskSendImLimitCountDown = taskSendImLimitCount;

    private boolean isImListShow = true;
    private boolean isDanmuShow = true;
    private boolean hasFetchIm = false;

    public boolean isDanmuShow() {
        return isDanmuShow;
    }

    public void setDanmuShow(boolean danmuShow) {
        isDanmuShow = danmuShow;
    }

    public boolean isImListShow() {
        return isImListShow;
    }

    public void setImListShow(boolean imListShow) {
        isImListShow = imListShow;
    }


    private LiveshowDanmuManager() {
        danmuRequestManager = new DanmuRequestManager();
        danmuImRequestManager = new DanmuImRequestManager(L.getApplicationContext());
        danmuRoomRequestManager = new DanmuRoomRequestManager(L.getApplicationContext());
    }

    private List<SendImLimitListener> sendImLimitListeners = new ArrayList<>();

    @Override
    public void onAuth() {
        handleRejoinRoom();
    }

    @Override
    public void onReceiveInf(ARecInfMsg inf) {
        handleMsgInf(inf);
    }

    private boolean checkSelf(long uid) {
        return TextUtils.equals(uid + "", LiveshowManager.getInstance().getUserId());
    }

    private void handleMsgInf(ARecInfMsg inf) {
        if (inf instanceof RoomRecInfMsg) {
            if (!hasFetchIm) {
                LogUtil.i(TAG, "hasFetchIm return");
                return;
            }
            List<FetchImRspBean.ImMsg> imMsgs = ((RoomRecInfMsg) inf).getInf();
            List<FetchImRspBean.ImMsg> imMsgsFilter = new ArrayList<>();
            for (FetchImRspBean.ImMsg imMsg : imMsgs) {
                if (TextUtils.isEmpty(imMsg.uid + "")) {
                    continue;
                }
                if (checkSelf(imMsg.uid)) {
                    continue;
                }
                long firstJoinRoomTime = LiveshowDanmuManager.getInstance().firstJoinRoomTime;
                LogUtil.i(TAG, String.format("filter time outdate %s %s", TimeUtils.millis2String(firstJoinRoomTime * 1000), TimeUtils.millis2String(imMsg.time * 1000)));
                if (firstJoinRoomTime > imMsg.time) {
                    try {
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    continue;
                }
                imMsgsFilter.add(imMsg);
            }
            LiveshowMsgDispatcher.getInstance().addLiveshowMsgs(true, imMsgsFilter);
        }


    }

    public interface SendImLimitListener {
        void onCountDown(boolean finish, String time);
    }

    public void regSendImLimitListener(SendImLimitListener sendImLimitListener) {
        sendImLimitListeners.add(sendImLimitListener);
    }

    public void unRegSendImLimitListener(SendImLimitListener sendImLimitListener) {
        sendImLimitListeners.remove(sendImLimitListener);

    }

    private void initRoomData() {
        isDanmuShow = true;
        isImListShow = true;
        hasFetchIm = false;
        firstJoinRoomTime = 0;
    }

    public void joinRoom() {
        initRoomData();
        if (isImListShow) {
            LiveshowTrackManager.getInstance().trackHalfScreenOnAction();
        }
        LiveshowMsgDispatcher.getInstance().init();
        fetchIm();
        WebSocketEngine.getInstance().registerWebSocketEngineCallback(this);
    }

    private ARecInfMsgDanmuFactory aRecInfMsgDanmuFactory = new ARecInfMsgDanmuFactory();

    public void joinLiveShow() {
        LogUtil.i(TAG, "joinLiveShow");
        WebSocketEngine.getInstance().addARecInfMsgBaseFactory(aRecInfMsgDanmuFactory);
    }

    public void leaveRoom() {
        LogUtil.i(TAG, "leaveRoom");
        if (!LiveshowManager.getInstance().isLiveshow()) {
            LogUtil.i(TAG, "leaveRoom return");
            return;
        }
        uploadLiveShowSwitchAction(true, isImListShow);
        initRoomData();
        LiveshowMsgDispatcher.getInstance().release();
        danmuRoomRequestManager.leaveRoom(null);
        danmuRequestManager.reqLeaveRoom(LiveshowManager.getInstance().getRoomId(), null);
        taskSendImLimit.stop();
        WebSocketEngine.getInstance().unregisterWebSocketEngineCallback(this);

    }

    public void leaveLiveShow() {
        LogUtil.i(TAG, "leaveLiveShow");
        if (!LiveshowManager.getInstance().isLiveshowList() && !LiveshowManager.getInstance().isLiveshow()) {
            LogUtil.i(TAG, "leaveLiveShow return");
            return;
        }
        leaveRoom();
        WebSocketEngine.getInstance().removeARecInfMsgBaseFactory(aRecInfMsgDanmuFactory);

    }


    public void fetchIm() {
        LiveshowMsgDispatcher.getInstance().addLiveshowImMsg(false, true, new AImMsgFactory().buidNotification());
        LiveshowDanmuManager.getInstance().danmuImRequestManager.fetchIm(new SqHttpCallback<FetchImRspBean>() {
            @Override
            public void onSuccess(FetchImRspBean data) {
                if (data != null) {
                    if (data.items != null && !data.items.isEmpty()) {
                        LiveshowMsgDispatcher.getInstance().addLiveshowMsgs(false, data.items);
                    }
                }
                onFinish();
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                onFinish();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                onFinish();
            }

            private void onFinish() {
                LiveshowMsgDispatcher.getInstance().flush();
                hasFetchIm = true;
                firstJoinRoomTime = CommonConfigs.getInstance().getCurrentTime();
                LiveshowDanmuManager.getInstance().getDanmuRequestManager().reqJoinRoom(LiveshowManager.getInstance().getRoomId(), null);
            }
        });
    }

    private void handleImLimitTask() {
        taskSendImLimitCountDown = taskSendImLimitCount + taskSendImLimitDuration;
        taskSendImLimit.repeat(taskSendImLimitDuration * 1000, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                taskSendImLimitCountDown = taskSendImLimitCountDown - taskSendImLimitDuration;
                LogUtil.i(TAG, "handleImLimitTask taskSendImLimitCount:" + taskSendImLimitCountDown);
                boolean isFinish = taskSendImLimitCountDown == 0;
                for (SendImLimitListener sendImLimitListener : sendImLimitListeners) {
                    if (sendImLimitListener != null) {
                        sendImLimitListener.onCountDown(isFinish, taskSendImLimitCountDown + "");
                    }
                }
                if (isFinish) {
                    return Task.Result.Stop;
                } else {
                    return Task.Result.Next;
                }
            }
        });

    }

    public void sendIm(String content) {
        String nickname = LiveshowManager.getInstance().getUsernick();
        final SendImReqBean sendImReqBean = new SendImReqBean(nickname, content);
        danmuImRequestManager.sendIm(sendImReqBean, new SqHttpCallback<SendImRspBean>() {
            @Override
            public void onSuccess(SendImRspBean data) {
                if (!data.isForbid()) {
                    FetchImRspBean.ImMsg imMsg = new AImMsgFactory().buildSelfMsg(sendImReqBean);
                    LiveshowMsgDispatcher.getInstance().addLiveshowMsg(true, true, imMsg);
                    handleImLimitTask();
                }
                LiveshowTrackManager.getInstance().bulletChatAction(
                    sendImReqBean.msg.content, data.forbid + "", 1, LiveshowDanmuManager.getInstance().isImListShow);
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                ToastUtil.showToast(msg);
                LiveshowTrackManager.getInstance().bulletChatAction(
                    sendImReqBean.msg.content, "", state, LiveshowDanmuManager.getInstance().isImListShow);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                ToastUtil.showToast(errorMsg);
            }
        });
    }

    private void uploadLiveShowSwitchAction(boolean isFinish, boolean isUploadHalfWatchTime) {
        if (isUploadHalfWatchTime) {
            LiveshowTrackManager.getInstance().trackHalfScreenOffAction();
            if (isFinish) {
                return;
            }
            LiveshowTrackManager.getInstance().trackFullScreenOnAction();
        } else {
            LiveshowTrackManager.getInstance().trackFullScreenOffAction();
            if (isFinish) {
                return;
            }
            LiveshowTrackManager.getInstance().trackHalfScreenOnAction();
        }
    }

    public OnBehaviorListener onBehaviorListener = new OnBehaviorListenerAdapter() {
        @Override
        public void showChatList(boolean show) {
            setImListShow(show);
            uploadLiveShowSwitchAction(false, !show);
        }

        @Override
        public void showDanmu(boolean show) {
            if (show) {
                ToastUtil.showToast("弹幕已开启");
            } else {
                ToastUtil.showToast("弹幕已关闭");
            }
            setDanmuShow(show);
        }
    };


    /**
     * 断网重连重新进房
     */
    private void handleRejoinRoom() {
        if (LiveshowManager.getInstance().isLiveshow()) {
            String roomId = LiveshowManager.getInstance().getRoomId();
            if (!TextUtils.isEmpty(roomId)) {
                danmuRoomRequestManager.enterRoom(new EnterRoomReqBean(roomId), new SimpleSqHttpCallback<EnterRoomRspBean>() {
                    @Override
                    public void onSuccess(EnterRoomRspBean responseBean) {
                        LogUtil.i(TAG, "enterRoom result:" + responseBean.isForbid());
                    }
                });
                LiveshowDanmuManager.getInstance().getDanmuRequestManager().reqJoinRoom(LiveshowManager.getInstance().getRoomId(), new ReqWrapperHandler.FinishListener<Boolean>() {
                    @Override
                    public void on(Boolean result) {
                        LogUtil.i(TAG, "reqJoinRoom result:" + result);
                    }
                });
            }
        }
    }

    public void showSoftkeyboardPadding(int height) {
        LiveshowFloatView liveshowFloatView = LiveshowManager.getInstance().getLiveshowFloatView();
        if (liveshowFloatView == null) {
            return;
        }
        View liveshowSoftkeyboardPadding = liveshowFloatView.liveshow_softkeyboard_padding;
        if (liveshowSoftkeyboardPadding == null) {
            return;
        }
        ViewGroup.LayoutParams params = liveshowSoftkeyboardPadding.getLayoutParams();
        params.height = height;
        liveshowSoftkeyboardPadding.setLayoutParams(params);
        ViewUtils.show(liveshowSoftkeyboardPadding);

    }

    public void hideSoftkeyboardPadding() {
        LiveshowFloatView liveshowFloatView = LiveshowManager.getInstance().getLiveshowFloatView();
        if (liveshowFloatView == null) {
            return;
        }
        View liveshowSoftkeyboardPadding = liveshowFloatView.liveshow_softkeyboard_padding;
        if (liveshowSoftkeyboardPadding == null) {
            return;
        }
        ViewUtils.gone(liveshowSoftkeyboardPadding);
    }
}
