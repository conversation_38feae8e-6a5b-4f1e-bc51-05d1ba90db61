package com.sqwan.liveshow.huya.skin;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.util.Xml;
import android.view.View;
import android.widget.ImageView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;

import org.xmlpull.v1.XmlPullParser;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述: 处理直播需要替换的ui
 * 作者：znb
 * 时间：2021-10-14 10:08
 */
public class SkinHelper {
    private static final String UI_VERSION = "2";

    private static final String TAG = "LiveshowUiHelper";

    private static final String appsrcdirpath = "liveshowUI";

    private static final String pluginsrcdirpath = "liveshowUIplugin";

    private static final String colorConfigPath = "colorConfig";

    private static final String colorXmlFile = "colors.xml";

    private static List<String> appsrcnames = new ArrayList<>();

    private static List<String> pluginsrcnames = new ArrayList<>();

    private static Map<String,String> colorMap = new HashMap<>();

    private static String imgpostfix = ".png";

    private static String idnamepostfix = "_selector";

    private static String liveshow_ui_anchor = "sy37_liveshow";


    public static void init(Context context) {
        findSrcNames(context, getSrcDirPath(true), appsrcnames);
        findSrcNames(context, getSrcDirPath(false), pluginsrcnames);
        findColorXmlInHost(context);
    }

    private static void findSrcNames(Context context, String srcDirPath, List<String> srcNames) {
        if (srcNames != null) {
            srcNames.clear();
            try {
                String[] names = context.getAssets().list(srcDirPath);
                for (String name : names) {
                    String _name = name.split(imgpostfix)[0];
                    LogUtil.d(TAG, String.format("name:%s _name:%s", name, _name));
                    srcNames.add(_name);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static void findColorXmlInHost(Context context){
        try {
            InputStream inputStream = context.getAssets().open(colorConfigPath + File.separator + colorXmlFile);
            //创建XmlPullParser
            XmlPullParser parser= Xml.newPullParser();
            //解析文件输入流
            parser.setInput(inputStream,"UTF-8");
            int eventType=parser.getEventType();

            while (eventType!=XmlPullParser.END_DOCUMENT){
                if (eventType == XmlPullParser.START_TAG) {//获得解析器当前指向的元素的名字
                    //当指向元素的名字和id，name，sex这些属性重合时可以返回他们的值
                    String itemName = parser.getName();
                    if ("color".equals(itemName)) {
                        //通过解析器获取id的元素值，并设置一个新的Student对象的id
                        String keyName = parser.getAttributeValue(null, "name");
                        String value = parser.nextText();
                        colorMap.put(keyName, value);
                    }
                }
                eventType = parser.next();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public static void setImage(View view, Drawable drawable) {
        if (drawable != null) {
            if (view instanceof ImageView) {
                ((ImageView) view).setImageDrawable(drawable);
            } else {
                view.setBackground(drawable);
            }
        }
    }

    private static String getSrcDirPath(boolean isHost) {
        if (isHost) {
            return appsrcdirpath + File.separator + UI_VERSION;
        } else {
            return pluginsrcdirpath + File.separator + UI_VERSION;
        }
    }

    private static String getSrcPath(boolean isHost, String srcName) {
        return getSrcDirPath(isHost) + File.separator + srcName;
    }

    private static Drawable getDrawableAsset(Context context, String srcPathName) {
        Drawable drawable;
        try {
            InputStream ims = context.getAssets().open(srcPathName);
            TypedValue typedValue = new TypedValue();
            typedValue.density = DisplayMetrics.DENSITY_XXHIGH;
            drawable = Drawable.createFromResourceStream(context.getResources(), typedValue, ims, "src", null);
        } catch (Exception ex) {
            return null;
        }
        return drawable;
    }

    private static Drawable getDrawableWrapper(Context context, String srcPathName) {
        return getDrawableAsset(context, srcPathName);
    }

    private static Drawable getDrawableHostAsset(Context context, String srcName) {
        return getDrawableWrapper(context, getSrcPath(true, srcName));
    }

    private static Drawable getDrawablePluginAsset(Context context, String srcName) {
        return getDrawableWrapper(context, getSrcPath(false, srcName));
    }

    private static Drawable getDrawablePlugin(Context context, String srcName) {
        try {
            return context.getResources().getDrawable(SqResUtils.getDrawableId(context, srcName));
        } catch (Exception e) {
            return null;
        }
    }

    private static Drawable getDrawablePluginWrapper(Context context, String srcName) {
        try {
            return context.getResources().getDrawable(SqResUtils.getDrawableId(context, srcName));
        } catch (Exception e) {
            return null;
        }
    }


    public static int getColorValue(Context context ,String name,int defaultColorId){
        String colorValue = getColorValueFromHostAsset(name);
        if (!TextUtils.isEmpty(colorValue)){
            try {
                return Color.parseColor(colorValue);
            }catch (Exception e){
                return getColorValueFromPluginResource(context,name,defaultColorId);
            }
        }

        return getColorValueFromPluginResource(context,name,defaultColorId);
    }

    private static String getColorValueFromHostAsset(String name){
            String colorValue = "";
            if (colorMap != null){
                colorValue =  colorMap.get(name);
            }
            return colorValue;
    }

    private static int getColorValueFromPluginResource(Context context,String name,int defaultColorId){
        try {
            return context.getResources().getColor(SqResUtils.getColorId(context, name));
        } catch (Exception e) {
            return defaultColorId;
        }
    }

    public static Drawable getDrawable(Context context, String srcName) {
        Drawable drawable = getDrawableHostAsset(context, srcName+imgpostfix);
        if (drawable == null) {
            drawable = getDrawablePluginAsset(context, srcName+imgpostfix);
            if (drawable == null) {
                drawable = getDrawablePlugin(context, srcName);
            }
        }
        return setEmptyDrawable(drawable);
    }

    public static boolean checkSelectorName(String srcName){
        return (srcName.endsWith(SkinHelper.idnamepostfix) && srcName.startsWith(SkinHelper.liveshow_ui_anchor));
    }
    private static Drawable setEmptyDrawable(Drawable drawable){
        if (drawable==null) {
            drawable = new BitmapDrawable();
        }
        return drawable;
    }

}
