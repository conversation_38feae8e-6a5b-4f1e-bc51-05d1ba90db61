package com.sqwan.liveshow.huya.bean;

import java.util.List;

public class LiveMenuBean {


    private List<ItemsBean> items;

    public List<ItemsBean> getItems() {
        return items;
    }

    public void setItems(List<ItemsBean> items) {
        this.items = items;
    }

    public static class ItemsBean {
        /**
         * platform_id : 0
         * platform_name : 热门推荐
         * icon_url : https://imgcs.s98s2.com/common/1624961311phpBoxCkj.png
         * rank : 1
         * anchor_white_list : 123,456
         * whitelist : ["123","456"]
         */

        private int platform_id;
        private String platform_name;
        private String icon_url;
        private int rank;
        private List<String> whitelist;

        public int getPlatform_id() {
            return platform_id;
        }

        public void setPlatform_id(int platform_id) {
            this.platform_id = platform_id;
        }

        public String getPlatform_name() {
            return platform_name;
        }

        public void setPlatform_name(String platform_name) {
            this.platform_name = platform_name;
        }

        public String getIcon_url() {
            return icon_url;
        }

        public void setIcon_url(String icon_url) {
            this.icon_url = icon_url;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }


        public List<String> getWhitelist() {
            return whitelist;
        }

        public void setWhitelist(List<String> whitelist) {
            this.whitelist = whitelist;
        }
    }
}
