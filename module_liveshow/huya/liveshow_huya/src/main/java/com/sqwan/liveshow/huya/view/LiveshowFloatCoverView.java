package com.sqwan.liveshow.huya.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.DisplayCutout;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.nbvideo.NBVideo;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.danmu.LiveshowDanmuManager;
import com.sqwan.liveshow.huya.danmu.view.InputAndCountDownView;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sqwan.liveshow.huya.skin.view.SkinImageView;
import com.sqwan.liveshow.huya.trackaction.LiveshowTrackManager;
import com.sy.window.WindowX;
import com.sy.window.draggable.SpringBackDraggable;

public class LiveshowFloatCoverView extends FrameLayout implements OnMethodListener, LiveshowDanmuManager.SendImLimitListener {

    private static final String ANIM_TRANSLATION_Y = "translationY";

    // 显示
    private final static int ANIMATOR_SHOW = 0;
    // 隐藏中
    private final static int ANIMATOR_HIDING = 1;
    // 隐藏
    private final static int ANIMATOR_HIDE = 2;
    // 隐藏
    private final static int ANIMATOR_SHOWING = 3;

    // 隐藏&显示的动画时间
    private final static long TOOLBAR_ANIMATOR_DURATION = 500;
    // 无操作时间隐藏时间
    private final static long AUTO_HIDE_TOOLBAR_DURATION = 5000;


    // 当前状态
    private int statusAnimator = ANIMATOR_SHOW;

    private FrameLayout mFlTopContainer;
    private ViewGroup mVBottomContainer;
    private ImageView mIvLiveSwitchWidth;
    private ImageView mIvLiveSwitchSound;
    private RatioFrameLayout mFlLiveContainer;
    private ImageView mIvLiveAnchorPortrait;
    private TextView mTvLiveAnchorNickname;
    private TextView mTvLiveAudienceCount;
    private ImageView mIvSmallLiveAnchorPortrait;
    private TextView mTvSmallLiveAnchorNickname;
    private TextView mTvSmallLiveAudienceCount;
    private View mFlRoot;
    private LinearLayout mLlSmallTopContainer;
    private ImageView mIvLiveEnlarge;
    private SkinImageView mIvTopContainerBg;


    private ImageView mIvSmallLiveClose;
    private SkinImageView mIvLiveBack;
    private AnimatorSet mHideToolbarAnimatorSet;
    private AnimatorSet mShowToolbarAnimatorSet;
    private ObjectAnimator mLiveLoadingAnimator;
    private InputAndCountDownView ll_input_and_countdownview;
    private SkinImageView iv_chatlist;
    private SkinImageView iv_danmu;
    private TextView iv_definition;
    private final Task mAnimatorTask;
    private View liveshowFloatView;

    private OnBehaviorListener mBehaviorListener;

    private RequestOptions mRequestOptions;

    private WindowX<?> mSoundWindow;

    public ImageView getmIvLiveSwitchWidth() {
        return mIvLiveSwitchWidth;
    }

    public ImageView getIvLiveSwitchSound() {
        return mIvLiveSwitchSound;
    }

    /**
     * 自动隐藏的toolbar的动画执行
     */
    private final Task.TaskFunc mAutoHideAnimatorRunnable = () -> {
        hideToolbar();
        return null;
    };


    /**
     * 隐藏动画的监听
     */
    private final AnimatorListenerAdapter mHideAnimatorListenerAdapter = new AnimatorListenerAdapter() {
        @Override
        public void onAnimationCancel(Animator animation) {
            super.onAnimationCancel(animation);
            statusAnimator = ANIMATOR_SHOW;
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            super.onAnimationEnd(animation);
            statusAnimator = ANIMATOR_HIDE;
        }

        @Override
        public void onAnimationStart(Animator animation) {
            super.onAnimationStart(animation);
            statusAnimator = ANIMATOR_HIDING;
        }
    };

    /**
     * 显示动画的监听
     */
    private final AnimatorListenerAdapter mShowAnimatorListenerAdapter = new AnimatorListenerAdapter() {
        @Override
        public void onAnimationCancel(Animator animation) {
            super.onAnimationCancel(animation);
            statusAnimator = ANIMATOR_HIDE;
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            super.onAnimationEnd(animation);
            statusAnimator = ANIMATOR_SHOW;
            resetAutoHide();
        }

        @Override
        public void onAnimationStart(Animator animation) {
            super.onAnimationStart(animation);
            statusAnimator = ANIMATOR_SHOWING;
        }
    };


    @Override
    public void addVideoFragmentContainer(View liveshowFloatView,View view) {
        this.liveshowFloatView = liveshowFloatView;
        mFlLiveContainer.removeAllViews();
        mFlLiveContainer.addView(view, 0);
    }

    @Override
    public void setAnchorInfo(String anchorPortraitUrl, String anchorNickname, String audienceCount) {
        initData(anchorPortraitUrl, anchorNickname, audienceCount);
    }

    @Override
    public void setOnBehaviorListener(OnBehaviorListener onBehaviorListener) {
        mBehaviorListener = onBehaviorListener;
    }

    /**
     * 切换成全屏
     */
    @Override
    public void switchFullLiveContainer() {
        MarginLayoutParams layoutParams = (MarginLayoutParams) mFlLiveContainer.getLayoutParams();
        layoutParams.topMargin = 0;
        layoutParams.leftMargin = 0;
        layoutParams.rightMargin = 0;
        layoutParams.bottomMargin = 0;
        mFlLiveContainer.setLayoutParams(layoutParams);
        mFlTopContainer.setVisibility(VISIBLE);
        mVBottomContainer.setVisibility(VISIBLE);
        mLlSmallTopContainer.setVisibility(GONE);
        mIvLiveEnlarge.setVisibility(GONE);
        if (mBehaviorListener != null) {
            mBehaviorListener.fullScreen();
        }
        startAutoHide();
    }

    @Override
    public void loading() {
    }

    @Override
    public void networkError() {
        showError();
    }

    @Override
    public void loadSuccess() {
        showSuccess();
    }

    /**
     * 切换成小屏
     */
    @Override
    public void switchSmallLiveContainer() {
        MarginLayoutParams layoutParams = (MarginLayoutParams) mFlLiveContainer.getLayoutParams();
        if (getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            layoutParams.topMargin = DisplayUtil.dip2px(getContext(), 20);
            layoutParams.leftMargin = DisplayUtil.dip2px(getContext(), 4);
            layoutParams.rightMargin = DisplayUtil.dip2px(getContext(), 4);
            layoutParams.bottomMargin = DisplayUtil.dip2px(getContext(), 4);
        } else {
            layoutParams.topMargin = DisplayUtil.dip2px(getContext(), 28);
            layoutParams.leftMargin = DisplayUtil.dip2px(getContext(), 8);
            layoutParams.rightMargin = DisplayUtil.dip2px(getContext(), 8);
            layoutParams.bottomMargin = DisplayUtil.dip2px(getContext(), 8);
        }
        mFlLiveContainer.setLayoutParams(layoutParams);
        // 设置边距为0
        mFlLiveContainer.setPadding(0, 0, 0, 0);
        mFlTopContainer.setVisibility(GONE);
        mVBottomContainer.setVisibility(GONE);
        mLlSmallTopContainer.setVisibility(VISIBLE);
        mIvLiveEnlarge.setVisibility(VISIBLE);
        if (mBehaviorListener != null) {
            mBehaviorListener.smallScreen();
        }
    }

    public LiveshowFloatCoverView(@NonNull Context context) {
        this(context, null);
    }

    public LiveshowFloatCoverView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LiveshowFloatCoverView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        mAnimatorTask = Task.create();
        LiveshowDanmuManager.getInstance().unRegSendImLimitListener(this);
        LiveshowDanmuManager.getInstance().regSendImLimitListener(this);
        post(this::startAutoHide);
    }

    private void initData(String anchorPortraitUrl, String anchorNickname, String audienceCount) {
        if (mRequestOptions == null) {
            //内存缓存
            mRequestOptions = RequestOptions.circleCropTransform()//设置圆形图片
                    //以下是额外设置一些配置
                    .diskCacheStrategy(DiskCacheStrategy.NONE)//磁盘缓存
                    .placeholder(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_default_portrait))
                    .error(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_default_portrait))
                    .skipMemoryCache(true);
        }
        Glide.with(getContext())
                .load(anchorPortraitUrl)
                .apply(mRequestOptions).into(mIvLiveAnchorPortrait);

        mTvLiveAnchorNickname.setText(anchorNickname);
        mTvLiveAudienceCount.setText(audienceCount);

        Glide.with(getContext())
                .load(anchorPortraitUrl)
                .apply(mRequestOptions).into(mIvSmallLiveAnchorPortrait);

        mTvSmallLiveAnchorNickname.setText(anchorNickname);
        mTvSmallLiveAudienceCount.setText(audienceCount);
    }

    private void initView() {
        mFlRoot = LayoutInflater.from(getContext()).inflate(SqResUtils.getLayoutId(getContext(), SqR.layout.sy37_liveshow_float_cover_view), this, false);
        addView(mFlRoot, new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        mLlSmallTopContainer = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.ll_small_top_container));
        // 放大
        mIvLiveEnlarge = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_icon_enlarge));
        mFlTopContainer = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.fl_sy37_liveshow_room_top_layout));

        mIvTopContainerBg = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_room_top_bg));
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            mIvTopContainerBg.setImageDrawable(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_room_top_bg));
        } else {
            mIvTopContainerBg.setBackground(SqR.drawable.sy37_liveshow_room_top_bg_long, SqR.drawable.sy37_liveshow_room_top_bg);
        }
        mIvTopContainerBg.setSelected(!LiveshowDanmuManager.getInstance().isImListShow());

        ViewGroup flTopContent = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.fl_sy37_liveshow_room_top_content));

        if (getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            int safeInsetTop = getSafeInsetTop();
            ViewGroup.LayoutParams layoutParams = flTopContent.getLayoutParams();
            if (layoutParams instanceof MarginLayoutParams) {
                ((MarginLayoutParams) layoutParams).topMargin = safeInsetTop;
                flTopContent.setLayoutParams(layoutParams);
            }
        }

        mIvLiveAnchorPortrait = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_live_anchor_portrait));
        mTvLiveAnchorNickname = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.tv_live_anchor_nickname));
        mTvLiveAudienceCount = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.tv_live_audience_count));
        mIvSmallLiveAnchorPortrait = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_small_live_anchor_portrait));
        mTvSmallLiveAnchorNickname = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.tv_small_live_anchor_nickname));
        mTvSmallLiveAudienceCount = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.tv_small_live_audience_count));
        mVBottomContainer = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.v_sy37_liveshow_bottom_bg));
        mIvLiveSwitchSound = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_live_switch_sound));
        mIvLiveSwitchWidth = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_live_switch_width));
        mFlLiveContainer = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.fl_live_container));

        mIvSmallLiveClose = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_icon_small_close));
        mIvLiveBack = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_icon_top_back));
        ll_input_and_countdownview = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.ll_input_and_countdownview));
        iv_definition = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_definition));
        iv_chatlist = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_chatlist));
        iv_chatlist.setBackground(SqR.drawable.sy37_liveshow_ic_imlist_open, SqR.drawable.sy37_liveshow_ic_imlist_close);
        iv_chatlist.setSelected(LiveshowDanmuManager.getInstance().isImListShow());
        iv_danmu = mFlRoot.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_danmu));
        iv_danmu.setBackground(SqR.drawable.sy37_liveshow_ic_danmu_open, SqR.drawable.sy37_liveshow_ic_danmu_close);
        iv_danmu.setSelected(LiveshowDanmuManager.getInstance().isDanmuShow());
        mIvLiveSwitchSound.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(final View v) {
                if (liveshowFloatView == null) {
                    return;
                }

                final Context context = getContext();
                if (!(context instanceof Activity)) {
                    return;
                }

                Activity activity = (Activity) context;

                WindowManager windowManager = activity.getWindowManager();
                DisplayMetrics displayMetrics = new DisplayMetrics();
                windowManager.getDefaultDisplay().getMetrics(displayMetrics);
                int screenWidth = displayMetrics.widthPixels;
                int screenHeight = displayMetrics.heightPixels;

                if (mSoundWindow != null && mSoundWindow.isShowing()) {
                    mSoundWindow.cancel();
                }

                mSoundWindow = new WindowX<>(activity)
                        .setContentView(SqResUtils.getLayoutId(context, SqR.layout.sy37_layout_sound_float_window))
                        .setGravity(Gravity.TOP | Gravity.END)
                        .setYOffset(screenHeight / 4 * 3)
                        // 设置指定的拖拽规则
                        .setDraggable(new SpringBackDraggable() {

                            private View backgroundView;
                            private Drawable leftSemicircleDrawable;
                            private Drawable rightSemicircleDrawable;
                            private Drawable circleDrawable;

                            @Override
                            public void updateWindowCoordinate(int x, int y) {
                                super.updateWindowCoordinate(x, y);
                                if (backgroundView == null) {
                                    backgroundView = getWindowX().findViewById(SqResUtils.getId(getContext(), SqR.id.bg_sound_float_record));
                                }
                                if (leftSemicircleDrawable == null) {
                                    leftSemicircleDrawable = ContextCompat.getDrawable(getContext(),
                                        SqResUtils.getDrawableId(getContext(), SqR.drawable.sy37_liveshow_bg_sound_float_left_semicircle));
                                }
                                if (rightSemicircleDrawable == null) {
                                    rightSemicircleDrawable = ContextCompat.getDrawable(getContext(),
                                        SqResUtils.getDrawableId(getContext(), SqR.drawable.sy37_liveshow_bg_sound_float_right_semicircle));
                                }
                                if (circleDrawable == null) {
                                    circleDrawable = ContextCompat.getDrawable(getContext(),
                                        SqResUtils.getDrawableId(getContext(), SqR.drawable.sy37_liveshow_bg_sound_float_circle));
                                }
                                if (x == 0) {
                                    backgroundView.setBackground(leftSemicircleDrawable);
                                } else if (x == (getWindowWidth() - getDecorView().getWidth())) {
                                    backgroundView.setBackground(rightSemicircleDrawable);
                                } else {
                                    backgroundView.setBackground(circleDrawable);
                                }
                            }
                        })
                        .setOnClickListener((toast, view) -> toast.cancel())
                        .setOnWindowLifecycle(new WindowX.OnWindowLifecycle() {

                            private ObjectAnimator rotationAnimator;

                            @Override
                            public void onWindowShow(WindowX<?> window) {
                                if (LiveshowDanmuManager.getInstance().isImListShow()) {
                                    LiveshowTrackManager.getInstance().trackHalfScreenOffAction();
                                } else {
                                    LiveshowTrackManager.getInstance().trackFullScreenOffAction();
                                }
                                LiveshowTrackManager.getInstance().trackListeningOnAction();

                                if (liveshowFloatView == null) {
                                    return;
                                }

                                // 隐藏直播列表
                                RoomListView roomListView = LiveshowManager.getInstance().getRoomListView();
                                if (roomListView != null) {
                                    roomListView.release();
                                }

                                ValueAnimator alphaAnimator = ValueAnimator.ofFloat(1f, 0f);
                                alphaAnimator.setDuration(200);
                                alphaAnimator.addUpdateListener(animation -> {
                                    float value = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setAlpha(value);
                                });
                                alphaAnimator.addListener(new AnimatorListenerAdapter() {
                                    @Override
                                    public void onAnimationEnd(Animator animation) {
                                        liveshowFloatView.setVisibility(GONE);
                                        NBVideo playView = liveshowFloatView.findViewById(SqResUtils.getId(getContext(), SqR.id.playerview));
                                        if (playView == null) {
                                            return;
                                        }
                                        playView.setOnlySoundEnable(!playView.isOnlySoundEnable());
                                    }
                                });
                                alphaAnimator.start();

                                ValueAnimator scaleAnimator = ValueAnimator.ofFloat(1f, 0f);
                                scaleAnimator.setDuration(250);
                                scaleAnimator.addUpdateListener(animation -> {
                                    float value = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setScaleX(value);
                                    liveshowFloatView.setScaleY(value);
                                });
                                scaleAnimator.start();

                                ValueAnimator translationXAnimator = ValueAnimator.ofFloat(0, screenWidth / 2f);
                                translationXAnimator.setDuration(250);
                                translationXAnimator.addUpdateListener(animation -> {
                                    float x = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setTranslationX(x);
                                });
                                translationXAnimator.start();

                                ValueAnimator translationYAnimator = ValueAnimator.ofFloat(0, window.getWindowParams().y / 2f);
                                translationYAnimator.setDuration(250);
                                translationYAnimator.addUpdateListener(animation -> {
                                    float y = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setTranslationY(y);
                                });
                                translationYAnimator.start();


                                SkinImageView imageView = window.findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sound_float_record));
                                if (imageView == null) {
                                    return;
                                }
                                imageView.setImageDrawable(ContextCompat.getDrawable(getContext(),
                                        SqResUtils.getDrawableId(getContext(), SqR.drawable.sy37_liveshow_icon_record)));
                                rotationAnimator = ObjectAnimator.ofFloat(imageView, "rotation", 0f, 360f);
                                rotationAnimator.setDuration(5000);
                                rotationAnimator.setRepeatCount(ValueAnimator.INFINITE);
                                rotationAnimator.setInterpolator(new LinearInterpolator());
                                rotationAnimator.start();
                            }

                            @Override
                            public void onWindowCancel(WindowX<?> window) {
                                LiveshowTrackManager.getInstance().trackListeningOffAction();
                                if (LiveshowDanmuManager.getInstance().isImListShow()) {
                                    LiveshowTrackManager.getInstance().trackHalfScreenOnAction();
                                } else {
                                    LiveshowTrackManager.getInstance().trackFullScreenOnAction();
                                }

                                if (rotationAnimator != null) {
                                    rotationAnimator.cancel();
                                }

                                if (liveshowFloatView == null) {
                                    return;
                                }

                                liveshowFloatView.setVisibility(VISIBLE);

                                // 显示直播列表
                                RoomListView roomListView = LiveshowManager.getInstance().getRoomListView();
                                if (roomListView != null) {
                                    roomListView.addView();
                                }

                                ValueAnimator alphaAnimator = ValueAnimator.ofFloat(0f, 1f);
                                alphaAnimator.setDuration(200);
                                alphaAnimator.addUpdateListener(animation -> {
                                    float value = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setAlpha(value);
                                });
                                alphaAnimator.start();

                                ValueAnimator scaleAnimator = ValueAnimator.ofFloat(0f, 1f);
                                scaleAnimator.setDuration(250);
                                scaleAnimator.addUpdateListener(animation -> {
                                    float value = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setScaleX(value);
                                    liveshowFloatView.setScaleY(value);
                                });
                                scaleAnimator.start();

                                ValueAnimator translationXAnimator = ValueAnimator.ofFloat(liveshowFloatView.getTranslationX(), 0);
                                translationXAnimator.setDuration(250);
                                translationXAnimator.addUpdateListener(animation -> {
                                    float x = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setTranslationX(x);
                                });
                                translationXAnimator.start();

                                ValueAnimator translationYAnimator = ValueAnimator.ofFloat(liveshowFloatView.getTranslationY(), 0);
                                translationYAnimator.setDuration(250);
                                translationYAnimator.addUpdateListener(animation -> {
                                    float y = (float) animation.getAnimatedValue();
                                    liveshowFloatView.setTranslationY(y);
                                });
                                translationYAnimator.start();

                                NBVideo playView = liveshowFloatView.findViewById(SqResUtils.getId(getContext(), SqR.id.playerview));
                                if (playView != null) {
                                    playView.setOnlySoundEnable(false);
                                }
                            }

                            @Override
                            public void onWindowRecycler(WindowX<?> window) {
                                rotationAnimator = null;
                            }
                        });
                mSoundWindow.show();
            }
        });
        mIvLiveSwitchWidth.setOnClickListener(v -> switchSmallLiveContainer());
        mFlLiveContainer.setOnClickListener(v -> {
            if (statusAnimator == ANIMATOR_SHOW || statusAnimator == ANIMATOR_SHOWING) {
                endAnimatorAndHideToolbar();
            } else {
                showToolbar();
            }
        });
        mIvLiveEnlarge.setOnClickListener(v -> switchFullLiveContainer());

        mIvSmallLiveClose.setOnClickListener(v -> {
            // 小屏关闭直播
            if (mBehaviorListener != null) {
                mBehaviorListener.levelLive();
            }
        });

        mIvLiveBack.setImageDrawable(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_top_back));
        mIvLiveBack.setOnClickListener(v -> {
            // 如果是全屏播放，点击返回按钮要先退出全屏
            if (!iv_chatlist.isSelected()) {
                iv_chatlist.performClick();
                return;
            }
            // 全屏返回按钮
            if (mBehaviorListener != null) {
                mBehaviorListener.backLiveRoom();
            }
        });
        ll_input_and_countdownview.setOnClickListener(v -> {
            if (mBehaviorListener != null) {
                mBehaviorListener.clickInputChat();
            }
        });

        int inPutMessageTextColor = SkinHelper.getColorValue(getContext(), SqR.color.sy37_item_roast_view_tv_input_message_text_color, Color.WHITE);
        int countDownTextColor = SkinHelper.getColorValue(getContext(),SqR.color.sy37_item_roast_view_tv_countdown_text_color, Color.WHITE);
        ll_input_and_countdownview.getTvInputMessage().setTextColor(inPutMessageTextColor);
        ll_input_and_countdownview.getTvCountDown().setTextColor(countDownTextColor);

        iv_chatlist.setOnClickListener(v -> {
            iv_chatlist.setSelected(!iv_chatlist.isSelected());
            if (iv_chatlist.isSelected()) {
                ViewUtils.hidden(ll_input_and_countdownview);
                if (getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                    mFlLiveContainer.setSizeRatio(16, 9);
                    ViewGroup.LayoutParams layoutParams = mFlLiveContainer.getLayoutParams();
                    if (layoutParams instanceof RelativeLayout.LayoutParams) {
                        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                        ((RelativeLayout.LayoutParams) layoutParams).addRule(RelativeLayout.BELOW, mFlTopContainer.getId());
                        mFlLiveContainer.setLayoutParams(layoutParams);
                    }

                    ViewGroup parentLayout = (ViewGroup) mFlTopContainer.getParent();
                    parentLayout.removeView(mFlTopContainer);
                    parentLayout.addView(mFlTopContainer, 0);

                    mIvTopContainerBg.setImageDrawable(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_room_top_bg));
                    mIvLiveBack.setImageDrawable(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_top_back));
                }
            } else {
                endAutoHideAndShowToolbar();
                ViewUtils.show(ll_input_and_countdownview);
                if (getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                    mFlLiveContainer.setSizeRatio(0, 0);
                    ViewGroup.LayoutParams layoutParams = mFlLiveContainer.getLayoutParams();
                    if (layoutParams instanceof RelativeLayout.LayoutParams) {
                        layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
                        ((RelativeLayout.LayoutParams) layoutParams).removeRule(RelativeLayout.BELOW);
                        mFlLiveContainer.setLayoutParams(layoutParams);
                    }

                    ViewGroup parentLayout = (ViewGroup) mFlTopContainer.getParent();
                    parentLayout.removeView(mFlTopContainer);
                    parentLayout.addView(mFlTopContainer, parentLayout.getChildCount() - 1);

                    mIvTopContainerBg.setImageDrawable(null);
                    mIvLiveBack.setImageDrawable(SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_live_show_full_screen_back_icon));
                }
            }
            mIvTopContainerBg.setSelected(!iv_chatlist.isSelected());
            if (mBehaviorListener != null) {
                startAutoHide();
                mBehaviorListener.showChatList(iv_chatlist.isSelected());
            }
        });
        iv_danmu.setOnClickListener(v -> {
            iv_danmu.setSelected(!iv_danmu.isSelected());
            if (mBehaviorListener != null) {
                mBehaviorListener.showDanmu(iv_danmu.isSelected());
            }
        });
        iv_definition.setOnClickListener(v -> {
            if (mBehaviorListener != null) {
                mBehaviorListener.clickDefinition();
            }
        });
    }


    /**
     * 启动自动隐藏
     */
    public void startAutoHide() {
        mAnimatorTask.oneShot(AUTO_HIDE_TOOLBAR_DURATION, mAutoHideAnimatorRunnable);
    }

    /**
     * 关闭自动隐藏 同时 显示状态栏
     */
    private void endAutoHideAndShowToolbar() {
        mAnimatorTask.stop();
        showToolbar();
    }

    /**
     * 重置自动隐藏 显示状态下，再次点击显示的时候，需要重置 handler的倒计时
     */
    private void resetAutoHide() {
//        mHandler.removeCallbacks(mAutoHideAnimatorRunnable);
//        startAutoHide();
        mAnimatorTask.stop();
        mAnimatorTask.oneShot(AUTO_HIDE_TOOLBAR_DURATION, mAutoHideAnimatorRunnable);
    }

    private void endAnimatorAndHideToolbar() {
        mAnimatorTask.stop();
        hideToolbar();
    }

    private void hideToolbar() {
        if (statusAnimator == ANIMATOR_HIDING || statusAnimator == ANIMATOR_HIDE) {
            return;
        }
        if (statusAnimator == ANIMATOR_SHOWING) {
            if (mShowToolbarAnimatorSet != null && mShowToolbarAnimatorSet.isRunning()) {
                mShowToolbarAnimatorSet.cancel();
            }
        }
        hideToolbarAnimator();
    }

    private void hideToolbarAnimator() {
        // 小窗界面，不显示导航条的时候，获取不到高度，也不需要隐藏
        if (mFlTopContainer.getVisibility() != VISIBLE && mVBottomContainer.getVisibility() != VISIBLE) {
            return;
        }
        mHideToolbarAnimatorSet = new AnimatorSet();
        // 保留一个像素，让屏幕中持续有显示这个布局，不移出到布局之外，防止旋转的时候，虎牙的那个view的奇奇怪怪的操作
        // 误差像素中，动画执行完，可以考虑执行布局隐藏
        // 其实布局中也有几个像素是透明的，所以，用户无法察觉到的。
        int revisePx = 1;
        ObjectAnimator topContainerOutAnimator = ObjectAnimator.ofFloat(mFlTopContainer,
                ANIM_TRANSLATION_Y, -mFlTopContainer.getHeight() + revisePx);
        ObjectAnimator bottomContainerOutAnimator = ObjectAnimator.ofFloat(mVBottomContainer,
                ANIM_TRANSLATION_Y, mVBottomContainer.getHeight() - revisePx);
        if (isImListShow()) {
            if (!isShowGuide()) {
                mHideToolbarAnimatorSet.playTogether(bottomContainerOutAnimator);
            }
        } else {
            mHideToolbarAnimatorSet.playTogether(topContainerOutAnimator, bottomContainerOutAnimator);
        }
        mHideToolbarAnimatorSet.addListener(mHideAnimatorListenerAdapter);
        mHideToolbarAnimatorSet.setDuration(TOOLBAR_ANIMATOR_DURATION);
        // 正在运行
        if (mHideToolbarAnimatorSet.isRunning()) {
            return;
        }
        mHideToolbarAnimatorSet.start();
    }

    private void showToolbar() {
        // 已经在显示状态或者显示动画中的时候直接跳出了
        if (statusAnimator == ANIMATOR_SHOW || statusAnimator == ANIMATOR_SHOWING) {
            // 时候需要重置自动消失的时间
            resetAutoHide();
            return;
        }
        // 消失动画中，直接取消
        if (statusAnimator == ANIMATOR_HIDING) {
            if (mHideToolbarAnimatorSet != null && mHideToolbarAnimatorSet.isRunning()) {
                mHideToolbarAnimatorSet.cancel();
            }
        }
        showToolbarAnimator();
    }

    private void showToolbarAnimator() {
        // 小窗界面，不显示导航条的时候，获取不到高度，也不需要隐藏
        if (mFlTopContainer.getVisibility() != VISIBLE && mVBottomContainer.getVisibility() != VISIBLE) {
            return;
        }
        mShowToolbarAnimatorSet = new AnimatorSet();
        ObjectAnimator topContainerInAnimator = ObjectAnimator.ofFloat(mFlTopContainer, ANIM_TRANSLATION_Y, 0);
        ObjectAnimator bottomContainerInAnimator = ObjectAnimator.ofFloat(mVBottomContainer, ANIM_TRANSLATION_Y, 0);
        if (isImListShow()) {
            mShowToolbarAnimatorSet.playTogether(bottomContainerInAnimator);
        } else {
            mShowToolbarAnimatorSet.playTogether(topContainerInAnimator, bottomContainerInAnimator);
        }
        mShowToolbarAnimatorSet.addListener(mShowAnimatorListenerAdapter);
        mShowToolbarAnimatorSet.setDuration(TOOLBAR_ANIMATOR_DURATION);
        // 正在运行
        if (mShowToolbarAnimatorSet.isRunning()) {
            return;
        }
        mShowToolbarAnimatorSet.start();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        recycle();
    }

    /**
     * 释放资源
     */
    public void recycle() {
        if (mAnimatorTask != null && mAnimatorTask.isRunning()) {
            mAnimatorTask.stop();
        }
        if (mHideToolbarAnimatorSet != null) {
            mHideToolbarAnimatorSet.removeAllListeners();
            mHideToolbarAnimatorSet.cancel();
        }

        if (mShowToolbarAnimatorSet != null) {
            mShowToolbarAnimatorSet.removeAllListeners();
            mShowToolbarAnimatorSet.cancel();
        }
        if (mLiveLoadingAnimator != null) {
            mLiveLoadingAnimator.cancel();
            mLiveLoadingAnimator = null;
        }
        if (mSoundWindow != null) {
            mSoundWindow.recycle();
            mSoundWindow = null;
        }
        LiveshowDanmuManager.getInstance().unRegSendImLimitListener(this);
    }

    /**
     * 网络异常
     */
    public void showError() {
    }

    /**
     * 网络恢复
     */
    public void showSuccess() {
    }

    public boolean isImListShow() {
        return iv_chatlist.isSelected();
    }

    public boolean isDanmuShow() {
        return iv_danmu.isSelected();
    }

    public TextView getIv_definition() {
        return iv_definition;
    }

    @Override
    public void onCountDown(boolean finish, String time) {
        ll_input_and_countdownview.setEnabled(finish);
        if (!finish) {
            ll_input_and_countdownview.getTvCountDown().setText(String.format("%ss", time));
        } else {
            ll_input_and_countdownview.getTvCountDown().setText("");
        }
    }

    public InputAndCountDownView getLl_input_and_countdownview() {
        return ll_input_and_countdownview;
    }

    public FrameLayout getFlTopContainer() {
        return mFlTopContainer;
    }

    public ViewGroup getVBottomContainer() {
        return mVBottomContainer;
    }

    private boolean isShowGuide(){
        if (liveshowFloatView!=null) {
            if (liveshowFloatView instanceof LiveshowFloatView) {
                if (ViewUtils.isShow(((LiveshowFloatView)liveshowFloatView).guideView)) {
                    return true;
                }
            }
        }
        return false;
    }

    public int getSafeInsetTop() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
            return 0;
        }

        Context context = getContext();
        if (!(context instanceof Activity)) {
            return 0;
        }
        Activity activity = ((Activity) context);
        Window window = activity.getWindow();
        if (window == null) {
            return 0;
        }
        View decorView = window.getDecorView();
        if (decorView == null) {
            return 0;
        }
        WindowInsets windowInsets = decorView.getRootWindowInsets();
        if (windowInsets == null) {
            return 0;
        }
        DisplayCutout displayCutout = windowInsets.getDisplayCutout();
        if (displayCutout == null) {
            return 0;
        }
        return displayCutout.getSafeInsetTop();
    }
}
