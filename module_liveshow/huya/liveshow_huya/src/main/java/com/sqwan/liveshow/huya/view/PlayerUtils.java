package com.sqwan.liveshow.huya.view;

import android.os.Build;
import android.text.TextUtils;

import com.huya.berry.client.customui.model.BitRateInfo;
import com.huya.berry.client.customui.model.LiveInfo;
import com.huya.berry.gamesdk.wup.WupHelper;
import com.nbvideo.VideoInfo;
import com.sqwan.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-23 17:33
 */
public class PlayerUtils {
    private static String testUrl = "https://txdirect.hls.huya.com/huyalive/1704199086-1704199086-7319479340243091456-3408521628-10057-A-0-1.m3u8?wsSecret=1698b58c2a40f4bfafb765b34982e877&wsTime=60fbb7e6&u=0&seqid=16270229498540&ctype=huya_adr_game_sdk&txyp=o%3Ac8%3B&fs=bgct&ratio=2000";
    private static final String TAG = "PlayerUtils";
    private static  List<String> disPlayNames = Arrays.asList("蓝光", "蓝光4M", "蓝光5M","蓝光6M","蓝光6M","蓝光8M","蓝光9M","蓝光10M","超清", "高清", "流畅");
    private static  List<String> disPlayNamesLowVersion = Arrays.asList("流畅","高清", "超清", "蓝光", "蓝光4M", "蓝光8M","蓝光5M","蓝光6M","蓝光6M","蓝光8M","蓝光9M","蓝光10M");
    public static List<String> getLines(LiveInfo liveInfo){
        List<String> disPlayNames= new ArrayList<>();
        for (Integer line : liveInfo.getLines()) {
            LogUtil.i(TAG, "line:" + line);
            for (BitRateInfo bitRateInfo : liveInfo.getBitRateList(line)){
                String disPlayName = bitRateInfo.disPlayName;
                if (!disPlayNames.contains(disPlayName)) {
                    disPlayNames.add(disPlayName);
                }
            }
        }
        return disPlayNames;
    }

    public static VideoInfo matchLine(LiveInfo liveInfo,String target){
        List<String> _disPlayNames = null;
        if (target!=null) {
            _disPlayNames = new ArrayList<>();
            _disPlayNames.add(target);
        }else{
            if (isLowVideoViewSdkVersion()) {
                _disPlayNames = disPlayNamesLowVersion;
            }else{
                _disPlayNames = disPlayNames;
            }
        }
        for (String disPlayName : _disPlayNames) {
            LogUtil.i(TAG, "disPlayName:" + disPlayName);
            for (Integer line : liveInfo.getLines()) {
                LogUtil.i(TAG, "line:" + line);
                for (BitRateInfo bitRateInfo : liveInfo.getBitRateList(line)) {
                    LogUtil.i(TAG, "bitRateInfo:" + bitRateInfo.toString());
                    if (TextUtils.equals(disPlayName, bitRateInfo.disPlayName)) {
                        int bitRate = bitRateInfo.bitRate;
                        LogUtil.i(TAG, "bitRate:" + bitRate);
                        if (bitRate != 0) {
                            String url = liveInfo.getPlayUrlByLineAndBitrate(false, line, bitRate);
                            LogUtil.i(TAG, "url:" + url);
                            if (!TextUtils.isEmpty(url)) {
                                Map<String,String> headerMap = new HashMap();
                                String _userAgent = WupHelper.getSHuYaUA();
                                LogUtil.i(TAG, "userAgent:" + _userAgent);
                                headerMap.put("User-Agent", _userAgent);
//                                url = testUrl;
                                return new VideoInfo(disPlayName,url,headerMap);

                            }
                        }
                    }
                }
            }
        }
        return null;
    }



    public static VideoInfo matchLine(LiveInfo liveInfo,String target,ArrayList<String> hasLoadUrl){
        List<String> _disPlayNames = null;
        if (target!=null) {
            _disPlayNames = new ArrayList<>();
            _disPlayNames.add(target);
        }else{
            if (isLowVideoViewSdkVersion()) {
                _disPlayNames = disPlayNamesLowVersion;
            }else{
                _disPlayNames = disPlayNames;
            }
        }
        for (String disPlayName : _disPlayNames) {
            LogUtil.i(TAG, "disPlayName:" + disPlayName);
            for (Integer line : liveInfo.getLines()) {
                LogUtil.i(TAG, "line:" + line);
                for (BitRateInfo bitRateInfo : liveInfo.getBitRateList(line)) {
                    LogUtil.i(TAG, "bitRateInfo:" + bitRateInfo.toString());
                    if (TextUtils.equals(disPlayName, bitRateInfo.disPlayName)) {
                        int bitRate = bitRateInfo.bitRate;
                        LogUtil.i(TAG, "bitRate:" + bitRate);
                        if (bitRate != 0) {
                            String url = liveInfo.getPlayUrlByLineAndBitrate(false, line, bitRate);
                            LogUtil.i(TAG, "url:" + url);

                            if (hasLoadUrl != null && hasLoadUrl.size() > 0){
                                if (hasLoadUrl.contains(url)) continue;
                            }

                            if (!TextUtils.isEmpty(url)) {
                                Map<String,String> headerMap = new HashMap();
                                String _userAgent = WupHelper.getSHuYaUA();
                                LogUtil.i(TAG, "userAgent:" + _userAgent);
                                headerMap.put("User-Agent", _userAgent);
//                                url = testUrl;
                                return new VideoInfo(disPlayName,url,headerMap);

                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    private static boolean isLowVideoViewSdkVersion(){
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.M;
    }
}
