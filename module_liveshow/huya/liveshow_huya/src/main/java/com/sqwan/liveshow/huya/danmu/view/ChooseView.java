package com.sqwan.liveshow.huya.danmu.view;

import android.content.Context;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.danmu.adpter.ChooseAdapter;
import com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout;

import java.util.List;

public class ChooseView extends SkinRelativeLayout{

    private ChooseAdapter chooseAdapter;

    public ChooseView(Context context) {
        this(context,null);
    }

    public ChooseView(Context context, AttributeSet attrs) {
        this(context,attrs,0);
    }

    public ChooseView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public ChooseView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }

    private void initView(Context context){
        View.inflate(context, SqResUtils.getLayoutId(context, SqR.layout.sy37_choose_view),this);
        RecyclerView recyclerView = findViewById(SqResUtils.getId(context, SqR.id.rl_choose_view));
        chooseAdapter = new ChooseAdapter(context);
        final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setAdapter(chooseAdapter);
    }

    public void setData(List<String> list,String dis){
        chooseAdapter.setDataList(list,dis);
    }

    public void setOnclickItemListener(ChooseAdapter.ItemClickListener itemClickListener){
        chooseAdapter.setItemClickListener(itemClickListener);
    }


}
