package com.sqwan.liveshow.huya.skin.manager;

import android.content.Context;
import android.support.v4.view.LayoutInflaterCompat;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.view.LayoutInflater;

import com.sqwan.liveshow.huya.danmu.view.ChooseView;
import com.sqwan.liveshow.huya.danmu.view.InputAndCountDownView;
import com.sqwan.liveshow.huya.danmu.view.InputView;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;
import com.sqwan.liveshow.huya.skin.inflater.SkinInflaterFactory;
import com.sqwan.liveshow.huya.skin.view.SkinEditText;
import com.sqwan.liveshow.huya.skin.view.SkinFrameLayout;
import com.sqwan.liveshow.huya.skin.view.SkinImageView;
import com.sqwan.liveshow.huya.skin.view.SkinLinearLayout;
import com.sqwan.liveshow.huya.skin.view.SkinRelativeLayout;
import com.sqwan.liveshow.huya.skin.view.SkinTextView;

import java.util.Map;

/**
 * Created by znb on 2021-10-20
 */
public class SkinManager {
    public static final String TAG = "SkinManager";


    public static SkinManager getInstance() {
        return Singleton.INSTANCE;
    }

    private static class Singleton {
        private static final SkinManager INSTANCE = new SkinManager();
    }

    /**
     * View转化映射关系
     */
    private Map<String, String> skinInflaterMap = new ArrayMap<>();
    /**
     * 注册可以进行换肤的自定义View，该View实现 {@link SkinViewInterface}
     * @param viewName view的名称
     * @return
     */
    public SkinManager registSkinViewConvertMap(String viewName) {
        if (!TextUtils.isEmpty(viewName)) {
            if (!skinInflaterMap.containsKey(viewName)) {
                skinInflaterMap.put(viewName, viewName);
            }
        }
        return this;
    }

    /**
     * 该view是否可以转化为换肤View
     * @param name view的名称
     * @return
     */
    public boolean canSkinViewConvert(String name) {
        return skinInflaterMap.containsKey(name);
    }

    /**
     * 该view是否可以转化为换肤View
     * @param name view的名称
     * @return
     */
    public String findSkinInflaterViewClassName(String name) {
        return skinInflaterMap.get(name);
    }
    public void inject(Context context){
        registSkinViewConvertMap(SkinTextView.class.getName());
        registSkinViewConvertMap(SkinImageView.class.getName());
        registSkinViewConvertMap(SkinEditText.class.getName());
        registSkinViewConvertMap(SkinRelativeLayout.class.getName());
        registSkinViewConvertMap(SkinLinearLayout.class.getName());
        registSkinViewConvertMap(SkinFrameLayout.class.getName());
        registSkinViewConvertMap(InputAndCountDownView.class.getName());
        registSkinViewConvertMap(ChooseView.class.getName());
        registSkinViewConvertMap(InputView.class.getName());
        LayoutInflaterCompat.setFactory2(LayoutInflater.from(context),new SkinInflaterFactory());
    }
}
