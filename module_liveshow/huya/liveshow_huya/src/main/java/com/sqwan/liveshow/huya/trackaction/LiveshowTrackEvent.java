package com.sqwan.liveshow.huya.trackaction;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-19 17:16
 */
public enum LiveshowTrackEvent {
    BUILTIN_LIVE_ICON("builtin_live_icon","入口点击"),

    BUILTIN_LIVE_WATCH("builtin_live_watch","进入直播间"),

    BUILTIN_LIVE_OFF("builtin_live_off","退出直播间"),

    BULLET_CHAT("bullet_chat","弹幕发言"),

    BUILTIN_LIVE_HALF_OFF("builtin_live_half_off","退出半屏模式直播间"),

    BUILTIN_LIVE_HALF_WATCH("builtin_live_half_watch","进入半屏模式直播间"),

    BUILTIN_LIVE_LISTENING("builtin_live_listening","进入仅听声音模式"),

    BUILTIN_LIVE_LISTENING_OFF("builtin_live_listening_off","退出仅听声音模式");

    private String event;
    private String name;

    LiveshowTrackEvent(String event, String name) {
        this.event = event;
        this.name = name;
    }

    public String getEvent() {
        return event;
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "LiveshowTrackEvent{" +
                "event='" + event + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
