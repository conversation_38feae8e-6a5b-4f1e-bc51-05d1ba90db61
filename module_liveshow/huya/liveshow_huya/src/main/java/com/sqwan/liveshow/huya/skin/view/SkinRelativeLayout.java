package com.sqwan.liveshow.huya.skin.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.RelativeLayout;

import com.sqwan.liveshow.huya.skin.attr.SkinBackgroundAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;

/**
 * Created by znb on 2021-10-20
 */
public class SkinRelativeLayout extends RelativeLayout implements SkinViewInterface {
    private SkinBackgroundAttr skinBackgroundAttr;
    public SkinRelativeLayout(Context context) {
        this(context, null);
    }

    public SkinRelativeLayout(Context context,AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public SkinRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, -1);
    }

    public SkinRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        skinBackgroundAttr = new SkinBackgroundAttr(this, attrs);
    }

    @Override
    public void setBackgroundResource(int resid) {
        if (skinBackgroundAttr!=null) {
            skinBackgroundAttr.setBackgroundResource(resid);
        }
    }

    @Override
    public void applySkin() {
        if (skinBackgroundAttr!=null) {
            skinBackgroundAttr.applySkin();
        }
    }
}