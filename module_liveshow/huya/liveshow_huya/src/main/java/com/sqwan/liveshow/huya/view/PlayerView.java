package com.sqwan.liveshow.huya.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.nbvideo.NBVideo;
import com.nbvideo.VideoCallback;
import com.nbvideo.VideoInfo;
import com.nbvideo.VideoManager;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.CheckNetwork;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.engine.LiveInfoEx;
import com.sqwan.liveshow.huya.engine.LiveshowManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-09 15:22
 */
public class PlayerView extends FrameLayout implements VideoCallback {
    private static final String TAG = "PlayerView";
    // 加载的动画时间周期
    private final static int LIVE_LOADING_ANIMATION_DURATION = 1000;
    private Context mContext;
    private LinearLayout mLlLiveNetworkError;
    private ImageView mIvLiveLoading;
    private ObjectAnimator mLiveLoadingAnimator;
    private NBVideo heartVideo;

    private ActivityLifeCycleUtils.AppVisibilityCallback appVisibilityCallback = new ActivityLifeCycleUtils.AppVisibilityCallbackAdapter(){
        @Override
        public void onBackground() {
            if (heartVideo!=null) {
                LogUtil.d(TAG, "onBackground: pause");
                heartVideo.pause();
            }
        }

        @Override
        public void onForeground() {
            if (heartVideo!=null) {
                LogUtil.d(TAG, "onForeground: restart");
                heartVideo.restart();
            }
        }

        @Override
        public void onActivityStarted(Activity activity) {
            if ((mContext == activity)) {
                onForeground();
            }
        }

        @Override
        public void onActivityStopped(Activity activity) {
            if ((mContext == activity)) {
                onBackground();
            }

        }
    };
    private LiveInfoEx liveInfoEx;

    public PlayerView(@NonNull Context context) {
        this(context, null);
    }

    public PlayerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
        initClick();
        LiveshowManager.getInstance().setVisibilityCallback(appVisibilityCallback);
    }

    private void initClick() {
        if (mLlLiveNetworkError != null) {
            mLlLiveNetworkError.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (CheckNetwork.getInstance().hasNetwork()) {
                        LogUtil.i(TAG, "hasNetwork true");
                        hideErrorView();
                        liveLoading();
                        heartVideo.restart();
                    } else {
                        ToastUtil.showToast("暂无网络");
                        LogUtil.i(TAG, "hasNetwork false");
                    }

                }
            });
        }

    }


    private void initView() {
        inflate(mContext, SqResUtils.getLayoutId(mContext, SqR.layout.sy37_layout_playerview), this);
        heartVideo = findViewById(SqResUtils.getId(mContext, SqR.id.playerview));
        heartVideo.setVideoCallback(this);
        mLlLiveNetworkError = findViewById(SqResUtils.getId(getContext(), SqR.id.ll_live_network_error));
        mIvLiveLoading = findViewById(SqResUtils.getId(getContext(), SqR.id.iv_sy37_liveshow_loading));

    }

    public void getLiveShowRoomData() {
        liveLoading();
        if (liveInfoEx!=null) {
            VideoInfo videoInfo = liveInfoEx.videoInfo;
            if (videoInfo!=null) {
                heartVideo.setVideoInfo(videoInfo);
                heartVideo.start(loadingResultListener);
            }
        }
    }

    private Map<Long,ArrayList<String>> mapUrl = new HashMap<>();

    public void restart(final String target){
        if (this.liveInfoEx!=null) {
            VideoManager.getInstance().release();
            VideoInfo videoInfo = PlayerUtils.matchLine(this.liveInfoEx.liveInfo,target);
            this.liveInfoEx.videoInfo = videoInfo;
            start(liveInfoEx);
        }
    }

    private final NBVideo.LoadingResultListener loadingResultListener =  new NBVideo.LoadingResultListener() {
        @Override
        public void loadFail() {
            LogUtil.i(TAG, "视频无法播放，回调重新加载");
            VideoInfo videoInfo = PlayerUtils.matchLine(PlayerView.this.liveInfoEx.liveInfo,liveInfoEx.videoInfo.disPlayName,mapUrl.get(liveInfoEx.liveInfo.uid));
            PlayerView.this.liveInfoEx.videoInfo = videoInfo;
            if (videoInfo != null){
                if (mapUrl.get(liveInfoEx.liveInfo.uid) == null){
                    ArrayList<String> list = new ArrayList<>();
                    mapUrl.put(liveInfoEx.liveInfo.uid,list);
                }
                mapUrl.get(liveInfoEx.liveInfo.uid).add(videoInfo.getUrl());
                heartVideo.setVideoInfo(videoInfo);
                heartVideo.changeUrlMediaAndReset(loadingResultListener);
            }
        }
    };


    public void start(final LiveInfoEx liveInfoEx) {
        this.liveInfoEx = liveInfoEx;
        if (this.liveInfoEx.videoInfo != null){
            if (mapUrl.get(liveInfoEx.liveInfo.uid) != null){
                ArrayList<String> list = mapUrl.get(liveInfoEx.liveInfo.uid);
                String url = this.liveInfoEx.videoInfo.getUrl();
                list.add(url);
            }else{
                ArrayList<String> list = new ArrayList<>();
                String url = this.liveInfoEx.videoInfo.getUrl();
                list.add(url);
                mapUrl.put(liveInfoEx.liveInfo.uid,list);
            }
        }
        getLiveShowRoomData();
    }


    /**
     * 加载直播信息中
     */
    private void liveLoading() {
        mIvLiveLoading.setVisibility(VISIBLE);
        if (mLiveLoadingAnimator == null) {
            mLiveLoadingAnimator = ObjectAnimator.ofFloat(mIvLiveLoading, "rotation", 0, 360);
            mLiveLoadingAnimator.setDuration(LIVE_LOADING_ANIMATION_DURATION);
            mLiveLoadingAnimator.setRepeatCount(ValueAnimator.INFINITE);
            mLiveLoadingAnimator.setRepeatMode(ValueAnimator.RESTART);
        }
        if (mLiveLoadingAnimator.isRunning()) {
            return;
        }
        mLiveLoadingAnimator.start();
    }

    private void showErrorView() {
        ViewUtils.show(mLlLiveNetworkError);
    }

    private void hideErrorView() {
        ViewUtils.gone(mLlLiveNetworkError);
    }

    /**
     * 信息加载完成
     */
    public void liveLoaded() {
        mIvLiveLoading.setVisibility(GONE);
        if (mLiveLoadingAnimator != null) {
            mLiveLoadingAnimator.cancel();
        }
        mIvLiveLoading.setAnimation(null);
    }

    public void release() {
        VideoManager.getInstance().release();
    }

    @Override
    public void onError() {
        liveLoaded();
        showErrorView();

    }

    @Override
    public void onSuccess() {
        liveLoaded();
        hideErrorView();
    }

    @Override
    public void onLoading() {
        liveLoading();
    }

    @Override
    public void onPlaying() {
        liveLoaded();
        hideErrorView();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        LiveshowManager.getInstance().setVisibilityCallback(null);
    }
}
