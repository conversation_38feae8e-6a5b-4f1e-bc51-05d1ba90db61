package com.sqwan.liveshow.huya.request;

import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.msdk.config.MultiSdkManager;

public class LiveRoomUrl {
    //域名
    private static String LIVE_SHOW_HOST = "https://live-api" + MultiSdkManager.SECURE_SUFFIX + "37.com.cn";

    private static String LIVE_SHOW_ROOM_HOST = "https://room-api" + MultiSdkManager.SECURE_SUFFIX + "37.com.cn";

    private static String LIVE_SHOW_IM_HOST = "https://im-api" + MultiSdkManager.SECURE_SUFFIX + "37.com.cn";


    public static final String KEY_LIVE_PLATFORM = "live_platform";
    public static final String KEY_LIVE_SETTING = "live_setting";
    public static final String KEY_LIVE_RECOMMENDED_ANCHOR = "live_recommend";
    public static final String KEY_LIVE_ROOM_ENTER = "live_room_enter";
    public static final String KEY_LIVE_ROOM_LEAVE = "live_room_leave";
    public static final String KEY_LIVE_ROOM_IM_SEND = "live_send_danmu";
    public static final String KEY_LIVE_ROOM_IM_FETCH = "live_fetch_danmu";

    // 菜单配置
    @UrlUpdate(value = KEY_LIVE_PLATFORM, xValue = "x_live_platform")
    public static String LIVE_MENU_URL =  LIVE_SHOW_HOST + "/api/live-service/v1/video/platforms";

    // 虎牙SDK配置相关
    @UrlUpdate(value = KEY_LIVE_SETTING, xValue = "x_live_setting")
    public static String LIVE_CONFIG = LIVE_SHOW_HOST + "/api/live-service/v1/video/apps";

    // 获取直播列表
    @UrlUpdate(value = KEY_LIVE_RECOMMENDED_ANCHOR, xValue = "x_live_recommend")
    public static String LIVE_RECOMMENDED_ANCHOR = LIVE_SHOW_HOST + "/api/live-service/v1/video/anchors";

    //进入直播间
    @UrlUpdate(value = KEY_LIVE_ROOM_ENTER, xValue = "x_enter_live_room")
    public static String LIVE_ROOM_ENTER = LIVE_SHOW_ROOM_HOST + "/v1/room/enter";

    //退出直播间
    @UrlUpdate(value = KEY_LIVE_ROOM_LEAVE, xValue = "x_leave_live_room")
    public static String LIVE_ROOM_LEAVE = LIVE_SHOW_ROOM_HOST + "/v1/room/leave";

    //直播间用户发送公屏
    @UrlUpdate(value = KEY_LIVE_ROOM_IM_SEND, xValue = "x_send_danma")
    public static String LIVE_ROOM_IM_SEND = LIVE_SHOW_IM_HOST + "/v1/im/send";

    //获取直播间公屏
    @UrlUpdate(value = KEY_LIVE_ROOM_IM_FETCH, xValue = "x_fetch_danma")
    public static String LIVE_ROOM_IM_FETCH = LIVE_SHOW_IM_HOST + "/v1/im/fetch";


}
