package com.sqwan.liveshow.huya.request;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.SendImRspBean;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 10:20
 */
public class DanmuImRequestManager extends HyLiveshowBaseRequestManager {

    public DanmuImRequestManager(Context context) {
        super(context);
    }

    protected Map<String, String> generateBaseParams(Map<String, String> params) {
        Map<String, String> baseParams = generateBaseParams();
        if (params != null) {
            baseParams.putAll(params);
        }
        return baseParams;
    }

    protected Map<String, String> generateBaseParams() {
        Map<String, String> baseParams = new HashMap<>();
        SQAppConfig config = ConfigManager.getInstance(mContext).getSQAppConfig();
        baseParams.put("pid", config.getPartner());
        baseParams.put("gid", config.getGameid());
        baseParams.put("refer", config.getRefer());
        baseParams.put("dev", DevLogic.getInstance(mContext).getValue());
        String time = "" + getCurrentTime();
        baseParams.put("time", time);
        baseParams.put("rid", LiveshowManager.getInstance().getRoomId());
        baseParams.put("uid", LiveshowManager.getInstance().getUserId());
        baseParams.put("uname", LiveshowManager.getInstance().getUserName());
        return baseParams;
    }

    public void fetchIm(SqHttpCallback<FetchImRspBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_ROOM_IM_FETCH)
            .signV3()
            .params(generateBaseParams(new FetchImReqBean().toMap()));
        get(request, callback, FetchImRspBean.class);
    }

    public void sendIm(SendImReqBean sendImBean, SqHttpCallback<SendImRspBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_ROOM_IM_SEND)
            .signV3()
            .params(generateBaseParams(sendImBean.toMap()));
        post(request, callback, SendImRspBean.class);
    }
}
