package com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory;


import android.text.TextUtils;

import com.sq.websocket_engine.ARecInfMsg;
import com.sq.websocket_engine.ARecInfMsgBaseFactory;
import com.sqwan.liveshow.huya.engine.LiveshowManager;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/24 12:24
 */
public class ARecInfMsgDanmuFactory extends ARecInfMsgBaseFactory {
    @Override
    public ARecInfMsg convert(String ev) {
        if (TextUtils.equals(ev, getRoomMsgEv())) {
            return new RoomRecInfMsg();
        }
        return null;
    }

    @Override
    public String getTargetAppid() {
        return "danmu";
    }
    private String getRoomMsgEv(){
        return String.format("lv.rm.%s",LiveshowManager.getInstance().getRoomId());
    }

}
