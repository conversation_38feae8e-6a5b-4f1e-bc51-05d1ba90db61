package com.sqwan.liveshow.huya.skin.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.EditText;

import com.sq.libwebsocket.util.LogUtil;
import com.sqwan.liveshow.huya.skin.attr.SkinBackgroundAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinTextColorAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinTextColorHintAttr;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;
import com.sqwan.supportview.LimitedEditText;

/**
 * Created by znb on 2021-10-20
 */
public class SkinEditText extends LimitedEditText implements SkinViewInterface {

    private SkinBackgroundAttr skinBackgroundAttr;
    private SkinTextColorAttr skinTextColorAttr;
    private SkinTextColorHintAttr skinTextColorHintAttr;

    public SkinEditText(Context context) {
        super(context, null);
    }

    public SkinEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        skinBackgroundAttr = new SkinBackgroundAttr(this,attrs);
        skinTextColorAttr = new SkinTextColorAttr(this,attrs);
        skinTextColorHintAttr = new SkinTextColorHintAttr(this,attrs);
    }



    @Override
    public void setBackgroundResource(int resid) {
        if (skinBackgroundAttr!=null) {
            skinBackgroundAttr.setBackgroundResource(resid);
        }
    }

    @Override
    public void applySkin() {
        if (skinBackgroundAttr!=null) {
            skinBackgroundAttr.applySkin();
        }

        if (skinTextColorHintAttr != null){
            skinTextColorHintAttr.applySkin();
        }

        if (skinTextColorAttr != null){
            skinTextColorAttr.applySkin();
        }
    }
}