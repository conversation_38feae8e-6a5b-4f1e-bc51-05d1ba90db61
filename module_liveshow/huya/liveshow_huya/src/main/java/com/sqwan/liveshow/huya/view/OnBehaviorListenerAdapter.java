package com.sqwan.liveshow.huya.view;

public class OnBehaviorListenerAdapter implements  OnBehaviorListener{
    @Override
    public void fullScreen() {

    }

    @Override
    public void smallScreen() {

    }

    @Override
    public void levelLive() {

    }

    @Override
    public void resetLive() {

    }

    @Override
    public void backLiveRoom() {

    }

    @Override
    public void clickInputChat() {

    }

    @Override
    public void showChatList(boolean show) {

    }

    @Override
    public void showDanmu(boolean show) {

    }

    @Override
    public void clickDefinition() {

    }
}