package com.sqwan.liveshow.huya.request;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomRspBean;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 10:20
 */
public class DanmuRoomRequestManager extends HyLiveshowBaseRequestManager {

    public DanmuRoomRequestManager(Context context) {
        super(context);
    }

    protected Map<String, String> generateBaseParams(Map<String, String> params) {
        Map<String, String> baseParams = generateBaseParams();
        if (params != null) {
            baseParams.putAll(params);
        }
        return baseParams;
    }

    protected Map<String, String> generateBaseParams() {
        Map<String, String> baseParams = new HashMap<>();
        SQAppConfig config = ConfigManager.getInstance(mContext).getSQAppConfig();
        baseParams.put("pid", config.getPartner());
        baseParams.put("gid", config.getGameid());
        baseParams.put("refer", config.getRefer());
        baseParams.put("dev", DevLogic.getInstance(mContext).getValue());
        baseParams.put("sversion", VersionUtil.sdkVersion);
        baseParams.put("version", AppUtils.getVersionName(mContext));
        baseParams.put("gwversion", VersionUtil.gwversion);
        String time = "" + getCurrentTime();
        baseParams.put("time", time);
        baseParams.put("rid", LiveshowManager.getInstance().getRoomId());
        baseParams.put("uid", LiveshowManager.getInstance().getUserId());
        baseParams.put("uname", LiveshowManager.getInstance().getUserName());
        baseParams.put("nickname", CommonConfigs.getInstance().getBaseUserInfo().roleName);
        return baseParams;
    }

    public void enterRoom(EnterRoomReqBean enterRoomReqBean, SqHttpCallback<EnterRoomRspBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_ROOM_ENTER)
            .signV3()
            .params(generateBaseParams(enterRoomReqBean.toMap()));
        post(request, callback, EnterRoomRspBean.class);
    }

    public void leaveRoom(SqHttpCallback<Void> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_ROOM_LEAVE)
            .signV3()
            .params(generateBaseParams());
        post(request, callback, Void.class);
    }
}
