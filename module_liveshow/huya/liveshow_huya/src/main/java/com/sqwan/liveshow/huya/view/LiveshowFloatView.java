package com.sqwan.liveshow.huya.view;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.huya.berry.client.customui.model.LiveListInfo;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.activity.ChatInputActivity;
import com.sqwan.liveshow.huya.danmu.DanMuController;
import com.sqwan.liveshow.huya.danmu.LiveshowDanmuManager;
import com.sqwan.liveshow.huya.danmu.LiveshowMsgDispatcher;
import com.sqwan.liveshow.huya.danmu.view.ChattingRoomView;
import com.sqwan.liveshow.huya.danmu.view.ChooseView;
import com.sqwan.liveshow.huya.danmu.view.GuideView;
import com.sqwan.liveshow.huya.engine.ILiveshowControl;
import com.sqwan.liveshow.huya.engine.LiveInfoEx;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sy37sdk.account.floatview.DragViewLayout;

import master.flame.danmaku.ui.widget.DanmakuView;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-29 20:24
 */
public class LiveshowFloatView extends DragViewLayout implements OnBehaviorListener, ILiveshowControl, LiveshowMsgDispatcher.ILiveshowMsgDispatch, LiveshowDanmuManager.SendImLimitListener {
    private int minimizeWidth;
    private int minimizeHeight;

    private ChattingRoomView chattingRoomView;
    private ChooseView chooseView;
    private PlayerView playerView;
    public LiveshowFloatCoverView liveshowFloatCoverView;
    public DanmakuView sv_danmaku;
    private int lastX, lastY;
    private FrameLayout ftchooseView;
    private DanMuController danMuController;
    private OnBehaviorListener onBehaviorListener;
    private FrameLayout ftControlCorverView,ftControlCorverViewFloat;
    private LiveInfoEx liveInfoEx;
    public GuideView guideView;
    public View liveshow_softkeyboard_padding;
    private Task task = Task.create();

    public LiveshowFloatView(final Context context) {
        super(context);
        View.inflate(context, SqResUtils.getLayoutId(context, SqR.layout.sy37_layout_liveshowfloat), this);
        liveshowFloatCoverView = findViewById(SqResUtils.getId(getContext(), SqR.id.video_playerCover));
        chattingRoomView = findViewById(SqResUtils.getId(getContext(), SqR.id.chattingRoomView));
        chooseView = findViewById(SqResUtils.getId(getContext(), SqR.id.chooseView));
        ftchooseView = findViewById(SqResUtils.getId(getContext(), SqR.id.ftchooseView));
        sv_danmaku = liveshowFloatCoverView.findViewById(SqResUtils.getId(getContext(), SqR.id.sv_danmaku));
        ftControlCorverView = findViewById(SqResUtils.getId(getContext(), SqR.id.ftControlCorverView));
        ftControlCorverViewFloat = liveshowFloatCoverView.findViewById(SqResUtils.getId(getContext(), SqR.id.ftControlCorverViewFloat));
        guideView = findViewById(SqResUtils.getId(getContext(),SqR.id.layout_guide_view));
        initChattingRoomView();
        initDanmakuView();

        playerView = new PlayerView(mContext);

        liveshowFloatCoverView.addVideoFragmentContainer(this, playerView);
        liveshowFloatCoverView.setOnBehaviorListener(this);

        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            minimizeWidth = 180;
            minimizeHeight = 120;
        } else {
            minimizeWidth = 256;
            minimizeHeight = 170;
        }
        mWidth = DensityUtil.dip2px(mContext, minimizeWidth);
        mHeight = DensityUtil.dip2px(mContext, minimizeHeight);

        chattingRoomView.getInputView().setOnClickListener(v -> goChatInputActivity());
        chooseView.setOnclickItemListener(item -> {
            ViewUtils.gone(ftchooseView);
            String disPlayName = liveInfoEx.disPlayNames.get(item);
            liveshowFloatCoverView.getIv_definition().setText(disPlayName);
            if (playerView != null) {
                playerView.restart(disPlayName);
            }
        });
        liveshowFloatCoverView.getIv_definition().setOnClickListener(v -> ViewUtils.show(ftchooseView));
        ftchooseView.setOnClickListener(v -> ViewUtils.gone(ftchooseView));

        danMuController = new DanMuController(sv_danmaku);
        danMuController.init();

        LiveshowMsgDispatcher.getInstance().unRegister(this);
        LiveshowMsgDispatcher.getInstance().register(this);
        LiveshowDanmuManager.getInstance().unRegSendImLimitListener(this);
        LiveshowDanmuManager.getInstance().regSendImLimitListener(this);

        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            TextView tvInputMessage = chattingRoomView.getInputView().getTvInputMessage();
            if (tvInputMessage != null) {
                tvInputMessage.setGravity(Gravity.CENTER);
            }
        }

        post(() -> {
            guideView.resetGuideViewPosition(liveshowFloatCoverView.getIvLiveSwitchSound(),
                    SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_sound),
                    SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_guide_small_sound), v ->
                        guideView.resetGuideViewPosition(liveshowFloatCoverView.getmIvLiveSwitchWidth(),
                        SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_icon_narrow),
                        SkinHelper.getDrawable(getContext(), SqR.drawable.sy37_liveshow_guide_small_video), v1 -> {
                            ViewUtils.gone(guideView);
                            liveshowFloatCoverView.startAutoHide();
                        }));
            handleChatListChange(ftControlCorverView,LiveshowDanmuManager.getInstance().isImListShow());
            handleChatListChange(ftControlCorverViewFloat,LiveshowDanmuManager.getInstance().isImListShow());
        });
    }


    public void play(LiveInfoEx liveInfoEx) {
        this.liveInfoEx = liveInfoEx;
        LiveListInfo liveListInfo = liveInfoEx.liveListInfo;
        liveshowFloatCoverView.setAnchorInfo(liveListInfo.avatar, liveListInfo.nickName, liveListInfo.audienceCount);
        if (playerView != null) {
            playerView.start(liveInfoEx);
        }
        String disPlayName = liveInfoEx.videoInfo.disPlayName;
        chooseView.setData(liveInfoEx.disPlayNames, disPlayName);
        liveshowFloatCoverView.getIv_definition().setText(disPlayName);
    }


    public void showView() {
        if (floatViewConfig != null) {
            floatLayoutParams.y = floatViewConfig.regionHeight - mHeight;
            lastX = floatLayoutParams.x;
            lastY = floatLayoutParams.y;

        }
        maximize();
        liveshowFloatCoverView.switchFullLiveContainer();
        addView();
        StatusBarUtil.hideSystemUI(mContext);
    }

    @Override
    public boolean isAuthStartAnim() {
        return false;
    }

    @Override
    public boolean isCheckShowCompelete() {
        return false;
    }

    @Override
    public void maximize() {
        if (!LiveshowManager.getInstance().isMinimize()) {
            return;
        }
        LiveshowManager.getInstance().maximize();
        chanageViewSize(false);
        this.canDispatchTouchEvent = false;
    }

    @Override
    public void close() {
        LiveshowManager.getInstance().close();
    }

    private void chanageViewSize(boolean minimize) {
        if (floatLayoutParams != null) {
            if (minimize) {
                floatLayoutParams.x = lastX;
                floatLayoutParams.y = lastY;
                floatLayoutParams.width = DensityUtil.dip2px(mContext, minimizeWidth);
                floatLayoutParams.height = DensityUtil.dip2px(mContext, minimizeHeight);
            } else {
                lastY = floatLayoutParams.y;
                lastX = floatLayoutParams.x;
                floatLayoutParams.x = floatViewConfig.marginLeft;
                floatLayoutParams.y = floatViewConfig.marginTop;
                floatLayoutParams.width = floatViewConfig.regionWidth;
                floatLayoutParams.height = floatViewConfig.regionHeight;
            }
            update();

        }
    }

    @Override
    public void minimize() {
        if (LiveshowManager.getInstance().isMinimize()) {
            return;
        }
        LiveshowManager.getInstance().minimize();
        chanageViewSize(true);
        this.canDispatchTouchEvent = true;
    }

    @Override
    public void resume() {
        LiveshowManager.getInstance().resume();
    }

    @Override
    public void pause() {
        LiveshowManager.getInstance().pause();
    }

    @Override
    public void switchVoice(boolean isVoice) {
        LiveshowManager.getInstance().switchVoice(isVoice);
    }

    @Override
    public void fullScreen() {
        maximize();

        if (liveshowFloatCoverView.isImListShow()) {
            ViewUtils.show(chattingRoomView);
        }

        if (liveshowFloatCoverView.isDanmuShow()) {
            ViewUtils.show(sv_danmaku);
        }
    }

    @Override
    public void smallScreen() {
        minimize();
        if (liveshowFloatCoverView.isImListShow()) {
            ViewUtils.gone(chattingRoomView);
        }
        if (isDanmuShow()) {
            ViewUtils.gone(sv_danmaku);
        }
    }

    @Override
    public void levelLive() {
        close();
    }

    @Override
    public void resetLive() {
    }

    @Override
    public void backLiveRoom() {
        // 显示直播列表
        RoomListView roomListView = LiveshowManager.getInstance().getRoomListView();
        if (roomListView != null) {
            roomListView.show();
        }
        LiveshowManager.getInstance().leaveLiveshowRoomWrapper(false);
    }

    @Override
    public void clickInputChat() {
        goChatInputActivity();
    }

    @Override
    public void showChatList(boolean show) {
         handleChatListChange(ftControlCorverView,show);
         handleChatListChange(ftControlCorverViewFloat,show);
        if (onBehaviorListener != null) {
            onBehaviorListener.showChatList(show);
        }

        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            ViewGroup.LayoutParams layoutParams = liveshowFloatCoverView.getLayoutParams();
            if (layoutParams != null) {
                if (show) {
                    layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
                } else {
                    layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
                }
                liveshowFloatCoverView.setLayoutParams(layoutParams);
            }
        }
    }


    @Override
    public void showDanmu(boolean show) {
        if (show) {
            ViewUtils.show(sv_danmaku);

        } else {
            ViewUtils.gone(sv_danmaku);
        }
        if (onBehaviorListener != null) {
            onBehaviorListener.showDanmu(show);
        }
    }

    @Override
    public void clickDefinition() {
        ViewUtils.show(liveshowFloatCoverView.getIv_definition());
    }

    @Override
    public void release() {
        super.release();
        if (liveshowFloatCoverView != null) {
            liveshowFloatCoverView.recycle();
        }
        if (playerView != null) {
            playerView.release();
        }
        LiveshowMsgDispatcher.getInstance().unRegister(this);
        if (danMuController != null) {
            danMuController.finish();
        }
        if (onBehaviorListener != null) {
            onBehaviorListener = null;
        }
        LiveshowDanmuManager.getInstance().unRegSendImLimitListener(this);
        task.stop();
    }

    @Override
    public boolean isPostView() {
        return false;
    }

    @Override
    public boolean isScreenOnType() {
        return true;
    }

    public void goChatInputActivity() {
        Context context = getContext();
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            Intent intent = new Intent(activity, ChatInputActivity.class);
            intent.putExtra("Ex", true);
            activity.startActivity(intent);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        release();
    }
    @Override
    public void dispatchIm(final FetchImRspBean.ImMsg imMsg) {
        Task.post(() -> {
            if (imMsg != null && !TextUtils.isEmpty(imMsg.msg.content)) {
                LogUtil.i(TAG, "sendDanMuData:" + imMsg.toString());
                chattingRoomView.addChatMessage(imMsg);
            }
        });
    }

    @Override
    public void dispatchDanmu(final FetchImRspBean.ImMsg imMsg) {
        if (imMsg != null && !TextUtils.isEmpty(imMsg.msg.content)) {
            LogUtil.i(TAG, "sendDanMuData:" + imMsg.toString());
            danMuController.sendDanMuData(imMsg);
        }
    }

    public boolean isDanmuShow() {
        return liveshowFloatCoverView.isDanmuShow();
    }

    private void initChattingRoomView() {
        if (LiveshowDanmuManager.getInstance().isImListShow()) {
            ViewUtils.show(chattingRoomView);
        } else {
            ViewUtils.gone(chattingRoomView);
        }
    }

    private void initDanmakuView() {
        if (LiveshowDanmuManager.getInstance().isDanmuShow()) {
            ViewUtils.show(sv_danmaku);
        } else {
            ViewUtils.gone(sv_danmaku);
        }
    }

    @Override
    public void onCountDown(boolean finish, String time) {
        chattingRoomView.getInputView().setEnabled(finish);
        if (!finish) {
            chattingRoomView.setCountDownViewText(String.format("%ss", time));
        } else {
            chattingRoomView.setCountDownViewText("");
        }
    }

    public void setOnBehaviorListener(OnBehaviorListener onBehaviorListener) {
        this.onBehaviorListener = onBehaviorListener;
    }
    private void handleChatListChange(View view, boolean show){
        if (view == null) {
            return;
        }
        MarginLayoutParams params = (MarginLayoutParams) view.getLayoutParams();
        if (show) {
            if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                // 竖屏状态下不需要将弹幕区域往下移动一段状态栏的距离，因为竖屏的布局已经和视频播放区域边界对齐了
                if (view != ftControlCorverViewFloat) {
                    params.topMargin = liveshowFloatCoverView.getFlTopContainer().getHeight();
                }
            } else {
                // 横版直播的标题底部有一点点透明度，之前旧代码是直接写成 topMargin = 40dp，但还是有一点间隔，在这里做了一下优化
                params.topMargin = liveshowFloatCoverView.getFlTopContainer().getHeight() - DensityUtil.dip2px(mContext, 5);
            }
            ViewUtils.show(chattingRoomView);
            ViewUtils.hidden(liveshowFloatCoverView.getLl_input_and_countdownview());
            ViewGroup vBottomContainer = liveshowFloatCoverView.getVBottomContainer();
            if (vBottomContainer != null && mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
                vBottomContainer.setPadding(0, 0, 0, 0);
            }
            view.setLayoutParams(params);
            return;
        }

        params.topMargin = 0;
        ViewUtils.gone(chattingRoomView);
        ViewUtils.show(liveshowFloatCoverView.getLl_input_and_countdownview());
        ViewGroup vBottomContainer = liveshowFloatCoverView.getVBottomContainer();
        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT && vBottomContainer != null) {
            vBottomContainer.setPadding(0, DensityUtil.dip2px(mContext, 20), 0, DensityUtil.dip2px(mContext, 20));
        }
        view.setLayoutParams(params);
    }

    public PlayerView getPlayerView() {
        return playerView;
    }
}