package com.sqwan.liveshow.huya.engine;

import android.text.TextUtils;

import com.sqwan.base.L;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.liveshow.huya.bean.ConfigBean;
import com.sqwan.liveshow.huya.engine.bean.HyConfig;
import com.sqwan.msdk.config.ConfigManager;


/**
 * 描述: 直播相关配置
 * 作者：znb
 * 时间：2021-06-30 10:36
 */
public class HyConfigManager {
    private static final HyConfigManager ourInstance = new HyConfigManager();

    public static HyConfigManager getInstance() {
        return ourInstance;
    }

    private HyConfigManager() {
    }

    public HyConfig hyConfig;

    public boolean init(ConfigBean.ItemsBean itemsBean) {
        if (itemsBean != null) {
            hyConfig = new HyConfig();
            HyConfig.HyUiConfig hyUiConfig = new HyConfig.HyUiConfig();
            hyConfig.hyUiConfig = hyUiConfig;
//            hyConfig.gameId = 6745;
//            hyConfig.appId = "huya_app_603";
//            hyConfig.appKey = "a03ac0e5";
            String appkey = decodeAccessKey(itemsBean.getApp_key());
            hyConfig.gameId = Integer.parseInt(itemsBean.getGame_id());
            hyConfig.appId = itemsBean.getApp_id();
            hyConfig.appKey = appkey;
            LogUtil.i(hyConfig.toString());
            return true;
        }

        return false;
    }

    /**
     * 拿到服务端传的闪验accessKey后解密
     *
     * @param encodeAccessKey
     * @return
     */
    private String decodeAccessKey(String encodeAccessKey) {
        String decodeKey = "";
        String appKey = ConfigManager.getInstance(L.getApplicationContext()).getAppKey();
        if (!TextUtils.isEmpty(appKey)) {
            int length = appKey.length();
            //不足16位则补齐0
            if (length < 16) {
                StringBuilder sb = new StringBuilder(appKey);
                for (int i = 0; i < 16 - length; i++) {
                    sb.append("0");
                }
                decodeKey = sb.toString();
            } else {
                //满16位则截取前16位
                decodeKey = appKey.substring(0, 16);
            }
        }
        LogUtil.i("解密key：" + decodeKey);
        String decrypt = AESUtil.decryptString(encodeAccessKey, decodeKey);
        LogUtil.i("解密后：" + decrypt);
        return decrypt;
    }
}
