package com.sqwan.liveshow.huya.danmu.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.support.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.view.SkinImageView;
import com.sqwan.liveshow.huya.skin.view.SkinLinearLayout;
import com.sqwan.supportview.LimitedEditText;

public class InputView extends SkinLinearLayout {

    private LimitedEditText limitedEditText;

    private SkinImageView skinImageView;

    private String textContent;

    private Context context;

    public InputView(Context context) {
        this(context,null);
        this.context = context;
    }

    public InputView(Context context, @Nullable AttributeSet attrs) {
        this(context,attrs,0);
    }

    public InputView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public InputView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }


    private void initView(final Context context){
        View.inflate(context, SqResUtils.getLayoutId(context, SqR.layout.sy37_item_send_view),this);
        limitedEditText = findViewById(SqResUtils.getId(context, SqR.id.et_input_message));
        skinImageView = findViewById(SqResUtils.getId(context, SqR.id.tv_send_message));

        skinImageView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (sendTextListener != null){
                    if (!TextUtils.isEmpty(textContent)) {
                        sendTextListener.sendTextContent(textContent);
                    }
                }
            }
        });

        limitedEditText.setNumberFilter(30,new LimitedEditText.TextWatcherAdapter() {

            @Override
            public void afterTextChanged(final Editable s) {
                super.afterTextChanged(s);
                textContent = s.toString().trim();

                ((Activity)context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!TextUtils.isEmpty(textContent)) {
                            skinImageView.setImageResource(SqResUtils.getDrawableId(context,SqR.drawable.sy37_liveshow_iv_send));
                        }else {
                            skinImageView.setImageResource(SqResUtils.getDrawableId(context,SqR.drawable.sy37_liveshow_iv_not_send));
                        }
                    }
                });
            }

            @Override
            public void maxCharactersCallback() {
                ToastUtil.showToast("内容过长~");
            }

            @Override
            public void emptyChange(final Boolean isEmpty) {

            }
        });

    }

    public LimitedEditText getLimitedEditText(){
        return limitedEditText;
    }

    private SendTextListener sendTextListener;

    public void setSentTextListener(SendTextListener sendTextListener){
        this.sendTextListener = sendTextListener;
    }

   public interface SendTextListener{
        void sendTextContent(String content);
   }

    public SkinImageView getTextView(){
        return skinImageView;
    }

}
