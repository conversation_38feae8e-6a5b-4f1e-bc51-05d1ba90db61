package com.sqwan.liveshow.huya.request.bean.danmu;

import com.google.sqgson.Gson;
import com.google.sqgson.GsonBuilder;
import com.google.sqgson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/9 11:20
 */
public class LiveshowBaseRequest {
    public Map<String,String> toMap(){
        Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();
        Type type = new TypeToken<Map<String, String>>() {}.getType();
        String jsonString = new Gson().toJson(this);
        Map<String, String> map = gson.fromJson(jsonString, type);
        return map;
    }
}
