package com.sqwan.liveshow.huya.danmu.view;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.view.SkinImageView;

public class GuideView extends LinearLayout implements View.OnClickListener {

    private SkinImageView iv_guide_content;
    private SkinImageView iv_switch;
    private OnClickListener onClickListener;
    public GuideView(Context context) {
        this(context,null);
    }

    public GuideView(Context context, @Nullable AttributeSet attrs) {
        this(context,attrs,0);
    }

    public GuideView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public GuideView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }

    private void initView(Context context){
        View.inflate(context, SqResUtils.getLayoutId(context, SqR.layout.sy37_layout_guide_view),this);
        iv_guide_content = findViewById(SqResUtils.getId(context,SqR.id.iv_guide_content));
        iv_switch = findViewById(SqResUtils.getId(context,SqR.id.iv_live_switch_width));

        iv_guide_content.setOnClickListener(this);
        iv_switch.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (onClickListener == null) {
            return;
        }
        onClickListener.onClick(v);
    }

    public void resetGuideViewPosition(View anchorView,
                                       Drawable guideIconDrawable,
                                       Drawable guideContentDrawable,
                                       OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
        if (iv_guide_content != null) {
            int[] location = new int[2];
            anchorView.getLocationInWindow(location);
            MarginLayoutParams layoutParams = (MarginLayoutParams) iv_guide_content.getLayoutParams();
            layoutParams.leftMargin = location[0] - (int)(iv_guide_content.getWidth() * 0.7) - 10;
            layoutParams.topMargin = location[1] - iv_guide_content.getHeight()  - 5;
            iv_guide_content.setLayoutParams(layoutParams);
            iv_guide_content.setImageDrawable(guideContentDrawable);
        }

        if (iv_switch != null){
            int[] location = new int[2];
            anchorView.getLocationInWindow(location);
            MarginLayoutParams layoutParams = (MarginLayoutParams) iv_switch.getLayoutParams();
            layoutParams.leftMargin = location[0];
            layoutParams.topMargin = location[1];
            layoutParams.width = anchorView.getWidth();
            layoutParams.height = anchorView.getHeight();
            iv_switch.setLayoutParams(layoutParams);
            iv_switch.setImageDrawable(guideIconDrawable);
        }

        if (CommonConfigs.getInstance().getIsNewRole(getContext())) {
            setVisibility(VISIBLE);
            CommonConfigs.getInstance().setNewRoleId(getContext());
        }
    }
}
