package com.sqwan.liveshow.huya.danmu.adpter;

import android.content.Context;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;

import java.util.ArrayList;

public class IMAdapter extends RecyclerView.Adapter<IMAdapter.IMViewHolder> {

    private volatile ArrayList<UserIMBean> mUserImBeanList = new ArrayList<>() ;


    public Context context;

    public IMAdapter(Context context){
        this.context = context;
    }

    public void updateDataDirect(CharSequence userName,CharSequence msg){

        UserIMBean userIMBean = new UserIMBean();
        userIMBean.userName = userName;
        userIMBean.message = msg;
        mUserImBeanList.add(userIMBean);

        notifyDataSetChanged();
    }

    @Override
    public IMViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(context).inflate(SqResUtils.getLayoutId(context, SqR.layout.sy37_item_im_chat),viewGroup,false);
        return new IMViewHolder(view);
    }

    @Override
    public void onBindViewHolder(IMViewHolder imViewHolder, int i) {
        CharSequence str = SpanUtil.concat(mUserImBeanList.get(i).userName,mUserImBeanList.get(i).message);
        imViewHolder.mTvMsg.setText(str);
    }

    @Override
    public int getItemCount() {
        return mUserImBeanList == null ? 0 : mUserImBeanList.size();
    }

    class UserIMBean{
        CharSequence userName;
        CharSequence message;
    }


    class IMViewHolder extends RecyclerView.ViewHolder{
        TextView mTvMsg;

        IMViewHolder(View itemView) {
            super(itemView);
            mTvMsg = (TextView)itemView.findViewById(SqResUtils.getId(context, SqR.id.tv_msg));
        }
    }
}
