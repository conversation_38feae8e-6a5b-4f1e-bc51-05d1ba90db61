package com.sqwan.liveshow.huya.danmu.adpter;

import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.R;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.view.SkinTextView;

import java.util.ArrayList;
import java.util.List;

public class ChooseAdapter extends RecyclerView.Adapter<ChooseAdapter.ViewHolder>{

    private Context context;

    private List<String> dataList = new ArrayList<>();

    public ChooseAdapter(Context context){
        this.context = context;
    }

    int highlightIndex = 0;

    public void setDataList(List<String> list,String text){
        dataList.addAll(list);
        for (int i = 0; i <dataList.size(); i++){
            if (text.equals(dataList.get(i))){
                highlightIndex = i;
                break;
            }
        }

        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(context).inflate(SqResUtils.getLayoutId(context,SqR.layout.sy37_item_choose_view),viewGroup,false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final ViewHolder viewHolder, int i) {
        final int index = i;
        viewHolder.mTvItemText.setText(dataList.get(i));

        if (i == highlightIndex){
            viewHolder.mTvItemText.setTextColor(context,"sy37_item_choose_view_tv_item_text_color_highlight");
        }else{
            viewHolder.mTvItemText.setTextColor(context,"sy37_item_choose_view_tv_item_text_color");
        }
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                highlightIndex = index;
                notifyDataSetChanged();
                if (itemClickListener != null){
                    itemClickListener.ItemOnClick(index);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList == null ? 0: dataList.size();
    }

    public void setItemClickListener(ItemClickListener itemClickListener){
        this.itemClickListener = itemClickListener;
    }

    private ItemClickListener itemClickListener;

    public interface ItemClickListener{
        void ItemOnClick(int item);
    }


    class ViewHolder extends RecyclerView.ViewHolder {
        SkinTextView mTvItemText;

        ViewHolder(View itemView) {
            super(itemView);
            mTvItemText = (SkinTextView) itemView.findViewById(SqResUtils.getId(context, "tv_item_text"));
        }
    }
}
