package com.sqwan.liveshow.huya.request;

import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.parse.ResponseDataParse;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.DanmuReqService;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.JoinRoomMsgReq;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.LeaveRoomMsgReq;

/**
 * 描述: websocket弹幕请求接口
 * 作者：znb
 * 时间：2021/11/9 10:20
 */
public class DanmuRequestManager {
    private DanmuReqService danmuReqService;
    public DanmuRequestManager(){
        danmuReqService = new DanmuReqService();
    }
    public void reqJoinRoom(String roomId, final ReqWrapperHandler.FinishListener<Boolean> finishListener){
        danmuReqService.reqJoinRoom(new JoinRoomMsgReq(roomId), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(true);
                }
            }
        }).onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(false);
                }
            }
        });
    }
    public void reqLeaveRoom(String roomId, final ReqWrapperHandler.FinishListener<Boolean> finishListener){
        danmuReqService.reqleaveRoom(new LeaveRoomMsgReq(roomId), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(true);
                }
            }
        }).onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(false);
                }
            }
        });
    }

}
