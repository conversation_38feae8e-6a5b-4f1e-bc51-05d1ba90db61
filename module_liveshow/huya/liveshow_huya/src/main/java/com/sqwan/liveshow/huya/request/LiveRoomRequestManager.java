package com.sqwan.liveshow.huya.request;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.liveshow.huya.bean.ConfigBean;
import com.sqwan.liveshow.huya.bean.LiveMenuBean;
import com.sqwan.liveshow.huya.bean.RecommenedAnchorBean;
import com.sqwan.liveshow.request.LiveShowParams;


/**
 * 后端请求管理类
 */
public class LiveRoomRequestManager extends HyLiveshowBaseRequestManager {

    public LiveRoomRequestManager(Context context) {
        super(context);
    }

    public void ReqGetLiveMenuData(final SqHttpCallback<LiveMenuBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_MENU_URL)
            .signV3()
            .addParamsTransformer(new LiveShowParams());
        get(request, callback, LiveMenuBean.class);
    }

    public void ReqGetRecommendedAnchorData(final SqHttpCallback<RecommenedAnchorBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_RECOMMENDED_ANCHOR)
            .signV3()
            .addParamsTransformer(new LiveShowParams());
        get(request, callback, RecommenedAnchorBean.class);
    }

    public void ReqGetConfigData(final SqHttpCallback<ConfigBean> callback) {
        SqRequest request = SqRequest.of(LiveRoomUrl.LIVE_CONFIG)
            .signV3()
            .addParamsTransformer(new LiveShowParams());
        get(request, callback, ConfigBean.class);
    }
}
