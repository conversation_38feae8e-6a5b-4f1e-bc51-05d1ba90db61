package com.sqwan.liveshow.huya.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.IdRes;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.skin.view.SkinLinearLayout;

import java.util.Collection;

public class RoomTabAdapter extends RecyclerView.Adapter<RoomViewHolder> {

    private final Context mContext;
    private final int layoutRes;
    private final Collection<?> data;
    private OnBindViewListener onBindViewListener;
    private final OnItemClickListener onItemClickListener;

    //本次选中的item的位置
    private int currentSelectIndex = 0;

    //上一次选中的item的位置
    private int lastSelectIndex = 0;


    public RoomTabAdapter(@NonNull Context mContext,
                          @IdRes int layoutRes,
                          @NonNull Collection<?> data,
                          @NonNull OnBindViewListener onBindViewListener,
                          @NonNull OnItemClickListener onItemClickListener) {
        this.mContext = mContext;
        this.layoutRes = layoutRes;
        this.data = data;
        this.onBindViewListener = onBindViewListener;
        this.onItemClickListener = onItemClickListener;
    }

    @Override
    public RoomViewHolder onCreateViewHolder(ViewGroup parent, int position) {
        View itemView = LayoutInflater.from(mContext).inflate(layoutRes, parent, false);
        SkinLinearLayout selectorView = itemView.findViewById(SqResUtils.getId(mContext, SqR.id.ll_sy37_liveshow_bg_left_tab));
        selectorView.setBackground(SqR.drawable.sy37_liveshow_bg_left_tab_selected,SqR.drawable.sy37_liveshow_bg_left_tab_unselected);
        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            ViewGroup.LayoutParams layoutParams = selectorView.getLayoutParams();
            if (layoutParams != null) {
                layoutParams.width = parent.getWidth() / 2;
            }
        }
        return new RoomViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(RoomViewHolder holder, @SuppressLint("RecyclerView") final int position) {

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                currentSelectIndex = position;
                notifyItemChanged(position);
                notifyItemChanged(lastSelectIndex);
            }
        });

        if (currentSelectIndex == position) {
            onItemClickListener.onItemClick(holder, position);
            lastSelectIndex = currentSelectIndex;
            holder.itemView.setSelected(true);
        }else{
            holder.itemView.setSelected(false);
        }

        onBindViewListener.onBindView(holder, position, currentSelectIndex == position);
    }

    @Override
    public int getItemCount() {
        return this.data.size();
    }

    public interface OnBindViewListener {
        void onBindView(RoomViewHolder roomViewHolder, int position, boolean isSelect);
    }

    public interface OnItemClickListener {
        void onItemClick(RoomViewHolder roomViewHolder, int position);
    }
}
