package com.sqwan.liveshow.huya.danmu.view;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.support.annotation.Nullable;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.danmu.adpter.IMAdapter;
import com.sqwan.liveshow.huya.request.bean.danmu.http.FetchImRspBean;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.AImMsg;
import com.sqwan.liveshow.huya.request.bean.danmu.websocket.factory.AImMsgFactory;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sqwan.liveshow.huya.skin.view.SkinImageView;

public class ChattingRoomView extends LinearLayout {

    private static final String TAG = "ChattingRoomView";

    private RecyclerView recyclerView;

    private IMAdapter imAdapter;

    private InputAndCountDownView inputView;

    private SkinImageView mIvCheckNewMessage;

    @Nullable
    private SkinImageView mIvChatTitle;

    public ChattingRoomView(Context context) {
        this(context,null);
    }

    public ChattingRoomView(Context context, @Nullable AttributeSet attrs) {
        this(context,attrs,0);
    }

    public ChattingRoomView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context,attrs,defStyleAttr,0);
    }

    public ChattingRoomView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView(context);
    }


    private void initView(Context context){
        View.inflate(context, SqResUtils.getLayoutId(context, SqR.layout.sy37_liveshow_chatting_view),this);
        recyclerView = (RecyclerView) findViewById(SqResUtils.getId(context, SqR.id.rv_im_chat_message));
        mIvCheckNewMessage = findViewById(SqResUtils.getId(context,SqR.id.iv_im_check_new_message));
        imAdapter = new IMAdapter(getContext());
        final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setAdapter(imAdapter);

        inputView = (InputAndCountDownView)findViewById(SqResUtils.getId(context, SqR.id.ip_input_view));
        inputView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });

        mIvCheckNewMessage.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                recyclerView.scrollToPosition(imAdapter.getItemCount()-1);
                ViewUtils.gone(mIvCheckNewMessage);
            }
        });

        int inPutMessageTextColor = SkinHelper.getColorValue(context, SqR.color.sy37_item_roast_view_tv_input_message_text_color, Color.parseColor(
                getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT ? "#80FFFFFF" : "#6E718B"));
        inputView.getTvInputMessage().setTextColor(inPutMessageTextColor);
        int countDownTextColor = SkinHelper.getColorValue(context, SqR.color.sy37_item_roast_view_tv_countdown_text_color, Color.parseColor("#6E718B"));
        inputView.getTvCountDown().setTextColor(countDownTextColor);

        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                LogUtil.i(TAG,"onScrolled");
                boolean isVisBottom = isSlideToBottom(recyclerView);
                LogUtil.i(TAG,"addChatMessage isVisBottom:"+isVisBottom);
                if (isVisBottom) {
                    ViewUtils.gone(mIvCheckNewMessage);
                }
            }
        });

        mIvChatTitle = findViewById(SqResUtils.getId(context, SqR.id.iv_im_chat_title));
        if (mIvChatTitle != null && getContext().getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            mIvChatTitle.setImageDrawable(SkinHelper.getDrawable(context, SqR.drawable.sy37_liveshow_title_chat_room));
        }
    }

    public void addChatMessage(FetchImRspBean.ImMsg imMsg){
        if (imMsg!=null) {
            AImMsg aImMsg = new AImMsgFactory().convert(imMsg);
            if (aImMsg!=null) {
                addChatMessage(aImMsg.getUser(),aImMsg.getContent());
            }
        }
    }

    public void addChatMessage(CharSequence userName,CharSequence msg){
        if (imAdapter == null) return;
//        boolean isVisBottom = !canScrollVertically(1);
        boolean isVisBottom = isSlideToBottom(recyclerView);
        LogUtil.i(TAG,"addChatMessage isVisBottom:"+isVisBottom);
        imAdapter.updateDataDirect(userName,msg);
        if (isVisBottom) {
            recyclerView.scrollToPosition(imAdapter.getItemCount()-1);
        }else{
            ViewUtils.show(mIvCheckNewMessage);
        }
    }
    private  boolean isSlideToBottom(RecyclerView recyclerView) {
        if (recyclerView == null) return false;
        if (recyclerView.computeVerticalScrollExtent() + recyclerView.computeVerticalScrollOffset()
                >= recyclerView.computeVerticalScrollRange())
            return true;
        return false;
    }

    public void setCountDownViewText(String text){
        inputView.getTvCountDown().setText(text);
    }

    public InputAndCountDownView getInputView() {
        return inputView;
    }

    private float mRatio = 750f / 350;

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            int height = MeasureSpec.getSize(heightMeasureSpec);
            if (mRatio != 0) {
                float width = height / mRatio;
                widthMeasureSpec = MeasureSpec.makeMeasureSpec((int) width, MeasureSpec.EXACTLY);
            }
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
