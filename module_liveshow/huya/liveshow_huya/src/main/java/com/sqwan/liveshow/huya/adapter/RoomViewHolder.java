package com.sqwan.liveshow.huya.adapter;

import android.support.v7.widget.RecyclerView;
import android.util.SparseArray;
import android.view.View;

public class RoomViewHolder extends RecyclerView.ViewHolder {

    private final SparseArray<View> viewCache = new SparseArray<>();

    public RoomViewHolder(View itemView) {
        super(itemView);
    }

    public <T extends View> T findViewById(int viewId) {
        View view = viewCache.get(viewId);
        if (view == null) {
            view = itemView.findViewById(viewId);
            viewCache.put(viewId, view);
        }
        return (T) view;
    }
}
