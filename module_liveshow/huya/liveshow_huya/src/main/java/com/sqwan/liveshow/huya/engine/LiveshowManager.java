package com.sqwan.liveshow.huya.engine;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.huya.berry.client.HuyaBerry;
import com.huya.berry.client.HuyaBerryConfig;
import com.huya.berry.client.customui.CustomUICallback;
import com.huya.berry.client.customui.model.LiveInfo;
import com.huya.berry.client.customui.model.LiveListInfo;
import com.huya.berry.gamesdk.base.BaseCallback;
import com.nbvideo.VideoInfo;
import com.snail.antifake.jni.EmulatorDetectUtil;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.L;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.liveshow.IHyLiveshowManager;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.util.ApkInfoUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.LiveshowBaseManager;
import com.sqwan.liveshow.common.LiveShowParamsKey;
import com.sqwan.liveshow.error.LiveshowResult;
import com.sqwan.liveshow.huya.BuildConfig;
import com.sqwan.liveshow.huya.LiveRoomDataManager;
import com.sqwan.liveshow.huya.bean.ConfigBean;
import com.sqwan.liveshow.huya.danmu.LiveshowDanmuManager;
import com.sqwan.liveshow.huya.engine.bean.HyConfig;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomReqBean;
import com.sqwan.liveshow.huya.request.bean.danmu.http.EnterRoomRspBean;
import com.sqwan.liveshow.huya.skin.SkinHelper;
import com.sqwan.liveshow.huya.skin.manager.SkinManager;
import com.sqwan.liveshow.huya.trackaction.LiveshowTrackManager;
import com.sqwan.liveshow.huya.view.LiveshowFloatView;
import com.sqwan.liveshow.huya.view.PlayerUtils;
import com.sqwan.liveshow.huya.view.RoomListView;
import com.sqwan.msdk.api.SQResultListener;
import java.util.List;
import java.util.Map;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-17 15:41
 */
public class LiveshowManager extends LiveshowBaseManager implements IHyLiveshowManager,ILiveshowControl {
    private LiveRoomRequestManager liveshowRequestManager;
    private HuyaBerryConfig mHuyaBerryConfig;
    private LiveshowEventManager liveshowEventManager;
    private LiveshowFloatView liveshowFloatView;
    private RoomListView roomListView;
    private boolean hasCheckHyInited = false;
    private boolean isMinimize=true;
    public LiveInfoEx liveInfoEx;

    private int platformID;
    private ConfigBean configBean;
    public static LiveshowManager getInstance() {
        return (LiveshowManager) LiveshowEngine.getInstance().getLiveshowManager();
    }

    private LiveshowManager() {

    }



    public boolean isMinimize() {
        return isMinimize;
    }

    public int getPlatformID() {
        return platformID;
    }

    public void setMinimize(boolean minimize) {
        isMinimize = minimize;
    }

    @Override
    public void init(Context _context) {
        super.init(_context);
        if (liveshowEventManager==null) {
            liveshowEventManager = new LiveshowEventManager();
        }
        if (liveshowRequestManager==null) {
            liveshowRequestManager = new LiveRoomRequestManager();
        }
        SkinHelper.init(_context);
    }

    /**
     * 销毁数据
     */
    @Override
    public void resetData() {
        LogUtil.i(TAG,"resetData");
        liveInfoEx=null;
        platformID=0;
        isMinimize=true;
        liveshowFloatView = null;
        joinRoomStatu = JoinRoomStatu.unjoined;
    }

    private boolean initConfigs(){
        ConfigBean.ItemsBean _dataBean = null;
        if (configBean==null) {
            return false;
        }
        List<ConfigBean.ItemsBean> itemsBeans = configBean.getItems();
        if (itemsBeans==null) {
            return false;
        }
        for (ConfigBean.ItemsBean dataBean : itemsBeans) {
            if (dataBean.getPlatform_id() == LiveRoomDataManager.PLATFORM_HUYA) {
                _dataBean = dataBean;
                break;
            }
        }
        if (_dataBean==null) {
            return false;
        }
        try {
            return HyConfigManager.getInstance().init(_dataBean);
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }
    /**
     * 初始化虎牙
     * @return
     */
    @Override
    public boolean init() {
        LogUtil.i(TAG, "init start");
        boolean initResult = true;
        if (!hasCheckHyInited) {
            //hy初始化是否成功
            HuyaBerry.instance().setBerryEventDelegate(new HuyaBerry.BerryEvent() {
                @Override
                public void onEventCallback(Map<String, String> dataMap) {
                    if (dataMap == null) {
                        return;
                    }
                    LogUtil.i(TAG, dataMap.toString());
                    if (liveshowEventManager != null) {
                        liveshowEventManager.onEvent(dataMap);
                    }
                }
            });

            if (!initConfigs()) {
                LogUtil.e(TAG,"initConfigs error");
                return false;
            }
            HyConfig hyConfig = HyConfigManager.getInstance().hyConfig;
            LogUtil.i(TAG,"hyConfig:"+hyConfig.toString());
            mHuyaBerryConfig = new HuyaBerryConfig.Builder()
                    .gameId(hyConfig.gameId)
                    .appId(hyConfig.appId)
                    .appKey(hyConfig.appKey)
                    .debugMode(false)
                    .landscapeMode(hyConfig.hyUiConfig.landscapeMode)
                    .build();
            initLiveshowActivity(L.getActivity());
            Activity activity = getLiveshowActivity();
            if (activity != null) {
                try {
                    String userId = getUserId();
                    LogUtil.i(TAG,"init userId " + userId);
                    HuyaBerry.instance().setGameAccountID(userId);
                    HuyaBerry.instance().init(activity.getApplication(), mHuyaBerryConfig);
//                    GlideImageLoader glideImageLoader = new GlideImageLoader();
//                    ImageLoaderProxy.getInstance().setImageLoader(glideImageLoader);
//                    ImageLoaderProxy.getInstance().init(activity);
                }catch (Exception e){
                    e.printStackTrace();
                    initResult = false;
                }
            }
        }
        LogUtil.i(TAG, "init end initResult " + initResult);
        return initResult;
    }

    @Override
    public void initContext(Context context) {
        init(context);
    }

    @Override
    public void uninit() {
        HuyaBerry.instance().uninit();
    }

    @Override
    public void pauseChannel() {

    }

    @Override
    public void resumeChannel() {

    }

    @Override
    public boolean isLiveShow() {
        return joinRoomStatu==JoinRoomStatu.joined;
    }

    /**
     * 打开直播间列表
     */
    public void goLiveshowRoomList() {
        Activity activity = checkValid();
        if (activity != null) {
            if (roomListView==null) {
                roomListView = new RoomListView(activity);
            }
            roomListView.show();
        }

    }
    /**
     * 虎牙SDKso兼容的abi armeabi-v7a、arm64-v8aw
     * @return
     */
    private boolean isSupportAbi(){
        if (BuildConfig.DEBUG) {
            return true;
        }
        if (context!=null) {
            String cpu_architect = ApkInfoUtil.chooseByX86andArm(context);
            LogUtil.i(TAG,"cpu_architect " + cpu_architect);
            return cpu_architect.contains("arm64-v8a")||cpu_architect.contains("armeabi-v7a")
                    && !EmulatorDetectUtil.isEmulator(context);
        }
        return false;
    }
    /**
     * 系统5.0以下不支持
     * @return
     */
    private boolean isSupportSdkVersion(){
        if (BuildConfig.DEBUG) {
            return true;
        }
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP;
    }
    @Override
    public void joinLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        if (this.joinRoomListener==null) {
            this.joinRoomListener = listener;
        }
        if (!isSupportAbi()) {
            callbackInvokeFail(listener,LiveshowResult.error_cpu_supprot);
            LogUtil.e(TAG,"error_cpu_supprot");
            ToastUtil.showToast("当前手机系统不支持");
            return;
        }
        if (!isSupportSdkVersion()) {
            callbackInvokeFail(listener,LiveshowResult.error_sdkcode_support);
            LogUtil.e(TAG,"error_sdkcode_support");
            ToastUtil.showToast("当前手机系统版本过低");
            return;
        }
        if (!ModHelper.get(IAccountMod.class).hasSubmitRole()) {
            LogUtil.e(TAG,"hasSubmitRole false");
            callbackInvokeFail(listener, LiveshowResult.error_inited_not_submitrole);
            return;
        }
        if (joinRoomStatu== JoinRoomStatu.joining) {
            LogUtil.e(TAG,"JoinRoomStatu.joining");
            callbackInvokeFail(listener,LiveshowResult.error_inited_joining);
            return;
        }
        if (joinRoomStatu== JoinRoomStatu.liveshowlist) {
            LogUtil.e(TAG,"JoinRoomStatu.liveshowlist");
            handleRepeatClickLiveshowIcon();
            return;
        }
        if (joinRoomStatu==JoinRoomStatu.joined) {
            LogUtil.e(TAG,"JoinRoomStatu.joined");
            handleRepeatClickLiveshowIcon();
            return;
        }

        if (!hasCheckHyInited) {
            hasCheckHyInited = init();
        }
        if (hasCheckHyInited) {
            LiveshowTrackManager.getInstance().liveIconAction();
            joinRoomStatu = JoinRoomStatu.joining;
            goLiveshowRoomList();
            joinRoomStatu=JoinRoomStatu.liveshowlist;
            LiveshowDanmuManager.getInstance().joinLiveShow();

        }else{
            callbackInvokeFail(this.joinRoomListener,LiveshowResult.error_inited_sdkinit);
        }

    }

    /**
     * false 只是退出直播
     * true 退出直播间和列表
     * @param reset
     */
    public void leaveLiveshowRoomWrapper(boolean reset){
        LogUtil.i(TAG,"leaveLiveshowRoomWrapper reset:"+reset);
        if (!reset) {
            LiveshowDanmuManager.getInstance().leaveRoom();
            Task.post(new Runnable() {
                @Override
                public void run() {
                    if (liveshowFloatView!=null) {
                        liveshowFloatView.release();
                        handleLeaveRoomSuccessCallback();
                    }
                    if (roomListView!=null) {
                        roomListView.show();
                    }
                    uninit();

                    liveInfoEx=null;
                    platformID=0;
                    isMinimize=true;
                    liveshowFloatView = null;
                    joinRoomStatu = JoinRoomStatu.liveshowlist;
                }
            });
        }else{
           leaveLiveshowRoom(null,null);
        }


    }
    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        LiveshowDanmuManager.getInstance().leaveLiveShow();
        LogUtil.i(TAG,"leaveLiveshowRoom");
        if (listener!=null) {
            this.leaveRoomListener = listener;
        }
        Task.post(new Runnable() {
            @Override
            public void run() {
                if (liveshowFloatView!=null) {
                    liveshowFloatView.release();
                    handleLeaveRoomSuccessCallback();
                }
                if (roomListView!=null) {
                    roomListView.release();
                    roomListView = null;
                }
                uninit();
                resetData();
                LiveRoomDataManager.getInstance().clearPlatformData();

            }
        });
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        this.voiceChangeCallback = listener;
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        this.destroyCallback=listener;
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {
        performLiveshowFeature_switchVoice(data,listener);
    }

    @Override
    public void onActive(JSONObject data) {

    }

    @Override
    public void onSubmitRole() {
        if (isSupportAbi() && isSupportSdkVersion()) {
            if (!hasCheckHyInited) {
                LiveRoomDataManager.getInstance().getConfigData(new SimpleSqHttpCallback<ConfigBean>() {
                    @Override
                    public void onSuccess(ConfigBean data) {
                        LogUtil.i(TAG, "onSubmitRole onSuccess ConfigBean " + data);
                        if (data != null) {
                            configBean = data;
                        }
                    }
                });
            } else {
                LogUtil.i(TAG, "onSubmitRole hasCheckHyInited true");
            }

        } else {
            LogUtil.e(TAG, "onSubmitRole not isSupportAbi isSupportSdkVersion ");
        }

    }

    @Override
    public void initSkin(Context context) {
        try {
            SkinManager.getInstance().inject(context);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 是否展示直播间悬浮球
     * @return
     */
    public boolean isLiveshow(){
        return joinRoomStatu==JoinRoomStatu.joined;
    }

    /**
     * 是否展示直播列表
     * @return
     */
    public boolean isLiveshowList(){
        return joinRoomStatu==JoinRoomStatu.liveshowlist;
    }
    /**
     * 恢复视频播放
     */
    @Override
    public void resume() {
        if (isLiveshow()) {
            HuyaBerry.instance().startVideoPlay();
            channelChangeInvokeCallback(true);
        }
    }


    /**
     * 暂停视频播放
     */
    @Override
    public void pause() {
        if (isLiveshow()) {
            HuyaBerry.instance().pauseVideoPlay();
            channelChangeInvokeCallback(false);
        }
    }

    /**
     * 是否开启视频声音
     * @param isVoice
     */
    @Override
    public void switchVoice(boolean isVoice) {
        if (isLiveshow()) {
            HuyaBerry.instance().switchVoice(isVoice);
            channelChangeInvokeCallback(isVoice);
        }
    }

    @Override
    public void minimize() {
        if (roomListView!=null) {
            roomListView.hide();
        }
        isMinimize=true;
    }

    @Override
    public void maximize() {
        isMinimize=false;
    }

    @Override
    public void close() {
        if (liveInfoEx!=null) {
            uninitActivityLifeCycle();
        }
        leaveLiveshowRoomWrapper(isMinimize);
    }

      /**
     * 打开直播悬浮球
     */
    public void watchLive(final int platformID, final LiveListInfo liveListInfo, final WatchLiveCallback watchLiveCallback) {
        getLiveInfoEx(liveListInfo, new GetLiveInfoExCallback() {
            @Override
            public void result(final LiveInfoEx liveInfoEx) {
                if (liveInfoEx!=null) {
                    if (joinRoomStatu == JoinRoomStatu.joined) {
                        return;
                    }

                    final String userId = getUserId();
                    LogUtil.i(TAG,"watchLive userId " + userId);
                    LogUtil.i(TAG,"watchLive anchor uid " + liveListInfo.uid);
                    final long roomId = liveInfoEx.liveInfo.roomId;
                    LiveshowDanmuManager.getInstance().danmuRoomRequestManager.enterRoom(new EnterRoomReqBean(roomId + ""), new SqHttpCallback<EnterRoomRspBean>() {
                        @Override
                        public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                            @Nullable String data) {
                            ToastUtil.showToast("直播间信息加载失败");
                        }

                        @Override
                        public void onSuccess(EnterRoomRspBean data) {
                            if (data.isForbid()) {
                                ToastUtil.showToast("禁止进入房间");
                                return;
                            }
                                HuyaBerry.instance().setGameAccountID(userId);
                                if (!hasCheckHyInited) {
                                    if (watchLiveCallback!=null) {
                                        watchLiveCallback.loading();
                                    }
                                    hasCheckHyInited = init();
                                    if (!hasCheckHyInited) {
                                        LogUtil.e(TAG,"watchLive init error");
                                        ToastUtil.showToast("初始化配置失败");
                                        return;
                                    }
                                    LogUtil.i(TAG,"watchLive init success");
                                }
                                if (liveshowFloatView == null) {
                                    liveshowFloatView = new LiveshowFloatView(getLiveshowActivity());
                                    if (LiveshowDanmuManager.getInstance().onBehaviorListener!=null) {
                                        liveshowFloatView.setOnBehaviorListener(LiveshowDanmuManager.getInstance().onBehaviorListener);
                                    }

                                }
                                liveshowFloatView.showView();
                                if (roomListView!=null) {
                                    roomListView.hide();
                                }
                                if (watchLiveCallback!=null) {
                                    watchLiveCallback.loadFinish(hasCheckHyInited);
                                }
                                if (hasCheckHyInited) {
                                    joinRoomStatu = JoinRoomStatu.joined;
                                    if (liveListInfo!=null) {
                                        LiveshowManager.this.platformID = platformID;
                                        liveInfoEx.liveListInfo = liveListInfo;
                                        LiveshowManager.this.liveInfoEx = liveInfoEx;
                                        liveshowFloatView.play(liveInfoEx);
                                        callbackInvokeSuccess(LiveshowManager.this.joinRoomListener,null,LiveshowResult.success_joinRoom);
                                        channelChangeInvokeCallback(true);
                                        initActivityLifeCycle();
                                        LiveshowDanmuManager.getInstance().joinRoom();
                                    }

                                }
                            }

                        @Override
                        public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                            ToastUtil.showToast("直播间信息加载失败");
                        }
                    });

                }else {
                    ToastUtil.showToast("直播间信息加载失败");
                }
            }
        });



    }
    public void getLiveInfoEx(LiveListInfo liveListInfo, final GetLiveInfoExCallback getLiveInfoExCallback){
        HuyaBerry.instance().getLiveData(liveListInfo.uid, new
                CustomUICallback<LiveInfo>() {
                    @Override
                    public void onResultCallback(int status, LiveInfo liveInfo) {
                        if (status == BaseCallback.SUCCESS) {
                            if (liveInfo.roomId == 0) {
                                ToastUtil.showToast("主播已经离开房间");
                                LogUtil.e(TAG, "主播已经离开房间");
                                return;
                            }
                            LogUtil.i(TAG, liveInfo.toString());
                            VideoInfo videoInfo = PlayerUtils.matchLine(liveInfo,null);
                            if (videoInfo!=null) {
                                if (getLiveInfoExCallback!=null) {
                                    final LiveInfoEx liveInfoEx = new LiveInfoEx();
                                    liveInfoEx.liveInfo = liveInfo;
                                    liveInfoEx.videoInfo = videoInfo;
                                    liveInfoEx.disPlayNames = PlayerUtils.getLines(liveInfo);
                                    Task.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            getLiveInfoExCallback.result(liveInfoEx);
                                        }
                                    });

                                }
                                return;
                            }else{
                                LogUtil.e(TAG, "not match");
                                if (getLiveInfoExCallback!=null) {
                                    getLiveInfoExCallback.result(null);
                                }
                            }
                        } else {
                            LogUtil.e(TAG, "getdata error");
                            if (getLiveInfoExCallback!=null) {
                                getLiveInfoExCallback.result(null);
                            }
                        }
                    }

                    @Override
                    public void onResultListCallback(int status, List vItems) {

                    }
                });
    }

    /**
     * 获取直播间id
     * @return
     */
    public String getRoomId(){
        if (liveInfoEx!=null&&liveInfoEx.liveInfo!=null) {
            return liveInfoEx.liveInfo.roomId+"";
        }
        return "";
    }

    public LiveshowFloatView getLiveshowFloatView() {
        return liveshowFloatView;
    }

    public RoomListView getRoomListView() {
        return roomListView;
    }

    private void performLiveshowFeature_switchVoice(Map<String, String> data, SQResultListener listener){
        if (data.containsKey(LiveShowParamsKey.switchVoice)) {
            boolean switchVoice = data.get(LiveShowParamsKey.switchVoice).equalsIgnoreCase("true");
            if (isLiveshow()) {
                if (switchVoice) {
                    onForeground();
                }else{
                    onBackground();
                }
                if (listener!=null) {
                    listener.onSuccess(new Bundle());
                }
            }
        }
    }
}


