package com.sqwan.liveshow.huya.skin.inflater;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import com.sqwan.common.util.LogUtil;
import com.sqwan.liveshow.huya.skin.attr.SkinViewInterface;

/**
 * Created by znb on 2021-10-20
 */
public class SkinInflaterFactory extends AbsLayoutInflater implements LayoutInflater.Factory2 {
    private static final String TAG = "SkinInflaterFactory";
    private SkinAppLayoutInflater appLayoutInflater;

    @Override
    public View onCreateView(View parent, String name, Context context, AttributeSet attrs) {
        LogUtil.i(TAG, "onCreateView 2 "+ name);

        return createViewWithAttr(parent, name, context, attrs);
    }

    @Override
    public View onCreateView(String name, Context context, AttributeSet attrs) {
        LogUtil.i(TAG, "onCreateView 1 " + name);
        return createViewWithAttr(null, name, context, attrs);
    }

    /**
     * 创建根据View的名称和属性标签
     *
     * @param parent  父View
     * @param name    View的名称
     * @param context 上下文环境
     * @param attrs   view的属性标签
     * @return
     */
    private View createViewWithAttr(View parent, String name, Context context, AttributeSet attrs) {
        View view = null;
        if (appLayoutInflater == null) {
            appLayoutInflater = new SkinAppLayoutInflater();
        }
        view = appLayoutInflater.onCreateView(parent, name, context, attrs);
        if (view instanceof SkinViewInterface) {
            ((SkinViewInterface) view).applySkin();
        }
        return view;
    }

}
