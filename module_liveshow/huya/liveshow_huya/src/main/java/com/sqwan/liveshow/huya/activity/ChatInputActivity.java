package com.sqwan.liveshow.huya.activity;

import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.plugin.standard.RealBaseActivity;
import com.sqwan.common.util.KeyBoardUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.WindowManagerUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.huya.SqR;
import com.sqwan.liveshow.huya.danmu.LiveshowDanmuManager;
import com.sqwan.liveshow.huya.danmu.view.InputView;
import com.sqwan.liveshow.huya.engine.LiveshowManager;
import com.sqwan.liveshow.huya.skin.manager.SkinManager;
import com.sqwan.liveshow.huya.view.LiveshowFloatView;

public class ChatInputActivity extends RealBaseActivity implements KeyBoardUtils.OnSoftKeyBoardChangeListener{
    private static final String TAG = "ChatInputActivity";
    private InputView inputView;
    private Button btnInputCover;
    private LinearLayout llContainer;
    private KeyBoardUtils keyBoardUtils = new KeyBoardUtils();
    private boolean isFirstResume =  true;
    private Task taskKeyboard = Task.create();
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getContext().getTheme().applyStyle(SqResUtils.getStyleId(getContext(), SqR.style.chatInputDialog),true);
        SkinManager.getInstance().inject(getContext());
        setContentView(SqResUtils.getLayoutId(getContext(), SqR.layout.activity_chat_input));
        initLayout();
        inputView = findViewById(SqResUtils.getId(getContext(), SqR.id.inputview));
        llContainer = findViewById(SqResUtils.getId(getContext(),SqR.id.ll_chat_input));
        btnInputCover = findViewById(SqResUtils.getId(getContext(), SqR.id.btnInputCover));
        btnInputCover.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
               hideKeyboard();

            }
        });
        inputView.setSentTextListener(new InputView.SendTextListener() {
            @Override
            public void sendTextContent(String content) {
                LogUtil.i(TAG,"sendTextContent content:"+content);
                LiveshowDanmuManager.getInstance().sendIm(content);
                getEtView().setText("");
                hideKeyboard();
            }
        });

    }

    private void initLayout() {
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE
                        | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        LiveshowFloatView liveshowFloatView = LiveshowManager.getInstance().getLiveshowFloatView();
        int width = WindowManager.LayoutParams.MATCH_PARENT;
//        if (liveshowFloatView!=null) {
//            FloatViewUtils.FloatViewConfig floatViewConfig = liveshowFloatView.floatViewConfig;
//            if (floatViewConfig!=null) {
//                width = floatViewConfig.regionWidth;
//            }
//        }
        getWindow().setLayout(width, WindowManager.LayoutParams.MATCH_PARENT);
    }

    private void showKeyboard(){
        taskKeyboard.oneShot(50, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                ViewUtils.show(btnInputCover);
                return null;
            }
        });
        keyBoardUtils.showSoftInput(getEtView());
    }
    private void hideKeyboard(){
        taskKeyboard.oneShot(50, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                LiveshowDanmuManager.getInstance().setInputTxtTemp(getEtView().getText().toString());
                finish();
                return null;
            }
        });
        LiveshowDanmuManager.getInstance().setInputTxtTemp(getEtView().getText().toString());
        LiveshowDanmuManager.getInstance().hideSoftkeyboardPadding();
        ViewUtils.hidden(inputView);
        keyBoardUtils.hideSoftInput(getEtView());
    }
    private EditText getEtView(){
        return inputView.getLimitedEditText();
    }
    @Override
    public void onResume() {
        super.onResume();
        if (isFirstResume) {
            WindowManagerUtil.handleHideSystemUI(getContext());
            keyBoardUtils.addListener(getContext(), ChatInputActivity.this);
            isFirstResume = false;
            Task.postDelay(50, new Runnable() {
                @Override
                public void run() {
                   showKeyboard();
                    String inputTxtTemp = LiveshowDanmuManager.getInstance().getInputTxtTemp();
                   inputView.getLimitedEditText().setText(inputTxtTemp);
                   inputView.getLimitedEditText().setSelection(inputTxtTemp.length());
                }
            });

        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        keyBoardUtils.removeListener();
    }

    @Override
    public void keyBoardShow(int height) {
        LogUtil.i(TAG,"keyBoardShow height " + height);
        taskKeyboard.stop();
        ViewUtils.show(btnInputCover);
        llContainer.setPadding(llContainer.getPaddingLeft(),llContainer.getPaddingTop(),llContainer.getPaddingRight(),height);
        LiveshowDanmuManager.getInstance().showSoftkeyboardPadding(height);
    }

    @Override
    public void keyBoardHide(int height) {
        LogUtil.i(TAG,"keyBoardHide height " + height);
        ViewUtils.hidden(inputView);
        ViewUtils.hidden(btnInputCover);
        taskKeyboard.stop();
        LiveshowDanmuManager.getInstance().setInputTxtTemp(getEtView().getText().toString());
        LiveshowDanmuManager.getInstance().hideSoftkeyboardPadding();
        finish();
    }

    @Override
    public void viewChanged(int dheight) {
        LogUtil.i(TAG,"viewChanged dheight " + dheight);
    }
}