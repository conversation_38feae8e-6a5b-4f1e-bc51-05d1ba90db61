apply plugin: 'com.android.library'

android {
    compileSdkVersion 30
    buildToolsVersion "26.0.2"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets.main {
        jniLibs.srcDirs 'src/main/libs'
        jni.srcDirs = [] // This prevents the auto generation of Android.mk
    }
}

if (rootProject.file('gradle/gradle-mvn-push.gradle').exists()) {
    apply from: rootProject.file('gradle/gradle-mvn-push.gradle')
}
if (rootProject.file('gradle/gradle-bintray-upload.gradle').exists()) {
    apply from: rootProject.file('gradle/gradle-bintray-upload.gradle')
}