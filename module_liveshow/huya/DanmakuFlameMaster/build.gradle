/*
 * Copyright (C) 2013 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

apply plugin: 'com.android.library'

def SourcePath = 'src/main/'

android {
    compileSdkVersion 30
    buildToolsVersion "26.0.2"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30
    }

    sourceSets {
        main {
            manifest.srcFile "${SourcePath}AndroidManifest.xml"
            java.srcDirs = ["${SourcePath}java"]
            jniLibs.srcDirs = ["${SourcePath}libs"]
        }
        androidTest.setRoot("${SourcePath}../androidTest")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_7
        targetCompatibility JavaVersion.VERSION_1_7
    }
}
if (rootProject.file('gradle/gradle-mvn-push.gradle').exists()) {
    apply from: rootProject.file('gradle/gradle-mvn-push.gradle')
}
if (rootProject.file('gradle/gradle-bintray-upload.gradle').exists()) {
    apply from: rootProject.file('gradle/gradle-bintray-upload.gradle')
}