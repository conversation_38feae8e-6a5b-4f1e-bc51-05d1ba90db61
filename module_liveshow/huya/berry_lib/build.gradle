apply plugin: 'com.android.library'
apply from: libconfig()


def image_version="4.8.0"
repositories {
    flatDir {
        dirs 'libs'
    }
}

android {

    // 支持 JDK 1.8
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
	api fileTree(dir: 'libs', include: ['*.jar'])
    api fileTree(dir: 'libs', include: ['*.aar'])
    implementation ('com.android.support:support-v4:26.0.0')
    api("com.github.bumptech.glide:glide:${image_version}")
    annotationProcessor "com.github.bumptech.glide:compiler:${image_version}" //注解处理器
}
