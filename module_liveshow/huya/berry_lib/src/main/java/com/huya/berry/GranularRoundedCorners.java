package com.huya.berry;

import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.RectF;
import android.graphics.Shader;
import android.os.Build;

import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.load.resource.bitmap.TransformationUtils;
import com.bumptech.glide.util.Synthetic;
import com.bumptech.glide.util.Util;

import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/** A {@link BitmapTransformation} which has a different radius for each corner of a bitmap. */
public final class GranularRoundedCorners extends BitmapTransformation {

   // See #738.
   private static final Set<String> MODELS_REQUIRING_BITMAP_LOCK =
           new HashSet<>(
                   Arrays.asList(
                           // Moto X gen 2
                           "XT1085",
                           "XT1092",
                           "XT1093",
                           "XT1094",
                           "XT1095",
                           "XT1096",
                           "XT1097",
                           "XT1098",
                           // Moto G gen 1
                           "XT1031",
                           "XT1028",
                           "XT937C",
                           "XT1032",
                           "XT1008",
                           "XT1033",
                           "XT1035",
                           "XT1034",
                           "XT939G",
                           "XT1039",
                           "XT1040",
                           "XT1042",
                           "XT1045",
                           // Moto G gen 2
                           "XT1063",
                           "XT1064",
                           "XT1068",
                           "XT1069",
                           "XT1072",
                           "XT1077",
                           "XT1078",
                           "XT1079"));

   /**
    * https://github.com/bumptech/glide/issues/738 On some devices, bitmap drawing is not thread
    * safe. This lock only locks for these specific devices. For other types of devices the lock is
    * always available and therefore does not impact performance
    */
   private static final Lock BITMAP_DRAWABLE_LOCK =
           MODELS_REQUIRING_BITMAP_LOCK.contains(Build.MODEL) ? new ReentrantLock() : new NoLock();

   private static final String ID = "com.huya.berry.GranularRoundedCorners";
   private static final byte[] ID_BYTES = ID.getBytes(CHARSET);

   private final float topLeft;
   private final float topRight;
   private final float bottomRight;
   private final float bottomLeft;

   /** Provide the radii to round the corners of the bitmap. */
   public GranularRoundedCorners(
           float topLeft, float topRight, float bottomRight, float bottomLeft) {
      this.topLeft = topLeft;
      this.topRight = topRight;
      this.bottomRight = bottomRight;
      this.bottomLeft = bottomLeft;
   }

   @Override
   protected Bitmap transform(BitmapPool pool, Bitmap toTransform, int outWidth, int outHeight) {
      return roundedCorners(
              pool, toTransform, topLeft, topRight, bottomRight, bottomLeft);
   }

   @Override
   public boolean equals(Object o) {
      if (o instanceof GranularRoundedCorners) {
         GranularRoundedCorners other = (GranularRoundedCorners) o;
         return topLeft == other.topLeft
                 && topRight == other.topRight
                 && bottomRight == other.bottomRight
                 && bottomLeft == other.bottomLeft;
      }
      return false;
   }

   @Override
   public int hashCode() {
      int hashCode = Util.hashCode(ID.hashCode(), Util.hashCode(topLeft));
      hashCode = Util.hashCode(topRight, hashCode);
      hashCode = Util.hashCode(bottomRight, hashCode);
      return Util.hashCode(bottomLeft, hashCode);
   }

   @Override
   public void updateDiskCacheKey(MessageDigest messageDigest) {
      messageDigest.update(ID_BYTES);

      byte[] radiusData =
              ByteBuffer.allocate(16)
                      .putFloat(topLeft)
                      .putFloat(topRight)
                      .putFloat(bottomRight)
                      .putFloat(bottomLeft)
                      .array();
      messageDigest.update(radiusData);
   }

   public static Bitmap roundedCorners(
           BitmapPool pool,
           Bitmap inBitmap,
           final float topLeft,
           final float topRight,
           final float bottomRight,
           final float bottomLeft) {
      return roundedCorners(pool, inBitmap, (canvas, paint, rect) -> {
         Path path = new Path();
         path.addRoundRect(
                 rect,
                 new float[] {
                         topLeft,
                         topLeft,
                         topRight,
                         topRight,
                         bottomRight,
                         bottomRight,
                         bottomLeft,
                         bottomLeft
                 },
                 Path.Direction.CW);
         canvas.drawPath(path, paint);
      });
   }

   private static Bitmap roundedCorners(
           BitmapPool pool, Bitmap inBitmap, DrawRoundedCornerFn drawRoundedCornerFn) {

      // Alpha is required for this transformation.
      Bitmap.Config safeConfig = getAlphaSafeConfig(inBitmap);
      Bitmap toTransform = getAlphaSafeBitmap(pool, inBitmap);
      Bitmap result = pool.get(toTransform.getWidth(), toTransform.getHeight(), safeConfig);

      result.setHasAlpha(true);

      BitmapShader shader =
              new BitmapShader(toTransform, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
      Paint paint = new Paint();
      paint.setAntiAlias(true);
      paint.setShader(shader);
      RectF rect = new RectF(0, 0, result.getWidth(), result.getHeight());
      BITMAP_DRAWABLE_LOCK.lock();
      try {
         Canvas canvas = new Canvas(result);
         canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
         drawRoundedCornerFn.drawRoundedCorners(canvas, paint, rect);
         clear(canvas);
      } finally {
         BITMAP_DRAWABLE_LOCK.unlock();
      }

      if (!toTransform.equals(inBitmap)) {
         pool.put(toTransform);
      }

      return result;
   }

   // Avoids warnings in M+.
   private static void clear(Canvas canvas) {
      canvas.setBitmap(null);
   }

   private static Bitmap.Config getAlphaSafeConfig(Bitmap inBitmap) {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && Bitmap.Config.RGBA_F16.equals(inBitmap.getConfig())) {
         // Avoid short circuiting the sdk check.
         return Bitmap.Config.RGBA_F16;
      }

      return Bitmap.Config.ARGB_8888;
   }

   private static Bitmap getAlphaSafeBitmap(
           BitmapPool pool, Bitmap maybeAlphaSafe) {
      Bitmap.Config safeConfig = getAlphaSafeConfig(maybeAlphaSafe);
      if (safeConfig.equals(maybeAlphaSafe.getConfig())) {
         return maybeAlphaSafe;
      }

      Bitmap argbBitmap = pool.get(maybeAlphaSafe.getWidth(), maybeAlphaSafe.getHeight(), safeConfig);
      new Canvas(argbBitmap).drawBitmap(maybeAlphaSafe, 0 /*left*/, 0 /*top*/, null /*paint*/);

      // We now own this Bitmap. It's our responsibility to replace it in the pool outside this method
      // when we're finished with it.
      return argbBitmap;
   }

   /** Convenience function for drawing a rounded bitmap. */
   private interface DrawRoundedCornerFn {

      void drawRoundedCorners(Canvas canvas, Paint paint, RectF rect);
   }

   private static final class NoLock implements Lock {

      @Synthetic
      NoLock() {}

      @Override
      public void lock() {
         // do nothing
      }

      @Override
      public void lockInterruptibly() throws InterruptedException {
         // do nothing
      }

      @Override
      public boolean tryLock() {
         return true;
      }

      @Override
      public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
         return true;
      }

      @Override
      public void unlock() {
         // do nothing
      }

      @Override
      public Condition newCondition() {
         throw new UnsupportedOperationException("Should not be called");
      }
   }
}