<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.huya.berry"
    android:versionName="@string/hyberry_version">
    <uses-feature android:glEsVersion="0x00020000" android:required="true" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <application
        android:allowBackup="true"
        android:supportsRtl="true">
        <meta-data
            android:name="android.vendor.full_screen"
            android:value="true" />
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <meta-data
            android:name="com.huya.berry.LiveGlideModule"
            android:value="GlideModule"/>
        <meta-data android:name="NS_APPID" android:value="live_android"/>
        <meta-data android:name="CLIENT_TYPE" android:value="adr_game_sdk"/>
        <meta-data
            android:name="HY_CHANNEL"
            android:value="@string/hyberry_channel" />
        <meta-data
            android:name="HY_APPID"
            android:value="@string/hyberry_appid" />
        <meta-data
            android:name="HY_APPKEY"
            android:value="@string/hyberry_appkey" />
        <meta-data
            android:name="HY_VERSION"
            android:value="@string/hyberry_version" />
        <meta-data
            android:name="FB_APPID"
            android:value="@string/hy_fb_appid" />
        <activity
            android:name="com.huya.berry.webview.WebViewActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="@string/hyberry_webview_scheme"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.huya.berry.webview.ImagePickerActivity"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.NoTitleBar" />
        <activity
            android:name="com.huya.berry.sdkcamera.CameraEchoActivity"
            android:launchMode="singleTop"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">
        </activity>
        <activity
            android:name="com.huya.berry.sdkplayer.floats.view.PlayerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme.Live.Living2"
            android:windowSoftInputMode="stateAlwaysHidden|adjustNothing"/>
        <activity
            android:name="com.huya.berry.sdkplayer.floats.view.PortraitPlayerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Live.Living2"
            android:windowSoftInputMode="stateAlwaysHidden|adjustNothing"/>
        <service
            android:name="com.huya.berry.sdklive.liveTool.LiveToolService"
            android:configChanges="orientation|screenSize|keyboardHidden" />
    </application>
	<uses-sdk tools:overrideLibrary="com.huya.berry.client,com.huya.berry.common,
	        com.huya.berry.live,com.huya.berry.login,com.huya.berry.utils,
	        com.huya.force.capture,com.huya.force.client,com.huya.force.encode,
	        com.huya.force.upload,com.huya.force.utils, com.duowan.ark.hysignalwrapper,
	        com.tencent.mars, com.huya.httpdns.dns, com.huya.hysignal.core,
	        com.duowan.ark.xlog"/>
</manifest>
