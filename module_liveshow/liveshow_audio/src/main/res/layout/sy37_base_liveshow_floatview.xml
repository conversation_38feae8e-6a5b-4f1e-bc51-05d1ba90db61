<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/syrl_container"
    android:layout_width="wrap_content"
    android:layout_height="58dp"
    android:orientation="vertical">

    <RelativeLayout
        android:paddingRight="10dp"
        android:visibility="visible"
        android:id="@+id/syll_player_expand"
        android:layout_width="wrap_content"
        android:layout_height="58dp"
        android:background="@drawable/sy37_base_liveshow_player_expand"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        >

        <ImageView
            android:layout_marginLeft="10dp"
            android:id="@+id/ivdancing"
            android:layout_centerVertical="true"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/sy37_base_liveshow_players_dancing" />

        <TextView
            android:layout_toRightOf="@+id/ivdancing"
            android:layout_centerVertical="true"
            android:maxEms="6"
            android:id="@+id/sytv_anchor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ffffffff"
            android:textSize="12dp"
            tools:text="主播名称六个字" />
        <LinearLayout
            android:gravity="center_vertical"
            android:layout_toRightOf="@+id/sytv_anchor"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/syll_playstatu"
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/syiv_playstatu"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:src="@drawable/sy37_base_liveshow_player_playstatus" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/syll_playclose"
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:gravity="center">

            <ImageView
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:src="@drawable/sy37_base_liveshow_player_close" />
        </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/syll_player_edge"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:background="@drawable/sy37_base_liveshow_player_edge"
        android:gravity="center">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/sy37_base_liveshow_players_dancing" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/syll_player_drag"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:background="@drawable/sy37_base_liveshow_player_drag"
        android:gravity="center">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/sy37_base_liveshow_players_dancing" />
    </LinearLayout>
</RelativeLayout>