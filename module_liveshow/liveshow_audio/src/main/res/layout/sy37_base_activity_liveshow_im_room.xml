<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llimroomcontainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/sy37_liveshow_im_room_bg"
    android:orientation="vertical">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <LinearLayout
        android:background="#B3001726"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <RelativeLayout
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_radio_studio_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="斗罗大陆·武魂觉醒"
                android:textColor="#FFFFFF"
                android:textSize="16dp" />

            <ImageView
                android:id="@+id/iv_im_room_home"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/sy37_base_liveshow_im_anchor_bg"
                android:src="@drawable/sy37_liveshow_im_room_home" />
        </RelativeLayout>

    <LinearLayout
            android:gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/sy37_base_liveshow_im_anchor_bg"
            android:padding="5dp">

            <com.sqwan.liveshow.ui.RoundedImageView
                android:scaleType="centerCrop"
                app:corner_radius="18dp"
                android:id="@+id/iv_anchor_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/sy37_liveshow_im_room_avatar" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingEnd="5dp"
                android:paddingLeft="10dp">

                <TextView
                    android:id="@+id/tv_anchor_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="主播包名称哈哈哈哈哈"
                    android:textColor="#FFFFFF"
                    android:textSize="14dp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="10.5dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/sy37_liveshow_im_room_online" />

                    <TextView
                        android:id="@+id/tv_online_numbers"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="6.5dp"
                        android:text="199"
                        android:textColor="#FFFFFF"
                        android:textSize="10dp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    <android.support.v7.widget.RecyclerView
            android:layout_weight="1"
            android:id="@+id/rv_im_chat_message"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            />

    <LinearLayout
        android:id="@+id/ll_input_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <com.sqwan.supportview.LimitedEditText

        android:layout_weight="1"
        android:maxHeight="68dp"
        android:minHeight="32dp"
        android:maxLength="200"
        android:background="@drawable/sy37_base_liveshow_im_chat_bg"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:id="@+id/et_input_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="和主播聊聊吧～"
        android:textColorHint="#8B8D90"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:textColor="#ffffff"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/tv_im_room_send"
        android:layout_marginStart="7.5dp"
        android:gravity="center"
        android:textSize="12dp"
        android:text="发送"
        android:background="@drawable/sy37_btn_liveshow_send"
        android:layout_gravity="center_vertical"
        android:textColor="@drawable/sy37_txt_liveshow_send"
        android:layout_width="54.5dp"
        android:layout_height="32dp"/>
    </LinearLayout>
    </LinearLayout>
        <View
            android:visibility="gone"
            android:layout_marginBottom="60dp"
            android:id="@+id/clickView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </FrameLayout>
</LinearLayout>
