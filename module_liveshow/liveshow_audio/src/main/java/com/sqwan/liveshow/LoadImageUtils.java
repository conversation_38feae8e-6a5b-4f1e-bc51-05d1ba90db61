package com.sqwan.liveshow;

import android.content.Context;
import android.graphics.Bitmap;

import com.sq.sdk.tool.download.DownloadListener;
import com.sq.sdk.tool.download.DownloadTask;
import com.sqwan.common.util.EnvironmentUtils;

import java.io.File;

public class LoadImageUtils {


    /**获取sd中存储路径
     * @param context
     * @return
     */
    private static String getSDPath(Context context) {
        return EnvironmentUtils.getCommonSubDirPath(context,"avatar");
    }


    public static void loadBitmap(String imageUrl, String fileName, Context context, final LoadImageListener loadImageListener){
        String targetPath = getSDPath(context);

        new DownloadTask(imageUrl, fileName, targetPath, new DownloadListener() {
            @Override
            public void onUpdate(long l, long l1) {

            }

            @Override
            public void onSuccess(File file) {
                loadImageListener.loadSuccess(file);
            }

            @Override
            public void onFailure(Throwable throwable, int i, String s) {

                loadImageListener.loadFailure(i,s);

            }
        });

    }

    public interface LoadImageListener{

        void loadSuccess(File file);

        void loadFailure(int code ,String msg);
    }

}
