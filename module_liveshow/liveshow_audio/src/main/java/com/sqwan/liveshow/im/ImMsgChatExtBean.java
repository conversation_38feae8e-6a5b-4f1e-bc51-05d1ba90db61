package com.sqwan.liveshow.im;

import android.content.Context;

import com.google.sqgson.Gson;
import com.google.sqgson.annotations.Expose;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.base.L;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.request.SignUtils;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.Base64;
import com.sy37sdk.account.AccountCache;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-02 11:26
 */
public class ImMsgChatExtBean {
    public String username="";
    @Expose(serialize = false, deserialize = false)
    private VerifyData _verifyData;
    public String verifyData="";
    private String content="";
    public ImMsgChatExtBean(String username, String content) {
        this.username = username;
        this.content = content;
        _verifyData = new VerifyData(content);
        try {
            verifyData = Base64.encode(AESUtil.encrypt(new Gson().toJson(_verifyData)));
            _verifyData = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static class VerifyData{
        public String uid;
        public String gid;
        public String pid;
        public String idfa="";
        public String idfv="";
        public String oudid="";
        public String imei="";
        public String mac="";
        public String dsid="";
        public String actor_id="";
        public String actor_name="";
        public int actor_level;
        public String sign="";
        public String content="";


        public VerifyData(String content){
            this.content = content;
            fillData(L.getApplicationContext());
        }

        public void fillData(Context context){
            try {
                String imei = ImeiLogic.getInstance(context).getFromCache() == null ? "" : ImeiLogic.getInstance(context).getFromCache().getValue();
                String mac = MacLogic.getInstance(context).getFromCache() == null ? "" : MacLogic.getInstance(context).getFromCache().getValue();
                uid = AccountCache.getUserid(context);
                gid = SqTrackUtil.getGameID(context);
                pid = SqTrackUtil.getPaternerID(context);
                this.imei = imei;
                this.mac = mac;
                BaseBean baseBean = CommonConfigs.getInstance().getBaseUserInfo();
                if (baseBean!=null) {
                    this.actor_id = baseBean.roleId;
                    this.actor_name = baseBean.roleName;
                    this.actor_level = Integer.parseInt(baseBean.roleLevel);
                    this.dsid = baseBean.serverId;
                }
                Map<String,Object> params = new HashMap<>();
                params.put("uid",uid);
                params.put("gid",gid);
                params.put("pid",pid);
                params.put("imei",imei);
                params.put("mac",mac);
                params.put("actor_id",actor_id);
                params.put("actor_name",actor_name);
                params.put("actor_level",actor_level);
                params.put("dsid",dsid);
                params.put("content",content);
                params.put("idfa",idfa);
                params.put("idfv",idfv);
                params.put("oudid",oudid);
                this.sign = SignUtils.sign(params);

            }catch (Exception e){
                e.printStackTrace();
            }

        }
    }

    @Override
    public String toString() {
        return "ImMsgChatExtBean{" +
                "username='" + username + '\'' +
                ", _verifyData=" + _verifyData +
                ", verifyData='" + verifyData + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}

