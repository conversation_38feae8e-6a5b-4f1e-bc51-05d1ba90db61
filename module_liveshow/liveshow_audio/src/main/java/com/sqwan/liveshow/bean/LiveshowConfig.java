package com.sqwan.liveshow.bean;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-08-23 16:04
 * 接口文档：http://yapi.39on.com/project/75/interface/api/11474
 */
public class LiveshowConfig {

    /** AppKey */
    public String appkey = "";

    /** 应用密钥，使用AES加密过 */
    public String appsecret = "";

    @Override
    public String toString() {
        return "LiveshowConfig{" +
                "appkey='" + appkey + '\'' +
                ", appsecret='" + appsecret + '\'' +
                '}';
    }
}
