package com.sqwan.liveshow;

import android.os.Handler;
import android.os.Message;

import com.sqwan.common.util.LogUtil;
import com.youme.voiceengine.MemberChange;
import com.youme.voiceengine.YouMeCallBackInterface;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:46
 */
public class LiveshowEventManager implements YouMeCallBackInterface {
    public LiveshowEventWrapperDispatcher liveshowEventWrapperDispatcher;
    protected String TAG = this.getClass().getSimpleName();
    private String strTips = "请先初始化";
    public boolean mInit = false;
    private final int INIT_OK = 0;
    private final int INIT_NOK = 1;

    //记录当前模式,0 未在房间, 1 主播模式, 2  听主播模式, 3 自由通话模式 4.白名单模式  5.控制其他人麦克风扬声器
    private final int ANCHOR_MODE = 1;
    private final int LISTEN_ANCHOR_MODE = 2;
    private final int NORMAL_ROOM_MODE = 3;
    private final int WHITE_USER_MODE = 4;
    private final int  CONTROL_OTHER_MIC_SPEAK= 5;
    private final int JOIN_FAIL = 100;
    public int mMode = LISTEN_ANCHOR_MODE;

    public LiveshowEventManager() {
        liveshowEventWrapperDispatcher = new LiveshowEventWrapperDispatcher();
    }

    private Handler initHandler = new Handler(){
        public void handleMessage(Message msg){
            super.handleMessage(msg);
            switch (msg.what){
                case INIT_OK:
                    log(strTips);
                    liveshowEventWrapperDispatcher.dispatchInit(true);
                    break;
                case INIT_NOK:
                    log(strTips);
                    liveshowEventWrapperDispatcher.dispatchInit(false);
                    break;
                default:
                    break;
            }
        }
    };
    /*跳转**/
    private Handler joinHandler = new Handler(){
        public void handleMessage(Message msg){
            super.handleMessage(msg);
            switch (msg.what){
                case ANCHOR_MODE:
                    break;
                case LISTEN_ANCHOR_MODE:
                    liveshowEventWrapperDispatcher.dispatchJoinRoom(true);
                    break;
                case NORMAL_ROOM_MODE:
                    break;
                case WHITE_USER_MODE:
                    break;
                case CONTROL_OTHER_MIC_SPEAK:
                    break;

                case JOIN_FAIL:
                    log(strTips);
                    liveshowEventWrapperDispatcher.dispatchJoinRoom(false);
                    break;
                default:
                    break;
            }
        }
    };

    private Handler leaveHandler = new Handler(){
        public void handleMessage(Message msg){
            super.handleMessage(msg);
            switch (msg.what){
                case ANCHOR_MODE:
                    log("已成功退出主播模式");
                    break;
                case LISTEN_ANCHOR_MODE:
                    log("已成功退出听众模式");
                    liveshowEventWrapperDispatcher.dispatchLeaveRoom(true);
                    break;
                case NORMAL_ROOM_MODE:
                    log("已成功退出普通房间");
                    break;
                default:
                    break;
            }
        }
    };
    void log(String logStr){
        LogUtil.d(TAG,logStr);
    }
    @Override
    public void onEvent(int eventType, int errorCode, String channelID, Object param){
        log("OnEvent:event "+eventType + ",error " + errorCode + ",channel " + channelID + ",param_" + param.toString());
        Message msg = new Message();
        switch( eventType ){
            case 0: //YOUME_EVENT_INIT_OK:
                log("Talk 初始化成功");
                strTips = "初始化成功";
                mInit = true;
                msg.what = INIT_OK;
                initHandler.sendMessage(msg);
                break;
            case 1://YOUME_EVENT_INIT_FAILED:
                log("Talk 初始化失败");
                strTips = "初始化失败";
                mInit = false;
                msg.what = INIT_NOK;
                initHandler.sendMessage(msg);
                break;
            case 2://YOUME_EVENT_JOIN_OK:
                log("Talk 进入频道成功，频道："+channelID+" 用户id:"+param);
                msg.what = mMode;
                joinHandler.sendMessage(msg);
                break;
            case 3://YOUME_EVENT_JOIN_FAILED:
                log("Talk 进入频道:"+channelID+"失败,code:"+errorCode);
                strTips = "进入房间失败,error code:"+errorCode;
                msg.what = JOIN_FAIL;
                joinHandler.sendMessage(msg);
                break;
            case 4://YOUME_EVENT_LEAVED_ONE:
                log("Talk 离开单个频道:"+channelID);
                msg.what = mMode;
                leaveHandler.sendMessage(msg);
                break;
            case 5://YOUME_EVENT_LEAVED_ALL:
                log("Talk 离开所有频道，这个回调channel参数为空字符串");
                msg.what = mMode;
                leaveHandler.sendMessage(msg);
                break;
            case 6://YOUME_EVENT_PAUSED:
                log("Talk 暂停");
                liveshowEventWrapperDispatcher.dispatchChannelChange(false);
                break;
            case 7://YOUME_EVENT_RESUMED:
                liveshowEventWrapperDispatcher.dispatchChannelChange(true);
                log("Talk 恢复");
                break;
            case 8://YOUME_EVENT_SPEAK_SUCCESS:///< 切换对指定频道讲话成功（适用于多频道模式）
                break;
            case 9://YOUME_EVENT_SPEAK_FAILED:///< 切换对指定频道讲话失败（适用于多频道模式）
                break;
            case 10://YOUME_EVENT_RECONNECTING:///< 断网了，正在重连
                log("Talk 正在重连");
                break;
            case 11://YOUME_EVENT_RECONNECTED:///< 断网重连成功
                log("Talk 重连成功");
                break;
            case 12://YOUME_EVENT_REC_FAILED:///< 通知录音启动失败（此时不管麦克风mute状态如何，都没有声音输出）
                log("录音启动失败，code："+errorCode);
                break;
            case 13://YOUME_EVENT_BGM_STOPPED:///< 通知背景音乐播放结束
                log("背景音乐播放结束,path："+param);
                break;
            case 14://YOUME_EVENT_BGM_FAILED:///< 通知背景音乐播放失败
                log("背景音乐播放失败,code："+errorCode);
                break;
            case 16://YOUME_EVENT_OTHERS_MIC_ON:///< 其他用户麦克风打开
                log("其他用户麦克风打开,userid:"+param);
                if (param instanceof String) {
                    liveshowEventWrapperDispatcher.dispatcheOthersMicChange(true, (String) param);
                }

                break;
            case 17://YOUME_EVENT_OTHERS_MIC_OFF:///< 其他用户麦克风关闭
                log("其他用户麦克风关闭,userid:"+param);
                if (param instanceof String) {
                    liveshowEventWrapperDispatcher.dispatcheOthersMicChange(false, (String) param);
                }
                break;
            case 18://YOUME_EVENT_OTHERS_SPEAKER_ON:///< 其他用户扬声器打开
                log("其他用户扬声器打开,userid:"+param);
                break;
            case 19://YOUME_EVENT_OTHERS_SPEAKER_OFF: ///< 其他用户扬声器关闭
                log("其他用户扬声器关闭,userid:"+param);
                break;
            case 20://YOUME_EVENT_OTHERS_VOICE_ON: ///< 其他用户进入讲话状态
                log("开始讲话,userid:"+param);
                break;
            case 21://YOUME_EVENT_OTHERS_SPEAKER_OFF: ///< 其他用户停止讲话
                log("停止讲话userid:"+param);
                break;
            case 22://YOUME_EVENT_MY_MIC_LEVEL: ///< 自己的麦克风的语音音量级别
                log("我当前讲话的音量级别是,数值："+errorCode);
                break;
            case 23://YOUME_EVENT_MIC_CTR_ON: ///< 自己的麦克风被其他用户打开
                log("自己的麦克风被其他用户打开，userid："+param);
                break;
            case 24://YOUME_EVENT_MIC_CTR_OFF: ///< 自己的麦克风被其他用户关闭
                log("自己的麦克风被其他用户关闭，userid："+param);
                break;
            case 25://YOUME_EVENT_SPEAKER_CTR_ON: ///< 自己的扬声器被其他用户打开
                log("自己的扬声器被其他用户打开，userid："+param);
                break;
            case 26://YOUME_EVENT_SPEAKER_CTR_OFF: ///< 自己的扬声器被其他用户关闭
                log("自己的扬声器被其他用户关闭，userid："+param);
                break;
            case 27://YOUME_EVENT_LISTEN_OTHER_ON: ///< 取消屏蔽某人语音
                log("取消屏蔽某人语音，userid："+param);
                break;
            case 28://YOUME_EVENT_LISTEN_OTHER_OFF: ///< 屏蔽某人语音
                log("屏蔽某人语音,userid："+param);
                break;
            case 62://YOUME_EVENT_SET_WHITE_USER_LIST_OK: ///对指定频道设置白名单成功，但可能有异常用户
                log("对指定频道设置白名单成功");
                if (errorCode==-501){
                    log("设置白名单部分用户异常：已不在房间");
                }
                break;
            case 63://YOUME_EVENT_SET_WHITE_USER_LIST_FAILED: ///对指定频道设置白名单失败
                log("对指定频道设置白名单失败");
                break;
            default:
                break;
        }

    }

    @Override
    public void onRequestRestAPI(int requestID, int errorCode, String queryParam, String resultJsonStr){
        log("onRequestRestAPI requestID:"+requestID+" errorCode:"+errorCode+" queryParam"+queryParam);
        log(resultJsonStr);
    }

    @Override
    public void onMemberChange(String channelID, MemberChange[] changeList, boolean b) {
        log("OnMemberChange:"+channelID+" member count:"+changeList.length);
        for(int i = 0 ;i < changeList.length; i++) {
            MemberChange obj = changeList[i];
            log("userid:"+obj.userID+" isJoin:"+obj.isJoin);
        }
        liveshowEventWrapperDispatcher.dispatchMemberChange(channelID,changeList,b);
    }



    @Override
    public void onBroadcast(int YouMeBroadcastType, String channelID, String param1, String param2, String content){
        //连麦抢麦通知事件
    }
}
