package com.sqwan.liveshow.bean;

import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-18 20:17
 * 接口文档：http://yapi.39on.com/project/75/interface/api/6374
 */
public class LiveshowChannelInfo {

    /** 频道列表 */
    private List<ChannelsBean> channels;

    public List<ChannelsBean> getChannels() {
        return channels;
    }

    public void setChannels(List<ChannelsBean> channels) {
        this.channels = channels;
    }

    public static class ChannelsBean {

        /** 频道 id */
        private String cid;
        /** 频道名称 */
        private String cname;
        /** 主播 id */
        private String anchor_id;
        /** 主播名称 */
        private String anchor_name;
        /** 主播头像 url */
        private String anchor_avatar;
        /** 应用名称 */
        private String app_name;

        public String getCid() {
            return cid;
        }

        public void setCid(String cid) {
            this.cid = cid;
        }

        public String getCname() {
            return cname;
        }

        public void setCname(String cname) {
            this.cname = cname;
        }

        public String getAnchor_id() {
            return anchor_id;
        }

        public void setAnchor_id(String anchor_id) {
            this.anchor_id = anchor_id;
        }

        public String getAnchor_name() {
            return anchor_name;
        }

        public void setAnchor_name(String anchor_name) {
            this.anchor_name = anchor_name;
        }

        public String getAnchor_avatar() {
            return anchor_avatar;
        }

        public void setAnchor_avatar(String anchor_avatar) {
            this.anchor_avatar = anchor_avatar;
        }

        public String getApp_name() {
            return app_name;
        }

        public void setApp_name(String app_name) {
            this.app_name = app_name;
        }

        @Override
        public String toString() {
            return "ChannelsBean{" +
                    "cid='" + cid + '\'' +
                    ", cname='" + cname + '\'' +
                    ", anchor_id='" + anchor_id + '\'' +
                    ", anchor_name='" + anchor_name + '\'' +
                    ", anchor_avatar='" + anchor_avatar + '\'' +
                    ", app_name='" + app_name + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "LiveshowChannelInfo{" +
                "channels=" + channels +
                '}';
    }
}
