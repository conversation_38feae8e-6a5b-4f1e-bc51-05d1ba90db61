package com.sqwan.liveshow;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.liveshow.IAudioLiveshowManager;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.bean.LiveshowChannelInfo;
import com.sqwan.liveshow.bean.LiveshowConfig;
import com.sqwan.liveshow.common.IListenerManager;
import com.sqwan.liveshow.common.ILiveshowEventWrapper;
import com.sqwan.liveshow.common.ILiveshowViewEvent;
import com.sqwan.liveshow.common.LiveShowParamsKey;
import com.sqwan.liveshow.error.LiveshowResult;
import com.sqwan.liveshow.im.LiveshowImManager;
import com.sqwan.liveshow.request.LiveshowRequestManager;
import com.sqwan.liveshow.trackaction.LiveshowTrackManager;
import com.sqwan.liveshow.ui.LiveshowFloatView;
import com.sqwan.liveshow.ui.LiveshowRoomActivity;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;
import com.youme.voiceengine.MemberChange;
import com.youme.voiceengine.api;
import com.youme.voiceengine.mgr.YouMeManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:01
 */
public class LiveshowManager extends LiveshowBaseManager implements IAudioLiveshowManager, ILiveshowEventWrapper{

    public static LiveshowManager getInstance() {
        return (LiveshowManager) LiveRadioEngine.getInstance().getLiveshowManager();
    }


    private LiveshowManager() {
        liveshowEventManager = new LiveshowEventManager();
        listenerManager = new ListenerManager();
    }



    enum JoinRoomStatu{
        joined,unjoined,joining
    }

    private int onlineCount = -1;
    //    private ILiveshowViewEvent iLiveshowViewEvent;
    private List<ILiveshowViewEvent> iLiveshowViewEvents = new ArrayList<>();
    private LiveshowFloatView liveshowFloatView;
    private LiveshowRequestManager liveshowRequestManager;
    private LiveshowEventManager liveshowEventManager;
    private IListenerManager listenerManager;
    private JoinRoomStatu joinRoomStatu = JoinRoomStatu.unjoined;
    private boolean hasInitNative = false;
    private long sTime, sTimeReally, eTime, eTimeReally;
    private LiveshowChannelInfo liveshowChannelInfo;
    private ActivityLifeCycleUtils.AppVisibilityCallback appVisibilityCallback;
    public boolean isAnchorOnline;
    public LiveshowConfig liveshowConfig=new LiveshowConfig();
    public int getOnlineCount() {
        if ((onlineCount<0)) {
            return 0;
        }
        return onlineCount;
    }

    public void setOnlineCount(int onlineCount) {
        this.onlineCount = onlineCount;
    }

    @Override
    public void init(Context _context) {
        super.init(_context);
        liveshowRequestManager = new LiveshowRequestManager();
        LiveshowImManager.getInstance().init(_context);
    }


    public void addiLiveshowViewEvent(ILiveshowViewEvent iLiveshowViewEvent) {
        iLiveshowViewEvents.add(iLiveshowViewEvent);
    }
    public void removeLiveshowViewEvent(ILiveshowViewEvent iLiveshowViewEvent){
        iLiveshowViewEvents.remove(iLiveshowViewEvent);
    }
    public void resetData(){
        isAnchorOnline = false;
        onlineCount = -1;
        joinRoomStatu = JoinRoomStatu.unjoined;
    }
    @Override
    public boolean init() {
        resetData();
        Context context = checkValid();
        if (context!=null) {
            if (!hasInitNative) {
                api.SetCallback(liveshowEventManager);
                if (YouMeManager.Init(context)) {
                    LogUtil.i(TAG,"YouMeManager.Init");
                    hasInitNative =true;
                }else{
                    callbackInvokeFail(this.joinRoomListener, LiveshowResult.error_inited_youmemanager);
                    return false;
                }
            }
            if (!api.isInited()) {
                LogUtil.i(TAG,"api init");
                String appKey = liveshowConfig.appkey;
                String appSecret = liveshowConfig.appsecret;
                int apiInitResult = api.init(appKey,appSecret,0,"cn");
                LogUtil.i(TAG,"apiInitResult " + apiInitResult);
                if (apiInitResult!=0) {
                    callbackInvokeFail(this.joinRoomListener,LiveshowResult.error_inited_api);
                    return false;
                }

            }
        }

        if (appVisibilityCallback==null) {
            appVisibilityCallback = new ActivityLifeCycleUtils.AppVisibilityCallbackAdapter(){
                @Override
                public void onBackground() {
                    if (isResume) {
                        isResumeLast=true;
                        pauseChannel();
                    }
                }
                @Override
                public void onForeground() {
                    if (isResumeLast) {
                        isResumeLast=false;
                        resumeChannel();
                    }

                }

            };
        }
        ActivityLifeCycleUtils.getInstance().registerActivityListener(appVisibilityCallback);

        register(this);
        isResume=true;
        return true;
    }

    @Override
    public void initContext(Context context) {
        if (this.context==null) {
            init(context);
        }
    }

    @Override
    public void uninit() {
        LogUtil.i(TAG,"uninit");
        if (api.isInited()) {
            api.unInit();
            LogUtil.i(TAG,"api uninit");
        }
        unregister(this);
        resetData();
        if (appVisibilityCallback!=null) {
            ActivityLifeCycleUtils.getInstance().unRegisterActivityListener(appVisibilityCallback);
        }
    }

    private void leaveRoom(){
        releaseLiveShowView();
        if (hasInitNative) {
            if (isJoined()) {
                listenerManager.leaveRoom();
            }else{
                uninit();
                handleLeaveRoomSuccessCallback();
            }
        }


    }
    private void leaveRoomActivty(){
        CopyOnWriteArrayList<Activity> activities = ActivityLifeCycleUtils.getInstance().activities;
        if (activities.size()>0) {
            Activity topActivity = activities.get(activities.size()-1);
            if (ActivityLifeCycleUtils.getInstance().equalActivity(topActivity, LiveshowRoomActivity.class)) {
                LogUtil.i(TAG,"leaveRoomActivty");
                topActivity.finish();
            }
        }

    }

    @Override
    public String getUserId() {
        return AccountCache.getUserid(context);
    }
    public boolean isResume=true;
    public boolean isResumeLast=true;
    public void resumeOrPauseChannel(){
        if (isResume) {
            pauseChannel();
        }else{
            resumeChannel();
        }
    }

    @Override
    public void pauseChannel() {
        if (isJoined()) {
            turnOffAction();
            api.pauseChannel();
            channelChangeInvokeCallback(false);
        }
    }

    @Override
    public void resumeChannel() {
        if (isJoined()) {
            turnOnAction();
            api.resumeChannel();
            channelChangeInvokeCallback(true);
        }
    }

    public boolean isJoined(){
        return joinRoomStatu==JoinRoomStatu.joined;
    }
    @Override
    public boolean isLiveShow() {
        return !TextUtils.isEmpty(getRoomId());
    }
    protected void handleRepeatClickLiveshowIcon(){
        if (liveshowEventManager!=null &&liveshowEventManager.liveshowEventWrapperDispatcher!=null) {
            liveshowEventManager.liveshowEventWrapperDispatcher.dispatchRepeatClickLiveshowIcon();
        }

    }
    private void handleMemberChange(int count){
        if (liveshowEventManager!=null &&liveshowEventManager.liveshowEventWrapperDispatcher!=null) {
            liveshowEventManager.liveshowEventWrapperDispatcher.dispatchMemberChange(count);
        }

    }
    private void goChatRoom(){
        Intent intent = new Intent(checkValid(), LiveshowRoomActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra("Ex",true);
        checkValid().startActivity(intent);


    }
    @Override
    public void joinLiveshowRoom(Map<String, String> data, final SQResultListener listener) {
        LogUtil.i(TAG,"joinLiveshowRoom");
//        goChatRoom();
        if (this.joinRoomListener==null) {
            this.joinRoomListener = listener;
        }
        if (!ModHelper.get(IAccountMod.class).hasSubmitRole()) {
            LogUtil.e(TAG,"hasSubmitRole false");
            callbackInvokeFail(listener,LiveshowResult.error_inited_not_submitrole);
            return;
        }
        if (joinRoomStatu== JoinRoomStatu.joining) {
            LogUtil.e(TAG,"JoinRoomStatu.joining");
            callbackInvokeFail(listener,LiveshowResult.error_inited_joining);
            return;
        }
        if (joinRoomStatu==JoinRoomStatu.joined) {
            LogUtil.e(TAG,"JoinRoomStatu.joined");
            handleRepeatClickLiveshowIcon();
            return;
        }
        joinRoomStatu = JoinRoomStatu.joining;
        liveshowRequestManager.reqGetRadioChannels(new SqHttpCallback<LiveshowChannelInfo>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                callbackInvokeFail(listener, LiveshowResult.error_inited_reqeust);
                joinRoomStatu = JoinRoomStatu.unjoined;
            }

            @Override
            public void onSuccess(LiveshowChannelInfo data) {
                if (data != null) {
                    liveshowChannelInfo = data;
                }
                if (TextUtils.isEmpty(getRoomId())) {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            showEmptyLiveshowTips();
                            callbackInvokeFail(listener, LiveshowResult.error_inited_room_empty);
                            joinRoomStatu = JoinRoomStatu.unjoined;
                        }
                    });

                } else {
                    if (init()) {
                        joinRoomStatu = JoinRoomStatu.joined;
                        Task.post(new Runnable() {
                            @Override
                            public void run() {
                                showLiveShowView(false);
                            }
                        });

                    } else {
                        joinRoomStatu = JoinRoomStatu.unjoined;
                    }
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                callbackInvokeFail(listener, LiveshowResult.error_inited_reqeust);
                joinRoomStatu = JoinRoomStatu.unjoined;
            }
        });

    }

    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        if (listener!=null) {
            this.leaveRoomListener = listener;
        }
        leaveRoom();
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        this.voiceChangeCallback = listener;
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        this.destroyCallback=listener;
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {

    }

    @Override
    public void onActive(JSONObject data) {
        LiveShowUrl.rlapi = data.optString("rlapi");
        LiveShowUrl.rlcapi = data.optString("rlcapi");
    }

    @Override
    public void onSubmitRole() {
        liveshowRequestManager.reqGetRadioConfig(new SimpleSqHttpCallback<LiveshowConfig>() {
            @Override
            public void onSuccess(LiveshowConfig data) {
                try {
                    LogUtil.i(TAG, "befault LiveshowConfig:" + data);
                    liveshowConfig.appsecret = new String(AESUtil.decrypt(data.appsecret));
                    liveshowConfig.appkey = new String(AESUtil.decrypt(data.appkey));
                    LogUtil.i(TAG, "after LiveshowConfig:" + liveshowConfig);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void initSkin(Context context) {

    }

    private void showLiveShowView(final boolean justView) {
        if (liveshowFloatView==null) {
            liveshowFloatView = new LiveshowFloatView(checkValid());
            liveshowFloatView.show();
            sTime = System.currentTimeMillis();
            if (justView) {
                return;
            }
            for (ILiveshowViewEvent iLiveshowViewEvent : iLiveshowViewEvents) {
                if (iLiveshowViewEvent!=null) {
                    iLiveshowViewEvent.onShowLiveShowView();
                }
            }


        }

    }

    private void releaseLiveShowView() {
        Task.post(new Runnable() {
            @Override
            public void run() {
                leaveRoomActivty();
                eTime = System.currentTimeMillis();
                for (ILiveshowViewEvent iLiveshowViewEvent : iLiveshowViewEvents) {
                    if (iLiveshowViewEvent!=null) {
                        iLiveshowViewEvent.onReleaseLiveShowView();

                    }
                }

                if (liveshowFloatView!=null) {
                    liveshowFloatView.release();
                    liveshowFloatView=null;
                }

            }
        });

    }


    @Override
    public void initCallback(boolean success) {
        if (success) {
            listenerManager.joinRoom(getUserId(),getRoomId());
        }else{

        }
    }

    @Override
    public void joinRoomCallback(boolean success) {
        if (success) {
            joinRoomStatu = JoinRoomStatu.joined;
            api.getChannelUserList(getRoomId(),-1,true);
            listenerManager.init();
            callbackInvokeSuccess(this.joinRoomListener,null,LiveshowResult.success_joinRoom);
            channelChangeInvokeCallback(true);
            turnOnAction();
        }else{
            joinRoomStatu = JoinRoomStatu.unjoined;
            callbackInvokeFail(this.joinRoomListener, LiveshowResult.error_joinRoom);
        }
    }

    @Override
    public void leaveRoomCallback(boolean success) {
        if (success) {
            handleLeaveRoomSuccessCallback();
        }else{
            callbackInvokeFail(this.leaveRoomListener, LiveshowResult.error_leaveRoom);

        }
        uninit();
        turnOffAction();
    }

    @Override
    public void channelChange(boolean resume) {
        this.isResume=resume;
        channelChangeInvokeCallback(this.isResume);
    }

    public void channelChangeInvokeCallback(boolean resume){
        Bundle data = new Bundle();
        data.putBoolean(LiveShowParamsKey.isResume,resume);
        callbackInvokeSuccess(this.voiceChangeCallback,data,LiveshowResult.success_voiceChange);

    }
    @Override
    public void othersMicChange(boolean isOn, String userid) {
        if (isOn) {
            updateChannelInfo(userid);
        }
    }
    private void handleOnlineCount(String channelID, MemberChange[] changeList, boolean isJoin){
        if (onlineCount==-1) {
            onlineCount = changeList.length;
        }else{
            for (MemberChange memberChange : changeList) {
                if (memberChange.isJoin) {
                    onlineCount+=1;
                }else{
                    onlineCount-=1;
                }
            }
        }
        handleMemberChange(onlineCount);

    }

    @Override
    public void onMemberChange(String channelID, MemberChange[] changeList, boolean isJoin) {
        handleOnlineCount(channelID,changeList,isJoin);
        if (TextUtils.equals(channelID,getRoomId())) {
            for (MemberChange memberChange : changeList) {
                if (TextUtils.equals(getAnchorId(),memberChange.userID)) {
                    isAnchorOnline = memberChange.isJoin;
                    LogUtil.i(TAG,"isAnchorOnline " + isAnchorOnline);
                    if (!memberChange.isJoin) {
                        for (ILiveshowViewEvent iLiveshowViewEvent : iLiveshowViewEvents) {
                            if (iLiveshowViewEvent!=null) {
                                iLiveshowViewEvent.checkEmptyRoom();
                            }
                        }
                        break;
                    }
                }
            }
        }
    }

    @Override
    public void onMemberChange(int count) {

    }

    @Override
    public void onRepeatClickLiveshowIcon() {

    }

    public LiveshowChannelInfo.ChannelsBean getChannelInfo(){
        if (liveshowChannelInfo!=null && !liveshowChannelInfo.getChannels().isEmpty()) {
            return liveshowChannelInfo.getChannels().get(0);
        }
        return null;
    }
    @Override
    public String getRoomId() {
        LiveshowChannelInfo.ChannelsBean channelsBean = getChannelInfo();
        if (channelsBean!=null) {
            return channelsBean.getCid();
        }
        return "";
    }

    /**
     * 开始收听/继续收听
     */
    public void turnOnAction() {
        /*
        不能这样子判断，因为 isResume 默认为 true，这样下面的代码就得不到执行
        if (isResume) {
            // 当前必须不是开始/继续收听状态
            return;
        }
        */
        sTimeReally = SystemClock.uptimeMillis();
        LiveshowTrackManager.getInstance().turnOnAction(getRoomId(),getAnchorId());
    }

    /**
     * 退出直播间/退出游戏/暂停收听/切到后台
     */
    public void turnOffAction() {
        if (!isResume) {
            // 当前必须不是暂停/停止收听状态
            return;
        }
        eTimeReally = SystemClock.uptimeMillis();
        long dTimeReally = eTimeReally - sTimeReally;
        LiveshowTrackManager.getInstance().turnOffAction(getRoomId(),getAnchorId(),dTimeReally + "");
    }

    public String getAnchorId(){
        LiveshowChannelInfo.ChannelsBean channelsBean = getChannelInfo();
        if (channelsBean!=null) {
            return channelsBean.getAnchor_id();
        }
        return "";
    }
    public String getAnchorName(){
        LiveshowChannelInfo.ChannelsBean channelsBean = getChannelInfo();
        if (channelsBean!=null) {
            return channelsBean.getAnchor_name();
        }
        return "";
    }
    private void close(){
        leaveRoom();
    }

    public void register(ILiveshowEventWrapper iLiveshowEventWrapper){
        if (liveshowEventManager.liveshowEventWrapperDispatcher!=null) {
            liveshowEventManager.liveshowEventWrapperDispatcher.register(iLiveshowEventWrapper);
        }
    }
    public void unregister(ILiveshowEventWrapper iLiveshowEventWrapper){
        if (liveshowEventManager.liveshowEventWrapperDispatcher!=null) {
            liveshowEventManager.liveshowEventWrapperDispatcher.unregister(iLiveshowEventWrapper);
        }
    }

    private void updateChannelInfo(String otherMicChangedUid) {
        if (liveshowRequestManager != null) {
            liveshowRequestManager.reqGetRadioChannel(getRoomId(), new SimpleSqHttpCallback<LiveshowChannelInfo.ChannelsBean>() {
                @Override
                public void onSuccess(LiveshowChannelInfo.ChannelsBean data) {
                    String anchorId = data.getAnchor_id();
                    if (!anchorId.isEmpty()) {
                        if (!liveshowChannelInfo.getChannels().isEmpty()) {
                            LiveshowChannelInfo.ChannelsBean channelsBean = liveshowChannelInfo.getChannels().get(0);
                            channelsBean.setAnchor_id(anchorId);
                            channelsBean.setAnchor_name(data.getAnchor_name());
                            channelsBean.setAnchor_avatar(data.getAnchor_avatar());
                        }
                        for (ILiveshowViewEvent iLiveshowViewEvent : iLiveshowViewEvents) {
                            if (iLiveshowViewEvent != null) {
                                iLiveshowViewEvent.update(data);
                            }
                        }

                    }
                }
            });
        }
    }
    private void showEmptyLiveshowTips(){
        Task.post(new Runnable() {
            @Override
            public void run() {
                new CommonAlertDialog.Builder(checkValid())
                        .setTitle("主播未开播")
                        .setMessage("主播走开啦，请稍后再来")
                        .setPositiveButton("确认",null)
                        .showEx();
            }
        });

    }
    public void close(Context context) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            new CommonAlertDialog.Builder(activity)
                    .setTitle("退出电台")
                    .setMessage("退出将不再收听电台音频")
                    .setPositiveButton("退出", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            LiveshowManager.getInstance().close();
                        }
                    })
                    .setNegativeButton("取消", null)
                    .showEx();
        }


    }
    public interface MinimizeCallback{
        void callbcak(boolean isShow,boolean isFinish);
    }
    private void finish(Activity activity){
        activity.finish();
    }
    public void  minimize( Context context, final MinimizeCallback minimizeCallback) {
        if (context instanceof Activity) {
            final Activity activity = (Activity) context;
            if (!LiveshowManager.getInstance().isAnchorOnline) {
                finish(activity);
                return;
            }
            Dialog dialog = new CommonAlertDialog.Builder(activity)
                    .setTitle("退出电台")
                    .setMessage("退出将不再收听电台音频")
                    .setNegativeButton("退出", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (minimizeCallback!=null) {
                                minimizeCallback.callbcak(false,true);
                            }
                            LiveshowManager.getInstance().close();
                            finish(activity);
                        }
                    })
                    .setPositiveButton("最小化", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (minimizeCallback!=null) {
                                minimizeCallback.callbcak(false,true);
                            }
                            finish(activity);
                        }
                    })
                    .setmCanceledOnTouchOutside(true)
                    .setCancelable(true)
                    .showEx();
            if (minimizeCallback!=null) {
                minimizeCallback.callbcak(true,false);
            }
            dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    if (!activity.isFinishing()) {
                        if (minimizeCallback!=null) {
                            minimizeCallback.callbcak(false,false);
                        }
                    }

                }
            });
        }

    }

}
