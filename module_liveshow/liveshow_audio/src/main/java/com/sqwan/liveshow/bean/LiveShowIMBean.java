package com.sqwan.liveshow.bean;

import java.util.List;

public class LiveShowIMBean {

    String radioStudioName;

     AnchorMessageBean anchorMessageBean;

     List<UserImBean> userIMBean;

    public AnchorMessageBean getAnchorMessageBean() {
        return anchorMessageBean;
    }

    public void setAnchorMessageBean(AnchorMessageBean anchorMessageBean) {
        this.anchorMessageBean = anchorMessageBean;
    }

    public List<UserImBean> getUserIMBean() {
        return userIMBean;
    }

    public void setUserIMBean(List<UserImBean> userIMBean) {
        this.userIMBean = userIMBean;
    }


    public String getRadioStudioName() {
        return radioStudioName;
    }

    public void setRadioStudioName(String radioStudioName) {
        this.radioStudioName = radioStudioName;
    }

    public static class AnchorMessageBean{
        String avatarUrl;
        String anchorName;
        int onlineNumber;

        public String getAnchorName() {
            return anchorName;
        }

        public void setAnchorName(String anchorName) {
            this.anchorName = anchorName;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public int getOnlineNumber() {
            return onlineNumber;
        }

        public void setOnlineNumber(int onlineNumber) {
            this.onlineNumber = onlineNumber;
        }
    }

   public static class  UserImBean{
        String userName="";
        String userChatContent="";
        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getUserChatContent() {
            return userChatContent;
        }

        public void setUserChatContent(String userChatContent) {
            this.userChatContent = userChatContent;
        }

       @Override
       public String toString() {
           return "UserImBean{" +
                   "userName='" + userName + '\'' +
                   ", userChatContent='" + userChatContent + '\'' +
                   '}';
       }
   }
}
