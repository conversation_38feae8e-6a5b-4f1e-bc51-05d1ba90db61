package com.sqwan.liveshow.ui;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.LiveshowManager;
import com.sqwan.liveshow.bean.LiveshowChannelInfo;
import com.sqwan.liveshow.common.ILiveshowEventWrapper;
import com.sqwan.liveshow.common.ILiveshowEventWrapperAdapter;
import com.sqwan.liveshow.common.ILiveshowViewEvent;
import com.sy37sdk.account.floatview.DragViewLayout;
import com.sy37sdk.account.floatview.FloatViewUtils;
import com.sy37sdk.account.floatview.FloatWindow;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-13 10:39
 */
public class LiveshowFloatView extends DragViewLayout {
    private ActivityLifeCycleUtils.AppVisibilityCallbackAdapter appVisibilityCallback;
    enum FloatViewStatu {
        Drag, Edge, Expand
    }

    private ILiveshowEventWrapper iLiveshowEventWrapper;
    private ILiveshowViewEvent iLiveshowViewEvent;
    private FloatViewStatu floatViewStatu = FloatViewStatu.Edge;
    private ViewGroup syll_player_expand, syll_playstatu, syll_player_edge, syll_playclose, syll_player_drag;
    private ImageView syiv_playstatu;
    private TextView sytv_anchor;
    public LiveshowFloatMaskView liveshowFloatMaskView;
    public int dx;
    boolean toReverz;
    private void goChatRoom(){
        Intent intent = new Intent(mContext, LiveshowRoomActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.putExtra("Ex",true);
        mContext.startActivity(intent);


    }
    public LiveshowFloatView(Context context) {
        super(context);
        View.inflate(context, SqResUtils.getLayoutId(context, "sy37_base_liveshow_floatview"), this);
        syll_player_expand = findViewById(SqResUtils.getId(context, "syll_player_expand"));
        syll_player_edge = findViewById(SqResUtils.getId(context, "syll_player_edge"));
        syll_player_drag = findViewById(SqResUtils.getId(context, "syll_player_drag"));
        syll_playstatu = findViewById(SqResUtils.getId(context, "syll_playstatu"));
        syll_playclose = findViewById(SqResUtils.getId(context, "syll_playclose"));
        syiv_playstatu = findViewById(SqResUtils.getId(context, "syiv_playstatu"));
        sytv_anchor = findViewById(SqResUtils.getId(context, "sytv_anchor"));
        syll_playclose.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                LiveshowManager.getInstance().close(mContext);
            }
        });
        syll_playstatu.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                LiveshowManager.getInstance().resumeOrPauseChannel();
            }
        });
        syll_player_expand.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                goChatRoom();
            }
        });
        setFilterDragClickListener(syll_player_edge, new FloatWindow.ClickListenerAdapter() {
            @Override
            public void onClick(View v, int x, int y) {
                if (filterClick(x, y)) {
                    return;
                }
                updateFloatViewStatu(FloatViewStatu.Expand);
                LiveshowFloatView.this.canDispatchTouchEvent = false;
                liveshowFloatMaskView.show();
            }
        });
        setFilterDragClickListener(syll_player_expand, new FloatWindow.ClickListenerAdapter() {
            @Override
            public void onClick(View v, int x, int y) {
                if (filterClick(x, y)) {
                    return;
                }

            }
        });

        setOnDragCallBack(new FloatWindow.OnDragCallBack() {
            @Override
            public void onStayEdge() {

            }

            @Override
            public void onStartDrag() {
                updateFloatViewStatu(FloatViewStatu.Drag);
            }
        });
        if (iLiveshowEventWrapper == null) {
            iLiveshowEventWrapper = new ILiveshowEventWrapperAdapter() {
                @Override
                public void joinRoomCallback(boolean success) {
                    super.joinRoomCallback(success);
                    if (success) {
                        Task.post(new Runnable() {
                            @Override
                            public void run() {
                                LogUtil.i(TAG, "joinRoomCallback ");
                                syiv_playstatu.setSelected(false);
                                sytv_anchor.setText(LiveshowManager.getInstance().getAnchorName());

                                goChatRoom();
                            }
                        });

                    }


                }

                @Override
                public void channelChange(final boolean resume) {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            LogUtil.i(TAG, "channelChange " + resume);
                            syiv_playstatu.setSelected(!resume);
                        }
                    });
                }


                @Override
                public void othersMicChange(final boolean isOn, final String userid) {

                }

                @Override
                public void onRepeatClickLiveshowIcon() {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            goChatRoom();
                        }
                    });

                }
            };
        }
        if (iLiveshowViewEvent == null) {
            iLiveshowViewEvent = new ILiveshowViewEvent() {
                @Override
                public void update(LiveshowChannelInfo.ChannelsBean channelsBean) {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            sytv_anchor.setText(LiveshowManager.getInstance().getAnchorName());
                        }
                    });
                }

                @Override
                public void onShowLiveShowView() {

                }

                @Override
                public void onReleaseLiveShowView() {

                }

                @Override
                public void checkEmptyRoom() {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            sytv_anchor.setText("暂无主播开播");
                        }
                    });

                }
            };
            LiveshowManager.getInstance().addiLiveshowViewEvent(iLiveshowViewEvent);
        }

        liveshowFloatMaskView = new LiveshowFloatMaskView(context);
        liveshowFloatMaskView.setLiveshowFloatView(this);
        liveshowFloatMaskView.closeCallback = new LiveshowFloatMaskView.CloseCallback() {
            @Override
            public void invoke() {
                LiveshowFloatView.this.canDispatchTouchEvent = true;
                updateFloatViewStatu(FloatViewStatu.Edge);

            }
        };
        LiveshowManager.getInstance().register(iLiveshowEventWrapper);
        if (appVisibilityCallback==null) {
            appVisibilityCallback = new ActivityLifeCycleUtils.AppVisibilityCallbackAdapter(){
                @Override
                public void onActivityDestroyed(Activity activity) {
                    if (ActivityLifeCycleUtils.getInstance().equalActivity(activity, LiveshowRoomActivity.class)) {
                        LogUtil.i(TAG,"LiveshowRoomActivity onActivityDestroyed");
                        if (liveshowFloatMaskView!=null) {
                            liveshowFloatMaskView.dismiss();
                        }
                    }

                }
            };
            ActivityLifeCycleUtils.getInstance().registerActivityListener(appVisibilityCallback);
        }

    }

    public boolean isCheckStayEdge() {
        return false;
    }

    public boolean isCheckRecordPos() {
        return false;
    }

    @Override
    public boolean isCheckShowCompelete() {
        return false;
    }

    public void show() {
        if (liveshowFloatMaskView != null) {
            liveshowFloatMaskView.show();
        }
        init();
        if (config != null) {
            int y = config.regionHeight / 3 * 2;
            updateY(y);
        }
        addView();
        updateFloatViewStatu(FloatViewStatu.Edge);

    }

    @Override
    protected void handleOnAnimationUpdate(ValueAnimator animation) {
        super.handleOnAnimationUpdate(animation);
        updateFloatViewStatu(FloatViewStatu.Edge);
    }

    @Override
    public void release() {
        if (liveshowFloatMaskView != null) {
            liveshowFloatMaskView.release();
        }
        super.release();
        if (iLiveshowViewEvent != null) {
            LiveshowManager.getInstance().removeLiveshowViewEvent(iLiveshowViewEvent);
        }
        if (iLiveshowEventWrapper != null) {
            LiveshowManager.getInstance().unregister(iLiveshowEventWrapper);
        }
        if (appVisibilityCallback!=null) {
            ActivityLifeCycleUtils.getInstance().unRegisterActivityListener(appVisibilityCallback);
        }
    }

    private void goneAll() {
        ViewUtils.gone(syll_player_expand);
        ViewUtils.gone(syll_player_edge);
        ViewUtils.gone(syll_player_drag);
    }


    private boolean isRightExpand() {
        return (!isLeft && this.floatViewStatu == FloatViewStatu.Expand);
    }

    private void updateFloatViewStatu(final FloatViewStatu floatViewStatu) {
        toReverz = false;
        if (!isRightExpand()) {
            goneAll();
        }

        if (floatViewStatu == FloatViewStatu.Edge) {
            syll_player_edge.setSelected(isLeft);
            if (isRightExpand()) {
                update();
                Task.post(new Runnable() {
                    @Override
                    public void run() {
                        goneAll();
                        ViewUtils.show(syll_player_edge);
                        LiveshowFloatView.this.floatViewStatu = floatViewStatu;
                        update();
                    }
                });
                return;

            } else {
                ViewUtils.show(syll_player_edge);
            }

        } else if (floatViewStatu == FloatViewStatu.Drag) {
            ViewUtils.show(syll_player_drag);
        } else if (floatViewStatu == FloatViewStatu.Expand) {
            ViewUtils.show(syll_player_expand);
            Task.post(new Runnable() {
                @Override
                public void run() {
                    syll_player_expand.setSelected(isLeft);
                    if (!isLeft) {
                        update();
                        int edgeWidth = syll_player_edge.getWidth();
                        int expandWidth = syll_player_expand.getWidth();
                        LogUtil.i(TAG, String.format("edgeWidth:%d expandWidth:%d", edgeWidth, expandWidth));
                        dx = expandWidth - edgeWidth;
                        floatLayoutParams.x -= dx;
                        toReverz = true;
                    }
                    syiv_playstatu.setSelected(!LiveshowManager.getInstance().isResume);
                    LiveshowFloatView.this.floatViewStatu = floatViewStatu;
                    update();
                    if (toReverz) {
                        floatLayoutParams.x += dx;
                    }
                }
            });
            return;

        }
        this.floatViewStatu = floatViewStatu;
        update();

    }

    private boolean filterClick(int x, int y) {
        if (FloatViewUtils.isTouchPointInView(syll_playstatu, x, y) || FloatViewUtils.isTouchPointInView(syll_playclose, x, y)) {
            return true;
        }
        return false;
    }

    public boolean fixWindowManager(MotionEvent event){
        if (ViewUtils.isShow(syll_player_expand)) {
            int x =  (int) event.getX();
            int y =  (int) event.getY();
            if(FloatViewUtils.isTouchPointInView(syll_playclose,x,y)){
                LiveshowManager.getInstance().close(mContext);
                return true;
            }
            if(FloatViewUtils.isTouchPointInView(syll_playstatu,x,y)){
                LiveshowManager.getInstance().resumeOrPauseChannel();
                return true;
            }
            if(FloatViewUtils.isTouchPointInView(syll_player_expand,x,y)){
                goChatRoom();
                return true;
            }

        }
        return false;
    }
}
