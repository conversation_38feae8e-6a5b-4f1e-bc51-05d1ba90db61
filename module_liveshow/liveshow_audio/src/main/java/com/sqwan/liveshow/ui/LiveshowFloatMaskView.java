package com.sqwan.liveshow.ui;

import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.account.floatview.CheckSystemUiViewBase;
import com.sy37sdk.account.floatview.DragViewLayout;
import com.sy37sdk.account.floatview.FloatViewUtils;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-17 12:02
 */
public class LiveshowFloatMaskView extends CheckSystemUiViewBase {
    private LiveshowFloatView liveshowFloatView;

    public void setLiveshowFloatView(LiveshowFloatView liveshowFloatView) {
        if (isFixWindowManager()) {
            this.liveshowFloatView = liveshowFloatView;
        }
    }

    private boolean isFixWindowManager() {
        return (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP
                && Build.VERSION.SDK_INT < Build.VERSION_CODES.M);

    }

    public LiveshowFloatMaskView(Context context) {
        super(context);
        View.inflate(context, SqResUtils.getLayoutId(context, "sy37_base_liveshow_floatview_mask"), this);
        if (isFixWindowManager()) {
            setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (event.getAction()== MotionEvent.ACTION_UP) {
                        if (liveshowFloatView != null) {
                            if (liveshowFloatView.fixWindowManager(event)) {
                                return false;
                            }

                        }
                        dismiss();
                    }
                    return true;
                }
            });
        } else {
            setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });

        }


    }

    @Override
    public void initLayoutParams() {
        super.initLayoutParams();
        floatLayoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        floatLayoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        ViewUtils.gone(this);

    }

    public void show() {
        if (!isAttachedToWindow()) {
            init();
            addView();
        } else {
            ViewUtils.show(this);
            update();
        }


    }

    public void dismiss() {
        ViewUtils.gone(this);
        update();
        if (closeCallback != null) {
            closeCallback.invoke();
        }
    }

    @Override
    public void release() {
        super.release();
    }
}
