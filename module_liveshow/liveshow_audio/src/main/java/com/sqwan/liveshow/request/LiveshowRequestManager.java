package com.sqwan.liveshow.request;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.liveshow.LiveShowUrl;
import com.sqwan.liveshow.bean.LiveshowChannelInfo;
import com.sqwan.liveshow.bean.LiveshowConfig;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-12 09:09
 */
public class LiveshowRequestManager {

    public void reqGetRadioChannels(SqHttpCallback<LiveshowChannelInfo> callback) {
        SqRequest.of(LiveShowUrl.rlapi)
            .signV3()
            .addParamsTransformer(new LiveShowParams())
            .post(callback, LiveshowChannelInfo.class);
    }

    public void reqGetRadioChannel(String cid, SqHttpCallback<LiveshowChannelInfo.ChannelsBean> callback) {
        SqRequest.of(LiveShowUrl.rlcapi)
            .signV3()
            .addParam("cid", cid)
            .addParamsTransformer(new LiveShowParams())
            .post(callback, LiveshowChannelInfo.ChannelsBean.class);
    }

    public void reqGetRadioConfig(SqHttpCallback<LiveshowConfig> callback) {
        SqRequest.of(LiveShowUrl.radio_config)
            .signV3()
            .addParamsTransformer(new LiveShowParams())
            .get(callback, LiveshowConfig.class);
    }
}
