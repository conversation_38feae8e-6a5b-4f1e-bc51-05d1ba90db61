package com.sqwan.liveshow.trackaction;

import com.sqwan.common.mod.liveshow.IAudioLiveshowTrackManager;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;

import java.util.HashMap;

/**
 *    author : 黄锦群
 *    time   : 2022/08/30
 *    desc   : 电台埋点管理类
 */
public class LiveshowTrackManager extends LiveshowTrackBaseManager implements IAudioLiveshowTrackManager {

    public static LiveshowTrackManager getInstance(){
        return (LiveshowTrackManager) LiveRadioEngine.getInstance().getLiveshowTrackManager();
    }

    /**
     * 开始/继续收听上报埋点
     */
    public void turnOnAction(String channelId ,String anchorId) {
        HashMap<String,String> params = new HashMap<>(2);
        params.put("channel_id",channelId);
        params.put("anchor_id",anchorId);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.FM_TURN_ON, params);
    }

    /**
     * 暂停/停止收听埋点
     */
    public void turnOffAction(String channelId ,String anchorId, String fmTime){
        HashMap<String,String> params = new HashMap<>(2);
        params.put("channel_id",channelId);
        params.put("anchor_id",anchorId);
        params.put("fm_time",fmTime);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.FM_TURN_OFF, params);
    }

    /**
     * 发送评论埋点
     */
    public void commentsAction(String channelId , String anchorId){
        HashMap<String,String> params = new HashMap<>(2);
        params.put("channel_id", channelId);
        params.put("anchor_id", anchorId);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.FM_COMMETNS, params);
    }
}