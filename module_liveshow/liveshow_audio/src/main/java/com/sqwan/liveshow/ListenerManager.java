package com.sqwan.liveshow;

import com.sqwan.liveshow.common.IListenerManager;
import com.youme.voiceengine.api;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:26
 */
public class ListenerManager implements IListenerManager {
    @Override
    public void setSpeakerMute(boolean isMute) {
        api.setSpeakerMute(isMute);
    }

    @Override
    public void setMicrophoneMute(boolean isMute) {
        api.setMicrophoneMute(isMute);
    }

    @Override
    public void setVolume(int volume) {
        api.setVolume(volume);
    }

    @Override
    public void leaveRoom() {
        api.leaveChannelAll();
    }

    @Override
    public void joinRoom(String userId, String roomId) {
        if (!api.isJoined()) {
            api.joinChannelSingleMode(userId,roomId,3);
        }
    }

    @Override
    public void init() {
        setSpeakerMute(false);
        setMicrophoneMute(true);
        setVolume(100);
    }

    @Override
    public void uninit() {

    }

}
