package com.sqwan.liveshow.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sqwan.TestConst;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.bean.LiveShowIMBean;

import java.sql.Time;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.Timer;
import java.util.TimerTask;
import java.util.logging.LogRecord;

import static com.sqnetwork.voly.VolleyLog.TAG;

public class LiveshowIMAdapter extends RecyclerView.Adapter<LiveshowIMAdapter.IMViewHolder> {

    private Task task = Task.create();

    private Context mContext;

    private int USER_LIST_SIZE = 500;

    private int SHOW_LIST_SIZE = 300;

    private int REPEAT_TIME = 1000;

    // 缓冲区设置为 500
    private volatile LinkedList<LiveShowIMBean.UserImBean> mUserImBeanList = new LinkedList<>() ;

    private volatile LinkedList<LiveShowIMBean.UserImBean> mShowUserImBeanList = new LinkedList<>();

    private LinkedList<LiveShowIMBean.UserImBean> mTempImBeanList = new LinkedList<>();


    public LiveshowIMAdapter(Context context){
        mContext = context;
    }
    public void updateDataDirect(LiveShowIMBean.UserImBean userImBean){
        if (mShowUserImBeanList.size() >= SHOW_LIST_SIZE ){
            mShowUserImBeanList.removeFirst();
//            notifyItemRemoved(0);
        }

        mShowUserImBeanList.add(userImBean);
//        notifyItemInserted(mShowUserImBeanList.size()-1);
        notifyDataSetChanged();
    }
    private void addData(LiveShowIMBean.UserImBean userImBean){

        if (mUserImBeanList.size() > USER_LIST_SIZE ){
            mUserImBeanList.removeFirst();
        }

        mUserImBeanList.add(userImBean);
    }


    public void addDataRefresh(LiveShowIMBean.UserImBean userImBean){
        addData(userImBean);
        checkLimit();
        notifyDataSetChanged();
    }


    public void updateDataRefresh( ArrayList<LiveShowIMBean.UserImBean> userImBeanList){
        checkLimit();
        notifyDataSetChanged();
    }


    public void release(){
        task.stop();
    }

    public void repeatUpdateView(final RepeatUpdateViewCallback repeatUpdateViewCallback){
        task.repeat(REPEAT_TIME, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {

                if ( mUserImBeanList.size() > 0 && mShowUserImBeanList != null){
                    checkLimit();
                    notifyDataSetChanged();
                    repeatUpdateViewCallback.updateViewCallback();
                }

                return null;
            }
        });
    }

    public interface RepeatUpdateViewCallback{
        void updateViewCallback();
    }


    /**
     * 此处分两种情况：
     * mUserImBeanList.size +  mShowUserImBeanList.size > SHOW_LIST_SIZE
     * mUserImBeanList.size +  mShowUserImBeanList.size <= SHOW_LIST_SIZE
     */
    private synchronized void checkLimit(){

        mTempImBeanList.clear();
        if (mUserImBeanList.size() + mShowUserImBeanList.size() > SHOW_LIST_SIZE ){
             mTempImBeanList.addAll(mShowUserImBeanList);
             mTempImBeanList.addAll(mUserImBeanList);

             //mShowUserImBeanList 的处理
             int length = mTempImBeanList.size() - SHOW_LIST_SIZE;
             for (int i = 0; i< length ; i++){
                 mTempImBeanList.removeFirst();
             }
             mShowUserImBeanList.clear();
             mShowUserImBeanList.addAll(mTempImBeanList);

            //mUserImBeanList 的处理
             int tmpList = SHOW_LIST_SIZE - mShowUserImBeanList.size();
             for (int j = 0; j< tmpList ; j++){
                 mUserImBeanList.removeFirst();
             }

        }else{

            mShowUserImBeanList.addAll(mUserImBeanList);
            mUserImBeanList.clear();
        }
    }




    @NonNull
    @Override
    public IMViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(SqResUtils.getLayoutId(mContext,"sy37_base_item_liveshow_im_chat"),parent,false);
        return new IMViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull IMViewHolder holder, int position) {
        holder.mUserName.setText(mShowUserImBeanList.get(position).getUserName() + ":  ");
        holder.mChatMessage.setText(mShowUserImBeanList.get(position).getUserChatContent());
    }


    @Override
    public int getItemCount() {
        return mShowUserImBeanList == null ?0 : mShowUserImBeanList.size();
    }

    class IMViewHolder extends RecyclerView.ViewHolder{
          TextView mUserName;
          TextView mChatMessage;
//        TextView mRadioStudioName;
//        ImageView mAvatorImageView;
//        TextView mAnchorName;
//        TextView mOnlineNumber;
         IMViewHolder(View itemView) {
            super(itemView);
            mUserName = (TextView)itemView.findViewById(SqResUtils.getId(mContext, "tv_user_name"));
            mChatMessage = (TextView)itemView.findViewById(SqResUtils.getId(mContext,"tv_chat_message"));
        }
    }
}
