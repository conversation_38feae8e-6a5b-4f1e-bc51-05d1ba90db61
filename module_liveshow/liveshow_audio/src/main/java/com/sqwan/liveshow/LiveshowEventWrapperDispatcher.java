package com.sqwan.liveshow;

import com.sqwan.liveshow.common.ILiveshowEventWrapper;
import com.youme.voiceengine.MemberChange;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 17:25
 */
public class LiveshowEventWrapperDispatcher {
    public List<ILiveshowEventWrapper> iLiveshowEventWrappers = new CopyOnWriteArrayList<>();
    public void register(ILiveshowEventWrapper iLiveshowEventWrapper){
        if (iLiveshowEventWrapper == null) {
            return;
        }
        if (iLiveshowEventWrappers.contains(iLiveshowEventWrapper)) {
            return;
        }
        iLiveshowEventWrappers.add(iLiveshowEventWrapper);
    }
    public void unregister(ILiveshowEventWrapper iLiveshowEventWrapper){
        if (iLiveshowEventWrapper!=null) {
            iLiveshowEventWrappers.remove(iLiveshowEventWrapper);
        }
    }
    public void release(){
        iLiveshowEventWrappers.clear();
    }
    public void dispatchInit(boolean suceess){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.initCallback(suceess);
        }
    }
    public void dispatchJoinRoom(boolean suceess){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.joinRoomCallback(suceess);
        }
    }
    public void dispatchLeaveRoom(boolean suceess){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.leaveRoomCallback(suceess);
        }
    }
    public void dispatchChannelChange(boolean resume){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.channelChange(resume);
        }
    }
    public void dispatcheOthersMicChange(boolean isOn,String userid){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.othersMicChange(isOn,userid);
        }
    }
    public void dispatchMemberChange(String channelID, MemberChange[] changeList, boolean isJoin){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.onMemberChange(channelID,changeList,isJoin);
        }
    }
    public void dispatchMemberChange(int count){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.onMemberChange(count);
        }
    }
    public void dispatchRepeatClickLiveshowIcon(){
        for (ILiveshowEventWrapper iLiveshowEventWrapper : iLiveshowEventWrappers) {
            iLiveshowEventWrapper.onRepeatClickLiveshowIcon();
        }
    }
}
