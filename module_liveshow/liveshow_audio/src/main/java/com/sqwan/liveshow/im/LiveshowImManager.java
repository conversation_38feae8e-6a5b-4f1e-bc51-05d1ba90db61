package com.sqwan.liveshow.im;

import static com.youme.imsdk.YIMConstInfo.Errorcode.AlreadyLogin;

import android.content.Context;
import android.text.TextUtils;

import com.google.sqgson.Gson;
import com.sqwan.common.util.LogUtil;
import com.sqwan.liveshow.LiveshowBaseManager;
import com.sqwan.liveshow.LiveshowManager;
import com.sqwan.liveshow.error.LiveshowResult;
import com.sqwan.liveshow.trackaction.LiveshowTrackManager;
import com.youme.im.IMEngine;
import com.youme.imsdk.YIMClient;
import com.youme.imsdk.YIMConstInfo;
import com.youme.imsdk.callback.YIMEventCallback;
import com.youme.imsdk.internal.ChatRoom;
import com.youme.imsdk.internal.SendMessage;

/**
 * 描述: 直播im
 * 作者：znb
 * 时间：2021-05-31 15:27
 */
public class LiveshowImManager extends LiveshowBaseManager {
    private static LiveshowImManager sInstance;
    public static LiveshowImManager getInstance() {
        if (sInstance == null) {
            synchronized (LiveshowImManager.class) {
                if(sInstance == null) {
                    sInstance = new LiveshowImManager();

                }
            }
        }
        return sInstance;
    }
    private String TAG = this.getClass().getSimpleName();
    private String chatRoomId;
    private String userId;
    private boolean hasInited;
    private String recordInput;

    public String getRecordInput() {
        return recordInput;
    }

    public boolean isRecordInput = false;
    public void setRecordInput(String input){
        if (isRecordInput) {
            this.recordInput = input;
        }
    }
    public void clearRecordInput(){
        if (isRecordInput) {
            this.recordInput = "";
        }
    }

    public ILiveshowImCallback iLiveshowImCallback;

    private void resetDatas(){

    }
    /**
     * 初始化引擎
     * init 初始化SDK，需传值strAppKey, strSecrect（由在后台注册获取），并设置连接的区域参数
     */
    private void initYouMeIMEngine(Context context) {
        if (!hasInited) {
            String appKey = LiveshowManager.getInstance().liveshowConfig.appkey;
            String appSecret = LiveshowManager.getInstance().liveshowConfig.appsecret;
            int result = YIMClient.getInstance().init(context.getApplicationContext(),appKey, appSecret, 0);
            LogUtil.i(TAG,"initYouMeIMEngine result " + result);
            if (result==0) {
                hasInited=true;
            }
        }


    }
    public void joinRoom(Context context,String userId, final String chatRoomId,ILiveshowImCallback iLiveshowImCallback){
        this.iLiveshowImCallback = iLiveshowImCallback;
        iLiveshowImCallback.init();
        initYouMeIMEngine(context);
        init();
        login(userId,chatRoomId);

    }
    private boolean hasLogined(){
        return !TextUtils.isEmpty(userId);
    }
    private boolean joinedRoom(){
        return !TextUtils.isEmpty(chatRoomId);
    }
    public void leaveRoom(){
        if (joinedRoom()) {
            leaveChatRoom(chatRoomId);
        }else{
            release();
        }

    }
    private void init(){
        // 仅抄送服务端，不会主动发送到其他客户端，具体流程如下：
        // 1. Android 客户端发送消息 -> 2. 调用游密 API -> 3. 经过游密服务器 -> 4. 会抄送消息给 37 服务器 ->
        // 5. 37 服务器敏感信息过滤 -> 6. 调用游密 API -> 7. 游密服务器收到指令 -> 8. 发送给其他 Android 客户端
        // 另外注意需要的是：服务器会根据 gid 进行配置才有效果，否则会卡在后端那一步过不去，会导致其他客户端接收不到消息
        YIMClient.getInstance().switchMsgTransType(1);
        YIMClient.getInstance().registerReconnectCallback(iLiveshowImCallback);     //设置重连回调监听
        YIMClient.getInstance().registerKickOffCallback(iLiveshowImCallback);       //设置用户被踢下线监听
        YIMClient.getInstance().registerMsgEventCallback(iLiveshowImCallback);      //设置消息回调监听

    }
    private void release(){
        YIMClient.getInstance().unRegisterReconnectCallback();
        YIMClient.getInstance().unRegisterKickOffCallback();
        YIMClient.getInstance().unRegisterMsgEventCallback();
        if (iLiveshowImCallback!=null) {
            iLiveshowImCallback.release();
            iLiveshowImCallback = null;

        }
    }
    /**
     * 用户登录
     * param userId 用户ID，由调用者分配，不可为空字符串，只可由字母或数字或下划线组成，长度限制为255字节
     * param password 登录密码，不可为空字符串，如无特殊要求可以设置为固定字符串
     * param token 用户验证token，使用服务器token验证模式时使用，如不使用token验证传入:""，由restAPI获取token值
     * param callback 登录回调
     */
    private void login(String userId, final String chatRoomId) {
        YIMClient.getInstance().login(userId, userId, "", new YIMEventCallback.ResultCallback<String>() {
            @Override
            public void onSuccess(String userId) {
                LogUtil.i(TAG,"用户: " + userId + " 登录成功");
                LiveshowImManager.this.userId = userId;
                joinChatRoom(chatRoomId);
            }

            @Override
            public void onFailed(int errorCode, String userId) {
                LogUtil.i(TAG,"用户: " + userId + " 登录失败:" + errorCode);
                if (errorCode==AlreadyLogin) {
                    onSuccess(userId);
                }else{
                    callbackInvokeFail(iLiveshowImCallback, LiveshowResult.error_im_join);
                }
            }
        });
    }
    private void logout() {
        YIMClient.getInstance().logout(new YIMEventCallback.OperationCallback() {
            @Override
            public void onSuccess() {
                LogUtil.i(TAG,"用户: "  + " 已经退出登录");
                onResult();
            }

            @Override
            public void onFailed(int i) {
                LogUtil.i(TAG,"用户: "  + " 已经退出登录，但是可能本来就没有登录");
                onResult();
            }
            public void onResult(){
                release();
            }
        });
    }
    private void joinChatRoom(final String chatRoomId) {
        YIMClient.getInstance().joinChatRoom(chatRoomId, new YIMEventCallback.ResultCallback<ChatRoom>() {
            @Override
            public void onSuccess(ChatRoom chatRoom) {
                LogUtil.i(TAG,"进入频道 onSuccess " + chatRoom.groupId);
                LiveshowImManager.this.chatRoomId = chatRoom.groupId;
                callbackInvokeSuccess(iLiveshowImCallback,null,LiveshowResult.success_im_join);
            }

            @Override
            public void onFailed(int errorCode, ChatRoom chatRoom) {
                LogUtil.e(TAG,"进入频道失败 onFailed errorCode " + errorCode);
                callbackInvokeFail(iLiveshowImCallback,LiveshowResult.error_im_join);
            }
        });
    }
    private void leaveChatRoom(String chatRoomId) {
        YIMClient.getInstance().leaveChatRoom(chatRoomId, new YIMEventCallback.ResultCallback<ChatRoom>() {
            @Override
            public void onSuccess(ChatRoom chatRoom) {
                LogUtil.i(TAG,"离开频道 onSuccess " + chatRoom);
                onResult();

            }

            @Override
            public void onFailed(int errorCode, ChatRoom chatRoom) {
                LogUtil.e(TAG,"离开频道 onFailed errorCode " + errorCode);
                onResult();
            }
            public void onResult(){
                LiveshowImManager.this.chatRoomId = "";
                logout();
            }
        });
    }

    /**
     *
     * @param msgContent
     * @param yimEventCallback
     */
    public void sendTextMessage(final String msgContent, final YIMEventCallback.ResultCallback<String> yimEventCallback) {
        if (!joinedRoom() || TextUtils.isEmpty(msgContent)) {
            LogUtil.e(TAG,"not joinRoom");
            return;
        }
        IMEngine imEng = new IMEngine();
        IMEngine.IntegerVal level = imEng.new IntegerVal();
        // 数值可传1，保留字段，暂未启用
        // https://youme.im/doc/IMSDKAndroid.php
        level.setValue(1);
        final String finalMsgContent = YIMClient.getInstance().getFilterText(msgContent, level);

        if (!TextUtils.equals(msgContent, finalMsgContent)) {
            if (yimEventCallback != null) {
                yimEventCallback.onFailed(0,"消息包含敏感内容，发送失败");
            }
            return;
        }

        ImMsgChatExtBean imMsgChatExtBean = new ImMsgChatExtBean(getUsernick(), finalMsgContent);
        String attachParam = new Gson().toJson(imMsgChatExtBean);
        LogUtil.i(TAG,"imMsgChatExtBean " + imMsgChatExtBean);
        LogUtil.i(TAG,"attachParam " + attachParam);
        YIMClient.getInstance().sendTextMessage(chatRoomId, YIMConstInfo.ChatType.RoomChat, finalMsgContent, attachParam, new YIMEventCallback.ResultCallback<SendMessage>() {
            @Override
            public void onSuccess(SendMessage sendMessage) {
                LogUtil.i(TAG,"发送消息成功: " + finalMsgContent);
                LiveshowTrackManager.getInstance().commentsAction(LiveshowManager.getInstance().getRoomId(),LiveshowManager.getInstance().getAnchorId());
                if (yimEventCallback != null) {
                    yimEventCallback.onSuccess(finalMsgContent);
                }
            }

            @Override
            public void onFailed(int i, SendMessage sendMessage) {
                LogUtil.i(TAG,"发送消息失败:" + finalMsgContent + " errorcode " + i);
                if (yimEventCallback != null) {
                    yimEventCallback.onFailed(i,"发送消息失败");
                }
            }
        });
    }
    public void test(){
        if (iLiveshowImCallback!=null) {
            iLiveshowImCallback.test();
        }
    }
    public void stopTask(){
        if (iLiveshowImCallback!=null) {
            iLiveshowImCallback.stopTask();
        }
    }
    public void startTask(){
        if (iLiveshowImCallback!=null) {
            iLiveshowImCallback.startTask();
        }
    }

}
