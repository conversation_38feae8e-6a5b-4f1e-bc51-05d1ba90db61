package com.sqwan.liveshow.common;

import com.youme.voiceengine.MemberChange;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:45
 */
public interface  ILiveshowEventWrapper {
    void initCallback(boolean success);
    void joinRoomCallback(boolean success);
    void leaveRoomCallback(boolean success);
    void channelChange(boolean resume);
    void othersMicChange(boolean isOn,String userid);
    void onMemberChange(String channelID, MemberChange[] changeList, boolean isJoin);
    void onMemberChange(int count);
    void onRepeatClickLiveshowIcon();
}
