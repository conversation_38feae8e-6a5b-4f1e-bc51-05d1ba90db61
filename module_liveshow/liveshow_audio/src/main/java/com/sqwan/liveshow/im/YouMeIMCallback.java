package com.sqwan.liveshow.im;

import android.nfc.Tag;
import android.os.Bundle;

import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.SQResultListener;
import com.youme.imsdk.YIMConstInfo;
import com.youme.imsdk.YIMMessage;
import com.youme.imsdk.YIMMessageBodyAudio;
import com.youme.imsdk.YIMMessageBodyText;
import com.youme.imsdk.callback.YIMEventCallback;

public abstract class YouMeIMCallback  implements YIMEventCallback.ReconnectCallback, YIMEventCallback.MessageEventCallback, YIMEventCallback.KickOffCallback {
    protected String TAG = this.getClass().getSimpleName();
    /**
     * 接收到用户发来的消息
     *
     * @param message 消息内容结构体
     */
    @Override
    public void onRecvMessage(YIMMessage message) {

    }

    /**
     * 语音的识别文本回调
     *
     * @param errorcode 错误码
     * @param requestID 消息ID
     * @param text      返回的语音识别文本
     */
    @Override
    public void onGetRecognizeSpeechText(int errorcode, long requestID, String text) {

    }

    /*
     * 功能：录音音量变化回调, 频率:1s 约8次
     * @param volume：音量值(0到1)
     */
    @Override
    public void onRecordVolume(float volume) {

    }

    /**
     * 开始重连通知
     */
    @Override
    public void onStartReconnect() {

    }

    /**
     * 重连结果通知
     *
     * @param result 0-重连成功，1-重连失败，再次重连，2-重连失败
     */
    @Override
    public void onRecvReconnectResult(int result) {

    }

    /**
     * 被踢下线通知
     */
    @Override
    public void onKickOff() {

    }

    /**
     * 功能：新消息通知（默认自动接收消息，只有调用setReceiveMessageSwitch设置为不自动接收消息，才会收到该回调），有新消息的时候会通知该回调，频道消息会通知消息来自哪个频道ID
     *
     * @param chatType：聊天类型
     * @param targetID：频道ID
     */
    @Override
    public void onRecvNewMessage(int chatType, String targetID) {

    }

}