package com.sqwan.liveshow.common;

import com.youme.voiceengine.MemberChange;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:45
 */
public class ILiveshowEventWrapperAdapter implements  ILiveshowEventWrapper{
    @Override
    public void initCallback(boolean success) {

    }

    @Override
    public void joinRoomCallback(boolean success) {

    }

    @Override
    public void leaveRoomCallback(boolean success) {

    }

    @Override
    public void channelChange(boolean resume) {

    }

    @Override
    public void othersMicChange(boolean isOn, String userid) {

    }

    @Override
    public void onMemberChange(String channelID, MemberChange[] changeList, boolean isJoin) {

    }

    @Override
    public void onMemberChange(int count) {

    }

    @Override
    public void onRepeatClickLiveshowIcon() {

    }
}
