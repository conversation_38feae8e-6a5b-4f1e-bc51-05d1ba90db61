package com.sqwan.liveshow.ui;

import static com.sqwan.TestConst.isLiveshowImtest;

import android.os.Bundle;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.Editable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.plugin.standard.RealBaseActivity;
import com.sqwan.common.util.ClickUtils;
import com.sqwan.common.util.KeyBoardUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.WindowManagerUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.liveshow.LiveshowManager;
import com.sqwan.liveshow.SqR;
import com.sqwan.liveshow.bean.LiveShowIMBean;
import com.sqwan.liveshow.bean.LiveshowChannelInfo;
import com.sqwan.liveshow.common.ILiveshowEventWrapper;
import com.sqwan.liveshow.common.ILiveshowEventWrapperAdapter;
import com.sqwan.liveshow.common.ILiveshowViewEvent;
import com.sqwan.liveshow.common.LiveshowViewEventAdapter;
import com.sqwan.liveshow.im.ILiveshowImCallback;
import com.sqwan.liveshow.im.LiveshowImManager;
import com.sqwan.supportview.LimitedEditText;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.youme.imsdk.callback.YIMEventCallback;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-31 18:20
 */
public class LiveshowRoomActivity extends RealBaseActivity implements KeyBoardUtils.OnSoftKeyBoardChangeListener {
    private final String TAG = this.getClass().getSimpleName();

    private Task taskRecordInput = Task.create();

    private TextView tvAnchorName;

    private TextView tvImRoomSend;

    private RoundedImageView ivAvatar;

    private TextView tvOnlineNumber;

    private TextView tvRadioStationName;

    private ImageView ivImRoomHome;

    private LinearLayout llContainer,ll_input_container;


    private LiveShowIMBean mliveShowIMBean;

    private LiveshowIMAdapter liveshowIMAdapter;

    private KeyBoardUtils keyBoardUtils = new KeyBoardUtils();

    private LimitedEditText etInputMessage;

    private RecyclerView rvChatMessage;

    private View clickView;

    private static final int CHAT_NUMBERFILTER = 200;

    private static final int CHAT_LIMITDURATION = 5000;

    private boolean isFirstResume =  true;

    private ILiveshowViewEvent iLiveshowViewEvent = new LiveshowViewEventAdapter() {
        @Override
        public void onShowLiveShowView() {

        }

        @Override
        public void onReleaseLiveShowView() {

        }

        @Override
        public void checkEmptyRoom() {

        }

        @Override
        public void update(final LiveshowChannelInfo.ChannelsBean channelsBean) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    ivAvatar.setImageUrl(channelsBean.getAnchor_avatar());
                    tvAnchorName.setText(channelsBean.getAnchor_name());

                }
            });
        }
    };
    private void postMsg(LiveShowIMBean.UserImBean userImBean){
        liveshowIMAdapter.updateDataDirect(userImBean);
        rvChatMessage.scrollToPosition(liveshowIMAdapter.getItemCount()-1);
    }

    private ILiveshowImCallback iLiveshowImCallback = new ILiveshowImCallback(){


        @Override
        public void onRecvChatMsg(LiveShowIMBean.UserImBean userImBean) {
            postMsg(userImBean);
        }

        @Override
        public void joinRoom(boolean success) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    tvOnlineNumber.setText(LiveshowManager.getInstance().getOnlineCount()+"");
                }
            });
        }

        @Override
        public void leaveRoom(boolean success) {

        }
    };

    private ILiveshowEventWrapper iLiveshowEventWrapper = new ILiveshowEventWrapperAdapter(){
        @Override
        public void onMemberChange(final int count) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    tvOnlineNumber.setText(count+"");
                }
            });
        }
    };


    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getContext().getTheme().applyStyle(SqResUtils.getStyleId(getContext(),SqR.style.chatRoomDialog),true);
//        getContext().setTheme(SqResUtils.getStyleId(getContext(),SqR.style.chatRoomDialog));
        setContentView(SqResUtils.getLayoutId(getContext(),SqR.layout.sy37_base_activity_liveshow_im_room));
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                        | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        WindowManagerUtil.handleNotch(getContext(),true);
        LiveshowImManager.getInstance().joinRoom(getContext(), LiveshowManager.getInstance().getUserId(),LiveshowManager.getInstance().getRoomId(),iLiveshowImCallback);
        LiveshowManager.getInstance().register(iLiveshowEventWrapper);
        initData();
        initView();
        LiveshowManager.getInstance().addiLiveshowViewEvent(iLiveshowViewEvent);
    }


    @Override
    public void onResume() {
        super.onResume();
        if (isFirstResume) {
            WindowManagerUtil.handleHideSystemUI(getContext());
            keyBoardUtils.addListener(getContext(), LiveshowRoomActivity.this);
            isFirstResume = false;
        }
    }

    @Override
    public void onDestroy(){
        super.onDestroy();
        LiveshowImManager.getInstance().leaveRoom();
        LiveshowManager.getInstance().unregister(iLiveshowEventWrapper);
        keyBoardUtils.removeListener();
        taskRecordInput.stop();
        liveshowIMAdapter.release();
        LiveshowManager.getInstance().removeLiveshowViewEvent(iLiveshowViewEvent);
    }


    private void initData(){
        LiveShowIMBean liveShowIMBean = new LiveShowIMBean();
        LiveshowChannelInfo.ChannelsBean channelsBean = LiveshowManager.getInstance().getChannelInfo();
        if (channelsBean!=null) {
            liveShowIMBean.setRadioStudioName(channelsBean.getCname());
            LiveShowIMBean.AnchorMessageBean anchorMessageBean = new LiveShowIMBean.AnchorMessageBean();
            anchorMessageBean.setAnchorName(channelsBean.getAnchor_name());
            anchorMessageBean.setAvatarUrl(channelsBean.getAnchor_avatar());
            anchorMessageBean.setOnlineNumber(LiveshowManager.getInstance().getOnlineCount());
            liveShowIMBean.setAnchorMessageBean(anchorMessageBean);
        }
        mliveShowIMBean = liveShowIMBean;
    }



    private void initView(){
        clickView = findViewById(SqResUtils.getId(getContext(),SqR.id.clickView));
        llContainer = findViewById(SqResUtils.getId(getContext(),SqR.id.llimroomcontainer));
        ivAvatar = findViewById(SqResUtils.getId(getContext(),SqR.id.iv_anchor_avatar));
        tvAnchorName = findViewById(SqResUtils.getId(getContext(),SqR.id.tv_anchor_name));
        tvOnlineNumber = findViewById(SqResUtils.getId(getContext(),SqR.id.tv_online_numbers));
        tvRadioStationName = findViewById(SqResUtils.getId(getContext(),SqR.id.tv_radio_studio_name));
        rvChatMessage =  findViewById(SqResUtils.getId(getContext(),SqR.id.rv_im_chat_message));
        etInputMessage  =  findViewById(SqResUtils.getId(getContext(),SqR.id.et_input_message));
        tvImRoomSend = findViewById(SqResUtils.getId(getContext(),SqR.id.tv_im_room_send));
        ivImRoomHome = findViewById(SqResUtils.getId(getContext(),SqR.id.iv_im_room_home));
        ll_input_container = findViewById(SqResUtils.getId(getContext(),SqR.id.ll_input_container));

        ivAvatar.setCornerRadius(TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                18, getContext().getResources().getDisplayMetrics()));

        ivImRoomHome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isLiveshowImtest) {
                    AuthCountDownManager.getInstance().setAuthResult(getContext(),false,true,null);
                    return;
                }
                onBackPressedHandle();
            }
        });
        clickView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                keyBoardUtils.hideKeyboard(getContext());
            }
        });
        liveshowIMAdapter = new LiveshowIMAdapter(getContext());
        final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        rvChatMessage.setLayoutManager(linearLayoutManager);
        rvChatMessage.setAdapter(liveshowIMAdapter);

        LiveShowIMBean.AnchorMessageBean anchorMessageBean = mliveShowIMBean.getAnchorMessageBean();
        if (anchorMessageBean!=null) {
            ivAvatar.setImageUrl(anchorMessageBean.getAvatarUrl());
            tvAnchorName.setText(anchorMessageBean.getAnchorName());
            tvOnlineNumber.setText(String.valueOf(anchorMessageBean.getOnlineNumber()));
        }

        tvRadioStationName.setText(mliveShowIMBean.getRadioStudioName());
        ViewGroup.LayoutParams layoutParams = tvRadioStationName.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = StatusBarUtil.getStatusBarHeight(getContext());
            tvRadioStationName.setLayoutParams(layoutParams);
        }


        etInputMessage.setNumberFilter(CHAT_NUMBERFILTER,new LimitedEditText.TextWatcherAdapter() {

            @Override
            public void afterTextChanged(final Editable s) {
                super.afterTextChanged(s);
                if (LiveshowImManager.getInstance().isRecordInput) {
                    taskRecordInput.oneShot(1000, new Runnable() {
                        @Override
                        public void run() {
                            String txt = s.toString();
                            LiveshowImManager.getInstance().setRecordInput(txt);
                        }
                    });
                }

            }

            @Override
            public void maxCharactersCallback() {
                ToastUtil.showToast("内容过长~");
            }

            @Override
            public void emptyChange(Boolean isEmpty) {
                tvImRoomSend.setSelected(!isEmpty);
            }
        });


        // 点击 屏幕收起软键盘
        llContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });

        ClickUtils.stePreviewClickListener(tvImRoomSend,CHAT_LIMITDURATION,"说话太快啦~~",new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(etInputMessage.getText())) {
                    sendMsg();
                }
            }
        });

        if (LiveshowImManager.getInstance().isRecordInput) {
            String recordInput = LiveshowImManager.getInstance().getRecordInput();
            if (!TextUtils.isEmpty(recordInput)) {
                etInputMessage.setText(recordInput);
            }
        }
    }

    private void test(){
        LiveshowImManager.getInstance().test();

    }


   private void sendMsg(){
        final String content = handleContent(etInputMessage.getText().toString());

        LiveshowImManager.getInstance().sendTextMessage(content, new YIMEventCallback.ResultCallback<String>() {
            @Override
            public void onSuccess(String result) {
                LogUtil.d("发送消息成功！！");
                final LiveShowIMBean.UserImBean userImBean = new LiveShowIMBean.UserImBean();
                userImBean.setUserName(LiveshowImManager.getInstance().getUsernick());
                userImBean.setUserChatContent(result);
                liveshowIMAdapter.updateDataDirect(userImBean);
                rvChatMessage.scrollToPosition(liveshowIMAdapter.getItemCount()-1);
                etInputMessage.setText("");
                LiveshowImManager.getInstance().clearRecordInput();
                keyBoardUtils.hideKeyboard(getContext());
            }

            @Override
            public void onFailed(int errorCode, String msg) {
                ToastUtil.showToast(msg);
                LogUtil.d("发送消息失败，errorCode = " + errorCode + ", msg = " + msg);
            }
        });
    }

    private String handleContent(String content){
        return content.replaceAll("\n+","\n");
    }



    @Override
    public void keyBoardShow(int height) {
        LogUtil.i(TAG,"keyBoardShow height " + height);
        ViewUtils.show(clickView);
        llContainer.setPadding(llContainer.getPaddingLeft(),llContainer.getPaddingTop(),llContainer.getPaddingRight(),height);
        rvChatMessage.scrollToPosition(liveshowIMAdapter.getItemCount()-1);
    }

    @Override
    public void keyBoardHide(int height) {
        ViewUtils.gone(clickView);
        LogUtil.i(TAG,"keyBoardHide height " + height);
        llContainer.setPadding(llContainer.getPaddingLeft(),llContainer.getPaddingTop(),llContainer.getPaddingRight(),0);
    }

    @Override
    public void viewChanged(int dheight) {
        LogUtil.i(TAG,"viewChanged height " + dheight);
        int paddingBottom = llContainer.getPaddingBottom() - dheight;
        llContainer.setPadding(llContainer.getPaddingLeft(),llContainer.getPaddingTop(),llContainer.getPaddingRight(),paddingBottom);
    }
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressedHandle();
            return true;
        }
        return false;
    }
    public void onBackPressedHandle() {
       LiveshowManager.getInstance().minimize(getContext(), new LiveshowManager.MinimizeCallback() {
           @Override
           public void callbcak(boolean isShow, boolean isFinish) {
               if (isFinish) {
                   LiveshowImManager.getInstance().stopTask();
               }else{
                   if (isShow) {
                       LiveshowImManager.getInstance().stopTask();
                   }else{
                       LiveshowImManager.getInstance().startTask();
                   }
               }

           }
       });

    }

}

