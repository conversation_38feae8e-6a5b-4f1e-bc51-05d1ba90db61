package com.sqwan.liveshow.im;

import com.google.sqgson.Gson;
import com.sqwan.TestConst;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.common.util.task.TaskSubThread;
import com.sqwan.liveshow.bean.LiveShowIMBean;
import com.sqwan.msdk.api.SQResultListener;
import com.youme.imsdk.YIMConstInfo;
import com.youme.imsdk.YIMMessage;
import com.youme.imsdk.YIMMessageBodyText;

import java.util.LinkedList;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-31 18:50
 */
public abstract class  YouMeIMCallbackWrapper extends YouMeIMCallback{
    private String TAG = "YouMeIMCallbackWrapper";
    private int PUSHIMDURATION = 20;
    private Task task = Task.create();
    private LinkedList<LiveShowIMBean.UserImBean> msgs = new LinkedList();
    private int i;
    private TaskSubThread testTask = TaskSubThread.create();
    public void test(){
        if (TestConst.isLiveshowImtest) {
            if (testTask.isRunning()) {
                testTask.stop();
            }else{
                testTask.repeat(5, new TaskSubThread.TaskFunc() {
                    @Override
                    public TaskSubThread.Result exec() {
                        i ++ ;
                        LiveShowIMBean.UserImBean userImBean = new LiveShowIMBean.UserImBean();
                        userImBean.setUserName("zhenjiasb" + i);
                        userImBean.setUserChatContent("zhenjiasb" + i );
                        msgs.add(userImBean);
                        LogUtil.i(TAG,"add msg " + userImBean.toString());
                        if (!task.isRunning()) {
                            startTask();
                        }
                        return null;
                    }
                });
            }
        }
    }
    public void release(){
        msgs.clear();
        stopTask();
        if (TestConst.isLiveshowImtest) {
            i = 0;
            testTask.stop();
        }
    }
    public void stopTask(){
        task.stop();
    }
    public void startTask(){
        task.repeat(PUSHIMDURATION, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                if (!msgs.isEmpty()) {
                    LiveShowIMBean.UserImBean msg = msgs.removeFirst();
                    onRecvChatMsg(msg);
                    LogUtil.i(TAG,"onRecvChatMsg msg " + msg.toString());
                    if (msgs.isEmpty()) {
                        return Task.Result.Stop;
                    }
                }
                return Task.Result.Next;
            }
        });
    }
    public void init(){
        release();
        startTask();
    }
    public abstract void onRecvChatMsg(LiveShowIMBean.UserImBean userImBean);

    @Override
    public void onRecvMessage(YIMMessage message) {
        if (null == message)
            return;
        int msgType = message.getMessageType();
        if (YIMConstInfo.MessageBodyType.TXT == msgType) {       //判断消息类型，为文本消息
            YIMMessageBodyText yimMessageBodyText = ((YIMMessageBodyText) message.getMessageBody());
            String yimMsgContent = yimMessageBodyText.getMessageContent();
            LogUtil.i(TAG,"接收到一条文本消息： " + yimMessageBodyText);
            LiveShowIMBean.UserImBean userImBean = new LiveShowIMBean.UserImBean();
            ImMsgChatExtBean imMsgChatExtBean = new Gson().fromJson(yimMessageBodyText.getAttachParam(),ImMsgChatExtBean.class);
            if (imMsgChatExtBean!=null) {
                userImBean.setUserName(imMsgChatExtBean.username);
            }
            userImBean.setUserChatContent(yimMsgContent);
            msgs.add(userImBean);
            if (!task.isRunning()) {
                startTask();
            }

        }
    }
}
