/**
 * This package provides the {@link com.google.sqgson.Gson} class to convert Json to Java and
 * vice-versa.
 *
 * <p>The primary class to use is {@link com.google.sqgson.Gson} which can be constructed with
 * {@code new Gson()} (using default settings) or by using {@link com.google.sqgson.GsonBuilder}
 * (to configure various options such as using versioning and so on).</p>
 *
 * <AUTHOR> <PERSON>
 */
package com.google.sqgson;