package com.parameters.utils;

import com.sqwan.common.util.LogUtil;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-07 22:01
 */
public class ClassCheckUtils {
    final static String TAG = "ClassCheckUtils";
    static String packageName = "com.parameters.performfeatureconfig.";
    static String PerformFeature = packageName+"PerformFeature";
    static String PerformFeatureKey = packageName+"PerformFeatureKey";
    static String PerformFeatureType = packageName+"PerformFeatureType";
    public static boolean isExistPerformFeatureConfig(){
        boolean exist = false;
        try {
            exist = null!=Class.forName(PerformFeature)&&
                    null!=Class.forName(PerformFeatureKey)&&
                    null!=Class.forName(PerformFeatureType);
            return exist;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }finally {
            LogUtil.i(TAG,"exist:"+exist);
            return exist;
        }
    }
    public static boolean isExist(String clazzName){
        try {
            return null!=Class.forName(clazzName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return false;
    }
}
