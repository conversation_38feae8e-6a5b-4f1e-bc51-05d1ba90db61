package com.parameters.bean;

import android.text.TextUtils;

import org.json.JSONObject;

/**
 *    author : 黄锦群
 *    time   : 2022/06/21
 *    desc   : 微信小程序数据 Bean 类
 */
public class MiniProgramBean {

    /** 微信 SDK 跳转 */
    public static final int SKIP_TYPE_WECHAT_SDK = 1;
    /** Scheme Url 跳转 */
    public static final int SKIP_TYPE_SCHEME_URL = 2;

    /** 跳转方式 */
    public int skipType;

    /** 微信 appId */
    public String appId;

    /** 微信小程序 id */
    public String miniProgramId;

    /** 微信小程序 path */
    public String miniProgramPath;

    /** 要跳转的 scheme Url */
    public String schemeUrl;

    public static MiniProgramBean parseToObject(String json) {
        MiniProgramBean miniProgramBean = new MiniProgramBean();
        if (TextUtils.isEmpty(json)) {
            return miniProgramBean;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            miniProgramBean.skipType = jsonObject.optInt("skip_type");
            miniProgramBean.appId = jsonObject.optString("appid");
            miniProgramBean.miniProgramId = jsonObject.optString("mini_program_id");
            miniProgramBean.miniProgramPath = jsonObject.optString("mini_program_path");
            miniProgramBean.schemeUrl = jsonObject.optString("scheme_url");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return miniProgramBean;
    }
}