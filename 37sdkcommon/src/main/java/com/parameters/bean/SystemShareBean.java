package com.parameters.bean;

import android.graphics.Bitmap;

import org.json.JSONObject;

/**
 *    author : 黄锦群
 *    time   : 2022/06/13
 *    desc   : 系统分享 Bean
 */
public class SystemShareBean {

    /** 分享的类型 */
    public int shareType;
    /** 分享的标题 */
    public String shareTitle;
    /** 分享的链接 */
    public String shareLinkUrl;
    /** 分享的图片 url */
    public String shareImageUrl;
    /** 分享的文本 */
    public String shareText;

    /** 用于存放已缓存的图片资源 */
    public Bitmap bitmap;

    /**
     * json转bean
     */
    public static SystemShareBean parseFromJson(String json) {
        SystemShareBean systemShareBean = new SystemShareBean();
        try {
            JSONObject jsonObject = new JSONObject(json);
            systemShareBean.shareType = jsonObject.optInt("shareType");
            systemShareBean.shareTitle = jsonObject.optString("shareTitle");
            systemShareBean.shareLinkUrl = jsonObject.optString("shareLinkUrl");
            systemShareBean.shareTitle = jsonObject.optString("shareTitle");
            systemShareBean.shareImageUrl = jsonObject.optString("shareImageUrl");
            systemShareBean.shareText = jsonObject.optString("shareText");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return systemShareBean;
    }
}