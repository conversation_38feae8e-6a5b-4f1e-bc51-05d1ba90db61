package com.sq.tool.network;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.text.TextUtils;
import com.sdk.sq.net.gateway.GateWayUtils;
import com.sqwan.common.util.SQContextWrapper;
import java.util.HashSet;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/5/10
 * @desc: 统一网关相关管理
 */
public class GateWayManager {

    private static final String DEFAULT_VERSION = "";
    private static final String DEFAULT_KEY = "soC2GAr8jN2fsbry";
    public static String SP_KEY_FIXED_KEY = "sq_gate_way_key";
    public static String SP_KEY_VERSION = "sq_x_version";
    private static final String SQ_PREFS = "sq_prefs";
    private static final HashSet<String> sWhiteList = new HashSet<>();


    static {
        sWhiteList.add("https://sdk-apix-secure.37.com.cn/server-info-service/get-url");
    }

    public static void saveKey(String key) {
        SharedPreferences sp = SQContextWrapper.getApplicationContext().getSharedPreferences(
            SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(SP_KEY_FIXED_KEY, key);
        editor.apply();
    }


    public static String getKey() {
        SharedPreferences sp = SQContextWrapper.getApplicationContext().getSharedPreferences(
            SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_FIXED_KEY, DEFAULT_KEY);
    }


    public static void saveXVersion(String version) {
        SharedPreferences sp = SQContextWrapper.getApplicationContext().getSharedPreferences(
            SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.putString(SP_KEY_VERSION, version);
        editor.apply();
    }


    public static String getXVersion() {
        SharedPreferences sp = SQContextWrapper.getApplicationContext().getSharedPreferences(
            SQ_PREFS, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_VERSION, DEFAULT_VERSION);
    }

    public static void updateWhiteList(Map<String, String> map) {
        sWhiteList.clear();
        for (String item : map.values()) {
            if (item.contains("-secure")) { //带加密的才走加密网关
                sWhiteList.add(item);
            }
        }
    }

    public static void addWhiteListUrl(String url) {
        if (url.contains("-secure")) { //带加密的才走加密网关
            sWhiteList.add(url);
        }
    }

    public static HashSet<String> getWhiteList() {
        return sWhiteList;
    }

    public static void updateKeySet(JSONObject json) {
        String secureKey = findSecureKey(json);
        try {
            if (TextUtils.isEmpty(secureKey)) {
                return;
            }
            JSONObject keyJson = new JSONObject(secureKey);
            String key = keyJson.optString("X-Request-AppKey");
            String appSecret = keyJson.optString("X-Request-AppSecret");
            String xRequestVersion = keyJson.optString("X-Request-Version");
            saveKey(GateWayUtils.getFixedKey(key, appSecret));
            saveXVersion(xRequestVersion);
        } catch (JSONException e) { //报错使用默认key和version
            e.printStackTrace();
            saveKey(DEFAULT_KEY);
            saveXVersion(DEFAULT_VERSION);
        }
    }

    private static String findSecureKey(JSONObject jsonObject) {
        try {
            JSONArray array = jsonObject.optJSONArray("api_infos");
            if (array == null) {
                return null;
            }
            for (int i = 0; i < array.length(); i++) {
                JSONObject apiObject = array.optJSONObject(i);
                String apiKey = apiObject.optString("api_key");
                if ("x_secure_key".equals(apiKey)) {
                    return apiObject.optString("api_info");
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return "";
    }
}
