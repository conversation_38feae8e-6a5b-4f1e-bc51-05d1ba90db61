package com.sq.tool.network;

import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 把所有value改为字符串, 空值用字符串代替
 *
 * <AUTHOR>
 * @since 2023/8/14
 */
class StringifyTransformer implements ParamsTransformer {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        Map<String, Object> ret = new LinkedHashMap<>();
        for (String key : map.keySet()) {
            Object value = map.get(key);
            if (value == null) {
                ret.put(key, "");
            } else if (value instanceof String) {
                ret.put(key, value);
            } else {
                ret.put(key, String.valueOf(value));
            }
        }
        return ret;
    }
}
