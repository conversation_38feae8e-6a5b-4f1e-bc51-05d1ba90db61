package com.sq.tool.network;

import android.support.annotation.NonNull;
import com.sqnetwork.voly.VolleyLog;
import com.sqnetwork.voly.toolbox.FormBody;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.msdk.config.ConfigManager;
import java.io.IOException;
import java.util.Locale;
import java.util.Map;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 国内旧版签名规则
 * 1. 拼接{$pid}{$gid}{$refer}{$version}{$time}{$key}
 * 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
 *
 * <AUTHOR>
 * @since 2023/6/30
 */
public class SignV1Interceptor implements Interceptor {

    private final static String PARAM_NAME = "sign";
    private final String mKey;

    public SignV1Interceptor(String key) {
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("App key can not be null!");
        }
        mKey = key;
    }

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Request request = chain.request();

        RequestBody requestBody = request.body();
        if (requestBody instanceof FormBody) {
            // 只对Post进行签名
            FormBody formBody = (FormBody) requestBody;
            FormBody.Builder builder = new FormBody.Builder();

            // 收集参数
            String pid = null;
            String gid = null;
            String refer = null;
            String version = null;
            String time = null;
            for (int i = 0; i < formBody.size(); i++) {
                String name = formBody.name(i);
                String value = formBody.value(i);
                switch (name) {
                    case "pid":
                        pid = value;
                        break;
                    case "gid":
                        gid = value;
                        break;
                    case "refer":
                        refer = value;
                        break;
                    case "version":
                        version = value;
                        break;
                    case "time":
                        time = value;
                        break;
                }
                builder.add(name, value);
            }

            if (pid == null) {
                throw new IllegalArgumentException("pid字段不能为空");
            }

            if (gid == null) {
                throw new IllegalArgumentException("gid字段不能为空");
            }

            if (refer == null) {
                throw new IllegalArgumentException("refer字段不能为空");
            }

            if (version == null) {
                throw new IllegalArgumentException("version字段不能为空");
            }

            if (time == null) {
                throw new IllegalArgumentException("time字段不能为空");
            }

            // 1. 拼接{$pid}{$gid}{$refer}{$version}{$time}{$key}
            String signStr = pid + gid + refer + version + time + mKey;
            // 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
            String sign = MD5Util.Md5(signStr).toLowerCase(Locale.US);
            if (VolleyLog.VERBOSE) {
                VolleyLog.i("[V1]%s\n签名原串: %s\n签名结果: %s", request.url(), signStr, sign);
            }
            builder.add(PARAM_NAME, sign);

            request = request.newBuilder()
                .post(builder.build())
                .build();
        }

        return chain.proceed(request);
    }

    public static String sign(Map<String, String> temp) {
        String appKey = ConfigManager.getInstance(SQContextWrapper.getApplicationContext()).getAppKey();
        return sign(appKey, temp);
    }

    public static String sign(String appKey, Map<String, String> temp) {
        if (temp == null || appKey == null) {
            return "";
        }
        String pid = temp.get("pid");
        String gid = temp.get("gid");
        String refer = temp.get("refer");
        String version = temp.get("version");
        String time = temp.get("time");
        // 1. 拼接{$pid}{$gid}{$refer}{$version}{$time}{$key}
        String signStr = pid + gid + refer + version + time + appKey;
        // 2. 对签名原始字符串计算md5值, 得到最终签名(小写)
        String sign = MD5Util.Md5(signStr).toLowerCase(Locale.US);
        if (VolleyLog.VERBOSE) {
            VolleyLog.i("[V1]签名原串: %s\n签名结果: %s", signStr, sign);
        }
        return sign;
    }
}
