package com.sq.tool.network;

import android.support.annotation.NonNull;
import com.sqnetwork.voly.VolleyLog;
import com.sqnetwork.voly.toolbox.FormBody;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.msdk.config.ConfigManager;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * https://developers.37.com.cn/developer/domestic/official/server/intro/#%E7%AD%BE%E5%90%8D%E8%A7%84%E5%88%99
 * 1. 提取除了sign以外的所有参数
 * 2. 如果参数值有为空的参数, 值用空字符串代替值, 不能使用null做值进行签名后验签
 * 3. 将参数以其参数名的字典序升序进行排序
 * 4. 遍历排序后的参数数组中的每一个key/value对, 生成一个key=value格式的字符串
 * 5. 在末尾连接上 key , 得到签名原始字符串
 * 6. 对签名原始字符串计算md5值, 得到最终签名(小写)
 *
 * <AUTHOR>
 * @since 2023/6/30
 */
public class SignV3Interceptor implements Interceptor {

    private final static String PARAM_NAME = "sign";
    private final String mKey;

    public SignV3Interceptor(String key) {
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("App key can not be null!");
        }
        mKey = key;
    }

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Request request = chain.request();

        RequestBody requestBody = request.body();
        if (requestBody instanceof FormBody) {
            // 只对Post进行签名
            FormBody formBody = (FormBody) requestBody;
            FormBody.Builder builder = new FormBody.Builder();

            // 收集参数
            Map<String, String> temp = new HashMap<>();
            for (int i = 0; i < formBody.size(); i++) {
                String name = formBody.name(i);
                String value = formBody.value(i);
                if (!PARAM_NAME.equals(name)) {
                    // 1. 提取除了sign以外的所有参数
                    // 2. 如果参数值有为空的参数, 值用空字符串代替值, 不能使用null做值进行签名后验签
                    // 此处value不可能为null
                    temp.put(name, value);
                }
                builder.add(name, value);
            }

            builder.add(PARAM_NAME, getSign(request, temp));

            request = request.newBuilder()
                .post(builder.build())
                .build();
        } else if ("get".equalsIgnoreCase(request.method())) {
            HttpUrl url = request.url();
            // 收集参数
            Map<String, String> temp = new HashMap<>();
            for (String name : url.queryParameterNames()) {
                String value = url.queryParameter(name);
                if (value != null) {
                    temp.put(name, value);
                } else {
                    temp.put(name, "");
                }
            }
            if (!temp.isEmpty()) {
                String sign = getSign(request, temp);
                request = request.newBuilder()
                    .url(url.newBuilder().addQueryParameter(PARAM_NAME, sign).build())
                    .build();
            }
        }

        return chain.proceed(request);
    }

    private String getSign(Request request, Map<String, String> temp) {
        StringBuilder signStr = buildString(mKey, temp);
        String sign = sign(signStr);
        if (VolleyLog.VERBOSE) {
            VolleyLog.i("[V3]%s\n签名原串: %s\n签名结果: %s", request.url(), signStr, sign);
        }
        return sign;
    }

    private static StringBuilder buildString(String appKey, Map<String, String> temp) {
        // 3. 将参数以其参数名的字典序升序进行排序
        List<String> keys = new ArrayList<>(temp.keySet());
        Collections.sort(keys);
        // 4. 遍历排序后的参数数组中的每一个key/value对, 生成一个key=value格式的字符串
        StringBuilder signStr = new StringBuilder();
        for (String key : keys) {
            signStr.append(key).append('=').append(temp.get(key));
        }
        // 5. 在末尾连接上 key , 得到签名原始字符串
        signStr.append(appKey);
        return signStr;
    }

    private static String sign(StringBuilder signStr) {
        // 6. 对签名原始字符串计算md5值, 得到最终签名(小写)
        return MD5Util.Md5(signStr.toString()).toLowerCase(Locale.US);
    }

    public static String sign(Map<String, String> temp) {
        String appKey = ConfigManager.getInstance(SQContextWrapper.getApplicationContext()).getAppKey();
        return sign(appKey, temp);
    }

    public static String sign(String appKey, Map<String, String> temp) {
        if (temp == null || appKey == null) {
            return "";
        }
        StringBuilder signStr = buildString(appKey, temp);
        String sign = sign(signStr);
        if (VolleyLog.VERBOSE) {
            VolleyLog.i("[V3]签名原串: %s\n签名结果: %s", signStr, sign);
        }
        return sign;
    }
}
