package com.sq.tool.network;

import android.text.TextUtils;
import com.sdk.sq.net.RequestErrorCode;
import com.sqnetwork.voly.VolleyError;

/**
 * <AUTHOR>
 * @since 2023/7/25
 */
public class VolleyErrorUtil {

    /**
     * 不带异常名称, 可以展示
     */
    public static String simpleErrorMsg(Throwable error) {
        if (error == null) {
            return "";
        }
        String errorMsg = error.getMessage();
        if (!TextUtils.isEmpty(errorMsg)) {
            return errorMsg;
        } else if (error.getCause() != null && !TextUtils.isEmpty(error.getCause().getMessage())) {
            return error.getCause().getMessage();
        } else {
            return error.getClass().getSimpleName();
        }
    }

    public static String errorMsg(VolleyError error) {
        return errorMsg(httpStatus(error), error);
    }

    /**
     * 打日志用
     */
    public static String errorMsg(int code, Exception error) {
        if (error == null) {
            return "";
        }
        String errorMsg = error.getMessage();
        if (!TextUtils.isEmpty(errorMsg) && code > 0) {
            return error.getClass().getSimpleName() + ":" + error.getMessage() + "(" + code + ")";
        } else if (!TextUtils.isEmpty(errorMsg)) {
            return error.getClass().getSimpleName() + ":" + error.getMessage();
        } else if (code > 0) {
            return error.getClass().getSimpleName() + "(" + code + ")";
        } else {
            return error.toString();
        }
    }

    public static int httpStatus(VolleyError error) {
        return error.networkResponse != null ? error.networkResponse.statusCode : -1;
    }

    public static int errorCode(VolleyError error) {
        return RequestErrorCode.of(error);
    }
}
