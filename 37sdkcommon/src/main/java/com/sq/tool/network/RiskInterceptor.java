package com.sq.tool.network;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder;
import com.sdk.sq.net.RequestBuilder.Method;
import com.sqwan.common.net.risk.OnRetryCallback;
import com.sqwan.common.net.risk.RiskWebActivity;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import org.json.JSONObject;

/**
 * 统一弹窗逻辑
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
public class RiskInterceptor implements SqRequest.ResponseStateInterceptor {

    public static final int RISK_WEB_STATE_CODE = 42000;

    @Override
    public boolean onInterceptResponseState(@NonNull RequestBuilder builder,
        int httpStatus, int state, @NonNull String msg, @Nullable String data,
        @Nullable SqHttpCallback<?> callback) {

        if (state != RISK_WEB_STATE_CODE || data == null || data.isEmpty()) {
            // 如果不是特定状态码就不拦截
            return false;
        }

        int method = builder.getMethod();
        if (method != Method.GET && method != Method.POST) {
            // 如果当前这个请求不是 Get 或者 Post，那么就不进行拦截
            return false;
        }

        Activity gameActivity = SQContextWrapper.getActivity();
        if (gameActivity == null || gameActivity.isFinishing() || gameActivity.isDestroyed()) {
            // 如果 Activity 状态异常就不拦截，因为下面要弹对话框
            return false;
        }

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("data", new JSONObject(data));
            RiskWebActivity.startActivity(gameActivity, jsonObject, new OnRetryCallback() {

                @Override
                public void onRetry() {
                    try {
                        LogUtil.w("执行统一弹窗重试逻辑");
                        SqHttpClient.getInstance().enqueue(builder.build());
                    } catch (Exception e) {
                        LogUtil.e("执行统一弹窗重试逻辑异常", e);
                    }
                }

                @Override
                public void onNotRetry() {
                    try {
                        if (callback != null) {
                            callback.onResponseStateError(httpStatus, state, msg, data);
                        }
                    } catch (Exception e) {
                        LogUtil.e("执行统一弹窗不重试逻辑异常", e);
                    }
                }
            });

            return true;
        } catch (Exception e) {
            LogUtil.e("执行统一弹窗拦截逻辑失败, 不拦截", e);
            return false;
        }
    }
}