package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tools.report.event.IEventReporter;
import com.sqwan.common.track.SqTrackActionManager2;
import java.util.HashMap;
import java.util.Map;

/**
 * 埋点上报器
 * todo 实际用的埋点上报器应该继承IEventReporter
 * <AUTHOR>
 * @since 2023/7/31
 */
public class EventReporter implements IEventReporter {

    @Override
    public void init(@NonNull Context context) {

    }

    @Override
    public void setAccountId(@Nullable String uid) {

    }

    @Override
    public void setSuperProperties(@Nullable Map<String, Object> properties) {

    }

    @Override
    public void setDynamicSuperPropertiesTracker(@Nullable DynamicSuperPropertiesTracker tracker) {

    }

    @Override
    public void report(@NonNull String event, @Nullable Map<String, Object> properties) {
        SqTrackActionManager2.getInstance().trackAction(event, convertMap(properties), null);
    }

    private HashMap<String, String> convertMap(Map<String, Object> map) {
        HashMap<String, String> ret = new HashMap<>();
        if (map == null || map.isEmpty()) {
            return ret;
        }
        for (String key : map.keySet()) {
            Object value = map.get(key);
            if (value == null) {
                ret.put(key, null);
            } else if (value instanceof String) {
                ret.put(key, (String) value);
            } else {
                ret.put(key, String.valueOf(value));
            }
        }
        return ret;
    }

    @Override
    public void flush() {
        SqTrackActionManager2.getInstance().flush();
    }

    @Override
    public void setUserConsent(Map<String, Boolean> consent) {

    }
}
