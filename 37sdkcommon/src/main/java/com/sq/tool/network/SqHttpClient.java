package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sdk.sq.net.HttpClient;
import com.sdk.sq.net.HttpClient.Builder;
import com.sdk.sq.net.SqRetryPolicyFactory;
import com.sdk.sq.net.gateway.GateWayEncryptInterceptor;
import com.sdk.sq.net.gateway.GateWayEncryptInterceptor.Provider;
import com.sqwan.common.net.base.RequestUtil;
import com.sqwan.common.util.SQContextWrapper;
import java.util.HashSet;
import okhttp3.OkHttpClient;

/**
 * 统一的网络客户端, 负责初始化网络相关配置
 *
 * <AUTHOR>
 * @since 2023/7/4
 */
public class SqHttpClient {

    private static volatile SqHttpClient sInstance;

    private HttpClient mHttpClient;

    public static HttpClient getInstance() {
        if (sInstance == null) {
            synchronized (SqHttpClient.class) {
                if (sInstance == null) {
                    sInstance = new SqHttpClient();
                }
            }
        }

        return sInstance.getHttpClient();
    }

    @NonNull
    public HttpClient getHttpClient() {
        if (mHttpClient != null) {
            return mHttpClient;
        }
        Context context = SQContextWrapper.getApplicationContext();
        Builder builder = new Builder();
        mHttpClient = builder
            .setOkHttpBuilder(createBuilder())
            .setReqIdGenerator(RequestUtil::generateRequestId)
            .setFallbackToHttp(true)
            .setRetryPolicyFactory(new SqRetryPolicyFactory(5000, 15000))
            .setEventReporter(new EventReporter())
            .setExceptionReporter(new ExceptionReporter())
            .build(context);
        return mHttpClient;
    }

    private static OkHttpClient.Builder createBuilder() {
        return new OkHttpClient.Builder()
            .addInterceptor(new RequestIdOkInterceptor())
            .addInterceptor(new SignInterceptor())
            .addInterceptor(new GateWayEncryptInterceptor(new Provider() {
                @NonNull
                @Override
                public String provideKey() {
                    return GateWayManager.getKey();
                }

                @NonNull
                @Override
                public HashSet<String> provideWhiteList() {
                    return GateWayManager.getWhiteList();
                }

                @NonNull
                @Override
                public String provideXRequestVersion() {
                    return GateWayManager.getXVersion();
                }
            }, new ExceptionReporter()));
    }
}
