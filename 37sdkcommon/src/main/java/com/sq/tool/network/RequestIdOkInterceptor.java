package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sqwan.common.data.cache.SpRequestInfo;
import com.sqwan.common.net.base.RequestUtil;
import com.sqwan.common.util.SQContextWrapper;
import java.io.IOException;
import okhttp3.Interceptor;
import okhttp3.Response;

/**
 * 增加请求id头
 *
 * <AUTHOR>
 * @since 2023/7/28
 */
public class RequestIdOkInterceptor implements Interceptor {

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Context context = SQContextWrapper.getApplicationContext();
        return chain.proceed(chain.request().newBuilder()
            .addHeader("Request-Id", RequestUtil.generateRequestId())
            .addHeader("Request-LiveId", SpRequestInfo.getRequestLiveId(context))
            .build());
    }
}
