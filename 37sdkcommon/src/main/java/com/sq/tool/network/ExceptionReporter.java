package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sq.tools.report.exception.IExceptionReporter;
import com.sqwan.common.BuglessAction;
import java.util.Map;

/**
 * 异常上报器
 * todo 实际使用的异常上报应该继承IExceptionReporter
 * <AUTHOR>
 * @since 2023/7/31
 */
public class ExceptionReporter implements IExceptionReporter {

    @Override
    public void init(Context context) {

    }

    @Override
    public void reportException(@NonNull Throwable exception, int actionType, String msg, String data) {
        BuglessAction.reportCatchException(exception, msg, data, actionType);
    }

    @Override
    public void setUserConsent(Map<String, Boolean> consent) {

    }
}
