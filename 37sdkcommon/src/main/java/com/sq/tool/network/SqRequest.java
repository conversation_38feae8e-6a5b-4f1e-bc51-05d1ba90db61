package com.sq.tool.network;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder;
import com.sdk.sq.net.RequestBuilder.Body;
import com.sdk.sq.net.RequestBuilder.Method;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sq.tool.network.SignInterceptor.SignVersion;
import com.sq.tool.network.SignV2Interceptor.SignExt;
import com.sqnetwork.voly.AuthFailureError;
import com.sqnetwork.voly.Request;
import com.sqnetwork.voly.VolleyError;
import com.sqnetwork.voly.VolleyLog;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONObject;

/**
 * 符合国内37规范的通用请求
 *
 * <AUTHOR>
 * @see SqHttpClient
 * @see RequestBuilder
 * @since 2022/8/16
 */
public class SqRequest {

    @Nullable
    private Request<?> mRequest;
    private final RequestBuilder mRequestBuilder;
    private List<ResponseStateInterceptor> mResponseStateInterceptors;

    private SqRequest(String url) {
        mRequestBuilder = SqHttpClient.getInstance().newBuilder().url(url);
    }

    public static SqRequest of(String url) {
        return new SqRequest(url)
            // 所有请求都拦截指定风险错误码
            .addResponseStateInterceptor(new RiskInterceptor());
    }

    /**
     * @see RequestBuilder.Body
     */
    public SqRequest bodyType(int bodyType) {
        mRequestBuilder.bodyType(bodyType);
        return this;
    }

    public SqRequest signV1() {
        mRequestBuilder.tag(SignVersion.class, SignVersion.V1);
        return this;
    }

    public SqRequest signV2(SignExt ext) {
        mRequestBuilder.tag(SignVersion.class, SignVersion.V2).tag(SignExt.class, ext);
        return this;
    }

    public SqRequest signV3() {
        mRequestBuilder.tag(SignVersion.class, SignVersion.V3);
        return this;
    }

    public SqRequest signV5() {
        mRequestBuilder.tag(SignVersion.class, SignVersion.V5);
        return this;
    }

    public SqRequest sign(SignVersion signVersion, SignExt ext) {
        mRequestBuilder.tag(SignVersion.class, signVersion).tag(SignExt.class, ext);
        return this;
    }

    /**
     * 增加参数转换器, 可以在请求前对请求参数做处理
     */
    public SqRequest addParamsTransformer(ParamsTransformer transformer) {
        mRequestBuilder.addParamsTransformer(transformer);
        return this;
    }

    /**
     * 批量添加参数, 会覆盖{@link #addParam(String, Object)}添加的参数
     * 可以通过{@link #bodyType(int)}指定请求体类型
     */
    public SqRequest params(Map<String, String> params) {
        mRequestBuilder.params(params);
        return this;
    }

    /**
     * form请求的参数, 会覆盖{@link #addParam(String, Object)}添加的参数
     */
    public SqRequest formParams(Map<String, String> params) {
        mRequestBuilder.formBody(params);
        return this;
    }

    /**
     * 添加参数, 会被批量添加参数的方法覆盖
     */
    public SqRequest addParam(String key, Object value) {
        mRequestBuilder.addParam(key, value);
        return this;
    }

    /**
     * 添加请求头
     */
    public SqRequest addHeader(String name, String value) {
        mRequestBuilder.addHeader(name, value);
        return this;
    }

    /**
     * json请求的参数, 会覆盖{@link #addParam(String, Object)}添加的参数
     */
    public SqRequest jsonParams(Map<String, Object> params) {
        mRequestBuilder.jsonBody(params);
        return this;
    }

    /**
     * 添加响应状态拦截器, 可以对服务端返回的state做统一逻辑处理
     */
    public SqRequest addResponseStateInterceptor(ResponseStateInterceptor interceptor) {
        if (interceptor == null) {
            return this;
        }
        if (mResponseStateInterceptors == null) {
            mResponseStateInterceptors = new ArrayList<>();
        }

        mResponseStateInterceptors.add(interceptor);
        return this;
    }

    public RequestBuilder builder() {
        return mRequestBuilder;
    }

    public void get(@Nullable SqHttpCallback<JSONObject> callback) {
        get(callback, JSONObject.class);
    }

    public <Data> void get(@Nullable SqHttpCallback<Data> callback, @NonNull Class<Data> dataClass) {
        enqueue(Method.GET, callback, dataClass);
    }

    public void post(@Nullable SqHttpCallback<JSONObject> callback) {
        post(callback, JSONObject.class);
    }

    public <Data> void post(@Nullable SqHttpCallback<Data> callback, @NonNull Class<Data> dataClass) {
        enqueue(Method.POST, callback, dataClass);
    }

    private <Data> void enqueue(int method, @Nullable SqHttpCallback<Data> callback, @NonNull Class<Data> dataClass) {
        mRequestBuilder.method(method);
        mRequestBuilder.callback(new SqHttpCallback<Data>(dataClass) {

            @Override
            protected int getOkState() {
                return callback != null ? callback.getOkState() : super.getOkState();
            }

            @Override
            protected String getStateKey() {
                return callback != null ? callback.getStateKey() : super.getStateKey();
            }

            @Override
            protected String getMsgKey() {
                return callback != null ? callback.getMsgKey() : super.getMsgKey();
            }

            @Override
            protected String getDataKey() {
                return callback != null ? callback.getDataKey() : super.getDataKey();
            }

            @Override
            public void onSuccess(int httpStatus, @NonNull Map<String, String> headers, JSONObject response) {
                if (callback != null) {
                    callback.setResp(response);
                }
                super.onSuccess(httpStatus, headers, response);
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                // 服务端返回的state表示失败
                boolean intercept = false;
                if (mResponseStateInterceptors != null) {
                    for (ResponseStateInterceptor interceptor : mResponseStateInterceptors) {
                        intercept = interceptor.onInterceptResponseState(mRequestBuilder,
                            httpStatus, state, msg, data, callback);
                        if (intercept) {
                            VolleyLog.w("请求回调被%s拦截, state=%d, msg=%s",
                                interceptor.getClass().getName(), state, msg);
                            break;
                        }
                    }
                }
                if (intercept) {
                    return;
                }
                if (VolleyLog.DEBUG) {
                    VolleyLog.w("%s 请求失败, state=%d, msg=%s", mRequestBuilder.getUrl(), state, msg);
                }
                if (callback != null) {
                    callback.onResponseStateError(httpStatus, state, msg, data);
                }
            }

            @Override
            public void onSuccess(Data data) {
                if (VolleyLog.DEBUG) {
                    VolleyLog.i("%s 请求成功 %s", mRequestBuilder.getUrl(), getResponseStr());
                }
                if (callback != null) {
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                // 接口请求异常
                if (VolleyLog.DEBUG) {
                    VolleyLog.e("%s 请求异常, code=%d, errorMsg=%s", mRequestBuilder.getUrl(), code, errorMsg);
                }
                if (callback != null) {
                    callback.onFailure(code, errorMsg, error);
                }
            }
        });
        if (mRequestBuilder.getBodyType() == Body.FORM || mRequestBuilder.getBodyType() == Body.RAW_FORM) {
            // 对于form请求, 强制把参数转string, 空对象转空字符串
            mRequestBuilder.addParamsTransformer(new StringifyTransformer());
        }
        mRequestBuilder.addHeadersTransformer(new IpHeaderTransformer());
        Request<?> request = mRequestBuilder.build();
        mRequest = request;
        SqHttpClient.getInstance().enqueue(request);
    }

    @Nullable
    public Map<String, String> getRequestParams() {
        try {
            return mRequest != null ? mRequest.getParams() : null;
        } catch (AuthFailureError e) {
            return null;
        }
    }

    public interface ResponseStateInterceptor {

        /**
         * state非成功时拦截
         *
         * @param builder 请求
         * @param httpStatus http状态码
         * @param state 接口返回的state, 非成功
         * @param msg 接口返回的错误信息
         * @return true表示拦截, 不进行回调
         */
        boolean onInterceptResponseState(@NonNull RequestBuilder builder, int httpStatus, int state,
            @NonNull String msg, @Nullable String data, @Nullable SqHttpCallback<?> callback);
    }

    @Nullable
    public static Map<String, String> stringMap(@Nullable Map<String, ?> map) {
        if (map == null) {
            return null;
        }
        Map<String, String> ret = new LinkedHashMap<>();
        for (String key : map.keySet()) {
            Object value = map.get(key);
            if (value == null) {
                ret.put(key, "");
            } else if (value instanceof String) {
                ret.put(key, (String) value);
            } else {
                ret.put(key, String.valueOf(value));
            }
        }
        return ret;
    }

    @NonNull
    public static String getTransInfo() {
        // TODO: 2024/2/4 预留透传参数
        return "";
    }
}
