package com.sq.tool.network;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sdk.sq.net.SqRequestCallback;
import com.sqnetwork.voly.VolleyError;
import java.util.Map;
import org.json.JSONObject;

/**
 * 符合国内通用37结构的请求回调
 *
 * <AUTHOR>
 * @since 2022/8/16
 */
public abstract class SqHttpCallback<Data> extends SqRequestCallback<Data> {

    private static final int STATE_OK = 1;

    private static final String KEY_ERROR_CODE = "state";

    private static final String KEY_ERROR_MSG = "msg";

    private static final String KEY_DATA = "data";

    private JSONObject mResp;

    public SqHttpCallback() {
        super();
    }

    public SqHttpCallback(Class<Data> dataClass) {
        super(dataClass);
    }

    @Override
    protected int getOkState() {
        return STATE_OK;
    }

    @Override
    protected String getStateKey() {
        return KEY_ERROR_CODE;
    }

    @Override
    protected String getMsgKey() {
        return KEY_ERROR_MSG;
    }

    @Override
    protected String getDataKey() {
        return KEY_DATA;
    }

    @Override
    public void onSuccess(int httpStatus, @NonNull Map<String, String> headers, JSONObject response) {
        mResp = response;
        super.onSuccess(httpStatus, headers, response);
    }

    /**
     * 请求成功, 并且解析成功
     */
    public abstract void onSuccess(Data data);

    public abstract void onFailure(int code, String errorMsg, @NonNull VolleyError error);

    @Override
    public void onRequestSuccess(int httpStatus, Data response) {
        onSuccess(response);
    }

    @Override
    public void onRequestError(int code, @NonNull VolleyError error) {
        onFailure(code, VolleyErrorUtil.simpleErrorMsg(error), error);
    }

    public static String wrapStr(int state, String msg) {
        return "state=" + state + "(" + msg + ")";
    }

    protected void setResp(JSONObject resp) {
        mResp = resp;
    }

    @Nullable
    @Override
    public JSONObject getResponse() {
        return mResp;
    }

    @NonNull
    @Override
    public String getResponseStr() {
        return mResp == null ? "" : mResp.toString();
    }

    public static abstract class SimpleSqHttpCallback<Data> extends SqHttpCallback<Data> {

        @Override
        public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {

        }

        @Override
        public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {

        }
    }
}
