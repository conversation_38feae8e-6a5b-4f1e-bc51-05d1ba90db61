package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.msdk.config.ConfigManager;
import java.io.IOException;
import okhttp3.Interceptor;
import okhttp3.Response;

/**
 * 全局签名拦截器, 通过{@link com.sqnetwork.voly.Request#setTag(Class, Object)}选择具体的签名
 *
 * <AUTHOR>
 * @since 2023/7/28
 */
public class SignInterceptor implements Interceptor {

    private SignV1Interceptor mV1;
    private SignV2Interceptor mV2;
    private SignV3Interceptor mV3;
    private SignV3Interceptor mV4;
    private SignV5Interceptor mV5;

    private synchronized void init() {
        if (mV1 != null) {
            return;
        }
        Context context = SQContextWrapper.getApplicationContext();
        String key = ConfigManager.getInstance(context).getAppKey();
        if (key == null || key.isEmpty()) {
            throw new IllegalStateException("需要在ConfigManager设置key后调用");
        }

        mV1 = new SignV1Interceptor(key);
        mV2 = new SignV2Interceptor(key);
        mV3 = new SignV3Interceptor(key);
        // 部分用户相关接口使用特殊的key加解密
        mV4 = new SignV3Interceptor("!/DIzcJLYE)@X7UC~b9Pn]}<eAr?|Wlw");
        mV5 = new SignV5Interceptor(key);
    }

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        com.sqnetwork.voly.Request<?> volleyRequest = chain.request().tag(com.sqnetwork.voly.Request.class);
        if (volleyRequest != null) {
            SignVersion signVersion = volleyRequest.getTag(SignVersion.class);
            if (signVersion != null) {
                init();
                switch (signVersion) {
                    case V1:
                        return mV1.intercept(chain);
                    case V2:
                        return mV2.intercept(chain);
                    case V3:
                        return mV3.intercept(chain);
                    case V4:
                        return mV4.intercept(chain);
                    case V5:
                        return mV5.intercept(chain);
                    default:
                        break;
                }
            }
        }
        return chain.proceed(chain.request());
    }

    /**
     * 签名版本, 结合{@link com.sqnetwork.voly.Request#setTag(Class, Object)}选择签名
     */
    public enum SignVersion {
        V1,
        V2,
        V3,
        V4,
        V5,
    }
}
