package com.sq.tool.network;

import android.content.Context;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sdk.sq.net.RequestBuilder.HeadersTransformer;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.SQContextWrapper;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/5
 */
class IpHeaderTransformer implements HeadersTransformer {

    @Nullable
    @Override
    public Map<String, String> transform(@Nullable Map<String, String> headers) {
        Context context = SQContextWrapper.getApplicationContext();
        if (context == null) {
            return headers;
        }
        if (!ActivityLifeCycleUtils.getInstance().isForeground()) {
            // 合规问题, 不能在后台频繁获取ip, 因此当在后台时, 直接返回
            return headers;
        }
        String ipv4 = SensitiveInfoManager.getInstance().getIpAddress(context);
        String ipv6 = SensitiveInfoManager.getInstance().getIpV6Address(context);
        if (TextUtils.isEmpty(ipv4) && TextUtils.isEmpty(ipv6)) {
            return headers;
        }
        if (headers == null) {
            headers = new HashMap<>();
        }
        if (!TextUtils.isEmpty(ipv4)) {
            headers.put("Client-Ipv4", ipv4);
        }
        if (!TextUtils.isEmpty(ipv6)) {
            headers.put("Client-Ipv6", ipv6);
        }
        return headers;
    }
}
