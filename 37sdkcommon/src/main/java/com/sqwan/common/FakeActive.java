package com.sqwan.common;

import android.content.Context;
import android.content.SharedPreferences;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.RequestIdOkInterceptor;
import com.sq.tool.network.SignInterceptor.SignVersion;
import com.sq.tool.network.SignV1Interceptor;
import com.sq.tool.network.SignV2Interceptor;
import com.sq.tool.network.SignV3Interceptor;
import com.sq.tools.Logger;
import com.sq.tools.event.EventCollection;
import com.sq.tools.event.EventPost;
import com.sq.tools.event.EventRequest;
import com.sq.tools.event.EventsTracker;
import com.sq.tools.proxy.Pair;
import com.sqwan.common.request.MapParams;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.NetWorkUtils;
import java.util.Map;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.json.JSONObject;


/**
 * 假激活实现,根据讨论与调研，以下情况会使用假激活:
 * <ol>
 * <blockquote> 后端返回 500 </blockquote>
 * <blockquote> 登陆超时超过两次以上 </blockquote>
 * <blockquote> http 错误码是5开头 </blockquote>
 * </ol>
 */
public enum FakeActive {

    INSTANCE;

    private final String SQ_PREFS = "sq_prefs";
    public final String M_FAKE_ACTIVE_KEY = "sq_m_fake_active_content";
    public final String S_FAKE_ACTIVE_KEY = "sq_s_fake_active_content";

    private EventsTracker tracker;


    public void saveContent(Context context, String content, String key) {
        if (context == null || TextUtils.isEmpty(content)) return;
        SharedPreferences.Editor editor = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE).edit();
        editor.putString(key, content);
        editor.apply();
    }

    /**
     * Get Saved Fake Active Content and start keep request active
     *
     * @param version 签名走拦截器, 所以这里需要传参, 然后手动签名
     */
    @Nullable
    public String requireContent(Context context, String key, String url, Map<String, String> params,
        SignVersion version) {
        if (context == null) {
            return null;
        }
        if (!NetWorkUtils.isNetworkAvailable(context) || NetWorkUtils.isWifiProxy()) {
            return null;
        }

        String content = getContent(context, key);
        if (TextUtils.isEmpty(content)) {
            return null;
        }

        Logger.info("本地激活逻辑开始运行");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOACL_ACTIVE);
        BuglessAction.reportCatchException(
            new Exception("Fake Active"), "本地激活: url:" + url, BuglessAction.FAKE_ACTIVE);

        String sign;
        switch (version) {
            case V1:
                sign = SignV1Interceptor.sign(params);
                break;
            case V2:
                // 暂时的场景没有ext, 所以固定传空
                sign = SignV2Interceptor.sign(params, null);
                break;
            case V3:
                sign = SignV3Interceptor.sign(params);
                break;
            default:
                sign = null;
                break;
        }
        if (sign != null) {
            params.put("sign", sign);
        }
        keepRequest(context, url, params);
        return content;
    }

    @Nullable
    public String getContent(Context context, String key) {
        if (context == null) return null;
        SharedPreferences preferences = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        if (!preferences.contains(key)) return null;
        return preferences.getString(key, "");
    }

    private void keepRequest(Context context, String url, Map<String, String> params) {
        if (tracker == null) {
            tracker = new EventsTracker(context, new FakeActiveFiled(context), url);
            tracker.setPostAvailable(true);
            tracker.setEventPoster(new FakeActivePost());
            tracker.setStorageFileName("SQ_FAKE_ACTIVE.log");
            tracker.setFlushNum(1);
        }
        MapParams m = new MapParams(params);
        tracker.log("fakeActive", new Pair("params", m.toPostParam(context)),
            new Pair("url", url),
            new Pair("type", m.contentType));
    }

    private static class FakeActiveFiled extends EventRequest {

        // We don't have any common data, so leave this class empty
        public FakeActiveFiled(Context context) {
            super(context);
        }

        @Override
        protected void fresh(Context context) {
        }
    }

    private final OkHttpClient mOkHttpClient = new OkHttpClient.Builder()
        .addInterceptor(new RequestIdOkInterceptor())
        .build();

    private static class FakeActivePost extends EventPost {

        @Override
        protected boolean sendToServer(@NonNull Context context, @NonNull EventRequest eventRequest,
            @NonNull EventCollection co, String url) {
            SQLog.d("本地激活尝试进行补偿上报, 条数: %d", co.size());
            boolean success = true;
            for (int i = 0; i < co.size(); i++) {
                JSONObject json = co.get(i).toJsonObject();
                String params = json.optString("params");
                String contentType = json.optString("type");
                // 方法参数传进来的url是固定的, 不能用
                HttpUrl realUrl = HttpUrl.parse(json.optString("url"));
                if (TextUtils.isEmpty(params) || TextUtils.isEmpty(contentType) || realUrl == null) {
                    continue;
                }

                RequestBody body = RequestBody.create(MediaType.parse(contentType), params);
                Request request = new Request.Builder()
                    .post(body)
                    .url(realUrl)
                    .build();

                Response response = null;
                try {
                    response = FakeActive.INSTANCE.mOkHttpClient.newCall(request).execute();
                    if (response.isSuccessful()) {
                        SQLog.i("补偿请求[" + realUrl + "]成功");
                    } else {
                        SQLog.w("补偿请求[" + realUrl + "]失败, " + response.code());
                    }
                    // 因EventPost 本身为批量埋点而设计，此处若部分请求成功，部分失败，下次轮询依旧全部重新请求
                    // 此值是 true 才会清空请求值，否则下次轮询此方法依旧会被调用
                    success = success && response.isSuccessful();
                } catch (Exception e) {
                    SQLog.e("补偿请求[" + realUrl + "]异常", e);
                    success = false;
                } finally {
                    if (response != null) {
                        response.close();
                    }
                }
            }
            SQLog.i("本次本地激活补偿上报结果: %s", success);
            return success;
        }
    }

}
