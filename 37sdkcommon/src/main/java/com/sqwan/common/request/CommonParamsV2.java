package com.sqwan.common.request;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/1
 */
public class CommonParamsV2 implements ParamsTransformer {

    private final boolean mBeforeActive;

    public CommonParamsV2() {
        this(false);
    }

    public CommonParamsV2(boolean beforeActive) {
        mBeforeActive = beforeActive;
    }

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        params.put("pid", config.getPartner());
        params.put("gid", config.getGameid());
        params.put("refer", config.getRefer());
        params.put("dev", DevLogic.getInstance(context).getValue());
        params.put("sversion", VersionUtil.sdkVersion);
        params.put("version", AppUtils.getVersionName(context));
        params.put("gwversion", VersionUtil.gwversion);
        params.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("scut", getCodeOfLogin(context));
        if (MultiSdkManager.getInstance().isScut3()) {
            params.put("scut3", MultiSdkManager.getInstance().getScut3());
        }
        params.put("from", "android");
        // 宿主版本号
        params.put("host_sdk_version", VersionUtil.getOriginalVersion());
        params.put(SqConstants.IS_ROOT, RootLogic.getInstance(context).getValue());
        params.put(SqConstants.IS_SIMULATOR, SimulatorLogic.getInstance(context).getValue());
        return params;
    }

    protected String getCodeOfLogin(Context context) {
        if (mBeforeActive) {
            return "";
        } else {
            return ConfigManager.getInstance(context).getLoginCode() + "";
        }
    }
}
