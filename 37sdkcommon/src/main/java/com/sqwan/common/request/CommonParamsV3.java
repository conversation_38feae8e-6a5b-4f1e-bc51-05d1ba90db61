package com.sqwan.common.request;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sq.sdk.tool.util.AppUtils;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.data.cache.DevManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/1
 */
public class CommonParamsV3 implements ParamsTransformer {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        final Context context = SQContextWrapper.getApplicationContext();
        SQAppConfig sqAppConfig = ConfigManager.getInstance(context).getSQAppConfig();
        String gid = sqAppConfig.getGameid();
        String pid = sqAppConfig.getPartner();
        String refer = sqAppConfig.getRefer();
        String version = AppUtils.getAppVersionName(context);
        params.put(SqConstants.PID, pid);
        params.put(SqConstants.GID, gid);
        params.put(SqConstants.REFER, refer);
        params.put(SqConstants.DEV, DevLogic.getInstance(context).getValue());
        params.put(SqConstants.ANDROID_ID, DevManager.getAndroidId(DevLogic.getInstance(context).isAuthCheck()));
        params.put(SqConstants.VERSION, version);
        params.put(SqConstants.GWVERSION, VersionUtil.gwversion);
        params.put(SqConstants.SVERSION, VersionUtil.sdkVersion);
        params.put(SqConstants.MAC, MacLogic.getInstance(context).getValue());
        params.put(SqConstants.IMEI, ImeiLogic.getInstance(context).getValue());
        params.put(SqConstants.OS, "android");
        params.put(SqConstants.OVER, android.os.Build.VERSION.RELEASE);
        params.put(SqConstants.TIME, String.valueOf(System.currentTimeMillis() / 1000));
        // 宿主的版本号
        params.put(SqConstants.HOST_SDK_VERSION, VersionUtil.getOriginalVersion());
        params.put(SqConstants.IS_ROOT, RootLogic.getInstance(context).getValue());
        params.put(SqConstants.IS_SIMULATOR, SimulatorLogic.getInstance(context).getValue());
        return params;
    }
}
