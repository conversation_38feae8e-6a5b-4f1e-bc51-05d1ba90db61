package com.sqwan.common.request;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/19 15:51
 */
public class RspBeanWrapperBase {
    private int state;
    private String msg;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
    public boolean isSuccess() {
        return state == 1;
    }
}
