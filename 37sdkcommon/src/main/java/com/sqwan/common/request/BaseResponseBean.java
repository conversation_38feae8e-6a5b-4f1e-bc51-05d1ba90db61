package com.sqwan.common.request;


import com.sqwan.common.net.sq.Response;

/**
 * <AUTHOR>
 * @date 2019/9/2
 */
public class BaseResponseBean {
    private int state;
    private String msg;
    private String data;

    public BaseResponseBean(){}

    public BaseResponseBean(Response response) {
        if(response == null) return;
        setMsg(response.getMessage());
        setData(response.getData());
        setState(response.getState());
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getData() {
        return data;
    }

    public boolean isSuccess() {
        return state == 1;
    }

    @Override
    public String toString() {
        return "BaseResponseBean{" +
                "state=" + state +
                ", msg='" + msg + '\'' +
                ", data='" + data + '\'' +
                '}';
    }
}
