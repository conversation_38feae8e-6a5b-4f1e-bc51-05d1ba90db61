package com.sqwan.common.request;

import com.sqwan.base.L;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MD5Util;
import com.sqwan.msdk.config.ConfigManager;

import java.util.Map;
import java.util.TreeMap;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-09 15:15
 */
public class SignUtils {
    public static String sign(Map<String, Object> params) {
        Map<String, Object> sortedParams = new TreeMap<>(params);
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> param : sortedParams.entrySet()) {
            signStr.append(param.getKey()).append("=").append(param.getValue());
        }
        signStr.append(ConfigManager.getInstance(L.getApplicationContext()).getAppKey());
        LogUtil.i("common sgin: signStr :" + signStr);
        return MD5Util.Md5(signStr.toString()).toLowerCase();
    }
}
