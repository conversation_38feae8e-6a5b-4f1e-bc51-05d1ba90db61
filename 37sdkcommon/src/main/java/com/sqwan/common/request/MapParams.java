package com.sqwan.common.request;

import com.sq.tools.network.ContentType;
import com.sq.tools.network.request.RequestTools;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO annotation
 */
public class MapParams extends RequestTools {


    public MapParams(Map<String, String> params) {
        this(new HashMap<>(params));
    }

    public MapParams(HashMap<String, String> params) {
        this.transparent = new HashMap<>(params);
        isSign = false;
        contentType = ContentType.FORM;
    }
}
