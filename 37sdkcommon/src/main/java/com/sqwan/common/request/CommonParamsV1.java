package com.sqwan.common.request;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/1
 */
public class CommonParamsV1 implements ParamsTransformer {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        String gid = config.getGameid();
        String pid = config.getPartner();
        String refer = config.getRefer();
        String version = AppUtils.getVersionName(context);
        params.put("gid", gid);
        params.put("pid", pid);
        params.put("refer", refer);
        params.put("version", version);
        params.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("dev", DevLogic.getInstance(context).getValue());

        params.put("sversion", VersionUtil.sdkVersion);
        params.put("gwversion", VersionUtil.gwversion);
        params.put(SqConstants.IS_ROOT, RootLogic.getInstance(context).getValue());
        params.put(SqConstants.IS_SIMULATOR, SimulatorLogic.getInstance(context).getValue());
        return params;
    }
}
