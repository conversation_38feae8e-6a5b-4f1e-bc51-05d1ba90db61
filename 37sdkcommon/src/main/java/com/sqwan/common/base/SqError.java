package com.sqwan.common.base;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.network.VolleyErrorUtil;
import com.sqnetwork.voly.VolleyError;

/**
 * 所有错误码都有相同格式
 *
 * <AUTHOR>
 * @since 2023/4/6
 */
public class SqError implements Parcelable {

    /**
     * sdk 内部错误码
     */
    public final int code;
    @NonNull
    public final String msg;
    /**
     * 其他端口错误码, 如http请求错误码, 第三方sdk错误码
     */
    public final int originCode;
    @Nullable
    public final String originMsg;

    public SqError(int code, @NonNull String msg) {
        this(code, msg, -1, null);
    }

    public SqError(int code, @NonNull String msg, int originCode, @Nullable String originMsg) {
        this.code = code;
        this.msg = msg;
        this.originCode = originCode;
        this.originMsg = originMsg;
    }

    public SqError(SqError error) {
        this(error.code, error.msg, error.originCode, error.originMsg);
    }

    private static final String HTTP_ERROR = "http request error";
    private static final String PARSE_ERROR = "http data parse error";

    /**
     * 接口返回的code != 0
     */
    public static SqError serverError(int code, int state, String msg) {
        return new SqError(code, msg, state, msg);
    }

    /**
     * 接口请求异常
     */
    public static SqError httpError(int code, @Nullable VolleyError error) {
        int originCode = VolleyErrorUtil.errorCode(error);
        return new SqError(code, HTTP_ERROR, originCode, VolleyErrorUtil.simpleErrorMsg(error));
    }

    public static SqError parseError(int code, @Nullable Exception error) {
        return new SqError(code, PARSE_ERROR, -1, error != null ? error.getMessage() : null);
    }

    /**
     * 百位数指定出错模块
     */
    public int getModule() {
        return (Math.abs(code) % 1000) / 100;
    }

    @NonNull
    @Override
    public String toString() {
        if (TextUtils.isEmpty(originMsg)) {
            return msg + "(" + code + "/" + originCode + ")";
        } else {
            return msg + "(" + code + "/" + originCode + "), [" + originMsg + "]";
        }
    }

    protected SqError(Parcel in) {
        code = in.readInt();
        String m = in.readString();
        msg = m == null ? "unknown(p)" : m;
        originCode = in.readInt();
        originMsg = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(code);
        dest.writeString(msg);
        dest.writeInt(originCode);
        dest.writeString(originMsg);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SqError> CREATOR = new Creator<SqError>() {
        @Override
        public SqError createFromParcel(Parcel in) {
            return new SqError(in);
        }

        @Override
        public SqError[] newArray(int size) {
            return new SqError[size];
        }
    };
}
