package com.sqwan.common.dev;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.cache.DeviceCacheHelper;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;

/**
 * @author: zls
 * @date: 2025/3/25
 */
public class SimulatorLogic extends AbstractDeviceInfoLogic {

    private static final String SIMULATOR_KEY = "dev_simulator";

    //simulator默认值
    private static final String SIMULATOR_DEFAULT = "";

    private static SimulatorLogic instance;

    private SimulatorLogic(Context context) {
        mContext = context;
    }

    public static SimulatorLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (SimulatorLogic.class) {
                if (instance == null) {
                    instance = new SimulatorLogic(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void saveToCache(String value) {
        DeviceCacheHelper.save(mContext, SIMULATOR_KEY, value);
    }

    @Override
    public DeviceInfoBean getFromCache() {
        String simulator = DeviceCacheHelper.get(mContext, SIMULATOR_KEY);
        if (!TextUtils.isEmpty(simulator)) {
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_CACHE, simulator);
        }
        return null;
    }

    @Override
    public DeviceInfoBean getFromSystemApi() {
        if (!isAuthCheck()) return null;
        LogUtil.i("simulator, get from sys api");
        String simulator = DeviceUtils.isSimulator(mContext) ? "1" : "0";
        if (!TextUtils.isEmpty(simulator)) {
            saveToCache(simulator);
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_SYS_API, simulator);
        }
        return null;
    }

    @Override
    public DeviceInfoBean random() {
        LogUtil.i("simulator, get from default value");
        if (isAuthCheck()) {
            saveToCache(SIMULATOR_DEFAULT);
        }
        return new DeviceInfoBean(DeviceInfoBean.SOURCE_RANDOM, SIMULATOR_DEFAULT);
    }
}
