package com.sqwan.common.dev;

import android.content.Context;
import android.text.TextUtils;

import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.cache.DeviceCacheHelper;
import com.sqwan.common.util.LogUtil;

/**
 * <AUTHOR>
 * @date 2020/1/15
 */
public class MacLogic extends AbstractDeviceInfoLogic {


    private static final String MAC_KEY = "dev_mac_2";

    /**
     * mac 默认值
     */
    private static final String MAC_DEFAULT = "020000000000";


    private static MacLogic instance;

    private MacLogic(Context context) {
        mContext = context;
    }

    public static MacLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (MacLogic.class) {
                if (instance == null) {
                    instance = new MacLogic(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void saveToCache(String value) {
        DeviceCacheHelper.save(mContext, MAC_KEY, value);
    }

    @Override
    public DeviceInfoBean getFromCache() {
        String mac = DeviceCacheHelper.get(mContext, MAC_KEY);
        if (!TextUtils.isEmpty(mac)) {
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_CACHE, mac);
        }
        return null;
    }

    @Override
    public DeviceInfoBean getFromSystemApi() {
        if (!isAuthCheck()) return null;
        LogUtil.i("mac, get from sys api");
        String mac = SensitiveInfoManager.getInstance().getMacAddress(mContext);
        if (!TextUtils.isEmpty(mac)) {
            saveToCache(mac);
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_SYS_API, mac);
        }
        return null;
    }

    /**
     * mac获取不到 直接返回默认值
     */
    @Override
    public DeviceInfoBean random() {
        LogUtil.i("mac, get from default value");
        if (isAuthCheck()) {
            saveToCache(MAC_DEFAULT);
        }
        return new DeviceInfoBean(DeviceInfoBean.SOURCE_RANDOM, MAC_DEFAULT);
    }
}
