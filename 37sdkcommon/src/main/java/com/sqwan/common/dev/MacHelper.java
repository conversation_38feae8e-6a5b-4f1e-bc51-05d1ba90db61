package com.sqwan.common.dev;

import android.content.Context;

import com.sq.tools.manager.SensitiveInfoManager;

/**
 * <AUTHOR>
 * @date 2020/1/16
 */
public class MacHelper {

    public static String getMac(Context context){
        return getMac(context,true);
    }

    public static String getMac(Context context,boolean isAuthCheck) {
        if (!isAuthCheck) {
            return "";
        }
        return SensitiveInfoManager.getInstance().getMacAddress(context);
    }
}
