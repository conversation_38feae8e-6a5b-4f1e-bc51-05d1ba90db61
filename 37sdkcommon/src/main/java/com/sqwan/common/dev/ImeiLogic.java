package com.sqwan.common.dev;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.support.v4.app.ActivityCompat;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.sqwan.common.cache.DeviceCacheHelper;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.TelephonyInfoUtils;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2020/1/15
 */
public class ImeiLogic extends AbstractDeviceInfoLogic {


    private static final String IMEI_KEY = "dev_imei_2";

    /**
     * 随机生成imei前缀
     */
    private static final String IMEI_PREFIX = "999";

    /**
     * 随机生成imei, 随机数的长度
     */
    private static final int IMEI_RANDOM_LENGTH = 14;

    private static ImeiLogic instance;

    private ImeiLogic(Context context) {
        mContext = context;
    }

    public static ImeiLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (ImeiLogic.class) {
                if (instance == null) {
                    instance = new ImeiLogic(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void saveToCache(String value) {
        DeviceCacheHelper.save(mContext, IMEI_KEY, value);
    }

    @Override
    public DeviceInfoBean getFromCache() {
        String imei = DeviceCacheHelper.get(mContext, IMEI_KEY);
        if (!TextUtils.isEmpty(imei)) {
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_CACHE, imei);
        }
        return null;
    }

    @Override
    public DeviceInfoBean getFromSystemApi() {
        if (!isAuthCheck()) return null;
        LogUtil.i("imei, get from sys api");
        String imei = TelephonyInfoUtils.getDeviceId(mContext);
        if (!TextUtils.isEmpty(imei)) {
            saveToCache(imei);
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_SYS_API, imei);
        }
        return null;
    }

    /**
     * imei 生成规则： 999 + 14位随机数字
     */
    @Override
    public DeviceInfoBean random() {
        LogUtil.i("imei, get from random");
        Random random = new Random(System.currentTimeMillis());
        StringBuilder randomNum = new StringBuilder();
        for (int i = 0; i < IMEI_RANDOM_LENGTH; i++) {
            int subNum = random.nextInt(9);
            randomNum.append(subNum);
        }
        String imei = IMEI_PREFIX + randomNum.toString();
        if (isAuthCheck()) {
            saveToCache(imei);
        }
        return new DeviceInfoBean(DeviceInfoBean.SOURCE_RANDOM, imei);
    }
}
