package com.sqwan.common.dev;

import android.content.Context;
import android.text.TextUtils;
import com.sqwan.common.cache.DeviceCacheHelper;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;

/**
 * @author: zls
 * @date: 2025/3/25
 */
public class RootLogic extends AbstractDeviceInfoLogic {

    private static final String ROOT_KEY = "dev_root";
    private static RootLogic instance;

    //root默认值
    private static final String ROOT_DEFAULT = "";

    private RootLogic(Context context) {
        mContext = context;
    }

    public static RootLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (RootLogic.class) {
                if (instance == null) {
                    instance = new RootLogic(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void saveToCache(String value) {
        DeviceCacheHelper.save(mContext, ROOT_KEY, value);
    }

    @Override
    public DeviceInfoBean getFromCache() {
        String root = DeviceCacheHelper.get(mContext, ROOT_KEY);
        if (!TextUtils.isEmpty(root)) {
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_CACHE, root);
        }
        return null;
    }

    @Override
    public DeviceInfoBean getFromSystemApi() {
        if (!isAuthCheck()) return null;
        LogUtil.i("root, get from sys api");
        String root = DeviceUtils.isRoot() ? "1" : "0";
        if (!TextUtils.isEmpty(root)) {
            saveToCache(root);
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_SYS_API, root);
        }
        return null;
    }

    @Override
    public DeviceInfoBean random() {
        LogUtil.i("root, get from default value");
        if (isAuthCheck()) {
            saveToCache(ROOT_DEFAULT);
        }
        return new DeviceInfoBean(DeviceInfoBean.SOURCE_RANDOM, ROOT_DEFAULT);
    }
}
