package com.sqwan.common.dev;

import android.content.Context;

import com.plugin.standard.IDevValue;

/**
 * <AUTHOR>
 * @date 2020/1/15
 *
 * 获取设备信息逻辑模板类
 *
 */
public abstract class AbstractDeviceInfoLogic implements IDevValue {


    protected Context mContext;

    private boolean isAuthCheck = false;

    /**
     * 获取顺序  cache --> system api --> random
     */
    public DeviceInfoBean getDeviceInfo() {
        DeviceInfoBean deviceInfoBean = getFromCache();
        if(deviceInfoBean == null) {
            deviceInfoBean = getFromSystemApi();
        }
        if(deviceInfoBean == null) {
            deviceInfoBean = random();
        }
        return deviceInfoBean;
    }

    public String getValue() {
        return getDeviceInfo().getValue();
    }

    public abstract void saveToCache(String value);

    public abstract DeviceInfoBean getFromCache();

    public abstract DeviceInfoBean getFromSystemApi();

    /**
     * 按照一定的规则生成
     */
    public abstract DeviceInfoBean random();


    public boolean isAuthCheck() {
        return isAuthCheck;
    }

    public void setAuthCheck(boolean authCheck) {
        isAuthCheck = authCheck;
    }

}
