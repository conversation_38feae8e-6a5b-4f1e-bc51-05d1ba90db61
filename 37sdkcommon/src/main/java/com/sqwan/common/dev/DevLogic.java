package com.sqwan.common.dev;

import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;

import com.sqwan.common.cache.DeviceCacheHelper;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.TelephonyInfoUtils;

/**
 * <AUTHOR>
 * @date 2020/1/16
 */
public class DevLogic extends AbstractDeviceInfoLogic {

    private static final String DEV_KEY = "dev2";

    private static DevLogic instance;

    private DevLogic(Context context) {
        mContext = context;
    }

    public static DevLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (DevLogic.class) {
                if (instance == null) {
                    instance = new DevLogic(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void saveToCache(String value) {
        DeviceCacheHelper.save(mContext, DEV_KEY, value, false);
    }

    @Override
    public DeviceInfoBean getFromCache() {
        String dev = DeviceCacheHelper.get(mContext, DEV_KEY, false);
        if (!TextUtils.isEmpty(dev)) {
            return new DeviceInfoBean(DeviceInfoBean.SOURCE_CACHE, dev);
        }
        return null;
    }

    @Override
    public DeviceInfoBean getFromSystemApi() {
        return null;
    }

    /**
     * 新版dev生成规则：
     * 按照 md5(imei + mac + android_id + t) 计算
     * t 代表请求m层激活接⼝口传递的时间参数对应的毫秒级时间戳
     */
    @Override
    public DeviceInfoBean random() {
        String imei = ImeiLogic.getInstance(mContext).getValue();
        String mac = MacLogic.getInstance(mContext).getValue();
        String androidId = TelephonyInfoUtils.getSettingAndroidId(mContext, isAuthCheck());
        String t = System.currentTimeMillis() + "";
        String dev = MD5Util.Md5(imei + mac + androidId + t);
        if (isAuthCheck()) {
            saveToCache(dev);
        }
        return new DeviceInfoBean(DeviceInfoBean.SOURCE_RANDOM, dev);
    }

}
