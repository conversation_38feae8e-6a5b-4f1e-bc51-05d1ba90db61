package com.sqwan.common.constants;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface SqConstants {

    /**
     * 联运商
     */
    String PID = "pid";

    /**
     * 游戏ID
     */
    String GID = "gid";

    /**
     * 位置标识
     */
    String REFER = "refer";

    /**
     * 设备号
     */
    String DEV = "dev";

    /**
     * 安卓ID
     */
    String ANDROID_ID = "android_id";

    /**
     * gaid
     */
    String GAID = "gaid";

    /**
     * afid
     */
    String AFID = "afid";


    /**
     * 是否简版
     */
    String SCUT = "scut";

    String APP_ID = "appid";

    String OPEN_ID = "openid";

    String CODE = "code";

    String ACCESS_TOKEN = "access_token";

    String REFRESH_TOKEN = "refresh_token";

    /**
     * 游戏版本号
     */
    String VERSION = "version";

    /**
     * 时间戳
     */
    String TIME = "time";

    /**
     * 宿主版本号
     */
    String HOST_SDK_VERSION = "host_sdk_version";

    /**
     * 校验令牌
     */
    String SIGN = "sign";

    /**
     * 设备MAC地址
     */
    String MAC = "mac";

    /**
     * 设备标识码
     */
    String IMEI = "imei";

    /**
     * 分辨率宽
     */
    String WPI = "wpi";

    /**
     * 分辨率高
     */
    String HPI = "hpi";

    /**
     * 型号, 如MI 1S
     */
    String MODE = "mode";

    /**
     * 系统，Android为1
     */
    String OS = "os";

    /**
     * 系统版本
     */
    String OVER = "over";

    /**
     * 设备厂商
     */
    String BRAND = "brand";

    /**
     * 电话号码
     */
    String PHONE = "phone";

    /**
     * 底层包名
     */
    String DPGN = "dpgn";

    /**
     * 广告标识符
     */
    String IDFA = "idfa";


    /**
     * Vindor标识符
     */
    String IDFV = "idfv";

    /**
     * 开源唯一标识
     */
    String OUDID = "oudid";

    /**
     * gaid
     */
    String IDENTIFIER = "identifier";

    /**
     * 网络类型
     */
    String NWK = "nwk";

    /**
     * 37手游自编版本号
     */
    String SVERSION = "sversion";

    /**
     * 37手游sdk业务版本号
     */
    String GWVERSION = "gwversion";

    /**
     * 透传参数
     */
    String TRANS_INFO = "trans_info";

    /**
     * 设备是否root
     */
    String IS_ROOT = "is_root";

    /**
     * 是否模拟器
     */
    String IS_SIMULATOR = "is_simulator";

    /**
     * 加密接口渠道标识
     */
    String SUA = "sua";

    /**
     * 唯一ID，unique ID, 使用H5渲染生成的指纹
     */
    String UNID = "unid";

    /**
     * 文件系统SizeID,md5(机型+dsize+key)后小写(key为dev加密的key)
     */
    String SIZD = "sizd";

    /**
     * 硬盘总空间大小
     */
    String DSIZE = "dsize";

    /**
     * 手机安装的app列表
     */
    String APPS = "apps";

    /**
     * 渠道登录验证token;
     */
    String TOKEN = "token";

    /**
     * 渠道登录验证参数
     */
    String PDATA = "pdata";

    /**
     * 登录用户name
     */
    String UNAME = "uname";

    /**
     * 登录用户password
     */
    String UPWD = "upwd";

    /**
     * 手机号码注册验证码
     */
    String SCODE = "scode";

    /**
     * Cp订单ID
     */
    String DOID = "doid";

    /**
     * Cp游戏服ID(标识)
     */
    String DSID = "dsid";

    /**
     * 服务器名字
     */
    String DSNAME = "dsname";

    /**
     * CP扩展回调参数
     */
    String DEXT = "dext";

    /**
     * Cp角色ID
     */
    String DRID = "drid";

    /**
     * Cp角色名
     */
    String DRNAME = "drname";

    /**
     * Cp角色等级
     */
    String DRLEVEL = "drlevel";

    /**
     * Cp金额
     */
    String DMONEY = "dmoney";

    /**
     * Cp兑换比率
     */
    String DRADIO = "dradio";

    /**
     * Dtime研发下单时间，单位秒
     */
    String DTIME = "dtime";

    /**
     * Dsign研发下单参数签名
     */
    String DSIGN = "dsign";

    /**
     * 用户UID
     */
    String UID = "uid";

    /**
     * 地区
     */
    String LOCALE = "locale";

    /**
     * Cp商品ID
     */
    String PRODUCT_ID = "product_id";

    /**
     * CP商品名称
     */
    String PRODUCT_NAME = "product_name";

    /**
     * CP商品描述
     */
    String PRODUCT_DESC = "product_desc";

    /**
     * m层订单Id
     */
    String MOID = "moid";

    /**
     * 支付方式
     */
    String PAYWAY = "pway";

    /**
     * 游戏Vip等级
     */
    String DVIPLEVEL = "dviplevel";

    /**
     * 角色创建时间
     */
    String DRCTIME = "drctime";

    /**
     * 角色升级时间
     */
    String DRLEVELMTIME = "drlevelmtime";

    /**
     * 公会名称
     */
    String DPNAME = "dpname";

    /**
     * 玩家余额
     */
    String DRBALANCE = "drbalance";

    /**
     * AppId
     */
    String APPID = "appid";

    /**
     * session_id, sdk打点统计使用
     */
    String SESSION_ID = "session_id";

    /**
     * 客户端事件发生时间
     */
    String CDATE = "cdate";

    /**
     * 客户端上报时间
     */
    String REPORT_TIME = "report_time";

    /**
     * 事件ID
     */
    String EVENT = "event";

    /**
     * UUID
     */
    String UUID = "uuid";

    /**
     * CURRENCY本地货币
     */
    String CURRENCY = "currency";

    /**
     * MONEY金额
     */
    String MONEY = "money";

    /**
     * 时区
     */
    String TIMEZONE = "timezone";

    /**
     * 国家
     */
    String COUNTRY = "country";

    /**
     * 国家码
     */
    String COUNTRY_CODE = "country_code";

    /**
     * VERTIFY
     */
    String VERIFY = "verify";

    String OAUTH_FB = "fb";

    String OAUTH_GP = "google";

    String OAUTH_SQ = "self";

    String OAUTH_TYPE = "type";

    String ALIAS_NAME = "alias_name";

    /**
     * 渠道登录验证
     */
    String PTOKEN = "ptoken";

    /********************************************相关请求码，响应码*************************************************/
    int RC_GP_SIGN_IN = 10001;
    int RC_GP_PAY = 10002;


    /**
     * 推广事件上报
     */
    String APP_LAUNCH = "AppLaunch";
    String COMPLETED_REGISTRATION = "CompletedRegistration";
    String COMPLETED_TUTORIAL = "CompletedTutorial";
    String LEVEL_ACHIEVED = "LevelAchieved";
    String INITIAL_CHECKOUT = "InitialCheckout";
    String PURCHASED = "Purchased";
    String PURCHASE_CANCELLED = "PurchaseCancelled";
    String ALL_CHANNEL = "AllChannel";
    String PURCHASE_DETAIL = "PurchasedDetail";
    String PURCHASE_DETAIL_NONE = "none";
    String PURCHASE_DETAIL_ONE = "one";
    String PURCHASE_DETAIL_ALL = "all";

    String EVENT_CODE = "event_code";
    String EVENT_VALUE = "event_value";
    String EXT = "ext";
    String COMPLEMENT = "complement";

    /**
     *
     * 上线前测试发现的订单异常
     * 10002：Verify Param Validate Fail     -- 订单参数验证失败
     * 30004：order not found                --  订单表找不到这笔订单
     * 30019：order already shipped          -- sy_payment表显示发货失败
     * 30027：verify failed；                --订单验证失败
     * 30078：verify pay status fail         -- 订单支付状态验证失败
     *
     *
     *SDK目前未判断的状态
     * 30005：order verify failed!           -- 订单信息校验失败
     * 30016：verify failed!'                -- 订单早于现在60天不允许发货
     * 30018：'order already shipped!'       -- sy_payment表已经显示发货成功
     *
     * 20000：'not login!'                   --token校验失败 需要重新登录以后拿到token
     * */

    int VerifyParamValidateFailCode = 10002;
    int VerifyOrderNotFound = 30004;
    int VerifyOrderAlreadyShippedFailed = 30019;
    int VerifyFailCode = 30027;
    int VerifyPayStatusFailCode = 30078;

    int VerifyOrderAlreadyShippedSuccess = 30018;
    int VerifyOrderOverdue = 30016;
    int VerifyTokenFailCode = 20000;

}
