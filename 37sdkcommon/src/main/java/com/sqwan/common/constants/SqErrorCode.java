package com.sqwan.common.constants;


import android.support.annotation.IntRange;

/**
 * SDK错误码
 * 格式：37xyyy
 * 备注：x表示模块，yyy表示错误码
 *
 * <AUTHOR>
 * @since 2022/4/19
 */
public final class SqErrorCode {

    /**
     * 初始化模块
     */
    public static final int MODULE_INIT = 0;
    /**
     * 登录模块
     */
    public static final int MODULE_LOGIN = 1;
    /**
     * 分享模块
     */
    public static final int MODULE_SHARE = 2;
    /**
     * 支付模块
     */
    public static final int MODULE_PAY = 3;

    /**
     * @param module 模块, 1位数, 全局支持10个模块
     * @param code 错误码, 3位数
     */
    public static int code(@IntRange(from = 0, to = 9) int module, @IntRange(from = 0, to = 999) int code) {
        return 370000 + module * 1000 + code;
    }
}
