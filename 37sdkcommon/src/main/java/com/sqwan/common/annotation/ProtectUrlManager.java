package com.sqwan.common.annotation;


import android.text.TextUtils;

import com.sq.tools.network.httpdns.SqHttpDns;
import com.sq.tools.utils.SoftwareUtils;

import java.util.ArrayList;
import java.util.HashSet;

public class ProtectUrlManager {

    private static ProtectUrlManager instance;

    //域名保护列表
    private ArrayList<String> protectHosts = new ArrayList<>();

    //主域保护列表
    private ArrayList<String> protectMainHosts = new ArrayList<>();

    public static ArrayList<String> defaultProtectHosts;

    static {
        defaultProtectHosts = new ArrayList<>();
        //bugles上报接口
        defaultProtectHosts.add("bugless.shan-yu-tech.com");
        //埋点上报接口
        defaultProtectHosts.add("track.37.com.cn");
    }

    private ProtectUrlManager() {
    }

    public static ProtectUrlManager getInstance() {
        if (instance == null) {
            synchronized (ProtectUrlManager.class) {
                if (instance == null) {
                    instance = new ProtectUrlManager();
                }
            }
        }
        return instance;
    }


    /**
     * 设置域名保护列表
     */
    public void setProtectUrls(ArrayList<String> protectUrls) {
        HashSet<String> preHostSet = new HashSet<>();
        if (protectUrls == null || protectUrls.isEmpty()) {
            return;
        }
        this.protectHosts.clear();
        this.protectHosts.addAll(defaultProtectHosts);
        for (String url : protectUrls) {
            String host = SoftwareUtils.getHost(url);
            if (!TextUtils.isEmpty(host)) {
                preHostSet.add(host);
            }
        }
        this.protectHosts.addAll(preHostSet);
        SqHttpDns.getInstance().setProtectHosts(protectHosts);
    }

    public ArrayList<String> getProtectHosts() {
        return this.protectHosts;
    }


    /**
     * 获取主域保护列表
     */
    public ArrayList<String> getProtectMainHosts() {
        return this.protectMainHosts;
    }

    /**
     * 添加主域保护列表
     */
    public void addProtectHost(String host) {
        if (protectHosts != null && !protectHosts.contains(host)) {
            protectMainHosts.add(host);
            SqHttpDns.getInstance().addProtectHost(host);
        }
    }

    /**
     * 添加主域保护列表
     */
    public void addProtectHosts(ArrayList<String> hosts) {
        if (protectHosts != null) {
            for (String host : hosts) {
                if (!protectHosts.contains(host)) {
                    protectMainHosts.add(host);
                    SqHttpDns.getInstance().addProtectHost(host);
                }
            }
        }
    }

    /**
     * 添加主域保护列表
     */
    public void addProtectMainHost(String mainHost) {
        if (protectMainHosts != null && !protectMainHosts.contains(mainHost)) {
            protectMainHosts.add(mainHost);
            SqHttpDns.getInstance().addProtectMainHost(mainHost);
        }
    }

}
