package com.sqwan.common.annotation;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.GateWayManager;
import com.sqwan.common.url.UrlMod;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class UrlUpdateUtils {

    private static final String SQ_URL_UPDATE = "sq_url_update";
    private static Map<String, String> urlMap = new HashMap<>();

    /**
     * 获取所有请求的url
     *
     * @return
     */
    public static ArrayList<String> getUrls() {
        ArrayList<String> urls = new ArrayList<>();
        for (Map.Entry<String, String> entry : UrlMod.getUrlMod().entrySet()) {
            Class<?> clazz = null;
            try {
                clazz = Class.forName(entry.getValue());
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    UrlUpdate urlUpdate = field.getAnnotation(UrlUpdate.class);
                    if (urlUpdate != null) {
                        if (Modifier.isStatic(field.getModifiers())) {
                            Object fieldUrl = field.get(clazz);
                            if (fieldUrl instanceof String) {
                                String url = (String) fieldUrl;
                                if (!TextUtils.isEmpty(url)) {
                                    urls.add(url);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return urls;
    }

    /**
     * 获取项目中所有的urlMap
     */
    public static HashMap<String, String> getProgramUrlMap() {
        HashMap<String, String> programUrlMap = new HashMap<>();
        for (Map.Entry<String, String> entry : UrlMod.getUrlMod().entrySet()) {
            Class<?> clazz = null;
            try {
                clazz = Class.forName(entry.getValue());
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    UrlUpdate urlUpdate = field.getAnnotation(UrlUpdate.class);
                    if (urlUpdate != null) {
                        String key = urlUpdate.value();
                        if (Modifier.isStatic(field.getModifiers())) {
                            Object fieldUrl = field.get(clazz);
                            if (fieldUrl instanceof String) {
                                String url = (String) fieldUrl;
                                programUrlMap.put(key, url);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return programUrlMap;
    }

    /**
     * 更新url值
     *
     * @param context
     */
    public static void urlUpdate(Context context) {
        for (Map.Entry<String, String> entry : UrlMod.getUrlMod().entrySet()) {
            Class<?> clazz = null;
            try {
                clazz = Class.forName(entry.getValue());
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    UrlUpdate urlUpdate = field.getAnnotation(UrlUpdate.class);
                    if (urlUpdate != null) {
                        String key;
                        if (urlMap.get(urlUpdate.xValue()) != null) {
                            key = urlUpdate.xValue();
                        } else {
                            key = urlUpdate.value();
                        }
                        if (Modifier.isStatic(field.getModifiers())) {
                            Object url = field.get(clazz);
                            if (url instanceof String) {
                                GateWayManager.addWhiteListUrl((String) url);
                                if (urlMap.get(key) != null) {
                                    String targetUrl = getUrlFromSpOrLocal(key, context);
                                    if (!TextUtils.isEmpty(targetUrl)) {
                                        SQLog.v("originalUrl: " + url + " targetUrl: " + targetUrl + " " + url.equals(targetUrl));
                                        field.set(null, targetUrl);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 刷新sp数据
     *
     * @param map
     * @param context
     */
    public static void setUrlData(Map<String, String> map, Context context) {
        if (map == null) return;
        urlMap.putAll(map);
        for (Map.Entry<String, String> entry : urlMap.entrySet()) {
            String key = entry.getKey();
            String url = entry.getValue();
            setUrlUpdateFromSp(key, url, context);
        }
    }

    private static void setUrlUpdateFromSp(String key, String value, Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_URL_UPDATE, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        String url = sp.getString(key, "");
        if (url != null && !url.equals(value)) {
            SQLog.w("原先url：" + url + "更新为：" + value);
        }
        editor.putString(key, value);
        editor.commit();
    }

    private static String getUrlFromSpOrLocal(String key, Context context) {
        SharedPreferences sp = context.getSharedPreferences(SQ_URL_UPDATE, Context.MODE_PRIVATE);
        String url = sp.getString(key, "");
        if (!TextUtils.isEmpty(url)) {
            return url;
        }
        return "";
    }

}
