package com.sqwan.common.util;

import android.support.annotation.NonNull;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @since 2023/4/13
 */
public class JsonMap {

    private JSONObject json;

    public JsonMap put(String key, Object value) {
        if (json == null) {
            json = new JSONObject();
        }

        try {
            json.put(key, value);
        } catch (Exception e) {
            /* no-op */
        }
        return this;
    }

    @NonNull
    @Override
    public String toString() {
        return json != null ? json.toString() : "";
    }
}
