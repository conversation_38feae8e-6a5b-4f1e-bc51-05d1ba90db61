package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;

/**
 * Android大小单位转换工具类
 *
 * <AUTHOR>
 * 废弃， 请使用tool
 */
public class DisplayUtil {
    /**
     * 将px值转换为dip或dp值，保证尺寸大小不变
     *
     * @param pxValue
     * @param scale   （DisplayMetrics类中属性density）
     * @return
     */
    public static int px2dip(float pxValue, float scale) {
        return (int) (pxValue / scale + 0.5f);
    }

    /**
     * 将px值转换为dip或dp值，保证尺寸大小不变
     *
     * @param context
     * @param pxValue
     * @return
     */
    public static int px2dip(Context context, float pxValue) {
        float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }

    /**
     * 将dip或dp值转换为px值，保证尺寸大小不变
     *
     * @param dipValue
     * @param scale    （DisplayMetrics类中属性density）
     * @return
     */
    public static int dip2px(float dipValue, float scale) {
        return (int) (dipValue * scale + 0.5f);
    }

    /**
     * 将dip或dp值转换为px值，保证尺寸大小不变
     *
     * @param context
     * @param dipValue
     * @return
     */
    public static int dip2px(Context context, float dipValue) {
        float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }


    /**
     * 将px值转换为sp值，保证文字大小不变
     *
     * @param pxValue
     * @param fontScale （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int px2sp(float pxValue, float fontScale) {
        return (int) (pxValue / fontScale + 0.5f);
    }

    /**
     * 将sp值转换为px值，保证文字大小不变
     *
     * @param spValue
     * @param fontScale （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int sp2px(float spValue, float fontScale) {
        return (int) (spValue * fontScale + 0.5f);
    }

    /**
     * 设置view的高和宽
     *
     * @param view
     * @param width      view的宽度
     * @param height     view的高度
     * @param widthScale 宽度的比例
     */
    public static void setViewMeasurement(View view, int width, int height, double widthScale) {

        if (width < height) {
            width = height;
        }
        int view_width = (int) (width * widthScale);

        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) view.getLayoutParams();
        params.width = view_width;
        params.height = view_width;
        params.setMargins(0, view_width + (view_width / 2), view_width, 0);

    }


    public static int getScreenWidth(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        return windowManager.getDefaultDisplay().getWidth();
    }

    public static int getScreenHeight(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        return windowManager.getDefaultDisplay().getHeight();
    }


    public static int getScreenLong(Context context) {
        int w = getScreenWidth(context);
        int h = getScreenHeight(context);
        return Math.max(w, h);
    }

    public static int getScreenShort(Context context) {
        int w = getScreenWidth(context);
        int h = getScreenHeight(context);
        return Math.min(w, h);
    }

    public static boolean isLandscape(Context context) {
        return getScreenWidth(context) > getScreenHeight(context);
    }

    /**
     * 是否是刘海屏手机
     *
     * @param activity
     * @return
     */
    public static boolean isCutoutScreen(Activity activity) {
        try {
            if (activity == null) {
                return false;
            }
            if (Build.VERSION.SDK_INT >= 28) {
                return activity.getWindow().getDecorView().getRootWindowInsets().getDisplayCutout() != null;
            }
            String manufacturer = Build.MANUFACTURER;
            if (!TextUtils.isEmpty(manufacturer)) {
                if (manufacturer.equalsIgnoreCase("HUAWEI")) {
                    return hwCutout(activity);
                }
                if (manufacturer.equalsIgnoreCase("xiaomi")) {
                    return xiaomiCutout(activity);
                }
                if (manufacturer.equalsIgnoreCase("oppo")) {
                    return oppoCutout(activity);
                }
                if (manufacturer.equalsIgnoreCase("vivo")) {
                    return vivoCutout(activity);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
            LogUtil.e(e.getMessage());
        }
        return false;
    }

    /**
     * 获取状态栏高度
     *
     * @param activity
     * @return
     */
    public static int getStatusBarHeight(Activity activity) {
        int identifier;
        if (activity != null && (identifier = activity.getResources().getIdentifier("status_bar_height", "dimen", "android")) > 0) {
            return activity.getResources().getDimensionPixelSize(identifier);
        }
        return 0;
    }


    /**
     * 华为刘海屏判断
     *
     * @param activity
     * @return
     */
    public static boolean hwCutout(Activity activity) {
        try {
            Class<?> loadClass = activity.getClassLoader().loadClass("com.huawei.android.util.HwNotchSizeUtil");
            return ((Boolean) loadClass.getMethod("hasNotchInScreen", new Class[0]).invoke(loadClass, new Object[0])).booleanValue();
        } catch (Exception unused) {
            return false;
        }
    }


    /**
     * oppo刘海屏判断
     *
     * @param activity
     * @return
     */
    public static boolean oppoCutout(Activity activity) {
        return activity.getPackageManager().hasSystemFeature("com.oppo.feature.screen.heteromorphism");
    }


    /**
     * vivo刘海屏判断
     *
     * @param activity
     * @return
     */
    public static boolean vivoCutout(Activity activity) {
        try {
            Class<?> cls = Class.forName("android.util.FtFeature");
            return ((Boolean) cls.getMethod("isFeatureSupport", new Class[]{Integer.TYPE}).invoke(cls, new Object[]{32})).booleanValue();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 小米刘海屏判断
     *
     * @param activity
     * @return
     */
    public static boolean xiaomiCutout(Activity activity) {
        try {
            Class<?> cls = Class.forName("android.os.SystemProperties");
            return ((Integer) cls.getMethod("getInt", new Class[]{String.class, Integer.TYPE}).invoke(cls, new Object[]{"ro.miui.notch", 0})).intValue() == 1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置view大小
     *
     * @param view   控件
     * @param width  宽 单位：dp
     * @param height 高 单位：dp
     */
    public static void setViewSize(View view, int width, int height) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        layoutParams.width = dip2px(view.getContext(), width);
        layoutParams.height = dip2px(view.getContext(), height);
        view.setLayoutParams(layoutParams);
    }


}
