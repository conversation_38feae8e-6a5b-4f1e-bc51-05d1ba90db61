package com.sqwan.common.util;


import com.sq.tool.logger.SQLog;

/**
 * <AUTHOR>
 * @date 2020/1/17
 */
public class LogUtil {

    public static String mTag = "sqsdk";

    public static void setTag(String tag) {
        mTag = tag;
    }

    public static void v(String msg) {
        v(mTag, msg);
    }

    public static void e(String msg) {
        e(mTag, msg);
    }

    public static void i(String msg) {
        i(mTag, msg);
    }

    public static void d(String msg) {
        d(mTag, msg);
    }

    public static void w(String msg) {
        w(mTag, msg);
    }

    public static void v(String tag, String msg) {
        SQLog.vt(tag, wrapLogMsg(msg));
    }

    public static void e(String tag, String msg) {
        SQLog.et(tag, wrapLogMsg(msg));
    }

    public static void i(String tag, String msg) {
        SQLog.it(tag, wrapLogMsg(msg));
    }

    public static void d(String tag, String msg) {
        SQLog.dt(tag, wrapLogMsg(msg));
    }

    public static void w(String tag, String msg) {
        SQLog.wt(tag, wrapLogMsg(msg));
    }

    public static void e(String msg, Throwable throwable) {
        e(mTag, msg, throwable);
    }

    public static void v(String tag, String msg, Throwable throwable) {
        SQLog.vt(tag, wrapLogMsg(msg), throwable);
    }

    public static void e(String tag, String msg, Throwable throwable) {
        SQLog.et(tag, wrapLogMsg(msg), throwable);
    }

    public static void i(String tag, String msg, Throwable throwable) {
        SQLog.it(tag, wrapLogMsg(msg), throwable);
    }

    public static void d(String tag, String msg, Throwable throwable) {
        SQLog.dt(tag, wrapLogMsg(msg), throwable);
    }

    public static void w(String tag, String msg, Throwable throwable) {
        SQLog.wt(tag, wrapLogMsg(msg), throwable);
    }

    private static String wrapLogMsg(String msg) {
        if (msg == null) {
            msg = "null";
        }
        return msg;
    }
}