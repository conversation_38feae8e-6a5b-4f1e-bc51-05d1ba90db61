package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONObject;

import java.lang.reflect.Field;

/**
 * @author: zls
 * @date: 2025/8/1
 */
public class MMOBindingUtils {

    private static final String TAG = "MMOBindingUtils";
    private static final String MMO_PACKAGE_NAME = "com.sy.yxjun1";

    public static final String BINDING_PARAMS = "bindingParams";

    private static MMOBindingCallback sCallback;
    private static String sBindingParams = "";
    private static String sCallingPackageName = "";
    private static Context sContext;

    public static void handlerBinding(Activity activity, String bindingParams) {
        try {
            sContext = activity.getApplicationContext();
            sBindingParams = bindingParams;
            if (TextUtils.isEmpty(bindingParams)) {
                LogUtil.i(TAG, "bindingParams is null");
                return;
            }
            String callingPackageName = getCallingPackageByReflection(activity);
            if (TextUtils.isEmpty(callingPackageName)) {
                JSONObject json = new JSONObject(bindingParams);
                String packageName = json.optString("callingPackageName");
                if (!TextUtils.isEmpty(packageName)) {
                    callingPackageName = packageName;
                }
            }
            sCallingPackageName = callingPackageName;
            if (MMO_PACKAGE_NAME.equals(callingPackageName)) {
                //校验成功，存储callback，登录成功之后再回调
//                sCallback = new MMOBindingCallback() {
//                    @Override
//                    public void executeCallback() {
//                        LogUtil.i(TAG, "handlerBinding bindingParams = " + sBindingParams + " callingPackageName = " + sCallingPackageName);
//                        //todo 执行绑定逻辑
//                        jumpToDlyzApp();
//                    }
//                };
                jumpToDlyzApp();
                LogUtil.i(TAG, "packageName = " + MMO_PACKAGE_NAME + " 校验通过");
            } else {
                sBindingParams = "";
                sCallingPackageName = "";
                sCallback = null;
            }
        } catch (Exception e) {
            LogUtil.i(TAG, "handlerBinding bindingParams error = " + e.getMessage());
        }
    }

    private static void jumpToDlyzApp() {
        try {
            if (sContext == null) {
                Log.e(TAG, "sContext = null ");
                return;
            }
            Intent launchIntent = sContext.getPackageManager().getLaunchIntentForPackage(MMO_PACKAGE_NAME);
            if (launchIntent != null) {
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                sContext.startActivity(launchIntent);
            } else {
                Log.e(TAG, "No launch intent found for package: " + MMO_PACKAGE_NAME);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to jumpToDlyzApp: " + e.getMessage());
        }
    }

    private static String getCallingPackageByReflection(Activity activity) {
        try {
            Field referrerField = Activity.class.getDeclaredField("mReferrer");
            referrerField.setAccessible(true);
            return (String) referrerField.get(activity);
        } catch (Exception e) {
            return "";
        }
    }

    interface MMOBindingCallback {
        void executeCallback();
    }
}
