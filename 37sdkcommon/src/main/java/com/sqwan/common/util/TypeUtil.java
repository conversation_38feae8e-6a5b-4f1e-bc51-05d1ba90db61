package com.sqwan.common.util;

public class TypeUtil {

    public static int saveParseInteger(String valueStr, int defaultValue) {
        try {
            return Integer.valueOf(valueStr);
        }catch (Exception e) {
            e.printStackTrace();
            return defaultValue;
        }
    }

    private float saveParseFloat(String valueStr, float defaultValue) {
        try {
            return Float.valueOf(valueStr);
        } catch (Exception e) {
            e.printStackTrace();
            return defaultValue;
        }
    }

}
