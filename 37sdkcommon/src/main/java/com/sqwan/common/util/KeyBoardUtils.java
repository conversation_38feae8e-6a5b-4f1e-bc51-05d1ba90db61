package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.ResultReceiver;
import android.support.annotation.NonNull;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.inputmethod.InputMethodManager;

import com.sqwan.base.L;

import java.util.Collections;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-03 18:25
 */
public class KeyBoardUtils {
    private final String TAG = this.getClass().getSimpleName();
    private View rootView; //activity的根视图
    private int rootViewVisibleHeight;//纪录根视图的显示高度
    private ViewTreeObserver.OnGlobalLayoutListener globalListener;
    private boolean isShowing = false;

    public interface OnSoftKeyBoardChangeListener {
        void keyBoardShow(int height);

        void keyBoardHide(int height);

        void viewChanged(int dheight);
    }


    long starttime;

    long endtime;

    int phoneHeight;

    public void addListener(Context activity, final OnSoftKeyBoardChangeListener onSoftKeyBoardChangeListener) {
        if (activity instanceof Activity) {
            //获取activity的根视图
            removeListener();
            rootView = ((Activity) activity).getWindow().getDecorView();
            //监听视图树中全局布局发生改变或者视图树中的某个视图的可视状态发生改变
            if (rootView != null) {

                globalListener = new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        Rect r = new Rect();
                        rootView.getWindowVisibleDisplayFrame(r);
                        //获取当前根视图在屏幕上显示的大小
                        int visibleHeight = r.height();
                        LogUtil.i(TAG, String.format("visibleHeight:%d,rootViewVisibleHeight:%d", visibleHeight, rootViewVisibleHeight));

                        if (rootViewVisibleHeight == 0) {
                            rootViewVisibleHeight = visibleHeight;
                            phoneHeight = visibleHeight;
                            LogUtil.i(TAG, "初始化高度：rootViewVisibleHeight: " + rootViewVisibleHeight);
                            return;
                        }

                        //根视图显示高度没有变化，可以看作软键盘显示／隐藏状态没有改变
                        if (rootViewVisibleHeight == visibleHeight) {
                            return;
                        }

                        //根视图显示高度变小超过200，可以看作软键盘显示了
                        if (rootViewVisibleHeight - visibleHeight > phoneHeight/4) {
                            if (onSoftKeyBoardChangeListener != null) {
                                onSoftKeyBoardChangeListener.keyBoardShow(rootViewVisibleHeight - visibleHeight);
                                starttime = System.currentTimeMillis();
                            }
                            isShowing = true;
                            rootViewVisibleHeight = visibleHeight;
                            return;
                        }

                        //根视图显示高度变大超过200，可以看作软键盘隐藏了
                        if (visibleHeight - rootViewVisibleHeight > phoneHeight/4) {
                            if (onSoftKeyBoardChangeListener != null) {
                                onSoftKeyBoardChangeListener.keyBoardHide(visibleHeight - rootViewVisibleHeight);
                            }
                            isShowing = false;
                            rootViewVisibleHeight = visibleHeight;
                            return;
                        }


                        if (isShowing) {
                            endtime = System.currentTimeMillis();
                            //int dHeight = visibleHeight - rootViewVisibleHeight;
                            // if (KeyBoardUtils.this.dHeight != dHeight) {
                            //  KeyBoardUtils.this.dHeight = dHeight;
                            //   rootViewVisibleHeight = visibleHeight;
                            if (endtime - starttime > 200) {
                                if (onSoftKeyBoardChangeListener != null) {
                                    onSoftKeyBoardChangeListener.viewChanged(visibleHeight - rootViewVisibleHeight);
                                }
                            }
                        }

                        rootViewVisibleHeight = visibleHeight;
                    }

                };
                rootView.getViewTreeObserver().addOnGlobalLayoutListener(globalListener);
            }
        }
    }

    public void removeListener() {
        if (globalListener != null) {
            if (rootView != null) {
                rootView.getViewTreeObserver().removeOnGlobalLayoutListener(globalListener);
            }
        }

    }

    public boolean dispatchTouchEvent(Activity activity, MotionEvent ev, List<? extends View> filterViews) {
        if (isShowing) {
            View view = activity.getCurrentFocus();
            if (isTouchView(Collections.singletonList(view), ev)) {
                return false;
            } else {
                if (isTouchView(filterViews, ev)) {
                    return false;
                } else {
                    hideKeyboard(activity);
                    return true;
                }
            }
        }
        return false;

    }


    private boolean isTouchView(List<? extends View> filterViews, MotionEvent ev) {
        int[] location = new int[2];
        for (View filterView : filterViews) {
            if (filterView == null) continue;
            filterView.getLocationOnScreen(location);
            int x = location[0];
            int y = location[1];
            if (ev.getX() > x && ev.getX() < x + filterView.getWidth()
                    && ev.getY() > y && ev.getY() < y + filterView.getHeight()) {
                return true;
            }
        }
        return false;
    }

    public void hideKeyboard(Context activity) {
        if (activity instanceof Activity) {
            Activity _activitiy = (Activity) activity;
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(_activitiy.getWindow().getDecorView().getWindowToken(), 0);
        }

    }

    /**
     * 隐藏键盘
     * 弹窗弹出的时候把键盘隐藏掉
     */
    public void hideInputKeyboard(Context activity,View v) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
    }


    /**
     * 弹起键盘
     */
    public void showInputKeyboard(Context activity,View v) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(v, 0);
    }
    public void showSoftInput() {
        InputMethodManager imm = (InputMethodManager) L.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm == null) {
            return;
        }
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
    }
    public  void showSoftInput(@NonNull final View view) {
        showSoftInput(view, 0);
    }

    /**
     * Show the soft input.
     *
     * @param view  The view.
     * @param flags Provides additional operating flags.  Currently may be
     *              0 or have the {@link InputMethodManager#SHOW_IMPLICIT} bit set.
     */
    public  void showSoftInput(@NonNull final View view, final int flags) {
        InputMethodManager imm =
                (InputMethodManager) L.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm == null) return;
        view.setFocusable(true);
        view.setFocusableInTouchMode(true);
        view.requestFocus();
        imm.showSoftInput(view, flags, new ResultReceiver(new Handler()) {
            @Override
            protected void onReceiveResult(int resultCode, Bundle resultData) {
                if (resultCode == InputMethodManager.RESULT_UNCHANGED_HIDDEN
                        || resultCode == InputMethodManager.RESULT_HIDDEN) {
                    toggleSoftInput();
                }
            }
        });
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
    }
    public void toggleSoftInput() {
        InputMethodManager imm =
                (InputMethodManager) L.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm == null) return;
        imm.toggleSoftInput(0, 0);
    }
    /**
     * Hide the soft input.
     *
     * @param view The view.
     */
    public  void hideSoftInput(@NonNull final View view) {
        InputMethodManager imm =
                (InputMethodManager) L.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm == null) return;
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }
}
