package com.sqwan.common.util.toast;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.util.SqResUtils;
import java.lang.reflect.Field;

public class MyToast {

    private static final int LONG_DELAY = 3500; // 3.5 seconds
    private static final int SHORT_DELAY = 2000; // 2 seconds
    private static String oldMsg;
    protected static Toast toast = null;
    private static ViewGroup toastView = null;
    private static TextView tvMsg = null;
    private static long oneTime=0;
    private static long twoTime=0;

    private static Field sField_TN ;
    private static Field sField_TN_Handler ;
    static {
        try {
            sField_TN = Toast.class.getDeclaredField("mTN");
            sField_TN.setAccessible(true);
            sField_TN_Handler = sField_TN.getType().getDeclaredField("mHandler");
            sField_TN_Handler.setAccessible(true);
        } catch (Exception e) {}
    }

    private static void hook(Toast toast) {
        try {
            Object tn = sField_TN.get(toast);
            Handler preHandler = (Handler)sField_TN_Handler.get(tn);
            sField_TN_Handler.set(tn,new SafelyHandlerWarpper(preHandler));
        } catch (Exception e) {}
    }

    public static void showToast(Context context, String s,int gravity,int offsetX,int offsetY){
        if (TextUtils.isEmpty(s)) {
            return;
        }
        if (toast == null) {
            toast = Toast.makeText(context, "", Toast.LENGTH_SHORT);
            toastView = (ViewGroup) LayoutInflater.from(context).inflate(
                SqResUtils.getLayoutId(context, "sy37_s_toast_layout"), null, false);
            tvMsg = toastView.findViewById(SqResUtils.getId(context, "tv_msg"));
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(Color.parseColor("#BB000000"));
            gradientDrawable.setShape(GradientDrawable.RECTANGLE);
            gradientDrawable.setCornerRadius(DisplayUtil.dip2px(context, 6));
            toastView.setBackground(gradientDrawable);
            tvMsg.setText(s);
            toast.setView(toastView);
            hook(toast);
            toast.setGravity(gravity, offsetX, offsetY);
            toast.show();
            oneTime = System.currentTimeMillis();
        } else {
            twoTime = System.currentTimeMillis();
            if (s.equals(oldMsg)) {
                if (twoTime - oneTime > SHORT_DELAY) {
                    toast.show();
                }
            } else {
                oldMsg = s;
                tvMsg.setText(s);
                toast.show();
            }
        }
        oneTime = twoTime;
    }

}