package com.sqwan.common.util;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;

import com.sqwan.msdk.config.ConfigManager;

public class EncryptUtil {
    /**
     * 对密码进行加密
     *
     * @param content 密码
     * @return 加密后的密码
     */
    public static String encrypt(String content) {
        String encode = "";
        try {
            byte[] encryptBytes = AESUtil.encrypt(content, getEncodeKey());
            encode = Base64.encodeToString(encryptBytes, Base64.NO_WRAP);
            LogUtil.i("加密后：" + encode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encode;
    }

    /**
     * 对密码进行解密
     *
     * @param content
     * @return
     */
    public static String decrypt(String content) {
        String decrypt = "";
        try {
            decrypt = AESUtil.decryptString(content, getEncodeKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decrypt;
    }

    /**
     * 加解密的密钥
     *
     * @return
     */
    private static String getEncodeKey() {
        Context context = SQContextWrapper.getActivity();
        if (context == null) {
            return "";
        }
        String encodeKey = "";
        String appKey = ConfigManager.getInstance(context).getAppKey();
        if (!TextUtils.isEmpty(appKey)) {
            int length = appKey.length();
            //不足16位则补齐0
            if (length < 16) {
                StringBuilder sb = new StringBuilder(appKey);
                for (int i = 0; i < 16 - length; i++) {
                    sb.append("0");
                }
                encodeKey = sb.toString();
            } else {
                //满16位则截取前16位
                encodeKey = appKey.substring(0, 16);
            }
        }
        return encodeKey;
    }
}
