package com.sqwan.common.util;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public enum SDKError {

    /**
     *
     */
    NET_REQUEST_FAIL(10001, "网络异常，请稍候再试"),
    NET_DATA_PARSE_ERROR(10002, "服务端数据异常，请联系客服【20002】"),


    NET_TIME_OUT_ERROR(10003, "网络超时,请稍候再试"),

    ACCOUNT_LOGIN_ERROR(20001, "登录数据错误，请联系客服【10004】"),

    ACCOUNT_LOGIN_CANCEL(20002, "取消登录"),


    PARAMS_ERROR_NULL(30001, "游戏数据为空，请联系客服【10005】"),

    PAY_FAIL(40001, "支付失败【20003】");



    public int code;
    public String message;

    SDKError(int code, String message) {
        this.code = code;
        this.message = message;
    }

}
