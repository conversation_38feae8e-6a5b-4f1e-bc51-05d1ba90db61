package com.sqwan.common.util;

import android.os.Bundle;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 数据转换工具类
 */
public class DataTransferUtil {


    /**
     * map转bundle
     *
     * @param data
     * @return
     */
    public static Bundle convertMapToBundle(Map<String, String> data) {
        Bundle bundle = new Bundle();
        if (data != null && !data.isEmpty()) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                bundle.putString(entry.getKey(), entry.getValue());
            }
        }
        return bundle;
    }

    /**
     * bundle转map
     *
     * @param bundle
     * @return
     */
    public static Map<String, String> convertBundleToMap(Bundle bundle) {
        Map<String, String> map = new HashMap<>();
        if (bundle != null && !bundle.isEmpty()) {
            Set<String> keySet = bundle.keySet();
            for (String key : keySet) {
                Object value = bundle.get(key);
                map.put(key, String.valueOf(value));
            }
        }
        return map;
    }

}
