package com.sqwan.common.util;

import android.Manifest;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Build.VERSION_CODES;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.MediaStore.MediaColumns;
import android.support.v4.content.FileProvider;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;

public class ImageUtils {
    /**
     * 保存图片至系统图库
     * @param context
     * @param bitmap
     * @param fileName
     * @return                 如果保存成功，则返回图片文件对象
     */
    public static Uri save(Context context, Bitmap bitmap, String fileName) {
        try {

            ContentResolver contentResolver = context.getContentResolver();

            Uri uri;
            File file = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // 适配 Android 10 分区存储特性
                ContentValues values = new ContentValues();
                // 设置显示的文件名
                values.put(MediaColumns.DISPLAY_NAME, fileName);
                // 设置输出的路径信息
                values.put(MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DCIM + File.separator + fileName);
                // 设置文件类型
                String mimeType = getMimeType(fileName);
                if (mimeType != null) {
                    values.put(MediaColumns.MIME_TYPE, mimeType);
                }
                // 生成一个新的 uri 路径
                uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                    context.checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_DENIED) {
                    LogUtil.i("没有存储卡权限，图片没法保存：" + fileName);
                    return null;
                }

                File parentFile = new File(EnvironmentUtils.getCommonDirPath(context), Environment.DIRECTORY_DCIM);
                file = new File(parentFile, fileName);

                if (!parentFile.exists()) {
                    parentFile.mkdirs();
                }
                if (!file.exists()) {
                    file.createNewFile();
                }

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    uri = FileProvider.getUriForFile(context, context.getPackageName() + ".provider", file);
                } else {
                    uri = Uri.fromFile(file);
                }
            }

            OutputStream outputStream = contentResolver.openOutputStream(uri);

            if (outputStream == null) {
                LogUtil.i("输出流为空，无法保存图片文件");
            }

            if (bitmap == null) {
                LogUtil.i("Bitmap 对象为空，无法保存图片文件");
            }

            if (outputStream != null && bitmap != null) {
                bitmap.compress(getBitmapFormat(fileName), 100, outputStream);
                outputStream.flush();
                outputStream.close();
            }

            ContentValues imageValues = new ContentValues();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                imageValues.put(MediaColumns.IS_PENDING, 0);
                contentResolver.update(uri, imageValues, null, null);
            } else {
                MediaScannerConnection.scanFile(context.getApplicationContext(), new String[]{file.getPath()}, null, null);
            }

            return uri;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.i("数据有误，保存图片失败：" + fileName);
            return null;
        }
    }

    private static String getMimeType(String fileName) {
        String lowerCaseFileName = fileName.toLowerCase();
        if (lowerCaseFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerCaseFileName.endsWith(".jpg") || lowerCaseFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerCaseFileName.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerCaseFileName.endsWith(".gif")) {
            return "image/gif";
        } else {
            return null;
        }
    }

    private static Bitmap.CompressFormat getBitmapFormat(String fileName) {
        String lowerCaseFileName = fileName.toLowerCase();
        if (lowerCaseFileName.endsWith(".png")) {
            return Bitmap.CompressFormat.PNG;
        } else if (lowerCaseFileName.endsWith(".jpg") || lowerCaseFileName.endsWith(".jpeg")) {
            return Bitmap.CompressFormat.JPEG;
        } else if (lowerCaseFileName.endsWith(".webp")) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                return Bitmap.CompressFormat.WEBP_LOSSLESS;
            } else {
                return Bitmap.CompressFormat.WEBP;
            }
        } else {
            return Bitmap.CompressFormat.PNG;
        }
    }
}
