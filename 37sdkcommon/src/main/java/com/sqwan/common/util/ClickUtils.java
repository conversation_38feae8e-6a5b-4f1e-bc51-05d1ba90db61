package com.sqwan.common.util;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-07 14:14
 */
public class ClickUtils {
    private static long fastClickTime = 500;
    public static void stePreviewClickListener(final View view, final View.OnClickListener onClickListener){
        view.setOnClickListener(new View.OnClickListener() {
            long lastTime = 0L;
            @Override
            public void onClick(View v) {
                long duration = Math.abs(SystemClock.elapsedRealtime() - lastTime);
                if (duration > fastClickTime) {
                    lastTime = SystemClock.elapsedRealtime();
                    onClickListener.onClick(view);
                }
            }
        });
    }
    public static void stePreviewClickListener(final View view, final long fastClickTime, final String toastMsg, final View.OnClickListener onClickListener){
        view.setOnClickListener(new View.OnClickListener() {
            long lastTime = 0L;
            @Override
            public void onClick(View v) {
                long duration = Math.abs(SystemClock.elapsedRealtime() - lastTime);
                if (duration > fastClickTime) {
                    lastTime = SystemClock.elapsedRealtime();
                    onClickListener.onClick(view);
                }else{
                    if (!TextUtils.isEmpty(toastMsg)) {
                        ToastUtil.showToast(toastMsg);
                    }
                }
            }
        });
    }
    public static void stePreviewEditorActionListener(final EditText view, final long fastClickTime, final TextView.OnEditorActionListener onEditorActionListener){
        view.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            long lastTime = 0L;
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEND){
                    long duration = Math.abs(SystemClock.elapsedRealtime() - lastTime);
                    if (duration > fastClickTime) {
                        lastTime = SystemClock.elapsedRealtime();
                        onEditorActionListener.onEditorAction(v,actionId,event);
                    }
                    return true;
                }

                return false;
            }
        });
    }

}
