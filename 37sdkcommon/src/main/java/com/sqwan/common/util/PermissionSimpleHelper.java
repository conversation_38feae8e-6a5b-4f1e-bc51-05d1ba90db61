package com.sqwan.common.util;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import com.sqwan.common.util.PermissionHelper.OnPermissionPageCallback;

public class PermissionSimpleHelper {

    public static final String[] STORAGE_PERMISSION = new String[] {Manifest.permission.WRITE_EXTERNAL_STORAGE};

    public static final String STORAGE_PERMISSION_NAME = "存储权限";

    public static final String STORAGE_PERMISSION_BY_SHARE = "申请存储权限：用于保存图片到本地，以实现图片分享功能";

    public static final String STORAGE_PERMISSION_BY_WEB = "申请存储权限：用于保存图片到本地，以实现网页保存图片功能";

    public static void requestPermission(String[] requestPermissions, String permissionName, String explain, OnPermissionCallback callback) {
        Activity activity = null;
        Activity resumedActivity = ActivityLifeCycleUtils.getInstance().getResumedActivity();
        if (resumedActivity != null) {
            activity = resumedActivity;
        }

        if (activity == null) {
            activity = SQContextWrapper.getActivity();
        }

        if (activity == null) {
            if (callback == null) {
                return;
            }
            callback.onDenied();
            return;
        }

        PermissionHelper permissionHelper = PermissionHelper.getInstance();

        String[] requestPermissionExplains = new String[] {"权限说明\n" + explain};

        if (permissionHelper.checkPermissions(requestPermissions)) {
            if (callback == null) {
                return;
            }
            callback.onGranted();
            return;
        }

        final Activity finalActivity = activity;
        permissionHelper.requestPermissions(activity, requestPermissions, requestPermissionExplains,
            PermissionHelper.SQ_REQUEST_PERMISSION_CODE, (permissions, grantResults) -> {

                boolean allGrantPermission = true;
                for (int grantResult : grantResults) {
                    if (grantResult != PackageManager.PERMISSION_GRANTED) {
                        allGrantPermission = false;
                        break;
                    }
                }

                if (!allGrantPermission) {
                    if (callback != null) {
                        callback.onDenied();
                    }
                } else {
                    if (callback != null) {
                        callback.onGranted();
                    }
                }
//                permissionHelper.showPermissionGuideDialog(finalActivity, permissions,
//                    "获取权限失败，请手动授予" + permissionName, new OnPermissionPageCallback() {
//
//                        @Override
//                        public void onGranted() {
//                            if (callback == null) {
//                                return;
//                            }
//                            callback.onGranted();
//                        }
//
//                        @Override
//                        public void onDenied() {
//                            if (callback == null) {
//                                return;
//                            }
//                            callback.onDenied();
//                        }
//                    });
            });
    }

    public interface OnPermissionCallback {

        void onGranted();

        void onDenied();
    }
}