package com.sqwan.common.util;

import android.text.TextUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.logging.Logger;

import javax.crypto.BadPaddingException;
import android.text.TextUtils;

import java.nio.charset.StandardCharsets;

import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class AESUtil {


    private static final String DEFAULT_PASSWORD = "2384039739279483";

    public static byte[] encrypt(String sSrc) throws Exception {
        return encrypt(sSrc, DEFAULT_PASSWORD);
    }
    public static byte[] encrypt(String sSrc, String password) throws Exception {
        byte[] raw = null;
        if (password != null) {
            raw = password.getBytes("UTF-8");
        }
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        return cipher.doFinal(sSrc.getBytes("UTF-8"));
    }
    public static byte[] decrypt(String sSrc) throws Exception {
        return decrypt(sSrc, DEFAULT_PASSWORD);
    }
    public static byte[] decrypt(String sSrc, String password) throws Exception {
        byte[] raw = null;
        if (password != null) {
            raw = password.getBytes("UTF-8");
        }
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        return cipher.doFinal(Base64.decode(sSrc));
    }

    public static String decryptString(String sSrc, String sKey) {
        try {
            // 判断Key是否正确
            if (TextUtils.isEmpty(sKey)) {
                LogUtil.i("AES 解密Key为null");
                return "";
            }
            // 判断Key是否为16位
            if (sKey.length() != 16) {
                LogUtil.i("AES 解密Key长度不是16位");
                return "";
            }
            byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);

            byte[] encrypted1 = Base64.decode(sSrc);//先用base64解密
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original, StandardCharsets.UTF_8);
        } catch (Exception e) {
            LogUtil.i("AES 解密失败");
            e.printStackTrace();
            return "";
        }
    }
}
