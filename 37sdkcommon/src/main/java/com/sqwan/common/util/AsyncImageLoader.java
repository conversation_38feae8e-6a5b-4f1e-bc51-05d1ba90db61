package com.sqwan.common.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.widget.ImageView;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.lang.ref.SoftReference;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


public class AsyncImageLoader {

	/**
	 * 图片加载线程池最大并发量
	 */
	public static final int POLL_MAX_LOADIMAGE_NUM = 8;

	public static final String BITMAP_CACHE = "/fdk_bitmap/";

	public static ExecutorService pool;
	private Context context;

	static{
		pool = Executors.newFixedThreadPool(POLL_MAX_LOADIMAGE_NUM);
	}

	/**
	 * 软引用对象，在响应内存需要时，由垃圾回收器决定是否清除此对象。软引用对象最常用于实现内存敏感的缓存。
	 */
	private HashMap<String, SoftReference<Bitmap>> imageCache;

	public AsyncImageLoader(Context context) {
		this.context = context;
		imageCache = new HashMap<String, SoftReference<Bitmap>>();
	}

	public void loadDrawable (final String imageUrl,
								final ImageView imageView, final ImageCallback imagecallback){

		final Handler handler = new Handler() {
			@Override
			public void handleMessage(Message msg) {
				super.handleMessage(msg);
				Bitmap bitmap = (Bitmap) msg.obj;
				imagecallback.imageLoaded(bitmap, imageView, imageUrl);
			}
		};
		if(TextUtils.isEmpty(imageUrl)){
			Message msg = handler.obtainMessage(0, null);
			handler.sendMessage(msg);
			return;
		}

		if (imageCache.containsKey(imageUrl)) {
			// 从缓存中读取
			SoftReference<Bitmap> softReference = imageCache.get(imageUrl);
			Bitmap bitmap = softReference.get();
			if (bitmap != null) {
				imagecallback.imageLoaded(bitmap, imageView, imageUrl);
				return;
			}

		} else if (EnvironmentUtils.checkSdCardPermission(context)) {

			/**
			 * 加上一个对本地缓存的查找
			 */

			if (imageUrl.lastIndexOf("/") == -1 || imageUrl.lastIndexOf(".")==-1) {
				Message msg = handler.obtainMessage(0, null);
				handler.sendMessage(msg);
				return;
			}
			String bitmapName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1,imageUrl.lastIndexOf("."));
			File cacheDir = new File(EnvironmentUtils.getCommonDirPath(context)+ BITMAP_CACHE);

			// 创建文件夹
			if (!cacheDir.exists()) {
				cacheDir.mkdirs();
			}

			File[] cacheFiles = cacheDir.listFiles();

			if(cacheFiles == null) {
				return;
			}

			int i = 0;
			for (; i < cacheFiles.length; i++) {
				if (bitmapName.equals(cacheFiles[i].getName())) {
					break;
				}
			}

			if (i < cacheFiles.length) {
				Bitmap bitmap = BitmapFactory.decodeFile(EnvironmentUtils.getCommonDirPath(context)
						+ BITMAP_CACHE
						+ bitmapName);

				imagecallback.imageLoaded(bitmap, imageView, imageUrl);
				return;
			}
		}

		pool.execute(new Thread() {
			public void run() {
				File bitmapFile = null;
				try {
					URL url = new URL(imageUrl);
					HttpURLConnection conn = (HttpURLConnection) url.openConnection();
					conn.setConnectTimeout(8*1000); // 注意要设置超时，设置时间不要超过10秒，避免被android系统回收
					if (conn.getResponseCode() != 200){
						Message msg = handler.obtainMessage(0, null);
						handler.sendMessage(msg);
						return;
					}

					InputStream inSream = conn.getInputStream();

					Bitmap bitmap = null;


					/**********检测sdcard的状态*********/
					if (EnvironmentUtils.checkSdCardPermission(context)) {

						/********Envinronment.getExternalStrorageDirectory()返回sdcard目录*******/
						File dir = new File(EnvironmentUtils.getCommonDirPath(context) + BITMAP_CACHE);
						if (!dir.exists()) {
							dir.mkdirs();
						}

						String bitmapName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1,imageUrl.lastIndexOf("."));

						bitmapFile = new File(EnvironmentUtils.getCommonDirPath(context)
								+ BITMAP_CACHE
								+ bitmapName);

						if (!bitmapFile.exists()) {
							bitmapFile.createNewFile();
						}

						FileOutputStream outStream = new FileOutputStream(bitmapFile);
						byte[] buffer = new byte[1024];
						int len = -1;
						while ((len = inSream.read(buffer)) != -1) {
							outStream.write(buffer, 0, len);
						}
						outStream.close();
						inSream.close();

						bitmap = BitmapFactory.decodeFile(EnvironmentUtils.getCommonDirPath(context)
								+ BITMAP_CACHE
								+ bitmapName);

						if (bitmap == null){
							LogUtil.e("bitmap 网络加载失败");
							if(bitmapFile.exists()){
								bitmapFile.delete();
							}
						}
					}else{
						bitmap = BitmapFactory.decodeStream(inSream);
					}

					Message msg = handler.obtainMessage(0, bitmap);
					handler.sendMessage(msg);

					imageCache.put(imageUrl, new SoftReference<Bitmap>(bitmap));

				} catch (Exception e) {
					// TODO Auto-generated catch block
					Message msg = handler.obtainMessage(0, null);
					handler.sendMessage(msg);

					if(bitmapFile!=null&&bitmapFile.exists()){
						bitmapFile.delete();
					}
				}
			}
		});

		return;

	}

	public interface ImageCallback {
		void imageLoaded(Bitmap imageDrawable, ImageView imageView,
								String imageUrl);
	}


}
