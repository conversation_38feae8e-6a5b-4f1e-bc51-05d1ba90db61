package com.sqwan.common.util;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import com.sqwan.base.ActivityResultListener;
import com.sqwan.base.BaseEnginHandler;
import com.sqwan.base.EventDispatcher;
import com.sqwan.base.L;
import com.sqwan.common.dialog.MessageDialog;
import com.sqwan.common.dialog.PermissionDescDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PermissionHelper extends BaseEnginHandler {
    public static final String[] DEFAULT_PERMISSIONS_DESC = new String[]{"权限说明\n读取设备唯一标识用于保护账号安全", "权限说明\n实现账号、图片的缓存和使用，图片保存与分享"};

    public static final int SQ_REQUEST_PERMISSION_CODE = 1110;
    public static String[] mInitPermissions = new String[]{Manifest.permission.READ_PHONE_STATE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
    private static PermissionHelper sInstance;
    private Map<Integer, PermissionCallback> mCallbackMaps;
    public static final int SETTING_REQUEST_CODE = 2121;
    public static final int APPLICATION_DETAILS_SETTINGS_REQUEST_CODE = 1024;
    private List<String> needRequestPermissions = new ArrayList<>();
    private List<String> permissionDesc = new ArrayList<>();
    private int currentPermissionPos = 0;
    private String currentPermission;
    private int currentRequestCode;
    private PermissionHelper.PermissionCallback currentPermissionCallback;
    private PermissionDescDialog permissionDescDialog;
    private Context mContext;
    private List<Integer> mGrantResults = new ArrayList<>();

    private PermissionHelper() {
        mCallbackMaps = new HashMap<>();
    }

    public static PermissionHelper getInstance() {
        if (sInstance == null) {
            sInstance = new PermissionHelper();
        }
        return sInstance;
    }

    public boolean checkPermission(String permission) {
        if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
            return false;
        }
        return true;
    }

    /**
     * 权限是否被禁止
     *
     * @param permission
     * @return
     */
    public boolean isPermissionForbiden(String permission) {
        Activity activity = checkValid();
        if (activity != null) {
            return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
        }
        return false;
    }

    /**
     * 请求权限
     *
     * @param permissions
     * @param requestCode
     */
    public void requestPermissions(String[] permissions, int requestCode, PermissionCallback onPermissionCallback) {
        mCallbackMaps.put(requestCode, onPermissionCallback);
        List<String> permissionsNeedRequest = new ArrayList<>();
        for (String permission : permissions) {
            if (!checkPermission(permission)) {
                permissionsNeedRequest.add(permission);
            }
        }
        if (permissionsNeedRequest.size() > 0) {
            String[] permissionArray = new String[permissionsNeedRequest.size()];
            Activity activity = checkValid();
            if (activity != null) {
                ActivityCompat.requestPermissions(activity, permissionsNeedRequest.toArray(permissionArray), requestCode);
            }
        }
    }

    private void clear() {
        mCallbackMaps.clear();
        permissionDesc.clear();
        needRequestPermissions.clear();
        mGrantResults.clear();
        currentPermissionPos = 0;
    }

    public void requestPermissions(String[] permissions, String[] descs, int requestCode, PermissionCallback onPermissionCallback) {
        requestPermissions(L.getActivity(), permissions, descs, requestCode, onPermissionCallback);
    }

    public void requestPermissions(Context context, String[] permissions, String[] descs, int requestCode, PermissionCallback onPermissionCallback) {
        this.mContext = context;
        clear();
        mCallbackMaps.put(requestCode, onPermissionCallback);
        List<String> permissionsNeedRequest = new ArrayList<>();
        for (int i = 0; i < permissions.length; i++) {
            String permission = permissions[i];
            if (!checkPermission(permission) && !SpUtils.get(L.getActivity()).getBoolean("sq_" + permission, false)) {
                permissionsNeedRequest.add(permission);
                this.permissionDesc.add(descs[i]);
            }
        }

        if (permissionsNeedRequest.size() > 0) {
            this.needRequestPermissions = permissionsNeedRequest;
            this.currentRequestCode = requestCode;
            this.currentPermissionCallback = onPermissionCallback;
            requestPermission(currentPermissionPos);
        } else {
            PermissionCallback callback = mCallbackMaps.get(requestCode);
            if (callback != null) {
                int[] resultGrants = new int[permissions.length];
                for (int i = 0; i < permissions.length; i++) {
                    int grant = ContextCompat.checkSelfPermission(context, permissions[i]);
                    resultGrants[i] = grant;
                }
                callback.onRequestPermissionsResult(permissions, resultGrants);
            } else {
                LogUtil.d("权限回调==null： " + requestCode);
            }
        }
    }

    private void requestPermissions(String permission, String desc, int requestCode, PermissionCallback onPermissionCallback) {
        if (mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            permissionDescDialog = new PermissionDescDialog(mContext);
            permissionDescDialog.setDesc(desc);
            permissionDescDialog.show();
            currentPermission = permission;
            ActivityCompat.requestPermissions(activity, new String[]{permission}, requestCode);
        }

    }

    private void requestPermission(int permissionPos) {
        requestPermissions(needRequestPermissions.get(permissionPos), permissionDesc.get(permissionPos), currentRequestCode, currentPermissionCallback);

    }

    /**
     * 检测权限
     *
     * @param permissions
     * @return
     */
    public boolean checkPermissions(String[] permissions) {
        for (String permission : permissions) {
            if (!checkPermission(permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 拉起权限提示框
     *
     * @param context
     * @param message
     */
    public void showPermissionDialog(final Context context, String message) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context).setTitle("权限申请").setMessage(message + ",我们保证权限获取仅用于必要功能").setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                Uri packageURI = Uri.parse("package:" + context.getPackageName());
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
                ((Activity) context).startActivityForResult(intent, SETTING_REQUEST_CODE);
                dialogInterface.dismiss();
            }
        });
        AlertDialog dialog = builder.create();
        dialog.setCancelable(false);
        dialog.show();
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (grantResults.length > 0) {
            for (int grantResult : grantResults) {
                mGrantResults.add(grantResult);
            }
        }
        SpUtils.get(L.getActivity()).put("sq_" + currentPermission, true);
        if (permissionDescDialog != null) {
            permissionDescDialog.dismiss();
        }
        currentPermissionPos++;
        if (currentPermissionPos < needRequestPermissions.size()) {
            requestPermission(currentPermissionPos);
        } else {
            PermissionCallback callback = mCallbackMaps.get(requestCode);
            if (callback != null) {
                LogUtil.d("权限回调： " + requestCode);
                int[] finalGrants = new int[mGrantResults.size()];
                for (int i = 0; i < mGrantResults.size(); i++) {
                    finalGrants[i] = mGrantResults.get(i);
                }
                callback.onRequestPermissionsResult(needRequestPermissions.toArray(new String[0]), finalGrants);
            } else {
                LogUtil.d("权限回调==null： " + requestCode);
            }
        }

    }

    public void showPermissionGuideDialog(Activity activity, String[] permissions, String message, OnPermissionPageCallback callback) {
        MessageDialog messageDialog = new MessageDialog(activity)
            .setDialogTitle("授权提醒")
            .setDialogMessage(message)
            .setDialogCancel("")
            .setDialogListener(dialog -> {

                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, APPLICATION_DETAILS_SETTINGS_REQUEST_CODE);

                EventDispatcher.getInstance().addActivityResultListener(new ActivityResultListener() {
                    @Override
                    public void onResult(OnActivityResultEvent onActivityResultEvent) {
                        if (onActivityResultEvent.getRequestCode() != APPLICATION_DETAILS_SETTINGS_REQUEST_CODE) {
                            return;
                        }

                        EventDispatcher.getInstance().removeActivityResultListener(this);

                        boolean allGrantPermission = true;
                        for (String permission : permissions) {
                            if (ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_DENIED) {
                                allGrantPermission = false;
                                break;
                            }
                        }

                        if (allGrantPermission) {
                            callback.onGranted();
                        } else {
                            callback.onDenied();
                        }
                    }
                });
            });
        messageDialog.setCancelable(false);
        messageDialog.show();
    }

    public interface PermissionCallback {
        void onRequestPermissionsResult(String[] permissions, int[] grantResults);
    }

    public interface OnPermissionPageCallback {
        /**
         * 权限已经授予
         */
        void onGranted();

        /**
         * 权限已经拒绝
         */
        default void onDenied() {}
    }
}