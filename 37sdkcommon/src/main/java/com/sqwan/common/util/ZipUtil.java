package com.sqwan.common.util;

import android.content.Context;

import com.sq.sdk.tool.util.EncodeUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;

/**
 * <AUTHOR>
 * @date 2020-05-11
 */
public class ZipUtil {


    public static String enZip(String key,String content) {
        try {
            return EncodeUtil.encode(key, content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String unZip(String key,String content) {
        try {
            return EncodeUtil.decode(key, content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String sqEnZip(Context context, String content) {
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        String key = config.getPartner() + config.getGameid() + ConfigManager.getInstance(context).getAppKey();
        return enZip(key, content);
    }

    public static String sqUnZip(Context context,String content){
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        String key = config.getPartner() + config.getGameid() + ConfigManager.getInstance(context).getAppKey();
        return unZip(key, content);
    }


}
