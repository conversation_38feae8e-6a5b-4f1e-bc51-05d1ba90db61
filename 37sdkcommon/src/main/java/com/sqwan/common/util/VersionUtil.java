package com.sqwan.common.util;

import android.content.Context;

import com.sq.data.BuildConfig;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.msdk.config.MultiSdkManager;

/**
 * <AUTHOR>
 * @date 2020/2/25
 */
public class VersionUtil {


    /**
     * 业务版本号, 记录在此文档:
     * https://vulenhv772.feishu.cn/sheets/shtcnJwsDnDwKx8HoqzSxFIy4og
     */
    public static final String gwversion = BuildConfig.gwVersion;

    public static String original_version = getSdkVersion() ;

    public static String getVersionStr(Context context) {
        //如果是非插件版本，获取到的插件版本是-1，不显示出来
        int pluginVersion = ModHelper.getPluginMod().getPluginVersion();
        if (pluginVersion >= 0) {
            return sdkVersion + "_" + gwversion + "_" + getPluginVersion(context);
        } else {
            return sdkVersion + "_" + gwversion;
        }
    }

    /**
     * 为了兼容官斗的版本
     *
     * @return
     */
    public static String getSdkVersion() {
        if ("gds".equals(MultiSdkManager.getInstance().getScut3())) {
            return BuildConfig.gdsLibraryVersion;
        } else {
            return BuildConfig.sqLibraryVersion;
        }
    }

    public static String sdkVersion = getSdkVersion();

    /**
     * 获取插件版本号
     *
     * @param context
     * @return
     */
    public static int getPluginVersion(Context context) {
        return ModHelper.getPluginMod().getPluginVersion();
    }

    public static void setOriginalVersion(String originalVersion) {
        original_version = originalVersion;
    }


    public static String getOriginalVersion() {
        return original_version;
    }

}
