package com.sqwan.common.util;

import android.content.Context;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class AssetsUtils {

    public static final String SQ_MULTI_CONFIG = "multiconfig";//multiconfig文件

    public static final String PRO_SHOW_SCREENSHOT = "isShowScreenshot";//显示截图选项
    public static final String PRO_SHOW_SPLASH_WHEN_SCUT = "isShowSplashWhenSCut";//简版下是否显示闪屏

    /**
     * 取assets下的配置参数
     *
     * @param context
     * @param file
     * @return
     */
    public static Properties readProperties(Context context, String file) {
        Properties p = null;
        try {
            InputStream in = context.getResources().getAssets().open(file);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            p = null;
        }
        return p;
    }
}
