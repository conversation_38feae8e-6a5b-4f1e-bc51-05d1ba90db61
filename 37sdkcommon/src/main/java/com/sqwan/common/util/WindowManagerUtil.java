package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-06-10 11:28
 */
public class WindowManagerUtil {
    public static void handleNotch(Context activity,boolean useshort_edges){
        if (activity instanceof Activity) {
            Activity _activity = (Activity) activity;
            Window window = _activity.getWindow();
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                if (useshort_edges) {
                layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                }
                window.setAttributes(layoutParams);
            }
        }

    }
    public static void handleHideSystemUI(Context activity){
        if (activity instanceof Activity) {
            Activity _activity = (Activity) activity;
            final Window window = _activity.getWindow();
            StatusBarUtil.hideSystemUI(window);
            window.getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    StatusBarUtil.hideSystemUI(window);
                }
            });
        }
    }
}
