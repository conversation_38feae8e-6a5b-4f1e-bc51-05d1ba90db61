package com.sqwan.common.util;

import android.content.Context;

import java.lang.reflect.Method;

public class SqResUtils {

    private static final String TYPE_ID = "id";
    private static final String TYPE_LAYOUT = "layout";
    private static final String TYPE_STRING = "string";
    private static final String TYPE_DRAWABLE = "drawable";
    private static final String TYPE_DIMEN = "dimen";
    private static final String TYPE_COLOR = "color";
    private static final String TYPE_STYLE = "style";
    private static final String TYPE_ANIM = "anim";
    private static final String TYPE_MENU = "menu";
    private static final String TYPE_ATTR = "attr";

    public static int getAttr(Context context,String resName){
        return getIdentifier(context,resName,TYPE_ATTR,context.getPackageName());

    }
    /**
     * 获取资源id
     *
     * @param context
     * @param resName
     * @return
     */
    public static int getId(Context context, String resName) {
        return getIdentifier(context, resName, TYPE_ID, context.getPackageName());
    }

    /**
     * @param context
     * @param layoutName
     * @return
     */
    public static int getLayoutId(Context context, String layoutName) {
        return getIdentifier(context, layoutName, TYPE_LAYOUT, context.getPackageName());
    }

    /**
     * 获取String资源id
     *
     * @param context
     * @param stringName
     * @return
     */
    public static int getStringId(Context context, String stringName) {
        return getIdentifier(context, stringName, TYPE_STRING, context.getPackageName());
    }

    /**
     * 获取String 文本
     *
     * @param context
     * @param stringName
     * @return
     */
    public static String getStringByName(Context context, String stringName) {
        return context.getString(getStringId(context, stringName));
    }

    /**
     * 获取drawable资源id
     *
     * @param context
     * @param drawableName
     * @return
     */
    public static int getDrawableId(Context context, String drawableName) {
        return getIdentifier(context, drawableName, TYPE_DRAWABLE, context.getPackageName());
    }

    /**
     * 获取color资源id
     *
     * @param context
     * @param colorName
     * @return
     */
    public static int getColorId(Context context, String colorName) {
        return getIdentifier(context, colorName, TYPE_COLOR, context.getPackageName());
    }

    /**
     * 通过资源获取color值
     *
     * @param context
     * @param colorName
     * @return
     */
    public static int getColorByName(Context context, String colorName) {
        return context.getResources().getColor(getColorId(context, colorName));
    }

    /**
     * 获取dimen资源id
     *
     * @param context
     * @param dimenName
     * @return
     */
    public static int getDimenId(Context context, String dimenName) {
        return getIdentifier(context, dimenName, TYPE_DIMEN, context.getPackageName());
    }

    /**
     * 获取style资源id
     *
     * @param context
     * @param themeName
     * @return
     */
    public static int getStyleId(Context context, String themeName) {
        return getIdentifier(context, themeName, TYPE_STYLE, context.getPackageName());
    }

    /**
     * 获取资源文件ainm的id
     *
     * @param context
     * @param resName
     * @return
     */
    public static int getAnimId(Context context, String resName) {

        return getIdentifier(context, resName, TYPE_ANIM, context.getPackageName());
    }

    /**
     * 获取资源文件menu的id
     */
    public static int getMenuId(Context context, String resName) {
        return getIdentifier(context, resName, TYPE_MENU, context.getPackageName());
    }

    public static int getIdByName(String name, String type, Context context) {
        int id = 0x0;
        try {
            id = getIdentifier(context, name, type, context.getPackageName());
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    public static int getIdByNameHostFirst(String name, String type, Context context) {
        int id = 0x0;
        try {
            id = getIdentifierHostFirst(context, name, type, context.getPackageName());
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    private static int getIdentifierHostFirst(Context context, String name, String type, String pkgName) {
        int id = context.getResources().getIdentifier(name, type, pkgName);
        if (id != -1) {
            return id;
        }
        return getIdentifierFromPlugin(context, name, type);
    }

    private static int getIdentifier(Context context, String name, String type, String pkgName) {
        int id = getIdentifierFromPlugin(context, name, type);
        if (id != -1) {
            return id;
        }
        return context.getResources().getIdentifier(name, type, pkgName);
    }

    private static Class MixResourceClass = null;

    private static int getIdentifierFromPlugin(Context context, String name, String defType) {
        try {
            if (MixResourceClass == null) {
                MixResourceClass = context.getClassLoader().loadClass("com.plugin.core.resources.MixResources");
            }
            if (MixResourceClass == null) {
                return -1;
            }
            Method method = MixResourceClass.getMethod("getIdentifierFromPlugin", String.class, String.class);
            return (int) method.invoke(context.getResources(), name, defType);
        } catch (Exception e) {
            return -1;
        }
    }

    public static int getDimensionPixelSize(Context context, String dimenName) {
        return (int) context.getResources().getDimension(SqResUtils.getDimenId(context, dimenName));

    }


}
