package com.sqwan.common.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.support.v4.app.ActivityCompat;
import android.telephony.TelephonyManager;

import com.sq.tools.manager.SensitiveInfoManager;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-31 16:30
 */
public class TelephonyInfoUtils {
    public static String getDeviceId(Context context){
        return SensitiveInfoManager.getInstance().getIMEI(context);
    }
    public static String getLine1Number(Context context){
        return SensitiveInfoManager.getInstance().getPhoneNumber(context);
    }
    public static String getSimSerialNumber(Context context){
        if(ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String getSimSerialNumber = telephonyManager.getSimSerialNumber();
            return getSimSerialNumber;
        }
        return "";
    }
    public static String getSubscriberId(Context context){
        return SensitiveInfoManager.getInstance().getIMSI(context);
    }
    /**
     * sim 卡状态
     * @param context
     * @return
     */
    public static int getSimState(Context context) {
        if(ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if(tm != null) {
                return tm.getSimState();
            }
        }
        return -1;
    }
    /**
     * SIM卡是否可用
     */
    public static boolean isSIMCardAvailable(Context context) {
        if(ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
            try {
                TelephonyManager mgr = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                return TelephonyManager.SIM_STATE_READY == mgr.getSimState();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return false;
        }
        return false;
    }
    public static String getSettingAndroidId(Context context){
        return getSettingAndroidId(context,true);
    }
    public static String getSettingAndroidId(Context context,boolean isAuthCheck){
        try {
            if (isAuthCheck) {
                return Settings.System.getString(context.getContentResolver(), Settings.System.ANDROID_ID);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";

    }

}
