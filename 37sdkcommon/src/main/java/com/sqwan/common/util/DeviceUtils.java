package com.sqwan.common.util;

import android.annotation.TargetApi;
import android.app.ActivityManager;
import android.bluetooth.BluetoothAdapter;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.Uri;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.os.SystemClock;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;

import com.sq.tools.Logger;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/19
 * <p>
 * 采集设备信息工具类
 */
public class DeviceUtils {

    private static final String DEFAULT_MAC = "02:00:00:00:00:00";


    private static final String HARMONY = "harmony";
    private static final String ANDROID = "android";

    public static String getOs() {
        return isHarmony() ? HARMONY : ANDROID;
    }

    private static final String IDENTIFY = "identify";

    private static final String SQ_PREFS = "sq_prefs";


    public static boolean isHarmony() {
        try {
            Class.forName("ohos.app.Application");
            return true;
        } catch (Throwable ignored) {
        }

        try {
            Class.forName("ohos.system.version.SystemVersion");
            return true;
        } catch (Throwable ignored) {
        }

        try {
            Class<?> clz = Class.forName("com.huawei.system.BuildEx");
            Method method = clz.getMethod("getOsBrand");
            ClassLoader classLoader = clz.getClassLoader();
            if (classLoader != null && classLoader.getParent() != null) {
                return HARMONY.equals(method.invoke(clz));
            }
        } catch (Throwable ignore) {
        }

        return false;
    }

    public static String getMac(Context context) {
        return MacLogic.getInstance(context).getValue();
    }

    private static String getBlueToothAddress() {
        String address = "02:00:00:00:00:00";
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        //6.0及以前
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
            address = bluetoothAdapter.getAddress();
        } else {
            try {
                Field field = bluetoothAdapter.getClass().getDeclaredField("mService");
                field.setAccessible(true);
                Object bluetoothManagerService = field.get(bluetoothAdapter);
                if (bluetoothManagerService == null) {
                    return null;
                }
                Method method = bluetoothManagerService.getClass().getMethod("getAddress");
                Object obj = method.invoke(bluetoothManagerService);
                if (obj != null && obj instanceof String) {
                    address = (String) obj;
                }
            } catch (Exception e) {

            }
        }
        return address;
    }


    /**
     * 获取屏幕亮度
     *
     * @param context
     * @return
     */
    public static int getScreenBrightness(Context context) {
        int brightness = 0;
        ContentResolver contentResolver = context.getContentResolver();
        try {
            brightness = Settings.System.getInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        return brightness;
    }

    /**
     * @return 手机系统上次更新的时间
     */
    public static long getBuildTime() {
        return Build.TIME;
    }

    /**
     * sdk 版本
     *
     * @return
     */
    public static String getDeviceSDKVersion() {
        return Build.VERSION.SDK_INT + "";
    }

    /**
     * targetSdkVersion 版本
     *
     * @return
     */
    public static String getTargetVersion(Context context) {
        return context.getApplicationInfo().targetSdkVersion + "";
    }

    /**
     * cpu 核心数
     *
     * @return
     */
    public static int availableProcessors() {
        return Runtime.getRuntime().availableProcessors();
    }

    //是否首次启动
    private static boolean isFirstLaunch;
    //是否已经判断过首次启动了
    private static boolean valuatedLaunch;

    /**
     * 是否第一次启动
     *
     * @return
     */
    public static boolean isFirstLaunch(Context context) {
        if (valuatedLaunch) {
            return isFirstLaunch;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences("sq_prefs", Context.MODE_PRIVATE);
        isFirstLaunch = sharedPreferences.getBoolean("is_first_launch", true);
        if (isFirstLaunch) {
            sharedPreferences.edit().putBoolean("is_first_launch", false).apply();
        }
        valuatedLaunch = true;
        return isFirstLaunch;
    }

    /**
     * 是否首次调起登录
     *
     * @param context 上下文
     * @return
     */
    public static boolean isFirstInvokeLogin(Context context) {
        return SpUtils.get(context).getBoolean("is_first_invoke_login", true);
    }

    public static void setLoginInvoked(Context context) {
        SpUtils.get(context).put("is_first_invoke_login", false);
    }


    /**
     * 获取出入法列表
     *
     * @return
     */
    public static List<String> getInputMethodList(Context context) {
        List<String> result = new ArrayList<>();
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            List<InputMethodInfo> methodList = imm.getInputMethodList();
            for (InputMethodInfo mi : methodList) {
                String name = mi.loadLabel(context.getPackageManager()).toString();
                result.add(name);
            }
        }
        return result;
    }

    /**
     * 总内存
     *
     * @param context
     * @return
     */
    public static long getRamTotal(Context context) {
        long result = 0;
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        if (am != null) {
            am.getMemoryInfo(memoryInfo);
            result = memoryInfo.totalMem;
        }
        return result;
    }


    /**
     * 所有传感器列表
     *
     * @param context
     * @return
     */
    public static List<String> sensorList(Context context) {
        return SensitiveInfoManager.getInstance().getSensorList(context);
    }

    /**
     * 设备是否开启调试模式
     *
     * @param context
     * @return
     */
    public static boolean enableAdb(Context context) {
        return Settings.Secure.getInt(context.getContentResolver(), Settings.Global.ADB_ENABLED, 0) > 0;
    }

    /**
     * sim 卡状态
     *
     * @param context
     * @return
     */
    public static int simState(Context context) {
        return TelephonyInfoUtils.getSimState(context);
    }

    /**
     * 获取国家和语言
     *
     * @return
     */
    public static String localeInfo() {
        return Locale.getDefault().getCountry() + "_" + Locale.getDefault().getLanguage();
    }

    /**
     * 是否连接代理
     *
     * @param context
     * @return
     */
    private static boolean isWifiProxy(Context context) {
        try {
            String proxyAddress;
            int proxyPort;
            proxyAddress = System.getProperty("http.proxyHost");
            String portStr = System.getProperty("http.proxyPort");
            proxyPort = Integer.parseInt((portStr != null ? portStr : "-1"));
            return (!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1);
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * SIM卡是否可用
     */
    public static boolean isSIMCardAvailable(Context context) {
        return TelephonyInfoUtils.isSIMCardAvailable(context);
    }

    /**
     * 判断是否有root权限
     *
     * @return
     */
    public static boolean isRoot() {
        String binPath = "/system/bin/su";
        String xBinPath = "/system/xbin/su";
        return new File(binPath).exists() && isExecutable(binPath) || new File(xBinPath).exists() && isExecutable(xBinPath);
    }

    private static boolean isExecutable(String filePath) {
        Process p = null;
        try {
            p = Runtime.getRuntime().exec("ls -l " + filePath);
            BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String str = in.readLine();
            if (str != null && str.length() >= 4) {
                char flag = str.charAt(3);
                if (flag == 's' || flag == 'x') {
                    return true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (p != null) {
                p.destroy();
            }
        }
        return false;
    }

    /**
     * 获取当前电量的百分比，如果是 40%，则返回40
     *
     * @param context
     * @return
     */
    public static int getBatteryLevel(Context context) {
        Intent intent = context.registerReceiver(null, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
        if (intent == null) {
            return 0;
        }
        return intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
    }

    /**
     * 获取当前电池的状态
     * battery_status  1:unknown, 2:charging, 3:discharging, 4:not_charging, 5:full
     *
     * @param context
     * @return
     */
    public static int getBatteryStatus(Context context) {
        Intent intent = context.registerReceiver(null, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
        if (intent == null) {
            return BatteryManager.BATTERY_STATUS_UNKNOWN;
        }
        return intent.getIntExtra(BatteryManager.EXTRA_STATUS, BatteryManager.BATTERY_STATUS_UNKNOWN);
    }

    /**
     * 获取WiFi的SSID 连接WiFi名
     *
     * @param context
     * @return
     */
    public static String getWifiSSID(Context context) {
        if (!isWifi(context)) {
            return "";
        }
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        String ssid = "";
        if (wifiManager != null) {
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            if (wifiInfo == null) {
                return ssid;
            }
            if (android.os.Build.VERSION.SDK_INT <= Build.VERSION_CODES.O) {
                ssid = wifiInfo.getSSID();
            } else {
                int networkId = wifiInfo.getNetworkId();
                List<WifiConfiguration> list = wifiManager.getConfiguredNetworks();
                if (list != null) {
                    for (WifiConfiguration wifiConfig : list) {
                        if (wifiConfig.networkId == networkId) {
                            ssid = wifiConfig.SSID;
                            break;
                        }
                    }
                }
            }
        }
        if (ssid.contains("\"")) {
            ssid = ssid.replaceAll("\"", "");
        }
        return ssid;
    }

    /**
     * 获取WiFi的BSSID
     *
     * @param context
     * @return
     */
    public static String getWifiBSSID(Context context) {
        if (!isWifi(context)) {
            return "";
        }
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager != null) {
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            if (wifiInfo != null) {
                return wifiInfo.getBSSID();
            }
        }
        return "";
    }

    /**
     * 判断当前网络是否为WiFi
     *
     * @param mContext
     * @return
     */
    private static boolean isWifi(Context mContext) {
        ConnectivityManager connectivityManager = (ConnectivityManager) mContext
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = connectivityManager.getActiveNetworkInfo();
        return info != null && info.getType() == ConnectivityManager.TYPE_WIFI;
    }


    /**
     * 获取当前设备系统版本
     */
    public static String getOSVersion() {
        return Build.VERSION.RELEASE;
    }

    /**
     * 获取国家
     */
    public static String getCountry() {
        return Locale.getDefault().getCountry();
    }

    /**
     * 获取国家代码
     */
    public static String getCountryCode() {
        return "";
    }

    /**
     * 获取当前网络类型
     *
     * @return
     */
    public static String getNetWorkType(Context context) {
        return NetWorkUtils.getNetworkType(context);
    }

    /**
     * 获取ip地址
     *
     * @return
     */
    public static String getIpAddress(Context context) {
        return NetWorkUtils.getIpAddress(context);
    }

    /**
     * 获取手机品牌
     */
    public static String getBrand() {
        return Build.BRAND;
    }

    /**
     * 获取手机型号
     */
    public static String getModel() {
        return Build.MODEL;
    }

    /**
     * 屏幕属性(宽度、高度、像素密度)
     *
     * @param context
     * @return
     */
    public static Map<String, String> getDisplayMetrics(Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();
        Map<String, String> display = new HashMap<>();
        if (wm != null) {
            wm.getDefaultDisplay().getRealMetrics(dm);
            display.put("height", dm.heightPixels + "");
            display.put("width", dm.widthPixels + "");
            display.put("dpi", dm.densityDpi + "");
        }
        return display;
    }


    //获取手机ram大小 单位kb
    public static String getTotalRam() {
        String path = "/proc/meminfo";
        String firstLine = null;
        try {
            FileReader fileReader = new FileReader(path);
            BufferedReader br = new BufferedReader(fileReader, 8192);
            firstLine = br.readLine().split("\\s+")[1];
            br.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return firstLine;
//        if(firstLine != null){
//            totalRam = (int)Math.ceil((new Float(Float.valueOf(firstLine) / (1024 * 1024)).doubleValue()));
//        }
//
//        return totalRam + "GB";//返回1GB/2GB/3GB/4GB
    }


    /**
     * 判断SD卡是否可用
     *
     * @return true : 可用<br>false : 不可用
     */
    public static boolean isSDCardEnable() {
        return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState());
    }

    /**
     * 获取手机外部总空间大小
     *
     * @return 总大小，字节为单位
     */
    static public long getTotalExternalMemorySize() {
        try {
            if (isSDCardEnable()) {
                //获取SDCard根目录
                File path = Environment.getExternalStorageDirectory();
                StatFs stat = new StatFs(path.getPath());
                long blockSize = stat.getBlockSize();
                long totalBlocks = stat.getBlockCount();
                return totalBlocks * blockSize;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }


    /**
     * cpu 核心数
     *
     * @return
     */
    public static int getCpuCore() {
        try {
            return Runtime.getRuntime().availableProcessors();
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }

    }


    /**
     * 获取CPU型号
     *
     * @return
     */
    public static String getCpuHardware() {

        String str1 = "/proc/cpuinfo";
        String str2 = "";

        try {
            FileReader fr = new FileReader(str1);
            BufferedReader localBufferedReader = new BufferedReader(fr);
            while ((str2 = localBufferedReader.readLine()) != null) {
                if (str2.contains("Hardware")) {
                    return str2.split(":")[1];
                }
            }
            localBufferedReader.close();
        } catch (IOException e) {
        }
        return null;

    }


    // 获取CPU最大频率（单位KHZ）

    // "/system/bin/cat" 命令行

    // "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq" 存储最大频率的文件的路径

    private static String sMaxCpuFreq;

    public static String getMaxCpuFreq() {
        if (sMaxCpuFreq != null) {
            return sMaxCpuFreq;
        }
        String result = "";
        ProcessBuilder cmd;
        try {
            String[] args = {"/system/bin/cat",
                    "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq"};
            cmd = new ProcessBuilder(args);
            Process process = cmd.start();
            InputStream in = process.getInputStream();
            byte[] re = new byte[24];
            while (in.read(re) != -1) {
                result = result + new String(re);
            }
            in.close();
        } catch (IOException ex) {
            ex.printStackTrace();
            result = "N/A";
        }
        sMaxCpuFreq = result.trim();
        return sMaxCpuFreq;
    }

    // 获取CPU最小频率（单位KHZ）
    public static String getMinCpuFreq() {
        String result = "";
        ProcessBuilder cmd;
        try {
            String[] args = {"/system/bin/cat",
                    "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_min_freq"};
            cmd = new ProcessBuilder(args);
            Process process = cmd.start();
            InputStream in = process.getInputStream();
            byte[] re = new byte[24];
            while (in.read(re) != -1) {
                result = result + new String(re);
            }
            in.close();
        } catch (IOException ex) {
            ex.printStackTrace();
            result = "N/A";
        }
        return result.trim();
    }

    // 实时获取CPU当前频率（单位KHZ）
    public static String getCurCpuFreq() {
        String result = "N/A";
        try {
            FileReader fr = new FileReader(
                    "/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq");
            BufferedReader br = new BufferedReader(fr);
            String text = br.readLine();
            result = text.trim();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    // 获取CPU名字
    public static String getCpuName() {
        try {
            FileReader fr = new FileReader("/proc/cpuinfo");
            BufferedReader br = new BufferedReader(fr);
            String text = br.readLine();
            String[] array = text.split(":\\s+", 2);
            return array[1];
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取imei
     *
     * @param context
     * @return
     */
    public static String getIMEI(Context context) {
        return ImeiLogic.getInstance(context).getValue();
    }

    /**
     * 获取dev
     */
    public static String getDev(Context context) {
        return DevLogic.getInstance(context).getValue();
    }


    /**
     * 获取oaid
     */
    public static String getOaid(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString(IDENTIFY, "");
    }

    /**
     * 获取vaid
     */
    public static String getVaid(Context context) {
        return "";
    }

    /**
     * 获取aaid
     */
    public static String getAaid(Context context) {
        return "";
    }

    /**
     * 获取imsi
     */
    public static String getIMSI(Context context) {
        return TelephonyInfoUtils.getSubscriberId(context);
    }

    /**
     * 获取Android id
     *
     * @param context
     * @return
     */
    public static String getAndroidId(Context context) {
        return SensitiveInfoManager.getInstance().getAndroidId(context);
    }

    /**
     * 获取系统启动时间
     * 当前时间-启动时长，单位 毫秒
     *
     * @return
     */
    public static long getBootTime() {
        return System.currentTimeMillis() - SystemClock.elapsedRealtime();
    }

    /**
     * 获取sim卡数量
     *
     * @param context
     * @return
     */
    @TargetApi(Build.VERSION_CODES.M)
    public static String getSIM(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                TelephonyManager tm = (TelephonyManager) (context).getSystemService(Context.TELEPHONY_SERVICE);
                int phoneCount = tm.getPhoneCount(); //获取SIM卡槽数量
                return phoneCount + "";
            } catch (Exception e) {
                e.printStackTrace();
                return "";
            }
        } else {
            return "";
        }

    }

    /**
     * 根据CPU是否为电脑来判断是否为模拟器
     * 返回:true 为模拟器
     */
    public static boolean isSimulator(Context context) {
        try {
            boolean checkProperty = Build.FINGERPRINT.startsWith("generic")
                    || Build.FINGERPRINT.toLowerCase().contains("vbox")
                    || Build.FINGERPRINT.toLowerCase().contains("test-keys")
                    || Build.MODEL.contains("google_sdk")
                    || Build.MODEL.contains("Emulator")
                    || Build.MODEL.contains("Android SDK built for x86")
                    || Build.MANUFACTURER.contains("Genymotion")
                    || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                    || "google_sdk".equals(Build.PRODUCT);
            if (checkProperty) return true;

            String operatorName = "";
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (tm != null) {
                String name = tm.getNetworkOperatorName();
                if (name != null) {
                    operatorName = name;
                }
            }
            boolean checkOperatorName = operatorName.equalsIgnoreCase("android");
            if (checkOperatorName) return true;

            String url = "tel:" + "123456";
            Intent intent = new Intent();
            intent.setData(Uri.parse(url));
            intent.setAction(Intent.ACTION_DIAL);
            boolean checkDial = intent.resolveActivity(context.getPackageManager()) == null;
            if (checkDial) return true;
            String cpuInfo = readCpuInfo();
            if ((cpuInfo.contains("intel") || cpuInfo.contains("amd"))) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static String cpuInfo = "";
    private static boolean hasReadCpu;

    public static String readCpuInfo() {
        if (hasReadCpu) {
            return cpuInfo;
        }
        String result = "";
        try {
            String[] args = {"/system/bin/cat", "/proc/cpuinfo"};
            ProcessBuilder cmd = new ProcessBuilder(args);
            Process process = cmd.start();
            StringBuilder sb = new StringBuilder();
            String readLine = "";
            BufferedReader responseReader = new BufferedReader(new InputStreamReader(process.getInputStream(), "utf-8"));
            while ((readLine = responseReader.readLine()) != null) {
                sb.append(readLine);
            }
            responseReader.close();
            result = sb.toString().toLowerCase();
        } catch (IOException ex) {
            Logger.info("readCpuInfo " + ex.toString());
        }
        hasReadCpu = true;
        cpuInfo = result;
        return result;
    }

    private static boolean isRoot;
    private static boolean hasReadRoot;

    /**
     * 是否是root设备
     *
     * @return
     */
    public static boolean isRootDevice() {
        if (hasReadRoot) {
            return isRoot;
        }
        String[] strArr = {"/system/xbin/", "/system/bin/", "/system/sbin/", "/sbin/", "/vendor/bin/", "/su/bin/"};
        try {
            int length = strArr.length;
            for (int i = 0; i < length; i++) {
                String str = strArr[i] + "su";
                if (new File(str).exists()) {
                    String fileInfo = readFileInfo(new String[]{"ls", "-l", str});
                    isRoot = !TextUtils.isEmpty(fileInfo) && fileInfo.indexOf("root") != fileInfo.lastIndexOf("root");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        hasReadRoot = true;
        return isRoot;
    }

    /**
     * 读取文件信息
     *
     * @param strArr
     * @return
     */
    private static String readFileInfo(String[] strArr) {
        StringBuilder sb = new StringBuilder();
        try {
            Process start = new ProcessBuilder(strArr).start();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(start.getInputStream()));
            while (true) {
                String readLine = bufferedReader.readLine();
                if (readLine == null) {
                    break;
                }
                sb.append(readLine);
            }
            start.getInputStream().close();
            start.destroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    // 获取运营商信息
    public static String getCarrier(Context context) {
        final Map<String, String> carrierMap = new HashMap<String, String>() {
            {
                put("46000", "中国移动");
                put("46002", "中国移动");
                put("46007", "中国移动");
                put("46008", "中国移动");

                put("46001", "中国联通");
                put("46006", "中国联通");
                put("46009", "中国联通");

                put("46003", "中国电信");
                put("46005", "中国电信");
                put("46011", "中国电信");

                put("46004", "中国卫通");

                put("46020", "中国铁通");

            }
        };

        try {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            String simOperator = tm.getSimOperator();
            if (!TextUtils.isEmpty(simOperator) && carrierMap.containsKey(simOperator)) {
                return carrierMap.get(simOperator);
            }

            String simOperatorName = tm.getSimOperatorName();
            if (!TextUtils.isEmpty(simOperatorName)) {
                return simOperatorName;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }


    /**
     *
     */
    public static void printInfo(Context context) {
        String phoneInfo = "产品Product: " + android.os.Build.PRODUCT + "\n";
        phoneInfo += ", CPU_ABI: " + android.os.Build.CPU_ABI + "\n";
        phoneInfo += ", CPU_ABI2: " + android.os.Build.CPU_ABI2 + "\n";
        phoneInfo += ", 标签TAGS: " + android.os.Build.TAGS + "\n";
        phoneInfo += ", VERSION_CODES.BASE: " + android.os.Build.VERSION_CODES.BASE + "\n";
        phoneInfo += ", 型号MODEL: " + android.os.Build.MODEL + "\n";
        phoneInfo += ", Android 版本 VERSION.RELEASE: " + android.os.Build.VERSION.RELEASE + "\n";
        phoneInfo += ", 驱动 DEVICE: " + android.os.Build.DEVICE + "\n";
        phoneInfo += ", DISPLAY: " + android.os.Build.DISPLAY + "\n";
        phoneInfo += ", 品牌 BRAND: " + android.os.Build.BRAND + "\n";
        phoneInfo += ", 基板 BOARD: " + android.os.Build.BOARD + "\n";
        phoneInfo += ", 设备标识 FINGERPRINT: " + android.os.Build.FINGERPRINT + "\n";
        phoneInfo += ", 版本号 ID: " + android.os.Build.ID + "\n";
        phoneInfo += ", 制造商 MANUFACTURER: " + android.os.Build.MANUFACTURER + "\n";
        phoneInfo += ", 用户 USER: " + android.os.Build.USER + "\n";
        phoneInfo += ", BOOTLOADER: " + android.os.Build.BOOTLOADER + "\n";
        phoneInfo += ", 主机地址 :" + android.os.Build.HOST + "\n";
        phoneInfo += ", 硬件 HARDWARE: " + android.os.Build.HARDWARE + "\n";
        phoneInfo += ", INCREMENTAL: " + android.os.Build.VERSION.INCREMENTAL + "\n";
        phoneInfo += ", CODENAME: " + android.os.Build.VERSION.CODENAME + "\n";
        phoneInfo += ", SDK: " + android.os.Build.VERSION.SDK_INT + "\n";

        phoneInfo += ", Android id: " + getAndroidId(context) + "\n";
        phoneInfo += ", 手机启动时间: " + getBootTime() + "\n";
        phoneInfo += ", 手机屏幕亮度: " + getScreenBrightness(context) + "\n";
        phoneInfo += ", cpu 核心数: " + availableProcessors() + "\n";
        phoneInfo += ", 是否第一次启动: " + isFirstLaunch(context) + "\n";
        phoneInfo += ", 输入法列表: " + getInputMethodList(context) + "\n";
        phoneInfo += ", 总内存: " + getRamTotal(context) + "\n";
        phoneInfo += ", 屏幕属性: " + getDisplayMetrics(context).toString() + "\n";
        phoneInfo += ", 传感器列表: " + sensorList(context) + "\n";
        phoneInfo += ", 设备是否开启调试模式: " + enableAdb(context) + "\n";
        phoneInfo += ", sim卡状态: " + simState(context) + "\n";
        phoneInfo += ", 国家和语言: " + localeInfo() + "\n";
        phoneInfo += ", 是否连接代理: " + isWifiProxy(context) + "\n";
        phoneInfo += ", 是否有root权限: " + isRoot() + "\n";

        LogUtil.i(phoneInfo);
    }


    public static String loadInfo(Context context) {
        Map<String, Object> infos = new HashMap<>();
        infos.put("product", Build.PRODUCT);
        infos.put("cpu_abi", Build.CPU_ABI);
        infos.put("cpu_abi2", Build.CPU_ABI2);
        infos.put("tags", Build.TAGS);
        infos.put("version_codes.base", Build.VERSION_CODES.BASE);
        infos.put("model", Build.MODEL);
        infos.put("version.release", Build.VERSION.RELEASE);
        infos.put("device", Build.DEVICE);
        infos.put("display", Build.DISPLAY);
        infos.put("brand", Build.BRAND);
        infos.put("board", Build.BOARD);
        infos.put("fingerprint", Build.FINGERPRINT);
        infos.put("id", Build.ID);
        infos.put("manufacturer", Build.MANUFACTURER);
        infos.put("user", Build.USER);
        infos.put("bootloader", Build.BOOTLOADER);
        infos.put("host", Build.HOST);
        infos.put("hardware", Build.HARDWARE);
        infos.put("incremental", Build.VERSION.INCREMENTAL);
        infos.put("codename", Build.VERSION.CODENAME);
        infos.put("sdk", Build.VERSION.SDK_INT);
        infos.put("adid", getAndroidId(context));
        infos.put("boot_time", getBootTime());
        infos.put("screen_brightness", getScreenBrightness(context));
        infos.put("cpu_count", availableProcessors());
        infos.put("is_first_launch", isFirstLaunch(context));
        infos.put("input_method_list", getInputMethodList(context));
        infos.put("ram_total", getRamTotal(context));
        infos.put("display_metrics", getDisplayMetrics(context));
        infos.put("sensor_list", sensorList(context));
        infos.put("enable_adb", enableAdb(context));
        infos.put("sim_state", simState(context));
        infos.put("country", Locale.getDefault().getCountry());
        infos.put("language", Locale.getDefault().getLanguage());
        infos.put("is_wifi_proxy", isWifiProxy(context));
        infos.put("is_root", isRoot());
        JSONObject jsonObject = new JSONObject(infos);
        return jsonObject.toString();
    }
}
