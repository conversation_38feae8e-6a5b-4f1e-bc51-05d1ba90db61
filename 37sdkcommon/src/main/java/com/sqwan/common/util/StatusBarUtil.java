package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2020/1/19
 */
public class StatusBarUtil {
    public static void hideSystemUI(Context context){
        if (context instanceof Activity) {
            hideSystemUI(((Activity)context).getWindow());
        }

    }
    public static void hideSystemUI(Window window) {
        if (window == null) {
            LogUtil.i("windon is null");
            return;
        }
        View decorView = window.getDecorView();
        hideSystemUI(decorView);
    }
    public static void hideSystemUI(View view){
        view.setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |  //用于全屏展示状态栏
                        View.SYSTEM_UI_FLAG_FULLSCREEN  //用于全屏展示状态栏
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION  //使得虚拟导航栏隐藏，用户可以从屏幕下边缘“拖出”且不会再次消失，同时activity界面会被挤压
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION  //效果使得导航栏出现的时候不会挤压activity高度，导航栏会覆盖在activity之上
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY //它被称作“粘性”的沉浸模式，这个模式会在状态栏和导航栏显示一段时间后，自动隐藏（你可以点击一下屏幕，立即隐藏）。同时需要重点说明的是，这种模式下，状态栏和导航栏出现的时候是“半透明”状态，
                        | View.SYSTEM_UI_FLAG_IMMERSIVE);

    }
    public static void hideSystemKeyBoard(Context context, View v) {
        InputMethodManager imm = (InputMethodManager) context
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
    }

    /**
     * 用于获取状态栏的高度。
     *
     * @return 返回状态栏高度的像素值。
     */
    public static int getStatusBarHeight(Context context) {

        int statusBarHeight = 0;
        if (statusBarHeight == 0) {
            try {
                Class<?> c = Class.forName("com.android.internal.R$dimen");
                Object o = c.newInstance();
                Field field = c.getField("status_bar_height");
                int x = (Integer) field.get(o);
                statusBarHeight = context.getResources().getDimensionPixelSize(x);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return statusBarHeight;
    }
}
