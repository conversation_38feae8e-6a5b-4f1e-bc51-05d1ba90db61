package com.sqwan.common.util;

import android.content.Context;

/**
 * 业务相关的工具类
 */
public class BusinessUtil {


    public static final String hasCheckUserAuthPermission = "hasCheckUserAuthPermission";

    /**
     * 业务工具类，返回当前用户是否同意隐私权限
     *
     * @return
     */
    public static boolean getAuthPermission(Context context) {
        if (context == null) {
            return false;
        }
        return SpUtils.get(context).getBoolean(hasCheckUserAuthPermission, false);
    }


    public static void setAuthPermission(Context context, Boolean value) {
        if (context == null) {
            return;
        }
        SpUtils.get(context).put(hasCheckUserAuthPermission, value);
    }


}

