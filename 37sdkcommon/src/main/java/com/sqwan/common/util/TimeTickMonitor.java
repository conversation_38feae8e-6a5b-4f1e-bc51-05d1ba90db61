package com.sqwan.common.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

/**
 * 系统闹钟监听工具类
 */
public class TimeTickMonitor {

    BroadcastReceiver mTimeTickReceiver;
    private Context mContext;
    private OnTimeTickListener mTimeTickListener;
    private int mCurrentTimeTick = 1;
    //回调频率，默认1分钟，单位按分钟
    private int mCallBackFrequency = 1;

    public TimeTickMonitor(Context context, OnTimeTickListener onTimeChangeListener){
        mContext = context.getApplicationContext();
        mTimeTickListener = onTimeChangeListener;
    }

    public void register() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_TIME_TICK);
        mTimeTickReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (mTimeTickListener != null){
                    if (mCurrentTimeTick >= mCallBackFrequency) {
                        mTimeTickListener.onTimeTick();
                        mCurrentTimeTick = 1;
                    } else {
                        mCurrentTimeTick++;
                    }
                }
            }
        };
        mContext.registerReceiver(mTimeTickReceiver, intentFilter);
    }

    public void setTimeTickFrequency(int minute) {
        mCallBackFrequency = minute;
    }

    public void unregister(){
        if (mTimeTickReceiver != null){
            mContext.unregisterReceiver(mTimeTickReceiver);
            mTimeTickReceiver = null;
        }
    }


    public interface OnTimeTickListener{
        void onTimeTick();
    }

}
