package com.sqwan.common.util;

import android.app.Activity;
import android.app.Application;
import android.app.Application.ActivityLifecycleCallbacks;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.sdk.tool.util.SqLogUtil;
import java.lang.ref.WeakReference;

/**
 * 辅助持有主Activity
 *
 * <AUTHOR>
 * @since 2022/9/1
 */
public class SqAtyRef {

    private static volatile SqAtyRef sInstance;

    public static SqAtyRef getInstance() {
        if (sInstance == null) {
            synchronized (SqAtyRef.class) {
                if (sInstance == null) {
                    sInstance = new SqAtyRef();
                }
            }
        }

        return sInstance;
    }

    private SqAtyRef() {
    }

    private Class<? extends Activity> mAtyClass;
    private WeakReference<Activity> mActivityRef;
    private ActivityLifecycleCallbacks mLifecycleCallback;

    /**
     * 监听主Activity, 以便在页面被回收或者重新创建时能获取到正确的Activity
     */
    private void observeAty(Activity activity) {
        if (mLifecycleCallback != null) {
            return;
        }
        Context context = activity.getApplicationContext();
        if (!(context instanceof Application)) {
            SqLogUtil.w("获取Application失败, 不能监听");
            return;
        }
        mLifecycleCallback = new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                if (activity.getClass().equals(mAtyClass)) {
                    SqLogUtil.i("刷新" + mAtyClass + "实例 " + activity);
                    mActivityRef = new WeakReference<>(activity);
                }
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {

            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                if (activity.getClass().equals(mAtyClass)) {
                    SqLogUtil.w("主Activity onStopped: " + activity);
                }
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                if (activity.getClass().equals(mAtyClass) && activity.equals(mActivityRef.get())) {
                    mActivityRef = null;
                    SqLogUtil.w("主Activity销毁: " + activity);
                }
            }
        };
        ((Application) context).registerActivityLifecycleCallbacks(mLifecycleCallback);
    }

    public void init(@NonNull Activity activity) {
        mActivityRef = new WeakReference<>(activity);
        mAtyClass = activity.getClass();
        SqLogUtil.i("主Activity为" + mAtyClass);
        observeAty(activity);
    }

    public Class<? extends Activity> getMainAtyClass() {
        if (mAtyClass == null) {
            throw new IllegalStateException("未初始化主Activity");
        }
        return mAtyClass;
    }

    public Activity getActivity() {
        return mActivityRef != null ? mActivityRef.get() : null;
    }
}
