package com.sqwan.common.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.os.Build;
import android.text.TextUtils;

import com.sqwan.base.L;

/**
 * 描述: 检测本地网络相关
 * 作者：znb
 * 时间：2021-07-01 14:58
 */
public class CheckNetwork {
    private final String TAG = this.getClass().getSimpleName();

    private static final CheckNetwork ourInstance = new CheckNetwork();

    public static CheckNetwork getInstance() {
        return ourInstance;
    }

    private CheckNetwork() {
    }

    public interface CheckNetworkListener {

        void onNetworkChange(boolean isConnected);
    }

    class ConnectionStateMonitor extends ConnectivityManager.NetworkCallback {
        private boolean isWifi;
        private NetworkRequest networkRequest;

        public ConnectionStateMonitor(boolean isWifi) {
            this.isWifi = isWifi;
            if (isWifi) {
                networkRequest = new NetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_WIFI).build();
            } else {
                networkRequest = new NetworkRequest.Builder().addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR).build();
            }

        }

        public void register() {
            ConnectivityManager connectivityManager = (ConnectivityManager) L.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivityManager.registerNetworkCallback(networkRequest, this);
        }

        public void unregister() {
            ConnectivityManager connectivityManager = (ConnectivityManager) L.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivityManager.unregisterNetworkCallback(this);
        }

        @Override
        public void onAvailable(Network network) {
            boolean isAvilableLast = CheckNetwork.this.isWifiAvailable || CheckNetwork.this.isCellularAvailable;
            LogUtil.d(TAG, "onAvailable  isWifi:" + this.isWifi);
            if (isWifi) {
                CheckNetwork.this.isWifiAvailable = true;
            } else {
                CheckNetwork.this.isCellularAvailable = true;
            }
            boolean isAvilable = CheckNetwork.this.isWifiAvailable || CheckNetwork.this.isCellularAvailable;
            if (isAvilableLast != isAvilable) {
                if (CheckNetwork.this.checkNetworkListener != null) {
                    LogUtil.i(TAG, "isConnected:true");
                    CheckNetwork.this.checkNetworkListener.onNetworkChange(true);
                }

            }

        }

        @Override
        public void onUnavailable() {
            LogUtil.d(TAG, "onUnavailable isWifi:" + this.isWifi);
        }

        @Override
        public void onLost(Network network) {
            boolean isAvilableLast = CheckNetwork.this.isWifiAvailable || CheckNetwork.this.isCellularAvailable;
            LogUtil.d(TAG, "onLost isWifi:" + this.isWifi);
            if (isWifi) {
                CheckNetwork.this.isWifiAvailable = false;
            } else {
                CheckNetwork.this.isCellularAvailable = false;
            }
            boolean isAvilable = CheckNetwork.this.isWifiAvailable || CheckNetwork.this.isCellularAvailable;
            if (isAvilableLast != isAvilable) {
                if (CheckNetwork.this.checkNetworkListener != null) {
                    LogUtil.i(TAG, "isConnected:false");
                    checkNetworkListener.onNetworkChange(false);
                }
            }
        }

        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
            if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    LogUtil.d(TAG, "onCapabilitiesChanged: 网络类型为wifi isWifi:" + this.isWifi);
                } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    LogUtil.d(TAG, "onCapabilitiesChanged: 蜂窝网络 isWifi:" + this.isWifi);
                } else {
                    LogUtil.d(TAG, "onCapabilitiesChanged: 其他网络 isWifi:" + this.isWifi);
                }
            }
        }
    }

    private boolean isWifiAvailable = false;
    private boolean isCellularAvailable = false;
    private boolean isUseNewCheckNetwork = Build.VERSION.SDK_INT >= Build.VERSION_CODES.N;
    private ConnectionStateMonitor connectivityManagerCallback_WIFI = null;
    private ConnectionStateMonitor connectivityManagerCallback_CELLULAR = null;
    private NetWorkStateReceiver netWorkStateReceiver = null;
    public CheckNetworkListener checkNetworkListener;

    public void register() {
        if (isUseNewCheckNetwork) {
            if (connectivityManagerCallback_WIFI == null) {
                connectivityManagerCallback_WIFI = new ConnectionStateMonitor(true);
                connectivityManagerCallback_WIFI.register();
            }
            if (connectivityManagerCallback_CELLULAR == null) {
                connectivityManagerCallback_CELLULAR = new ConnectionStateMonitor(false);
                connectivityManagerCallback_CELLULAR.register();
            }
        } else {
            if (netWorkStateReceiver == null) {
                netWorkStateReceiver = new NetWorkStateReceiver();
            }
            IntentFilter filter = new IntentFilter();
            filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            L.getApplicationContext().registerReceiver(netWorkStateReceiver, filter);
        }


    }

    public void unregister() {
        if (isUseNewCheckNetwork) {
            if (connectivityManagerCallback_CELLULAR != null) {
                connectivityManagerCallback_CELLULAR.unregister();
                connectivityManagerCallback_CELLULAR = null;
            }
            if (connectivityManagerCallback_WIFI != null) {
                connectivityManagerCallback_WIFI.unregister();
                connectivityManagerCallback_WIFI = null;
            }
        } else {
            if (netWorkStateReceiver != null) {
                L.getApplicationContext().unregisterReceiver(netWorkStateReceiver);
                netWorkStateReceiver = null;
            }
        }


    }

    class NetWorkStateReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            boolean isConnected = hasNetwork();
            if (CheckNetwork.this.checkNetworkListener != null) {
                CheckNetwork.this.checkNetworkListener.onNetworkChange(isConnected);
            }

        }
    }

    public boolean hasNetwork() {
        return isWifi() || isMobile();
    }

    public boolean isMobile() {
        ConnectivityManager connMgr = (ConnectivityManager) L.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
            NetworkInfo mobileNetworkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
            return mobileNetworkInfo.isConnected();
        } else {
            //获得ConnectivityManager对象
            //获取所有网络连接的信息
            Network[] networks = connMgr.getAllNetworks();
            //通过循环将网络信息逐个取出来
            for (int i = 0; i < networks.length; i++) {
                //获取ConnectivityManager对象对应的NetworkInfo对象
                NetworkInfo networkInfo = connMgr.getNetworkInfo(networks[i]);
                if (TextUtils.equals("MOBILE", networkInfo.getTypeName())) {
                    if (networkInfo.isConnected()) {
                        return true;
                    }
                }

            }
            return false;
        }
    }

    public boolean isWifi() {
        ConnectivityManager connMgr = (ConnectivityManager) L.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
            NetworkInfo wifiNetworkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            return wifiNetworkInfo.isConnected();
        } else {
            //获得ConnectivityManager对象
            //获取所有网络连接的信息
            Network[] networks = connMgr.getAllNetworks();
            //通过循环将网络信息逐个取出来
            for (int i = 0; i < networks.length; i++) {
                //获取ConnectivityManager对象对应的NetworkInfo对象
                NetworkInfo networkInfo = connMgr.getNetworkInfo(networks[i]);
                if (networkInfo != null && TextUtils.equals("WIFI", networkInfo.getTypeName())) {
                    if (networkInfo.isConnected()) {
                        return true;
                    }
                }

            }
            return false;
        }
    }
}