package com.sqwan.common.util;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.CharacterStyle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.base.L;
import com.sqwan.common.util.toast.MyToast;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-05-13
 */
public class ToastUtil {

    public static void showToast(Context context, CharSequence msg){
        if(TextUtils.isEmpty(msg)){
            return;
        }
        try {
            LogUtil.v("Toast Log -> " + msg);
            new ToastUtil.Builder(context)
                    .setBackgroundColor(Color.parseColor("#BB000000"))
                    .addText(msg.toString())
                    .show();
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    public static void showToast(String msg){
        LogUtil.v("Toast Log -> " + msg);
        MyToast.showToast(L.getActivity(),msg,Gravity.CENTER,0,0);
    }

    public static class Builder {

        private Context mContext;
        private int mBackgroundColor;
        private List<String> mTextList;
        private Map<Integer, CharacterStyle> mSpannable;
        private int mGravity = Gravity.CENTER;

        public Builder(Context context) {
            this.mContext = context;
            mTextList = new ArrayList<>();
            mSpannable = new HashMap<>();
        }

        public Builder setBackgroundColor(int color) {
            this.mBackgroundColor = color;
            return this;
        }

        public Builder addText(String text) {
            mTextList.add(text);
            return this;
        }

        public Builder addText(String text, CharacterStyle spannable) {
            mTextList.add(text);
            mSpannable.put(mTextList.size() - 1, spannable);
            return this;
        }

        public Builder setGravity(int gravity) {
            this.mGravity = gravity;
            return this;
        }



        public void show() {
            if(mTextList.size() == 0) {
                LogUtil.e("toast msg is empty!");
                return;
            }
            Toast toast = Toast.makeText(mContext, "", Toast.LENGTH_SHORT);

            ViewGroup toastView = (ViewGroup) LayoutInflater.from(mContext).inflate(
                    SqResUtils.getLayoutId(mContext, "sy37_s_toast_layout"), null, false);
            TextView tvMsg = toastView.findViewById(SqResUtils.getId(mContext, "tv_msg"));

            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(mBackgroundColor);
            gradientDrawable.setShape(GradientDrawable.RECTANGLE);
            gradientDrawable.setCornerRadius(DisplayUtil.dip2px(mContext, 6));
            toastView.setBackground(gradientDrawable);

            StringBuilder sb = new StringBuilder();
            for(String str : mTextList) {
                sb.append(str);
            }
            SpannableString spannableString = new SpannableString(sb.toString());
            int lengthCount = 0;
            for(int index = 0; index < mTextList.size(); index++) {
                int length = mTextList.get(index).length();
                if(mSpannable.get(index) != null) {
                    spannableString.setSpan(mSpannable.get(index), lengthCount,
                            lengthCount + length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                lengthCount += length;
            }

            tvMsg.setText(spannableString);
            toast.setView(toastView);
            toast.setGravity(mGravity, 0, 0);
            toast.show();
        }
    }

}
