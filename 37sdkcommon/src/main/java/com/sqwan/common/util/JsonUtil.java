package com.sqwan.common.util;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * Json工具类
 * <AUTHOR>
 */
public class JsonUtil {

    /**
     * 把Map组装成Json格式
     *
     * @param map
     * @return
     */
    public static JSONObject mapToJson(Map<String, ? extends Object> map) {
        if (map == null || map.isEmpty()) {
            return new JSONObject();
        }
        Set<String> keys = map.keySet();
        Iterator<String> it = keys.iterator();
        JSONObject obj = new JSONObject();
        while (it.hasNext()) {
            try {
                String key = it.next();
                Object value = map.get(key);
                obj.put(key, value);
            } catch (JSONException e1) {
                e1.printStackTrace();
            }
        }
        return obj;
    }

}
