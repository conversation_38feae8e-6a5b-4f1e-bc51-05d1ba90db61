package com.sqwan.common.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.support.v4.app.ActivityCompat;
import java.io.File;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-31 11:56
 */
public class EnvironmentUtils {
    private static boolean checkedSdCard;
    private static boolean sdCardPermission;

    public static boolean checkSdCardPermission(Context context) {
        boolean sq_auth_handle = SpUtils.get(context).getBoolean("sq_auth_handle");
        if (!sq_auth_handle) {
            LogUtil.w("检测频繁获取，未同意，直接返回false");
            return false;
        }
        if (checkedSdCard) {
            return sdCardPermission;
        }
        sdCardPermission = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED) && (ActivityCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED);
        checkedSdCard = true;
        LogUtil.i("检测频繁获取，检查sd卡权限：" + sdCardPermission);
        return sdCardPermission;
    }

    private static File getExternalCacheDir(Context context) {
        return context.getExternalCacheDir();
    }

    public static File getExternalStorageDir() {
        return Environment.getExternalStorageDirectory();
    }

    public static File getCommonDir(Context context) {
        if (checkSdCardPermission(context) &&
            (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ||
                context.getApplicationInfo().targetSdkVersion < Build.VERSION_CODES.Q)) {
            return getExternalStorageDir();
        } else {
            return getExternalCacheDir(context);
        }
    }

    public static String getCommonDirPath(Context context) {
        return getCommonDir(context).getAbsolutePath();
    }

    public static String getCommonDirPathEndWithSprit(Context context) {
        try {
            return getCommonDir(context).getAbsolutePath() + "/";
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getCommonSubDirPath(Context context, String subFile) {
        File dirFile =
                new File(getCommonDir(context), subFile);
        try {
            if (!(dirFile.exists() && !(dirFile.isDirectory()))) {
                dirFile.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        return dirFile.getAbsolutePath() + File.separator;
    }

    public static String getCommonSubDirPathEndWithSprit(Context context, String subFile) {
        try {
            return getCommonSubDirPath(context, subFile) + "/";
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 文件是否存在
     */
    public static boolean isFileExits(Context context, String fileName) {
        String filePath = EnvironmentUtils.getCommonDirPath(context) + File.separator + fileName;
        LogUtil.i(filePath);
        File file = new File(filePath);
        if (file.exists() && file.isDirectory()) {
            LogUtil.i(fileName + " directory is exists");
            return true;
        } else {
            LogUtil.i(fileName + " directory is not exists");
        }
        return false;
    }
}
