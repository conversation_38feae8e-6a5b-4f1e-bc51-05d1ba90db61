package com.sqwan.common.util;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.web.SY37web;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2020-05-12
 */
public class AppUtils {

    public static String constructWebUrlParam(Context context, String sdkUrl) {

        if (TextUtils.isEmpty(sdkUrl)) return "";
        String url_fixed = sdkUrl;
        Uri uri = Uri.parse(sdkUrl);
        String url_host = uri.getHost();
        //去掉只对主域名增加参数的逻辑，解决跳转某些多包个人中心页面，缺少登录态的问题
        if (url_host != null) {
            SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
            String appendUrl = "?gid=" + config.getGameid()
                    + "&pid=" + config.getPartner()
                    + "&dev=" + DevLogic.getInstance(context).getValue()
                    + "&token=" + ModHelper.get(IAccountMod.class).getToken()
                    + "&sversion=" + VersionUtil.sdkVersion
                    + "&gwversion=" + VersionUtil.gwversion
                    + "&refer=" + config.getRefer()
                    + "&scut=" + ConfigManager.getInstance(context).getLoginCode()
                    + "&uid=" + ModHelper.get(IAccountMod.class).getUid()
                    + "&uname=" + ModHelper.get(IAccountMod.class).getUname()
                    + "&version=" + getVersionName(context)
                    //新版政策 区分新老包
                    + "&policy=" + "1"
                    // 宿主的版本号
                    + "&host_sdk_version=" + VersionUtil.getOriginalVersion();

            // 特殊SDK增加scut3这个参数
            if (!TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3())) {
                appendUrl = appendUrl + "&scut3=" + MultiSdkManager.getInstance().getScut3();
            }

            //201709添加 在线客服
            String dsid = ConfigManager.getInstance(context).getRoleInfo() != null ? ConfigManager.getInstance(context).getRoleInfo().get("serverId") : "0";
            appendUrl += "&dsid=" + dsid + "&os=1";
            if (sdkUrl.contains("?")) {
                appendUrl = appendUrl.replace("?", "&");
            }
            url_fixed += appendUrl;
        }
        LogUtil.i("SQ weburl：" + url_fixed);
        return url_fixed;
    }

    /**
     * 跳转到外部浏览器打开带参数的url
     *
     * @param context
     * @param sdkUrl
     */
    public static void toSdkUrl(Context context, String sdkUrl) {
        if (TextUtils.isEmpty(sdkUrl)) {
            LogUtil.e("跳转到外部浏览器，url 为空");
            return;
        }
        toUri(context, constructWebUrlParam(context, sdkUrl));
    }

    /**
     * 浏览器打开url
     *
     * @param context
     * @param url
     */
    public static void toUri(Context context, String url) {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction(Intent.ACTION_VIEW);
        intent.setComponent(getDefaultBrowserIntent(context));
        intent.setData(Uri.parse(url));
        context.startActivity(intent);
    }


    /**
     * 跳转到内置浏览器打开带参数的url
     *
     * @param context
     * @param sdkUrl
     * @param title
     */
    public static void toSQWebUrl(Context context, String sdkUrl, String title) {
        toSQWebUrl(context, sdkUrl, title, false);
    }

    public static void toSQWebUrl(Context context, String sdkUrl, String title, boolean quitAnim) {
        toSQWebUrlNoAppend(context, constructWebUrlParam(context, sdkUrl), title, quitAnim);
    }

    public static void toSQWebUrlNoAppend(Context context, String sdkUrl, String title, boolean quitAnim) {
        if (sdkUrl == null) {
            return;
        }
        Intent intent = new Intent(context, SY37web.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(SY37web.WEB_URL, sdkUrl);
        intent.putExtra(SY37web.WEB_TITLE, title);
        intent.putExtra(SY37web.WEB_QUIT_ANIM, quitAnim);
        context.startActivity(intent);
    }


    /**
     * 跳转到内置浏览器打开带参数的url  浏览器带title
     *
     * @param context
     * @param url
     * @param title
     */
    public static void toSQWebUrlWithTitle(Context context, String url, String title) {
        if (url == null) {
            return;
        }
        Intent intent = new Intent(context, SY37web.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(SY37web.WEB_URL, constructWebUrlParam(context, url));
        intent.putExtra(SY37web.WEB_TITLE, title);
        intent.putExtra(SY37web.WEB_SHOW_TITLE, true);
        context.startActivity(intent);
    }

    /**
     * 获取手机浏览器列表的首项
     *
     * @param context
     * @return 浏览器列表首项，无则返回null
     */
    private static ComponentName getDefaultBrowserIntent(Context context) {

        PackageManager packageManager = context.getPackageManager();

        Intent defaultBrowserIntent = new Intent("android.intent.action.VIEW");
        defaultBrowserIntent.addCategory("android.intent.category.BROWSABLE");
        defaultBrowserIntent.addCategory("android.intent.category.DEFAULT");
        Uri uri = Uri.parse("http://");
        defaultBrowserIntent.setDataAndType(uri, null);

        // 找出手机当前安装的所有浏览器程序
        List<ResolveInfo> resolveInfoList = packageManager
                .queryIntentActivities(defaultBrowserIntent,
                        PackageManager.GET_INTENT_FILTERS);


        int size = resolveInfoList.size();
        ComponentName[] arrayOfComponentName = new ComponentName[size];
        ComponentName componentName = null;
        boolean hasUcBrowser = false;
        String defaultBrowserClassName = "com.UCMobile"; //"com.android.chrome"


        for (int i = 0; i < size; i++) {
            ActivityInfo activityInfo = resolveInfoList.get(i).activityInfo;
            String packageName = activityInfo.packageName;
            String className = activityInfo.name;

            if (packageName.contains(defaultBrowserClassName)) { //默认UC打开
                hasUcBrowser = true;
                componentName = new ComponentName(packageName, className);
            } else {

                hasUcBrowser = false;
            }

            arrayOfComponentName[i] = new ComponentName(packageName, className);
        }

        if (arrayOfComponentName != null && arrayOfComponentName.length > 0 && !hasUcBrowser) {
            return arrayOfComponentName[0];
        }

        return componentName;
    }

    public static void startAppFromPackage(Context context, String packageName) {
        PackageInfo pi;
        try {
            pi = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            LogUtil.e("需要启动的应用不存在！");
            return;
        }

        Intent resolveIntent = new Intent(Intent.ACTION_MAIN, null);
        resolveIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        if (pi != null) {
            resolveIntent.setPackage(pi.packageName);
        }

        List<ResolveInfo> apps = context.getPackageManager().queryIntentActivities(resolveIntent, 0);

        ResolveInfo ri = apps.iterator().next();
        if (ri != null) {
            String packageName1 = ri.activityInfo.packageName;
            String className = ri.activityInfo.name;

            Intent intent = new Intent(Intent.ACTION_MAIN);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);

            ComponentName cn = new ComponentName(packageName1, className);

            intent.setComponent(cn);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }
    }

    public static String getLocaleLanguage() {
        Locale locale = Locale.getDefault();
        return locale.getLanguage() + "-" + locale.getCountry();
    }

    public static String getAppName(Context context) {
        PackageManager packageManager = null;
        ApplicationInfo applicationInfo = null;
        try {
            packageManager = context.getApplicationContext().getPackageManager();
            applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            return "";
        }
        return packageManager.getApplicationLabel(applicationInfo).toString();
    }


    /**
     * 验证手机号码是否符合格式
     *
     * @param mobiles
     * @return
     */
    public static boolean isMobileNO(String mobiles) {
        boolean result = mobiles.length() == 11;
        if (result) {
            for (int i = 0; i < mobiles.length(); i++) {
                try {
                    Integer.parseInt(mobiles.charAt(i) + "");
                } catch (Exception e) {
                    e.printStackTrace();
                    result = false;
                    break;
                }

            }
        }
        return result;
    }


    /**
     * 获取和保存当前View的截图
     */
    public static boolean saveView(Context context, View view, String FileName) {
        boolean sdcardExist = EnvironmentUtils.checkSdCardPermission(context);
        // 有存储才保存
        if (sdcardExist) {
            try {
                // 获取屏幕
                view.setBackgroundResource(SqResUtils.getIdByName("s_transparent", "color", context));
                Bitmap bitmap = convertViewToBitmap(view);
                if (bitmap == null) {
                    System.out.println("生成账密截图失败");
                    return false;
                }

                Bitmap screenShot = Bitmap.createBitmap(bitmap);

                view.setDrawingCacheEnabled(false);
                view.destroyDrawingCache();

                // 3.保存Bitmap，默认放到系统相册路径中去
                String savePath = EnvironmentUtils.getCommonDirPath(context) + File.separator + "DCIM" + File.separator;

                File path = new File(savePath);
                // 文件
                String filepath = savePath + File.separator + FileName;
                File file = new File(filepath);
                if (!path.exists()) {
                    path.mkdirs();
                }
                if (!file.exists()) {
                    if (!file.createNewFile()) {
                        return false;
                    }
                }

                FileOutputStream fos = null;
                fos = new FileOutputStream(file);

                screenShot.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();

                String screenTips = "账号已截图并保存" + "\n"
                        + savePath;
                LogUtil.w(screenTips);

                //发送广播更新图库
                Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                Uri uri = Uri.fromFile(file);
                intent.setData(uri);
                context.sendBroadcast(intent);
                return true;
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("数据有误，账号截图保存失败：" + FileName);
                return false;
            }
        } else {
            System.out.println("没有存储卡权限，账号截图没法保存：" + FileName);
            return false;
        }

    }

    public static Bitmap convertViewToBitmap(View view) {
        LogUtil.i("convert view to bitmap");
        view.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        view.buildDrawingCache();
        Bitmap bitmap = view.getDrawingCache();
        return bitmap;
    }


    /**
     * 获取当前屏幕方向
     *
     * @return
     */
    public static int getOrientation() {
        Activity activity = SQContextWrapper.getActivity();
        if (activity != null) {
            return activity.getResources().getConfiguration().orientation;
        }
        return Configuration.ORIENTATION_PORTRAIT;
    }

    /**
     * 获取包名
     *
     * @param context
     * @return
     */
    public static String getPackageName(Context context) {
        return context.getPackageName();
    }

    /**
     * 获取应用安装时间
     */
    public static long getAppInstallTime(Context context) {
        try {
            PackageManager packageManager = context.getApplicationContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
            //应用装时间
            return packageInfo.firstInstallTime;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 获取应用最近更新时间
     */
    public static long getAppUpdateTime(Context context) {
        try {
            PackageManager packageManager = context.getApplicationContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
            //应用最后一次更新时间
            return packageInfo.lastUpdateTime;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 获取软件版本名称
     * 用于更新
     *
     * @param context
     * @return
     */
    public static String getVersionName(Context context) {
        String versionName = "1.0.0.0";

        try {
            // 获取软件版本号，对应AndroidManifest.xml下android:versionName
            versionName = context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }
}
