package com.sqwan.common.util;

import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.sqwan.common.data.SpannableEntity;

public class SpannableHelper {
    /**
     * 处理textview中部分字体高亮以及可点击
     * @param textView
     * @param content
     * @param spannableEntities
     */
    public static void handleSpannableHighLineClick(TextView textView, String content, SpannableEntity... spannableEntities) {
        try{
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(content);
            if (spannableEntities == null || spannableEntities.length < 1) {
                return;
            }
            for (final SpannableEntity spannableEntity : spannableEntities) {
                String tag = spannableEntity.getTag();
                int index = content.indexOf(tag);
                spannableStringBuilder.setSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        SpannableEntity.OnClickListener onClickListener = spannableEntity.getOnClickListener();
                        if (onClickListener != null) {
                            onClickListener.onClick();
                        }
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(Color.parseColor(spannableEntity.getColorValue()));
                        ds.setUnderlineText(false);
                    }
                }, index, index + tag.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

            }
            textView.setText(spannableStringBuilder);
            textView.setMovementMethod(LinkMovementMethod.getInstance());
            textView.setHighlightColor(Color.TRANSPARENT);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
