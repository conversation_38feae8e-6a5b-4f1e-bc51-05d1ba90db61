package com.sqwan.common.util.task;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;

import java.util.HashSet;
import java.util.Set;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/1/5 09:29
 */
class HandlerSubThreadManager {
    private Handler handler;
    private Set<Runnable> runnables = new HashSet<>();
    private static final HandlerSubThreadManager ourInstance = new HandlerSubThreadManager();
    public static HandlerSubThreadManager getInstance() {
        return ourInstance;
    }

    private HandlerSubThreadManager() {
        HandlerThread handlerThread = new HandlerThread("HandlerManagerSubThread");
        handlerThread.start();
        this.handler = new Handler(handlerThread.getLooper());
    }
    public void release() {
        for (Runnable runnable : runnables) {
            this.handler.removeCallbacks(runnable);
        }

        this.handler = null;

    }
    public void postDelay(long delay, Runnable runnable) {
        runnables.add(runnable);
        if (handler == null) {
            return;
        }
        if (delay==0) {
            handler.post(runnable);
        }else{
            handler.postDelayed(runnable, delay);
        }
    }

    public void remove(Runnable runnable) {
        if (runnable!=null) {
            runnables.remove(runnable);
        }
    }

    public void stop(Runnable runnable) {
        remove(runnable);
        if (handler != null) {
            handler.removeCallbacks(runnable);
        }
    }
}
