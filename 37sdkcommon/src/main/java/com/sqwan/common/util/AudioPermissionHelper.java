package com.sqwan.common.util;

import android.Manifest;
import android.app.Activity;
import android.webkit.PermissionRequest;
import com.sqwan.base.ActivityResultListener;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.dialog.AudioPermissionDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;

/**
 * @author: zls
 * @date: 2025/3/18
 */
public class AudioPermissionHelper {

    public static final String[] AUDIO_PERMISSION = new String[]{Manifest.permission.RECORD_AUDIO};

    public static final String AUDIO_PERMISSION_NAME = "麦克风权限";

    public static final String AUDIO_PERMISSION_BY_RECORD = "申请麦克风权限：用于客服AI语音功能";


    public static final int SQ_AUDIO_REQUEST_PERMISSION_CODE = 2025;

    /**
     * 请求麦克风权限
     */
    public static void requestAudioPermission(Activity context, PermissionRequest permissionRequest) {
        boolean isPermissionForbidden = PermissionHelper.getInstance().isPermissionForbiden(AUDIO_PERMISSION[0]);
        LogUtil.i("isPermissionForbidden:" + isPermissionForbidden);
        if (isPermissionForbidden) {
            showDialog(context, permissionRequest);
        } else {
            PermissionSimpleHelper.requestPermission(
                    AUDIO_PERMISSION, AUDIO_PERMISSION_NAME, AUDIO_PERMISSION_BY_RECORD, new PermissionSimpleHelper.OnPermissionCallback() {
                        @Override
                        public void onGranted() {
                            LogUtil.i("AudioPermissionHelper.AUDIO_PERMISSION is granted");
                            permissionRequest.grant(permissionRequest.getResources());
                        }

                        @Override
                        public void onDenied() {
                            LogUtil.i("AudioPermissionHelper.AUDIO_PERMISSION is denied");
                            showDialog(context, permissionRequest);
                        }
                    }
            );
        }
    }

    private static void showDialog(Activity context, PermissionRequest permissionRequest) {
        AudioPermissionDialog dialog = new AudioPermissionDialog(context);
        dialog.setConfirmListener(new AudioPermissionDialog.ConfirmListener() {
            @Override
            public void onConfirm() {
                EventDispatcher.getInstance().addActivityResultListener(new ActivityResultListener() {
                    @Override
                    public void onResult(OnActivityResultEvent event) {
                        int requestCode = event.getRequestCode();
                        if (requestCode != SQ_AUDIO_REQUEST_PERMISSION_CODE) {
                            return;
                        }
                        // 移除监听对象，避免内存泄漏
                        EventDispatcher.getInstance().removeActivityResultListener(this);
                        boolean isHasAudioPermission = PermissionHelper.getInstance().checkPermission(AUDIO_PERMISSION[0]);
                        LogUtil.i("AudioPermissionHelper onResult isHasAudioPermission = " + isHasAudioPermission);
                        if (isHasAudioPermission) {
                            permissionRequest.grant(permissionRequest.getResources());
                        } else {
                            dialog.show();
                        }
                    }
                });
            }
        });
        dialog.setCancelListener(new AudioPermissionDialog.CancelListener() {
            @Override
            public void onCancel() {
                permissionRequest.deny();
            }
        });
        dialog.show();
    }
}
