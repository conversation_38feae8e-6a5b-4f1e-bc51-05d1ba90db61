package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;
import android.support.annotation.Nullable;

public class ViewUtils {



	public static View inflate(Activity activity,int layout){
		LayoutInflater inflater = activity.getLayoutInflater();
        View view = inflater.inflate(layout, null);
        return view;
	}

	public static View inflate(Context context,int layout){
		LayoutInflater inflater = ((Activity) context).getLayoutInflater();
        View view = inflater.inflate(layout, null);
        return view;
	}


	public static void showToast(Context context,String words){
		try {
			ToastUtil.showToast(context, words);
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	@Deprecated
	public static void hideSystemView(Window window){
		if(window == null) {
			return;
		}
		if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
			View v = window.getDecorView();
			if(v!=null){
				v.setSystemUiVisibility(View.GONE);
			}
		} else {
			View decorView = window.getDecorView();
			if (decorView != null) {
				decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
						| View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
			}
			window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
		}
	}
	public static boolean show(final View view) {
		if(view==null){
			return false;
		}
		if (view.getVisibility() == View.VISIBLE) {
			return true;
		}
		view.setVisibility(View.VISIBLE);
		return true;
	}

	public static boolean hidden(final View view) {
		if(view==null){
			return false;
		}
		if (view.getVisibility() == View.INVISIBLE) {
			return true;
		}
		view.setVisibility(View.INVISIBLE);
		return true;
	}

	public static boolean gone(final View view) {
		if(view==null){
			return false;
		}
		if (view.getVisibility() == View.GONE) {
			return true;
		}
		view.setVisibility(View.GONE);
		return true;
	}
	public static boolean isHidden(View view){
		if( view == null){
			return true;
		}
		return view.getVisibility() == View.INVISIBLE;
	}
	public static boolean isGone(View view){
		if( view == null){
			return true;
		}
		return view.getVisibility() == View.GONE;
	}
	public static boolean isShow(View view){
		if( view == null){
			return true;
		}
		return view.getVisibility() == View.VISIBLE;
	}

	@Nullable
	public static Activity getActivity(@Nullable View view) {
		if (view == null) {
			return null;
		}
		Context context = view.getContext();
		do {
			if (context instanceof Activity) {
				return (Activity) context;
			} else if (context instanceof ContextWrapper) {
				context = ((ContextWrapper) context).getBaseContext();
			} else {
				return null;
			}
		} while (context != null);
		return null;
	}
}
