package com.sqwan.common.util;


import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import java.io.IOException;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * <AUTHOR>
 * @date 2019/3/21
 */
public class ChannelUtil {


    /**
     * info-1002464-1-xxxxxxxxxx
     */
    private static final String CHANNEL_KEY = "info-";


    /**
     * 从apk中获取版本信息
     */
    public static String getChannelFromApk(Context context) {
        SQLog.d("[ChannelUtil]getChannelFromApk channelKey=" + CHANNEL_KEY);
        ApplicationInfo appinfo = context.getApplicationInfo();
        String sourceDir = appinfo.sourceDir;
        String key = "META-INF/" + CHANNEL_KEY;
        String ret = "";
        ZipFile zipfile = null;
        try {
            zipfile = new ZipFile(sourceDir);
            Enumeration<?> entries = zipfile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = ((ZipEntry) entries.nextElement());
                String entryName = entry.getName();
                if (entryName.startsWith(key)) {
                    ret = entryName;
                    break;
                }
            }
        } catch (IOException e) {
            SQLog.e("[ChannelUtil]getChannelFromApk异常", e);
        } finally {
            if (zipfile != null) {
                try {
                    zipfile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (TextUtils.isEmpty(ret)) {
            SQLog.w("[ChannelUtil]getChannelFromApk为空, 忽略");
            return "";
        }
        SQLog.i("[ChannelUtil]getChannelFromApk res=" + ret);
        String channel = "";
        String[] table = ret.split("-");
        if (table.length < 5) {
            SQLog.w("[ChannelUtil]getChannelFromApk 无效channel: " + ret);
            return channel;
        }
        try {
            channel = table[2] + "-" + table[3] + "-" + table[4];
        } catch (Exception e) {
            SQLog.e("[ChannelUtil]getChannelFromApk 解析异常", e);
        }
        SQLog.i("[ChannelUtil]getChannelFromApk channel=" + channel);
        return channel;
    }

}
