package com.sqwan.common.util;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.text.TextUtils;

import android.support.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-26 10:07
 */
public class ActivityLifeCycleUtils {

    Object application;
    public CopyOnWriteArrayList<Activity> activities = new CopyOnWriteArrayList<>();
    final String tag = this.getClass().getSimpleName();
    private static final ActivityLifeCycleUtils ourInstance = new ActivityLifeCycleUtils();

    /** 栈顶的 Activity 对象 */
    private Activity topActivity;
    /** 前台并且可见的 Activity 对象 */
    private Activity resumedActivity;

    public static ActivityLifeCycleUtils getInstance() {
        return ourInstance;
    }

    private ActivityLifeCycleUtils() {
    }

    int appCount;

    public boolean isForeground() {
        logAppCount();
        return appCount > 0;
    }

    /**
     * 获取栈顶的 Activity
     */
    @Nullable
    public Activity getTopActivity() {
        return topActivity;
    }

    /**
     * 获取前台并且可见的 Activity
     */
    @Nullable
    public Activity getResumedActivity() {
        return resumedActivity;
    }

    List<AppVisibilityCallback> appVisibilityCallbacks = new ArrayList<>();

    public static class AppVisibilityCallbackAdapter extends AppVisibilityCallback {

        @Override
        public void onBackground() {

        }

        @Override
        public void onForeground() {

        }
    }

    public abstract static class AppVisibilityCallback extends ActivityLifecycleAdapter {
        public abstract void onBackground();

        public abstract void onForeground();
    }

    AppVisibilityCallback appVisibilityCallback = new AppVisibilityCallbackAdapter() {
        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            LogUtil.i(tag, "onActivityCreated " + activity);
            activities.add(activity);
            topActivity = activity;
        }

        @Override
        public void onActivityStarted(Activity activity) {
            LogUtil.i(tag, "onActivityStarted " + activity);
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityStarted(activity);
            }
            int appCountLast = appCount;
            appCount++;
            logAppCount();
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityStarted(activity);
            }
            if (appCountLast == 0 && appCount != 0) {
                LogUtil.i(tag, "onForeground");
                for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                    appVisibilityCallback.onForeground();
                }
            }
        }

        @Override
        public void onActivityStopped(Activity activity) {
            LogUtil.i(tag, "onActivityStopped " + activity);
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityStopped(activity);
            }
            appCount--;
            if (appCount < 0) {
                appCount = 0;
            }
            logAppCount();

            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityStopped(activity);
            }
            if (appCount == 0) {
                LogUtil.i(tag, "onBackground");
                for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                    appVisibilityCallback.onBackground();
                }
            }
            if (resumedActivity == activity) {
                resumedActivity = null;
            }
        }

        @Override
        public void onActivityPaused(Activity activity) {
            LogUtil.i(tag, "onActivityPaused " + activity);
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityPaused(activity);
            }
        }

        @Override
        public void onActivityResumed(Activity activity) {
            LogUtil.i(tag, "onActivityResumed " + activity);
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityResumed(activity);
            }
            topActivity = activity;
            resumedActivity = activity;
        }

        @Override
        public void onActivityDestroyed(Activity activity) {
            LogUtil.i(tag, "onActivityDestroyed " + activity);
            for (AppVisibilityCallback appVisibilityCallback : appVisibilityCallbacks) {
                appVisibilityCallback.onActivityDestroyed(activity);
            }
            activities.remove(activity);
            if (topActivity == activity) {
                topActivity = null;
            }
        }
    };

    public void init(Object application) {
        CompatibilityUtil.closeDetectedProblemApiDialog();
        if (this.application == null) {
            this.application = application;
            if (this.application instanceof Application) {
                LogUtil.e(tag, "registerActivityLifecycleCallbacks " + appVisibilityCallback);
                ((Application) application).registerActivityLifecycleCallbacks(appVisibilityCallback);
            } else {
                LogUtil.e(tag, "registerActivityLifecycleCallbacks error");
            }
        }

    }

    public void registerActivityListener(AppVisibilityCallback appVisibilityCallback) {
        LogUtil.i(tag, "registerActivityListener " + application);
        if (application != null && application instanceof Application) {
            appVisibilityCallbacks.add(appVisibilityCallback);
        }

    }

    public void unRegisterActivityListener(AppVisibilityCallback appVisibilityCallback) {
        LogUtil.i(tag, "unRegisterActivityListener " + application);
        if (application != null && application instanceof Application) {
            appVisibilityCallbacks.remove(appVisibilityCallback);
        }

    }

    public final String PluginActivityName = "com.host.PluginActivity";
    public final String PluginExActivityName = "com.host.PluginExActivity";

    public boolean equalActivity(Activity activity, Class clazz) {
        if (TextUtils.equals(PluginActivityName, activity.getComponentName().getClassName()) || TextUtils.equals(PluginExActivityName, activity.getComponentName().getClassName())) {
            String _activity = activity.getIntent().getStringExtra("activity");
            return TextUtils.equals(_activity, clazz.getName());
        } else {
            return activity.getClass() == clazz;
        }
    }

    private void logAppCount() {
        LogUtil.i(tag, "appCount:" + appCount);
    }
}
