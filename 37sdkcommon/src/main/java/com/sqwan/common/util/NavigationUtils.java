package com.sqwan.common.util;

import android.app.Service;
import android.content.Context;
import android.graphics.Point;
import android.os.Build;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;


/**
 * <AUTHOR>
 * @date 2021-01-03
 */
public class NavigationUtils {


    public static int getNavigationBarHeight(Context context) {
        int result = 0;
        int resourceId = context.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        if(resourceId > 0) {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    public static boolean hasNavigationBar(Context context) {
        if(getNavigationBarHeight(context) == 0) {
            return false;
        }
        if(RomUtil.isEmui() && isHuaWeiHideNav(context)) {
            return false;
        }
        if(RomUtil.isMiui() && isMiuiFullScreen(context)) {
            return false;
        }
        if(RomUtil.isVivo() && isVivoFullScreen(context)) {
            return false;
        }
        return isHasNavigationBar(context);
    }



    private static boolean isHuaWeiHideNav(Context context) {
        int navigationBarIsMin;
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            navigationBarIsMin = Settings.System.getInt(context.getContentResolver(), "navigationbar_is_min", 0);
        } else {
            navigationBarIsMin = Settings.Global.getInt(context.getContentResolver(), "navigationbar_is_min", 0);
        }
        return navigationBarIsMin != 0;
    }

    private static boolean isMiuiFullScreen(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), "force_fsg_nav_bar", 0) != 0;
    }

    private static boolean isVivoFullScreen(Context context) {
        return Settings.Secure.getInt(context.getContentResolver(), "navigation_gesture_on", 0) != 0;
    }

    public static boolean isHasNavigationBar(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Service.WINDOW_SERVICE);
        if(windowManager == null) {
            return false;
        }
        Display display = windowManager.getDefaultDisplay();
        DisplayMetrics realDisplayMetrics = new DisplayMetrics();
        display.getRealMetrics(realDisplayMetrics);
        int realHeight = realDisplayMetrics.heightPixels;
        int realWidth = realDisplayMetrics.widthPixels;

        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getMetrics(displayMetrics);
        int displayHeight = displayMetrics.heightPixels;
        int displayWidth = displayMetrics.widthPixels;

        Display _display = windowManager.getDefaultDisplay();
        Point size = new Point();
        Point realSize = new Point();
        _display.getRealSize(realSize);
        _display.getSize(size);
        return realWidth - displayWidth > 0 || realHeight - displayHeight > 0;
    }

}
