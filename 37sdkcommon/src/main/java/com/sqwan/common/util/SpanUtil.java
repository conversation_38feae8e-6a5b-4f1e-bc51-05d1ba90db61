package com.sqwan.common.util;

import android.graphics.Typeface;
import android.text.Editable;
import android.text.SpannableString;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;

/**
 * 描述:
 * 作者：znb
 * 时间：2020-11-30 16:19
 */
public class SpanUtil {
    public static CharSequence getFontString(String text,int size,int color,boolean isBold){
        if (!isBold)
            return getFontString(text, size, color);
        else {
            StyleSpan styleSpan = new StyleSpan(Typeface.BOLD);
            SpannableString spannableString = new SpannableString(text);
            AbsoluteSizeSpan absoluteSizeSpan = new AbsoluteSizeSpan(size);//SpannableString.SPAN_INCLUSIVE_INCLUSIVE
            spannableString.setSpan(absoluteSizeSpan, 0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(color);
            spannableString.setSpan(foregroundColorSpan, 0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(styleSpan, 0, spannableString.length(), SpannableString.SPAN_INCLUSIVE_EXCLUSIVE);

            return spannableString;
        }
    }
    public static CharSequence getFontString(String text,int size,int color) {
        SpannableString spannableString = new SpannableString(text);
        AbsoluteSizeSpan absoluteSizeSpan = new AbsoluteSizeSpan(size);//SpannableString.SPAN_INCLUSIVE_INCLUSIVE
        spannableString.setSpan(absoluteSizeSpan, 0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(color);
        spannableString.setSpan(foregroundColorSpan, 0, spannableString.length(), SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }
    public static CharSequence concat(CharSequence... charSequences) {
        CharSequence first = charSequences[0];
        first = (first!=null)?first:"";
        Editable editable = Editable.Factory.getInstance().newEditable(first);
        for(int i=1;i<charSequences.length;i++){
            CharSequence _charSequences = charSequences[i];
            if (charSequences!=null) {
                editable.append(_charSequences);
            }
        }
        return editable;
    }
}
