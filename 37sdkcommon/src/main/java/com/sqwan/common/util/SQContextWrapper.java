package com.sqwan.common.util;

import android.app.Activity;
import android.content.Context;
import android.support.annotation.NonNull;
import com.sq.tool.logger.SQLog;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2022/4/26
 */
public final class SQContextWrapper {

    private static Context sAppContext;

    private SQContextWrapper() {
    }

    public static void init(@NonNull Context context) {
        sAppContext = context.getApplicationContext() != null ? context.getApplicationContext() : context;
        hookOldContext(context);
    }

    public static void init(@NonNull Activity activity) {
        sAppContext = activity.getApplicationContext();
        SqAtyRef.getInstance().init(activity);
        com.sq.sdk.tool.util.SQContextWrapper.init(activity);
    }

    public static Activity getActivity() {
        return SqAtyRef.getInstance().getActivity();
    }

    public static Context getApplicationContext() {
        return sAppContext;
    }

    private static void hookOldContext(@NonNull Context context) {
        try {
            Field field = com.sq.sdk.tool.util.SQContextWrapper.class.getDeclaredField("mAppContext");
            field.setAccessible(true);
            field.set(null, context);
        } catch (Throwable e) {
            SQLog.w("设置com.sq.sdk.tool.util.SQContextWrapper context失败");
        }
    }
}
