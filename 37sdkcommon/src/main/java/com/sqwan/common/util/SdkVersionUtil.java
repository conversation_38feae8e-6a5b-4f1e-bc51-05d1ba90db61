package com.sqwan.common.util;

import android.content.Context;

public class SdkVersionUtil {

    private static final String KEY_LAST_SDK_VERSION = "sq_last_version";

    /**
     * 判断是否为新版本
     */
    public static boolean isNewVersion(Context context) {
        String lastVersion = SpUtils.get(context).getString(KEY_LAST_SDK_VERSION);
        String sdkVersion = VersionUtil.getSdkVersion();
        return !sdkVersion.equals(lastVersion);

    }

    /**
     * 存储版本号
     */
    public static void updateVersion(Context context) {
        String lastVersion = SpUtils.get(context).getString(KEY_LAST_SDK_VERSION);
        String sdkVersion = VersionUtil.getSdkVersion();
        if (!sdkVersion.equals(lastVersion)) {
            SpUtils.get(context).put(KEY_LAST_SDK_VERSION, sdkVersion);
        }
    }

}
