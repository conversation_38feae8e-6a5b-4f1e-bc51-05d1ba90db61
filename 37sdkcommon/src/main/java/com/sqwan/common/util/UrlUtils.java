package com.sqwan.common.util;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UrlUtils {

    public static class UrlEntity {
        /**
         * 基础url
         */
        public String baseUrl;
        /**
         * url参数
         */
        public Map<String, String> params;
    }

    public static UrlEntity parse(String url) {
        UrlEntity entity = new UrlEntity();
        if (url == null) {
            return entity;
        }
        url = url.trim();
        if (url.equals("")) {
            return entity;
        }
        String[] urlParts = url.split("\\?");
        entity.baseUrl = urlParts[0];
        //没有参数
        if (urlParts.length == 1) {
            return entity;
        }
        //有参数
        String[] params = urlParts[1].split("&");
        entity.params = new HashMap<>();
        for (String param : params) {
            String[] keyValue = param.split("=");
            if (keyValue[0] != null && keyValue[1] != null) {
                entity.params.put(keyValue[0], keyValue[1]);
            }
        }
        return entity;
    }

    /**
     * 通过url获取指定参数对应的值
     * @param urlStr
     * @param paramName
     * @return
     */
    public static String readValueFromUrlStrByParamName(String urlStr, String paramName) {
        if (!TextUtils.isEmpty(urlStr)) {
            int index = urlStr.indexOf("?");
            String temp = (index >= 0 ? urlStr.substring(index + 1) : urlStr);
            String[] keyValue = temp.split("&");
            String destPrefix = paramName + "=";
            for (String str : keyValue) {
                if (str.indexOf(destPrefix) == 0) {
                    return str.substring(destPrefix.length());
                }
            }
        }
        return "";
    }

    public static String appendUrlParams(String url, Map<String,String> params) {
        if (url == null) {
            return "";
        }
        if (params == null) {
            return url;
        }

        List<String> newParams = new ArrayList<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            newParams.add(String.format("%s=%s", entry.getKey(), entry.getValue()));
        }

        String joinParams = join(newParams,"&");

        if (url.indexOf("?") > 0) {
            url += "&" + joinParams;
        } else {
            url += "?" + joinParams;
        }
        return url;
    }

    private static String join(List<String> strList, String joint) {
        if (strList == null || strList.isEmpty()) {
            return "";
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(strList.get(0));
        for (int i = 1; i < strList.size(); i++) {
            String str = strList.get(i);
            if (TextUtils.isEmpty(str)) {
                continue;
            }
            stringBuilder.append(joint);
            stringBuilder.append(strList.get(i));
        }

        return stringBuilder.toString();
    }
}