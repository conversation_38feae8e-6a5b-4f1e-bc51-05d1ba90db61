package com.sqwan.common.widget.radiobutton;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.sqwan.common.util.LogUtil;

/**
 * <AUTHOR>
 * @date 2020-05-20
 */
public class SQRadioGroup extends LinearLayout {


    private int mCheckedId = -1;

    private SQRadioButton.OnCheckedChangeListener mChildOnCheckedChangeListener;

    private boolean mProtectFromCheckedChange = false;
    private OnCheckedChangeListener mOnCheckedChangeListener;

    private PassThroughHierarchyChangeListener mPassThroughListener;


    public SQRadioGroup(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SQRadioGroup(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mChildOnCheckedChangeListener = new CheckedStateTracker();
        mPassThroughListener = new PassThroughHierarchyChangeListener();
        super.setOnHierarchyChangeListener(mPassThroughListener);
    }


    public void check(int id) {
        if (id != -1 && (id == mCheckedId)) {
            return;
        }

        if (mCheckedId != -1) {
            setCheckedStateForView(mCheckedId, false);
        }

        if (id != -1) {
            setCheckedStateForView(id, true);
        }
        setCheckedId(id);
    }

    private void setCheckedId(int id) {
        LogUtil.i("current id is " + id);
        mCheckedId = id;
        if (mOnCheckedChangeListener != null) {
            mOnCheckedChangeListener.onCheckedChanged(this, mCheckedId);
        }
    }

    private void setCheckedStateForView(int viewId, boolean checked) {
        View checkedView = findViewById(viewId);
        if(checkedView instanceof SQRadioButton) {
            ((SQRadioButton)checkedView).setChecked(checked);
        }
    }

    public int getCheckedRadioButtonId() {
        return mCheckedId;
    }


    public void setOnCheckedChangeListener(OnCheckedChangeListener mOnCheckedChangeListener) {
        this.mOnCheckedChangeListener = mOnCheckedChangeListener;
    }

    public interface OnCheckedChangeListener {

        public void onCheckedChanged(SQRadioGroup radioGroup, int checkedId);

    }

    private class CheckedStateTracker implements SQRadioButton.OnCheckedChangeListener {

        @Override
        public void onCheckedChanged(SQRadioButton radioButton, boolean isChecked) {
            if(mProtectFromCheckedChange) {
                return;
            }
            mProtectFromCheckedChange = true;
            if(mCheckedId != -1) {
                setCheckedStateForView(mCheckedId, false);
            }
            mProtectFromCheckedChange = false;
            int id = radioButton.getId();
            setCheckedId(id);
        }
    }

    private class PassThroughHierarchyChangeListener implements ViewGroup.OnHierarchyChangeListener {


        @Override
        public void onChildViewAdded(View parent, View child) {
            if(parent == SQRadioGroup.this && child instanceof SQRadioButton) {
                int id = child.getId();
                if (id == View.NO_ID) {
                    id = View.generateViewId();
                    child.setId(id);
                }
                ((SQRadioButton)child).setOnCheckedChangeWidgetListener(
                        mChildOnCheckedChangeListener);
            }
        }

        @Override
        public void onChildViewRemoved(View parent, View child) {
            if(parent == SQRadioGroup.this && child instanceof SQRadioButton) {
                ((SQRadioButton)child).setOnCheckedChangeWidgetListener(null);
            }
        }
    }

}
