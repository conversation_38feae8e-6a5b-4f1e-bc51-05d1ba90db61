package com.sqwan.common.widget.radiobutton;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;

import com.sqwan.common.util.LogUtil;

/**
 * <AUTHOR>
 * @date 2020-05-20
 */
public class SQRadioButton extends RelativeLayout implements Checkable {


    private boolean mChecked;
    private boolean mBroadcasting;

    private OnCheckedChangeListener mOnCheckedChangeListener;
    private OnCheckedChangeListener mOnCheckedChangeWidgetListener;


    public SQRadioButton(Context context) {
        super(context);
    }

    public SQRadioButton(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SQRadioButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    public void setChecked(boolean checked) {
        if(mChecked != checked) {
            mChecked = checked;
            refreshDrawableState();
            if(mBroadcasting) {
                return;
            }
            mBroadcasting = true;
            if (mOnCheckedChangeListener != null) {
                mOnCheckedChangeListener.onCheckedChanged(this, mChecked);
            }
            if(mOnCheckedChangeWidgetListener != null) {
                mOnCheckedChangeWidgetListener.onCheckedChanged(this, mChecked);
            }
            mBroadcasting = false;
        }
    }


    @Override
    public void refreshDrawableState() {
        super.refreshDrawableState();
        for (int index = 0; index < getChildCount(); index++) {
            View child = getChildAt(index);
            if(child instanceof Checkable) {
                LogUtil.i("refresh image view state");
                ((Checkable)child).setChecked(isChecked());
            }
        }
    }


    @Override
    public boolean isChecked() {
        return mChecked;
    }

    @Override
    public boolean performClick() {
        LogUtil.i("sq radio button perform click");
        toggle();
        return super.performClick();
    }


    @Override
    public void toggle() {
        setChecked(!mChecked);
    }

    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        this.mOnCheckedChangeListener = listener;
    }

    void setOnCheckedChangeWidgetListener(OnCheckedChangeListener listener) {
        mOnCheckedChangeWidgetListener = listener;
    }

    public interface OnCheckedChangeListener {

        void onCheckedChanged(SQRadioButton radioButton, boolean isChecked);
    }


}
