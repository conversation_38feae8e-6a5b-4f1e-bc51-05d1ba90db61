package com.sqwan.common.widget.radiobutton;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;

/**
 * <AUTHOR>
 * @date 2020-05-21
 */
public class SQRadioView extends ImageView implements Checkable {


    private boolean mChecked;

    private static final int[] CHECKED_STATE_SET = {
            android.R.attr.state_checked
    };

    public SQRadioView(Context context) {
        super(context);
    }

    public SQRadioView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SQRadioView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }


    @Override
    public int[] onCreateDrawableState(int extraSpace) {
        final int[] drawableState = super.onCreateDrawableState(extraSpace + 1);
        if (mChecked) {
            mergeDrawableStates(drawableState, CHECKED_STATE_SET);
        }
        return drawableState;
    }


    @Override
    public void setChecked(boolean checked) {
        this.mChecked = checked;
        refreshDrawableState();
    }

    @Override
    public boolean isChecked() {
        return mChecked;
    }

    @Override
    public void toggle() {

    }
}
