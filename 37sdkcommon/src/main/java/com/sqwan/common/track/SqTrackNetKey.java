package com.sqwan.common.track;

public interface SqTrackNetKey {
    //请求方式
    String method = "method";
    //当前请求次数
    String currentRequestCount = "current_request_count";
    //原url
    String formerUrl = "formerUrl";
    //最终请求的url
    String requestUrl = "requestUrl";
    //是否发生了dns
    String dnsOccur = "dns_occur";
    //请求过程中所有请求过的url
    String requestUrls = "requestUrls";
    //是否请求成功
    String isSuccess = "is_success";
    //状态码
    String code = "statuscode";
    //信息
    String msg = "msg";
    //所有请求过程中每次请求的时长
    String spendTimes = "cost";
    //请求总耗时
    String spendTime = "cost_total";
    //后端返回的数据
    String responseData = "responseData";
    //domain 请求接口
    String domain = "domain";
    //协议
    String protocol = "protocol";
    //路径
    String path = "path";
    //请求参数
    String params = "params";
    //uri 路径
    String uri = "uri";
    //server_ip
    String server_ip = "server_ip";
    //请求的完整url
    String server_url = "server_url";

}
