package com.sqwan.common.track;

public interface SqTrackNetDnsKey {
    //域名
    String domain = "domain";
    //路径
    String uri = "uri";
    //请求协议 http/https
    String protocol = "protocol";
    //请求方法 get/post
    String method = "method";
    //请求参数
    String request = "request";
    //状态码
    String statusCode = "statusCode";
    //当前请求次数
    String currentRequestCount = "currentRequestCount";
    //当前请求耗时
    String cost = "cost";
    //总耗时
    String costTotal = "costTotal";
    //服务端id，若为原域就填host
    String server_ip = "server_ip";
    //网络请求信息 请求成功/失败原因
    String msg = "msg";
    //扩展参数
    String extra = "extra";
    //类型 dns服务接口/单域名接口/多域名接口
    String type = "type";
    //请求的url
    String requestUrl = "requestUrl";
    //服务端返回数据
    String responseData = "responseData";

}
