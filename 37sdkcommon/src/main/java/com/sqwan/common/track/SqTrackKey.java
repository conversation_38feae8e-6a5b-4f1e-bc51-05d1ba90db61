package com.sqwan.common.track;

public interface SqTrackKey {
    //错误码
    String fail_code = "fail_code";
    // 二级错误码
    String sub_error_code = "sub_err_code";
    //失败原因
    String reason_fail = "reason_fail";
    // 二级错误信息
    String sub_error_msg = "sub_err_msg";
    //登录账号类型。1：账号密码、2：登录手机
    String login_type = "login_type";
    //登录方式。1：账号密码、2：手机号+验证码、3：本机号码一键进入、4：自动登录、5、手机号+密码、6、历史账号快速登录、0：未知
    String login_way = "login_way";
    //用户id
    String uid = "uid";
    //按钮id
    String btn_id = "btn_id";
    //按钮描述
    String btn_ext = "btn_ext";
    //页面id
    String view_id = "view_id";
    //页面名称
    String view_name = "view_name";
    //悬浮球id
    String ball_id = "ball_id";
    //悬浮球name
    String ball_name = "ball_name";
    //悬浮球url
    String ball_url = "ball_url";
    //是否有红点
    String ball_red_dot = "ball_red_dot";
    //悬浮球红点数量
    String entry_red_dot = "entry_red_dot";
    //实名认证地址
    String certification_url = "certification_link";
    //人脸认证id
    String face_verify_id = "face_verify_id";
    //登出场景
    String logout_type = "logout_type";
    //弹窗id
    String push_id = "push_id";
    //跳转链接
    String push_link = "push_link";
    //返回场景类型：激活后，登录后（进服后），支付前，支付后，sdk登录（账号登录成功），进服
    String push_scene_id = "push_scene_id";
    // 支付session
    String pay_session = "pay_session";
    // 研发传入的订单id
    String cp_order_id = "cp_order_id";
    //订单id
    String order_id = "order_id";
    // 第三方订单id
    String third_order_id = "third_order_id";
    //初始金额。订单的原始金额，单位元，精确0.01
    String order_amount = "order_amount";
    //实付金额。实际需要支付的订单金额，单位元，精确0.01
    String pay_amount = "pay_amount";
    //充值渠道。1：安卓H5、2：安卓原生、3：iOS原生、4：iOS切支付、5：iOS钱包、6：其他、0：未知
    String pay_channels = "pay_channels";
    //支付页版本。例如：1.0.0
    String pay_version = "pay_version";
    //支付方式。1：微信、2：支付宝、3：钱包、4：花呗、5：其他、0：未知
    String pay_method = "pay_method";
    String pay_method_name = "pay_method_name";
    //是否使用代金券
    String is_vouchers = "is_vouchers";
    //代金券ID。使用的代金券ID，为空传0
    String vouchers_id = "vouchers_id";
    //订单类型。1：正式、2：测试、3：内服、4：其他、0：未知
    String order_type = "order_type";
    //更新链接
    String update_uurl = "update_uurl";
    //插件id
    String plug_id = "plug_id";
    //插件版本号
    String plug_version = "plug_version";
    //当前插件版本号
    String plug_current_version = "plug_current_version";
    //插件链接
    String plug_link = "plug_link";
    //插件 md5 值
    String plug_hash = "plug_hash";
    //插件类型(全量下发/部分下发)
    String plug_type = "plug_type";
    //最近一次加载的插件版本号
    String plug_latest_version = "plug_latest_version";
    // 插件加载耗时时间
    String PLUG_CONSUMING_TIME = "plug_consuming_time";
    // 过时的类名
    String deprecated_class_name = "deprecated_class_name";
    // 过时被调用的堆栈
    String deprecated_stack_trace = "deprecated_stack_trace";

    // 出现错误的类型
    String error_type = "error_type";
    // 出现错误的类名
    String error_class_name = "error_class_name";
    // 出现错误被调用的堆栈
    String error_stack_trace = "error_stack_trace";
}
