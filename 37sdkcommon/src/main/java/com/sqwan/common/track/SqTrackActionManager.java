package com.sqwan.common.track;

import android.content.Context;

import java.util.HashMap;

/**
 * 埋点管理类
 *
 * <AUTHOR>
 * @deprecated 已过时，请使用 {@link com.sqwan.common.track.SqTrackActionManager2} 代替
 */
@Deprecated
public class SqTrackActionManager {

    private static String TAG = "TrackActionManagerer";
    private static SqTrackActionManager sInstance;

    private Context mContext;
    private String mSdkVersion;
//    private static final String TRACK_URL = "http://track." + MultiSdkManager.APP_HOST + "/api/event/";

    private SqTrackActionManager() {
    }


    public static SqTrackActionManager getInstance() {
        if (sInstance == null) {
            sInstance = new SqTrackActionManager();
        }
        return sInstance;
    }

    public void init(Context context, String sqSdkVersion) {
        mContext = context.getApplicationContext();
        mSdkVersion = sqSdkVersion;
    }

    /**
     * 默认为非登录状态
     *
     * @param action
     */
    public void trackAction(SqTrackAction action) {
        trackAction(action, SqTrackUtil.getLogined(mContext));
    }

    /**
     * @param action
     * @param isLogin 登录状态，如果已经登录，传入用户id和用户名
     */
    public void trackAction(final SqTrackAction action, boolean isLogin) {
        trackAction(action, isLogin, (HashMap<String, String>) null);
    }

    public void trackAction(SqTrackAction action, HashMap<String, String> extraPram) {
        trackAction(action, extraPram, "");
    }


    public void trackAction(SqTrackAction action, String ext) {
        HashMap<String, String> map = new HashMap<>();
        map.put("ext", ext);
        trackAction(action, SqTrackUtil.getLogined(mContext), map);
    }

    /**
     * @deprecated please use {@link #trackAction(SqTrackAction, boolean, HashMap)}
     */
    @Deprecated
    public void trackAction(final SqTrackAction action, boolean isLogin, String ext) {
        HashMap<String, String> map = new HashMap<>();
        map.put("ext", ext);
        trackExtAction(action, isLogin, map);
    }

    private void trackExtAction(final SqTrackAction action, boolean isLogin, HashMap<String, String> extMap) {
        trackAction(action, isLogin, null, extMap);
    }

    public void trackAction(final SqTrackAction action, HashMap<String, String> extraPram, String ext) {
        HashMap<String, String> extMap = new HashMap<>();
        extMap.put("ext", ext);
        trackAction(action, SqTrackUtil.getLogined(mContext), extraPram, extMap);
    }

    public void trackAction(final SqTrackAction action, boolean isLogin, HashMap<String, String> extraPram) {
        trackAction(action, isLogin, extraPram, null);
    }

    /**
     * 带参数上报
     */
    public void trackAction(final SqTrackAction action, boolean isLogin, HashMap<String, String> extraPram, HashMap<String, String> extMap) {
        //3.7.8.5版本去除该旧埋点上报
    }


    /**
     * 点击按钮埋点
     *
     * @param btnId
     * @param btnTxt
     */
    public void trackBtn(String btnId, String btnTxt) {
        HashMap<String, String> extra = new HashMap<>();
        extra.put("btn_id", btnId);
        extra.put("btnTxt", btnTxt);
        extra.putAll(roleTrackParam());
        trackAction(SqTrackAction.SDK_BTN_CLICK, extra);
    }

    public HashMap<String, String> roleTrackParam() {
        HashMap<String, String> roleParams = new HashMap<>();
        roleParams.put("role_id", SqTrackUtil.getRoleid(mContext));
        roleParams.put("role_name", SqTrackUtil.getRolename(mContext));
        roleParams.put("role_level", SqTrackUtil.getRolelevel(mContext));
        roleParams.put("vip_level", SqTrackUtil.getVipLevel(mContext));
        roleParams.put("server_id", SqTrackUtil.getServerid(mContext));
        roleParams.put("server_name", SqTrackUtil.getServerName(mContext));
        return roleParams;
    }

}
