package com.sqwan.common.track;

public interface SqTrackBtn {

    interface SqTrackBtnId {
        String changeAccount = "btn01";
        String forgetPwd = "btn02";
        String logoff = "btn03";
        String register = "btn04";
        String login = "btn05";
        String quickStart = "btn06";
        String agreement = "btn07";
        String getPhoneCode = "btn08";
        String faceVerify = "btn09";
        String closeRegisterPage = "btn10";
        String closeLoginPage = "btn11";
        String accountRegister = "btn12";
        String accountLogin = "btn13";
        String modifyPersonInfo = "btn14";
        String verifyPhoneCode = "btn15";
        String updateCancel = "btn16";
        String updateStart = "btn17";
        String updateClose = "btn18";
        String updateForceStart = "btn19";
        String loginHelp = "btn20";
        String aliLogin = "btn21";
        String share_wechat = "btn22";
        String share_moment = "btn23";
        String share_qq = "btn24";
        String share_system = "btn25";
        String share_open_platform = "btn26";
        String wechat = "btn27";
    }

    interface SqTrackBtnExt {
        String CHANGE_ACCOUNT_FLOAT_VIEW = "悬浮窗切换账号";
        String CHANGE_ACCOUNT_CP = "研发在游戏设置里调用SDK切换账号接口";
        String CHANGE_ACCOUNT_AUTH = "实名认证切换账号";
        String CHANGE_ACCOUNT_FACE_TIP = "人脸识别弹窗切换账号";
        String CHANGE_ACCOUNT_FACE_FAIL = "人脸识别失败切换账号";
        String CHANGE_ACCOUNT_FACE_CANCEL = "取消人脸识别切换账号";
        String CHANGE_ACCOUNT_USER_CENTER = "个人中心切换账号";
        String CHANGE_ACCOUNT_AUTO_LOGIN = "自动登录切换账号";
        String forgetPwd = "忘记密码";
        String logoff = "注销";
        String register = "注册";
        String login = "登录";
        String quickStart = "快速游戏";
        String agreement = "同意用户协议";
        String getPhoneCode = "获取短信验证码";
        String faceVerify = "人脸识别";
        String closeRegisterPage = "注册页关闭";
        String closeLoginPage = "登录页关闭";
        String accountRegister = "立即注册";
        String ACCOUNT_LOGIN = "账号登录";
        String modifyPersonInfo = "修改个人信息点击";
        String verifyPhoneCode = "输入验证码完成，请求验证";
        String updateCancel = "普更取消";
        String updateStart = "普更开始";
        String updateClose = "普更关闭";
        String updateForceStart = "强更开始";
        String loginHelp = "登录问题";
        String aliLogin = "点击一键进入";
        String share_wechat = "微信分享";
        String share_moment = "朋友圈分享";
        String share_qq = "qq分享";
        String share_system = "系统分享";
        String share_open_platform = "分享确认页面打开分享客户端";
        String wechat = "微信登录";
    }
}
