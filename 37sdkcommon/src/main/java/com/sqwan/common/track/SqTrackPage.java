package com.sqwan.common.track;

public interface SqTrackPage {
    interface SqTrackViewId {
        String ali_fast = "view01";
        String phone_input = "view02";
        String phone_code = "view03";
        String phone_pwd = "view04";
        String account_login = "view05";
        String account_register = "view06";
        String face = "view07";
        String modifyUserInfo = "view08";
        String update = "view09";
        String updateForce = "view10";
        String history_account = "view11";
        String share_preview = "view12";
        String share_confirm = "view13";
        String wechat_login = "view14";
        String permission_scene = "view15";
    }

    interface SqTrackViewName {
        String ali_fast = "闪验页面曝光";
        String phone_input = "手机号获取验证码页面曝光";
        String phone_code = "手机号填写验证码页面曝光";
        String phone_pwd = "手机号填写密码页面曝光";
        String ACCOUNT_LOGIN = "账号密码登录页曝光";
        String ACCOUNT_REGISTER = "账号密码注册页曝光";
        String face = "人脸识别弹窗";
        String modifyUserInfo = "修改个人信息曝光";
        String update = "普更弹窗";
        String updateForce = "强更弹窗";
        String HISTORY_ACCOUNT = "历史账号登录页面";
        String share_preview = "分享预览界面";
        String share_confirm = "分享确认界面";
        String wechat_login = "微信登录页面曝光";
        String permission_scene = "权限场景说明弹窗";
    }
}
