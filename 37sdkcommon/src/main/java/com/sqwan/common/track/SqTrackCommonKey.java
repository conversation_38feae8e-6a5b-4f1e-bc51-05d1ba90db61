package com.sqwan.common.track;

public interface SqTrackCommonKey {
    String os = "os";
    String os_version = "os_version";
    String country = "country";
    String country_code = "country_code";
    String province = "province";
    String city = "city";
    String network_type = "network_type";
    String carrier = "carrier";
    String ip = "ip";
    String phone_brand = "phone_brand";
    String phone_model = "phone_model";
    String pixel = "pixel";
    String screen_height = "screen_height";
    String screen_width = "screen_width";
    String ram = "ram";
    String sd_memory = "sd_memory";
    String cpu_hardware = "cpu_hardware";
    String cpu_Ghz = "cpu_Ghz";
    String cpu_core = "cpu_core";
    String cpu_is_x86 = "cpu_is_x86";
    String event = "event";
    String event_time = "event_time";
    String mac = "mac";
    String imei = "imei";
    String dev = "dev";
    String uuid = "uuid";
    String android_id = "android_id";
    String oaid = "oaid";
    String sim = "sim";
    String gid = "gid";
    String refer = "refer";
    String pid = "pid";
    String cid = "cid";
    String pgid = "pgid";
    String tgid = "tgid";
    String sversion = "sversion";
    String gwversion = "gwversion";
    String version = "version";
    String versionName = "version_name";
    String pluginVersion = "plugin_version";
    String targetVersion = "target_version";
    String last_os_update_ts = "last_os_update_ts";
    String apk_name = "apk_name";
    String game_name = "game_name";
    String request_liveid = "request_liveid";
    String request_id = "request_id";
    String install_time = "install_time";
    String last_update_time = "last_update_time";
    String uid = "uid";
    String uname = "uname";
    String role_id = "role_id";
    String role_name = "role_name";
    String role_level = "role_level";
    String vip_level = "vip_level";
    String server_id = "server_id";
    String server_name = "server_name";
    String scut = "scut";
    String ext = "ext";
    String isSimulator = "isSimulator";
    //是否使用了代理
    String isProxy = "isProxy";
    //原始的sdk版本号（即出包时候的sdk版本号）
    String original_sversion = "original_sversion";
    String cpu_abi = "cpu_abi";
    String isRoot = "isRoot";
    String cpu_info = "cpu_info";
    String channel_sdk_version = "channel_sdk_version";
    String channel_name = "channel_name";
}
