package com.sqwan.common.track;

import android.content.Context;
import android.text.TextUtils;

import com.sq.tool.logger.SQLog;
import android.util.Log;
import com.sqwan.common.mod.track.TrackModManager2;
import com.sqwan.common.util.LogUtil;

import org.json.JSONObject;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * 埋点管理类
 *
 * <AUTHOR>
 */
public class SqTrackActionManager2 {

    private static String TAG = "SqTrackActionManager2";
    private static SqTrackActionManager2 sInstance;
    private Context mContext;
    private boolean isAuthCheck = false;

    /**
     * adb shell setprop log.tag.sysdk.cptest.track debug
     */
    public static boolean sIsCpTest = Log.isLoggable("sysdk.cptest.track", Log.DEBUG);
    private SqTrackActionManager2() {
    }

    public static SqTrackActionManager2 getInstance() {
        if (sInstance == null) {
            sInstance = new SqTrackActionManager2();
        }
        return sInstance;
    }


    public void init(Context context) {
        mContext = context.getApplicationContext();
    }

    public void setAuthCheck(boolean authCheck) {
        isAuthCheck = authCheck;
    }

    public void trackAction(String event, String describe) {
        trackAction(event, describe, null);
    }

    public void trackAction(String event, String describe, HashMap<String, String> extraPram) {
        SqTrackAction2.sdk_expand.construct(event, describe);
        trackAction(SqTrackAction2.sdk_expand, extraPram);
    }

    public void trackAction(SqTrackAction2 action) {
        trackAction(action, null, null);
    }

    public void trackAction(SqTrackAction2 action, Map<String, String> extraPram) {
        trackAction(action, extraPram, null);
    }

    public void trackActionExt(SqTrackAction2 action, HashMap<String, String> extMap) {
        trackAction(action, null, extMap);
    }

    /*
     * 带控制的上报，用于母包接入助手
     * */
    public void trackActionCPTest(SqTrackAction2 action, Map<String, String> extraPram) {
        if (!sIsCpTest) {
            return;
        }
        trackAction(action, extraPram);
    }

    public void trackActionCPTest(SqTrackAction2 action) {
        trackActionCPTest(action, null);
    }
    /**
     * 错误上报的方法
     */
    public void trackErrorReport(Object object, String errorType, Throwable throwable) {
        if (object == null) {
            return;
        }
        String className = object.getClass().getName();

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        String stackTrace = stringWriter.toString();

        HashMap<String, String> extraPram = new HashMap<>();
        extraPram.put(SqTrackKey.error_type, errorType);
        extraPram.put(SqTrackKey.error_class_name, className);
        extraPram.put(SqTrackKey.error_stack_trace, stackTrace);
        trackAction(SqTrackAction2.error_report, extraPram);
    }

    /**
     * 打点过时类的方法
     */
    public void trackDeprecatedCall(Object object) {
        if (object == null) {
            return;
        }
        String className = object.getClass().getName();

        Throwable throwable = new Throwable();
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        throwable.printStackTrace(printWriter);
        String stackTrace = stringWriter.toString();

        HashMap<String, String> extraPram = new HashMap<>();
        extraPram.put(SqTrackKey.deprecated_class_name, className);
        extraPram.put(SqTrackKey.deprecated_stack_trace, stackTrace);
        trackAction(SqTrackAction2.deprecated_call, extraPram);
    }

    /**
     * @param action    事件
     * @param extraPram 除了公共字段外的其他字段
     * @param extMap    ext：扩展属性的字段
     */
    public void trackAction(final SqTrackAction2 action, Map<String, String> extraPram, HashMap<String, String> extMap) {
        if (action == null) {
            return;
        }
        trackAction(action.getEvent(), extraPram, extMap);
    }

    public void trackAction(String event, Map<String, String> extraPram, HashMap<String, String> extMap) {
        if (mContext == null) {
            LogUtil.d(TAG, "please call init first!");
            return;
        }
        if (event == null) {
            LogUtil.d(TAG, "the track action is null");
            return;
        }

        Map<String, String> trackParams = new HashMap<>();
        trackParams.put(SqTrackCommonKey.event, event);
        if (extraPram != null && !extraPram.isEmpty()) {
            trackParams.putAll(extraPram);
        }
        if (extMap != null && !extMap.isEmpty()) {
            JSONObject extJson = new JSONObject();
            for (Map.Entry<String, String> entry : extMap.entrySet()) {
                if (TextUtils.isEmpty(entry.getKey()))
                    continue;
                String value = TextUtils.isEmpty(entry.getValue()) ? "" : entry.getValue();
                try {
                    extJson.put(entry.getKey(), value);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            trackParams.put(SqTrackCommonKey.ext, extJson.toString());
        }
        try {
            TrackModManager2.track(event, trackParams);
        } catch (Throwable e) {
            SQLog.e("上报异常, event=" + event, e);
        }
    }

    public void flush(){
        try {
            TrackModManager2.flush();
        } catch (Throwable e) {
            /* no-op */
        }
    }


    /**
     * 点击按钮埋点
     *
     * @param btnId
     * @param btnExt
     */
    public void trackBtn(String btnId, String btnExt) {
        HashMap<String, String> extra = new HashMap<>();
        extra.put(SqTrackKey.btn_id, btnId);
        extra.put(SqTrackKey.btn_ext, btnExt);
        trackAction(SqTrackAction2.sdk_btn_click, extra);
    }

    /**
     * 点击按钮埋点
     */
    public void trackBtn(String btnId, String btnExt, Map<String, String> params) {
        HashMap<String, String> extra = new HashMap<>();
        extra.put(SqTrackKey.btn_id, btnId);
        extra.put(SqTrackKey.btn_ext, btnExt);
        extra.putAll(params);
        trackAction(SqTrackAction2.sdk_btn_click, extra);
    }

}
