package com.sqwan.common.route;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.logger.SQLog;
import java.util.ArrayList;
import java.util.List;

/**
 * 通过路由来打开其他模块的页面
 *
 * <AUTHOR>
 * @since 2024/4/24
 */
public class FunctionRouterManager implements FunctionRouter {

    private static volatile FunctionRouterManager sInstance;

    public static FunctionRouterManager getInstance() {
        if (sInstance == null) {
            synchronized (FunctionRouterManager.class) {
                if (sInstance == null) {
                    sInstance = new FunctionRouterManager();
                }
            }
        }

        return sInstance;
    }

    private FunctionRouterManager() {
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final List<FunctionRouter> mRouters = new ArrayList<>();

    public void register(FunctionRouter router) {
        if (router != null && !mRouters.contains(router)) {
            SQLog.i(TAG + "增加路由: " + router);
            mRouters.add(router);
        }
    }

    @Override
    public void call(@NonNull Activity activity, @NonNull String key, @Nullable Bundle bundle) {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            mHandler.post(() -> call(activity, key, bundle));
            return;
        }

        SQLog.d(TAG + "通过路由调用: (" + activity + ")" + key + ", data=" + bundle);
        boolean called = false;
        for (FunctionRouter router : mRouters) {
            if (router != null) {
                router.call(activity, key, bundle);
                called = true;
            }
        }
        if (!called) {
            SQLog.w(TAG + "无路由响应调用: (" + activity + ")" + key + ", data=" + bundle);
        }
    }
}
