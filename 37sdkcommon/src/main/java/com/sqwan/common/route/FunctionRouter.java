package com.sqwan.common.route;

import android.app.Activity;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2024/4/24
 */
public interface FunctionRouter {

    String TAG = "【Router】";

    String KEY_DATA = "data";

    interface Func {
        /**
         * 通用的阿里支付
         */
        String FUNC_UNIVERSAL_ALI_PAY = "func_universal_ali_pay";
    }

    void call(@NonNull Activity activity, @NonNull String key, @Nullable Bundle data);
}
