package com.sqwan.common.web;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff.Mode;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.support.annotation.NonNull;
import android.util.AttributeSet;
import android.widget.ImageView;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.msdk.config.MultiConfigManager;

/**
 * @author: gsp
 * @date: 2023/8/22
 * @desc: 放缩变圆的动画
 */
public class ScaleRoundAnimView extends ImageView {

    private static final String PROPERTY_RADIUS_X = "property_radius_x";
    private static final String PROPERTY_RADIUS_Y = "property_radius_y";
    private int mRadiusX = 0;
    private int mRadiusY = 0;
    private Paint mPaint;

    private int mBitmapWidth = 0;
    private int mBitmapHeight = 0;
    private RectF mAnimRect;

    private ExitAnimParams mExitAnimParams;

    private AnimFinishListener mAnimFinishListener;

    public ScaleRoundAnimView(Context context) {
        this(context, null);
    }

    public ScaleRoundAnimView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScaleRoundAnimView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    public void setParams(ExitAnimParams exitAnimParams) {
        mExitAnimParams = exitAnimParams;
    }

    private void init() {
        mPaint = new Paint();
        mPaint.setDither(true);
        mPaint.setAntiAlias(true);
    }


    @Override
    public void setImageBitmap(Bitmap bm) {
        mBitmapWidth = bm.getWidth();
        mBitmapHeight = bm.getHeight();
        super.setImageBitmap(getRoundedCornerBitmap(bm));
        BitmapShader shader = new BitmapShader(bm, BitmapShader.TileMode.CLAMP, BitmapShader.TileMode.CLAMP);
        mPaint.setShader(shader);
        mAnimRect = new RectF(0, 0, bm.getWidth(), bm.getHeight());
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawRoundRect(mAnimRect, mRadiusX, mRadiusY, mPaint);
    }


    private void scale() {
        int[] scaleLocation = new int[2];
        getLocationOnScreen(scaleLocation);
        //因为悬浮球的坐标是屏幕左上角，所以放缩动画也要以左上角来计算
        float pivotX = mExitAnimParams.x / (1 - ((float) mExitAnimParams.width) / mBitmapWidth) - scaleLocation[0];
        int offset = MultiConfigManager.getInstance().isLandscape() ? 0 : DisplayUtil.dip2px(getContext(), 34);
        float pivotY = mExitAnimParams.y / (1 - ((float) mExitAnimParams.height) / mBitmapHeight) - scaleLocation[1] - offset;
        this.setPivotX(pivotX);
        this.setPivotY(pivotY);
        AnimatorSet set = new AnimatorSet();
        PropertyValuesHolder propertyRadiusX = PropertyValuesHolder.ofInt(PROPERTY_RADIUS_X, 0, mBitmapWidth / 2);
        PropertyValuesHolder propertyRadiusY = PropertyValuesHolder.ofInt(PROPERTY_RADIUS_Y, 0, mBitmapHeight / 2);
        ValueAnimator animator = new ValueAnimator();
        animator.setValues(propertyRadiusX, propertyRadiusY);
        animator.setDuration(300);
        animator.addUpdateListener(animation -> {
            mRadiusX = (int) animation.getAnimatedValue(PROPERTY_RADIUS_X);
            mRadiusY = (int) animation.getAnimatedValue(PROPERTY_RADIUS_Y);
            invalidate();
        });
        set.playTogether(
            ObjectAnimator.ofFloat(ScaleRoundAnimView.this, "scaleX", 1, mExitAnimParams.width / (float) mBitmapWidth)
                .setDuration(300),
            ObjectAnimator.ofFloat(ScaleRoundAnimView.this, "scaleY", 1, mExitAnimParams.height / (float) mBitmapHeight)
                .setDuration(300),
            animator
        );

        set.addListener(new AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                round();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        set.start();
    }

    private void round() {
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.play(ObjectAnimator.ofFloat(this, "alpha", 1f, 0f).setDuration(500));
        animatorSet.addListener(new AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mAnimFinishListener != null) {
                    mAnimFinishListener.onAnimFinish();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.start();
    }

    public void start() {
        post(this::scale);
    }


    public void setAnimFinishListener(AnimFinishListener animFinishListener) {
        mAnimFinishListener = animFinishListener;
    }

    public interface AnimFinishListener {
        void onAnimFinish();
    }


    /*
     * 转圆角矩形
     * */
    private Bitmap getRoundedCornerBitmap(Bitmap bitmap) {
        Bitmap roundBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Config.ARGB_8888);
        Canvas canvas = new Canvas(roundBitmap);
        Paint paint = new Paint();
        Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
        RectF rectF = new RectF(rect);
        paint.setAntiAlias(true);
        canvas.drawARGB(0, 0, 0, 0);
        canvas.drawRoundRect(rectF, 100, 100, paint);
        paint.setXfermode(new PorterDuffXfermode(Mode.SRC_IN));
        canvas.drawBitmap(bitmap, rect, rect, paint);
        return roundBitmap;
    }


}