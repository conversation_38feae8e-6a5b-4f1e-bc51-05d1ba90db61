package com.sqwan.common.web;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import com.parameters.bean.SystemShareBean;
import com.parameters.share.IShareInfo;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.share.SharePlatform;
import com.parameters.share.ShareTextInfo;
import com.parameters.share.ShareWebInfo;
import com.parameters.share.WebShare;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.share.IShareMod;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.PermissionSimpleHelper;
import com.sqwan.common.util.PermissionSimpleHelper.OnPermissionCallback;
import com.sqwan.common.webview.SQCommonJsInterface;


public class WebShareUtil {
    /**
     * 解析前端传过来的分享数据json
     *
     * @param json
     */
    public static void parseJsShare(final Context context, String json) {
        parseJsShare(context, json, null);
    }

    public static void parseJsShare(final Context context, String json, IShareResultListener listener) {
        parseJsShare(context, json, null, listener);
    }

    /**
     * 解析前端传过来的分享数据json
     */
    public static void parseJsShare(final Context context, String json, Bitmap bitmap, IShareResultListener listener) {
        final WebShare webShare = WebShare.parseFromJson(json);
        Runnable runnable = () -> {
            ShareMessage shareMessage = new ShareMessage();
            shareMessage.setSkipPreview(true);
            shareMessage.setPlatform(parsePlatform(webShare));
            shareMessage.setShareMessage(parseShareMessage(webShare));

            IShareMod shareMod = ModHelper.get(IShareMod.class);
            if (shareMod == null) {
                if (listener != null) {
                    listener.onFailture(404, "分享内部错误，请尝试重启一下应用");
                }
                return;
            }

            if (!(shareMessage.getShareMessage() instanceof ShareImageInfo)) {
                shareMod.share(shareMessage, listener);
                return;
            }

            PermissionSimpleHelper.requestPermission(
                PermissionSimpleHelper.STORAGE_PERMISSION,
                PermissionSimpleHelper.STORAGE_PERMISSION_NAME,
                PermissionSimpleHelper.STORAGE_PERMISSION_BY_SHARE, new OnPermissionCallback() {
                    @Override
                    public void onGranted() {
                        shareMod.share(shareMessage, listener);
                    }

                    @Override
                    public void onDenied() {
                        if (listener == null) {
                            return;
                        }
                        listener.onFailture(1002, "分享图片失败，没有" + PermissionSimpleHelper.STORAGE_PERMISSION_NAME);
                    }
                });
        };
        if (bitmap != null) {
            webShare.setBitmap(bitmap);
            runnable.run();
        } else {
            AsyncImageLoader loader = new AsyncImageLoader(context);
            loader.loadDrawable(webShare.getImg(), null, (imageDrawable, imageView, imageUrl) -> {
                webShare.setBitmap(imageDrawable);
                runnable.run();
            });
        }
    }

    /**
     * 根据js传的分享平台转换成分享对应的平台
     *
     * @param webShare 分享实体
     * @return
     */
    private static int parsePlatform(WebShare webShare) {
        if (webShare == null || TextUtils.isEmpty(webShare.getWay())) {
            return -1;
        }
        switch (webShare.getWay()) {
            case WebShare.JS_SHARE_CHANNEL_WECAHT:
                return SharePlatform.WECHAT;
            case WebShare.JS_SHARE_CHANNEL_MOMENT:
                return SharePlatform.WECHAT_MOMENT;
            case WebShare.JS_SHARE_CHANNEL_QQ:
                return SharePlatform.QQ;
            default:
                return -1;
        }
    }

    /**
     * 根据js传的分享类型转换成分享对应的分享实体
     *
     * @param webShare
     * @return
     */
    public static IShareInfo parseShareMessage(WebShare webShare) {
        switch (webShare.getType()) {
            case SQCommonJsInterface.JS_SHARE_TYPE_IMG:
                ShareImageInfo shareImageMessage = new ShareImageInfo();
                shareImageMessage.setBitmap(webShare.getBitmap());
                return shareImageMessage;
            case SQCommonJsInterface.JS_SHARE_TYPE_H5:
                ShareWebInfo shareWebMessage = new ShareWebInfo();
                shareWebMessage.setThumbBmp(webShare.getBitmap());
                shareWebMessage.setDesc(webShare.getDesc());
                shareWebMessage.setTitle(webShare.getTitle());
                shareWebMessage.setPageUrl(webShare.getLandingPageUrl());
                return shareWebMessage;
            default:
                return null;
        }
    }

    private static IShareInfo parseShareMessage(SystemShareBean systemShareBean) {
        switch (systemShareBean.shareType) {
            case SQCommonJsInterface.JS_SHARE_TYPE_IMG:
                ShareImageInfo shareImageMessage = new ShareImageInfo();
                shareImageMessage.setBitmap(systemShareBean.bitmap);
                return shareImageMessage;
            case SQCommonJsInterface.JS_SHARE_TYPE_H5:
                ShareWebInfo shareWebMessage = new ShareWebInfo();
                shareWebMessage.setThumbBmp(systemShareBean.bitmap);
                shareWebMessage.setTitle(systemShareBean.shareTitle);
                shareWebMessage.setPageUrl(systemShareBean.shareLinkUrl);
                return shareWebMessage;
            case SQCommonJsInterface.JS_SHARE_TYPE_TEXT:
                ShareTextInfo shareTextInfo = new ShareTextInfo();
                shareTextInfo.setText(systemShareBean.shareText);
                return shareTextInfo;
            default:
                return null;
        }
    }
}
