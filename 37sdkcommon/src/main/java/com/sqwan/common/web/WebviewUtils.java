package com.sqwan.common.web;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * <AUTHOR>
 * @date 2020-05-13
 */
public class WebviewUtils {

    public static final String PreName = "sq_webview_pf";


    /**某个链接的加载次数记录
     * @param context
     * @param url
     * @param count
     */
    public static void setUrlLoadCount(Context context, String url, int count){
        SharedPreferences uiState = context.getSharedPreferences(PreName, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putInt("count"+url, count);
        editor.commit();
    }

    public static int getUrlLoadCount(Context context,String url){
        SharedPreferences uiState = context.getSharedPreferences(PreName, Context.MODE_PRIVATE);
        return uiState.getInt("count"+url, 1);
    }

    public static void setUrlLoadTime(Context context,String url,long time){
        SharedPreferences uiState = context.getSharedPreferences(PreName, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = uiState.edit();
        editor.putLong("time"+url, time);
        editor.commit();
    }

    public static long getUrlLoadTime(Context context,String url){
        SharedPreferences uiState = context.getSharedPreferences(PreName, Context.MODE_PRIVATE);
        return uiState.getLong("time"+url, 1);
    }

    /**判断是否一次性加载完成
     * 1.当前时间减去开始时间>超时时间，则不算，因为超时了。
     * 2.当前时间减去开始时间<0，则不算，因为已经到下一次加载了。
     * @param context
     * @param url
     * @param timeout
     * @return
     */
    public static boolean isLoadOneTime(Context context,String url,long timeout){
        long delta = System.currentTimeMillis() - getUrlLoadTime(context, url);
        System.out.println("webview loading time="+delta);
        if (delta <= timeout && delta>0) {
            return true;
        }else {
            return false;
        }
    }

    public static void clearWebviewPrefs(Context context){
        SharedPreferences uiState = context.getSharedPreferences(PreName, Context.MODE_PRIVATE);
        uiState.edit().clear().commit();
    }
}
