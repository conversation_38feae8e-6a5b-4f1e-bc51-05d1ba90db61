package com.sqwan.common.web;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.sqwan.common.util.SqResUtils;

public class WebViewToolBar extends LinearLayout {

    private View backBar, forwardBar, refreshBar, closeBar;
    private ImageView ivBackBar, ivForwardBar, ivRefreshBar, ivCloseBar;
    private View rootView;
    private Context mContext;

    public WebViewToolBar(Context context) {
        this(context, null);
    }

    public WebViewToolBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WebViewToolBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        rootView = View.inflate(mContext, SqResUtils.getIdByName("sy37_web_tool_bar", "layout", mContext), this);
        initView();
    }

    private void initView() {
        ivBackBar = rootView.findViewById(SqResUtils.getIdByName("iv_back_bar", "id", mContext));
        ivForwardBar = rootView.findViewById(SqResUtils.getIdByName("iv_forward_bar", "id", mContext));
        ivRefreshBar = rootView.findViewById(SqResUtils.getIdByName("iv_refresh_bar", "id", mContext));
        ivCloseBar = rootView.findViewById(SqResUtils.getIdByName("iv_close_bar", "id", mContext));

        backBar = rootView.findViewById(SqResUtils.getIdByName("web_back_bar", "id", mContext));
        forwardBar = rootView.findViewById(SqResUtils.getIdByName("web_forward_bar", "id", mContext));
        refreshBar = rootView.findViewById(SqResUtils.getIdByName("web_refresh_bar", "id", mContext));
        closeBar = rootView.findViewById(SqResUtils.getIdByName("web_close_bar", "id", mContext));
        backBar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mToolBarClickListener != null) {
                    mToolBarClickListener.onClickBack();
                }
            }
        });
        forwardBar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mToolBarClickListener != null) {
                    mToolBarClickListener.onClickForward();
                }
            }
        });
        refreshBar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mToolBarClickListener != null) {
                    mToolBarClickListener.onClickRefresh();
                }
            }
        });
        closeBar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mToolBarClickListener != null) {
                    mToolBarClickListener.onClickClose();
                }
            }
        });
    }

    //设置回退按钮资源
    public void setBackBarSrc(int resId) {
        ivBackBar.setImageResource(resId);
    }

    //设置前进按钮资源
    public void setForwardBarSrc(int resId) {
        ivForwardBar.setImageResource(resId);
    }

    public void showAnimation() {
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(this, "translationY", 200, 0);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 0, 1);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translateAnimator, alphaAnimator);
        animatorSet.setDuration(500);
        animatorSet.start();
    }

    public void hideAnimation() {
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(this, "translationY", 0, 200);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 1, 0);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(translateAnimator, alphaAnimator);
        animatorSet.setDuration(500);
        animatorSet.start();
    }

    public interface WebToolBarClickListener {
        void onClickBack();

        void onClickForward();

        void onClickRefresh();

        void onClickClose();
    }

    private WebToolBarClickListener mToolBarClickListener;

    public void setWebToolBarClickListener(WebToolBarClickListener toolBarClickListener) {
        this.mToolBarClickListener = toolBarClickListener;
    }

}
