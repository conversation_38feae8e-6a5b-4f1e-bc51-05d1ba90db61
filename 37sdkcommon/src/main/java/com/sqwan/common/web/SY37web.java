package com.sqwan.common.web;

import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.AnimationDrawable;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.DownloadListener;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient.CustomViewCallback;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import com.plugin.standard.RealBaseActivity;
import com.sq.sdk.tool.util.NetworkUtils;
import com.sq.tools.Logger;
import com.sq.webview.SimpleWebHook;
import com.sq.webview.WebFunctionWrapper;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.CutoutUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.web.WebViewToolBar.WebToolBarClickListener;
import com.sqwan.common.webview.SQCommonJsInterface;
import com.sqwan.common.webview.SQWeb;
import com.sqwan.common.webview.SQWebView;
import com.sqwan.common.webview.UrlWebHook;
import com.sqwan.msdk.config.ConfigManager;
import java.lang.reflect.Field;
import java.util.List;
import notchtools.geek.com.notchtools.NotchTools;

/**
 * author : lidaisheng
 * time   : 2020/05/13
 * desc   : 全屏 Web 页面
 */
public class SY37web extends RealBaseActivity implements WebToolBarClickListener {

    public static final String WEB_URL = "url";
    public static final String WEB_TITLE = "title";
    public static final String WEB_SHOW_TITLE = "showTitle";
    public static final String WEB_QUIT_ANIM = "quit_anim";

    private ViewGroup parent;
    private RelativeLayout header;
    private SQWebView webView;
    private View errorNetView;
    private ImageView mIvErrorViewBack;
    private ImageView ivErrorView;
    private FrameLayout mFlVideoContainer;
    private static final String TEL = "tel:";
    private static final String MAILTO = "mailto:";
    private static final String QQ = "qq:";
    private static final String WEIXIN = "weixin:";
    private static final int MOBILEQQ_TYPE = 2;
    private static final int WEIXIN_TYPE = 3;


    private LoadingDialog waitDialog;
    private ValueCallback<Uri[]> uploadMessageAboveL;
    private final static int FILE_CHOOSER_RESULT_CODE = 10000;

    private boolean isQuitAnim = false;
    private String currentUrl = "";
    private String title = "";
    private boolean showTitle = false;
    private TextView tvTitle;
    private ImageButton ibClose;

    //加载超时
    private long LOAD_TIMEOUT = 5000L;
    private Handler mHandler;
    private boolean mShowVideo;
    private float mViewDownY;
    private int mCurScrollDirection = -1;
    private static final int SCROLL_THRESHOLD = 50;
    private static final int SCROLL_DOWN = 0;
    private static final int SCROLL_UP = 1;
    private WebViewToolBar mWebViewToolBar;
    private boolean mShowBar;

    /**
     * 显示加载中的任务
     */
    private final Runnable mShowLoadingRunnable = new Runnable() {
        @Override
        public void run() {
            showGifLoadingImage();
        }
    };

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Logger.info("打开SY37web");
        Intent intent = getIntent();
        if (getContext() != null && getContext() instanceof Activity) {
            if (intent == null || intent.getStringExtra("screenOrientation") == null) {
                Activity context = (Activity) getContext();
                try {
                    // 兼容问题：在 Android 8.0 的手机上可以固定 Activity 的方向，但是这个 Activity 不能是透明的，否则就会抛出异常
                    // 复现场景：只需要给 Activity 主题设置 <item name="android:windowIsTranslucent">true</item> 属性即可
                    context.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                } catch (IllegalStateException e) {
                    // java.lang.IllegalStateException: Only fullscreen activities can request orientation
                    e.printStackTrace();
                }
                context.requestWindowFeature(Window.FEATURE_NO_TITLE);
            }
        }
        setContentView(getIdByName("sy37_web_dialog_portrait_full", "layout"));

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        //隐藏导航栏
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                StatusBarUtil.hideSystemUI(getWindow());
            }
        });
        if (getIntent() != null && getIntent().getExtras() != null) {
            isQuitAnim = getIntent().getBooleanExtra(WEB_QUIT_ANIM, false);
            currentUrl = getIntent().getExtras().getString(WEB_URL);
            title = getIntent().getExtras().getString(WEB_TITLE);
            showTitle = getIntent().getExtras().getBoolean(WEB_SHOW_TITLE);
        }

        LogUtil.e("SY37web title: " + title + " showTitle: " + showTitle + " url: " + currentUrl);
        // 进入即隐藏软键盘
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                        | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        //初始化view
        header = findViewById(getIdByName("header", "id"));
        tvTitle = findViewById(getIdByName("title", "id"));
        ibClose = findViewById(getIdByName("togame", "id"));
        mFlVideoContainer = findViewById(getIdByName("fl_video_container", "id"));
        ibClose.setBackground(getContext().getResources().getDrawable(SqResUtils.getDrawableId(getContext(), "sy_icon_web_close")));
        mWebViewToolBar = findViewById(getIdByName("web_tool_bar", "id"));
        mWebViewToolBar.setWebToolBarClickListener(this);
        ivErrorView = findViewById(getIdByName("iv_error_view", "id"));
        ivErrorView.setImageResource(getIdByName("sy37_net_wifi", "drawable"));

        errorNetView = findViewById(getIdByName("sy37_m_net_error_view", "id"));
        mIvErrorViewBack = findViewById(getIdByName("sy37_iv_back", "id"));
        mIvErrorViewBack.setImageResource(getIdByName("sy37_icon_back", "drawable"));

        if (showTitle) {
            mIvErrorViewBack.setVisibility(View.GONE);
            header.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
            ibClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    quit();
                }
            });
        } else {
            mIvErrorViewBack.setVisibility(View.VISIBLE);
            mIvErrorViewBack.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    quit();
                }
            });
            header.setVisibility(View.GONE);
        }

        //设置整个界面大小
        parent = findViewById(getIdByName("web_parent", "id"));
        findViewById(getIdByName("keyboard", "id")).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                quit();
            }
        });

        //浏览器加载部分
        webView = findViewById(getIdByName("webView", "id"));
        WebFunctionWrapper wrapper = SQWeb.getInstance().bind(webView)
            .replace(new CustomUrlWebHook(), UrlWebHook.class)
            .addWebHook(new CustomWebViewClient())
            .addWebHook(new CustomWebChromeClient())
            .addWebHook(new SimpleWebHook() {
                @Override
                public void onShowCustomView(View view, CustomViewCallback callback) {
                    super.onShowCustomView(view, callback);
                    mShowVideo = true;
                    mFlVideoContainer.setVisibility(View.VISIBLE);
                    mFlVideoContainer.addView(view);
                }

                @Override
                public void onHideCustomView() {
                    super.onHideCustomView();
                    mFlVideoContainer.removeAllViews();
                    mShowVideo = false;
                    mFlVideoContainer.setVisibility(View.GONE);
                }
            });
        SQWeb.getInstance().checkLocalH5Enable(currentUrl, enable -> {
            if (!enable) {
                wrapper.disableLocalH5();
            }
            showWebHandler.sendEmptyMessage(0);
        });
        //各种监听
        webView.setOnFocusChangeListener(new myOnFocusChangeListener());  //防止输入框点击后页面放大
        webView.setDownloadListener(new MyWebViewDownLoadListener());//下载
        //属性设置
        webView.addJavascriptInterface(new CustomJsObj(webView), SQCommonJsInterface.INTERFACE_NAME);
        mHandler = new Handler(Looper.getMainLooper());
        int statusHeight = NotchTools.getFullScreenTools().getStatusHeight(getWindow());
        int notchHeight = NotchTools.getFullScreenTools().getNotchHeight(getWindow());
        boolean hasNotchScreen = true;
        if (getContext() != null && getContext() instanceof Activity) {
            hasNotchScreen = CutoutUtil.hasNotchScreen((Activity) getContext());
        }
        Logger.info("statusHeight=" + statusHeight + " notchHeight=" + notchHeight + " 是否刘海屏：" + hasNotchScreen);

        loadingRelativeLayout = findViewById(SqResUtils.getId(getContext(), "sy37_rl_loading"));
        gifMovieView = findViewById(SqResUtils.getId(getContext(), "sy37_iv_loading"));
        if (gifMovieView != null && !ConfigManager.getInstance(getContext()).isSimplifiedSDK()) {
            gifMovieView.setBackgroundResource(SqResUtils.getDrawableId(getContext(), "webview_loading"));
            animationDrawable = (AnimationDrawable) gifMovieView.getBackground();
        }
    }

    private RelativeLayout loadingRelativeLayout;
    private ImageView gifMovieView;
    private AnimationDrawable animationDrawable = null;

    private void showGifLoadingImage() {
        if (animationDrawable != null && !ConfigManager.getInstance(getContext()).isSimplifiedSDK()) {
            animationDrawable.start();
        }
        loadingRelativeLayout.setVisibility(View.VISIBLE);
    }

    private void hideGifLoadingImage() {
        if (animationDrawable != null && !ConfigManager.getInstance(getContext()).isSimplifiedSDK()) {
            animationDrawable.stop();
        }
        loadingRelativeLayout.setVisibility(View.GONE);
    }


    private void quit() {
        SY37web.this.finish();
        if (!isQuitAnim) {
            return;
        }
        // 横屏动画从左边退出，竖屏动画从下方退出
        if (this.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            SY37web.this.overridePendingTransition(0, getIdByName("activity_bottom_close", "anim"));
        } else {
            SY37web.this.overridePendingTransition(0, getIdByName("activity_left_close", "anim"));
        }

    }

    Handler showWebHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);

            if (currentUrl == null || TextUtils.isEmpty(currentUrl)) {
                toast("主人，网址是空的，即将为您关闭..");
                //关闭wap页
                closeHandler.sendEmptyMessageDelayed(0, 1000);
                return;
            }
            LogUtil.i("SY37web 打开url：" + currentUrl);
            // 正常加载
            if (NetworkUtils.isNetworkConnected(getContext())) {
                if (webView != null) {
                    //WebView加载url时候需要调用如下函数
                    webView.loadUrl(currentUrl);
                }
            } else {
                errorNetView.setVisibility(View.VISIBLE);
            }

        }
    };

    Handler closeHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);
            quit();
        }
    };

    Handler showErrorWebHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);
            errorNetView.setVisibility(View.VISIBLE);
        }
    };


    private void showWaitDialog(Context context) {
        if (context != null) {
            //如果上下文被杀掉了，则不执行
            if (waitDialog == null) {
                waitDialog = new LoadingDialog(context);
                waitDialog.setCanceledOnTouchOutside(false);
            }

            if (waitDialog != null && !waitDialog.isShowing() && !isFinishing()) {
                waitDialog.show();
            }
        }

    }

    private void hideWaitDialog() {
        if (waitDialog != null && waitDialog.isShowing() && !isFinishing()) {
            waitDialog.dismiss();
        }
    }

    private void updateWaitDialog(String msg) {
        if (waitDialog != null && waitDialog.isShowing() && !isFinishing()) {
            waitDialog.setMessage(msg);
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!mShowVideo) {
                if (webView.canGoBack()) {
                    webView.goBack();
                } else {
                    quit();
                }
            }

        }
        return true;
    }

    protected int getIdByName(String name, String type) {
        int id = 0x0;
        try {
            id = SqResUtils.getIdByName(name, type, getContext());
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    private void toast(String msg) {
        try {
            ToastUtil.showToast(getContext(),msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleJump() {
        if (webView.canGoBack()) {
            mWebViewToolBar.setBackBarSrc(SqResUtils.getIdByName("sy37_web_back_enable", "drawable", getContext()));
        } else {
            mWebViewToolBar.setBackBarSrc(SqResUtils.getIdByName("sy37_web_back_disable", "drawable", getContext()));
        }

        if (webView.canGoForward()) {
            mWebViewToolBar.setForwardBarSrc(SqResUtils.getIdByName("sy37_web_forward_enable", "drawable", getContext()));
        } else {
            mWebViewToolBar.setForwardBarSrc(SqResUtils.getIdByName("sy37_web_forward_disable", "drawable", getContext()));
        }
    }

    @Override
    public void onClickBack() {
        if (webView.canGoBack()) {
            webView.goBack();
        }
    }

    @Override
    public void onClickForward() {
        if (webView.canGoForward()) {
            webView.goForward();
        }
    }

    @Override
    public void onClickRefresh() {
        webView.reload();
    }

    @Override
    public void onClickClose() {
        quit();
    }


    public class CustomJsObj extends SQCommonJsInterface {

        public CustomJsObj(@NonNull SQWebView webView) {
            super(webView);
        }

        @JavascriptInterface
        public void enRefresh() {
            log("enRefresh", "");
            post(new Runnable() {
                @Override
                public void run() {
                    showWebHandler.sendEmptyMessageDelayed(0, 200);
                }
            });
        }

        @Override
        @JavascriptInterface
        public void enClose() {
            log("enClose", "");
            post(new Runnable() {
                @Override
                public void run() {
                    quit();
                }
            });
        }

        @JavascriptInterface
        @Override
        public void enLogin() {
            super.enLogin();
            post(new Runnable() {
                @Override
                public void run() {
                    SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_USER_CENTER);
                }
            });
        }

        @JavascriptInterface
        public void handleToolbar(final String visible) {
            log("handleToolbar", "visible = " + visible);
            post(new Runnable() {
                @Override
                public void run() {
                    if ("0".equals(visible)) {
                        refreshWebBar(false);
                    } else if ("1".equals(visible)) {
                        refreshWebBar(true);
                    }
                }
            });
        }
    }

    //----------------工具类---------------------------------
    private void shareTo(int type, String content) {

        Intent intent = new Intent(Intent.ACTION_SEND);
        String toastContent = "";
        switch (type) {
            case MOBILEQQ_TYPE:
                toastContent = "已复制QQ号码到剪贴版";
                copyString2System(getContext(), content, toastContent);
                intent.setAction(Intent.ACTION_SENDTO);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse("imto://qq"));
                break;
            case WEIXIN_TYPE:
                toastContent = "已复制微信号码到剪贴版";
                intent.setAction(Intent.ACTION_SENDTO);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse(content));
                break;
            default:
                break;
        }

        PackageManager packageManager = getContext().getPackageManager();
        List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        if (resolveInfos != null && !resolveInfos.isEmpty()) {
            startActivity(intent);
        } else {
            if (WEIXIN_TYPE == type) {
                ToastUtil.showToast(getContext(), "请安装微信，" + toastContent);
            } else {
                ToastUtil.showToast(getContext(), "请安装移动QQ，" + toastContent);
            }
        }
    }

    //--------------------------------WebView相关------------------------------------------

    private class MyWebViewDownLoadListener implements DownloadListener {

        @Override
        public void onDownloadStart(String url, String userAgent,
                                    String contentDisposition, String mimetype, long contentLength) {
            AppUtils.toUri(getContext(), url);
        }

    }

    public class myOnFocusChangeListener implements View.OnFocusChangeListener {

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                try {
                    Field defaultScale = WebView.class.getDeclaredField("mDefaultScale");
                    defaultScale.setAccessible(true);
                    // WebViewSettingUtil.getInitScaleValue(VideoNavigationActivity.this,
                    // false )/100.0f 是我的程序的一个方法，可以用float 的scale替代
                    defaultScale.setFloat(webView, 1);
                } catch (Exception e) {

                }
            }
        }
    }

    private void refreshWebBar(boolean showWebBar) {
        mShowBar = showWebBar;
        if (mWebViewToolBar == null) {
            return;
        }
        mWebViewToolBar.setVisibility(mShowBar ? View.VISIBLE : View.GONE);
    }

    public class CustomWebViewClient extends SimpleWebHook {

        @Override
        public void onPageStarted(final WebView view, final String url, Bitmap favicon) {
            //记录当前地址
            currentUrl = url;
            // 清除线程
            removeTimeoutCheckingRunnable();
            timeoutHandler = new Runnable() {
                @Override
                public void run() {
                    System.out.println("37web timeout..");
                    onReceivedError(view, url, -8, "网络超时，请稍后再试.");
                }
            };
            mHandler.postDelayed(timeoutHandler, LOAD_TIMEOUT);
            //记录当前URL的加载时间
            WebviewUtils.setUrlLoadTime(getContext(), url, System.currentTimeMillis());
            //进度框
//            showWaitDialog(getContext());
            super.onPageStarted(view, url, favicon);
            mHandler.postDelayed(mShowLoadingRunnable, 1000);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            //清楚线程
            removeTimeoutCheckingRunnable();
            //加载次数复原
            if (WebviewUtils.isLoadOneTime(getContext(), url, LOAD_TIMEOUT)) {
                LogUtil.i("当前页面:超时前加载完成");
                WebviewUtils.setUrlLoadCount(getContext(), url, 1);
            } else {
                LogUtil.i("当前页面:超时后加载完成");
            }
            //隐藏进度框
            hideWaitDialog();
            String title = view.getTitle();
            if (!TextUtils.isEmpty(title) && tvTitle != null) {
                tvTitle.setText(title);
            }

            mHandler.removeCallbacks(mShowLoadingRunnable);
            hideGifLoadingImage();
        }

        @Override
        public void onReceivedError(WebView webView, String url, int errorCode, String description) {
            //清楚线程
//            removeTimeoutCheckingRunnable();
//            if (-8 == errorCode) {
//                // 超时返回的,特殊处理
//                int count = WebviewUtils.getUrlLoadCount(getContext(), failedUrl);
//                if (count < RETRY_LOAD_COUNTS) {
//                    System.out.println("超时处理，准备加载第" + (count + 1) + "次");
//                    WebviewUtils.setUrlLoadCount(getContext(), failedUrl, count + 1);
//                    showWebHandler.sendEmptyMessageAtTime(1, 500);
//                } else {
//                    showErrorWebHandler.sendEmptyMessageDelayed(1, 200);
//                }
//            } else {
//                // 其他情况，直接加载错误信息
//                showErrorWebHandler.sendEmptyMessageDelayed(1, 200);
//            }
            hideGifLoadingImage();
        }

        @Override
        public void onReceivedSslError(WebView paramWebView,
                                       SslErrorHandler paramSslErrorHandler, SslError paramSslError) {
            super.onReceivedSslError(paramWebView, paramSslErrorHandler, paramSslError);
            hideGifLoadingImage();
        }

        @Override
        public void onLoadResource(WebView view, String url) {
            super.onLoadResource(view, url);
            handleJump();
        }
    }

    public class CustomUrlWebHook extends SimpleWebHook {

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            if (url.startsWith(TEL)) {

                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_DIAL);
                intent.setData(Uri.parse(url));
                startActivity(intent);

            } else if (url.startsWith(QQ)) {

                String qq = url.substring(3);
                shareTo(MOBILEQQ_TYPE, qq);

            } else if (url.startsWith(MAILTO)) {

                Uri uri = Uri.parse(url);
                Intent it = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(it);

            } else if (url.startsWith(WEIXIN)) {

                shareTo(WEIXIN_TYPE, url);

            } else {
                /* 当开启新的页面的时候用webview来进行处理而不是用系统自带的浏览器处理 */
                view.loadUrl(url);
            }

            hideGifLoadingImage();

            return true;
        }
    }

    private Runnable timeoutHandler;

    private void removeTimeoutCheckingRunnable() {
        if (timeoutHandler != null) {
            mHandler.removeCallbacks(timeoutHandler);
            timeoutHandler = null;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        OnActivityResultEvent onActivityResultEvent = new OnActivityResultEvent(requestCode, resultCode, data);
        EventDispatcher.getInstance().dispatcherActivityResultListener(onActivityResultEvent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode != PermissionHelper.SQ_REQUEST_PERMISSION_CODE) {
            return;
        }
        PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onDestroy() {
        //在Activity销毁前，先关闭dialog，防止窗体泄露。
        LogUtil.i("37Web被销毁，先关闭加载进度框");
        hideWaitDialog();
        //清除
        WebviewUtils.clearWebviewPrefs(getContext());
        if (webView != null) {
            webView.onDestroy();
            webView = null;
        }
        mHandler.removeCallbacks(mShowLoadingRunnable);
        mHandler.removeCallbacks(timeoutHandler);
        showWebHandler.removeCallbacksAndMessages(null);
        showErrorWebHandler.removeCallbacksAndMessages(null);
        closeHandler.removeCallbacksAndMessages(null);
        super.onDestroy();
    }

    public class CustomWebChromeClient extends SimpleWebHook {

        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            updateWaitDialog("加载中.." + newProgress + "%");
            super.onProgressChanged(view, newProgress);
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            if (tvTitle != null) {
                tvTitle.setText(title);
            }
        }
    }

    /**
     * 实现文本复制功能
     *
     * @param content API 11之前： android.text.ClipboardManager
     *                API 11之后： android.content.ClipboardManager
     */
    public static void copyString2System(Context context, String content, String tips) {
        if (content == null || "".equals(content)) {
            return;
        }
        try {
            android.content.ClipboardManager clipboardManager = (android.content.ClipboardManager) context
                    .getSystemService(Context.CLIPBOARD_SERVICE);
            if (clipboardManager != null) {
                clipboardManager.setPrimaryClip(ClipData.newPlainText("code", content.trim()));
                ToastUtil.showToast(context, tips);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}