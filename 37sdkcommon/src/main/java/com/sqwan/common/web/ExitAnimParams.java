package com.sqwan.common.web;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @author: gsp
 * @date: 2023/10/10
 * @desc: Web页面退出动画参数
 */
public class ExitAnimParams implements Parcelable {
    public int width;
    public int height;
    public int x;
    public int y;

    public ExitAnimParams(int width, int height, int x, int y) {
        this.width = width;
        this.height = height;
        this.x = x;
        this.y = y;
    }


    protected ExitAnimParams(Parcel in) {
        width = in.readInt();
        height = in.readInt();
        x = in.readInt();
        y = in.readInt();
    }

    public static final Creator<ExitAnimParams> CREATOR = new Creator<ExitAnimParams>() {
        @Override
        public ExitAnimParams createFromParcel(Parcel in) {
            return new ExitAnimParams(in);
        }

        @Override
        public ExitAnimParams[] newArray(int size) {
            return new ExitAnimParams[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(width);
        dest.writeInt(height);
        dest.writeInt(x);
        dest.writeInt(y);
    }
}
