package com.sqwan.common.eventbus;

import android.content.Intent;

/**
 * <AUTHOR>
 * @date 2020-05-26
 */
public class OnActivityResultEvent {

    private int requestCode;

    private int resultCode;

    private Intent intent;

    public OnActivityResultEvent(int requestCode, int resultCode, Intent intent) {
        this.requestCode = requestCode;
        this.resultCode = resultCode;
        this.intent = intent;
    }

    public int getRequestCode() {
        return requestCode;
    }

    public int getResultCode() {
        return resultCode;
    }

    public Intent getIntent() {
        return intent;
    }
}
