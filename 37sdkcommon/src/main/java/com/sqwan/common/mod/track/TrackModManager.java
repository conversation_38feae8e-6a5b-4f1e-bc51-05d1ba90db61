package com.sqwan.common.mod.track;

import android.content.Context;

import com.sq.data.BuildConfig;
import com.sqwan.common.mod.ModHelper;

import java.util.HashMap;

public class TrackModManager {

    public static void init(Context context) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod().init(context);
        }
    }

    public static void track(String event, HashMap<String, String> params) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod().track(event, params);
        }
    }

    public static void userSet(String properties, String value){
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod().userSet(properties,value);
        }
    }

    public static void userSetOnce(String properties, String value){
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod().userSetOnce(properties,value);
        }
    }

    public static void setUserId(String id){
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod().setUserId(id);
        }
    }
}
