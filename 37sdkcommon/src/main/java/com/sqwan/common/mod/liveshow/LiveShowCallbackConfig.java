package com.sqwan.common.mod.liveshow;


import com.sqwan.msdk.api.SQResultListener;

import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-05 10:52
 */
public class LiveShowCallbackConfig {
    public enum LiveShowCallbackType{
        joinRoom,leaveRoom,destroy,changeVoice;
    }

    public LiveShowCallbackConfig(Map<String, String> data, SQResultListener sqResultListener) {
        this.data = data;
        this.sqResultListener = sqResultListener;
    }

    public Map<String,String> data;
    public SQResultListener sqResultListener;

}
