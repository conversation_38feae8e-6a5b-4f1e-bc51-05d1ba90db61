package com.sqwan.common.mod.track;

import android.content.Context;

import com.sq.data.BuildConfig;
import com.sqwan.common.mod.ModHelper;

import java.util.HashMap;
import java.util.Map;

public class TrackModManager2 {

    public static void init(Context context) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().init(context);
        }
    }

    public static void track(String event, Map<String, String> params) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().track(event, params);
        }
    }

    public static void userSet(String properties, String value) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().userSet(properties, value);
        }
    }

    public static void userSetOnce(String properties, String value) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().userSetOnce(properties, value);
        }
    }

    public static void setUserId(String id) {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().setUserId(id);
        }
    }

    public static void flush() {
        if (BuildConfig.isTrack) {
            ModHelper.getTrackMod2().flush();
        }
    }
}
