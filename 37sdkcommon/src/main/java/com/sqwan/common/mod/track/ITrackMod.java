package com.sqwan.common.mod.track;

import android.content.Context;
import com.sqwan.common.mod.IModBase;
import java.util.HashMap;

public interface ITrackMod extends IModBase {

    void init(Context context);

    void track(String event, HashMap<String, String> params);

    void userSet(String properties, String value);

    void userSetOnce(String properties, String value);

    void setUserId(String id);
}
