package com.sqwan.common.mod;

import android.support.annotation.Nullable;
import com.sqwan.common.mod.config.IConfigMod;
import com.sqwan.common.mod.plugin.IPluginMod;
import com.sqwan.common.mod.track.ITrackMod;
import com.sqwan.common.mod.track.ITrackMod2;

/**
 * <AUTHOR>
 * @date 2020/2/28
 */
public class ModHelper {

    @SuppressWarnings("unchecked")
    @Nullable
    public static <T extends IModBase> T get(Class<T> clazz) {
        return (T) ModManager.getInstance().getMod(clazz);
    }

    public static IPluginMod getPluginMod() {
        return (IPluginMod) ModManager.getInstance().getMod(IPluginMod.class);
    }

    public static ITrackMod getTrackMod(){
        return (ITrackMod) ModManager.getInstance().getMod(ITrackMod.class);
    }

    public static ITrackMod2 getTrackMod2(){
        return (ITrackMod2) ModManager.getInstance().getMod(ITrackMod2.class);
    }

    public static IConfigMod getConfig(){
        return (IConfigMod) ModManager.getInstance().getMod(IConfigMod.class);
    }
}
