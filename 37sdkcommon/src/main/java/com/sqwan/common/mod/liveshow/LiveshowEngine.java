package com.sqwan.common.mod.liveshow;

import android.content.Context;

import com.sq.data.BuildConfig;
import com.sqwan.common.mod.IModBase;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.msdk.api.tool.ILiveshow;
import com.sqwan.msdk.api.SQResultListener;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-25 10:38
 */
public class LiveshowEngine implements ILiveshow, IModBase {
    private static final LiveshowEngine ourInstance = new LiveshowEngine();

    public static LiveshowEngine getInstance() {
        return ourInstance;
    }

    private Map<LiveShowCallbackConfig.LiveShowCallbackType, LiveShowCallbackConfig> map = new HashMap<>();
    private boolean hasInited;
    private ILiveshowTrackManager iLiveshowTrackManager;
    private ILiveshowManager iLiveshowManager;
    private Context mContext;

    private boolean isLiveShowTypeNone() {
        return !BuildConfig.isHyLiveShow;
    }

    public boolean isLiveshowTypeHy() {
        return BuildConfig.isHyLiveShow;

    }

    public void init(Context context) {
        if (!isLiveShowTypeNone()) {
            if (mContext==null) {
                mContext = context;
                initLiveshowManager();
                initLiveshowTrackManager();
                if (iLiveshowManager!=null) {
                    iLiveshowManager.initSkin(context);
                }
            }
        }
    }

    private void initLiveshowTrackManager() {
        if (iLiveshowTrackManager == null) {
            if (isLiveshowTypeHy()) {
                iLiveshowTrackManager = ModHelper.get(IHyLiveshowTrackManager.class);
            }
            if (iLiveshowTrackManager != null && mContext != null) {
                iLiveshowTrackManager.initContext(mContext);
            }
        }
    }

    private void initLiveshowManager() {
        if (iLiveshowManager == null) {
            if (isLiveshowTypeHy()) {
                iLiveshowManager = ModHelper.get(IHyLiveshowManager.class);
            }
            if (iLiveshowManager != null && mContext != null) {
                iLiveshowManager.initContext(mContext);
            }
        }
    }

    public ILiveshowTrackManager getLiveshowTrackManager() {
        return iLiveshowTrackManager;
    }

    public ILiveshowManager getLiveshowManager() {
        return iLiveshowManager;
    }

    public void setHasInited(boolean hasInited) {
        if (isLiveShowTypeNone()) {
            return;
        }
        this.hasInited = hasInited;
    }

    @Override
    public boolean isSupportLiveVideo() {
        return isLiveshowTypeHy();
    }

    @Override
    public void joinLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        if (isLiveShowTypeNone()) {
            return;
        }
        if (hasInited) {
            if (iLiveshowManager != null) {
                iLiveshowManager.joinLiveshowRoom(data, listener);
            }

        }
    }

    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        if (isLiveShowTypeNone()) {
            return;
        }
        if (hasInited) {
            if (iLiveshowManager != null) {
                iLiveshowManager.leaveLiveshowRoom(data, listener);
            }
        }
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        if (isLiveShowTypeNone()) {
            return;
        }
        if (hasInited) {
            if (iLiveshowManager != null) {
                iLiveshowManager.setLiveshowDestroyCallback(listener);
            }
        } else {
            map.put(LiveShowCallbackConfig.LiveShowCallbackType.destroy, new LiveShowCallbackConfig(null, listener));
        }
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        if (isLiveShowTypeNone()) {
            return;
        }
        if (hasInited) {
            if (iLiveshowManager != null) {
                iLiveshowManager.setLiveshowVoiceChangeCallback(listener);
            }
        } else {
            map.put(LiveShowCallbackConfig.LiveShowCallbackType.changeVoice, new LiveShowCallbackConfig(null, listener));
        }
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {
        if (isLiveShowTypeNone()) {
            return;
        }
        if (hasInited) {
            if (iLiveshowManager != null) {
                iLiveshowManager.performLiveshowFeature(data, listener);

            }
        }
    }

    public void handleCallback() {
        if (isLiveShowTypeNone()) {
            return;
        }
        Iterator<LiveShowCallbackConfig.LiveShowCallbackType> iterator = map.keySet().iterator();
        while (iterator.hasNext()) {
            LiveShowCallbackConfig.LiveShowCallbackType type = iterator.next();
            LiveShowCallbackConfig liveShowCallbackConfig = map.get(type);
            if (type == LiveShowCallbackConfig.LiveShowCallbackType.changeVoice) {
                setLiveshowVoiceChangeCallback(liveShowCallbackConfig.sqResultListener);
            } else if (type == LiveShowCallbackConfig.LiveShowCallbackType.destroy) {
                setLiveshowDestroyCallback(liveShowCallbackConfig.sqResultListener);
            }
        }
        map.clear();
    }
}
