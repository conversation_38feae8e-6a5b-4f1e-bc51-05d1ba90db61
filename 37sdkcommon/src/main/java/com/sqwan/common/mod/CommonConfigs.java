package com.sqwan.common.mod;

import android.content.Context;

import com.sqwan.base.L;
import com.sqwan.common.mod.liveshow.BaseBean;
import com.sqwan.common.util.SpUtils;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/11 17:38
 */
public class CommonConfigs {
    private static final CommonConfigs ourInstance = new CommonConfigs();

    public static CommonConfigs getInstance() {
        return ourInstance;
    }

    private CommonConfigs() {
        setAppKey(ConfigManager.getInstance(L.getApplicationContext()).getAppKey());
        sqAppConfig = ConfigManager.getInstance(L.getApplicationContext()).getSQAppConfig();
    }

    private SQAppConfig sqAppConfig;

    private BaseBean baseUserInfo;

    private String session_id = "";

    private String appKey = "";

    private String spkey = "";

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSpkey() {
        return spkey;
    }

    public void setSpkey(String spkey) {
        this.spkey = spkey;
    }

    public SQAppConfig getSqAppConfig() {
        return sqAppConfig;
    }

    public BaseBean getBaseUserInfo() {
        return baseUserInfo;
    }

    public void setBaseUserInfo(BaseBean baseUserInfo) {
        this.baseUserInfo = baseUserInfo;
    }

    public long getCurrentTime(){
        return ModHelper.getConfig().getCommonConfig().getCurrentTime();
    }
    public String getUserId(){
        return ModHelper.getConfig().getCommonConfig().getUserId();
    }
    public boolean getIsNewRole(Context context){
        SpUtils spUtils = new SpUtils(context,"new_guide");
        CommonConfigs.getInstance().setSpkey("guide_" + getBaseUserInfo().roleId);
        return !spUtils.getBoolean(CommonConfigs.getInstance().getSpkey());
    }

    public void setNewRoleId(Context context){
        SpUtils spUtils = new SpUtils(context,"new_guide");
        CommonConfigs.getInstance().setSpkey("guide_" + getBaseUserInfo().roleId);
        spUtils.put(CommonConfigs.getInstance().getSpkey(),true);
    }
}
