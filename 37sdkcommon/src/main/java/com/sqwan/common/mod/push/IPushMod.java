package com.sqwan.common.mod.push;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sqwan.common.mod.IModBase;
import java.util.Map;

/**
 * 推送相关功能
 *
 * <AUTHOR>
 * @since 2024/6/6
 */
public interface IPushMod extends IModBase {

    void onCreate(@NonNull Activity activity, @Nullable Bundle bundle);

    void onNewIntent(@NonNull Activity activity, @Nullable Intent intent);

    /**
     * 初始化
     */
    void init(@NonNull Context context);

    /**
     * 设置用户信息
     */
    void setUserInfo(String uid, String roleId);

    /**
     * 监听透传信息
     */
    void setTransmitMessageListener(TransmitMessageListenerInternal listener);

    /**
     * 设置回执
     *
     * @param params task_id: 任务id(str), message_id: 信息id(str), type: 类型(1展示, 2点击)
     */
    void sendFeedback(@NonNull Context context, @Nullable Map<String, String> params);

    interface TransmitMessageListenerInternal {

        /**
         * @param json 为json字符串, task_id: 任务id(str), message_id: 信息id(str), data: 透传的数据(str)
         */
        void onReceiveTransmitMessage(String json);
    }
}
