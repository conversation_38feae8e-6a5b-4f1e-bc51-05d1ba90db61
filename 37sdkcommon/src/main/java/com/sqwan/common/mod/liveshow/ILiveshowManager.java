package com.sqwan.common.mod.liveshow;

import android.app.Activity;
import android.content.Context;

import com.sqwan.common.mod.IModBase;
import com.sqwan.msdk.api.SQResultListener;

import org.json.JSONObject;

import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-05-10 16:16
 */
public interface ILiveshowManager extends IModBase {
    boolean init();
    void initContext(Context context);
    void uninit();
    String getRoomId();
    String getUserId();
    void pauseChannel();
    void resumeChannel();
    boolean isLiveShow();
    void joinLiveshowRoom(Map<String, String> data, SQResultListener listener);
    void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener);
    void setLiveshowVoiceChangeCallback(SQResultListener listener);
    void setLiveshowDestroyCallback(SQResultListener listener);
    void performLiveshowFeature(Map<String, String> data, SQResultListener listener);
    Activity getLiveshowActivity();
    void initLiveshowActivity(Context context);
    void onActive(JSONObject data);
    void onSubmitRole();
    void initSkin(Context context);
}
