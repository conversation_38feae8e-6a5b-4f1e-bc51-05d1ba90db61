package com.sqwan.common.mod.track;

import android.content.Context;
import com.sqwan.common.mod.IModBase;
import java.util.Map;

public interface ITrackMod2 extends IModBase {

    void init(Context context);

    void track(String event, Map<String, String> params);

    void userSet(String properties, String value);

    void userSetOnce(String properties, String value);

    void setUserId(String id);

    void flush();
}
