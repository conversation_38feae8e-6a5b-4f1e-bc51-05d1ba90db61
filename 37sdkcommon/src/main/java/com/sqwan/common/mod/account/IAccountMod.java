package com.sqwan.common.mod.account;


import android.content.res.Configuration;
import com.sqwan.common.mod.IModBase;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/28
 */
public interface IAccountMod extends IModBase {

    void login(ILoginListener loginListener);

    void logout();

    void changeAccount(ILoginListener loginListener);

    void showLoginView(ILoginListener loginListener);

    String getToken();

    String getUid();

    String getUname();

    /**
     * 保存账号到账号列表
     */
    void saveAccount(String name, String pwd);

    /**
     * 修改密码
     * web页面修改密码之后，需要清除当前缓存中保存的账号密码
     */
    void modifyPassword();

    /**
     * web页面调用登录接口
     * 悬浮窗调用登录接口
     * @param skipBackToGameLogin 是否跳过返回到游戏登录界面
     */
    void webEnLogin(boolean skipBackToGameLogin);

    void setBackToGameLoginListener(IBackToGameLoginListener listener);

    void setAccountChangeListener(IAccountChangeListener listener);

    void setScreenshotListener(IScreenshotListener listener);

    void showUAgreement();

    void submitRoleInfo(Map<String, String> roleInfo);

    void wxBind(IBindWxListener listener);

    void setAuthResultListener(IAuthResultListener authResultListener);

    IAuthResultListener getAuthResultListener();

    void showAgeAppropriate();

    boolean hasSubmitRole();

    void setSubmitRole(boolean isSubmitRole);

    void showFloatMenu();

    void redDotCalled(String title);

    void backToGameLogin();

    void onResume();

    void onPause();

    void onConfigurationChanged(Configuration newConfig);
}
