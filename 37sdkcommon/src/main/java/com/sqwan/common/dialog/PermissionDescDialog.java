package com.sqwan.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;


/**
 * <AUTHOR>
 * @date 2022/3/14
 */

public class PermissionDescDialog extends BaseDialog {
    protected String desc;
    protected CharSequence title;
    protected TextView tvTitle;
    protected Context mContext;

    public PermissionDescDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sy37_common_blank_dialog"));
        tvTitle = findViewById(SqResUtils.getId(mContext, "tv_title"));
        tvTitle.setText(desc);

    }

    public void setDesc(String desc) {
        this.desc = desc;
        if (tvTitle != null) {
            tvTitle.setText(this.desc);
        }
    }

}
