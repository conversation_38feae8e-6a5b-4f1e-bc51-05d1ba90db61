package com.sqwan.common.dialog.pop;


import com.sqwan.common.util.UrlUtils;

import org.json.JSONObject;


public class PopupDialogBean {

    /**
     * 弹窗的url
     */
    private String url;

    /**
     * 弹窗的id
     */
    private String id;

    /**
     * 是否强制弹窗,0不强制，1强制
     */
    private int enforce = 0;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getEnforce() {
        return enforce;
    }

    public void setEnforce(int enforce) {
        this.enforce = enforce;
    }

    /**
     * @return 是否强制弹窗
     */
    public boolean isForce() {
        return enforce == 1;
    }

    public static PopupDialogBean decodeFromJson(JSONObject jsonObject) {
        PopupDialogBean popupDialogBean = new PopupDialogBean();
        String url = jsonObject.optString("url");
        popupDialogBean.setUrl(url);
        String pop_id = UrlUtils.readValueFromUrlStrByParamName(url, "pop_id");
        popupDialogBean.setId(pop_id);
        int enforce = jsonObject.optInt("enforce");
        popupDialogBean.setEnforce(enforce);
        return popupDialogBean;
    }
}
