package com.sqwan.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;

/**
 * <AUTHOR>
 * @date 2019/5/7
 */
public class CommonAlertDialog extends Dialog {

    protected CharSequence title,mNegativeButtonText,mPositiveButtonText,message;
    protected View.OnClickListener mNegativeButtonListener,mPositiveButtonListener;
    protected TextView tvTitle,tvEnsure,tvCancel;
    protected View ViewDivider;
    protected Context mContext;
    public CommonAlertDialog(@NonNull Context context) {
        this(context, SqResUtils.getStyleId(context,"CustomDialog"));
    }
    public CommonAlertDialog(@NonNull Context context,int themeResId){
        super(context,themeResId);
        this.mContext = context;

    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(getContext(),"sy37_common_alert_dialog"));
        tvTitle = (TextView) findViewById(SqResUtils.getId(getContext(),"tv_title"));
        tvEnsure = (TextView) findViewById(SqResUtils.getId(getContext(),"tv_ensure"));
        tvCancel = (TextView) findViewById(SqResUtils.getId(getContext(),"tv_cancel"));
        ViewDivider = (View) findViewById(SqResUtils.getId(getContext(),"v_divider_h"));
        tvTitle.setText(title);
        tvEnsure.setText(mPositiveButtonText);
        tvEnsure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPositiveButtonListener != null) {
                    mPositiveButtonListener.onClick(v);
                }
                dismiss();
            }
        });
        tvCancel.setText(mNegativeButtonText);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNegativeButtonListener != null) {
                    mNegativeButtonListener.onClick(v);
                }
                dismiss();
            }
        });
    }

    public void setTitle(CharSequence title) {
        this.title = title;
    }
    public void setMessage(CharSequence message){
        this.message = message;
    }
    public void setNegativeButtonText(CharSequence negativeButtonText) {
        this.mNegativeButtonText = negativeButtonText;
    }

    public void setPositiveButtonListener(View.OnClickListener positiveButtonListener) {
        this.mPositiveButtonListener = positiveButtonListener;
    }

    public void setNegativeButtonListener(View.OnClickListener negativeButtonListener) {
        this.mNegativeButtonListener = negativeButtonListener;
    }

    public void setPositiveButtonText(CharSequence positiveButtonText) {
        this.mPositiveButtonText = positiveButtonText;
    }

    public static final class Builder {

        private Context mContext;
        private CharSequence title,message;
        private CharSequence mNegativeButtonText;
        private CharSequence mPositiveButtonText;
        private View.OnClickListener mNegativeButtonListener;
        private View.OnClickListener mPositiveButtonListener;
        private boolean mCancelable;
        private boolean mCanceledOnTouchOutside;

        public Builder(Context context) {
            this.mContext = context;
        }

        public Builder setTitle(CharSequence title) {
            this.title = title;
            return this;
        }

        public Builder setPositiveButton(CharSequence text, final View.OnClickListener listener) {
            this.mPositiveButtonText = text;
            this.mPositiveButtonListener = listener;
            return this;
        }

        public Builder setNegativeButton(CharSequence text, final View.OnClickListener listener) {
            this.mNegativeButtonText = text;
            this.mNegativeButtonListener = listener;
            return this;
        }

        public Builder setmCanceledOnTouchOutside(boolean mCanceledOnTouchOutside) {
            this.mCanceledOnTouchOutside = mCanceledOnTouchOutside;
            return this;
        }

        public Builder setCancelable(boolean cancelable) {
            this.mCancelable = cancelable;
            return this;
        }
        public Builder set(boolean cancelable) {
            this.mCanceledOnTouchOutside = cancelable;
            return this;
        }
        public Builder setMessage(CharSequence message) {
            this.message = message;
            return this;
        }

        public void show() {
            final CommonAlertDialog dialog = new CommonAlertDialog(mContext);
            dialog.setTitle(title);
            dialog.setNegativeButtonText(mNegativeButtonText);
            dialog.setNegativeButtonListener(mNegativeButtonListener);
            dialog.setPositiveButtonListener(mPositiveButtonListener);
            dialog.setPositiveButtonText(mPositiveButtonText);
            dialog.setCancelable(mCancelable);
            dialog.setCanceledOnTouchOutside(mCanceledOnTouchOutside);
            dialog.show();
        }
        public Dialog showEx(){
            final CommonAlertDialog dialog = new CommonAlertExDialog(mContext);
            dialog.setTitle(title);
            dialog.setMessage(message);
            dialog.setNegativeButtonText(mNegativeButtonText);
            dialog.setNegativeButtonListener(mNegativeButtonListener);
            dialog.setPositiveButtonListener(mPositiveButtonListener);
            dialog.setPositiveButtonText(mPositiveButtonText);
            dialog.setCancelable(mCancelable);
            dialog.setCanceledOnTouchOutside(mCanceledOnTouchOutside);
            dialog.show();
            return dialog;
        }
    }

}
