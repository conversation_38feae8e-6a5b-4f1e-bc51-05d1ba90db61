package com.sqwan.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.Window;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.sdk.libs.SqR;

/**
 * <AUTHOR>
 * @date 2020/2/11
 */
public class BaseDialog extends Dialog{


    public BaseDialog(@NonNull Context context) {
        super(context, SqResUtils.getStyleId(context, SqR.style.Mdialog));
    }

    public BaseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected BaseDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Window dialogWindow = getWindow();
        dialogWindow.getDecorView().setPadding(0, 0, 0, 0);
        dialogWindow.getDecorView().setBackgroundColor(getContext().getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
    }


    @Override
    public void show() {
        StatusBarUtil.hideSystemUI(getWindow());
        super.show();
    }
}
