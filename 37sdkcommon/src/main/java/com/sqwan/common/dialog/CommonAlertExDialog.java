package com.sqwan.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sq.data.SqR;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;

/**
 * <AUTHOR>
 * @date 2019/5/7
 */
public class CommonAlertExDialog extends CommonAlertDialog {

    public CommonAlertExDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        tvTitle.setGravity(Gravity.CENTER);
        ViewGroup.LayoutParams params = tvTitle.getLayoutParams();
        params.width = ViewGroup.LayoutParams.MATCH_PARENT;
        tvTitle.setLayoutParams(params);
        if (TextUtils.isEmpty(mNegativeButtonText)) {
            ViewUtils.gone(ViewDivider);
            ViewUtils.gone(tvCancel);
        }
        if (TextUtils.isEmpty(mPositiveButtonText)) {
            ViewUtils.gone(ViewDivider);
            ViewUtils.gone(tvEnsure);
        }
        final int titleSize = DensityUtil.dip2px(mContext,18);
        final int messageSize = DensityUtil.dip2px(mContext,16);
        CharSequence content = SpanUtil.concat(
                SpanUtil.getFontString(title + "\n\n",titleSize, Color.parseColor("#ff333333"),true),
                SpanUtil.getFontString(message.toString(),messageSize,Color.parseColor("#ff333333"),false));
        tvTitle.setText(content);

    }
    @Override
    public void show() {
        if(mContext instanceof Activity && ((Activity)mContext).isFinishing()) {
            return;
        }
        StatusBarUtil.hideSystemUI(getWindow());
        super.show();
    }

}
