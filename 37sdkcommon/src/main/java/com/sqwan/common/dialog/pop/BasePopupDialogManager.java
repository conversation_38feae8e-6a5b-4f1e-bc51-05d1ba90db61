package com.sqwan.common.dialog.pop;

import static com.sqwan.common.track.SqTrackUtil.isScreenOriatationPortrait;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.parameters.bean.WebDialogBean;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.web.SY37PortraitWebPage;
import com.sqwan.common.web.SY37web;
import com.sqwan.common.webview.SQWebViewDialog;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public abstract class BasePopupDialogManager {

    protected Context mContext;

    protected List<PopupDialogBean> popupDialogBeans;

    //当前显示的第几个弹窗
    private int index = 0;

    public void handlePopup(Context context) {
        this.mContext = context;
        requestPopup();
    }

    public abstract void requestPopup();

    public abstract String getDesc();

    /**
     * 存储弹窗数据
     *
     * @param popupJsonStr
     */
    public void setPopupData(String popupJsonStr) {
        try {
            if (popupDialogBeans == null) {
                popupDialogBeans = new ArrayList<>();
            }
            popupDialogBeans.clear();
            JSONObject popupJsonObj = new JSONObject(popupJsonStr);
            JSONArray urlArray = popupJsonObj.optJSONArray("urls");
            if (urlArray != null && urlArray.length() > 0) {
                for (int i = 0; i < urlArray.length(); i++) {
                    JSONObject jsonObj = urlArray.getJSONObject(i);
                    PopupDialogBean popupDialogBean = PopupDialogBean.decodeFromJson(jsonObj);
                    if (!TextUtils.isEmpty(popupDialogBean.getUrl())) {
                        popupDialogBeans.add(popupDialogBean);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取登录弹窗数据
     *
     * @return
     */
    public List<PopupDialogBean> getPopupDialogs() {
        return popupDialogBeans;
    }

    /**
     * 是否需要显示登录弹窗
     *
     * @return
     */
    public boolean needShowPopup() {
        return popupDialogBeans != null && popupDialogBeans.size() > 0;
    }


    /**
     * 弹窗
     */
    public void showPopupDialog(Context context, final OnDialogFinishListener onDialogFinishListener) {
        if (!needShowPopup()) {
            return;
        }
        showPopupDialog(context, index, new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                index += 1;
                if (index < popupDialogBeans.size()) {
                    showPopupDialog(context, index, this);
                } else {
                    if (onDialogFinishListener != null) {
                        onDialogFinishListener.onFinishPopup();
                    }
                    index = 0;
                }
            }
        });
    }


    public void showPopupDialog(final OnDialogFinishListener onDialogFinishListener) {
        showPopupDialog(mContext, onDialogFinishListener);
    }

    public void showPopupDialog(Context context, final int index, DialogInterface.OnDismissListener listener) {
        if (popupDialogBeans != null && index < popupDialogBeans.size()) {
            PopupDialogBean popupDialogBean = popupDialogBeans.get(index);
            WebDialogBean webDialogBean = new WebDialogBean();
            webDialogBean.setUrl(popupDialogBean.getUrl());
            if ((context instanceof Activity) && !((Activity) context).isFinishing()) {
                Uri uri = Uri.parse(popupDialogBean.getUrl());
                String forceOrientation = uri.getQueryParameter("forceOrientation");
                if (TextUtils.equals(forceOrientation, "1") && !isScreenOriatationPortrait(context)) { // 强制竖屏
                    boolean supportPlugin = isSupportPlugin();
                    Intent intent = new Intent();
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.putExtra(SY37web.WEB_URL, popupDialogBean.getUrl());
                    intent.setClass(context, supportPlugin ? SY37web.class : SY37PortraitWebPage.class);
                    intent.putExtra("screenOrientation", "portrait");
                    context.startActivity(intent);
                } else {
                    SQWebViewDialog dialog = new SQWebViewDialog(context);
                    dialog.setUrl(webDialogBean.getUrl());
                    dialog.setShowWebBar(webDialogBean.isShowToolBar());
                    dialog.setAllowJumpURL(false);
                    dialog.setOnDismissListener(listener);
                    dialog.setCancelable(!popupDialogBean.isForce());
                    dialog.show();
                }

                HashMap<String, String> pushShowMap = new HashMap<>();
                pushShowMap.put(SqTrackKey.push_id, popupDialogBean.getId());
                pushShowMap.put(SqTrackKey.push_link, popupDialogBean.getUrl());
                pushShowMap.put(SqTrackKey.push_scene_id, getDesc());
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_push_show, pushShowMap);
            } else {
                LogUtil.i("context is not an Activity or Activity is finishing ");
            }
        }
    }


    private static boolean isSupportPlugin() {
        try {
            Class<?> sqwanCoreClass = Class.forName("com.sqwan.msdk.SQwanCore");
            Method getInstanceMethod = sqwanCoreClass.getMethod("getInstance");
            Object sqwanCoreInstance = getInstanceMethod.invoke(null);
            Method isSupportPluginMethod = sqwanCoreClass.getMethod("isSupportPlugin");
            boolean isSupported = (boolean) isSupportPluginMethod.invoke(sqwanCoreInstance);
            LogUtil.i("isSupportPlugin() 返回值：" + isSupported);
            return isSupported;
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            LogUtil.e("反射 isSupportPlugin 方法失败", e);
        }
        return false;
    }
}
