package com.sqwan.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.msdk.config.ConfigManager;

/**
 * <AUTHOR>
 * @date 2020/3/3
 */
public class LoadingDialog extends FullScreenDialog {

    public TextView textView;
    private CharSequence mMessage;

    public LoadingDialog(Context context) {
        super(context);
    }

    public LoadingDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    protected LoadingDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sy37_progress_dialog", "layout"));
        textView = findViewById(getIdByName("msg", "id"));
    }

    protected int getIdByName(String name, String type) {
        int id = 0x0;
        try {
            id = SqResUtils.getIdByName(name, type, getContext());
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    @Override
    public void show() {
        StatusBarUtil.hideSystemUI(getWindow());
        super.show();
    }

    public void setMessage(CharSequence msg) {
        mMessage = msg;
        if (!TextUtils.isEmpty(mMessage) && textView != null) {
            textView.setText(mMessage);
        }
    }
    public void hideMessage(){
        if (textView!=null) {
            ViewUtils.gone(textView);
        }
    }
}
