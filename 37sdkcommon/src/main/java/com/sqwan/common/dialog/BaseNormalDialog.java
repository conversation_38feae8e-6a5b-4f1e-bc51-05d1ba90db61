package com.sqwan.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import com.sq.sdk.tool.util.NetworkUtils;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.util.AndroidBug5497Workaround;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.web.WebShareUtil;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

/**
 *    author : 王琪
 *    time   : 2021/07/16
 *    desc   : 适龄提示弹窗
 *
 *   @deprecated 已过时，请使用 {@link com.sqwan.common.webview.SQWebViewDialog} 代替
 */
@Deprecated
public class BaseNormalDialog extends FullScreenDialog {

    private static final long TIME_OUT_STEMP = 7500;

    protected Context mContext;
    protected Handler mHandler;
    protected WebView mWebView;
    private String mUrl;
    private boolean isNetCon = true;
    private Runnable timeOutRunnable;
    protected View view;

    public BaseNormalDialog(Context context) {
        super(context);
        this.mContext = context;
        mHandler = new Handler(Looper.getMainLooper());
    }

    public BaseNormalDialog(Context context, int themeResId) {
        super(context, themeResId);
        this.mContext = context;
        mHandler = new Handler(Looper.getMainLooper());
    }

    public BaseNormalDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.mContext = context;
        mHandler = new Handler(Looper.getMainLooper());
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //设置dailog布局铺满屏幕
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }
        //隐藏导航栏
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                StatusBarUtil.hideSystemUI(getWindow());
            }
        });
        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        view = inflater.inflate(SqResUtils.getLayoutId(getContext(), "sy37_base_web_normal_dialog"), null);
        mWebView = view.findViewById(SqResUtils.getId(getContext(), "webView"));
        setContentView(view);
        AndroidBug5497Workaround();
        initWebView();
    }

    protected void initWebView() {
        mWebView.setBackgroundColor(0);
        mWebView.getSettings().setAppCacheMaxSize(1024 * 1024 * 5);
        String appCachePath = mContext.getApplicationContext().getCacheDir().getAbsolutePath();
        mWebView.getSettings().setAppCachePath(appCachePath);
        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setAppCacheEnabled(true);
        mWebView.getSettings().setBuiltInZoomControls(false);
        mWebView.getSettings().setSupportZoom(false);
        mWebView.getSettings().setUseWideViewPort(true);
        mWebView.getSettings().setLoadWithOverviewMode(true);
        mWebView.getSettings().setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.addJavascriptInterface(new JsObj(mContext), "fee");
        //js获取当前应用包名
        final String js = "javascript:window.packageName('" + mContext.getPackageName() + "')";
        mWebView.loadUrl(js);
        mWebView.setWebViewClient(new SqWebViewClient());
        mWebView.getSettings().setTextZoom(100); // 通过百分比来设置文字的大小，默认值是100。
        loadUrl();
    }

    protected void loadUrl() {
        if (NetworkUtils.isNetworkConnected(mContext)) {
            if (mWebView != null) {
                mWebView.loadUrl(mUrl);
                timeOutCheck();
            }
        }
    }

    private void timeOutCheck() {
        if (timeOutRunnable == null) {
            timeOutRunnable = new Runnable() {
                @Override
                public void run() {
                    if (!isNetCon) {
                        LogUtil.e("网络连接7.5秒超时");
                        timeOut();
                    }
                }
            };
        }
        mHandler.postDelayed(timeOutRunnable, TIME_OUT_STEMP);
    }

    protected void timeOut() {

    }

    protected void jsClose(final String tag, final String data) {
        dismiss();
    }

    protected void jsToast(final String toast) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showToast(mContext, toast);
            }
        });
    }

    protected void jsOpenUrl(String url) {

    }

    protected void onPageStarted(WebView view, String url, Bitmap favicon) {

    }

    protected void onPageFinished(WebView view, String url) {

    }

    protected void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {

    }

    public void setUrl(String url) {
        this.mUrl = url;
    }

    /**
     * 提供给js外部调用的对象
     */
    public class JsObj {

        private Context jsContext;

        public JsObj(Context context) {
            jsContext = context;
        }

        @JavascriptInterface
        public void enClose(final String tag, final String data) {
            LogUtil.i("enClose --> " + "tag:" + tag + ",data:" + data);
            jsClose(tag, data);
        }

        @JavascriptInterface
        public void enToast(final String toast) {
            LogUtil.i("enToast --> " + toast);
            jsToast(toast);
        }

        @JavascriptInterface
        public void sqOpenUrl(String url) {
            LogUtil.i("sqOpenUrl --> " + url);
            jsOpenUrl(url);
        }

        @JavascriptInterface
        public void enClose() {
            LogUtil.i("enClose");
            jsClose("", "");
        }

        @JavascriptInterface
        public void sqOpenUrl() {
            LogUtil.i("sqOpenUrl");
            jsOpenUrl("");
        }

        @JavascriptInterface
        public String getPopData() {
            LogUtil.i("getPopData");
            return BaseNormalDialog.this.getPopData();
        }

        /**
         * 工具栏显示与隐藏
         *
         * @param visible
         */
        @JavascriptInterface
        public void handleToolbar(final String visible) {
            LogUtil.i("wap 调用handleToolbar " + visible);

        }

        //调起分享
        @JavascriptInterface
        public void share(final String json) {
            LogUtil.i("wap share " + json);
            ((Activity) mContext).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    WebShareUtil.parseJsShare(getContext(), json, new IShareResultListener() {

                        @Override
                        public void onSuccess(Bundle bundle) {
                            LogUtil.i("share", "分享成功");
                            ((Activity) mContext).runOnUiThread((new Runnable() {
                                @Override
                                public void run() {
                                    mWebView.loadUrl("javascript:window.fee.shareSuccessCallback('" + new JSONObject() + "')");
                                }
                            }));
                        }

                        @Override
                        public void onFailture(int code, String msg) {
                            LogUtil.i("share", "分享失败 code = " + code + ", msg = " + msg);
                            Map<String, Object> result = new HashMap<>();
                            result.put("code", code);
                            result.put("msg", msg);
                            ((Activity) mContext).runOnUiThread((new Runnable() {
                                @Override
                                public void run() {
                                    mWebView.loadUrl("javascript:window.fee.shareFailureCallback('" + new JSONObject(result) + "')");
                                }
                            }));
                        }
                    });
                }
            });
        }
    }


    public String getPopData() {
        return "";
    }

    private class SqWebViewClient extends WebViewClient {

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            isNetCon = false;
            BaseNormalDialog.this.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            isNetCon = true;
            mHandler.removeCallbacks(timeOutRunnable);
            BaseNormalDialog.this.onPageFinished(view, url);
        }

        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);
            BaseNormalDialog.this.onReceivedError(view, request, error);
        }

        @Override
        public void onLoadResource(WebView view, String url) {
            super.onLoadResource(view, url);
        }
    }


    protected void AndroidBug5497Workaround() {
        if (mContext != null && view != null && mContext instanceof Activity) {
            AndroidBug5497Workaround.assistActivity((Activity) mContext, view);
        }
    }

}
