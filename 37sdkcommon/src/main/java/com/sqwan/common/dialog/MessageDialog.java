package com.sqwan.common.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;

public class MessageDialog extends BaseDialog implements View.OnClickListener {

    private TextView mConfirmView;
    private TextView mCancelView;
    private TextView mTitleView;
    private TextView mMessageView;

    private boolean mClickDismiss = true;

    private OnListener mListener;

    public MessageDialog(Context context) {
        super(context);

        setContentView(SqResUtils.getLayoutId(context, "sy37_dialog_message"));
        mConfirmView = findViewById(SqResUtils.getId(context, "tv_confirm"));
        mCancelView = findViewById(SqResUtils.getId(context, "tv_cancel"));

        mTitleView = findViewById(SqResUtils.getId(context, "tv_title"));
        mMessageView = findViewById(SqResUtils.getId(context, "tv_message"));

        mConfirmView.setOnClickListener(this);
        mCancelView.setOnClickListener(this);
    }

    public MessageDialog setDialogTitle(CharSequence text) {
        if (text == null) {
            text = "";
        }
        mTitleView.setText(text);
        mTitleView.setVisibility(TextUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
        return this;
    }

    public MessageDialog setDialogMessage(CharSequence text) {
        if (text == null) {
            text = "";
        }
        mMessageView.setText(text);
        mMessageView.setVisibility(TextUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
        return this;
    }

    public MessageDialog setDialogConfirm(CharSequence text) {
        if (text == null) {
            text = "";
        }
        mConfirmView.setText(text);
        mConfirmView.setVisibility(TextUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
        return this;
    }

    public MessageDialog setDialogCancel(CharSequence text) {
        if (text == null) {
            text = "";
        }
        mCancelView.setText(text);
        mCancelView.setVisibility(TextUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
        return this;
    }

    public MessageDialog setDialogListener(OnListener listener) {
        mListener = listener;
        return this;
    }

    public MessageDialog setClickDismiss(boolean enable) {
        mClickDismiss = enable;
        return this;
    }

    public void performClickDismiss() {
        if (!mClickDismiss) {
            return;
        }
        dismiss();
    }

    @Override
    public void onClick(View v) {
        if (v == mConfirmView) {
            performClickDismiss();
            if (mListener == null) {
                return;
            }
            mListener.onConfirm(this);
        } else if (v == mCancelView) {
            performClickDismiss();
            if (mListener == null) {
                return;
            }
            mListener.onCancel(this);
        }
    }

    public interface OnListener {

        /**
         * 点击确定时回调
         */
        void onConfirm(BaseDialog dialog);

        /**
         * 点击取消时回调
         */
        default void onCancel(BaseDialog dialog) {}
    }
}