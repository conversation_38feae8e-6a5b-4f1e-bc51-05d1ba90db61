package com.sqwan.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;

import java.util.Objects;

import static com.sq.sdk.tool.app.SqThreadHelper.getContext;

/**
 * <AUTHOR>
 * @date 2020/3/3
 */
public class LoadingExDialog extends Dialog {

    TextView textView;
    private CharSequence mMessage;

    public LoadingExDialog(Context context) {
        super(context,SqResUtils.getStyleId(context, "FullScreenDialogTransparentStyle"));
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sy37_base_progress_dialog", "layout"));
        textView = findViewById(getIdByName("msg", "id"));
        Objects.requireNonNull(getWindow()).setBackgroundDrawableResource(android.R.color.transparent);
    }

    protected int getIdByName(String name, String type) {
        int id = 0x0;
        try {
            id = SqResUtils.getIdByName(name, type, getContext());
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    @Override
    public void show() {
        StatusBarUtil.hideSystemUI(getWindow());
        super.show();
    }

    public void setMessage(CharSequence msg) {
        mMessage = msg;
        if (!TextUtils.isEmpty(mMessage) && textView != null) {
            ViewUtils.show(textView);
            textView.setText(mMessage);
        }else{
            ViewUtils.gone(textView);

        }
    }
}
