package com.sqwan.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.plugin.standard.RealBaseActivity;
import com.sqwan.baseui.SqR;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.NavigationUtils;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;

import java.util.Objects;

import static com.sq.sdk.tool.app.SqThreadHelper.getContext;

/**
 * <AUTHOR>
 * @date 2019/5/7
 */
public class PlatformAnnouncementActivity extends RealBaseActivity {


    private TextView tvTitle,tvContent;

    private ImageView ivClose;

    private LinearLayout llEnsure;


    private Activity _activity;

    private Context _context;

    @Override
    public void finish() {
        super.finish();
        _activity.overridePendingTransition(0, 0);
    }



    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        _context = getContext();
        _activity = (Activity) _context;
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                        | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        getContext().getTheme().applyStyle(SqResUtils.getStyleId(_context, SqR.style.Transparent),true);
        setContentView(SqResUtils.getLayoutId(_context,"sy37_dialog_announcement"));
        tvTitle = (TextView) findViewById(SqResUtils.getId(_context,"tv_sy37_announcement_title"));
        tvContent = (TextView) findViewById(SqResUtils.getId(_context,"tv_sy37_announcement_content"));
        ivClose =  (ImageView) findViewById(SqResUtils.getId(_context,"iv_sy37_announcement_close"));
        llEnsure = (LinearLayout) findViewById(SqResUtils.getId(_context,"ll_sy37_announcement_ensure"));
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        llEnsure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
               finish();
            }
        });

        Intent intent = getIntent();
        String title = intent.getStringExtra("title");
        String content = intent.getStringExtra("content");
        if (title != null) {
            tvTitle.setText(title);
        }

        if (content != null)  {
            tvContent.setText(content);
        }
     }

}
