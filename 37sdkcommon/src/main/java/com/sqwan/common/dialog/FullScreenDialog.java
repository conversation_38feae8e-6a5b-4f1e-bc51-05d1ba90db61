package com.sqwan.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;

/**
 * <AUTHOR>
 * @date 2020/2/11
 */
public class FullScreenDialog extends Dialog {

    private Context mContext;

    public FullScreenDialog(Context context) {
        super(context, SqResUtils.getStyleId(context, "FullScreenDialogStyle"));
        this.mContext = context;
    }

    public FullScreenDialog(Context context, int themeResId) {
        super(context, SqResUtils.getStyleId(context, "FullScreenDialogStyle"));
        this.mContext = context;
    }

    public FullScreenDialog(Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void show() {
        if(mContext instanceof Activity && ((Activity)mContext).isFinishing()) {
            return;
        }
        StatusBarUtil.hideSystemUI(getWindow());
        super.show();
    }

}
