package com.sqwan.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.sdk.libs.SqR;

/**
 * <AUTHOR>
 * @date 2019/11/25
 */
public class CommonProgressDialog extends Dialog {

    private String mMessage;
    private Context context;

    TextView textView;

    public CommonProgressDialog(Context context) {
        this(context, SqResUtils.getStyleId(context, SqR.style.progressDialog));
    }

    public CommonProgressDialog(Context context, int theme) {
        super(context, theme);
        this.context = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(SqResUtils.getLayoutId(context, SqR.layout.sy37_progress_dialog_simple), null);
        textView = view.findViewById(SqResUtils.getId(context, SqR.id.msg));
        setContentView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    public void setMessage(String msg) {
        mMessage = msg;
        if (!TextUtils.isEmpty(mMessage) && textView != null) {
            textView.setText(mMessage);
        }
    }
}
