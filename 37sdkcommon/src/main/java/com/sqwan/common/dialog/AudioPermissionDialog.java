package com.sqwan.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.AudioPermissionHelper;
import com.sqwan.common.util.SqResUtils;

/**
 * @author: zls
 * @date: 2025/3/18
 */
public class AudioPermissionDialog extends BaseDialog {

    private TextView tvCancel, tvSure;

    private Context mContext;
    private CancelListener mCancelListener;
    private ConfirmListener mConfirmListener;
    public AudioPermissionDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    public void setCancelListener(CancelListener cancelListener) {
        this.mCancelListener = cancelListener;
    }

    public void setConfirmListener(ConfirmListener confirmListener) {
        this.mConfirmListener = confirmListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_dialog_audio_permission_tip"));
        tvCancel = findViewById(SqResUtils.getId(mContext, "tv_cancel"));
        tvSure = findViewById(SqResUtils.getId(mContext, "tv_sure"));
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCancelListener != null) {
                    mCancelListener.onCancel();
                }
                dismiss();
            }
        });
        tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mConfirmListener != null) {
                    mConfirmListener.onConfirm();
                }
                dismiss();
                goSetting((Activity) mContext);
            }
        });
    }

    private void goSetting(Activity activity) {
        Uri packageURI = Uri.parse("package:" + mContext.getPackageName());
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
        activity.startActivityForResult(intent, AudioPermissionHelper.SQ_AUDIO_REQUEST_PERMISSION_CODE);
    }

    public interface CancelListener {
        void onCancel();
    }

    public interface ConfirmListener {
        void onConfirm();
    }
}
