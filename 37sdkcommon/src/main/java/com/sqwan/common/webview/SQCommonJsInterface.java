package com.sqwan.common.webview;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.Toast;
import com.parameters.bean.MiniProgramBean;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.share.SharePlatform;
import com.parameters.share.ShareTextInfo;
import com.parameters.share.ShareWebInfo;
import com.sq.diagnostic.assistant.DiagnosticAssistant;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.ActivityResultListener;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.dialog.MessageDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.account.IBindWxListener;
import com.sqwan.common.mod.comment.ICommentMod;
import com.sqwan.common.mod.share.IShareMod;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.net.sq.CommonUrlConstant;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.route.FunctionRouter;
import com.sqwan.common.route.FunctionRouter.Func;
import com.sqwan.common.route.FunctionRouterManager;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.ImageUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.PermissionSimpleHelper;
import com.sqwan.common.util.PermissionSimpleHelper.OnPermissionCallback;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.web.SY37BehindWebPage;
import com.sqwan.common.web.SY37LandscapeWebPage;
import com.sqwan.common.web.SY37PortraitWebPage;
import com.sqwan.common.web.SY37web;
import com.sqwan.common.web.WebShareUtil;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * author : 黄锦群
 * time   : 2022/04/18
 * desc   : 三七互娱通用 Js 接口封装
 * doc    : https://37wiki.37wan.com/pages/viewpage.action?pageId=20980140
 */
@Keep
public class SQCommonJsInterface {

    private static final String TAG = "SQCommonJsInterface";

    /** 功能状态：正常 */
    public static final int FEATURE_STATUS_OK = 1;

    /** 功能状态：不支持 */
    public static final int FEATURE_STATUS_NO = 0;

    private static final Handler HANDLER = new Handler(Looper.getMainLooper());

    /**
     * Js 接口名称
     */
    public static final String INTERFACE_NAME = "fee";

    /**
     * 图片分享类型
     */
    public static final int JS_SHARE_TYPE_IMG = 1;
    /**
     * 链接分享类型
     */
    public static final int JS_SHARE_TYPE_H5 = 2;
    /**
     * 文本分享类型
     */
    public static final int JS_SHARE_TYPE_TEXT = 3;

    /** 竖屏方向 */
    public static final int ORIENTATION_PORTRAIT = 1;
    /** 横屏方向 */
    public static final int ORIENTATION_LANDSCAPE = 2;

    /** 软件安装状态：已安装 */
    public static final int INSTALL_STATUS_OK = 1;

    /** 软件安装状态：未安装 */
    public static final int INSTALL_STATUS_NO = 0;

    @NonNull
    private final WebView mWebView;

    public SQCommonJsInterface(@NonNull SQWebView webView) {
        mWebView = webView;
    }

    public WebView getWebView() {
        return mWebView;
    }

    public Context getContext() {
        return mWebView.getContext();
    }

    @Nullable
    public Activity getActivity() {
        return ViewUtils.getActivity(mWebView);
    }

    public final void post(Runnable runnable) {
        HANDLER.post(runnable);
    }

    public void log(String methodName, String message) {
        LogUtil.i(TAG + " " + methodName + " " + message);
    }

    /**
     * 显示 Toast
     */
    @JavascriptInterface
    public void enToast(final String message) {
        log("enToast", "message = " + message);
        post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showToast(getContext(), message);
            }
        });
    }

    @JavascriptInterface
    public void showMultipleUrl(int type,final String linkUrl,final String urlParams, final String titles) {
        enClose();
        post(new Runnable() {
            @Override
            public void run() {
                ModHelper.get(IAccountMod.class).showFloatMenu();
                String loadUrl = linkUrl;
                String params = "backurl=" + urlParams;
                if (loadUrl.indexOf("?") > 0) {
                    loadUrl += "&" + params;
                } else {
                    loadUrl += "?" + params;
                }
                log("showMultipleUrl", loadUrl);

                if (!TextUtils.isEmpty(titles)) {
                    String[] titleArr = titles.split(",");
                    for (String title : titleArr) {
                        ModHelper.get(IAccountMod.class).redDotCalled(title);
                    }
                }
                if (type == 1) {
                    showWebViewDialog(loadUrl, true);
                } else {
                    AppUtils.toSQWebUrl(getContext(), loadUrl, "");
                }
            }
        });
    }

    /**
     * 支付界面的提示信息
     */
    @JavascriptInterface
    public void enAlert(final String tips) {
        log("enAlert", "tips = " + tips);
        post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showToast(getContext(), tips);
            }
        });
    }

    @JavascriptInterface
    public void enClose() {
    }

    /**
     * 打开一个链接
     */
    @JavascriptInterface
    public void sqOpenUrl(final String url) {
        log("sqOpenUrl", "url = " + url);
        post(new Runnable() {
            @Override
            public void run() {
                try {
                    AppUtils.toSdkUrl(getContext(), url);
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast(getContext(), "sqOpenUrl 跳转失败：" + url);
                }
            }
        });
    }

    /**
     * 调起分享
     *
     * @param json          分享的数据
     */
    @JavascriptInterface
    public void share(final String json) {
        log("share", "json = " + json);
        shareByBitmap(json, (Bitmap) null);
    }

    /**
     * 调起分享
     *
     * @param json          分享的数据
     * @param base64        图片 base 数据
     */
    @JavascriptInterface
    public void share(final String json, String base64) {
        log("share", "json = " + json + ", base64 = " + (base64 != null));
        shareByBitmap(json, base64ToBitmap(base64));
    }

    private void shareByBitmap(final String json, Bitmap bitmap) {
        post(new Runnable() {
            @Override
            public void run() {
                WebShareUtil.parseJsShare(getContext(), json, bitmap, new IShareResultListener() {

                    @Override
                    public void onSuccess(Bundle bundle) {
                        log("share", "分享成功");
                        post(new Runnable() {
                            @Override
                            public void run() {
                                mWebView.loadUrl("javascript:window.fee.shareSuccessCallback('" + new JSONObject() + "')");
                            }
                        });
                    }

                    @Override
                    public void onFailture(int code, String msg) {
                        log("share", "分享失败 code = " + code + ", msg = " + msg);
                        Map<String, Object> result = new HashMap<>();
                        result.put("code", code);
                        result.put("msg", msg);
                        post(new Runnable() {
                            @Override
                            public void run() {
                                mWebView.loadUrl("javascript:window.fee.shareFailureCallback('" + new JSONObject(result) + "')");
                            }
                        });
                    }
                });
            }
        });
    }

    /**
     * 调起系统分享
     *
     * @param json          分享的数据
     */
    @JavascriptInterface
    public void shareToSystem(final String json) {
        log("shareToSystem", "json = " + json);
        shareToSystemByBitmap(json, (Bitmap) null);
    }

    /**
     * 调起系统分享
     *
     * @param json          分享的数据
     * @param base64        图片 base64 数据
     */
    @JavascriptInterface
    public void shareToSystem(final String json, String base64) {
        log("shareToSystem", "json = " + json + ", base64 = " + (base64 != null));
        shareToSystemByBitmap(json, base64ToBitmap(base64));
    }

    private void shareToSystemByBitmap(final String json, Bitmap bitmap) {
        post(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject;
                try {
                    jsonObject = new JSONObject(json);
                } catch (JSONException e) {
                    e.printStackTrace();
                    ToastUtil.showToast(getContext(), "数据解析失败，无法调起分享");
                    return;
                }

                // 分享的类型
                int shareType = jsonObject.optInt("shareType");
                // 分享的标题
                String shareTitle = jsonObject.optString("shareTitle");
                // 分享的链接
                String shareLinkUrl = jsonObject.optString("shareLinkUrl");
                // 分享的图片 url
                String shareImageUrl = jsonObject.optString("shareImageUrl");
                // 分享的文本
                String shareText = jsonObject.optString("shareText");

                switch (shareType) {
                    case SQCommonJsInterface.JS_SHARE_TYPE_IMG: {
                        if (bitmap != null) {
                            shareToSystemByBitmap(bitmap);
                        } else {
                            AsyncImageLoader loader = new AsyncImageLoader(getContext());
                            loader.loadDrawable(shareImageUrl, null, new AsyncImageLoader.ImageCallback() {

                                @Override
                                public void imageLoaded(Bitmap downloadBitmap, ImageView imageView, String imageUrl) {
                                    shareToSystemByBitmap(downloadBitmap);
                                }
                            });
                        }
                        break;
                    }
                    case SQCommonJsInterface.JS_SHARE_TYPE_H5: {
                        // 分享链接
                        ShareMessage shareMessage = new ShareMessage();
                        shareMessage.setSkipPreview(true);

                        ShareWebInfo shareWebInfo = new ShareWebInfo();
                        shareWebInfo.setTitle(shareTitle);
                        shareWebInfo.setPageUrl(shareLinkUrl);
                        shareMessage.setShareMessage(shareWebInfo);

                        startSystemShareActivity(shareMessage);
                        break;
                    }
                    case SQCommonJsInterface.JS_SHARE_TYPE_TEXT: {
                        // 分享文本
                        ShareMessage shareMessage = new ShareMessage();
                        shareMessage.setSkipPreview(true);

                        ShareTextInfo shareTextInfo = new ShareTextInfo();
                        shareTextInfo.setText(jsonObject.optString("shareText"));
                        shareMessage.setShareMessage(shareTextInfo);

                        startSystemShareActivity(shareMessage);
                        break;
                    }
                    default: {
                        ToastUtil.showToast(getContext(), "调起分享失败，不支持的分享类型");
                        break;
                    }
                }
            }
        });
    }

    private void shareToSystemByBitmap(Bitmap bitmap) {
        ShareMessage shareMessage = new ShareMessage();
        shareMessage.setSkipPreview(true);

        ShareImageInfo shareImageInfo = new ShareImageInfo();
        shareImageInfo.setBitmap(bitmap);
        shareMessage.setShareMessage(shareImageInfo);

        startSystemShareActivity(shareMessage);
    }

    /**
     * 打开系统分享界面
     */
    private void startSystemShareActivity(ShareMessage shareMessage) {
        if (shareMessage == null) {
            ToastUtil.showToast(getContext(), "分享失败，要分享的内容为空");
            return;
        }
        shareMessage.setPlatform(SharePlatform.SYSTEM);
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                ModHelper.get(IShareMod.class).share(shareMessage, new IShareResultListener() {

                    @Override
                    public void onSuccess(Bundle bundle) {}

                    @Override
                    public void onFailture(int code, String msg) {
                        ToastUtil.showToast(getContext(), msg);
                    }
                });
            }
        };

        if (!(shareMessage.getShareMessage() instanceof ShareImageInfo)) {
            runnable.run();
            return;
        }

        PermissionSimpleHelper.requestPermission(
            PermissionSimpleHelper.STORAGE_PERMISSION,
            PermissionSimpleHelper.STORAGE_PERMISSION_NAME,
            PermissionSimpleHelper.STORAGE_PERMISSION_BY_SHARE, new OnPermissionCallback() {
                @Override
                public void onGranted() {
                    runnable.run();
                }

                @Override
                public void onDenied() {
                    ToastUtil.showToast(getContext(), "分享图片失败，没有" + PermissionSimpleHelper.STORAGE_PERMISSION_NAME);
                }
            });
    }

    /**
     * 将 base64 保存到相册中
     */
    @JavascriptInterface
    public void saveImageByBase64(final String base64) {
        PermissionSimpleHelper.requestPermission(
            PermissionSimpleHelper.STORAGE_PERMISSION,
            PermissionSimpleHelper.STORAGE_PERMISSION_NAME,
            PermissionSimpleHelper.STORAGE_PERMISSION_BY_WEB, new OnPermissionCallback() {
                @Override
                public void onGranted() {
                    Bitmap bitmap = base64ToBitmap(base64);
                    Uri uri = ImageUtils.save(getContext(), bitmap,
                        String.format("share_image_%s", System.currentTimeMillis()) + ".png");
                    if (uri == null) {
                        callbackFailture(102, "保存图片失败");
                        return;
                    }

                    callbackSuccess();
                }

                @Override
                public void onDenied() {
                    callbackFailture(102, "权限申请失败，无法保存图片");
                }

                private void callbackSuccess() {
                    post(new Runnable() {
                        @Override
                        public void run() {
                            mWebView.loadUrl("javascript:window.fee.saveImageByBase64SuccessCallback('" + new JSONObject() + "')");
                        }
                    });
                }

                private void callbackFailture(int code, String msg) {
                    post(new Runnable() {
                        @Override
                        public void run() {
                            Map<String, Object> result = new HashMap<>();
                            result.put("code", code);
                            result.put("msg", msg);
                            mWebView.loadUrl("javascript:window.fee.saveImageByBase64FailtureCallback('" + new JSONObject(result) + "')");
                        }
                    });
                }
            });
    }

    /**
     * 下载图片 url 到相册中
     */
    @JavascriptInterface
    public void saveImageByUrl(final String url) {
        PermissionSimpleHelper.requestPermission(
            PermissionSimpleHelper.STORAGE_PERMISSION,
            PermissionSimpleHelper.STORAGE_PERMISSION_NAME,
            PermissionSimpleHelper.STORAGE_PERMISSION_BY_WEB, new OnPermissionCallback() {
                @Override
                public void onGranted() {
                    AsyncImageLoader loader = new AsyncImageLoader(getContext());
                    loader.loadDrawable(url, null, new AsyncImageLoader.ImageCallback() {

                        @Override
                        public void imageLoaded(Bitmap bitmap, ImageView imageView, String imageUrl) {
                            Uri uri = ImageUtils.save(getContext(), bitmap,
                                String.format("share_image_%s", System.currentTimeMillis()) + ".png");
                            if (uri == null) {
                                callbackFailture(101, "保存图片失败");
                                return;
                            }
                            callbackSuccess();
                        }
                    });
                }

                @Override
                public void onDenied() {
                    callbackFailture(102, "权限申请失败，无法保存图片");
                }

                private void callbackSuccess() {
                    post(new Runnable() {
                        @Override
                        public void run() {
                            mWebView.loadUrl("javascript:window.fee.saveImageByUrlSuccessCallback('" + new JSONObject() + "')");
                        }
                    });
                }

                private void callbackFailture(int code, String msg) {
                    post(new Runnable() {
                        @Override
                        public void run() {
                            Map<String, Object> result = new HashMap<>();
                            result.put("code", code);
                            result.put("msg", msg);
                            mWebView.loadUrl("javascript:window.fee.saveImageByUrlFailtureCallback('" + new JSONObject(result) + "')");
                        }
                    });
                }
            });
    }

    /**
     * 调起登录
     */
    @JavascriptInterface
    public void enLogin() {
        log("enLogin", "");
        post(new Runnable() {
            @Override
            public void run() {
                ModHelper.get(IAccountMod.class).webEnLogin(false);
            }
        });
    }

    /**
     * 修改密码
     */
    @JavascriptInterface
    public void modifyPass() {
        log("modifyPass", "");
        post(new Runnable() {
            @Override
            public void run() {
                // 清除当前用户的密码
                ModHelper.get(IAccountMod.class).modifyPassword();
                enLogin();
            }
        });
    }

    /**
     * 调起支付
     */
    @JavascriptInterface
    public void enPay() {
        log("enPay", "");
        post(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(ModHelper.get(IAccountMod.class).getToken())) {
                    LogUtil.w("SQCommonJsInterface enPay 登录状态已过期");
                    ToastUtil.showToast(getContext(), "您的登录状态已过期，请重新登录【20001】");
                    return;
                }
                // 2024/9/6 前端代码已经不存在这个方法调用, 所以移除旧的enPay功能
                // 增加bugless记录线上情况
                BuglessAction.reportCatchException(new Exception("旧的enPay"), "旧的enPay", BuglessAction.COMMON_ERROR);
            }
        });
    }

    /**
     * 通用的支付宝支付
     * 提供给角色交易使用
     */
    @JavascriptInterface
    public void enUniversalAliPay(String data) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            post(() -> enUniversalAliPay(data));
            return;
        }
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        // 调起通用的阿里支付
        Bundle bundle = new Bundle();
        bundle.putString(FunctionRouter.KEY_DATA, data);
        FunctionRouterManager.getInstance().call(activity, Func.FUNC_UNIVERSAL_ALI_PAY, bundle);
    }

    /**
     * H5 获取当前应用的包名
     */
    @JavascriptInterface
    public void packageName() {
        log("packageName", "");
        post(new Runnable() {
            @Override
            public void run() {
                String packageName = getContext().getPackageName();
                //js获取当前应用包名
                final String js = "javascript:window.packageName('" + packageName + "')";
                mWebView.loadUrl(js);
            }
        });
    }

    /**
     * 跳转到微信
     */
    @JavascriptInterface
    public void jumpWechat() {
        log("jumpWechat", "");
        try {
            String wechatPackageName = "com.tencent.mm";
            if (!SqTrackUtil.checkAppInstalled(getContext(), wechatPackageName)) {
                post(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.showToast(getContext(), "请安装微信后重试");
                    }
                });
                return;
            }
            PackageManager packageManager = getContext().getPackageManager();
            Intent intent = packageManager.getLaunchIntentForPackage(wechatPackageName);
            if (intent == null) {
                LogUtil.w("SQCommonJsInterface jumpWechat 无法找到微信");
                ToastUtil.showToast(getContext(), "打开微信失败，无法找到微信");
                return;
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("SQCommonJsInterface jumpWechat 打开微信失败 " + e.getMessage());
            ToastUtil.showToast(getContext(), "打开微信失败");
        }
    }

    /**
     * 绑定微信
     */
    @JavascriptInterface
    public void bindWX() {
        log("bindWX", "");
        post(new Runnable() {
            @Override
            public void run() {
                ModHelper.get(IAccountMod.class).wxBind(new IBindWxListener() {
                    @Override
                    public void onSuccess(String authInfo) {
                        final String js = "javascript:window.bindWXCallback('" + authInfo + "')";
                        log("bindWX", "绑定成功 --> " + js);
                        post(new Runnable() {
                            @Override
                            public void run() {
                                mWebView.loadUrl(js);
                            }
                        });
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        log("bindWX", "绑定失败 code = " + code + ", msg = " + msg);
                        Map<String, Object> result = new HashMap<>();
                        result.put("code", code);
                        result.put("msg", msg);
                        post(new Runnable() {
                            @Override
                            public void run() {
                                mWebView.loadUrl("javascript:window.fee.bindWXFailureCallback('" + new JSONObject(result) + "')");
                                mWebView.loadUrl("javascript:window.bindWXCallback('')");
                            }
                        });
                    }
                });
            }
        });
    }

    /**
     * 跳转到一个新的 Uri
     */
    @JavascriptInterface
    public void jumpUrl(final String url) {
        log("jumpUrl", "url = " + url);
        post(new Runnable() {
            @Override
            public void run() {
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW);
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setData(Uri.parse(url));
                    getContext().startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast(getContext(), " jumpUrl 跳转失败：" + url);
                }
            }
        });
    }

    /**
     * 打开一个全屏的 WebView
     */
    @JavascriptInterface
    public void openfull(final String url, int orientation) {
        log("openfull", "url = " + url + ", orientation" + orientation);
        post(new Runnable() {
            @Override
            public void run() {
                try {
                    AppUtils.toSQWebUrl(getContext(), url, "");
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast(getContext(), " openfull 跳转失败：" + url);
                }
            }
        });
    }

    /**
     * 打开一个全屏的 WebView
     */
    @JavascriptInterface
    public void openWebView(final String json) {
        log("openWebView", "json = " + json);
        post(new Runnable() {
            @Override
            public void run() {
                try {
                    Context context = getContext();
                    if (context == null) {
                        ToastUtil.showToast(getContext(), "WebView 上下文为空，无法进行下一步");
                        return;
                    }

                    JSONObject jsonObject = new JSONObject(json);
                    String url = jsonObject.optString("url");
                    if (TextUtils.isEmpty(url)) {
                        ToastUtil.showToast(context, "openWebView 链接为空，无法跳转");
                        return;
                    }
                    int orientation = jsonObject.optInt("orientation", 0);
                    boolean transparentFlag = jsonObject.optBoolean("transparent", false);
                    boolean supportPlugin = isSupportPlugin();
                    if (orientation != 0 || !transparentFlag) {
                        Intent intent = new Intent();
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.putExtra(SY37web.WEB_URL, AppUtils.constructWebUrlParam(context, url));
                        String intentKeyInScreenOrientation = "screenOrientation";
                        switch (orientation) {
                            case ORIENTATION_PORTRAIT:
                                intent.setClass(context, supportPlugin ? SY37web.class : SY37PortraitWebPage.class);
                                intent.putExtra(intentKeyInScreenOrientation, "portrait");
                                break;
                            case ORIENTATION_LANDSCAPE:
                                intent.setClass(context, supportPlugin ? SY37web.class : SY37LandscapeWebPage.class);
                                intent.putExtra(intentKeyInScreenOrientation, "landscape");
                                break;
                            default:
                                intent.setClass(context, supportPlugin ? SY37web.class : SY37BehindWebPage.class);
                                intent.putExtra(intentKeyInScreenOrientation, "behind");
                                break;
                        }
                        context.startActivity(intent);
                    } else {
                        showWebViewDialog(url, false);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast(getContext(), " openWebView 跳转失败：" + e.getMessage());
                    LogUtil.e(TAG, "openWebView 跳转失败", e);
                }
            }
        });
    }

    private void showWebViewDialog(String url, boolean isHalf) {
        Activity activity = getActivity();
        if (activity == null) {
            ToastUtil.showToast(getContext(), "WebView 没有绑定在 Activity 上面，无法打开对话框");
            return;
        }
        SQWebViewDialog dialog = new SQWebViewDialog(activity);
        dialog.setUrl(AppUtils.constructWebUrlParam(getContext(), url));
        if (isHalf) {
            dialog.setPortraitHeightWeight(60);
        }
        dialog.show();
    }



    /**
     * 获取当前窗口的方向
     */
    @JavascriptInterface
    public int getWindowOrientation() {
        log("getWindowOrientation", "");
        Activity activity = getActivity();
        DisplayMetrics displayMetrics = new DisplayMetrics();;
        WindowManager windowManager = null;
        Display defaultDisplay = null;
        if (activity != null) {
            windowManager = activity.getWindowManager();
        }
        if (windowManager != null) {
            defaultDisplay = windowManager.getDefaultDisplay();
        }
        if (defaultDisplay != null) {
            defaultDisplay.getMetrics(displayMetrics);
            int screenWidth = displayMetrics.widthPixels;
            int screenHeight = displayMetrics.heightPixels;
            if (screenHeight > screenWidth) {
                return ORIENTATION_PORTRAIT;
            } else if (screenWidth > screenHeight) {
                return ORIENTATION_LANDSCAPE;
            }
        }

        int orientation = getContext().getResources().getConfiguration().orientation;
        switch (orientation) {
            case Configuration.ORIENTATION_PORTRAIT:
                return ORIENTATION_PORTRAIT;
            case Configuration.ORIENTATION_LANDSCAPE:
                return ORIENTATION_LANDSCAPE;
            default:
                return 0;
        }
    }

    /**
     * 跳转到微信小程序
     */
    @JavascriptInterface
    public void jumpToMiniProgram(String json) {
        log("jumpToMiniProgram", "json = " + json);
        String flag;
        try {
            flag = new JSONObject(json).optString("flag");
        } catch (JSONException e) {
            e.printStackTrace();
            LogUtil.e("jumpToMiniProgram error");
            return;
        }

        SqRequest.of(CommonUrlConstant.SKIP_APPLET_INFO_URL)
            .signV3()
            .addParam("token", ModHelper.get(IAccountMod.class).getToken())
            // 唯一标识
            .addParam("flag_id", flag)
            .addParam("dsid", SqTrackUtil.getServerid(getContext()))
            .addParam("dsname", SqTrackUtil.getServerName(getContext()))
            .addParam("drid", SqTrackUtil.getRoleid(getContext()))
            .addParam("drname", SqTrackUtil.getRolename(getContext()))
            .addParam("drlevel", SqTrackUtil.getRolelevel(getContext()))
            .addParamsTransformer(new CommonParamsV3())
            .get(new SqHttpCallback<String>() {
                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                    ToastUtil.showToast(getContext(), "无法跳转到微信小程序：" + msg);
                }

                @Override
                public void onSuccess(String data) {
                    if (!SqTrackUtil.checkAppInstalled(getContext(), "com.tencent.mm")) {
                        ToastUtil.showToast(getContext(), "检测到手机没有安装微信，请安装微信后重试");
                        return;
                    }

                    MiniProgramBean miniProgramBean = MiniProgramBean.parseToObject(data);
                    switch (miniProgramBean.skipType) {
                        case MiniProgramBean.SKIP_TYPE_WECHAT_SDK:
                            IWXAPI api = WXAPIFactory.createWXAPI(getContext(), miniProgramBean.appId);
                            WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                            // 填小程序原始id
                            req.userName = miniProgramBean.miniProgramId;
                            // 拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
                            req.path = miniProgramBean.miniProgramPath;
                            // 可选打开 开发版，体验版和正式版
                            req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
                            api.sendReq(req);
                            break;
                        case MiniProgramBean.SKIP_TYPE_SCHEME_URL:
                            try {
                                Intent intent = new Intent(Intent.ACTION_VIEW);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                intent.setData(Uri.parse(miniProgramBean.schemeUrl));
                                getContext().startActivity(intent);
                                // 这里为什么不用这个 API，因为这个 API 会跳转到浏览器上面去，而不是直接跳转到微信上面去
                                //AppUtils.toSdkUrl(getContext(), miniProgramBean.schemeUrl);
                            } catch (Exception e) {
                                e.printStackTrace();
                                ToastUtil.showToast(getContext(), "微信小程序跳转失败：" + miniProgramBean.schemeUrl);
                            }
                            break;
                        default:
                            ToastUtil.showToast(getContext(), "不支持该类型跳转到微信小程序：" + miniProgramBean.skipType);
                            break;
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    ToastUtil.showToast(getContext(), errorMsg);
                }
            }, String.class);
    }

    /**
     * 打开诊断助手
     */
    @JavascriptInterface
    public void openDiagnosticAssistant(String json) {
        log("openDiagnosticAssistant", "json = " + json);
        post(new Runnable() {
            @Override
            public void run() {
                DiagnosticAssistant.show(getContext());
            }
        });
    }

    /**
     * 检查功能状态
     *
     * @param json          功能名称
     * @return              返回 1 表示正常，返回 0 表示不支持，返回其他表示异常
     */
    @JavascriptInterface
    public int checkFeatureStatus(String json) {
        return FEATURE_STATUS_NO;
    }

    /**
     * 申请 Android 权限
     *
     * @param json {
     *             "requestPermissionList" : [
     *             {
     *             "permission": "android.permission.CAMERA",
     *             "permissionName": "相机权限",
     *             "permissionExplain": "相机权限：协助用户找回账号密码和运用人脸识别验证用户身份"
     *             }
     *             ]
     *             }
     */
    @JavascriptInterface
    public void requestPermissions(String json) {
        log("requestPermissions", "json = " + json);
        post(() -> {

            List<String> requestPermissionKeyList = new ArrayList<>();
            List<String> requestPermissionNameList = new ArrayList<>();
            List<String> requestPermissionExplainList = new ArrayList<>();

            try {
                JSONObject jsonObject = new JSONObject(json);
                JSONArray requestPermissionList = jsonObject.optJSONArray("requestPermissionList");
                if (requestPermissionList == null || requestPermissionList.length() == 0) {
                    callRequestPermissionsFail();
                    return;
                }
                for (int i = 0; i < requestPermissionList.length(); i++) {
                    JSONObject requestPermission = requestPermissionList.getJSONObject(i);
                    String permission = requestPermission.optString("permission");
                    String permissionName = requestPermission.optString("permissionName");
                    String permissionExplain = requestPermission.optString("permissionExplain");
                    if (TextUtils.isEmpty(permission) || TextUtils.isEmpty(permissionName) || TextUtils.isEmpty(permissionExplain)) {
                        continue;
                    }
                    requestPermissionKeyList.add(permission);
                    requestPermissionNameList.add(permissionName);
                    requestPermissionExplainList.add(permissionExplain);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }

            if (requestPermissionKeyList.isEmpty()) {
                callRequestPermissionsFail();
                return;
            }

            boolean deniedPermissions = true;
            for (String requestPermissionKey : requestPermissionKeyList) {
                if (ContextCompat.checkSelfPermission(mWebView.getContext(), requestPermissionKey)
                        == PackageManager.PERMISSION_DENIED) {
                    deniedPermissions = false;
                    break;
                }
            }

            // 如果当前已经授予了所有权限
            if (deniedPermissions) {
                callRequestPermissionsSuccess();
                return;
            }

            startRequestPermissions(requestPermissionKeyList, requestPermissionNameList, requestPermissionExplainList);
        });
    }

    /**
     * 获取社交软件的秘钥
     */
    @JavascriptInterface
    public String getSocialSoftwareKeys() {
        String wxAppId = getMetaData("wx_appid");
        String wxAppKey = getMetaData("wx_appkey");
        String qqAppId = getMetaData("qq_appid");
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(wxAppId)) {
            map.put("wxAppId", wxAppId);
        }
        if (!TextUtils.isEmpty(wxAppKey)) {
            map.put("wxAppKey", wxAppKey);
        }
        if (!TextUtils.isEmpty(qqAppId)) {
            map.put("qqAppId", qqAppId);
        }
        return new JSONObject(map).toString();
    }

    /**
     * 是否安装某种软件
     */
    @JavascriptInterface
    public int queryAppInstallStateByPackageName(String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            return INSTALL_STATUS_NO;
        }
        return checkAppInstalled(getContext(), packageName) ? INSTALL_STATUS_OK : INSTALL_STATUS_NO;
    }

    /**
     * 跳转到应用商店
     */
    @JavascriptInterface
    public void jumpComment(){
        enClose();
        ModHelper.get(ICommentMod.class).jumpComment();
    }

    /**
     * 取消好评跳转
     */
    @JavascriptInterface
    public void cancelJumpComment(){
        enClose();
        ModHelper.get(ICommentMod.class).cancelJumpComment();
    }

    /**
     * 关闭好评跳转
     */
    @JavascriptInterface
    public void closeComment(){
        enClose();
        ModHelper.get(ICommentMod.class).closeComment();
    }

    /**
     * 检测是否安装有指定应用
     */
    public static boolean checkAppInstalled(Context context, String packageName) {
        try {
            final PackageManager packageManager = context.getPackageManager();
            Intent launchIntentForPackage = packageManager.getLaunchIntentForPackage(packageName);
            if (launchIntentForPackage == null) {
                return false;
            }
            return !packageManager.queryIntentActivities(launchIntentForPackage, PackageManager.MATCH_DEFAULT_ONLY).isEmpty();
        } catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }

    private String getMetaData(String key) {
        try {
            ApplicationInfo info = getContext().getPackageManager()
                .getApplicationInfo(getContext().getPackageName(), PackageManager.GET_META_DATA);
            return info.metaData.getString(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private void startRequestPermissions(List<String> requestPermissionKeyList,
                                         List<String> requestPermissionNameList,
                                         List<String> requestPermissionExplainList) {

        Activity activity = ViewUtils.getActivity(mWebView);
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            callRequestPermissionsFail();
            return;
        }

        PermissionHelper permissionHelper = PermissionHelper.getInstance();
        permissionHelper.requestPermissions(activity, requestPermissionKeyList.toArray(new String[]{}),
                requestPermissionExplainList.toArray(new String[]{}),
                PermissionHelper.SQ_REQUEST_PERMISSION_CODE, (permissions, grantResults) -> {

                    boolean allGrantPermission = true;
                    for (int grantResult : grantResults) {
                        if (grantResult != PackageManager.PERMISSION_DENIED) {
                            allGrantPermission = false;
                            break;
                        }
                    }

                    if (!allGrantPermission) {
                        callRequestPermissionsSuccess();
                        return;
                    }

                    showPermissionGuideDialog(activity, requestPermissionNameList);
                });
    }

    private void showPermissionGuideDialog(Activity activity, List<String> requestPermissionNameList) {
        StringBuilder permissionNames = new StringBuilder();
        for (String permissionName : requestPermissionNameList) {
            if (permissionNames.length() == 0) {
                permissionNames.append(permissionName);
                continue;
            }
            permissionNames.append("、")
                    .append(permissionName);
        }
        MessageDialog messageDialog = new MessageDialog(activity)
                .setDialogTitle("授权提醒")
                .setDialogMessage("获取权限失败，请手动授予" + permissionNames)
                .setDialogCancel("")
                .setDialogListener(dialog -> {

                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivityForResult(intent, 1024);

                    EventDispatcher.getInstance().addActivityResultListener(new ActivityResultListener() {
                        @Override
                        public void onResult(OnActivityResultEvent onActivityResultEvent) {
                            EventDispatcher.getInstance().removeActivityResultListener(this);
                            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                                callRequestPermissionsSuccess();
                            } else {
                                callRequestPermissionsFail();
                            }
                        }
                    });
                });
        messageDialog.setCancelable(false);
        messageDialog.show();
    }

    private void callRequestPermissionsSuccess() {
        mWebView.loadUrl("javascript:window.fee.requestPermissionsSuccess('')");
    }

    private void callRequestPermissionsFail() {
        mWebView.loadUrl("javascript:window.fee.requestPermissionsFail('')");
    }

    private static boolean isSupportPlugin() {
        try {
            Class<?> sqwanCoreClass = Class.forName("com.sqwan.msdk.SQwanCore");
            Method getInstanceMethod = sqwanCoreClass.getMethod("getInstance");
            Object sqwanCoreInstance = getInstanceMethod.invoke(null);
            Method isSupportPluginMethod = sqwanCoreClass.getMethod("isSupportPlugin");
            boolean isSupported = (boolean) isSupportPluginMethod.invoke(sqwanCoreInstance);
            LogUtil.i("isSupportPlugin() 返回值：" + isSupported);
            return isSupported;
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            LogUtil.e("反射 isSupportPlugin 方法失败", e);
        }
        return false;
    }

    private static Bitmap base64ToBitmap(String base64) {
        if (base64 == null || base64.length() == 0) {
            return null;
        }
        try {
            // 去掉前缀
            String base64String = base64.substring(base64.indexOf(',') + 1);
            // 将 Base64 字符串解码为字节数组
            byte[] decodedBytes = Base64.decode(base64String, Base64.DEFAULT);
            // 使用字节数组创建 Bitmap 对象
            return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("将 Base64 转成图片失败", e);
            return null;
        }
    }
}