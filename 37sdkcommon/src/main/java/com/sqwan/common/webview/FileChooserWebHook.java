package com.sqwan.common.webview;

import static com.sqwan.base.ActivityRequestCode.ACTIVITY_RESULT_CODE_FILE_CHOOSER;

import android.app.Activity;
import android.content.ClipData;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient.FileChooserParams;
import android.webkit.WebView;
import com.sq.webview.SimpleWebHook;
import com.sqwan.base.ActivityResultListener;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ViewUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
public class FileChooserWebHook extends SimpleWebHook {

    public void log(String methodName, String message) {
        LogUtil.i("FileChooserWebHook#" + methodName + ": " + message);
    }

    /**
     * 选择手机文件回调方法
     *
     * @param filePathCallback 回调监听
     * @param fileChooserParams 文件选择参数
     */
    @Override
    public boolean onShowFileChooser(WebView webView, final ValueCallback<Uri[]> filePathCallback,
        FileChooserParams fileChooserParams) {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.WEB_JUMP_PIC_SELECT);

        Activity activity = ViewUtils.getActivity(webView);
        if (activity == null) {
            return true;
        }

        Intent intent = fileChooserParams.createIntent();
        String[] mimeTypes = fileChooserParams.getAcceptTypes();
        boolean multipleSelect = fileChooserParams.getMode() == FileChooserParams.MODE_OPEN_MULTIPLE;
        if (mimeTypes != null && mimeTypes.length > 0 && !TextUtils.isEmpty(mimeTypes[0])) {
            // 设置要过滤的文件类型
            intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
        }
        // 是否是多选模式
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, multipleSelect);
        activity.startActivityForResult(Intent.createChooser(intent, fileChooserParams.getTitle()),
            ACTIVITY_RESULT_CODE_FILE_CHOOSER);

        EventDispatcher.getInstance().addActivityResultListener(new ActivityResultListener() {

            @Override
            public void onResult(OnActivityResultEvent event) {
                int requestCode = event.getRequestCode();
                if (requestCode != ACTIVITY_RESULT_CODE_FILE_CHOOSER) {
                    return;
                }

                // 移除监听对象，避免内存泄漏
                EventDispatcher.getInstance().removeActivityResultListener(this);

                log("onShowFileChooser", "call OnActivityResult");

                if (filePathCallback == null) {
                    log("onShowFileChooser", "filePathCallback == null");
                    return;
                }

                int resultCode = event.getResultCode();
                Intent data = event.getIntent();

                List<Uri> uris = new ArrayList<>();
                if (resultCode == Activity.RESULT_OK && data != null) {
                    Uri uri = data.getData();
                    if (uri != null) {
                        // 如果用户只选择了一个文件
                        uris.add(uri);
                        log("onShowFileChooser", "uri = " + uri);
                    } else {
                        // 如果用户选择了多个文件
                        ClipData clipData = data.getClipData();
                        if (clipData != null) {
                            log("onShowFileChooser", "clipData = " + clipData);
                            for (int i = 0; i < clipData.getItemCount(); i++) {
                                uris.add(clipData.getItemAt(i).getUri());
                            }
                        } else {
                            log("onShowFileChooser", "clipData = null");
                        }
                    }
                }
                // 不管用户最后有没有选择文件，最后必须要调用 onReceiveValue，如果没有调用就会导致网页再次点击上传无响应
                filePathCallback.onReceiveValue(uris.toArray(new Uri[0]));
                log("onShowFileChooser", "call filePathCallback.onReceiveValue");
            }
        });
        return true;
    }
}