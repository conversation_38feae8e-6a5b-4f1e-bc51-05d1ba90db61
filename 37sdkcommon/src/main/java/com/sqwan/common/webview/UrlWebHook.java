package com.sqwan.common.webview;

import android.net.Uri;
import android.webkit.WebView;
import com.sq.webview.SimpleWebHook;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
public class UrlWebHook extends SimpleWebHook {

    /**
     * 跳转到其他链接
     */
    @Override
    public boolean shouldOverrideUrlLoading(WebView view, String url) {
        String scheme = Uri.parse(url).getScheme();
        if (scheme == null) {
            return false;
        }
        switch (scheme) {
            // 如果这是跳链接操作
            case "http":
            case "https":
                view.loadUrl(url);
                break;
            default:
                break;
        }
        return true;
    }
}