package com.sqwan.common.webview;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import android.support.v4.content.ContextCompat;
import android.webkit.PermissionRequest;
import com.sq.webview.SimpleWebHook;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.AudioPermissionHelper;
import com.sqwan.common.util.ViewUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
public class PermissionWebHook extends SimpleWebHook {

    @Override
    public void onPermissionRequest(PermissionRequest request) {
        List<String> permissions = new ArrayList<>();
        String[] requestResources = request.getResources();
        if (requestResources == null) {
            // 如果网页请求的资源为空
            request.deny();
            return;
        }

        for (String resource : requestResources) {

            // 如果网页请求的是摄像头资源
            if (PermissionRequest.RESOURCE_VIDEO_CAPTURE.equals(resource)) {
                permissions.add(Manifest.permission.CAMERA);
                continue;
            }

            // 如果网页请求的是麦克风资源
            if (PermissionRequest.RESOURCE_AUDIO_CAPTURE.equals(resource)) {
                permissions.add(Manifest.permission.RECORD_AUDIO);
                continue;
            }

            // 如果网页请求的是别的资源
            request.deny();
            return;
        }

        if (permissions.isEmpty()) {
            // 如果没有请求权限，则直接拒绝
            request.deny();
            return;
        }

        List<String> deniedPermissions = new ArrayList<>();
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(mWebView.getContext(), permission)
                == PackageManager.PERMISSION_DENIED) {
                deniedPermissions.add(permission);
            }
        }

        if (deniedPermissions.isEmpty()) {
            LogUtil.i("PermissionWebHook is grant");
            request.grant(requestResources);
            return;
        }

        //只处理麦克风权限，其他权限还是按照原来逻辑拒绝授权
        if (deniedPermissions.contains(Manifest.permission.RECORD_AUDIO)) {
            Activity activity = ViewUtils.getActivity(mWebView);
            if (activity == null) {
                LogUtil.i("PermissionWebHook.AUDIO_PERMISSION is denied, Activity = null");
                request.deny();
            } else {
                LogUtil.i("PermissionWebHook.AUDIO_PERMISSION is requesting");
                AudioPermissionHelper.requestAudioPermission(activity, request);
            }
        } else {
            LogUtil.i("PermissionWebHook is denied");
            request.deny();
        }
    }
}