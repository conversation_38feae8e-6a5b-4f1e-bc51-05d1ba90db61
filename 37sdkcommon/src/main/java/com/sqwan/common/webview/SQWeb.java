package com.sqwan.common.webview;

import android.content.Context;
import android.os.Build;
import android.webkit.WebSettings;
import android.webkit.WebView;
import com.sq.tool.network.EventReporter;
import com.sq.tool.network.ExceptionReporter;
import com.sq.webview.SQWebAgent;
import com.sq.webview.WebHook;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
public class SQWeb {

    private static volatile SQWebAgent sInstance;

    public static SQWebAgent getInstance() {
        if (sInstance == null) {
            synchronized (SQWeb.class) {
                if (sInstance == null) {
                    sInstance = newAgent();
                }
            }
        }

        return sInstance;
    }

    private static SQWebAgent newAgent() {
        List<WebHook> globalWebHook = new ArrayList<>();
        globalWebHook.add(new UrlWebHook());
        globalWebHook.add(new PermissionWebHook());
        globalWebHook.add(new FileChooserWebHook());
        SQWebAgent.Builder builder = new SQWebAgent.Builder()
            .setCollectWhiteScreenEnable(true)
            .enableLocalH5(true)
            .enableMonitor()
            .setExceptionReporter(new ExceptionReporter())
            .setEventReporter(new EventReporter())
            .setGlobalWebHooks(globalWebHook)
            .configWebViewSetting(SQWeb::initWebViewSettings);
        return builder.build();
    }

    private static void initWebViewSettings(WebView webView) {
        if (webView == null) {
            return;
        }
        WebSettings settings = webView.getSettings();
        // 设置解码 html 页面时使用的默认文本编码名称（默认为 UTF-8）
        settings.setDefaultTextEncodingName("utf-8");
        // 设置是否需要设置初始焦点
        settings.setNeedInitialFocus(true);
        // 设置 WebView 是否保存表单数据
        settings.setSaveFormData(false);
        // 设置 WebView 是否保存密码
        settings.setSavePassword(false);
        // 设置布局算法，这将导致 WebView 的重新布局（默认值为 LayoutAlgorithm#NARROW_COLUMNS）
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
        // 设置缓存模式
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        // 开启 JavaScript
        settings.setJavaScriptEnabled(true);
        // 允许 WebView 访问文件
        settings.setAllowFileAccess(true);
        // 将图片调整到适合 WebView 的大小
        settings.setUseWideViewPort(true);
        // 设置网页自适应屏幕的大小
        settings.setLoadWithOverviewMode(true);

        // 设置 WebView 是否支持缩放（默认为 true）
        settings.setSupportZoom(false);
        // 设置是否使用内置的内置机制（缩放控制器和手势缩放，默认为 false）
        settings.setBuiltInZoomControls(false);
        // 设置是否显示内置的缩放控制器
        settings.setDisplayZoomControls(false);

        // 允许网页弹对话框
        settings.setJavaScriptCanOpenWindowsAutomatically(true);
        // 加快网页加载完成的速度，等页面完成再加载图片
        settings.setLoadsImagesAutomatically(true);
        // 本地 DOM 存储（解决加载某些网页出现白板现象）
        settings.setDomStorageEnabled(true);
        // 禁止或允许 WebView 从网络上加载图片
        // 需要注意的是，如果设置是从禁止到允许的转变的话
        // 图片数据并不会在设置改变后立刻去获取，而是在 WebView 调用 reload 的时候才会生效
        settings.setBlockNetworkImage(false);
        settings.setTextZoom(100); // 通过百分比来设置文字的大小，默认值是100。

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 解决 Android 5.0 上 WebView 默认不允许加载 Http 与 Https 混合内容
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        // 设置此 WebView 的初始比例，0 表示默认值
        webView.setInitialScale(0);
        // 设置触摸可获取焦点
        webView.requestFocusFromTouch();
        // 请求焦点
        webView.requestFocus();
        // 设置可获取焦点
        webView.setFocusable(true);

        // 不显示滚动条
        webView.setVerticalScrollBarEnabled(false);
        webView.setHorizontalScrollBarEnabled(false);
    }

    public static void enableCache(WebView webView) {
        if (webView == null || webView.getContext() == null) {
            return;
        }
        Context context = webView.getContext();
        WebSettings settings = webView.getSettings();
        webView.getSettings().setAppCacheMaxSize(1024 * 1024 * 5);
        // 设置WebView 缓存开关
        settings.setAppCacheEnabled(true);
        // 设置 WebView 缓存模式
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        // 设置 WebView 缓存目录
        settings.setAppCachePath(context.getCacheDir().getAbsolutePath());
    }
}
