package com.sqwan.common.webview;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.DownloadListener;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient.CustomViewCallback;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sq.sdk.tool.util.NetworkUtils;
import com.sq.tools.Logger;
import com.sq.webview.SimpleWebHook;
import com.sq.webview.WebFunctionWrapper;
import com.sqwan.common.dialog.FullScreenDialog;
import com.sqwan.common.util.AndroidBug5497Workaround;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.web.ExitAnimParams;
import com.sqwan.common.web.WebViewToolBar;

import com.sqwan.msdk.config.ConfigManager;
import notchtools.geek.com.notchtools.NotchTools;

/**
 * author : 黄锦群
 * time   : 2022/05/05
 * desc   : 通用 WebView 对话框封装
 */
public class SQWebViewDialog extends FullScreenDialog
        implements View.OnTouchListener,
        WebViewToolBar.WebToolBarClickListener {

    /**
     * 滚动阈值
     */
    private static final int SCROLL_THRESHOLD = 50;

    /**
     * 滑动动作：下滑
     */
    private static final int SCROLL_DOWN = 0;

    /**
     * 滑动动作：上滑
     */
    private static final int SCROLL_UP = 1;

    private static final Handler HANDLER = new Handler(Looper.getMainLooper());

    protected Context mContext;

    /**
     * 根布局视图
     */
    private LinearLayout mRootLayout;

    /**
     * 网页视图
     */
    protected SQWebView mWebView;

    /**
     * 网络错误时的占位图
     */
    protected View mErrorNetView;

    /**
     * 加载中的占位视图
     */
    private RelativeLayout mLoadingLayout;
    /**
     * 加载中的占位图标
     */
    private ImageView mLoadingIcon;
    /**
     * 加载中的占位文案
     */
    private AnimationDrawable mAnimationDrawable;

    /**
     * WebView 底部控制栏
     */
    private WebViewToolBar mWebViewToolBar;
    private WebViewToolBar.WebToolBarClickListener mWebBarClickListener;
    private boolean mShowWebBar;

    /**
     * 当前加载的 url
     */
    protected String mUrl;

    /**
     * 网页是否加载完成
     */
    private boolean mLoadingFinish = true;

    //是否显示全屏视频
    private boolean mShowVideo;

    /**
     * 加载超时任务
     */
    private Runnable mTimeOutRunnable;

    /**
     * 是否展示 WebView 的工具栏
     */
    private boolean mShowBar;

    /**
     * WebView 按下时的 Y 坐标
     */
    private float mViewDownY;

    /**
     * 当前的滑动方向
     */
    private int mCurScrollDirection = -1;

    /**
     * 竖屏状态下显示高度权重
     */
    private int mPortraitHeightWeight = 100;

    /**
     * WebView 背景颜色
     */
    private int mWebViewBackgroundColor = Color.TRANSPARENT;

    /**
     * 是否允许跳转到其他 url
     */
    private boolean mAllowJumpURL = true;

    private FrameLayout mFlVideoContainer;
    /**
     * 显示加载中的任务
     */
    private final Runnable mShowLoadingRunnable = new Runnable() {
        @Override
        public void run() {
            showGifLoadingImage();
        }
    };

    /**
     * 通统一弹窗-js是否有调用过关闭
     */
    private boolean unionCloseCalled = false;

    /**
     * js调用统一弹窗关闭是否需要重试
     */
    private boolean needUnionCloseRetry = false;

    private boolean mAdaptNotchScreen = false;

    public SQWebViewDialog(Context context) {
        super(context);
        mContext = context;
    }

    public SQWebViewDialog(Context context, int themeResId) {
        super(context, themeResId);
        mContext = context;
    }

    public SQWebViewDialog(Context context, boolean cancelable, DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        mContext = context;
    }

    @SuppressLint("WrongConstant")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Window window = getWindow();
        View decorView;
        if (window != null) {
            decorView = window.getDecorView();
            // 设置 Dialog 布局铺满屏幕
            decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            decorView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    // 隐藏导航栏
                    StatusBarUtil.hideSystemUI(getWindow());
                }
            });

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                // 让应用程序的内容延伸到刘海区域
                layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                window.setAttributes(layoutParams);
            }
        }
        window.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);

        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);

        mRootLayout = (LinearLayout) inflater.inflate(SqResUtils.getLayoutId(getContext(), getLayoutName()), null);
        setContentView(mRootLayout);

        initView(mRootLayout);
        initData();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (mContext instanceof Activity) {
            AndroidBug5497Workaround.assistActivity((Activity) mContext, mRootLayout);
        }
    }

    protected String getLayoutName() {
        return "sy37_web_view_dialog";
    }

    private void showGifLoadingImage() {
        if (mAnimationDrawable != null && !ConfigManager.getInstance(mContext).isSimplifiedSDK()) {
            mAnimationDrawable.start();
        }
        mLoadingLayout.setVisibility(View.VISIBLE);
    }

    private void hideGifLoadingImage() {
        if (mAnimationDrawable != null && !ConfigManager.getInstance(mContext).isSimplifiedSDK()) {
            mAnimationDrawable.stop();
        }
        mLoadingLayout.setVisibility(View.GONE);
    }

    private boolean mCancelable = true;

    public boolean isCancelable() {
        return mCancelable;
    }

    @Override
    public void setCancelable(boolean flag) {
        super.setCancelable(flag);
        mCancelable = flag;
    }

    @Override
    public void setCanceledOnTouchOutside(boolean cancel) {
        super.setCanceledOnTouchOutside(cancel);
        mCancelable = cancel;
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initWebView(SQWebView webView) {
        WebFunctionWrapper functionWrapper = SQWeb.getInstance().bind(webView)
            .replace(new CustomUrlWebHook(), UrlWebHook.class)
            .addWebHook(new CustomWebViewClient())
            .addWebHook(new SimpleWebHook() {
                @Override
                public void onShowCustomView(View view, CustomViewCallback callback) {
                    super.onShowCustomView(view, callback);
                    mShowVideo = true;
                    mFlVideoContainer.setVisibility(View.VISIBLE);
                    mFlVideoContainer.addView(view);
                }

                @Override
                public void onHideCustomView() {
                    super.onHideCustomView();
                    mFlVideoContainer.removeAllViews();
                    mFlVideoContainer.setVisibility(View.GONE);
                    mShowVideo = false;
                }
            });
        SQWeb.getInstance().checkLocalH5Enable(mUrl, enable -> {
            if (!enable) {
                functionWrapper.disableLocalH5();
            }
            loadUrl();
        });
        SQWeb.enableCache(webView);
        // 设置 WebView 调试模式
        WebView.setWebContentsDebuggingEnabled(true);

        WebSettings settings = webView.getSettings();
        // 设置布局算法，这将导致 WebView 的重新布局（默认值为 LayoutAlgorithm#NARROW_COLUMNS）
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        // 通过百分比来设置文字的大小，默认值是100。
        settings.setTextZoom(100);

        webView.setOnTouchListener(this);
        webView.setBackgroundColor(mWebViewBackgroundColor);

        webView.addJavascriptInterface(new CustomJsObj(webView), SQCommonJsInterface.INTERFACE_NAME);
        // 支持下载连接下载文件
        webView.setDownloadListener(new CustomDownloadListener());
    }

    protected void initView(View view) {
        mWebView = view.findViewById(SqResUtils.getId(getContext(), "wb_web_view_dialog_webview"));
        mLoadingLayout = findViewById(SqResUtils.getId(getContext(), "rl_web_view_dialog_loading_layout"));
        mLoadingIcon = findViewById(SqResUtils.getId(getContext(), "iv_web_view_dialog_loading_icon"));
        mErrorNetView = view.findViewById(SqResUtils.getId(getContext(), "nev_web_view_dialog_loading_error"));
        mWebViewToolBar = view.findViewById(SqResUtils.getId(getContext(), "wvtb_web_view_dialog_tool_bar"));
        mFlVideoContainer = view.findViewById(SqResUtils.getId(getContext(), "fl_video_container"));
        initWebView(mWebView);
        mWebViewToolBar.setWebToolBarClickListener(this);

        mRootLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isCancelable()) {
                    return;
                }
                dismiss();
            }
        });

        if (mPortraitHeightWeight != 100) {
            LinearLayout webDialogView = findViewById(SqResUtils.getId(getContext(), "ll_web_dialog_view"));
            ViewGroup contentLayout = findViewById(SqResUtils.getId(getContext(), "fl_web_view_dialog_content"));
            int screenWidth = DisplayUtil.getScreenWidth(mContext);
            int screenHeight = DisplayUtil.getScreenHeight(mContext);
            log("屏幕宽度；" + screenWidth + " 屏幕高度：" + screenHeight);
            Configuration configuration = mContext.getResources().getConfiguration();
            int orientation = configuration.orientation;
            LinearLayout.LayoutParams layoutParams;
            if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                webDialogView.setOrientation(LinearLayout.HORIZONTAL);
                webDialogView.setGravity(Gravity.START);
                layoutParams = new LinearLayout.LayoutParams(screenHeight, contentLayout.getLayoutParams().height);
            } else {
                webDialogView.setOrientation(LinearLayout.VERTICAL);
                webDialogView.setGravity(Gravity.BOTTOM);
                layoutParams = new LinearLayout.LayoutParams(contentLayout.getLayoutParams().width, 0, mPortraitHeightWeight);
            }

            View notchView = findViewById(SqResUtils.getId(getContext(), "v_web_view_dialog_notch"));
            if (isNotchAffected()) {
                notchView.setVisibility(View.VISIBLE);
                int notchWidth = NotchTools.getFullScreenTools().getNotchHeight(getWindow());
                if (notchWidth > 0) {
                    LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) notchView.getLayoutParams();
                    params.width = notchWidth;
                    notchView.setLayoutParams(params);
                }
            }

            contentLayout.setLayoutParams(layoutParams);
        }

        if (mAdaptNotchScreen) {
            View statusBarPlace = findViewById(SqResUtils.getId(getContext(), "v_status_bar_place"));
            statusBarPlace.setVisibility(View.VISIBLE);
            int notchHeight = NotchTools.getFullScreenTools().getStatusHeight(getWindow());
            if (notchHeight > 0) {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) statusBarPlace.getLayoutParams();
                params.height = notchHeight;
                params.width = 0;
                statusBarPlace.setLayoutParams(params);
            }
        }

    }

    public void setAdaptNotchScreen(boolean adaptNotchScreen) {
        mAdaptNotchScreen = adaptNotchScreen;
    }

    protected void initData() {
        refreshWebBar(isShowWebBar());

        // js 获取当前应用包名
        final String js = "javascript:window.packageName('" + mContext.getPackageName() + "')";
        mWebView.loadUrl(js);

        if (mLoadingIcon != null && !ConfigManager.getInstance(mContext).isSimplifiedSDK()) {
            mLoadingIcon.setBackgroundResource(SqResUtils.getDrawableId(getContext(), "webview_loading"));
            mAnimationDrawable = (AnimationDrawable) mLoadingIcon.getBackground();
        }

    }

    @Override
    public void show() {
        if (TextUtils.isEmpty(mUrl)) {
            ToastUtil.showToast(mContext, "打开失败，网址是空的");
            return;
        }
        super.show();
    }

    @Override
    public void dismiss() {
        removeCallbacks(mShowLoadingRunnable);
        if (mPortraitHeightWeight == 100) {
            super.dismiss();
            return;
        }

        Window window = getWindow();
        if (window == null) {
            return;
        }
        View decorView = window.getDecorView();
        animExit(decorView);
    }



    private void animExit(View decorView) {
        ObjectAnimator animator;
        if (mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            animator = ObjectAnimator.ofFloat(decorView, "translationY", DisplayUtil.getScreenHeight(getContext()));
        } else {
            animator = ObjectAnimator.ofFloat(decorView, "translationX", -DisplayUtil.getScreenWidth(getContext()));
        }

        animator.setDuration(500);
        animator.addListener(new AnimatorListenerAdapter() {

            @Override
            public void onAnimationEnd(Animator animation) {
                SQWebViewDialog.super.dismiss();
            }
        });
        animator.start();

    }

    @Override
    public boolean onKeyDown(int keyCode, @NonNull KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && mWebView != null && mWebView.canGoBack() && !mShowVideo) {
            mWebView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public void setPortraitHeightWeight(int portraitHeightWeight) {
        mPortraitHeightWeight = portraitHeightWeight;
    }

    public void setWebViewBackgroundColor(int color) {
        mWebViewBackgroundColor = color;
        if (mWebView == null) {
            return;
        }
        mWebView.setBackgroundColor(mWebViewBackgroundColor);
    }

    public void setAllowJumpURL(boolean enable) {
        mAllowJumpURL = enable;
    }

    public void post(Runnable action) {
        HANDLER.post(action);
    }

    public void postDelayed(Runnable runnable, long delayMillis) {
        HANDLER.postDelayed(runnable, delayMillis);
    }

    public void removeCallbacks(Runnable runnable) {
        HANDLER.removeCallbacks(runnable);
    }

    public void log(String message) {
        Logger.info("SQWebViewDialog", message);
    }

    public SQWebView getWebView() {
        return mWebView;
    }

    protected void loadUrl() {
        if (NetworkUtils.isNetworkConnected(mContext)) {
            if (mWebView != null) {
                log("SQWebViewDialog loadUrl");
                mWebView.loadUrl(mUrl);
                timeOutCheck();
            }
        } else {
            if (mErrorNetView != null) {
                mErrorNetView.setVisibility(View.VISIBLE);
            }
        }
    }

    public void setWebToolBarClickListener(WebViewToolBar.WebToolBarClickListener webToolBarClickListener) {
        this.mWebBarClickListener = webToolBarClickListener;
    }

    private void timeOutCheck() {
        if (mTimeOutRunnable == null) {
            mTimeOutRunnable = new Runnable() {
                @Override
                public void run() {
                    if (mLoadingFinish) {
                        return;
                    }

                    log("网络连接7.5秒超时");
                    timeOut();
                }
            };
        }
        HANDLER.postDelayed(mTimeOutRunnable, 7500);
    }

    protected void timeOut() {
    }

    protected void jsEnLogin() {
    }

    protected void jsCertificate(String code, String msg) {
    }

    protected void jsClose(final String tag, final String data) {
        dismiss();
        if (TextUtils.isEmpty(tag)) {
            return;
        }
        if (tag.equals("exitGame")) {
            log("退出游戏");
            if (mContext instanceof Activity) {
                ((Activity) mContext).finish();
            }
            System.exit(0);
        }
    }

    protected void jsOpenUrl(String url) {
        AppUtils.toSdkUrl(getContext(), url);
    }

    protected void onPageStarted(WebView view, String url, Bitmap favicon) {
        postDelayed(mShowLoadingRunnable, 1000);
    }

    protected void onPageFinished(WebView view, String url) {
        HANDLER.removeCallbacks(mShowLoadingRunnable);
        hideGifLoadingImage();
    }

    public void setUrl(String url) {
        mUrl = url;
    }

    /**
     * {@link WebViewToolBar.WebToolBarClickListener}
     */

    @Override
    public void onClickBack() {
        if (mWebView.canGoBack()) {
            mWebView.goBack();
        }
        if (mWebBarClickListener != null) {
            mWebBarClickListener.onClickBack();
        }
    }

    @Override
    public void onClickForward() {
        if (mWebView.canGoForward()) {
            mWebView.goForward();
        }
        if (mWebBarClickListener != null) {
            mWebBarClickListener.onClickForward();
        }
    }

    @Override
    public void onClickRefresh() {
        mWebView.reload();
        if (mWebBarClickListener != null) {
            mWebBarClickListener.onClickRefresh();
        }
    }

    @Override
    public void onClickClose() {
        dismiss();
        if (mWebBarClickListener != null) {
            mWebBarClickListener.onClickClose();
        }
    }

    /**
     * {@link View.OnTouchListener}
     */

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouch(View view, MotionEvent event) {
        return false;
    }

    /**
     * 提供给js外部调用的对象
     */
    public class CustomJsObj extends SQCommonJsInterface {

        public CustomJsObj(@NonNull SQWebView webView) {
            super(webView);
        }

        @JavascriptInterface
        public void enClose(final String tag, final String data) {
            log("enClose", "tag = " + tag + ", data = " + data);
            post(new Runnable() {
                @Override
                public void run() {
                    jsClose(tag, data);
                }
            });
        }

        @JavascriptInterface
        @Override
        public void sqOpenUrl(final String url) {
            log("sqOpenUrl", "url = " + url);
            post(new Runnable() {
                @Override
                public void run() {
                    jsOpenUrl(url);
                }
            });
        }

        @Override
        @JavascriptInterface
        public void enClose() {
            log("enClose", "");
            post(new Runnable() {
                @Override
                public void run() {
                    jsClose("", "");
                }
            });
        }

        @JavascriptInterface
        public void sqOpenUrl() {
            log("sqOpenUrl", "");
            post(new Runnable() {
                @Override
                public void run() {
                    jsOpenUrl("");
                }
            });
        }

        @JavascriptInterface
        public String getPopData() {
            log("getPopData", "");
            return SQWebViewDialog.this.getPopData();
        }

        /**
         * 工具栏显示与隐藏
         *
         * @param visible
         */
        @JavascriptInterface
        public void handleToolbar(final String visible) {
            log("handleToolbar", "visible = " + visible);
            post(new Runnable() {
                @Override
                public void run() {
                    if ("0".equals(visible)) {
                        refreshWebBar(false);
                    } else if ("1".equals(visible)) {
                        refreshWebBar(true);
                    }
                }
            });
        }

        @JavascriptInterface
        @Override
        public void enLogin() {
            super.enLogin();
            post(new Runnable() {
                @Override
                public void run() {
                    jsEnLogin();
                }
            });
        }

        @JavascriptInterface
        public void certificate(final String code, final String msg) {
            log("certificate", "code = " + code + ", msg = " + msg);
            post(new Runnable() {
                @Override
                public void run() {
                    jsCertificate(code, msg);
                }
            });
        }

        /**
         * 统一弹窗关闭时-是否要重试
         * retry：1/重试 0/不重试
         *
         * @param retry
         */
        @JavascriptInterface
        public void unionClose(String retry) {
            log("unionClose", "unionClose = " + retry);
            unionCloseCalled = true;
            needUnionCloseRetry = !TextUtils.isEmpty(retry) && "1".equals(retry);
        }

    }


    /**
     * js是否有调用过通用弹窗关闭，如果有则根据js的判断是否需要重试，否则根据webUrl判断
     *
     * @return
     */
    public boolean isUnionCloseCalled() {
        return this.unionCloseCalled;
    }

    /**
     * js调用通用弹窗时是否需要重试
     *
     * @return
     */
    public boolean isNeedUnionCloseRetry() {
        return this.needUnionCloseRetry;
    }

    /**
     * WebView 下载监听器
     */
    public class CustomDownloadListener implements DownloadListener {

        @Override
        public void onDownloadStart(String url, String userAgent, String contentDisposition,
                                    String mimetype, long contentLength) {
            log("WebView 收到下载请求，url = " + url);
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
        }
    }

    public void setShowWebBar(boolean showWebBar) {
        mShowWebBar = showWebBar;
    }

    protected boolean isShowWebBar() {
        return mShowWebBar;
    }

    protected void refreshWebBar(boolean showWebBar) {
        mShowBar = showWebBar;
        if (mWebViewToolBar == null) {
            return;
        }
        mWebViewToolBar.setVisibility(mShowBar ? View.VISIBLE : View.GONE);
    }

    public String getPopData() {
        return "";
    }

    private class CustomWebViewClient extends SimpleWebHook {

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            mLoadingFinish = false;
            SQWebViewDialog.this.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            mLoadingFinish = true;
            HANDLER.removeCallbacks(mTimeOutRunnable);
            SQWebViewDialog.this.onPageFinished(view, url);
        }

        @Override
        public void onLoadResource(WebView view, String url) {
            super.onLoadResource(view, url);
            handleJump();
        }
    }

    private class CustomUrlWebHook extends UrlWebHook {

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            if (!mAllowJumpURL) {
                return false;
            }
            return super.shouldOverrideUrlLoading(view, url);
        }
    }

    /**
     * 处理页面切换时工具栏的设置
     */
    private void handleJump() {
        if (mWebView.canGoBack()) {
            mWebViewToolBar.setBackBarSrc(SqResUtils.getIdByName("sy37_web_back_enable", "drawable", mContext));
        } else {
            mWebViewToolBar.setBackBarSrc(SqResUtils.getIdByName("sy37_web_back_disable", "drawable", mContext));
        }

        if (mWebView.canGoForward()) {
            mWebViewToolBar.setForwardBarSrc(SqResUtils.getIdByName("sy37_web_forward_enable", "drawable", mContext));
        } else {
            mWebViewToolBar.setForwardBarSrc(SqResUtils.getIdByName("sy37_web_forward_disable", "drawable", mContext));
        }
    }

    /**
     * 判断是否有刘海缺口
     */
    private boolean isNotchAffected() {
        if (mContext == null) {
            return false;
        }
        boolean isLand = mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
        if (!(mContext instanceof Activity)) {
            return isLand;
        }
        return NotchTools.getFullScreenTools().isNotchScreen(((Activity) mContext).getWindow()) && isLand;
    }
}