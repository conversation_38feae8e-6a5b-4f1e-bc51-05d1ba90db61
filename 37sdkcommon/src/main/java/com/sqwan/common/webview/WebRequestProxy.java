package com.sqwan.common.webview;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.webview.net.IRequest;
import com.sqnetwork.voly.VolleyError;
import java.util.Map;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/5/21
 * @desc: webview库中的请求代理
 */
public class WebRequestProxy implements IRequest {

    @Override
    public void getRequest(String url, Map<String, String> params, RequestCallback<JSONObject> callback) {
        SqRequest.of(url).params(params).get(new SqHttpCallback<JSONObject>() {

            @Override
            protected String getMsgKey() {
                return "message";
            }

            @Override
            protected String getStateKey() {
                return "code";
            }

            @Override
            protected int getOkState() {
                return 0;
            }


            @Override
            public void onResponseStateError(int httpStatus, int state,
                @NonNull String msg, @Nullable String data) {
                callback.onError(httpStatus, data);
            }

            @Override
            public void onSuccess(JSONObject t) {
                callback.onSuccess(t);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                callback.onError(code, error.getMessage());
            }
        });
    }
}
