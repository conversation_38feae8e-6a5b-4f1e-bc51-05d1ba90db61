package com.sqwan.common.data;

public class SpannableEntity {
    //跳转链接
    private String link;
    //十六进制的颜色值
    private String colorValue="#3087DE";
    //名称
    private String tag;
    //超链接点击事件
    private OnClickListener onClickListener;


    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getColorValue() {
        return colorValue;
    }

    public void setColorValue(String colorValue) {
        this.colorValue = colorValue;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public OnClickListener getOnClickListener() {
        return onClickListener;
    }

    public void setOnClickListener(OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
    }

    public interface OnClickListener{
        void onClick();
    }
}
