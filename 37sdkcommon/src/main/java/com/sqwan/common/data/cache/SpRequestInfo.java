package com.sqwan.common.data.cache;

import android.content.Context;
import android.content.SharedPreferences;

import com.sqwan.common.net.base.RequestUtil;
import com.sqwan.common.util.RandomUtils;

public class SpRequestInfo {
    private static final String REQUEST_LIVE_ID = "request_live_id";
    private static final String REQUEST_PREF = "request_prefs";


    /**
     * 存储接口请求的生命周期id
     *
     */
    public static void setRequestLiveId(Context context) {
        SharedPreferences sp = context.getSharedPreferences(REQUEST_PREF, Context.MODE_PRIVATE);
        SharedPreferences.Editor edit = sp.edit();
        edit.putString(REQUEST_LIVE_ID, RequestUtil.generateRequestLiveId(System.currentTimeMillis(), RandomUtils.randomData(16)));
        edit.apply();
    }

    /**
     * 获取接口请求的生命周期id
     */
    public static String getRequestLiveId(Context context) {
        SharedPreferences sp = context.getSharedPreferences(REQUEST_PREF, Context.MODE_PRIVATE);
        return sp.getString(REQUEST_LIVE_ID, "");
    }
}
