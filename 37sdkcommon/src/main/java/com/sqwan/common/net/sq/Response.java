package com.sqwan.common.net.sq;

import java.util.Map;

public class Response {

    public static final int STATE_OK = 1;

    public static final int STATE_FAIL = 0;

    private int state = STATE_FAIL;

    private String message;

    private String data;

    private String reqId;

    private int statusCode;

    private String jsonStr;

    private Map<String, String> headers;

    public void setState(int state) {
        this.state = state;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public int getState() {
        return state;
    }

    public String getJsonStr() {
        return jsonStr;
    }

    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getData() {
        return data;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int code) {
        this.statusCode = code;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> map) {
        this.headers = map;
    }

    @Override
    public String toString() {
        return "Response{" +
                "state=" + state +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                ", reqId='" + reqId + '\'' +
                ", statusCode=" + statusCode +
                ", headers=" + headers +
                '}';
    }
}
