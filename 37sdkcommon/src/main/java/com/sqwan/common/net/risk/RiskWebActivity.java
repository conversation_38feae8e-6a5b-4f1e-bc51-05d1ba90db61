package com.sqwan.common.net.risk;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;

import com.plugin.standard.RealBaseActivity;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.net.bean.WebDialogBean;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.webview.SQWebViewDialog;

import org.json.JSONObject;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 *    author : 黄锦群
 *    time   : 2023/07/19
 *    desc   : 人脸风控 Web 界面
 */
public class RiskWebActivity extends RealBaseActivity {

   public static final String INTENT_KEY_IN_URL = "url";
   public static final String INTENT_KEY_IN_CALLBACK_KEY = "callback_key";
   public static final String INTENT_KEY_IN_SCREEN_ORIENTATION = "screenOrientation";

   public static void startActivity(Activity activity, JSONObject jsonObject, OnRetryCallback callback) throws Exception {
      JSONObject dataJson = jsonObject.optJSONObject("data");
      JSONObject webViewJson = dataJson.getJSONObject("webview");
      String url = webViewJson.getString("pop_up_url");

      if (TextUtils.isEmpty(url)) {
         LogUtil.i("统一弹窗的 url 为空");
         if (callback != null) {
            callback.onNotRetry();
         }
         return;
      }

      LogUtil.i("统一弹窗的 url：" + url);

      WebDialogBean webDialogBean = WebDialogBean.parseWebDialogBean(url);

      if (TextUtils.isEmpty(url)) {
         return;
      }
      Intent intent = new Intent();
      intent.putExtra(RiskWebActivity.INTENT_KEY_IN_URL, url);
      boolean supportPlugin = isSupportPlugin();

      switch (webDialogBean.getScreenOrientation()) {
         case ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE:
            // 这里为了兼容插件和非插件两种版本，所以用了两种写法
            intent.setClass(activity, supportPlugin ? RiskWebActivity.class : RiskWebLandscapeActivity.class);
            intent.putExtra(INTENT_KEY_IN_SCREEN_ORIENTATION, "landscape");
            break;
         case ActivityInfo.SCREEN_ORIENTATION_PORTRAIT:
            intent.setClass(activity, supportPlugin ? RiskWebActivity.class : RiskWebPortraitActivity.class);
            intent.putExtra(INTENT_KEY_IN_SCREEN_ORIENTATION, "portrait");
            break;
         default:
         case ActivityInfo.SCREEN_ORIENTATION_BEHIND:
            intent.setClass(activity, supportPlugin ? RiskWebActivity.class : RiskWebBehindActivity.class);
            intent.putExtra(INTENT_KEY_IN_SCREEN_ORIENTATION, "behind");
            break;
      }
      try {
         long callbackKey = SystemClock.uptimeMillis();
         addRetryCallback(String.valueOf(callbackKey), callback);
         intent.putExtra(INTENT_KEY_IN_CALLBACK_KEY, String.valueOf(callbackKey));
         activity.startActivity(intent);
         LogUtil.i("通用弹窗界面跳转成功");
      } catch (Exception e) {
         LogUtil.e( "通用弹窗界面跳转失败", e);
         if (callback != null) {
            callback.onNotRetry();
         }
      }
   }

   private static void showRiskWebDialog(Context context, WebDialogBean webDialogBean,
                                        String url, OnRetryCallback callback) throws Exception {
      SQWebViewDialog webViewDialog = new SQWebViewDialog(context);
      webViewDialog.setUrl(AppUtils.constructWebUrlParam(context, url));
      webViewDialog.setCancelable(!webDialogBean.isFocus());
      webViewDialog.setOnDismissListener(dialog -> {
         LogUtil.i("通用弹窗关闭，isUnionCloseCalled = " + webViewDialog.isUnionCloseCalled() +
                 ", isNeedUnionCloseRetry = " + webViewDialog.isNeedUnionCloseRetry());
         if (callback == null) {
            return;
         }
         if (webViewDialog.isUnionCloseCalled()) {
            //如果js有调用过通用弹窗的关闭，则根据js接口判断是否需要重试
            if (webViewDialog.isNeedUnionCloseRetry()) {
               callback.onRetry();
            } else {
               callback.onNotRetry();
            }
            return;
         }
         // 如果js没调用过通用弹窗的关闭，则根据webDialogBean判断是否需要重试，如果是则重新发起请求
         if (webDialogBean.isRetry()) {
            callback.onRetry();
         } else {
            callback.onNotRetry();
         }
      });
      webViewDialog.show();
      LogUtil.i("通用弹窗显示了：" + url);

      HashMap<String, String> webDialogMap = new HashMap<>();
      webDialogMap.put("web_url", url);
      webDialogMap.put("web_path", webDialogBean.getWebPath());
      SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.popup_dialog_show, webDialogMap);
   }

   private static boolean isSupportPlugin() {
      try {
         Class<?> sqwanCoreClass = Class.forName("com.sqwan.msdk.SQwanCore");
         Method getInstanceMethod = sqwanCoreClass.getMethod("getInstance");
         Object sqwanCoreInstance = getInstanceMethod.invoke(null);
         Method isSupportPluginMethod = sqwanCoreClass.getMethod("isSupportPlugin");
         boolean isSupported = (boolean) isSupportPluginMethod.invoke(sqwanCoreInstance);
         LogUtil.i("isSupportPlugin() 返回值：" + isSupported);
         return isSupported;
      } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
         LogUtil.e("反射 isSupportPlugin 方法失败", e);
      }
      return false;
   }

   private static final Map<String, OnRetryCallback> RETRY_CALLBACK_MAP = new HashMap<>();

   private static synchronized void addRetryCallback(String key, OnRetryCallback onRetryCallback) {
      RETRY_CALLBACK_MAP.put(key, onRetryCallback);
   }

   private static synchronized OnRetryCallback getRetryCallback(String key) {
      return RETRY_CALLBACK_MAP.get(key);
   }

   private static synchronized void removeRetryCallback(String key) {
      RETRY_CALLBACK_MAP.remove(key);
   }

   private String mCallbackKey;

   @Override
   public void onCreate(Bundle bundle) {
      super.onCreate(bundle);

      mCallbackKey = getIntent().getStringExtra(INTENT_KEY_IN_CALLBACK_KEY);

      String url = getIntent().getStringExtra(INTENT_KEY_IN_URL);
      LogUtil.i("通用弹窗关闭 url 地址：" + url);
      if (TextUtils.isEmpty(url)) {
         dispatchTryResult(false);
         LogUtil.i("通用弹窗关闭 url 地址为空，直接关闭弹窗");
         return;
      }

      try {
          WebDialogBean webDialogBean = WebDialogBean.parseWebDialogBean(url);
          showRiskWebDialog(getContext(), webDialogBean, url, new OnRetryCallback() {

              @Override
              public void onRetry() {
                  dispatchTryResult(true);
              }

              @Override
              public void onNotRetry() {
                  dispatchTryResult(false);
              }
          });
      } catch (Exception e) {
          LogUtil.e("弹出统一弹窗失败", e);
          dispatchTryResult(false);
      }
   }

   @Override
   public void onActivityResult(int requestCode, int resultCode, Intent data) {
      super.onActivityResult(requestCode, resultCode, data);
      OnActivityResultEvent onActivityResultEvent = new OnActivityResultEvent(requestCode, resultCode, data);
      EventDispatcher.getInstance().dispatcherActivityResultListener(onActivityResultEvent);
   }

   @Override
   public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
      super.onRequestPermissionsResult(requestCode, permissions, grantResults);
      PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
   }

   private void dispatchTryResult(boolean retry) {
      OnRetryCallback callback = getRetryCallback(mCallbackKey);
      if (callback == null) {
         finish();
         return;
      }

      if (retry) {
         callback.onRetry();
      } else {
         callback.onNotRetry();
      }
      removeRetryCallback(mCallbackKey);
      finish();
   }
}