package com.sqwan.common.net.base;

import android.os.Process;

import com.sqwan.common.util.RandomUtils;

public class RequestUtil {

    /**
     * 生成规则：OS-进程ID-毫秒时间戳-16位随机数字字母（0-9a-zA-Z）；获取不到则用0表示
     *
     * @return
     */
    public static String generateRequestId() {
        StringBuilder reqBuilder = new StringBuilder();
        reqBuilder.append("android").append("-");
        reqBuilder.append(Process.myPid()).append("-");
        reqBuilder.append(System.currentTimeMillis()).append("-");
        reqBuilder.append(RandomUtils.randomData(16));
        return reqBuilder.toString();
    }


    /**
     * 生成规则：OS-进程ID-毫秒时间戳-16位随机数字字母（0-9a-zA-Z）；获取不到则用0表示
     *
     * @return
     */
    public static String generateRequestLiveId(long mills, String requestLiveId) {
        return "android" + "-" +
                Process.myPid() + "-" +
                mills + "-" +
                requestLiveId;
    }

}
