package com.sqwan.common.net.sq;

import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.msdk.config.MultiSdkManager;

/**
 *    author : 黄锦群
 *    time   : 2022/06/21
 *    desc   : Url 契约类
 */
public class CommonUrlConstant {

    public static final String KEY_GET_SKIP_APPLET_INFO_URL = "get_skip_applet_info";

    /**
     * 跳转配置获取接口
     *
     * 接口文档：http://yapi.39on.com/project/75/interface/api/19832
     */
    @UrlUpdate(value = KEY_GET_SKIP_APPLET_INFO_URL, xValue = "x_get_skip_applet_info")
    public static String SKIP_APPLET_INFO_URL = "https://sdk-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/api/mapi-service/v1/sdk/skip-applet-info";
}