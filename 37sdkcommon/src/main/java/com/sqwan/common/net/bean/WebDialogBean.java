package com.sqwan.common.net.bean;

import android.content.pm.ActivityInfo;
import android.text.TextUtils;

import com.sqwan.common.util.UrlUtils;

import java.net.URL;

public class WebDialogBean {
    private String webUrl;
    private boolean focus;
    private boolean retry;

    private int screenOrientation;

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public boolean isFocus() {
        return focus;
    }

    public void setFocus(boolean focus) {
        this.focus = focus;
    }

    public boolean isRetry() {
        return retry;
    }

    public void setRetry(boolean retry) {
        this.retry = retry;
    }

    public int getScreenOrientation() {
        return screenOrientation;
    }

    public void setScreenOrientation(int screenOrientation) {
        this.screenOrientation = screenOrientation;
    }

    public String getWebPath() {
        if (TextUtils.isEmpty(webUrl)) {
            return "";
        }
        try {
            URL url = new URL(webUrl);
            String protocol = url.getProtocol();
            String domain = url.getAuthority();
            String uri = url.getPath();
            return protocol + "://" + domain + uri;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static WebDialogBean parseWebDialogBean(String webUrl) {
        WebDialogBean webDialogBean = new WebDialogBean();
        webDialogBean.setWebUrl(webUrl);
        String focus = UrlUtils.readValueFromUrlStrByParamName(webUrl, "enforce");
        String retry = UrlUtils.readValueFromUrlStrByParamName(webUrl, "retry");
        String portrait = UrlUtils.readValueFromUrlStrByParamName(webUrl, "portrait");
        if (TextUtils.equals("1", portrait)) {
            webDialogBean.setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        } else if (TextUtils.equals("0", portrait)) {
            webDialogBean.setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        } else {
            webDialogBean.setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_BEHIND);
        }
        webDialogBean.setRetry("1".equals(retry));
        webDialogBean.setFocus("1".equals(focus));
        return webDialogBean;
    }
}
