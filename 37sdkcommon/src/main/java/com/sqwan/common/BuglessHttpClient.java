package com.sqwan.common;

import com.sdk.sq.net.HttpDns;
import com.sdk.sq.net.ReportStrategy;
import com.sq.tool.network.SqHttpClient;
import com.sqnetwork.voly.Request;
import com.sqnetwork.voly.Response.Listener;
import com.sqnetwork.voly.toolbox.StringRequest;
import com.sqwan.bugless.net.IHttpCallback;
import com.sqwan.bugless.net.IHttpClient;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/29
 */
public class BuglessHttpClient implements IHttpClient {

    @Override
    public void postString(String url, final String body, final Map<String, String> headers,
        final IHttpCallback callback) {
        if (body == null) {
            return;
        }
        final StringRequest request = new StringRequest(Request.Method.POST, url,
            (Listener<String>) (raw, response) -> callback.onSuccess(response),
            error -> callback.onFail(-1, error.getMessage())) {

            @Override
            public Map<String, String> getHeaders() {
                return headers == null ? Collections.emptyMap() : headers;
            }

            @Override
            public byte[] getBody() {
                return body.getBytes();
            }

            @Override
            public String getBodyContentType() {
                return "application/json; charset=" + this.getParamsEncoding();
            }
        };

        // 如果bugless接口失败, 仅上报数数, 不要再触发bugless上报, 避免陷入循环
        request.setTag(ReportStrategy.class, ReportStrategy.ONLY_EVENT);
        request.setLocalDNS(HttpDns.DEFAULT);
        SqHttpClient.getInstance().enqueue(request);
    }
}
