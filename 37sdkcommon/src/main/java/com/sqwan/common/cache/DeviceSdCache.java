package com.sqwan.common.cache;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Environment;
import android.support.v4.app.ActivityCompat;
import android.text.TextUtils;

import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ZipString;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/1/15
 */
public class DeviceSdCache {

    /**
     * 设备信息存储在sd的位置，命名该名称防止用户手动删除
     */
    private static final String DEVICE_DIR = "systemgameinfo";

    /**
     * 存储设备信息的文件
     */
    private static final String DEVICE_FILE = "devinfo2.txt";


    public static String getValue(Context context, String key) {
        LogUtil.i("get value key --> " + key);
        if(ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            String value = null;
            Map<String, String> deviceInfos = getInfoFormFile(context);
            if (deviceInfos != null && !deviceInfos.isEmpty()) {
                for(Map.Entry<String, String> entry : deviceInfos.entrySet()) {
                    if(entry.getKey().equals(key)) {
                        value = entry.getValue();
                    }
                }
            }
            LogUtil.i("return value --> " + value);
            return value;
        } else {
            LogUtil.i("return value --> " + null);
            return null;
        }
    }

    public static boolean saveValue(Context context, String key, String value) {
        LogUtil.i("save value key --> " + key + ", value --> " + value);
        if(ActivityCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
            Map<String, String> deviceInfos = getInfoFormFile(context);
            if(deviceInfos == null) {
                deviceInfos = new HashMap<>();
            }
            deviceInfos.put(key, value);
            return saveInfoToFile(context,deviceInfos);
        } else {
            return false;
        }
    }

    private static Map<String, String> getInfoFormFile(Context context) {
        LogUtil.i("get info from file");
        File file = getDeviceFile(context);
        if (file == null) {
            return null;
        }
        StringBuilder jsonBuilder = new StringBuilder();
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString;
            while ((tempString = reader.readLine()) != null) {
                jsonBuilder.append(tempString);
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e1) {

                }
            }
        }
        if (TextUtils.isEmpty(jsonBuilder.toString())) {
            return null;
        } else {
            Map<String, String> infos = new HashMap<>();
            try {
                String decodeJson = ZipString.zipString2Json(jsonBuilder.toString());
                LogUtil.i("json str --> " + decodeJson);
                JSONObject jsonObject = new JSONObject(decodeJson);
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    String value = jsonObject.optString(key);
                    infos.put(key, value);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return infos;
        }
    }

    private static boolean saveInfoToFile(Context context,Map<String, String> infos) {
        LogUtil.i("save info to file");
        File file = getDeviceFile(context);
        if(file == null) {
            return false;
        }
        try {
            JSONObject jsonObject = new JSONObject(infos);
            LogUtil.i("json str --> " + jsonObject.toString());
            FileWriter writer = new FileWriter(file.getAbsolutePath(),false);
            writer.write(ZipString.json2ZipString(jsonObject.toString()));
            writer.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    private static File getDeviceFile(Context context){
        try {
            File file = new File(getSDPath(context) + File.separator + DEVICE_FILE);
            if(!file.exists()){
                file.createNewFile();
            }
            return file;
        } catch(Exception e) {
            LogUtil.i("无SDCard，获取AF失败");
            e.printStackTrace();
        }
        return null;
    }


    private static String getSDPath(Context context) {
        return EnvironmentUtils.getCommonSubDirPath(context,DEVICE_DIR);
    }

}
