package com.sqwan.common.cache;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.util.SpUtils;

/**
 * <AUTHOR>
 * @date 2020/1/15
 */
public class DeviceCacheHelper {


    private static final String SQ_PREFS = "sq_prefs";

    public static void save(Context context, String key, String value) {
        save(context, key, value, true);
    }

    public static void save(Context context, String key, String value, boolean isSdCard) {
        saveToSp(context, key, value);
        if(isSdCard) {
            saveToSd(context, key, value);
        }
    }


    public static String get(Context context, String key) {
        return getFromSp(context, key, true);
    }

    public static String get(Context context, String key, boolean isSdCard) {
        return getFromSp(context, key, isSdCard);
    }

    private static void saveToSp(Context context, String key, String value) {
        SpUtils spUtils = new SpUtils(context, SQ_PREFS);
        spUtils.put(key, value);
    }

    private static String getFromSp(Context context, String key, boolean isSdCard) {
        String value;
        SpUtils spUtils = new SpUtils(context, SQ_PREFS);
        value = spUtils.getString(key);
        if(value == null && isSdCard) {
            value = getFromSd(context, key);
            if(!TextUtils.isEmpty(value)) {
                saveToSp(context, key, value);
            }
        }
        return value;
    }

    private static void saveToSd(Context context, String key, String value) {
        DeviceSdCache.saveValue(context, key, value);
    }

    private static String getFromSd(Context context, String key) {
        return DeviceSdCache.getValue(context, key);
    }


}
