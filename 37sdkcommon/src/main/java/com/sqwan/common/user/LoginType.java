package com.sqwan.common.user;

import android.support.annotation.NonNull;
import com.sq.tool.logger.SQLog;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

/**
 * 登录的账号类型
 *
 * <AUTHOR>
 * @since 2024/9/25
 */
public class LoginType {

    /**
     * 账密登录
     */
    public static final LoginType ACCOUNT = new LoginType(1, "account");
    /**
     * 手机号登录
     */
    public static final LoginType PHONE = new LoginType(2, "phone");
    /**
     * 微信登录
     */
    public static final LoginType WECHAT = new LoginType(3, "wechat");
    public static final LoginType UNKNOWN = new LoginType(-1, "unknown");

    public final int code;
    @NonNull
    public final String name;


    public LoginType(int code, @NonNull String name) {
        this.code = code;
        this.name = name;
    }

    private static final List<LoginType> VALUES = new ArrayList<LoginType>() {
        {
            add(ACCOUNT);
            add(PHONE);
            add(WECHAT);
            add(UNKNOWN);
        }
    };

    public static void register(LoginType type) {
        if (VALUES.contains(type)) {
            SQLog.wt("sqsdk", "重复注册登录类型" + type);
            return;
        }
        SQLog.dt("sqsdk", "注册登录类型" + type);
        VALUES.add(type);
    }

    public static Collection<LoginType> values() {
        return VALUES;
    }

    @NonNull
    @Override
    public String toString() {
        return name;
    }

    @NonNull
    public static LoginType get(String name) {
        if (name != null) {
            for (LoginType v : LoginType.values()) {
                if (v.name.equalsIgnoreCase(name)) {
                    return v;
                }
            }
        }

        return UNKNOWN;
    }

    @NonNull
    public static LoginType get(int type) {
        if (type > 0) {
            for (LoginType v : LoginType.values()) {
                if (v.code == type) {
                    return v;
                }
            }
        }

        return UNKNOWN;
    }

    /**
     * {@link #name}首字母大写
     */
    public String capitalize() {
        final String str = name.toLowerCase(Locale.US);
        int strLen;
        if ((strLen = str.length()) == 0) {
            return str;
        }

        final char firstChar = str.charAt(0);
        final char newChar = Character.toTitleCase(firstChar);
        if (firstChar == newChar) {
            // already capitalized
            return str;
        }

        char[] newChars = new char[strLen];
        newChars[0] = newChar;
        str.getChars(1, strLen, newChars, 1);
        return String.valueOf(newChars);
    }

    /**
     * {@link #name}全小写
     */
    public String lowerCase() {
        return name.toLowerCase(Locale.US);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        LoginType loginType = (LoginType) o;

        if (code != loginType.code) {
            return false;
        }
        return name.equals(loginType.name);
    }

    @Override
    public int hashCode() {
        int result = code;
        result = 31 * result + name.hashCode();
        return result;
    }
}
