package com.sqwan.common.user;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import java.util.Map;

public class RoleInfo {

    /**
     * 区服Id
     */
    private String mServerId = "";

    /**
     * 服务器名称
     */
    private String mServerName = "";

    /**
     * 角色Id
     */
    private String mRoleId = "";

    /**
     * 角色名
     */
    private String mRoleName = "";

    /**
     * 角色等级
     */
    private String mRoleLevel = "";

    /**
     * 角色创建时间
     */
    private String mRoleCreateTime;

    /**
     * 工会名称
     */
    private String mPartyName = "";

    /**
     * 角色余额
     */
    private String mRoleBalance = "";

    /**
     * 玩家Vip等级
     */
    private String mVipLevel;

    /**
     * 角色升级时间
     */
    private String mLevelUpTime;

    public RoleInfo setServerId(String serverId) {
        mServerId = serverId;
        return this;
    }

    public String getServerId() {
        return mServerId;
    }

    public RoleInfo setServerName(String serverName) {
        mServerName = serverName;
        return this;
    }

    public String getServerName() {
        return mServerName;
    }

    public RoleInfo setVipLevel(String vipLevel) {
        mVipLevel = vipLevel;
        return this;
    }

    public String getVipLevel() {
        return mVipLevel;
    }

    public RoleInfo setRoleId(String roleId) {
        mRoleId = roleId;
        return this;
    }

    public String getRoleId() {
        return mRoleId;
    }

    public RoleInfo setRoleCreateTime(String createTime) {
        mRoleCreateTime = createTime;
        return this;
    }

    public String getRoleCreateTime() {
        return mRoleCreateTime;
    }

    public RoleInfo setRoleLevelUpTime(String roleLevelUpTime) {
        mLevelUpTime = roleLevelUpTime;
        return this;
    }

    public String getRoleLevelUpTime() {
        return mLevelUpTime;
    }

    public RoleInfo setRoleName(String roleName) {
        mRoleName = roleName;
        return this;
    }

    public String getRoleName() {
        return mRoleName;
    }

    public RoleInfo setRoleLevel(String roleLevel) {
        mRoleLevel = roleLevel;
        return this;
    }

    public String getRoleLevel() {
        return mRoleLevel;
    }

    public RoleInfo setPartyName(String partyName) {
        mPartyName = partyName;
        return this;
    }

    public String getPartyName() {
        return mPartyName;
    }

    public RoleInfo setRoleBalance(String roleBalance) {
        mRoleBalance = roleBalance;
        return this;
    }

    public String getRoleBalance() {
        return mRoleBalance;
    }

    public boolean isInfoValid() {
        return ((!TextUtils.isEmpty(mServerId)) && (!TextUtils.isEmpty(mRoleId)));
    }

    @NonNull
    @Override
    public String toString() {
        return "RoleInfo: " +
            "服务器=" + mServerName + '(' + mServerId + ')' +
            ", 角色=" + mRoleName + '(' + mRoleId + ')' +
            ", createTime=" + mRoleCreateTime +
            ", level=" + mRoleLevel +
            ", levelUpTime=" + mLevelUpTime +
            ", 工会=" + mPartyName +
            ", 余额=" + mRoleBalance +
            ", vipLevel=" + mVipLevel;
    }

    public String toMapString() {
        return "{"
            + "serverName=" + mServerName + ", "
            + "serverId=" + mServerId + ", "
            + "roleName=" + mRoleName + ", "
            + "roleId=" + mRoleId + ", "
            + "createTime=" + mRoleCreateTime + ", "
            + "level=" + mRoleLevel + ", "
            + "levelUpTime=" + mLevelUpTime + ", "
            + "vipLevel=" + mVipLevel + ", "
            + "roleBalance=" + mRoleBalance + ", "
            + "partyName=" + mPartyName
            + "}";
    }

    /**
     * 角色信息通过map传递, 转成role info实例
     * https://developers.37.com.cn/developer/domestic/official/androidsdk/base/enter/#%E6%8E%A5%E5%8F%A3
     */
    public static RoleInfo fromRoleMap(Map<String, String> roleInfo) {
        if (roleInfo == null) {
            return null;
        }
        RoleInfo role = new RoleInfo();
        role.setRoleId(roleInfo.get("roleId"));
        role.setRoleName(roleInfo.get("roleName"));
        role.setRoleLevel(roleInfo.get("roleLevel"));
        role.setRoleCreateTime(roleInfo.get("roleCTime"));
        role.setRoleLevelUpTime(roleInfo.get("roleLevelMTime"));
        role.setRoleBalance(roleInfo.get("balance"));
        role.setVipLevel(roleInfo.get("vipLevel"));
        role.setServerId(roleInfo.get("serverId"));
        role.setServerName(roleInfo.get("serverName"));
        role.setPartyName(roleInfo.get("partyName"));
        return role;
    }
}
