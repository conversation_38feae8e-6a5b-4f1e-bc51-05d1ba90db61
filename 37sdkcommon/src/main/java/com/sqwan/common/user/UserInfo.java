package com.sqwan.common.user;

import android.support.annotation.CallSuper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sqwan.common.util.ZipString;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @since 2024/9/25
 */
public abstract class UserInfo {

    private static final String KEY_UID = "uid";
    private static final String KEY_TOKEN = "token";
    private static final String KEY_REFRESH_TOKEN = "refresh_token";
    private static final String KEY_TYPE = "type";
    private static final String KEY_UNAME = "uname";
    private static final String KEY_PHONE = "phone";
    private static final String KEY_PWD = "pwd";

    @NonNull
    public final String uid;
    @NonNull
    public final String token;
    @NonNull
    public final LoginType type;
    @Nullable
    public String uname;
    @Nullable
    public String refreshToken;

    @NonNull
    JSONObject toJson() {
        JSONObject obj = new JSONObject();
        try {
            obj.put(KEY_UID, uid);
            obj.put(KEY_TOKEN, token);
            obj.put(KEY_TYPE, type.code);
            obj.put(KEY_REFRESH_TOKEN, refreshToken);
            obj.put(KEY_UNAME, uname);
        } catch (Exception e) {
            /* no-op */
        }
        return obj;
    }

    @CallSuper
    String toJsonString() {
        return toJson().toString();
    }

    @NonNull
    @Override
    public String toString() {
        return "User(" + type.name + "): uid=" + uid + ", uname=" + uname;
    }

    @Nullable
    static UserInfo fromJson(String jsonStr) {
        UserInfo userInfo = null;
        if (jsonStr != null && !jsonStr.isEmpty()) {
            try {
                JSONObject obj = new JSONObject(jsonStr);
                LoginType type = LoginType.get(obj.getInt(KEY_TYPE));
                if (LoginType.ACCOUNT.equals(type)) {
                    userInfo = AccountUserInfo.fromJson_(jsonStr);
                } else if (LoginType.PHONE.equals(type)) {
                    userInfo = PhoneUserInfo.fromJson_(jsonStr);
                } else if (LoginType.WECHAT.equals(type)) {
                    userInfo = WechatUserInfo.fromJson_(jsonStr);
                }
            } catch (Exception e) {
                /* no-op */
            }
        }
        return userInfo;
    }

    public UserInfo(@NonNull LoginType type, @NonNull String uid, @NonNull String token) {
        this.type = type;
        this.uid = uid;
        this.token = token;
    }

    @NonNull
    public String getUid() {
        return uid;
    }

    @NonNull
    public String getToken() {
        return token;
    }

    @Nullable
    public String getUname() {
        return uname;
    }

    /**
     * 账密用户
     */
    public static class AccountUserInfo extends UserInfo {

        /**
         * 明文密码
         */
        @NonNull
        public final String pwd;

        public AccountUserInfo(@NonNull String uid, @NonNull String token, @NonNull String uname, @NonNull String pwd) {
            super(LoginType.ACCOUNT, uid, token);
            this.uname = uname;
            this.pwd = pwd;
        }

        @NonNull
        @Override
        JSONObject toJson() {
            JSONObject obj = super.toJson();
            try {
                obj.put(KEY_PWD, ZipString.json2ZipString(pwd));
            } catch (Exception e) {
                /* no-op */
            }
            return obj;
        }

        @Nullable
        private static UserInfo fromJson_(String jsonStr) {
            AccountUserInfo userInfo = null;
            if (jsonStr != null && !jsonStr.isEmpty()) {
                try {
                    JSONObject obj = new JSONObject(jsonStr);
                    String uid = obj.getString(KEY_UID);
                    String token = obj.getString(KEY_TOKEN);
                    String uname = obj.getString(KEY_UNAME);
                    String pwd = ZipString.zipString2Json(obj.getString(KEY_PWD));
                    userInfo = new AccountUserInfo(uid, token, uname, pwd);
                    userInfo.refreshToken = obj.optString(KEY_REFRESH_TOKEN);
                } catch (Exception e) {
                    /* no-op */
                }
            }
            return userInfo;
        }
    }

    /**
     * 手机用户
     * 1: 手机+验证码
     * 2: 手机+密码
     */
    public static class PhoneUserInfo extends UserInfo {

        @NonNull
        public final String phone;
        /**
         * 明文密码
         */
        @Nullable
        public String pwd;

        public PhoneUserInfo(@NonNull String uid, @NonNull String token,
            @NonNull String phone) {
            super(LoginType.PHONE, uid, token);
            this.phone = phone;
        }

        @NonNull
        @Override
        JSONObject toJson() {
            JSONObject obj = super.toJson();
            try {
                obj.put(KEY_PHONE, phone);
                obj.put(KEY_PWD, ZipString.json2ZipString(pwd));
            } catch (Exception e) {
                /* no-op */
            }
            return obj;
        }

        @Nullable
        private static UserInfo fromJson_(String jsonStr) {
            PhoneUserInfo userInfo = null;
            if (jsonStr != null && !jsonStr.isEmpty()) {
                try {
                    JSONObject obj = new JSONObject(jsonStr);
                    String uid = obj.getString(KEY_UID);
                    String token = obj.getString(KEY_TOKEN);
                    String phone = obj.getString(KEY_PHONE);
                    userInfo = new PhoneUserInfo(uid, token, phone);
                    userInfo.pwd = ZipString.zipString2Json(obj.optString(KEY_PWD));
                    userInfo.refreshToken = obj.optString(KEY_REFRESH_TOKEN);
                    userInfo.uname = obj.optString(KEY_UNAME);
                } catch (Exception e) {
                    /* no-op */
                }
            }
            return userInfo;
        }

        @NonNull
        @Override
        public String toString() {
            return "User(" + type.name + "): uid=" + uid + ", phone=" + phone + ", uname=" + uname;
        }
    }

    /**
     * 微信登录用户
     */
    public static class WechatUserInfo extends UserInfo {

        /**
         * 明文密码
         */
        @Nullable
        public String pwd;

        public WechatUserInfo(@NonNull String uid, @NonNull String token) {
            super(LoginType.WECHAT, uid, token);
        }

        @NonNull
        @Override
        JSONObject toJson() {
            JSONObject obj = super.toJson();
            try {
                obj.put(KEY_PWD, ZipString.json2ZipString(pwd));
            } catch (Exception e) {
                /* no-op */
            }
            return obj;
        }

        @Nullable
        private static UserInfo fromJson_(String jsonStr) {
            WechatUserInfo userInfo = null;
            if (jsonStr != null && !jsonStr.isEmpty()) {
                try {
                    JSONObject obj = new JSONObject(jsonStr);
                    String uid = obj.getString(KEY_UID);
                    String token = obj.getString(KEY_TOKEN);
                    userInfo = new WechatUserInfo(uid, token);
                    userInfo.uname = obj.optString(KEY_UNAME);
                    userInfo.pwd = ZipString.zipString2Json(obj.optString(KEY_PWD));
                    userInfo.refreshToken = obj.optString(KEY_REFRESH_TOKEN);
                } catch (Exception e) {
                    /* no-op */
                }
            }
            return userInfo;
        }
    }

    @NonNull
    public static String getPwd(UserInfo user) {
        if (user == null) {
            return "";
        }
        String pwd;
        if (user instanceof AccountUserInfo) {
            pwd = ((AccountUserInfo) user).pwd;
        } else if (user instanceof PhoneUserInfo) {
            pwd = ((PhoneUserInfo) user).pwd;
        } else if (user instanceof WechatUserInfo) {
            pwd = ((WechatUserInfo) user).pwd;
        } else {
            pwd = "";
        }
        return pwd == null ? "" : pwd;
    }
}
