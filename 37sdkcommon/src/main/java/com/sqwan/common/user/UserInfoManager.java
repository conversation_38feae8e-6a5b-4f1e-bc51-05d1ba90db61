package com.sqwan.common.user;

import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.sdk.tool.util.SpUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.user.UserInfo.AccountUserInfo;
import com.sqwan.common.user.UserInfo.PhoneUserInfo;
import com.sqwan.common.user.UserInfo.WechatUserInfo;
import com.sqwan.common.util.ZipString;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.json.JSONObject;

/**
 * 管理当前上下文的用户
 *
 * <AUTHOR>
 * @since 2022/5/13
 */
public class UserInfoManager {

    private static final String TAG = "【User】";
    private static final String SQ_PREFS = "sq_prefs";

    /**
     * 记录当前登录的用户, 让多进程可以同步登录信息
     */
    private static final String SP_KEY_CURRENT_USER = "current_user";
    /**
     * 记录最近一次登录的用户, 用来自动登录
     */
    private static final String SP_KEY_LAST_USER = "last_user";

    private volatile static UserInfoManager sInstance;

    public static UserInfoManager getInstance() {
        if (sInstance == null) {
            synchronized (UserInfoManager.class) {
                if (sInstance == null) {
                    sInstance = new UserInfoManager();
                }
            }
        }

        return sInstance;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper());
    /**
     * true表示可以使用内存中的缓存, 不需要每次都从sp文件中读取
     */
    private boolean mRamEnable;
    private UserInfo mCurrentUser;

    private final List<UserListener> mUserListeners = new ArrayList<>();
    private final List<RoleListener> mRoleListeners = new ArrayList<>();

    private UserInfoManager() {
    }

    /**
     * 是否已经登录
     */
    public boolean isLogin() {
        return getCurrentUser() != null;
    }

    /**
     * 上一次登录的用户
     */
    @Nullable
    public UserInfo getLastLoginUser() {
        UserInfo userInfo = getUserFromLastSp();
        if (userInfo == null) {
            userInfo = getUserFromOld();
            SQLog.w(TAG + "旧记录: " + userInfo);
            if (userInfo != null) {
                // 缓存到新sp文件
                saveUserToLastSp(userInfo);
            }
        }
        return userInfo;
    }

    /**
     * 允许使用内存中的{@link #mCurrentUser}, 不用每次都从SP文件中获取
     * 给游戏主进程调用
     */
    public void setRamEnable(boolean enable) {
        mRamEnable = enable;
    }

    /**
     * 当前登录的用户
     */
    @Nullable
    public UserInfo getCurrentUser() {
        if (mCurrentUser == null || !mRamEnable) {
            // 为了处理多进程同步问题, 所以每次都读取sp文件确定没有登录
            mCurrentUser = getCurrentUserFromSp();
        }
        return mCurrentUser;
    }

    /**
     * 退出登录, 返回退出前登录的用户
     */
    @Nullable
    public UserInfo logout() {
        UserInfo current = getCurrentUser();
        SQLog.w(TAG + "退出登录: " + current);
        setCurrentRoleInfo(null);
        clearCurrentUser();
        if (current != null) {
            // 退出登录, 回调
            mHandler.post(() -> {
                List<UserListener> copy = new ArrayList<>(mUserListeners);
                for (UserListener listener : copy) {
                    if (listener != null) {
                        listener.onLogout(current);
                    }
                }
            });
        }
        return current;
    }

    /**
     * 设置已经登录的用户
     */
    public void setLoginUser(@NonNull UserInfo userInfo) {
        SQLog.i(TAG + "设置登录用户" + userInfo);
        UserInfo oldUser = mCurrentUser;

        mCurrentUser = userInfo;
        saveCurrentUserToSp(userInfo);
        saveUserToLastSp(userInfo);

        mHandler.post(() -> {
            List<UserListener> copy = new ArrayList<>(mUserListeners);
            for (UserListener listener : copy) {
                if (listener != null) {
                    listener.onLogin(oldUser, userInfo);
                }
            }
        });
    }

    /**
     * 更新用户信息, 还是同一个用户, 登录态没有变化
     */
    public void updateUserInfo(@NonNull UserInfo userInfo) {
        SQLog.i(TAG + "更新用户信息" + userInfo);
        UserInfo oldUser = mCurrentUser;
        if (oldUser == null) {
            SQLog.w(TAG + "当前无用户, 请调用setLoginUser");
            return;
        }
        if (oldUser.uid.equals(userInfo.uid) && oldUser.type.equals(userInfo.type)) {
            mCurrentUser = userInfo;
            saveCurrentUserToSp(userInfo);
            saveUserToLastSp(userInfo);

            mHandler.post(() -> {
                    List<UserListener> copy = new ArrayList<>(mUserListeners);
                    for (UserListener listener : copy) {
                        if (listener != null) {
                            listener.onUserInfoChanged(oldUser, userInfo);
                        }
                    }
                }
            );
        } else {
            SQLog.w(TAG + userInfo + "和" + oldUser + "的uid和类型不一致, 请调用setLoginUser");
        }
    }

    /**
     * 清空登录的用户, 应用启动后, 触发登录前调用
     */
    public void clearCurrentUser() {
        mCurrentUser = null;
        clearCurrentUserFromSp();
    }

    @Nullable
    private UserInfo getCurrentUserFromSp() {
        String jsonStr = SpUtils.getInstance().getString(SQ_PREFS, SP_KEY_CURRENT_USER);
        return UserInfo.fromJson(jsonStr);
    }

    private void saveCurrentUserToSp(@NonNull UserInfo userInfo) {
        SpUtils.getInstance().putString(SQ_PREFS, SP_KEY_CURRENT_USER, userInfo.toJsonString());
    }

    private void clearCurrentUserFromSp() {
        SpUtils.getInstance().putString(SQ_PREFS, SP_KEY_CURRENT_USER, "");
    }

    private void saveUserToLastSp(@NonNull UserInfo userInfo) {
        SpUtils.getInstance().putString(SQ_PREFS, SP_KEY_LAST_USER, userInfo.toJsonString());
    }

    @Nullable
    private UserInfo getUserFromLastSp() {
        String jsonStr = SpUtils.getInstance().getString(SQ_PREFS, SP_KEY_LAST_USER);
        return UserInfo.fromJson(jsonStr);
    }

    /**
     * 兼容旧版本的sp文件(3.7.9.1版本以前)
     *
     * @since 2024/9/25
     */
    @Nullable
    private UserInfo getUserFromOld() {
        // 3.7.7版本开始存储userinfo的json信息
        String userInfoJsonStr = SpUtils.getInstance().getString(SQ_PREFS, "user_info", "");
        if (TextUtils.isEmpty(userInfoJsonStr)) {
            // 兼容3.7.7以前没有存储userinfo的json信息的时候，则直接获取
            String uid = SpUtils.getInstance().getString(SQ_PREFS, "userid", "");
            String userName = SpUtils.getInstance().getString(SQ_PREFS, "username", "");
            String token = SpUtils.getInstance().getString(SQ_PREFS, "token", "");
            // 保存到sp的密码做了加密
            String zipStringPwd = SpUtils.getInstance().getString(SQ_PREFS, "pd", "");
            // 明文密码
            String pwd = ZipString.zipString2Json(zipStringPwd);
            if (!TextUtils.isEmpty(uid) && !TextUtils.isEmpty(token) && !TextUtils.isEmpty(userName)
                && !TextUtils.isEmpty(pwd)) {
                return new AccountUserInfo(uid, token, userName, pwd);
            }
        } else {
            UserInfo userInfo;
            try {
                JSONObject jsonObject = new JSONObject(userInfoJsonStr);
                String uname = jsonObject.optString("uname");
                // 保存到sp的密码做了加密
                String zipStringPwd = jsonObject.optString("upwd");
                // 明文密码
                String pwd = ZipString.zipString2Json(zipStringPwd);
                String token = jsonObject.optString("token");
                String refreshToken = jsonObject.optString("refresh_token");
                String uid = jsonObject.optString("uid");
                String loginTypeStr = jsonObject.optString("login_type");
                LoginType loginType = LoginType.get(Integer.parseInt(loginTypeStr));
                if (loginType == LoginType.ACCOUNT) {
                    userInfo = new AccountUserInfo(uid, token, uname, pwd);
                } else if (loginType == LoginType.PHONE) {
                    String mobile = jsonObject.optString("mobile");
                    PhoneUserInfo myUserInfo = new PhoneUserInfo(uid, token, mobile);
                    myUserInfo.pwd = pwd;
                    userInfo = myUserInfo;
                } else if (loginType == LoginType.WECHAT) {
                    WechatUserInfo myUserInfo = new WechatUserInfo(uid, token);
                    myUserInfo.uname = uname;
                    myUserInfo.pwd = pwd;
                    userInfo = myUserInfo;
                } else {
                    // 异常的类型, 忽略
                    BuglessAction.reportCatchException(new IllegalArgumentException(),
                        "异常的用户类型", userInfoJsonStr, BuglessAction.COMMON_ERROR);
                    return null;
                }

                userInfo.refreshToken = refreshToken;
                return userInfo;
            } catch (Exception e) {
                BuglessAction.reportCatchException(e,
                    "解析上次登录用户失败", userInfoJsonStr, BuglessAction.COMMON_ERROR);
                return null;
            }
        }
        return null;
    }

    private volatile RoleInfo mCurrentRoleInfo;

    @Nullable
    public RoleInfo getCurrentRoleInfo() {
        return mCurrentRoleInfo;
    }

    public void setCurrentRoleInfo(@Nullable RoleInfo roleInfo) {
        RoleInfo old = mCurrentRoleInfo;
        mCurrentRoleInfo = roleInfo;
        if (roleInfo != null) {
            // 兼容旧版本, 保存到sp
            SpUtils.getInstance().putString(SQ_PREFS, "dsid", roleInfo.getServerId());
            SpUtils.getInstance().putString(SQ_PREFS, "drid", roleInfo.getRoleId());
            SpUtils.getInstance().putString(SQ_PREFS, "drname", roleInfo.getRoleName());
            SpUtils.getInstance().putString(SQ_PREFS, "drlevel", roleInfo.getRoleLevel());
            SpUtils.getInstance().putString(SQ_PREFS, "viplevel", roleInfo.getVipLevel());
            SpUtils.getInstance().putString(SQ_PREFS, "serverName", roleInfo.getServerName());
        }
        if (roleInfo != null) {
            if (old != null) {
                if (!Objects.equals(old.getRoleId(), roleInfo.getRoleId())) {
                    // 切换不同的角色
                    SQLog.w(TAG + "切换角色 " + roleInfo);
                    mHandler.post(() -> {
                            List<RoleListener> copy = new ArrayList<>(mRoleListeners);
                            for (RoleListener listener : copy) {
                                if (listener != null) {
                                    listener.onRoleIn(old, roleInfo);
                                }
                            }
                        }
                    );
                } else {
                    // 更新角色信息
                    SQLog.d(TAG + "更新角色 " + roleInfo);
                    mHandler.post(() -> {
                            List<RoleListener> copy = new ArrayList<>(mRoleListeners);
                            for (RoleListener listener : copy) {
                                if (listener != null) {
                                    listener.onRoleInfoChanged(old, roleInfo);
                                }
                            }
                        }
                    );
                }
            } else {
                // 新登入角色
                SQLog.i(TAG + "设置角色 " + roleInfo);
                mHandler.post(() -> {
                        List<RoleListener> copy = new ArrayList<>(mRoleListeners);
                        for (RoleListener listener : copy) {
                            if (listener != null) {
                                listener.onRoleIn(null, roleInfo);
                            }
                        }
                    }
                );
            }
        } else {
            // 登出
            SQLog.w(TAG + "清空当前角色 " + old);
            mHandler.post(() -> {
                    List<RoleListener> copy = new ArrayList<>(mRoleListeners);
                    for (RoleListener listener : copy) {
                        if (listener != null) {
                            listener.onRoleOut(old);
                        }
                    }
                }
            );
        }
    }

    public void addUserListener(@NonNull UserListener listener) {
        mUserListeners.add(listener);
    }

    public void removeUserListener(@NonNull UserListener listener) {
        mUserListeners.remove(listener);
    }

    public static class UserListener {

        /**
         * 从登录变成非登录
         */
        public void onLogout(@NonNull UserInfo logoutUser) {

        }

        /**
         * 登录用户
         *
         * @param oldUser 非空表示是切换账号
         */
        public void onLogin(@Nullable UserInfo oldUser, @NonNull UserInfo loginUser) {

        }

        /**
         * 更新用户信息, 还是同一个用户, 登录态没有变化
         */
        public void onUserInfoChanged(@NonNull UserInfo oldUser, @NonNull UserInfo newUser) {

        }
    }

    public void addRoleListener(@NonNull RoleListener listener) {
        mRoleListeners.add(listener);
    }

    public void removeRoleListener(@NonNull RoleListener listener) {
        mRoleListeners.remove(listener);
    }

    public static class RoleListener {

        /**
         * 角色登出
         */
        public void onRoleOut(@NonNull RoleInfo role) {

        }

        /**
         * 角色登入
         *
         * @param oldRole 非空表示是切换角色
         */
        public void onRoleIn(@Nullable RoleInfo oldRole, @NonNull RoleInfo role) {

        }

        /**
         * 更新角色信息, 还是同一个角色
         */
        public void onRoleInfoChanged(@NonNull RoleInfo oldRole, @NonNull RoleInfo newRole) {

        }
    }
}
