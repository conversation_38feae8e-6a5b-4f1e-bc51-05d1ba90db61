package com.sqwan.common.mvp;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.NonNull;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;

import com.sqwan.common.dialog.FullScreenDialog;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.util.AndroidBug5497Workaround;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.msdk.config.ConfigManager;

/**
 * <AUTHOR>
 * @date 2020-03-11
 */
public class BaseDialog extends FullScreenDialog implements ILoadView {

    private LoadingDialog loadingDialog;
    protected Context mContext;

    public BaseDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    protected int getIdByName(String name, String type) {
        return SqResUtils.getIdByName(name, type, getContext());
    }

    protected boolean isSplashSDK() {
        return ConfigManager.getInstance(getContext()).isSplashSDK();
    }

    @Override
    public void showLoading() {
        if (loadingDialog == null) {
            loadingDialog = new LoadingDialog(mContext);
            loadingDialog.setCancelable(false);
        }
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    @Override
    public void hideLoading() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }

    public void setLoadingMsg(String msg) {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.setMessage(msg);
        }
    }

    @Override
    public void setContentView(@NonNull View view) {
        super.setContentView(view);
        Configuration configuration = mContext.getResources().getConfiguration();
        int orientation = configuration.orientation;
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            AndroidBug5497Workaround(view);
        }
    }

    @Override
    public void setContentView(int layoutResID) {
        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(layoutResID, null);
        setContentView(view);
    }

    protected void toast(String msg) {
        ToastUtil.showToast(getContext(),msg);
    }

    protected void AndroidBug5497Workaround(View view) {
        if (mContext != null && view != null && mContext instanceof Activity) {
            AndroidBug5497Workaround.assistActivity((Activity) mContext, view);
        }
    }
}
