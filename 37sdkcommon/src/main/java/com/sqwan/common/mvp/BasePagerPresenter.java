package com.sqwan.common.mvp;

import android.content.Context;
import android.view.View;

import com.sqwan.common.mod.account.ILoginListener;


public abstract class BasePagerPresenter<K extends IView> extends BasePresenter<K> {

    private IPageSwitcher mPageSwitcher;

    protected ILoginListener mLoginListener;

    public BasePagerPresenter(Context context, K view) {
        super(context, view);
    }

    public void setAccountPageSwitcher(IPageSwitcher pageSwitcher) {
        mPageSwitcher = pageSwitcher;
    }

    public abstract View getView();

}
