package com.sqwan.common.mvp;

import android.content.Context;
import android.view.View;

import com.sqwan.common.mod.account.ILoginListener;


public abstract class BaseViewPagerPresenter<K extends IView> extends BasePresenter<K> {

    private IPageSwitcher mPageSwitcher;

    protected ILoginListener mLoginListener;

    public BaseViewPagerPresenter(Context context, K view) {
        super(context, view);
    }

    public void setAccountPageSwitcher(IPageSwitcher accountPageSwitcher) {
        mPageSwitcher = accountPageSwitcher;
    }

    public void setLoginListener(ILoginListener loginListener) {
        mLoginListener = loginListener;
    }

    /**
     * 切换账号弹窗
     *
     * @param page
     */
    public void onSwitch(int page) {
        if (mPageSwitcher != null) {
            mPageSwitcher.onSwitch(page);
        }
    }

    public abstract View getView();

}
