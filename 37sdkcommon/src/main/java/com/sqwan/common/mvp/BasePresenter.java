package com.sqwan.common.mvp;

import android.content.Context;

import com.sqwan.common.util.LogUtil;

/**
 * <AUTHOR>
 * @date 2019/9/9
 */
public class BasePresenter<T extends IView> implements IPresenter  {


    protected T mView;

    protected Context context;

    public BasePresenter(Context context, T view) {
        this.context = context;
        this.mView = view;
    }

    @Override
    public void initData() {

    }

    @Override
    public void detachView() {
        if(mView != null) {
            LogUtil.i(mView.getClass().getName() + " detach from window");
        }
        this.mView = null;
    }
}
