package com.sqwan.common.view;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;

import com.sqwan.common.util.SqResUtils;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public class BaseView extends FrameLayout {

    private long clickTime = 0;

    public BaseView(Context context) {
        super(context);
    }

    protected int getIdByName(String name, String type) {
        return SqResUtils.getIdByName(name, type, getContext());
    }

    protected <T extends View> T getViewByName(View view, String name) {
        return view.findViewById(getIdByName(name, "id"));
    }

    protected synchronized boolean isQuickClick() {
        long current = System.currentTimeMillis();
        if (current - clickTime < 400) {
            clickTime = current;
            return true;
        }
        clickTime = current;
        return false;
    }
}
