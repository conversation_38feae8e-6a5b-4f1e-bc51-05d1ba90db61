package com.sqwan.common;


import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.util.Log;
import com.sq.diagnostic.assistant.DiagnosticAssistant;
import com.sq.sdk.tool.util.SqLogUtil;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.bugless.core.Bugless;
import com.sqwan.bugless.model.AppExtension;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;

/**
 * <AUTHOR>
 * @date 2019/8/14
 */
public class BuglessAction {

    /**
     * m层初始化错误
     */
    public static final int M_INIT = 2;

    /**
     * 登录验证token错误
     */
    public static final int M_TOKEN_VERIFY = 3;

    /**
     * 下单错误
     */
    public static final int M_ORDER = 4;

    /**
     * s层初始化错误
     */
    public static final int S_INIT = 5;

    /**
     * 自动生成账号失败
     */
    public static final int S_AUTO_REG = 10;

    /**
     * 登录错误
     */
    public static final int S_LOGIN = 11;


    /**
     * 支付失败
     */
    public static final int S_PAY_FAIL = 13;

    /**
     * 登录成功之后解析配置出错
     */
    public static final int PARSE_CONFIG_ERROR = 19;

    /**
     * 获取支付方式错误
     */
    public static final int ORDER_GET_PWAY = 20;

    /**
     * 获取钱包余额错误
     */
    public static final int ORDER_GET_WALLET = 21;

    /**
     * 获取代金券错误
     */
    public static final int ORDER_GET_COUPON = 22;

    /**
     * s层下单接口错误
     */
    public static final int S_ORDER = 23;


    /**
     * 检测登录接口username是否为空
     */
    public static final int LOGIN_USERNAME_EMPTY = 24;

    /**
     * 发生本地激活
     */
    public static final int FAKE_ACTIVE = 28;

    /** 个人信息保护指引弹窗失败 */
    public static final int USER_AUTH_POLICY_DES_DIALOG_ERROR = 101;

    /** 支付页跳转失败 */
    public static final int PAY_DIALOG_SHOW_FAIL = 102;

    /** H5 支付页失败 */
    public static final int PAY_WEB_PAGE_ERROR = 103;

    /** 原生支付页失败 */
    public static final int PAY_NATIVE_PAGE_ERROR = 106;

    /**
     * 应用内购买异常
     */
    public static final int PAY_ERROR = 124;

    /** 普通错误 */
    public static final int COMMON_ERROR = 999;

    private static String getMsg(int code) {
        String result = "未知类型";
        switch (code) {
            case M_INIT:
                result = "m层初始化解析过程-异常";
                break;
            case M_TOKEN_VERIFY:
                result = "登录验证token过程出错";
                break;
            case M_ORDER:
                result = "m层下单失败";
                break;
            case S_INIT:
                result = "s层初始化解析过程-异常";
                break;
            case S_AUTO_REG:
                result = "自动生成账号失败";
                break;
            case S_LOGIN:
                result = "登录失败";
                break;
            case S_PAY_FAIL:
                result = "调用支付返回了失败";
                break;
            case PARSE_CONFIG_ERROR:
                result = "登录成功之后解析配置出错";
                break;
            case ORDER_GET_PWAY:
                result = "查询支付方式失败";
                break;
            case ORDER_GET_WALLET:
                result = "查询钱包余额失败";
                break;
            case ORDER_GET_COUPON:
                result = "查询代金券失败";
                break;
            case S_ORDER:
                result = "s层下单失败";
                break;
            case PAY_ERROR:
                result = "应用内购买异常";
                break;
            default:
                break;
        }
        return result;
    }

    private static final String TAG = "【BUGLESS】";

    public static void init(Context context) {
        String url = "http://bugless.shan-yu-tech.com/api/bugless/";
        SqLogUtil.d("Bugless url=" + url);
        Bugless.getInstance().setUrl(url);
        Bugless.getInstance().init(context, new BuglessHttpClient());
    }

    public static void setAppExtension(SQAppConfig config) {
        if (config == null) {
            Log.e("sqsdk", TAG + "config为空, 无法设置bugless参数");
            return;
        }
        AppExtension appExtension = new AppExtension();
        appExtension.setSversion(VersionUtil.sdkVersion);
        appExtension.setRefer(config.getRefer());
        appExtension.setGid(config.getGameid());
        appExtension.setPid(config.getPartner());

        Bugless.getInstance().setAppExtension(appExtension);
    }

    public static void reportCatchException(Exception e, String data, int code) {
        reportCatchException(e, getMsg(code), data, code);
    }

    public static void reportCatchException(Throwable e, String businessMsg, String businessData, int actionType) {
        Log.d("sqsdk", TAG + "actionType：" + actionType
            + ", businessMsg：" + businessMsg + ", businessData：" + businessData, e);
        boolean isDebuggable = (0 != (SQContextWrapper.getApplicationContext().getApplicationInfo().flags
            & ApplicationInfo.FLAG_DEBUGGABLE));
        if (isDebuggable) {
            SqLogUtil.w(TAG + "debug版本, 不上报埋点数据, 直接认为上报成功 " + businessMsg);
            return;
        }
        Bugless.getInstance().reportCatchedExcaption(e, businessMsg, businessData, actionType, SensitiveInfoManager.getInstance().isAuthCheck());
        // 记录到诊断助手
        DiagnosticAssistant.getInstance().recordBuglessActionType(e, businessMsg, businessData, actionType);
    }
}
