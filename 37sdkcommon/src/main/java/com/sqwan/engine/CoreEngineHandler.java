package com.sqwan.engine;

import android.app.Activity;

import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.util.task.Task;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-16 16:17
 */
public class CoreEngineHandler extends BaseEnginHandler {
    private static final CoreEngineHandler ourInstance = new CoreEngineHandler();

    public static CoreEngineHandler getInstance() {
        return ourInstance;
    }

    private CoreEngineHandler() {
    }
    private LoadingDialog loadingDialog;
    public void showInitLoading(){
        Task.post(new Runnable() {
            @Override
            public void run() {
                if (loadingDialog==null) {
                    Activity activity = checkValid();
                    if (activity!=null) {
                        loadingDialog = new LoadingDialog(activity);
                    }
                }
                loadingDialog.show();
            }
        });


    }
    public void dismissInitLoading(){
        Task.post(new Runnable() {
            @Override
            public void run() {
                if (loadingDialog!=null) {
                    loadingDialog.dismiss();
                }
            }
        });

    }
}
