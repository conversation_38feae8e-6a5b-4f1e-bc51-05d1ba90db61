package com.sqwan.base;

import android.app.Activity;
import android.content.Context;

import com.sq.data.BuildConfig;

import java.lang.ref.WeakReference;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-16 10:29
 */
public class BaseEnginHandler {
    protected Context context;
    protected String TAG = this.getClass().getSimpleName();
    protected WeakReference<Activity> activityWeakReference;
    protected Activity checkValid(){
        return activityWeakReference.get();
    }
    public void init(Context _context){
        if (_context instanceof Activity && activityWeakReference==null) {
            activityWeakReference = new WeakReference<>((Activity)_context);
            context = _context.getApplicationContext();
        }
    }
    /**
     * 退出游戏
     */
    public void exit(){
        final Activity activity = checkValid();
        if (activity==null) {
            return;
        }
        activity.finish();
        System.exit(0);
    }

    protected boolean isLiveshowTypeHy(){
        return BuildConfig.isHyLiveShow;

    }
    protected boolean isLiveshowTypeAudio(){
        return BuildConfig.isYimAudio;
    }

}
