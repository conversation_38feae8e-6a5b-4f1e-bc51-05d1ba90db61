package com.sqwan.base;

import android.app.Activity;
import android.content.Context;

import com.sqwan.common.util.LogUtil;

import java.lang.ref.WeakReference;

/**
 * 描述: 全局上下文
 * 作者：znb
 * 时间：2021-06-04 12:01
 */
public class L {
    private static String TAG = "L";
    private static WeakReference<Activity> activityWeakReference;
    private static Context context;
    public static  void init(Context context){
        if (context instanceof Activity && activityWeakReference==null) {
            activityWeakReference = new WeakReference<>((Activity)context);
        }
        if (L.context==null) {
            L.context = context.getApplicationContext();
        }

    }
    public static Activity getActivity(){
        LogUtil.i(TAG,"getActivity");
        if (activityWeakReference!=null) {
            Activity activity = activityWeakReference.get();
            LogUtil.i(TAG,"activityWeakReference activity:"+activity);
            return activity;
        }
        LogUtil.i(TAG,"null");
        return null;
    }
    public static Context getApplicationContext(){
        return context;
    }
}
