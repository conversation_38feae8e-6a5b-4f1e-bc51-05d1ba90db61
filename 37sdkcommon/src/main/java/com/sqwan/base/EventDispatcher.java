package com.sqwan.base;

import com.sqwan.common.eventbus.OnActivityResultEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-01-07 11:49
 */
public class EventDispatcher {
    private static final EventDispatcher ourInstance = new EventDispatcher();

    public static EventDispatcher getInstance() {
        return ourInstance;
    }

    private EventDispatcher() {
    }

    public List<ActivityResultListener> activityResultListeners = new ArrayList<>();

    public void dispatcherActivityResultListener(OnActivityResultEvent onActivityResultEvent){
        for (ActivityResultListener activityResultListener : activityResultListeners) {
            if (activityResultListener!=null) {
                activityResultListener.onResult(onActivityResultEvent);
            }
        }
    }
    public void addActivityResultListener(ActivityResultListener activityResultListener){
        activityResultListeners.add(activityResultListener);
    }
    public void removeActivityResultListener(ActivityResultListener activityResultListener){
        activityResultListeners.remove(activityResultListener);
    }
    public void clearActivityResultListener(){
        activityResultListeners.clear();
    }
    public void clear(){
        clearActivityResultListener();
    }

}
