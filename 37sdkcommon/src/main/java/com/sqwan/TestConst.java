package com.sqwan;

import android.support.v4.BuildConfig;

/**
 * 描述:
 * 作者：znb
 * 时间：2020/11/12 15:25
 */
public class TestConst {
    private static final boolean isDebug = BuildConfig.DEBUG;

    /***** start 红包活动   *****/
    public static final boolean isTestOrientation = isDebug?false:false;
    public static final boolean isTestDownloadGifUrl = isDebug?false:false;
    public static final boolean isTestRedPacketDialog = isDebug?false:false;
    //强制弹出红包弹窗
    public static final boolean isForceShowRedPacketPop = isDebug?false:false;
    //弹窗url为空
    public static final boolean isTestEmptyRedPacketPop = isDebug?false:false;
    /***** end 红包活动   *****/



    /***** start 游客倒计时   *****/
    public static final boolean isRemainingTime = isDebug?false:false;
    public static final int testRemainingTime = 5;
    public static final boolean isForceStopReportDevDuration = isDebug?false:false;
    public static final boolean isForceStopReportUserDuration = isDebug?false:false;
    public static final String forceDevStopData = "{\n" +
            "\t\t\"needStop\": 1,\n" +
            "\t\t\"code\": 2,\n" +
            "\t\t\"url\": \"https://39ej7e.com/sdk/sdk-smrz/?authType=2\\u0026code=2\\u0026triggerType=reportDevDuration\\u0026operation=1\\u0026needCurfew=0\\u0026gid=1000000\\u0026pid=1\\u0026dev=c4c6f0a8e946b44a82f2b5e732a1d195\\u0026token=BASE64MjcyMTVWekRRb1Q4djQ5cDVZNlFVYkNhTG5zc0hyT0NuWlNGcmFWeHBRVEtIak5WcGxtQ09QdkFlZy8xSGNXTW5KeE52ZHJnenlVWDFZVnlLTUtvVmVLRUtKeGM0NDM1QnA0bUZrOE9XZWhUZHVVREhidU9OakwzQVdNb1pQdUZ0NmpVQndZeU9WVjFLOXNKbWJONmFOTW1qdThla0xlUFoyclpQdXUwc0FRZlBkRTZicmdTVUxndVBvNm82Ukk0ZDZkcHpJZlU4bUxGZ0pwK256Zw==\\u0026sversion=3.7.5\\u0026refer=1_1000000_1234_1234\",\n" +
            "\t\t\"interval\": 1,\n" +
            "\t\t\"remainingTime\": 0\n" +
            "\t}";
    public static final String forceUserStopData = "{\n" +
            "\t\t\"needStop\": 1,\n" +
            "\t\t\"code\": 1,\n" +
            "\t\t\"url\": \"https://39ej7e.com/sdk/anti-addiction/vertical-indulge.html?indulgeType=0\\u0026code=0\\u0026ageCode=3\\u0026gid=1000000\\u0026pid=1\\u0026dev=9552cfd00bfed7dbd9f8133a0fc9b03e\\u0026token=BASE64OTU5ZXRWTURPQTlZd2twNkwxVTQwVGRpYU1WVVhFc0JEbzZCSE1wR0Judk5LUGNzK0dZQkFBWVBiYVRmc2g1V3F6aWMraU1MeGZ1NTNRd043UWNLNFNmYUI4ZDNGZEFVVHZJc1dlRitnNXdseTBzalRnd1AyNkVFVms4KzJzVUVJMmRWWm1nQ3FVbUJzaUIvYzY2NkNnWC85Qnh2d2krdmV6WCtJOE9tWXFRK0N5Z0R5emdjNUcvWmh4U0ZBSE9RU1k1bUJRWnlmVS9YMWYxQ1BTYw==\\u0026sversion=3.7.5\\u0026refer=1_1000000_1234_1234\\u0026scut=1\",\n" +
            "\t\t\"interval\": 1\n" +
            "\t}";
    /***** end 游客倒计时   *****/


    /****start 悬浮球适配***/
    public static final boolean isTestFloatConfig = isDebug?true:false;
    /****end 悬浮球适配***/

    public static final boolean isTestInitLoadingDialog = isDebug?false:false;

    public static final boolean isDebugLiveShowConfigs = isDebug?true:true;

    public static final boolean isLiveshowImtest = isDebug?true:false;

    public static final int im_show_list_size = isDebug?5:300;

    public static final boolean isLiveshowHytest = isDebug?true:false;







}
