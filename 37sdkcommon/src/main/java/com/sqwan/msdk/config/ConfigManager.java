package com.sqwan.msdk.config;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.SQAppConfig;

import com.sqwan.msdk.api.SQAppConfigUtil;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2020/2/25
 */
public class ConfigManager {

    private Context mContext;
    private static ConfigManager instance;

    private SQAppConfig sqAppConfig;
    private String appKey;

    private HashMap<String, String> roleInfo;

    /**
     * 激活返回截流配置，简版判断依据
     * 默认值 "0"
     */
    private int loginCode=1;

    /**
     * 功能精简版SDK，依旧是37界面
     * 默认值 "0"
     */
    private int scode;

    private ConfigManager(Context context) {
        this.mContext = context.getApplicationContext();
    }

    public static ConfigManager getInstance(Context context) {
        if(instance == null) {
            synchronized (ConfigManager.class) {
                if(instance == null) {
                    instance = new ConfigManager(context);
                }
            }
        }
        return instance;
    }

    public void initConfig() {
        SQAppConfigUtil sqAppConfigUtil = new SQAppConfigUtil(mContext);
        sqAppConfigUtil.init();
        sqAppConfig = sqAppConfigUtil.getSQAppConfig();
    }

    public SQAppConfig getSQAppConfig() {
        return sqAppConfig;
    }

    public void setAppKey(String appKey) {
        LogUtil.i("ConfigManager--> set app key" + appKey);
        this.appKey = appKey;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setLoginCode(int code) {
        this.loginCode = code;
    }

    public int getLoginCode() {
        return loginCode;
    }

    public boolean isSplashSDK() {
        // 如果是多包SDK, 默认采用简版
        if(!TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3())) {
            return true;
        } else {
            return loginCode == 1;
        }
    }

    public boolean isSimplifiedSDK() {
        // 37sdk判断是否采用简版
        return loginCode == 1;
    }

    public void setLessFunctionCode(int scode) {
        this.scode = scode;
    }

    public int getLessFunctionCode() {
        return scode;
    }

    public boolean isLessFunction() {
        return scode == 1;
    }

    public void setRoleInfo(HashMap<String, String> roleInfo) {
        this.roleInfo = roleInfo;
    }

    public HashMap<String, String> getRoleInfo() {
        return roleInfo;
    }

    /**
     * 是否为sq sdk
     * gd jc获取到该值分别为gds jcs
     * @return
     */
    public boolean isSqSDK(){
        return TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3());
    }
}
