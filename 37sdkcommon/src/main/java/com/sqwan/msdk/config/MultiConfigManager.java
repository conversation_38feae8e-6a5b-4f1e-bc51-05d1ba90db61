package com.sqwan.msdk.config;

import com.sqwan.common.util.LogUtil;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;


public class MultiConfigManager {
    private static final String MULTI_SDK = "multiconfig";
    private static final String TAG = "MultiConfigManager";

    private static final String PRO_KEY_SQ_UNION = "isSqUnion";
    private static final String PRO_KEY_ORIENTATION = "isLandScape";
    private static final String PRO_KEY_WECHAT = "isWechat";
    private static final String PRO_KEY_SKIN = "skin";

    private static volatile MultiConfigManager instance;

    private String sqUnion;
    private String orientation;
    private String wechat;
    private String skin;

    private MultiConfigManager() {
    }

    public static MultiConfigManager getInstance() {
        if (instance == null) {
            synchronized (MultiConfigManager.class) {
                if (instance == null) {
                    instance = new MultiConfigManager();
                }
            }
        }
        return instance;
    }

    public void initMultiConfig(Context context) {
        Properties pro = readProperties(context);
        sqUnion = pro.getProperty(PRO_KEY_SQ_UNION);
        orientation = pro.getProperty(PRO_KEY_ORIENTATION);
        wechat = pro.getProperty(PRO_KEY_WECHAT);
        skin = pro.getProperty(PRO_KEY_SKIN);
        LogUtil.i(TAG,"initMultiConfig");
        LogUtil.e(TAG, "multiconfig: " + sqUnion + " orientation:" + orientation + " wechat:" + wechat + " skin:" + skin);
    }

    private Properties readProperties(Context context) {
        Properties p = null;
        InputStream in = null;
        try {
            in = context.getResources().getAssets().open(MULTI_SDK);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            Log.e(TAG, "readProperties catch: " + e.getMessage());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "readProperties finally: " + e.getMessage());
            }
        }
        return p;
    }

    /**
     * 是否为三七互娱体系
     *
     * @return
     */
    public boolean isSqUnion() {
        return !TextUtils.isEmpty(sqUnion) && sqUnion.equals("1");
    }

    /**
     * 游戏是否为横屏
     */
    public boolean isLandscape() {
        return !TextUtils.isEmpty(orientation) && orientation.equals("1");
    }

    /**
     * 是否微信小游戏转端的包
     */
    public boolean isWechat() {
        return !TextUtils.isEmpty(wechat) && wechat.equals("1");
    }

    /**
     * 皮肤类型
     */
    public String getSkinType() {
        return skin;
    }
}
