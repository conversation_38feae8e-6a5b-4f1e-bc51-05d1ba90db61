package com.sqwan.msdk.config;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.sqwan.common.util.LogUtil;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020-09-15
 */
public class MultiSdkManager {

    private static final String MULTI_SDK = "multi_sdk";
    private static final String TAG = "MultiSdkManager";

    private static final String PRO_KEY_CONFIG = "config";
    private static final String PRO_KEY_INFO = "info";
    private static final String PRO_KEY_HOST = "host";
    private static final String PRO_KEY_TAG = "logTag";
    private static final String PRO_KEY_ACCOUNT_DIR = "accountDir";
    private static final String PRO_KEY_ACCOUNT_FILE = "accountFile";
    private static final String PRO_KEY_SCUT3 = "scut3";

    private String mConfig;
    private String mInfo;
    private String mHost;
    private String mTag;
    private String mAccountDir;
    private String mAccountFile;

    /**
     * 如果参数里面有这个sdk类型的配置，则需要使用 如果没有，则表示为默认37sdk，不需要传递此参数
     */
    private String mScut3;

    /**
     * 统一域名配置
     *
     */
    public static String APP_HOST = "37.com.cn";


    public static final String SECURE_SUFFIX = "-secure."; //统一网关url拼接部分
//    public static final String SECURE_SUFFIX = "."; //非统一网关url拼接部分

    private static volatile MultiSdkManager instance;

    private MultiSdkManager() {
    }

    public static MultiSdkManager getInstance() {
        if(instance == null) {
            synchronized (MultiSdkManager.class) {
                if(instance == null) {
                    instance = new MultiSdkManager();
                }
            }
        }
        return instance;
    }

    public void initMultiSdk(Context context) {
        Properties pro = readProperties(context);
        if(pro == null) {
            Log.e(TAG, "multi_sdk config is null!");
            return;
        }
        mConfig = pro.getProperty(PRO_KEY_CONFIG);
        mInfo = pro.getProperty(PRO_KEY_INFO);
        mHost = pro.getProperty(PRO_KEY_HOST);
        mTag = pro.getProperty(PRO_KEY_TAG);
        mAccountDir = pro.getProperty(PRO_KEY_ACCOUNT_DIR);
        mAccountFile = pro.getProperty(PRO_KEY_ACCOUNT_FILE);
        mScut3 = pro.getProperty(PRO_KEY_SCUT3);
        Log.e(TAG, "multi_sdk config: " + mConfig + ", info:" + mInfo + ", host:" + mHost
                + ", tag:" + mTag + ", accountDir:" + mAccountDir + ", accountFile:" + mAccountFile + ", scut3:" + mScut3);
        refreshStaticConfig();
    }

    private void refreshStaticConfig() {
        LogUtil.setTag(mTag);
        APP_HOST = mHost;
    }


    private Properties readProperties(Context context) {
        Properties p = null;
        InputStream in = null;
        try {
            in = context.getResources().getAssets().open(MULTI_SDK);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (in!=null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return p;
    }


    public String getConfig() {
        return mConfig;
    }

    public String getInfo() {
        return mInfo;
    }

    public String getHost() {
        return mHost;
    }

    public String getTag() {
        return mTag;
    }

    public String getAccountDir() {
        return mAccountDir;
    }

    public String getAccountFile() {
        return mAccountFile;
    }

    public String getScut3() {
        return mScut3;
    }

    public boolean isScut3() {
        return !TextUtils.isEmpty(mScut3);
    }

}
