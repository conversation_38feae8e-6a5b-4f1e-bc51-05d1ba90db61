package com.sqwan.msdk;

import android.content.Context;

import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.PurchaseReportBean;
import com.sqwan.msdk.api.RegisterReportBean;
import com.sqwan.msdk.api.SQDefaultMediaReport;
import com.sqwan.msdk.api.SQDefaultReporter;
import com.sqwan.msdk.api.SQMediaReportInterface;
import com.sqwan.msdk.api.SQReportInterface;

import java.util.HashMap;
import java.util.Map;

public class BaseSQReportCore implements SQReportInterface {

    private SQReportInterface mReporter;
    private SQMediaReportInterface mMediaReport;
    private MDevObservable mDevObservable;
    private String mMDev;

    @Override
    public void init(Context context) {
        LogUtil.i("BaseSQReportCore init");
        if (mReporter == null) {
            LogUtil.i("mReporter is null 设置默认的");
            mReporter = new SQDefaultReporter();
        }
        if (mMediaReport == null) {
            LogUtil.i("mMediaReport is null  设置默认的");
            mMediaReport = new SQDefaultMediaReport();
        } else {
            LogUtil.i("mMediaReport is not null " + mMediaReport.toString());
        }
        mReporter.init(context);
    }

    @Override
    public void afterPermission(Context context) {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_after_permission);
        mReporter.afterPermission(context);
    }

    @Override
    public void eventRegister(RegisterReportBean bean) {
        HashMap<String, String> registerMap = new HashMap<>();
        if (bean != null) {
            registerMap.put("type", bean.getType());
            registerMap.put("success", bean.isSuccess() + "");
        }
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_register, registerMap);
        mReporter.eventRegister(bean);
    }

    @Override
    public void eventPurchase(PurchaseReportBean bean) {
        HashMap<String, String> purchaseMap = new HashMap<>();
        if (bean != null) {
            purchaseMap.put("productType", bean.getProductType());
            purchaseMap.put("channel", bean.getChannel());
            purchaseMap.put("currency", bean.getCurrency());
            purchaseMap.put("order_id", bean.getOrderId());
            purchaseMap.put("product_id", bean.getProductId());
            purchaseMap.put("product_name", bean.getProductName());
            purchaseMap.put("count", bean.getCount()+"");
            purchaseMap.put("price", bean.getPrice()+"");
            purchaseMap.put("success", bean.isSuccess()+"");
            purchaseMap.put("product_type", bean.getProductType());
        }
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_purchase, purchaseMap);
        mReporter.eventPurchase(bean);
    }

    @Override
    public void eventCpPay(String json) {
        Map<String, String> map = new HashMap<>();
        map.put("cp_data", json);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_cp_pay_event, map);
        mReporter.eventCpPay(json);
    }

    @Override
    public void report(String event, Map<String, String> params) {
        Map<String, String> map = new HashMap<>();
        map.put("report_event", event);
        map.putAll(params);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.report_event, map);
        mReporter.report(event, params);
    }

    public void setReporter(SQReportInterface reporter) {
        mReporter = reporter;
    }

    public void setMediaReporter(SQMediaReportInterface reporter) {
        LogUtil.i(this.toString() + " BaseSQReportCore setMediaReporter " + reporter.toString());
        mMediaReport = reporter;
    }

    public void setDevObservable(MDevObservable devObservable) {
        mDevObservable = devObservable;
    }

    public MDevObservable getDevObservable() {
        if (mDevObservable == null) {
            mDevObservable = new MDevObservable();
        }
        return mDevObservable;
    }

    public void setMDev(String mDev) {
        mMDev = mDev;
    }

    public String getMDev() {
        return mMDev;
    }

    public String getRefer() {
        LogUtil.i(this.toString() + " BaseSQReportCore getRefer " + mMediaReport.toString());
        return mMediaReport.mediaRefer();
    }
}
