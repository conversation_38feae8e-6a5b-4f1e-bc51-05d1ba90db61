package com.sqwan.msdk.api;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.AssetManager;
import android.content.res.Resources;

public class PluginContext extends ContextWrapper {

    private Resources mPluginResources;

    public PluginContext(Context base, Resources pluginResources) {
        super(base);
        mPluginResources = pluginResources;
    }

    @Override
    public AssetManager getAssets() {
        return mPluginResources.getAssets();
    }

    @Override
    public Resources getResources() {
        return mPluginResources;
    }
}