package com.sqwan.msdk.api;

import android.content.Context;

import com.sqwan.common.util.LogUtil;
import java.util.Map;

/**
 * <AUTHOR>
 * 默认事件上报，空实现
 */
public class SQDefaultReporter implements SQReportInterface {

    @Override
    public void init(Context context) {
        LogUtil.i("sq default reporter --> init");
    }

    @Override
    public void afterPermission(Context context) {
        LogUtil.i("sq default reporter --> afterPermission");
    }

    @Override
    public void eventRegister(RegisterReportBean bean) {
        LogUtil.i("sq default reporter --> eventRegister type: " + bean.getType() + ", success" + bean.isSuccess());
    }

    @Override
    public void eventPurchase(PurchaseReportBean bean) {
        LogUtil.i("sq default reporter --> eventPurchase orderId: " + bean.getOrderId() + ", success" + bean.isSuccess());
    }

    @Override
    public void eventCpPay(String json) {
        LogUtil.i("sq default reporter --> eventCpPay json : " + json);
    }

    @Override
    public void report(String event, Map<String, String> params) {
        LogUtil.i("sq default reporter --> report event: " + event);
    }


}
