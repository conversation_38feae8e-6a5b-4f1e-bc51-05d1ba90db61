package com.sqwan.msdk.api;

import android.content.Context;
import android.content.res.AssetManager;
import android.text.TextUtils;
import com.sqwan.common.util.ChannelUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpUtils;
import com.sqwan.msdk.SQReportCore;
import com.sqwan.msdk.config.MultiSdkManager;
import java.io.InputStream;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserFactory;

public class SQAppConfigUtil {

    public static String CONFIG_NODE_GAMEID = "gameid";
    public static String CONFIG_NODE_PARTNER = "partner";
    public static String CONFIG_NODE_REFER = "referer";
    private static final String CONFIG_REFER_CONTENT = "sy00000_1";

    private static final String SP_KEY_REFER = "refer";
    private static final String SP_KEY_GID = "gid";
    private static final String SP_KEY_PID = "pid";

    private Context context;
    private String gameid = "1000001";
    private String partner = "1";
    private String refer = "";

    public SQAppConfigUtil(Context context) {
        this.context = context;
    }

    public void init() {
        getGameInfo();
        getChannelInfoFromAPK(context);
        getAdvertiseMediaInfo();
        saveConfigValue();
    }

    public SQAppConfig getSQAppConfig() {
        return new SQAppConfig(gameid, partner, refer);
    }

    private void saveConfigValue() {
        SpUtils.get(context).put(SP_KEY_GID, gameid);
        SpUtils.get(context).put(SP_KEY_PID, partner);
        SpUtils.get(context).put(SP_KEY_REFER, refer);
    }


    private void getGameInfo() {
        AssetManager assetManager = context.getAssets();
        try {
            String configFileName = MultiSdkManager.getInstance().getConfig();
            InputStream is = assetManager.open(configFileName);
            XmlPullParserFactory xppf = XmlPullParserFactory.newInstance();
            XmlPullParser xpp = xppf.newPullParser();
            xpp.setInput(is, "utf-8");
            int eventType;
            eventType = xpp.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                switch (eventType) {
                    case XmlPullParser.START_TAG:
                        if (xpp.getName().equals(CONFIG_NODE_GAMEID)) {
                            gameid = xpp.nextText().trim();
                        }
                        if (xpp.getName().equals(CONFIG_NODE_PARTNER)) {
                            partner = xpp.nextText().trim();
                        }
                        if (xpp.getName().equals(CONFIG_NODE_REFER)) {
                            refer = getLocalRefer(context, xpp.nextText().trim());
                        }
                        break;
                    default:
                        break;
                }
                eventType = xpp.next();
            }
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("解析配置文件出错");
        }
    }

    /**
     * 获取特殊的渠道号
     *
     * @param context
     * @return
     */
    public String getLocalRefer(Context context, String referXML) {
        String localRefer = SpUtils.get(context).getString(SP_KEY_REFER, CONFIG_REFER_CONTENT);
        if (referXML.equals(CONFIG_REFER_CONTENT) && !TextUtils.isEmpty(localRefer)) {
            return localRefer;
        } else {
            return referXML;
        }
    }


    private void getChannelInfoFromAPK(Context context) {
        if (!"1".equals(partner)) {
            LogUtil.e("pid is not 1");
            return;
        }
        try {
            String channel = ChannelUtil.getChannelFromApk(context);
            LogUtil.e("channel info is " + channel);
            if (!"".equals(channel)) {
                String[] config = channel.split("-");
                if (config.length == 3) {
                    gameid = config[0];
                    partner = config[1];
                    refer = config[2];
                }
            }
        } catch (Exception ex) {
            LogUtil.e("读取 apk 渠道信息失败！" + ex.getMessage());
        }
    }

    private void getAdvertiseMediaInfo() {
        LogUtil.i("从广告媒体中读取渠道号");
        String advertiseMediaRefer = SQReportCore.getInstance().getRefer();
        if (!TextUtils.isEmpty(advertiseMediaRefer)) {
            LogUtil.i("广告媒体读取的refer为 " + advertiseMediaRefer);
            //媒体分包获取到渠道后进行refer拼接，规则： pid_gid_cid_num
            //其中cid_num是投手在媒体后台上手动填的，所以只需要拼接pid_gid
            refer = partner + "_" + gameid + "_" + advertiseMediaRefer;
        } else {
            LogUtil.i("广告媒体读取的refer为空");
        }
    }
}
