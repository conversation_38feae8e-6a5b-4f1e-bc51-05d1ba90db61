package com.sqwan.msdk;


import com.sqwan.common.util.LogUtil;

import java.util.Properties;

/**
 * 描述:
 * 作者：znb
 * 时间：2020-11-30 18:18
 */
public class ConfigsBean {
    public static final String string_1 = "1";
    public static final String string_0 = "0";
    public static ConfigsBean configsBean;
    interface ConfigsBeanKey{
        String isCheckPermissionPreview = "isCheckPermissionPreview";
        String isTvType = "isTvType";
        String isPlayWithouPermission = "isPlayWithouPermission";
    }
    public boolean isCheckPermissionPreview;
    public boolean isPlayWithouPermission;
    public boolean isTvType;
    public static ConfigsBean init(Properties prop) {
        configsBean = null;
        if (prop != null) {
            configsBean = new ConfigsBean();
            configsBean.isCheckPermissionPreview = string_1.equals(prop.getProperty(ConfigsBeanKey.isCheckPermissionPreview,string_0));
            configsBean.isTvType =string_1.equals(prop.getProperty(ConfigsBeanKey.isTvType,string_0));
            configsBean.isPlayWithouPermission = string_1.equals(prop.getProperty(ConfigsBeanKey.isPlayWithouPermission,string_0));
            LogUtil.d("ConfigsBean " + configsBean.toString());
        }
        return configsBean;

    }

    @Override
    public String toString() {
        return "ConfigsBean{" +
                "isCheckPermissionPreview=" + isCheckPermissionPreview +
                ", isTvType=" + isTvType +
                '}';
    }

}
