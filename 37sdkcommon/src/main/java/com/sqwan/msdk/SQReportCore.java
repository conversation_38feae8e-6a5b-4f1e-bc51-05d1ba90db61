package com.sqwan.msdk;

public class SQReportCore extends BaseSQReportCore {

    private SQReportCore(){}

    public static SQReportCore instance;
    public static byte[] lock = new byte[0];

    /**
     * 获取SQwanCore单例
     * @return
     */
    public static SQReportCore getInstance(){
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new SQReportCore();
                }
            }
        }
        return instance;
    }

}
