package com.sqwan.msdk;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * 被观察者
 */
public class ISQObservable<S, F> implements Runnable {

    private static ExecutorService sExecutorService = Executors.newSingleThreadExecutor();

    /**
     * 观察者
     */
    private ISQObserver<S, F> mObserver;

    @Override
    public void run() {

    }

    public ISQObservable startHandle() {
        sExecutorService.execute(this);
        return this;
    }

    public ISQObservable registerObserver(ISQObserver<S,F> observer) {
        mObserver = observer;
        return this;
    }

    public void unRegisterObserver() {
        mObserver = null;
    }

    public void handleSuccess(S s) {
        if (mObserver != null) {
            mObserver.onSuccess(s);
        }
    }

    public void handleFail(F f) {
        if (mObserver != null) {
            mObserver.onFail(f);
        }
    }
}
