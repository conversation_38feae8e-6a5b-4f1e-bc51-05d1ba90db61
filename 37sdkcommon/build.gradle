apply plugin: 'com.android.library'
apply plugin: "com.jfrog.artifactory"
apply plugin: 'maven-publish'
apply plugin: 'com.kezong.fat-aar'
apply plugin: 'com.sq.sqinject'

def libraryVersion = get_libraryVersion()
def sqPluginVersion = sqPluginVersion()
def gdsLibraryVersion = gdsLibraryVersion()
def sqLibraryVersion = sqLibraryVersion()
def gwVersion = gwVersion()

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode 1
        versionName libraryVersion

        buildConfigField "String", "libraryVersion", String.format("\"%s\"", libraryVersion)
        buildConfigField "String", "sqPluginVersion", String.format("\"%s\"", sqPluginVersion)
        buildConfigField "String", "gdsLibraryVersion", String.format("\"%s\"", gdsLibraryVersion)
        buildConfigField "String", "sqLibraryVersion", String.format("\"%s\"", sqLibraryVersion)
        buildConfigField "String", "gwVersion", String.format("\"%s\"", gwVersion)

        buildConfigField "boolean", "isPluginMode", "${get_isPluginMode}"
        buildConfigField "boolean", "isSqSdkType", "${true}"
        buildConfigField "boolean", "isHyLiveShow", "${isHyLiveShow}"
        buildConfigField "boolean", "isYimAudio", "${isYimAudio}"
        buildConfigField "boolean", "isTrack", "${isTrack}"

    }

    buildTypes {
        release {
            matchingFallbacks = ['release']
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    compileOptions {
        encoding "UTF-8"
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

publishing {
    publications {
        aar(MavenPublication) {
            groupId = rootProject.ext.artifactory_groupId
            artifactId "sySDKData"
            version = libraryVersion
            artifact "${project.buildDir}/outputs/aar/${project.name}-release.aar"
        }
    }
}

artifactory {
    contextUrl = rootProject.ext.artifactory_address
    publish {
        repository {
            repoKey = rootProject.ext.artifactory_repoKey
            username = rootProject.ext.artifactory_user
            password = rootProject.ext.artifactory_password
        }
        defaults {
            publishArtifacts = true
            publications('aar')
            publishPom = true //Publish generated POM files to Artifactory (true by default)
            publishIvy = true
            //Publish generated Ivy descriptor files to Artifactory (true by default)
        }
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api(rootProject.ext.sqsdk.volly) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    api rootProject.ext.sqsdk.bugless
    api(rootProject.ext.sqsdk.webview) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'org.jetbrains', module: 'annotations'
    }
    api 'com.37sy.android:tool:2.0.5'
    api 'com.37sy.android:sqeventbus:1.0.0'
    api rootProject.ext.other.okhttp
    api rootProject.ext.other.okio

    api project(':module_api')
    api project(':module_base:base_mod')
    api project(':37SDKLibrarys')
    api project(':notchtools')
    api project(':module_plugin:plugin_standard')
    api project(':gson')
    //插件资源
    api project(":lib_tools")
    api project(":module_ui:sq")
    api project(":module_ui:skin:fx")
    //宿主跟插件都需要的的资源
    api project(':module_ui_common:base')
    api project(":module_ui_common:sq")

    api rootProject.ext.sqsdk.logger

    //按压上报库
    api 'com.37sy.android.touch:touch:1.6'
    //设备评分库
    api 'com.37sy.android.device_tools:device_tools:2.10.4'

    // 分享相关类用到, 所以暂时引入
    compileOnly project(':module_third:social_sdk')
}

// Task to delete old jar
task deleteOldJar(type: Delete) {
    delete "build/libs/37SDK_common_v${libraryVersion}.jar"
}

// task to export contents as jar
task exportJar(type: Copy) {
    from('build/intermediates/packaged-classes/release/')
    into('build/libs/')
    include('classes.jar')
    rename('classes.jar', "37SDK_common_v${libraryVersion}.jar")
}
exportJar.dependsOn(deleteOldJar, assemble)
