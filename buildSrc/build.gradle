// Top-level build file where you can add configuration options common to all sub-projects/modules.
group 'com.sq.gradle'
version '1.0.6'
//是否发布到远程仓库的开关
def isBuildRemoteJar = false

buildscript {
    repositories {
        maven{ url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.4'
        classpath "com.github.jengelman.gradle.plugins:shadow:4.0.2"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven{ url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        jcenter()
    }
}

apply plugin: 'groovy'
apply plugin:'maven'
apply plugin: 'com.github.johnrengelman.shadow'
apply from: '../BuildSystem/artifact_config.gradle'
//hook res
//apply from: '../pluginconfig/resplugin.gradle'
shadowJar {
    zip64 true
}

dependencies {
    compileOnly gradleApi()
    compileOnly localGroovy()
    if (isBuildRemoteJar) {
        compileOnly 'com.android.tools.build:gradle:3.6.4'
    } else {
        implementation 'com.android.tools.build:gradle:3.6.4'
    }
    //javassist
    implementation 'org.javassist:javassist:3.23.1-GA'
    //commons-io
    implementation 'commons-io:commons-io:2.6'
    implementation 'com.google.code.gson:gson:2.8.5'
}

//编译远程jar包，则传到远程仓库，编译本地jar包则传到本地仓库，使用isBuildRemoteJar作为开关，远程jar包提供给channel工程使用
if (!isBuildRemoteJar) {
    uploadArchives {
        repositories {
            mavenDeployer {
                repository(url: uri('../gradlerepo'))
            }
        }
    }
} else {
    artifacts {
        archives(file("$buildDir/libs/$project.name-$project.version-all.jar"))
    }
    uploadArchives {
        repositories {
            mavenDeployer {
                repository(url: uri('http://www.azact.com/artifactory/sy_dev_libs')) {
                    authentication(userName: rootProject.ext.artifactory_user, password: rootProject.ext.artifactory_password)
                }
                pom.artifactId = 'sqfix-gradle-plugin'
                pom.groupId = 'com.37sy.android'
                pom.version = project.version
            }
        }
    }
}