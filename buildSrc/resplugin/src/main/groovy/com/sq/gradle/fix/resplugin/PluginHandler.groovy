package com.sq.gradle.fix.resplugin

import com.sq.gradle.fix.common.ClassFileTransform
import com.sq.gradle.fix.common.CtClassBean
import javassist.ClassPool
import javassist.CtClass
import org.gradle.api.Project

class PluginHandler {

    private static PluginHandler sInstance

    private ClassPool pool

    private ClassFileTransform mClassFileTransform

    ResConfig  resconfig

    private PluginHandler() {
        pool = ClassPool.getDefault()
        mClassFileTransform = new ClassFileTransform(pool)

    }
    void init(Project project){
        ResConfig  resconfig = project.resconfig
        resconfig  = resconfig.parse(resconfig.configPath)
        this.resconfig = resconfig
        //将当前路径加入类池,不然找不到这个类
        //project.android.bootClasspath 加入android.jar，不然找不到android相关的所有类
//        pool.appendClassPath(project.android.bootClasspath[0].toString())
//        //引入android.os.Bundle包，因为onCreate方法参数有Bundle
//        pool.importPackage("android.os.Bundle")
//        pool.importPackage("android.content.Context");
//        pool.importPackage("com.sqwan.hook.ReflectionUtils")
    }
    static PluginHandler getInstance() {
        if (sInstance == null) {
            sInstance = new PluginHandler()
        }
        return sInstance
    }

    void appendClassPath(String path) {
        pool.appendClassPath(path)
        mClassFileTransform.addFilePath(path)
    }

    void handleInputFile() {
        Map<String, CtClassBean> map = mClassFileTransform.toCtClass()
        for (String key : map.keySet()) {
            CtClassBean ctClassBean = map.get(key)
            CtClass ctClass = ctClassBean.ctClass
            if (!ctClass.name.contains("\$")) {
                handleClass(ctClassBean)
            }
        }
//        for (String classKey : map.keySet()) {
//            CtClassBean deleteClassBean = map.get(classKey)
//            if (deleteFileFilter(deleteClassBean)) {
//                deleteClassBean.deleteFile()
//            }
//        }
    }
    void handleClass(CtClassBean ctClassBean){
        MyInjects.inject(this.resconfig,ctClassBean)
    }

}
