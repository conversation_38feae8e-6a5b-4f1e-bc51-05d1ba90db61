package com.sq.gradle.fix.resplugin


import org.gradle.api.Plugin
import org.gradle.api.Project

class ResPlugin implements Plugin<Project> {

    @Override
    String toString() {
        return super.toString()
    }

    @Override
    void apply(Project project) {
        project.extensions.create('resconfig', ResConfig)
        def android = project.extensions.android
        android.registerTransform(new ResTransform(project))
    }

}
