package com.sq.gradle.fix.resplugin

import javassist.ClassPool
import javassist.CtMethod


public class Utils {

    /**
     * 事先载入相关类
     * @param pool
     */
    static void importBaseClass(ClassPool pool) {
        pool.importPackage("android.os.Bundle")
        pool.importPackage("android.content.Context")
        pool.importPackage("com.hook.ReflectionUtils")
    }

    static String getSimpleName(CtMethod ctmethod) {
        def methodName = ctmethod.getName();
        return methodName.substring(
                methodName.lastIndexOf('.') + 1, methodName.length());
    }

    static String getClassName(int index, String filePath) {
        int end = filePath.length() - 6 // .class = 6
        return filePath.substring(index, end).replace('\\', '.').replace('/', '.')
    }
}
