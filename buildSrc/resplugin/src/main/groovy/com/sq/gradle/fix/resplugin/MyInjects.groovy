package com.sq.gradle.fix.resplugin

import com.android.build.api.transform.DirectoryInput
import com.android.build.api.transform.Format
import com.android.build.api.transform.JarInput
import com.android.build.api.transform.TransformOutputProvider
import javassist.ClassPool
import javassist.CtClass
import javassist.CtMethod
import javassist.JarClassPath
import javassist.NotFoundException
import org.gradle.api.Project
import org.apache.commons.io.FileUtils
import org.gradle.util.TextUtil

import java.lang.reflect.Method

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.println


class MyInjects {
    //初始化类池
    private final static ClassPool pool = ClassPool.getDefault();
    static def classPathList = new ArrayList<JarClassPath>()

    static void removeClassPath(Project project) {
        if (classPathList != null) {
            def pool = ClassPool.getDefault()
            classPathList.each {
                try {

                    pool.removeClassPath(it)
                } catch (Exception e) {
                    project.logger.error(e.getMessage())
                }
            }
            classPathList.clear()
        }
    }


    static void injectJar(String path, Project project) {
        ClassPool pool = ClassPool.getDefault()
        def classPath = new JarClassPath(path)
        classPathList.add(classPath)
        pool.appendClassPath(classPath)

        //project.android.bootClasspath 加入android.jar，否则找不到android相关的所有类
        pool.appendClassPath(project.android.bootClasspath[0].toString());
        Utils.importBaseClass(pool);
    }
    static void inject(String path, Project project) {
        ClassPool pool = ClassPool.getDefault()
        pool.appendClassPath(path)

        //project.android.bootClasspath 加入android.jar，否则找不到android相关的所有类
        pool.appendClassPath(project.android.bootClasspath[0].toString());
        Utils.importBaseClass(pool);
        File dir = new File(path)
        if (!dir.isDirectory()) {
            return;
        }


        ResConfig resconfig = project.resconfig
        resconfig = resconfig.parse(resconfig.configPath)
        Map<String, ResConfig.ActivityBean> activityBeanMap = resconfig.activityBeanMap
            //遍历文件夹
            dir.eachFileRecurse { File file ->
                String filePath = file.absolutePath
                println("filePath = " + filePath)
                for (String s : activityBeanMap.keySet()) {
                    //获取MainActivity.class
                    String className = activityBeanMap.get(s).className
                    CtClass ctClass = null
                    try {
                        ctClass = pool.getOrNull(className)
                    }catch(NotFoundException e){
                        e.printStackTrace()
                        continue
                    }
                    if(ctClass==null)
                        continue
                    println("ctClass = " + ctClass)
                    //解冻
                    if (ctClass.isFrozen())
                        ctClass.defrost()

                    try {
                        CtMethod ctMethod = ctClass.getDeclaredMethod("attachBaseContext")
                        if(ctMethod!=null){
                            println("method")
                        }
                    }catch(Exception e){
                        e.printStackTrace()
                        println("method not")
                        String method = "protected void attachBaseContext(android.content.Context newBase) {\n" +
                                "        super.attachBaseContext(newBase);\n" +
                                "    }"
                        try {
                            CtMethod ctMethod = CtMethod.make(method,ctClass)
                            ctClass.addMethod(ctMethod)
                            String hook = "ReflectionUtils.hookResources(newBase);"
                            ctMethod.insertAfter(hook)
                        }catch(Exception ex){
                            println("insertAfter error")
                            ex.printStackTrace()
                        }

                    }finally{
                        ctClass.writeFile(path)
                        ctClass.detach()//释放
                    }
                }


            }

    }

    static void processJarInput(JarInput jarInput, TransformOutputProvider outputProvider,Project project) {
        File dest = outputProvider.getContentLocation(
                jarInput.getFile().getAbsolutePath(),
                jarInput.getContentTypes(),
                jarInput.getScopes(),
                Format.JAR)
        inject(jarInput.getFile().getAbsolutePath(),project)
        //将修改过的字节码copy到dest，就可以实现编译期间干预字节码的目的了
        FileUtils.copyFile(jarInput.getFile(), dest)
    }

    static void processDirectoryInputs(DirectoryInput directoryInput, TransformOutputProvider outputProvider,Project project) {
        File dest = outputProvider.getContentLocation(directoryInput.getName(),
                directoryInput.getContentTypes(), directoryInput.getScopes(),
                Format.DIRECTORY)
        //建立文件夹
        FileUtils.forceMkdir(dest)
//        inject(directoryInput.getFile().getAbsolutePath(),project)
        //将修改过的字节码copy到dest，就可以实现编译期间干预字节码的目的了
        FileUtils.copyDirectory(directoryInput.getFile(), dest)
    }
}
