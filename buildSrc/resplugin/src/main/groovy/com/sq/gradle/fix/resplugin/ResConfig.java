package com.sq.gradle.fix.resplugin;

import com.google.gson.Gson;
import com.sq.gradle.fix.common.FileUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-07-14 21:53
 */
public class ResConfig {
    class ActivityBean{
        public String fileName;
        public String className;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            ActivityBean that = (ActivityBean) o;

            if (!fileName.equals(that.fileName)) return false;
            return className.equals(that.className);
        }

        @Override
        public int hashCode() {
            int result = fileName.hashCode();
            result = 31 * result + className.hashCode();
            return result;
        }
    }
    public String configPath;
    public List<String> resActivity = new ArrayList<>();
    public Map<String,ActivityBean> activityBeanMap = new HashMap<>();
    public ResConfig parse(String configPath){
        String jsonContent = FileUtil.read(configPath);
        ResConfig resConfig =  new Gson().fromJson(jsonContent,ResConfig.class);
        for (String s : resConfig.resActivity) {
            ActivityBean activityBean = new ActivityBean();
            activityBean.className = s;
            String[] result = s.split("\\.");
            activityBean.fileName = result[result.length-1];
            activityBeanMap.put(s,activityBean);
        }
        resConfig.resActivity = resActivity;
        resConfig.activityBeanMap = activityBeanMap;
        resConfig.configPath = configPath;
        return resConfig;
    }
}
