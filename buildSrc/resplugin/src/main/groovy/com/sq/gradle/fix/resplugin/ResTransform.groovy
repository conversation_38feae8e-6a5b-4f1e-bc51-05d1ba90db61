package com.sq.gradle.fix.resplugin

import com.android.build.api.transform.Context
import com.android.build.api.transform.DirectoryInput
import com.android.build.api.transform.Format
import com.android.build.api.transform.JarInput
import com.android.build.api.transform.QualifiedContent
import com.android.build.api.transform.Transform
import com.android.build.api.transform.TransformException
import com.android.build.api.transform.TransformInput
import com.android.build.api.transform.TransformInvocation
import com.android.build.api.transform.TransformOutputProvider
import com.android.build.gradle.internal.pipeline.TransformManager
import com.sq.gradle.fix.common.LogUtils
import javassist.ClassPool
import javassist.CtClass
import javassist.CtMethod
import javassist.bytecode.stackmap.BasicBlock
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FileUtils
import org.gradle.api.Project

import java.lang.reflect.Method

/**
 * 插件变换使用的Transform
 */
class ResTransform extends Transform {
    String tag = this.getClass().getSimpleName()
    Project project
    ResTransform(Project project) {
        this.project = project
    }
    /**
     * transform的名称
     * transformClassesWithMyClassTransformForDebug 运行时的名字
     * transformClassesWith + getName() + For + Debug或Release
     *
     * @return String
     */
    @Override
    String getName() {
        return tag
    }

    /**
     * 需要处理的数据类型，有两种枚举类型
     * CLASSES和RESOURCES，CLASSES代表处理的java的class文件，RESOURCES代表要处理java的资源
     *
     * @return
     */
    @Override
    Set<QualifiedContent.ContentType> getInputTypes() {
        return TransformManager.CONTENT_CLASS
    }

    /**
     * 指Transform要操作内容的范围，官方文档Scope有7种类型：
     * EXTERNAL_LIBRARIES        只有外部库
     * PROJECT                   只有项目内容
     * PROJECT_LOCAL_DEPS        只有项目的本地依赖(本地jar)
     * PROVIDED_ONLY             只提供本地或远程依赖项
     * SUB_PROJECTS              只有子项目。
     * SUB_PROJECTS_LOCAL_DEPS   只有子项目的本地依赖项(本地jar)。
     * TESTED_CODE               由当前变量(包括依赖项)测试的代码
     *
     * Returns the scope(s) of the Transform. This indicates which scopes the transform consumes.
     */
    @Override
    Set<? super QualifiedContent.Scope> getScopes() {
        return TransformManager.SCOPE_FULL_PROJECT
    }

    /**
     * 指明当前Transform是否支持增量编译
     * If it does, then the TransformInput may contain a list of changed/removed/added files, unless
     * something else triggers a non incremental run.
     */
    @Override
    boolean isIncremental() {
        return false
    }

//    @Override
//    void transform(TransformInvocation transformInvocation) throws TransformException, InterruptedException, IOException {
//        super.transform(transformInvocation)
//
//        //OutputProvider管理输出路径，如果消费型输入为空，你会发现OutputProvider == null
//        TransformOutputProvider outputProvider = transformInvocation.getOutputProvider();
//        outputProvider.deleteAll()
//        transformInvocation.inputs.each { TransformInput input ->
//            input.jarInputs.each { JarInput jarInput ->
//                //处理Jar
//                MyInjects.processJarInput(jarInput, outputProvider,project)
//            }
//
//            input.directoryInputs.each { DirectoryInput directoryInput ->
//                //处理源码文件
////                MyInjects.processDirectoryInputs(directoryInput, outputProvider,project)
//            }
//        }
//    }
    /**
     * Transform中的核心方法
     * transformInvocation.getInputs() 中是传过来的输入流，其中有两种格式，一种是jar包格式一种是目录格式。
     * transformInvocation.getOutputProvider() 获取到输出目录，最后将修改的文件复制到输出目录，这一步必须做不然编译会报错
     *
     * @param transformInvocation
     * @throws TransformException* @throws InterruptedException* @throws IOException
     */
    @Override
    void transform(Context context,
                   Collection<TransformInput> inputs,
                   Collection<TransformInput> referencedInputs,
                   TransformOutputProvider outputProvider,
                   boolean isIncremental) throws IOException, TransformException, InterruptedException {
        outputProvider.deleteAll()
        CtClass.debugDump ="./dump"
        System.out.println("start--------------res transform----------------")
        // Transform的inputs有两种类型，一种是目录，一种是jar包，要分开遍历
        inputs.each { TransformInput input ->
//            try {
//                //对 jar包 类型的inputs 进行遍历
//                input.jarInputs.each {
//
//                    //这里处理自定义的逻辑
//                    MyInjects.inject(it.file.getAbsolutePath(), project)
//
//                    // 重命名输出文件（同目录copyFile会冲突）
//                    String outputFileName = it.name.replace(".jar", "") + '-' + it.file.path.hashCode()
//                    def output = outputProvider.getContentLocation(outputFileName, it.contentTypes, it.scopes, Format.JAR)
//                    FileUtils.copyFile(it.file, output)
//
//                }
//            } catch (Exception e) {
//                project.logger.error(e.getMessage())
//            }

            //对类型为“文件夹”的input进行遍历
            input.directoryInputs.each { DirectoryInput directoryInput ->
                //文件夹里面包含的是我们手写的类以及R.class、BuildConfig.class以及R$XXX.class等
                MyInjects.inject(directoryInput.file.absolutePath, project)

                // 获取output目录
                def dest = outputProvider.getContentLocation(directoryInput.name,
                        directoryInput.contentTypes, directoryInput.scopes,
                        Format.DIRECTORY)

                // 将input的目录复制到output指定目录
                FileUtils.copyDirectory(directoryInput.file, dest)
            }

            //关闭classPath，否则会一直存在引用
            MyInjects.removeClassPath(project)
        }
        ClassPool.getDefault().clearImportedPackages();
        System.out.println("end--------------res transform----------------")
    }
}
