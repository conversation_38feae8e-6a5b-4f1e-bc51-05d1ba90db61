package com.sq.gradle.fix.common

import javassist.ClassPool
import javassist.CtClass

class ClassFileTransform {

    final Set<String> pathSet

    final ClassPool pool

    ClassFileTransform(ClassPool pool, Collection<String> paths) {
        pathSet = new HashSet<>(paths)
        this.pool = pool
    }

    Map<String, CtClassBean> toCtClass() {
        Map<String, CtClassBean> map = new HashMap<>()
        for (String path : pathSet) {
            File file = new File(path)
            if (file.isDirectory()) {
                handleDir(file, map)
            } else {
                handleJar(path, map)
            }
        }
        return map
    }

    private void handleDir(File dir, Map<String, CtClassBean> map) {
        // 递归处理目录
        dir.eachFileRecurse { File child ->
            if (child.isFile() && child.name.endsWith(".class")) {
                handleClassFileInDir(child, dir.absolutePath, map)
            }
        }
    }

    private void handleClassFileInDir(File file, String dirRoot, Map<String, CtClassBean> map) {
        if (file.isDirectory() || !file.name.endsWith(".class")) {
            return
        }
        // 移除根目录
        String classPath = file.path.replace(dirRoot, "")
        // 移除首位斜杠
        if (classPath.startsWith(File.separator)) {
            classPath = classPath.substring(1)
        }
        // 路径转类名
        String className = classPath.replace(File.separator, ".").replace(".class", "")
        CtClass ctClass
        try {
            //这里可以排除一下R文件的
            ctClass = pool.getCtClass(className)
        } catch (Exception ex) {
            LogUtils.e("没有找到$dirRoot 中的类: ${classPath}(${className})\n\t${ex}")
            throw ex
        }
        CtClassBean ctClassBean = new CtClassBean(ctClass, dirRoot, CtClassBean.TYPE_CLASS_FILE)
        // 类名作key
        CtClassBean exist = map.put(className, ctClassBean)
        if (exist != null) {
            throw new IllegalStateException("${className}出现重复类: \nA: ${ctClass.URL}\nB: ${exist.ctClass.URL}")
        }
    }

    private void handleJar(String jarPath, Map<String, CtClassBean> map) {
        // 避免Jar句柄问题
        JarFileUtils.clearJarFactoryCache(jarPath)
        // 是单个文件，是jar包
        Set<String> jarFileNameSet = JarFileUtils.deCopressName(jarPath)
        for (String classPath : jarFileNameSet) {
            if (classPath.endsWith(".class")) {
                if (classPath.startsWith("META-INF")) {
                    // 忽略特殊文件
                    continue
                }
                // 路径转类名
                String className = classPath.replace(File.separator, ".").replace(".class", "")
                CtClass ctClass
                try {
                    ctClass = pool.getCtClass(className)
                } catch (Exception ex) {
                    LogUtils.e("没有找到$jarPath 中的类: ${classPath}(${className})\n\t$ex")
                    throw ex
                }
                CtClassBean ctClassBean = new CtClassBean(ctClass, jarPath, CtClassBean.TYPE_JAR_FILE)
                // 类名作key
                CtClassBean exist = map.put(className, ctClassBean)
                if (exist != null) {
                    throw new IllegalStateException("${className}出现重复类: \nA: ${ctClass.URL}\nB: ${exist.ctClass.URL}")
                }
            }
        }
    }
}
