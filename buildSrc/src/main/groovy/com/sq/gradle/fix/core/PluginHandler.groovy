package com.sq.gradle.fix.core

import com.sq.gradle.fix.common.ClassFileTransform
import com.sq.gradle.fix.common.CtClassBean
import com.sq.gradle.fix.common.JarFileUtils
import com.sq.gradle.fix.common.LogUtils
import com.sq.gradle.fix.config.ConfigBean
import com.sq.gradle.fix.handler.ActivityHandler
import com.sq.gradle.fix.handler.ApplicationHandler
import com.sq.gradle.fix.handler.ReceiverHandler
import com.sq.gradle.fix.manifest.ManifestHelper
import javassist.ClassPath
import javassist.ClassPool
import javassist.CtClass

class PluginHandler {

    private static PluginHandler sInstance

    private final static ClassPool pool = ClassPool.getDefault()
    private final static Map<String, ClassPath> classPathMap = new HashMap<>()

    private ManifestHelper manifestHelper

    private ActivityHandler mActivityHandler

    private ApplicationHandler mAppHandler

    private ReceiverHandler mReceiverHandler

    //名称替换映射
    Map<String, String> nameMap

    //不处理的第三方activity过滤
    Set<String> mActivityFilter

    //需要删除的class列表，规避虚拟机加载规则
    Set<String> mDeleteClassList

    //不处理的receiver
    Set<String> mReceiverFilter

    //第三方不打入插件的jar包过滤
    Set<String> mFilterLocalJar

    //要处理的Application的名称
    String mApplicationName

    private PluginHandler() {
        manifestHelper = new ManifestHelper()
        mActivityHandler = new ActivityHandler()
        mReceiverHandler = new ReceiverHandler()
        mAppHandler = new ApplicationHandler()
    }

    void init(String manifestPath, ConfigBean configBean) {
        manifestHelper.init(manifestPath)
        nameMap = configBean.nameMap
        mActivityFilter = configBean.activityFilter
        mFilterLocalJar = configBean.filterLocalJar
        mDeleteClassList = configBean.deleteClassList
        mReceiverFilter = configBean.receiverFilter
        mApplicationName = configBean.applicationName
    }

    void appendClassPath(String path) {
        LogUtils.v("添加类 $path")
        ClassPath classPath = pool.appendClassPath(path)
        classPathMap.put(path, classPath)
    }

    static PluginHandler getInstance() {
        if (sInstance == null) {
            sInstance = new PluginHandler()
        }
        return sInstance
    }

    @Override
    int hashCode() {
        return super.hashCode()
    }

    void handleInputFile() {
        // 根据配置重命名类, 但是没修改类中的索引
        renameClasses()

        LogUtils.d("收集类信息")
        ClassFileTransform classFileTransform = new ClassFileTransform(pool, classPathMap.keySet())
        Map<String, CtClassBean> map = classFileTransform.toCtClass()

        LogUtils.i("包含${map.size()}个类, 开始处理类")

        for (String key : map.keySet()) {
            CtClassBean ctClassBean = map.get(key)
            handleClass(ctClassBean)
        }

        for (String classKey : map.keySet()) {
            CtClassBean deleteClassBean = map.get(classKey)
            if (deleteFileFilter(deleteClassBean)) {
                LogUtils.w("删除: ${deleteClassBean.ctClass.name} (${deleteClassBean.ctClass.URL})")
                deleteClassBean.deleteFile()
            }
        }
    }

    /**
     * 重命名指定类
     * 注意: 没有修改类中的重命名类的引用
     */
    private void renameClasses() {
        Map<String, String> originNameMap = new HashMap<>(nameMap)
        LogUtils.i("重命名配置中的类: ${originNameMap.size()}个")
        for (Map.Entry<String, String> entry : originNameMap.entrySet()) {
            String oldName = entry.key
            String newName = entry.value

            renameClass(oldName, newName)
        }
        LogUtils.i("重命名结束, 配置${originNameMap.size()}个类, 实际处理${nameMap.size()}")
        LogUtils.i("重命名后的类")
        for (Map.Entry<String, String> entry : nameMap.entrySet()) {
            String oldName = entry.key
            String newName = entry.value
            try {
                CtClass newClass = pool.get(newName)
                LogUtils.i("\t${newName}, ${newClass.URL}")
            } catch (Exception e) {
                LogUtils.e("\t找不到重命名后的类 ${newName}")
                throw e
            }
            // 检查配置中是否有被重命名的类, 替换
            if (mApplicationName == oldName) {
                LogUtils.i("\t\t修改要处理的ApplicationName: ${oldName} => ${newName}")
                mApplicationName = newName
            }
            if (mActivityFilter != null && mActivityFilter.contains(oldName)) {
                LogUtils.i("\t\t修改要过滤的Activity: ${oldName} => ${newName}")
                mActivityFilter.remove(oldName)
                mActivityFilter.add(newName)
            }
            if (mReceiverFilter != null && mReceiverFilter.contains(oldName)) {
                LogUtils.i("\t\t修改要过滤的Receiver: ${oldName} => ${newName}")
                mReceiverFilter.remove(oldName)
                mReceiverFilter.add(newName)
            }
            if (mDeleteClassList != null && mDeleteClassList.contains(oldName)) {
                LogUtils.i("\t\t修改要删除的类: ${oldName} => ${newName}")
                mDeleteClassList.remove(oldName)
                mDeleteClassList.add(newName)
            }
        }
    }

    private void renameClass(String oldName, String newName) {
        LogUtils.d("重命名${oldName} => $newName")
        CtClass oldClass = pool.get(oldName)
        if (oldClass.nestedClasses != null) {
            for (CtClass nest : oldClass.nestedClasses) {
                String nestName = nest.name
                String newNestName = nestName.replace(oldName, newName)
                nameMap.put(nestName, newNestName)
                renameClass(nestName, newNestName)
            }
        }
        CtClass newClass = pool.getAndRename(oldName, newName)
        if (oldClass.URL.protocol == "jar") {
            String oldClassPath = oldClass.URL.toString()
                    .replaceFirst("jar:", "")
                    .replaceFirst("file:", "")
                    .trim()
            String jarFilePath = oldClassPath.replace(".class", "")
                    .replace(oldClass.name.replace(".", File.separator), "")
            if (jarFilePath.endsWith("!/")) {
                jarFilePath = jarFilePath.substring(0, jarFilePath.length() - 2)
            }
            File jarFile = new File(jarFilePath)
            if (!jarFile.exists()) {
                throw new IllegalStateException("原始Jar文件 ${jarFile} (${oldClass.URL})不存在")
            }
            // 保存新类到jar, 并删除旧类
            List<String> oldEntryList = new ArrayList<>()
            oldEntryList.add(oldClass.name.replace(".", File.separator) + ".class")
            String newClassEntry = newClass.name.replace(".", File.separator) + ".class"
            LogUtils.w("\t创建新类${newClass.name}到${jarFile}并移除${oldEntryList}")
            JarFileUtils.replaceFileAndDeleteOldFile(jarFile.absolutePath, newClassEntry, newClass.toBytecode(), oldEntryList)
            ClassPath classPath = classPathMap.get(jarFile.absolutePath)
            if (classPath == null) {
                throw new IllegalStateException("找不到${jarFile}对应的classPath实例")
            }
            // jar包要添加一次, 否则会找不到新类
            pool.appendClassPath(jarFile.absolutePath)
            pool.removeClassPath(classPath)
            // 避免Jar句柄问题
            JarFileUtils.clearJarFactoryCache(jarFile.absolutePath)
        } else if (oldClass.URL.protocol == "file") {
            // 保存新类的目录, 并删除旧类
            String oldClassPath = oldClass.URL.path
            String dir = oldClassPath.replace(".class", "").replace(oldClass.name.replace(".", File.separator), "")
            LogUtils.w("\t创建新类${newClass.name}到$dir")
            newClass.writeFile(dir)
            // 删除旧类
            File oldClassFile = new File(oldClassPath)
            if (oldClassFile.exists()) {
                LogUtils.w("\t删除旧类: $oldClassFile")
                oldClassFile.delete()
            } else {
                throw new IllegalStateException("原始类文件 ${originalFile} 不存在")
            }
        } else {
            throw new IllegalStateException("暂不支持处理${oldClass.URL.protocol}类型, ${oldClass.URL}")
        }
        // 移除出pool, 重新从文件加载
        oldClass.detach()
        newClass.detach()
    }


    /**
     * 返回true代表需要删除
     * @param classBean
     * @return
     */
    boolean deleteFileFilter(CtClassBean classBean) {
        String className = classBean.ctClass.name
        //xxx.*
        for (String scheme : mDeleteClassList) {
            if (scheme.endsWith("*")) {
                String newScheme = scheme.replace("*", "")
                if (className.startsWith(newScheme)) {
                    return true
                }
            } else if (scheme.equals(className)) {
                return true
            }
        }
        return false
    }

    /**
     * 1. 处理组件的继承关系
     * 2. 类是否引用了替换名称的类, 是的话替换
     * @param ctClassBean
     * @param nameMap
     */
    void handleClass(CtClassBean ctClassBean) {
        //处理自身包名替换
        CtClass ctClass = ctClassBean.ctClass
        if (fileLocalJar(ctClass.URL)) {
            LogUtils.v("忽略被过滤的Jar中的类: ${ctClass.name} (${ctClass.URL})")
            return
        }
        if (ctClass.isFrozen()) {
            ctClass.defrost()
        }

        // 修改Application继承关系
        if (mApplicationName == ctClass.name) {
            mAppHandler.handle(pool, ctClass)
        }

        // 处理组件
        handleManifestComponent(ctClass)

        // 修改重命名过的类的索引
        for (String key : nameMap.keySet()) {
            if (ctClass.getRefClasses() != null && ctClass.getRefClasses().contains(key)) {
                LogUtils.d("重命名${ctClass.name}中的$key => ${nameMap.get(key)}")
                ctClass.replaceClassName(key, nameMap.get(key))
            }
        }

        //如果修改过则需要保存
        if (ctClass.isModified()) {
            ctClassBean.saveFile()
        } else {
            ctClass.detach()
        }
    }

    /**
     * 处理Manifest中声明的组件
     * @param ctClass
     */
    void handleManifestComponent(CtClass ctClass) {
        String name = ctClass.name
        if (nameMap.values().contains(name)) {
            // 类被重命名了, 所以要替换下
            for (Map.Entry<String, String> entry : nameMap.entrySet()) {
                if (entry.value == name) {
                    name = entry.key
                    LogUtils.w("处理Manifest组件时转换回重命名前的名称: ${ctClass.name} => $name")
                }
            }
        }
        // 在manifest中有声明, 没有被配置文件过滤, 那么就修改Activity的继承关系
        if (manifestHelper.mActivities.contains(name)) {
            if (!mActivityFilter.contains(name)) {
                mActivityHandler.handle(pool, ctClass)
            } else {
                LogUtils.w("忽略Activity: ${name} ($ctClass.URL)")
            }
        }

        if (manifestHelper.mReceivers.contains(name)) {
            if (!mReceiverFilter.contains(name)) {
//            mReceiverHandler.handle(pool, ctClass)
            } else {
                LogUtils.w("忽略Receiver: ${name} ($ctClass.URL)")
            }
        }
    }

    boolean fileLocalJar(String path) {
        for (String jarName : mFilterLocalJar) {
            if (path.endsWith(jarName)) {
                return true
            } else if ((path.endsWith("classes.jar") || path.endsWith("runtime.jar")) && path.contains(jarName)) {
                return true
            }
        }
        return false
    }

    boolean fileLocalJar(URL url) {
        for (String jarName : mFilterLocalJar) {
            if (url.path.contains(jarName)) {
                return true;
            }
        }
        return false
    }
}
