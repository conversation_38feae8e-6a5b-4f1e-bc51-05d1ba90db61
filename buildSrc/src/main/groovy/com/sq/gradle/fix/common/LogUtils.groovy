package com.sq.gradle.fix.common

//TODO 补齐日志级别
class LogUtils {

    static boolean VERBOSE = false

    private LogUtils() {

    }

    static void e(String msg) {
        log('red', msg)
    }

    static void w(String msg) {
        log('yellow', msg)
    }

    static void i(String msg) {
        log('green', msg)
    }

    static void d(String msg) {
        log('blue', msg)
    }

    static void v(String msg) {
        if (VERBOSE) {
            log('white', msg)
        }
    }

    static void log(String color, String msg) {
        def styler = 'black red green yellow blue magenta cyan white'
                .split().toList().withIndex(30)
                .collectEntries { key, val -> [(key): { "\033[${val}m${it}\033[0m" }] }
        println "[Log]${styler[color](msg)}"
    }
}
