package com.sq.gradle.fix.handler

import com.sq.gradle.fix.common.LogUtils
import javassist.ClassPool
import javassist.CtClass

/**
 * 处理Receiver代理
 */
class ReceiverHandler extends BaseHandler {

    @Override
    boolean handle(ClassPool pool, CtClass ctClass) {
        try {
            CtClass baseBroadcastReceiver = pool.get("com.plugin.standard.BaseBroadcastReceiver")
            ctClass.setSuperclass(baseBroadcastReceiver)
            LogUtils.d("处理Receiver: ${ctClass.name} ($ctClass.URL)")
        } catch (Exception e) {
            LogUtils.e("处理Receiver异常: ${ctClass.name} ($ctClass.URL)\n\t${e}")
            return false
        }
        return true
    }

}
