package com.sq.gradle.fix.handler

import com.sq.gradle.fix.common.LogUtils
import javassist.ClassPool
import javassist.CtClass

/**
 * 处理Activity代理
 */
class ActivityHandler extends BaseHandler {

    @Override
    boolean handle(ClassPool pool, CtClass ctClass) {
        try {
            CtClass baseActivity = pool.get("com.plugin.standard.BaseActivity")
            ctClass.setSuperclass(baseActivity)
            ctClass.replaceClassName("com.plugin.standard.RealBaseActivity", "com.plugin.standard.BaseActivity")
            LogUtils.d("处理Activity: ${ctClass.name} ($ctClass.URL)")
        } catch (Exception e) {
            LogUtils.e("处理Activity异常: ${ctClass.name} ($ctClass.URL)\n\t${e}")
            return false
        }
        return true
    }

}
