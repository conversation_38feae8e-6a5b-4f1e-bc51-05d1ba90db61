package com.sq.gradle.fix.config

import com.google.gson.Gson
import com.sq.gradle.fix.common.FileUtil
import com.sq.gradle.fix.common.LogUtils

/**
 *
 * <AUTHOR> @date 2020-07-23
 */
class ConfigBean {

    //名称映射，打插件时替换类名
    Map<String, String> nameMap

    //过滤掉class，不打到插件中
    Set<String> deleteClassList

    //过滤掉本地jar包，不打到插件中
    Set<String> filterLocalJar

    //activity过滤器
    Set<String> activityFilter

    //receiver过滤器
    Set<String> receiverFilter

    String applicationName

    String baseConfigUrl

    String configPath

    /**
     * 供媒体模块配置新增特定配置
     * @param bean
     * @return
     */
    ConfigBean combine(ConfigBean bean) {
        if (bean != null) {
            combineNameMap(bean.nameMap)
            //删除类和jar包的不合并，因为不同场景下使用不同
//            combineSetField(deleteClassList, bean.deleteClassList)
//            combineSetField(filterLocalJar, bean.filterLocalJar)
            combineSetField(activityFilter, bean.activityFilter)
            combineSetField(receiverFilter, bean.receiverFilter)
        }
    }

    /**
     * 合并名称映射配置
     * @param otherNameMap
     */
    private void combineNameMap(Map<String, String> otherNameMap) {
        if (nameMap == null) {
            nameMap = otherNameMap
        } else if (otherNameMap != null) {
            nameMap.putAll(otherNameMap)
        }
    }

    private void combineSetField(Set<String> selfField, Set<String> otherFiled) {
        if (selfField == null) {
            selfField = otherFiled
        } else if (otherFiled != null) {
            selfField.addAll(otherFiled)
        }
    }

    ConfigBean() {

    }

    void refresh(RefreshCallback callback) {
        if (baseConfigUrl == null || baseConfigUrl.equals("")) {
            LogUtils.w("baseConfigUrl is not set, don't need handle combine")
            callback.onComplete()
        } else {
            try {
                URL url = new URL(baseConfigUrl)
                HttpURLConnection connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("GET")
                connection.connect()
                int responseCode = connection.getResponseCode()
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    InputStream inputStream = connection.getInputStream()
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))
                    String jsonContent = ""
                    String line = ""
                    while ((line = reader.readLine()) != null) {
                        jsonContent += line
                    }
                    reader.close()
                    connection.disconnect()
                    LogUtils.w("baseConfig is download success, content is $jsonContent")
                    Gson gson = new Gson()
                    ConfigBean configBean = gson.fromJson(jsonContent, ConfigBean.class)
                    combine(configBean)
                    LogUtils.w("baseConfig content combine success")
                    callback.onComplete()
                } else {
                    LogUtils.e("baseConfigUrl is net error, url is $baseConfigUrl")
                    callback.onComplete()
                }
            } catch (Exception e) {
                e.printStackTrace()
            }
        }
    }

    static ConfigBean parse(String jsonPath) {
        String jsonContent = FileUtil.read(jsonPath)
        Gson gson = new Gson()
        return gson.fromJson(jsonContent, ConfigBean.class)
    }

    @Override
    String toString() {
        Gson gson = new Gson()
        return gson.toJson(this)
    }

    interface RefreshCallback {
        void onComplete()
    }
}
