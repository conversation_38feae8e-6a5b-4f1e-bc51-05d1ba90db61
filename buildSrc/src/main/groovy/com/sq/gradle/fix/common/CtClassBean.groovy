package com.sq.gradle.fix.common

import javassist.CtClass
import javassist.NotFoundException

class CtClassBean {

    static final int TYPE_JAR_FILE = 0

    static final int TYPE_CLASS_FILE = 1

    /**
     * CtClass类
     */
    CtClass ctClass

    /**
     * 路径
     */
    String path

    /**
     * 类型，是jar里解析的file，还是文件夹中的
     */
    int type

    /**
     * CtClass初始名称
     */
    String originalName

    /**
     * 内部类
     */
    Set<String> nestClassNames

    /**
     *
     * @param ctClass
     * @param path
     * @param type
     */
    CtClassBean(CtClass ctClass, String path, int type) {
        this.ctClass = ctClass
        this.originalName = ctClass.name
        this.path = path
        this.type = type
        try {
            CtClass[] nestClasses = this.ctClass.nestedClasses
            if (nestClasses != null && nestClasses.size() > 0) {
                nestClassNames = new HashSet<>()
                nestClasses.each {
                    nestClassNames.add(it.name)
                }
            }
        } catch (NotFoundException e) {
            // 有些jar包内部类有问题, 所以catch掉
            LogUtils.e("$path 中的类: ${ctClass.name}缺少内部类定义: ${e}")
        }
    }

    /*
     * 保存CtClass文件
     */
    void saveFile() {
        if (type == TYPE_CLASS_FILE) {
            if (ctClass != null) {
                if (originalName != ctClass.name) {
                    // 重命名了
                    throw new UnsupportedOperationException("不支持重命名, 独立处理重命名问题")
                }
                ctClass.writeFile(path)
                ctClass.detach()
            }
        } else {
            //是jar里面的
            if (ctClass != null) {
                if (originalName != ctClass.name) {
                    throw new UnsupportedOperationException("不支持重命名, 独立处理重命名问题")
                } else {
                    String entryName = ctClass.name.replace(".", File.separator) + ".class"
                    JarFileUtils.replaceFile(path, entryName, ctClass.toBytecode())
                }
                ctClass.detach()
            }
        }
    }

    void deleteFile() {
        if (type == TYPE_CLASS_FILE) {
            if (ctClass != null) {
                String classFilePath = path + File.separator + (ctClass.name.replace(".", File.separator)) + ".class"
                File classFile = new File(classFilePath)
                if (classFile.exists()) {
                    classFile.delete()
                }
                if (nestClassNames != null) {
                    nestClassNames.each {
                        String nestClassFilePath = path + File.separator + (it.replace(".", File.separator)) + ".class"
                        File nestClassFile = new File(nestClassFilePath)
                        if (nestClassFile.exists()) {
                            nestClassFile.delete()
                        }
                    }
                }
            }
        } else {
            if (ctClass != null) {
                ArrayList<String> entryNameList = new ArrayList<>()
                String entryName = ctClass.name.replace(".", File.separator) + ".class"
                entryNameList.add(entryName)
                if (nestClassNames != null) {
                    nestClassNames.each {
                        String nestClassEntryName = it.replace(".", File.separator) + ".class"
                        entryNameList.add(nestClassEntryName)
                    }
                }
                JarFileUtils.deleteEntry(path, entryNameList)
            }
        }
    }
}
