<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="FullScreenDialogStyle" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>


    <style name="protocol_activity_dialog" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>
    <style name="protocol_activity" parent="@android:style/Theme.Light.NoTitleBar.Fullscreen">

    </style>
</resources>