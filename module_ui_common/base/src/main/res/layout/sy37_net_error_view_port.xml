<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_centerInParent="true">
        <ProgressBar
            android:id="@+id/sy37_m_net_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:visibility="gone"
            android:indeterminateDuration="1000" />
        <ImageView
            android:id="@+id/iv_error_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            />
        <TextView
            android:id="@+id/sy37_m_net_error_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/net_error_title"
            android:layout_gravity="center"
            android:textSize="26dp"
            android:textColor="#222222"
            android:layout_marginTop="60dp"
            android:textStyle="bold"/>
        <TextView
            android:id="@+id/sy37_m_net_error_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/net_error_tips"
            android:layout_gravity="center"
            android:textSize="18dp"
            android:textColor="#222222"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"/>
    </LinearLayout>

</RelativeLayout>