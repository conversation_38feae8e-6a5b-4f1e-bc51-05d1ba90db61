<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:clickable="true"
          android:focusable="true"
          android:gravity="bottom"
          android:id="@+id/ll_web_dialog_view"
          android:orientation="vertical"
          android:weightSum="100">
            <View
              android:id="@+id/v_web_view_dialog_notch"
              android:layout_width="60dp"
              android:layout_height="match_parent"
              android:background="#171717"
              android:visibility="gone" />

            <View
              android:id="@+id/v_status_bar_place"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:visibility="gone" />
            <FrameLayout
              android:id="@+id/fl_web_view_dialog_content"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

                <com.sqwan.common.webview.SQWebView
                  android:id="@+id/wb_web_view_dialog_webview"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:background="@android:color/transparent" />

                <RelativeLayout
                  android:id="@+id/rl_web_view_dialog_loading_layout"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:visibility="gone"
                  tools:visibility="visible">

                    <ImageView
                      android:id="@+id/iv_web_view_dialog_loading_icon"
                      android:layout_width="66dp"
                      android:layout_height="66dp"
                      android:layout_centerInParent="true" />

                    <TextView
                      android:layout_width="wrap_content"
                      android:layout_height="wrap_content"
                      android:layout_below="@+id/iv_web_view_dialog_loading_icon"
                      android:layout_centerHorizontal="true"
                      android:layout_marginTop="10dp"
                      android:text="@string/sysq_loading_txt"
                      android:textColor="#666666"
                      android:textSize="12dp"/>
                </RelativeLayout>

                <com.sy37sdk.views.NetErrorView
                  android:id="@+id/nev_web_view_dialog_loading_error"
                  android:layout_width="match_parent"
                  android:layout_height="match_parent"
                  android:background="#ffffff"
                  android:visibility="gone" />

            </FrameLayout>
        </LinearLayout>

        <FrameLayout
          android:id="@+id/fl_video_container"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:visibility="gone"/>

    </FrameLayout>

    <com.sqwan.common.web.WebViewToolBar
        android:id="@+id/wvtb_web_view_dialog_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"/>
</LinearLayout>