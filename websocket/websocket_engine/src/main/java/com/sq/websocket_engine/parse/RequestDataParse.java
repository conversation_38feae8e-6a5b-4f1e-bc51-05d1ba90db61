package com.sq.websocket_engine.parse;


import static com.sq.websocket_engine.RequestQueueHandler.RequestPriority.DEFAULT;

import com.sq.websocket_engine.BodyData;
import com.sq.websocket_engine.MsgBaseReq;
import com.sq.websocket_engine.RequestQueueHandler;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/8 16:30
 */
public class RequestDataParse{
    public final  static short version = 100;
    private static final String TAG = "RequestDataParse";
    private static AtomicInteger globalSeq = new AtomicInteger(0);
    public int op;
    public int seq;
    public BodyData body;
    public long sendTime;
    public int timeout=10*1000;
    public RequestQueueHandler.RequestPriority requestPriority = DEFAULT;
    public RequestQueueHandler.IRequestCallback<ResponseDataParse> requestCallback;
    public RequestDataParse(MsgBaseReq msgBaseReq) {
        this.requestPriority = msgBaseReq.requestPriority;
        this.timeout = msgBaseReq.timeout;
        this.seq = globalSeq.incrementAndGet();
        this.op = msgBaseReq.getOp();
        this.body = new BodyData().getReqBodyData(msgBaseReq);
    }

    public byte[] pack() {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(bos);
        String jsonString = body.toJson();
        short headerlen = 16;
        int packlen = jsonString.length() + headerlen;
        short version = RequestDataParse.version;
        try {
            dos.writeInt(packlen);
            dos.writeShort(headerlen);
            dos.writeShort(version);
            dos.writeInt(op);
            dos.writeInt(seq);
            dos.writeBytes(jsonString);
            dos.close();
            StringBuilder sb = new StringBuilder();
            byte[] result = bos.toByteArray();
            for (byte b : result) {
                sb.append(Integer.toBinaryString(b & 0xFF));
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    @Override
    public String toString() {
        return "RequestDataParse{" +
                "op=" + op +
                ", seq=" + seq +
                ", body=" + body +
                ", sendTime=" + sendTime +
                ", timeout=" + timeout +
                ", requestPriority=" + requestPriority +
                ", requestCallback=" + requestCallback +
                '}';
    }
}
