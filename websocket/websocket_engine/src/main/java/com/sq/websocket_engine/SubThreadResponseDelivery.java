package com.sq.websocket_engine;

import android.os.Handler;
import android.os.HandlerThread;

import com.sq.libwebsocket.dispatcher.MainThreadResponseDelivery;


/**
 * Created by znb on 2021/11/29
 */
public class SubThreadResponseDelivery extends MainThreadResponseDelivery {
    private Handler handler;
    public SubThreadResponseDelivery(){
        HandlerThread handlerThread = new HandlerThread("SubThreadResponseDelivery");
        handlerThread.start();
        handler = new Handler(handlerThread.getLooper());
    }
    protected void runThread(Runnable runnable){
        handler.post(runnable);
    }

    @Override
    public void destroy() {
        if (RUNNABLE_POOL!=null) {
            for (CallbackRunnable callbackRunnable : RUNNABLE_POOL) {
                handler.removeCallbacks(callbackRunnable);
            }
        }
    }
}
