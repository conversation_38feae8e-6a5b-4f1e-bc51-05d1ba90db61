package com.sq.websocket_engine;


import com.google.sqgson.Gson;
import com.google.sqgson.internal.$Gson$Types;
import com.sq.websocket_engine.parse.ResponseDataParse;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/24 12:25
 */
public abstract class ARecInfMsg<T> {
    protected String TAG = this.getClass().getSimpleName();
    public ResponseDataParse responseDataParse;
    protected T inf;
    public abstract T getInf();
    public void init(ResponseDataParse responseDataParse){
        this.responseDataParse = responseDataParse;
        inf = handle();
    }
    public T handle(){
        if (!filter()) {
            String msg = responseDataParse.body.msg;
            Type mType = getSuperclassTypeParameter(getClass());
            return new Gson().fromJson(msg,mType);
        }
        return null;
    }
    public abstract boolean filter();
    public Type getSuperclassTypeParameter(Class<?> subclass) {
        Type superClass = subclass.getGenericSuperclass();
        if (superClass instanceof Class) {
            throw new RuntimeException("Missing type parameter.");
        }
        ParameterizedType parameterized = (ParameterizedType) superClass;
        return $Gson$Types
                .canonicalize(parameterized.getActualTypeArguments()[0]);
    }
}
