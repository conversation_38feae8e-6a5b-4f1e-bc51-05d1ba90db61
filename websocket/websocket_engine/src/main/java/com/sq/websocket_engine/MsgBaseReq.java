package com.sq.websocket_engine;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/11 18:34
 */
public  class MsgBaseReq {
    public int timeout=10*1000;
    public RequestQueueHandler.RequestPriority requestPriority = RequestQueueHandler.RequestPriority.DEFAULT;
    private int op;
    private String ev;
    private String appid="platform";
    public  int getOp(){
     return op;
    }
    public  String getEv(){
     return ev;
    }

    public void setOp(int op) {
        this.op = op;
    }

    public void setEv(String ev) {
        this.ev = ev;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }
}
