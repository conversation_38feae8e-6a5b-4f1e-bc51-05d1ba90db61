package com.sq.websocket_engine;

import com.sq.websocket_engine.parse.ResponseDataParse;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.TaskSubThread;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/12/20 14:58
 */
public class WebSocketEngine implements WebSocketCenter.WebSocketCenterListener,RequestQueueHandler.IReciveInf{
    private static final String TAG = "WebSocketEngine";

    private static final WebSocketEngine ourInstance = new WebSocketEngine();
    public static WebSocketEngine getInstance() {
        return ourInstance;
    }

    private final int authReqErrorTimeMax = 5;
    private static AtomicInteger authReqErrorTime = new AtomicInteger(0);
    private TaskSubThread heartBeatTask = TaskSubThread.create();
    //默认心跳间隔
    private long heartBeatDuration = 15 * 1000;

    private BaseRequestManager baseRequestManager;

    private List<ARecInfMsgBaseFactory> aRecInfMsgBaseFactories = new ArrayList<>();

    private List<WebSocketEngineCallback> webSocketEngineCallbacks = new ArrayList<>();

    private WebSocketEngine() {
        baseRequestManager = new BaseRequestManager();
    }
    @Override
    public void onRecive(ResponseDataParse responseDataParse) {
        for (ARecInfMsgBaseFactory aRecInfMsgBaseFactory : aRecInfMsgBaseFactories) {
            if (aRecInfMsgBaseFactory!=null) {
                Object obj = aRecInfMsgBaseFactory.convert(responseDataParse);
                if (obj!=null && obj instanceof ARecInfMsg) {
                    ARecInfMsg aRecInfMsg = (ARecInfMsg) obj;
                    Object inf = ((ARecInfMsg<?>) aRecInfMsg).inf;
                    if (inf!=null) {
                        for (WebSocketEngineCallback webSocketEngineCallback : webSocketEngineCallbacks) {
                            if (webSocketEngineCallback!=null) {
                                webSocketEngineCallback.onReceiveInf(aRecInfMsg);
                            }
                        }
                    }
                }
            }
        }
    }

    public interface WebSocketEngineCallback{
        void onAuth();
        void onReceiveInf(ARecInfMsg inf);
    }
    public class WebSocketEngineCallbackAdapter implements WebSocketEngineCallback{

        @Override
        public void onAuth() {

        }

        @Override
        public void onReceiveInf(ARecInfMsg inf) {

        }
    }

    public void registerWebSocketEngineCallback(WebSocketEngineCallback webSocketEngineCallback){
        webSocketEngineCallbacks.add(webSocketEngineCallback);
    }

    public void unregisterWebSocketEngineCallback(WebSocketEngineCallback webSocketEngineCallback){
        webSocketEngineCallbacks.remove(webSocketEngineCallback);
    }
    //开启websocket链接
    public void open(){
        LogUtil.i(TAG,"open start");
        WebSocketCenter.getInstance().initWebSocket(this);
        authReqErrorTime.set(0);
        RequestQueueHandler.getInstance().registerReciveInf(this);
        LogUtil.i(TAG,"open end");
    }
    //关闭websocket链接
    public void close(){
        LogUtil.i(TAG,"close start");
        webSocketEngineCallbacks.clear();
        heartBeatTask.stop();
        RequestQueueHandler.getInstance().unRegisterReciveInf(this);
        RequestQueueHandler.getInstance().release();
        WebSocketCenter.getInstance().destroy();
        LogUtil.i(TAG,"close end");
    }

    @Override
    public void onNetworkChange(boolean isConnected) {
        LogUtil.i(TAG,"onNetworkChange isConnected:"+isConnected);
        if (!isConnected) {
            heartBeatTask.stop();
        }else{
            authReqErrorTime.set(0);
        }
    }

    @Override
    public void onConnected() {
        LogUtil.i(TAG,"onConnected");
        if (authReqErrorTime.get()==authReqErrorTimeMax) {
            LogUtil.i(TAG,"authReqErrorTimeMax");
            return;
        }
        LogUtil.i(TAG,"reqAuth");
        baseRequestManager.reqAuth(new ReqWrapperHandler.FinishListener<BodyData>() {
            @Override
            public void on(BodyData result) {
                if (result!=null) {
                    LogUtil.i(TAG,"reqAuth true");
                    if (result.hb_time!=0) {
                        heartBeatDuration = result.hb_time * 1000L;
                    }
                    authReqErrorTime.set(0);
                    heartBeatTask.repeat(heartBeatDuration, new TaskSubThread.TaskFunc() {
                        @Override
                        public TaskSubThread.Result exec() {
                            baseRequestManager.reqHeartBeat(new ReqWrapperHandler.FinishListener<Boolean>() {
                                @Override
                                public void on(Boolean result) {
                                    LogUtil.i(TAG,"reqHeartBeat result:"+result);
                                }
                            });
                            return null;
                        }
                    });
                    for (WebSocketEngineCallback webSocketEngineCallback : webSocketEngineCallbacks) {
                        if (webSocketEngineCallback!=null) {
                            webSocketEngineCallback.onAuth();
                        }
                    }
                }else{
                    //容错处理
                    authReqErrorTime.incrementAndGet();
                }
            }
        });
    }

    public void addARecInfMsgBaseFactory(ARecInfMsgBaseFactory aRecInfMsgBaseFactory){
        aRecInfMsgBaseFactories.add(aRecInfMsgBaseFactory);
    }
    public void removeARecInfMsgBaseFactory(ARecInfMsgBaseFactory aRecInfMsgBaseFactory){
        aRecInfMsgBaseFactories.remove(aRecInfMsgBaseFactory);
    }
}
