package com.sq.websocket_engine;


import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.common.util.CheckNetwork;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;
import com.sq.libwebsocket.SimpleListener;
import com.sq.libwebsocket.SocketListener;
import com.sq.libwebsocket.WebSocketHandler;
import com.sq.libwebsocket.WebSocketManager;
import com.sq.libwebsocket.WebSocketSetting;
import com.sq.libwebsocket.response.ErrorResponse;
import java.nio.ByteBuffer;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/18 18:22
 */
public class WebSocketCenter {
    private static final String TAG = "WebSocketCenter";

    private static final WebSocketCenter ourInstance = new WebSocketCenter();

    public static WebSocketCenter getInstance() {
        return ourInstance;
    }

    private WebSocketCenter() {
    }


    private static final boolean isTest = false;

    //test
    private static final String websocket_url_test = "ws://push-comet-test.37.com.cn/sub";

    public static final String KEY_WEBSOCKET = "websocket";
    @UrlUpdate(KEY_WEBSOCKET)
    private static  String websocket_url_release = "ws://push-comet.37.com.cn/sub";


    private static  String websocket_url = websocket_url_test;

    static {
        if (isTest) {
            websocket_url = websocket_url_test;
        }else{
            websocket_url = websocket_url_release;
        }
    }
    private WebSocketManager manager;

    public interface WebSocketCenterListener {
        void onNetworkChange(boolean isConnected);

        void onConnected();
    }

    private WebSocketCenterListener webSocketCenterListener;

    private volatile boolean isLocalConnected = true;

    public boolean isLocalConnected() {
        return isLocalConnected;
    }

    public void setLocalConnected(boolean localConnected) {
        isLocalConnected = localConnected;
    }
    private WebSocketSetting initWebSocketSetting(){
        WebSocketSetting setting = new WebSocketSetting();
        setting.setConnectUrl(websocket_url);//必填
        //设置连接超时时间
        setting.setConnectTimeout(15 * 1000);
        //设置心跳间隔时间
        setting.setConnectionLostTimeout(30);
        //设置断开后的重连次数，可以设置的很大，不会有什么性能上的影响
        setting.setReconnectFrequency(5);
//        //设置Header
//        setting.setHttpHeaders(header);
        //设置消息分发器，接收到数据后先进入该类中处理，处理完再发送到下游
        setting.setResponseProcessDispatcher(new AppResponseDispatcher());
        //接收到数据后是否放入子线程处理，只有设置了 ResponseProcessDispatcher 才有意义
        setting.setProcessDataOnBackground(true);

        setting.setResponseDelivery(new SubThreadResponseDelivery());
        return setting;
    }
    public void initWebSocket(final WebSocketCenterListener webSocketCenterListener) {
        if (manager == null) {
            this.webSocketCenterListener = webSocketCenterListener;
            CheckNetwork.getInstance().checkNetworkListener = new CheckNetwork.CheckNetworkListener() {
                @Override
                public void onNetworkChange(boolean isConnected) {
                    LogUtil.i(TAG, "onNetworkChange isConnected:" + isConnected);
                    if (webSocketCenterListener != null) {
                        webSocketCenterListener.onNetworkChange(isConnected);
                    }
                    RequestQueueHandler.getInstance().setLocalConnected(isConnected);
                    setLocalConnected(isConnected);
                    if (WebSocketHandler.getDefault() != null) {
                        if (WebSocketHandler.getDefault().getSetting().reconnectWithNetworkChanged()) {
                            WebSocketHandler.getDefault().reconnect();
                        }
                    }
                }
            };

            //通过 init 方法初始化默认的 WebSocketManager 对象
            manager = WebSocketHandler.init(initWebSocketSetting());
            WebSocketHandler.getDefault().addListener(socketListener);
            //启动连接
            Task.postDelay(50, new Runnable() {
                @Override
                public void run() {
                    manager.start();
                    CheckNetwork.getInstance().register();
                }
            });

        }
    }

    private SocketListener socketListener = new SimpleListener() {
        @Override
        public void onConnected() {
            logMsg("onConnected");
            if (webSocketCenterListener != null) {
                webSocketCenterListener.onConnected();
            }
        }

        @Override
        public void onConnectFailed(Throwable e) {
            if (e != null) {
                logMsg("onConnectFailed:" + e.toString());
            } else {
                logMsg("onConnectFailed:null");
            }
        }

        @Override
        public void onDisconnect() {
            logMsg("onDisconnect");
        }

        @Override
        public void onSendDataError(ErrorResponse errorResponse) {
            logMsg(errorResponse.getDescription());
            errorResponse.release();
        }

        @Override
        public <T> void onMessage(ByteBuffer bytes, T data) {
            LogUtil.i(TAG,"onMessage");
            RequestQueueHandler.getInstance().receiveMessageData(bytes);
        }
    };

    private void logMsg(String msg) {
        LogUtil.i(TAG, "logMsg:" + msg);
    }


    public void destroy() {
        if (manager != null) {
            CheckNetwork.getInstance().unregister();
            manager.removeListener(socketListener);
            manager.destroy();
            WebSocketHandler.uninit();
            manager = null;
        }
    }

    public WebSocketManager getManager() {
        return manager;
    }
}
