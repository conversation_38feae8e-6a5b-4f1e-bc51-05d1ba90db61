package com.sq.websocket_engine;


import com.google.sqgson.ExclusionStrategy;
import com.google.sqgson.FieldAttributes;
import com.google.sqgson.Gson;
import com.google.sqgson.GsonBuilder;
import com.sqwan.common.mod.CommonConfigs;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MD5Util;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/11 17:10
 */
public class BodyData {
    private static final String TAG = "BodyData";
    public String msg;
    public String appid;
    public String ev;
    public long ts;
    public String sign;
    public String userid;
    public String session_id;
    public String pid;
    public String gid;
    public int opres;
    public int hb_time;
    public MsgBaseReq msgBaseReq;
    public BodyData getReqBodyData(MsgBaseReq msgBaseReq){
        this.msgBaseReq = msgBaseReq;
        this.ev = msgBaseReq.getEv();
        this.msg = getGson().toJson(msgBaseReq);
        this.appid = msgBaseReq.getAppid();
        sign();
        return this;
    }
    private void sign(){
        pid = CommonConfigs.getInstance().getSqAppConfig().getPartner();
        gid = CommonConfigs.getInstance().getSqAppConfig().getGameid();
        ts = ModHelper.getConfig().getCommonConfig().getCurrentTime();
        userid = CommonConfigs.getInstance().getUserId();
        session_id = CommonConfigs.getInstance().getSession_id();
        String appKey = CommonConfigs.getInstance().getAppKey();
        String key = appid+ev+pid+gid+ts+userid+session_id+msg+appKey;
        String sign = MD5Util.Md5(key).toLowerCase();
        LogUtil.i(TAG,"key:"+key);
        LogUtil.i(TAG,"sign:"+sign);
        this.sign = sign;
    }
    private Gson getGson(){
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.addSerializationExclusionStrategy(new ExclusionStrategy() {
            @Override
            public boolean shouldSkipField(FieldAttributes f) {
                //过滤掉字段名包含"id","address"的字段
                return f.getName().contains("msgBase") | f.getName().contains("opres");
            }

            @Override
            public boolean shouldSkipClass(Class<?> clazz) {
                //过滤掉 类名包含 Bean的类
                return false;
            }
        });
        gsonBuilder.addDeserializationExclusionStrategy(new ExclusionStrategy() {
            @Override
            public boolean shouldSkipField(FieldAttributes f) {
                return f.getName().contains("msgBase");
            }

            @Override
            public boolean shouldSkipClass(Class<?> clazz) {
                return false;
            }
        });
        return gsonBuilder.create();
    }
    public String toJson(){
        return getGson().toJson(this);
    }
    public BodyData fromJson(String body){
        return getGson().fromJson(body,this.getClass());
    }
    public boolean isSuccess(){
        return opres==1;
    }

    @Override
    public String toString() {
        return "BodyData{" +
                "msg='" + msg + '\'' +
                ", appid='" + appid + '\'' +
                ", ev='" + ev + '\'' +
                ", ts=" + ts +
                ", sign='" + sign + '\'' +
                ", userid='" + userid + '\'' +
                ", session_id='" + session_id + '\'' +
                ", pid='" + pid + '\'' +
                ", gid='" + gid + '\'' +
                ", opres=" + opres +
                ", hb_time=" + hb_time +
                ", msgBaseReq=" + msgBaseReq +
                '}';
    }
}
