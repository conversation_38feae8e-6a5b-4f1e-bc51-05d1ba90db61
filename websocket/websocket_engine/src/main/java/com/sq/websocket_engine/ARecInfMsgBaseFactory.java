package com.sq.websocket_engine;


import android.text.TextUtils;

import com.sq.websocket_engine.parse.ResponseDataParse;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/24 12:24
 */
public abstract class ARecInfMsgBaseFactory {
    public static final int inf_msg = 9;
    protected abstract ARecInfMsg convert(String ev);
    public Object convert(ResponseDataParse responseDataParse){
        if (responseDataParse.op==inf_msg) {
            ARecInfMsg aRecInfMsg = null;
            if (TextUtils.equals(getTargetAppid(),responseDataParse.body.appid)) {
                aRecInfMsg = convert(responseDataParse.body.ev);
            }
            if (aRecInfMsg!=null) {
                aRecInfMsg.init(responseDataParse);
                return aRecInfMsg;
            }
        }

        return null;
    }
    public abstract String getTargetAppid();

}
