package com.sq.websocket_engine;


import com.sq.websocket_engine.parse.ResponseDataParse;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/16 11:19
 */
public class ReqWrapperHandler {
    public abstract class ReqCallback<T> {
        private T rsp = null;

        public void setRsp(T rsp) {
            this.rsp = rsp;
        }

        public T getRsp() {
            return rsp;
        }

        public void onSuccess() {
            on(this.rsp);
        }

        abstract void on(T rsp);

        public void failed(ResponseDataParse msgBaseRsp) {

        }

    }
    public interface FinishListener<T> {
        void on(T result);
    }
    public interface SuccessListener<T> {
        void on(T rsp);
    }

    public interface FailedListener<T> {
        void on(T rsp);
    }

    public class FailedHolder<T> {
        FailedListener failedListener;

        public FailedHolder<T> onFailed(FailedListener<T> failedListener) {
            this.failedListener = failedListener;
            return this;
        }

        public FailedHolder<T> banTips() {
            return this.banTips(true);

        }

        public FailedHolder<T> banTips(Boolean isBan) {
            banFailedTips = isBan;
            return this;

        }

        boolean banFailedTips = false;
    }



    private <T> void req(MsgBaseReq req, final ReqCallback<T> reqCallback) {
        RequestQueueHandler.getInstance().sendMessage(req, new RequestQueueHandler.IRequestCallback<ResponseDataParse>() {
            @Override
            public void onSuccess(ResponseDataParse data) {
                reqCallback.setRsp((T) data);
                reqCallback.onSuccess();
            }

            @Override
            public void onFalid(ResponseDataParse data) {
                reqCallback.failed(data);
            }
        });

    }

    public   <Req extends MsgBaseReq, Rsp extends ResponseDataParse> FailedHolder<Rsp> reqImpl(Req req, final SuccessListener<Rsp> callback) {
        final FailedHolder failedHolder = new FailedHolder();
        req(req, new ReqCallback<Rsp>() {
            @Override
            public void on(Rsp rsp) {
                callback.on(rsp);
            }

            @Override
            public void failed(ResponseDataParse responseDataParse) {
                if (failedHolder.failedListener == null) {
                    if (!failedHolder.banFailedTips) {
                        super.failed(responseDataParse);
                        return;
                    }
                }

                failedHolder.failedListener.on(getRsp());
                if (!failedHolder.banFailedTips) {
                    super.failed(responseDataParse);
                    return;
                }
            }
        });
        return failedHolder;
    }


}
