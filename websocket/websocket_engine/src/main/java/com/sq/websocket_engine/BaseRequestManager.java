package com.sq.websocket_engine;

import android.text.TextUtils;

import com.sq.websocket_engine.parse.ResponseDataParse;
import com.sqwan.common.mod.CommonConfigs;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/12/20 15:09
 */
public class BaseRequestManager {
    private BaseReqService baseReqService;
    public BaseRequestManager(){
        baseReqService = new BaseReqService();

    }
    public void reqAuth(final ReqWrapperHandler.FinishListener<BodyData> finishListener){
        CommonConfigs.getInstance().setSession_id("");
        baseReqService.reqAuth(new AuthMsgReq(), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                String session_id = rsp.body.session_id;
                if (!TextUtils.isEmpty(session_id)) {
                    CommonConfigs.getInstance().setSession_id(session_id);
                }
                if (finishListener!=null) {
                    finishListener.on(rsp.body);
                }
            }
        }).onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(null);
                }
            }
        });
    }
    public void reqHeartBeat(final ReqWrapperHandler.FinishListener<Boolean> finishListener){
        baseReqService.reqHeartbeart(new HeartbeatMsgReq(), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(true);
                }
            }
        }).onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
            @Override
            public void on(ResponseDataParse rsp) {
                if (finishListener!=null) {
                    finishListener.on(false);
                }
            }
        });
    }
}
