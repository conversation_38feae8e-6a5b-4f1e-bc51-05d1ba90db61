package com.sq.websocket_engine.parse;

import com.sq.websocket_engine.BodyData;
import com.sqwan.common.util.LogUtil;

import java.nio.ByteBuffer;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/8 16:31
 */
public class ResponseDataParse {
    private static final String TAG = "ResponseDataParse";
    public int op;
    public int seq;
    public BodyData body;

    public ResponseDataParse parse(ByteBuffer data) {
        try {
            int size = data.capacity();
            LogUtil.i(TAG, "size:" + size);
            int packlen = data.getInt(0);
            short headlen = data.getShort(4);
            short version = data.getShort(6);
            if (version!= RequestDataParse.version) {
                return null;
            }
            int op = data.getInt(8);
            int seq = data.getInt(12);
            byte[] bytes = bytebuffer2ByteArray(data);
            byte[] byteBody = new byte[packlen - headlen];
            LogUtil.i(TAG, "byteBody size:" + byteBody.length);
            for (int i = 0; i < byteBody.length; i++) {
                byteBody[i] = bytes[i + headlen];
            }
            String body = new String(byteBody);

            BodyData bodyData = new BodyData().fromJson(body);
            this.op = op;
            this.seq = seq;
            this.body = bodyData;
            return this;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * byteBuffer 转 byte数组
     *
     * @param buffer
     * @return
     */
    public static byte[] bytebuffer2ByteArray(ByteBuffer buffer) {
        byte[] bytes = new byte[buffer.remaining()];
        buffer.get(bytes, 0, bytes.length);
        return bytes;
    }

    public boolean isSuccess() {
        if (body != null) {
            return body.isSuccess();
        }
        return false;
    }

    @Override
    public String toString() {
        return "ResponseDataParse{" +
                "op=" + op +
                ", seq=" + seq +
                ", body=" + body +
                '}';
    }
}
