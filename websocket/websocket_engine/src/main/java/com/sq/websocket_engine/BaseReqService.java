package com.sq.websocket_engine;

import com.sq.websocket_engine.parse.ResponseDataParse;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/12/20 12:15
 */
public class BaseReqService extends ReqWrapperHandler {
    public FailedHolder<ResponseDataParse> reqAuth(AuthMsgReq authMsgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(authMsgReq,successListener);
    }
    public FailedHolder<ResponseDataParse> reqHeartbeart(HeartbeatMsgReq hearbeatMsgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(hearbeatMsgReq,successListener);
    }
}
