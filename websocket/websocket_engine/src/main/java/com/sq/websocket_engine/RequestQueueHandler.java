package com.sq.websocket_engine;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;

import com.sq.websocket_engine.parse.RequestDataParse;
import com.sq.websocket_engine.parse.ResponseDataParse;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.task.Task;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/11/15 17:55
 */
public class RequestQueueHandler {
    private static final String TAG = "RequestQueueHandler";

    private int MSG_REQUEST_SEND = 0;
    private int MSG_REQUEST_RECEIVE = 1;

    public enum RequestPriority {
        DEFAULT,
        SUPER,
        HIGH,
        LOW
    }
    public interface IRequestCallback<T> {
        void onSuccess(T data);

        void onFalid(T data);
    }

    private static final RequestQueueHandler ourInstance = new RequestQueueHandler();
    public static RequestQueueHandler getInstance() {
        return ourInstance;
    }

    private RequestQueueHandler() {
        initHandler();
    }


    private final byte[] lock = new byte[0];
    private ConcurrentHashMap<RequestPriority, Vector<RequestDataParse>> requestQueueMap = new ConcurrentHashMap();
    private ConcurrentHashMap<Integer, RequestDataParse> requestSendingMap = new ConcurrentHashMap();
    private ConcurrentHashMap<Integer, Task> requestSendingTimerMap = new ConcurrentHashMap();
    private Handler handler;

    public interface IReciveInf {
        void onRecive(ResponseDataParse responseDataParse);
    }

    private final List<IReciveInf> iReciveInfs = new ArrayList<>();

    private volatile boolean isLocalConnected = false;

    public boolean isLocalConnected() {
        return isLocalConnected;
    }

    public void setLocalConnected(boolean localConnected) {
        isLocalConnected = localConnected;
    }

    public void registerReciveInf(IReciveInf iReciveInf) {
        LogUtil.i(TAG,"registerReciveInf");
        iReciveInfs.add(iReciveInf);
    }

    public void unRegisterReciveInf(IReciveInf iReciveInf) {
        LogUtil.i(TAG,"unRegisterReciveInf");
        iReciveInfs.remove(iReciveInf);
    }

    private void handleInf(ResponseDataParse responseDataParse) {
        LogUtil.i(TAG,"iReciveInfs size:"+iReciveInfs.size());
        for (IReciveInf iReciveInf : iReciveInfs) {
            if (iReciveInf != null) {
                if (responseDataParse==null) {
                    LogUtil.i(TAG,"responseDataParse==null return");
                    return;
                }
                if (responseDataParse.body==null) {
                    LogUtil.i(TAG,"responseDataParse.body==null return");
                    return;
                }
                iReciveInf.onRecive(responseDataParse);
            }
        }
    }

    private void initHandler() {
        HandlerThread handlerThread = new HandlerThread("Conn_Send_Thread");
        handlerThread.start();
        handler = new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                int what = msg.what;
                if (what == MSG_REQUEST_SEND) {
                    RequestDataParse requestDataParse = (RequestDataParse) msg.obj;
                    requestDataParse.sendTime = System.currentTimeMillis();
                    final int seq = requestDataParse.seq;
                    int timeout = requestDataParse.timeout;
                    Task task = Task.create();
                    task.oneShot(timeout, new Runnable() {
                        @Override
                        public void run() {
                            handleRequestTimeout(seq);
                        }
                    });
                    requestSendingTimerMap.put(seq, task);
                    if (WebSocketCenter.getInstance().getManager()!=null) {
                        WebSocketCenter.getInstance().getManager().send(requestDataParse.pack());
                    }

                } else if (what == MSG_REQUEST_RECEIVE) {
                    handleMessageData((ByteBuffer) msg.obj);
                }
            }
        };
    }

    private void handleRequestTimeout(int seq) {
        if (requestSendingTimerMap.get(seq) != null) {
            requestSendingTimerMap.remove(seq);
            if (!requestSendingMap.containsKey(seq)) {
                return;
            }
            RequestDataParse requestData = requestSendingMap.get(seq);
            requestSendingMap.remove(seq);
            if (requestData.requestCallback != null) {
                requestData.requestCallback.onFalid(null);
            }
        }

        // 尝试处理下一条命令
        trySendNextRequest();
    }

    private void trySendNextRequest() {
        // 判断socket是否已经连接
        // 正在请求的数量大于最大最求数
        RequestDataParse nextRequest = getNextRequest();
        if (nextRequest == null) {
            LogUtil.i(TAG, "trySendNextRequest nextRequest null");
            return;
        }
        requestSendingMap.put(nextRequest.seq, nextRequest);
        sendSocketMessage(nextRequest);
    }

    private void sendSocketMessage(RequestDataParse requestDataParse) {
        Message message = Message.obtain();
        message.what = MSG_REQUEST_SEND;
        message.obj = requestDataParse;
        handler.sendMessage(message);

    }

    private void addRequest(RequestDataParse requestDataParse) {
        RequestPriority priority = requestDataParse.requestPriority;
        Vector requestQueue = requestQueueMap.get(priority);
        if (requestQueue == null) {
            requestQueue = new Vector();
        }
        requestQueue.add(requestDataParse);
        requestQueueMap.put(priority, requestQueue);

    }

    private synchronized RequestDataParse getNextRequest() {
        RequestDataParse nextRequest = getNextRequestWithPriority(RequestPriority.SUPER);

        if (nextRequest == null) {
            nextRequest = getNextRequestWithPriority(RequestPriority.HIGH);
        }

        if (nextRequest == null) {
            nextRequest = getNextRequestWithPriority(RequestPriority.DEFAULT);
        }

        if (nextRequest == null) {
            nextRequest = getNextRequestWithPriority(RequestPriority.LOW);
        }

        return nextRequest;
    }

    private RequestDataParse getNextRequestWithPriority(RequestPriority priority) {
        Vector<RequestDataParse> queue = requestQueueMap.get(priority);
        if (queue == null) {
            queue = new Vector<>();
        }
        if (queue.size() == 0) {
            return null;
        } else {
            return (RequestDataParse) queue.remove(0);
        }

    }

    private void handleMessageData(ByteBuffer data) {
        LogUtil.i(TAG,"handleMessageData 1");
        ResponseDataParse responseDataParse = new ResponseDataParse().parse(data);
        if (responseDataParse!=null) {
            handleResponseData(responseDataParse);
        }
    }



    private void handleResponseData(ResponseDataParse responseDataParse) {
        LogUtil.i(TAG,"handleMessageData 2");
        int seq = responseDataParse.seq;
        int op = responseDataParse.op;
        String ev = responseDataParse.body.ev;
        boolean isSuccess = responseDataParse.isSuccess();
        LogUtil.i(TAG, "responseDataParse:"+responseDataParse.toString());
        RequestDataParse requestDataParse = requestSendingMap.get(seq);
        if (requestDataParse == null) {
            handleInf(responseDataParse);
            return;
        }
        this.requestSendingMap.remove(seq);
        if (requestDataParse.requestCallback != null) {
            try {
                if (responseDataParse.body.isSuccess()) {
                    LogUtil.e(TAG, "onSuccess");
                    LogUtil.e(TAG,"responseDataParse:"+responseDataParse);
                    requestDataParse.requestCallback.onSuccess(responseDataParse);
                } else {
                    LogUtil.e(TAG, "onFalid");
                    requestDataParse.requestCallback.onFalid(responseDataParse);

                }
            } catch (Exception e) {
                e.printStackTrace();
                LogUtil.e(TAG, "onFalid");
                requestDataParse.requestCallback.onFalid(responseDataParse);
            }

        }
        // 尝试处理下一条命令
        this.trySendNextRequest();

    }

    public void receiveMessageData(ByteBuffer data) {
        LogUtil.i(TAG, "receiveMessageData");
        Message message = Message.obtain();
        message.obj = data;
        message.what = MSG_REQUEST_RECEIVE;
        handler.sendMessage(message);
    }

    public void sendMessage(MsgBaseReq msgBase, IRequestCallback<ResponseDataParse> reqCallback) {
        synchronized (lock) {
            LogUtil.i(TAG, "sendMessage");
            RequestDataParse requestDataParse = new RequestDataParse(msgBase);
            int op = requestDataParse.op;
            LogUtil.i(TAG, "sendMessage:"+requestDataParse.toString());
            requestDataParse.requestCallback = reqCallback;
            if (!isLocalConnected()) {
                reqCallback.onFalid(new ResponseDataParse());
                return;
            }
            addRequest(requestDataParse);
            trySendNextRequest();
        }
    }
    public void release(){
        synchronized (lock){
            for (Task task : requestSendingTimerMap.values()) {
                task.stop();
            }
            requestSendingTimerMap.clear();
            for (Vector<RequestDataParse> value : requestQueueMap.values()) {
                value.clear();
            }
            requestQueueMap.clear();
            requestSendingMap.clear();
            iReciveInfs.clear();
        }
    }
}
