package com.sq.libwebsocket;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.sq.libwebsocket.request.Request;
import com.sq.libwebsocket.response.ErrorResponse;
import com.sq.libwebsocket.util.LogUtil;

import java.util.ArrayDeque;
import java.util.Queue;

/**
 * 使用操作线程发送数据
 * <p>
 */
public class WebSocketEngine {

    private static final String TAG = "WSWebSocketEngine";


    private OptionThread mOptionThread;

    WebSocketEngine() {
        mOptionThread = new OptionThread();
        mOptionThread.start();
    }

    void sendRequest(WebSocketWrapper webSocket,
                     Request request,
                     SocketWrapperListener listener) {
        if (mOptionThread.mHandler == null) {
            listener.onSendDataError(request,
                    ErrorResponse.ERROR_UN_INIT,
                    null);
        } else {
            ReRunnable runnable = ReRunnable.obtain();
            runnable.type = ReRunnable.type_send;
            runnable.request = request;
            runnable.webSocketWrapper = webSocket;
            mOptionThread.mHandler.post(runnable);
        }
    }

    void connect(WebSocketWrapper webSocket,
                 SocketWrapperListener listener) {
        if (mOptionThread.mHandler == null) {
            listener.onConnectFailed(new Exception("WebSocketEngine not start!"));
        }else{
            ReRunnable runnable = ReRunnable.obtain();
            runnable.type = ReRunnable.type_connect;
            runnable.webSocketWrapper = webSocket;
            mOptionThread.mHandler.post(runnable);
        }
    }

    void disConnect(WebSocketWrapper webSocket,
                    SocketWrapperListener listener) {
        if (mOptionThread.mHandler != null) {
            ReRunnable runnable = ReRunnable.obtain();
            runnable.type = ReRunnable.type_disconnect;
            runnable.webSocketWrapper = webSocket;
            mOptionThread.mHandler.post(runnable);
        }else{
            LogUtil.e(TAG, "WebSocketEngine not start!");
        }
    }

    void destroyWebSocket(WebSocketWrapper webSocket){
        if (mOptionThread.mHandler != null) {
            ReRunnable runnable = ReRunnable.obtain();
            runnable.type = ReRunnable.type_destroy;
            runnable.webSocketWrapper = webSocket;
            mOptionThread.mHandler.post(runnable);
        }else{
            LogUtil.e(TAG, "WebSocketEngine not start!");
        }
    }

    public void destroy() {
        if (mOptionThread != null) {
            if (mOptionThread.mHandler != null) {
                mOptionThread.mHandler.sendEmptyMessage(OptionHandler.QUIT);
            }
        }
    }

    private class OptionThread extends Thread {

        private OptionHandler mHandler;

        @Override
        public void run() {
            super.run();
            Looper.prepare();
            mHandler = new OptionHandler();
            Looper.loop();
        }
    }

    private static class OptionHandler extends Handler {

        private static final int QUIT = 1;

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
        }
    }

    private static class ReRunnable implements Runnable {

        private static Queue<ReRunnable> POOL = new ArrayDeque<>(10);

        static ReRunnable obtain() {
            ReRunnable runnable = POOL.poll();
            if (runnable == null) {
                runnable = new ReRunnable();
            }
            return runnable;
        }
        public static final int type_send = 0;
        public static final int type_connect = 1;
        public static final int type_disconnect = 2;
        public static final int type_destroy = 3;
        /**
         * 0-发送数据；
         * 1-连接；
         * 2-断开连接；
         * 3-销毁 WebSocketWrapper 对象。
         */
        private int type;
        private WebSocketWrapper webSocketWrapper;
        private Request request;

        @Override
        public void run() {
            try {
                if (webSocketWrapper == null) return;
                if (type == type_send && request == null) return;
                if (type == type_send) {
                    webSocketWrapper.send(request);
                } else if (type == type_connect) {
                    webSocketWrapper.reconnect();
                } else if (type == type_disconnect) {
                    webSocketWrapper.disConnect();
                }else if(type == type_destroy){
                    webSocketWrapper.destroy();
                }
            } finally {
                webSocketWrapper = null;
                request = null;
                release();
            }
        }

        void release() {
            POOL.offer(this);
        }
    }
}
