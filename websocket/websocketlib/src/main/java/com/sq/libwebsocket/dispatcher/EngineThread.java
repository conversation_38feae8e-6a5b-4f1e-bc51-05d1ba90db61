package com.sq.libwebsocket.dispatcher;

import android.os.Process;

import com.sq.libwebsocket.util.LogUtil;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;

/**
 * Created by znb on 2021/11/10
 */
public class EngineThread extends Thread {

    private String TAG = "EngineThread";

    private ArrayBlockingQueue<ResponseProcessEngine.EngineEntity> jobQueue = new ArrayBlockingQueue<>(3000);

    private ExecutorService executorService;

    private boolean stop;

    @Override
    public synchronized void start() {
        stop = false;
        super.start();
    }

    @Override
    public void run() {
        super.run();
        Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
        while (!stop) {
            try {
                ResponseProcessEngine.EngineEntity entity = jobQueue.take();
                if (entity.isError) {
                    entity.dispatcher.onSendDataError(entity.errorResponse,
                            entity.delivery);
                    LogUtil.i(TAG,"onSendDataError");
                } else {
                    entity.response.onResponse(entity.dispatcher, entity.delivery);
                    LogUtil.i(TAG,"onResponse");
                }
                ResponseProcessEngine.EngineEntity.release(entity);
            } catch (InterruptedException e) {
                if (stop) {
                    return;
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "run()->Exception", e);
            }
        }
    }

    void add(final ResponseProcessEngine.EngineEntity entity) {
        if (!jobQueue.offer(entity)) {
            LogUtil.e(TAG, "Offer response to Engine failed!start an thread to put.");
//            if (executorService == null) {
//                int processorsLength = Runtime.getRuntime().availableProcessors();
//                int nThreads = 2 * processorsLength;
//                LogUtil.i(TAG,"executorService processorsLength:"+processorsLength);
//                executorService = new ThreadPoolExecutor(nThreads, nThreads,
//                        0L, TimeUnit.MILLISECONDS,
//                        new LinkedBlockingQueue<Runnable>(), Executors.defaultThreadFactory(),new ThreadPoolExecutor.DiscardOldestPolicy());
//            }
//            executorService.execute(new Runnable() {
//                @Override
//                public void run() {
//                    if (stop) {
//                        return;
//                    }
//                    try {
//                        jobQueue.put(entity);
//                    } catch (Exception e) {
//                        if (stop) {
//                            LogUtil.e(TAG, "put response failed!", e);
//                        } else {
//                            interrupt();
//                        }
//                    }
//                }
//            });
        }
    }

    /**
     * 结束线程
     */
    void quit() {
        stop = true;
        jobQueue.clear();
        EngineThread.this.interrupt();
    }
}
