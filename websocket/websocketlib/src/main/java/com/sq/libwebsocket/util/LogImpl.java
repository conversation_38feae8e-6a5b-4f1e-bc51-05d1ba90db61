package com.sq.libwebsocket.util;


import com.sq.tool.logger.SQLog;

/**
 * Logable 默认实现类
 * <p>
 */
public class LogImpl implements Logable {

    @Override
    public void v(String tag, String msg) {
        SQLog.vt(tag, msg);
    }

    @Override
    public void v(String tag, String msg, Throwable tr) {
        SQLog.vt(tag, msg, tr);
    }

    @Override
    public void d(String tag, String text) {
        SQLog.dt(tag, text);
    }

    @Override
    public void d(String tag, String text, Throwable tr) {
        SQLog.dt(tag, text, tr);
    }

    @Override
    public void i(String tag, String text) {
        SQLog.it(tag, text);
    }

    @Override
    public void i(String tag, String text, Throwable tr) {
        SQLog.it(tag, text, tr);
    }

    @Override
    public void e(String tag, String text) {
        SQLog.et(tag, text);
    }

    @Override
    public void e(String tag, String msg, Throwable tr) {
        SQLog.et(tag, msg, tr);
    }

    @Override
    public void w(String tag, Throwable tr) {
        SQLog.wt(tag, "", tr);
    }

    @Override
    public void wtf(String tag, String msg) {
        SQLog.et(tag, msg);
    }

    @Override
    public void wtf(String tag, Throwable tr) {
        SQLog.et(tag, "", tr);
    }

    @Override
    public void wtf(String tag, String msg, Throwable tr) {
        SQLog.et(tag, msg, tr);
    }
}
