package com.sq.libwebsocket.request;


import com.sq.sywebsocket.client.WebSocketClient;

/**
 * 发送 Ping
 * <p>
 */
public class PingRequest implements Request<Object> {

    PingRequest() {
    }

    @Override
    public void setRequestData(Object data) {

    }

    @Override
    public Object getRequestData() {
        return null;
    }

    @Override
    public void send(WebSocketClient client) {
        client.sendPing();
    }

    @Override
    public void release() {
        RequestFactory.releasePingRequest(this);
    }
}
