package com.sq.libwebsocket.request;


import com.sq.sywebsocket.client.WebSocketClient;

import java.util.Arrays;

/**
 * byte[] 类型的请求
 * <p>
 */
public class ByteArrayRequest implements Request<byte[]> {
    private static final String TAG = "ByteArrayRequest";
    ByteArrayRequest() {
    }

    private byte[] data;

    @Override
    public void setRequestData(byte[] data) {
        this.data = data;
    }

    @Override
    public byte[] getRequestData() {
        return this.data;
    }

    @Override
    public void send(WebSocketClient client) {
        client.send(this.data);
    }

    @Override
    public void release() {
        RequestFactory.releaseByteArrayRequest(this);
    }

    @Override
    public String toString() {
        return String.format("[@ByteArrayRequest%s,%s]\n%s",
                hashCode(),
                data == null ?
                        "data:null" :
                        "data.length:" + data.length, Arrays.toString(data));
    }
}
