package com.sq.libwebsocket.dispatcher;

import com.sq.libwebsocket.response.ErrorResponse;
import com.sq.libwebsocket.response.Response;
import com.sq.libwebsocket.util.LogUtil;

import java.util.ArrayDeque;
import java.util.Queue;

/**
 * Created by znb on 2021/11/10
 */
public class ResponseProcessEngine {
    private static final String TAG = "ResponseProcessEngine";
    private EngineThread mThread;

    public ResponseProcessEngine() {
        mThread = new EngineThread();
        mThread.start();
    }

    public void onMessageReceive(Response message,
                                 IResponseDispatcher dispatcher,
                                 ResponseDelivery delivery) {
        LogUtil.i(TAG,"onMessageReceive 1");
        if (message == null || dispatcher == null || delivery == null) {
            return;
        }
        LogUtil.i(TAG,"onMessageReceive 2");
        EngineEntity entity = EngineEntity.obtain();
        entity.dispatcher = dispatcher;
        entity.delivery = delivery;
        entity.isError = false;
        entity.response = message;
        entity.errorResponse = null;
        mThread.add(entity);
    }

    public void onSendDataError(ErrorResponse errorResponse,
                                IResponseDispatcher dispatcher,
                                ResponseDelivery delivery) {
        if (errorResponse == null || dispatcher == null || delivery == null) {
            return;
        }
        EngineEntity entity = EngineEntity.obtain();
        entity.dispatcher = dispatcher;
        entity.delivery = delivery;
        entity.isError = true;
        entity.errorResponse = errorResponse;
        entity.response = null;
        mThread.add(entity);
    }

    static class EngineEntity {

        private static Queue<EngineEntity> ENTITY_POOL = new ArrayDeque<>(10);

        boolean isError;
        Response response;
        ErrorResponse errorResponse;
        IResponseDispatcher dispatcher;
        ResponseDelivery delivery;

        static EngineEntity obtain() {
            EngineEntity engineEntity = ENTITY_POOL.poll();
            if (engineEntity == null) {
                engineEntity = new EngineEntity();
            }
            return engineEntity;
        }

        static void release(EngineEntity entity) {
            ENTITY_POOL.offer(entity);
        }
    }
}
