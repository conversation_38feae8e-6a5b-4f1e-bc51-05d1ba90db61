package com.sq.libwebsocket.request;


import com.sq.sywebsocket.client.WebSocketClient;
import com.sq.sywebsocket.framing.Framedata;

/**
 * 发送 {@link Framedata}
 * <p>
 */
public class FrameDataRequest implements Request<Framedata> {

    private Framedata framedata;

    FrameDataRequest() {
    }

    @Override
    public void setRequestData(Framedata data) {
        this.framedata = data;
    }

    @Override
    public Framedata getRequestData() {
        return this.framedata;
    }

    @Override
    public void send(WebSocketClient client) {
        client.sendFrame(framedata);
    }

    @Override
    public void release() {
        RequestFactory.releaseFrameDataRequest(this);
    }

    @Override
    public String toString() {
        return String.format("[@FrameDataRequest%s,Framedata:%s]",
                hashCode(),
                framedata == null ?
                        "null" :
                        framedata.toString());
    }
}
