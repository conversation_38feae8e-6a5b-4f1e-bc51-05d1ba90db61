package com.sq.libwebsocket;

import android.text.TextUtils;

import com.sq.libwebsocket.request.Request;
import com.sq.libwebsocket.response.ErrorResponse;
import com.sq.libwebsocket.response.Response;
import com.sq.libwebsocket.response.ResponseFactory;
import com.sq.libwebsocket.util.LogUtil;

import com.sq.sywebsocket.WebSocket;
import com.sq.sywebsocket.client.WebSocketClient;
import com.sq.sywebsocket.drafts.Draft;
import com.sq.sywebsocket.drafts.Draft_6455;
import com.sq.sywebsocket.exceptions.WebsocketNotConnectedException;
import com.sq.sywebsocket.framing.Framedata;
import com.sq.sywebsocket.handshake.ServerHandshake;

import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

/**
 * 负责 WebSocket 连接的建立，数据发送，监听数据等。
 * <p>
 */
public class WebSocketWrapper {

    private static final String TAG = "WebSocketWrapper";

    private WebSocketSetting mSetting;
    private SocketWrapperListener mSocketListener;

    private WebSocketClient mWebSocket;


    public static int CONNECTSTATUS_UNCONNECT = 0;
    public static int CONNECTSTATUS_CONNECTING = 1;
    public static int CONNECTSTATUS_CONNECTED = 2;

    /**
     * 0-未连接
     * 1-正在连接
     * 2-已连接
     */
    private volatile int connectStatus = CONNECTSTATUS_UNCONNECT;

    /**
     * 需要关闭连接标志，调用 #disconnect 方法后为 true
     */
    private volatile boolean needClose = false;
    /**
     * 是否已销毁
     */
    private volatile boolean destroyed = false;

    WebSocketWrapper(WebSocketSetting setting, SocketWrapperListener socketListener) {
        this.mSetting = setting;
        this.mSocketListener = socketListener;
    }

    void connect() {
        if (destroyed) {
            return;
        }
        needClose = false;
        if (connectStatus == CONNECTSTATUS_UNCONNECT) {
            connectStatus = CONNECTSTATUS_CONNECTING;
            try {
                if (mWebSocket == null) {
                    if (TextUtils.isEmpty(mSetting.getConnectUrl())) {
                        throw new RuntimeException("WebSocket connect url is empty!");
                    }
                    Draft draft = mSetting.getDraft();
                    if (draft == null) {
                        draft = new Draft_6455();
                    }
                    int connectTimeOut = mSetting.getConnectTimeout();
                    if (connectTimeOut <= 0) {
                        connectTimeOut = 0;
                    }
                    mWebSocket = new MyWebSocketClient(
                            new URI(mSetting.getConnectUrl()),
                            draft,
                            mSetting.getHttpHeaders(),
                            connectTimeOut);
                    LogUtil.i(TAG, "WebSocket start connect...");
                    if (mSetting.getProxy() != null) {
                        mWebSocket.setProxy(mSetting.getProxy());
                    }
                    mWebSocket.connect();
                    mWebSocket.setConnectionLostTimeout(mSetting.getConnectionLostTimeout());
                    if (needClose) {
                        disConnect();
                    }
                    checkDestroy();
                } else {
                    LogUtil.i(TAG, "WebSocket reconnecting...");
                    mWebSocket.reconnect();
                    if (needClose) {
                        disConnect();
                    }
                    checkDestroy();
                }
            } catch (Throwable e) {
                connectStatus = CONNECTSTATUS_UNCONNECT;
                LogUtil.e(TAG, "WebSocket connect failed:", e);
                if (mSocketListener != null) {
                    mSocketListener.onConnectFailed(e);
                }
            }
        }
    }

    /**
     * 重新连接
     */
    void reconnect() {
        needClose = false;
        if (connectStatus == CONNECTSTATUS_UNCONNECT) {
            connect();
        }
    }

    /**
     * 断开连接
     */
    void disConnect() {
        needClose = true;
        if (connectStatus == CONNECTSTATUS_CONNECTED) {
            LogUtil.i(TAG, "WebSocket disconnecting...");
            if (mWebSocket != null) {
                mWebSocket.close();
            }
            LogUtil.i(TAG, "WebSocket disconnected");
        }
    }

    /**
     * 发送数据
     *
     * @param request 请求数据
     */
    void send(Request request) {
        if (mWebSocket == null) {
            return;
        }
        if (request == null) {
            LogUtil.e(TAG, "send data is null!");
            return;
        }
        if (connectStatus == CONNECTSTATUS_CONNECTED) {
            try {
                request.send(mWebSocket);
            } catch (WebsocketNotConnectedException e) {
                connectStatus = CONNECTSTATUS_UNCONNECT;
                LogUtil.e(TAG, "ws is disconnected, send failed:" + request.toString(), e);
                if (mSocketListener != null) {
                    //not connect
                    mSocketListener.onSendDataError(request,
                            ErrorResponse.ERROR_NO_CONNECT,
                            e);
                    mSocketListener.onDisconnect();
                }
            } catch (Throwable e) {
                connectStatus = CONNECTSTATUS_UNCONNECT;
                LogUtil.e(TAG, "Exception,send failed:" + request.toString(), e);
                if (mSocketListener != null) {
                    //unknown error
                    mSocketListener.onSendDataError(request,
                            ErrorResponse.ERROR_UNKNOWN,
                            e);
                }
            } finally {
                request.release();
            }
        } else {
            if (mSocketListener != null) {
                //not connect
                mSocketListener.onSendDataError(request,
                        ErrorResponse.ERROR_NO_CONNECT,
                        null);
            }
        }
    }
    int getConnectState() {
        return connectStatus;
    }
    /**
     * 彻底销毁资源
     */
    void destroy() {
        destroyed = true;
        disConnect();
        if (connectStatus == CONNECTSTATUS_UNCONNECT) {
            mWebSocket = null;
        }
        releaseResource();
    }

    private void checkDestroy() {
        if (destroyed) {
            try {
                if (mWebSocket != null && !mWebSocket.isClosed()) {
                    mWebSocket.close();
                }
                releaseResource();
                connectStatus = CONNECTSTATUS_UNCONNECT;
            } catch (Throwable e) {
                LogUtil.e(TAG, "checkDestroy(WebSocketClient)", e);
            }
        }
    }

    private void releaseResource() {
        if (mSocketListener != null) {
            mSocketListener = null;
        }
    }

    private void onWSCallbackOpen(ServerHandshake handshakeData) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        connectStatus = CONNECTSTATUS_CONNECTED;
        LogUtil.i(TAG, "WebSocket connect success");
        if (needClose) {
            disConnect();
        } else {
            if (mSocketListener != null) {
                mSocketListener.onConnected();
            }
        }
    }

    private void onWSCallbackMessage(String message) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        connectStatus = CONNECTSTATUS_CONNECTED;
        if (mSocketListener != null) {
            Response<String> response = ResponseFactory.createTextResponse();
            response.setResponseData(message);
            LogUtil.i(TAG, "WebSocket received message:" + response.toString());
            mSocketListener.onMessage(response);
        }
    }

    private void onWSCallbackMessage(ByteBuffer bytes) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        connectStatus = CONNECTSTATUS_CONNECTED;
        if (mSocketListener != null) {
            Response<ByteBuffer> response = ResponseFactory.createByteBufferResponse();
            response.setResponseData(bytes);
            LogUtil.i(TAG, "WebSocket received message:" + response.toString());
            mSocketListener.onMessage(response);
        }
    }

    private void onWSCallbackWebsocketPing(Framedata f) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        connectStatus = CONNECTSTATUS_CONNECTED;
        if (mSocketListener != null) {
            Response<Framedata> response = ResponseFactory.createPingResponse();
            response.setResponseData(f);
            LogUtil.i(TAG, "WebSocket received ping:" + response.toString());
            mSocketListener.onMessage(response);
        }
    }

    private void onWSCallbackWebsocketPong(Framedata f) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        connectStatus = CONNECTSTATUS_CONNECTED;
        if (mSocketListener != null) {
            Response<Framedata> response = ResponseFactory.createPongResponse();
            response.setResponseData(f);
            LogUtil.i(TAG, "WebSocket received pong:" + response.toString());
            mSocketListener.onMessage(response);
        }
    }

    private void onWSCallbackClose(int code, String reason, boolean remote) {
        connectStatus = CONNECTSTATUS_UNCONNECT;
        LogUtil.d(TAG, String.format("WebSocket closed!code=%s,reason:%s,remote:%s",
                code,
                reason,
                remote));
        if (mSocketListener != null) {
            mSocketListener.onDisconnect();
        }
        checkDestroy();
    }

    private void onWSCallbackError(Exception ex) {
        if (destroyed) {
            checkDestroy();
            return;
        }
        LogUtil.e(TAG, "WebSocketClient#onError(Exception)", ex);
    }

    private class MyWebSocketClient extends WebSocketClient {

        public MyWebSocketClient(URI serverUri) {
            super(serverUri);
        }

        public MyWebSocketClient(URI serverUri, Draft protocolDraft) {
            super(serverUri, protocolDraft);
        }

        public MyWebSocketClient(URI serverUri, Map<String, String> httpHeaders) {
            super(serverUri, httpHeaders);
        }

        public MyWebSocketClient(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders) {
            super(serverUri, protocolDraft, httpHeaders);
        }

        public MyWebSocketClient(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
            super(serverUri, protocolDraft, httpHeaders, connectTimeout);
        }

        @Override
        public void onOpen(ServerHandshake handshakeData) {
            onWSCallbackOpen(handshakeData);
        }

        @Override
        public void onMessage(String message) {
            onWSCallbackMessage(message);
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            LogUtil.i(TAG,"onMessage");
            onWSCallbackMessage(bytes);
        }

        @Override
        public void onWebsocketPing(WebSocket conn, Framedata f) {
            super.onWebsocketPing(conn, f);
            onWSCallbackWebsocketPing(f);
        }

        @Override
        public void onWebsocketPong(WebSocket conn, Framedata f) {
            super.onWebsocketPong(conn, f);
            onWSCallbackWebsocketPong(f);
        }

        @Override
        public void onClose(int code, String reason, boolean remote) {
            onWSCallbackClose(code, reason, remote);
        }

        @Override
        public void onError(Exception ex) {
            onWSCallbackError(ex);
        }

        @Override
        public void send(byte[] data) {
            super.send(data);
            LogUtil.i(TAG,"send");
        }
    }
}
