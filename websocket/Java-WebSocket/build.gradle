apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode 1
        versionName rootProject.ext.libraryVersion

    }

    buildTypes {
        release {
            matchingFallbacks=['release']
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        abortOnError false
    }

}

dependencies {
   // implementation group: 'org.slf4j', name: 'slf4j-api', version: '1.7.25'
    implementation 'org.slf4j:slf4j-api:1.7.25'
}
