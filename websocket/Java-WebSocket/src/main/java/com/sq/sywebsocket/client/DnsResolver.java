/*
 * Copyright (c) 2010-2020 <PERSON>
 *
 *  Permission is hereby granted, free of charge, to any person
 *  obtaining a copy of this software and associated documentation
 *  files (the "Software"), to deal in the Software without
 *  restriction, including without limitation the rights to use,
 *  copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the
 *  Software is furnished to do so, subject to the following
 *  conditions:
 *
 *  The above copyright notice and this permission notice shall be
 *  included in all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *  OTHER DEALINGS IN THE SOFTWARE.
 */

package com.sq.sywebsocket.client;

import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;

/**
 * Users may implement this interface to override the default DNS lookup offered by the OS.
 *
 * @since 1.4.1
 */
public interface DnsResolver {

  /**
   * Resolves the IP address for the given URI.
   * <p>
   * This method should never return null. If it's not able to resolve the IP address then it should
   * throw an UnknownHostException
   *
   * @param uri The URI to be resolved
   * @return The resolved IP address
   * @throws UnknownHostException if no IP address for the <code>uri</code> could be found.
   */
  InetAddress resolve(URI uri) throws UnknownHostException;

}
