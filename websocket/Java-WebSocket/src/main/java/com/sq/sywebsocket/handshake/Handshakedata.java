/*
 * Copyright (c) 2010-2020 <PERSON>
 *
 *  Permission is hereby granted, free of charge, to any person
 *  obtaining a copy of this software and associated documentation
 *  files (the "Software"), to deal in the Software without
 *  restriction, including without limitation the rights to use,
 *  copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the
 *  Software is furnished to do so, subject to the following
 *  conditions:
 *
 *  The above copyright notice and this permission notice shall be
 *  included in all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *  OTHER DEALINGS IN THE SOFTWARE.
 */

package com.sq.sywebsocket.handshake;

import java.util.Iterator;

/**
 * The interface for the data of a handshake
 */
public interface Handshakedata {

  /**
   * Iterator for the http fields
   *
   * @return the http fields
   */
  Iterator<String> iterateHttpFields();

  /**
   * Gets the value of the field
   *
   * @param name The name of the field
   * @return the value of the field or an empty String if not in the handshake
   */
  String getFieldValue(String name);

  /**
   * Checks if this handshake contains a specific field
   *
   * @param name The name of the field
   * @return true, if it contains the field
   */
  boolean hasFieldValue(String name);

  /**
   * Get the content of the handshake
   *
   * @return the content as byte-array
   */
  byte[] getContent();
}
