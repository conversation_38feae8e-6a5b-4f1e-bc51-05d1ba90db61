package com.plugin.sdk;

import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Resources;


/**
 * <AUTHOR>
 * @date 2020-08-24
 */
public class BasePluginInterface {

    public void startActivity(Intent intent) {

    }

    public void startActivityForResult(Intent intent, int requestCode) {

    }

    public Resources getResources(Resources hostResource) {
        return hostResource;
    }

    public AssetManager getAssets(AssetManager hostAssetManager) {
        return hostAssetManager;
    }

    public ClassLoader getClassLoader(ClassLoader hostClassLoader) {
        return hostClassLoader;
    }

    public boolean isSupportPlugin() {
        return false;
    }

}
