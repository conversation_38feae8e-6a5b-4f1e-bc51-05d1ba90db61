package com.plugin.standard;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;

public class BaseActivity implements IActivityInterface {

    public BaseActivity() {

    }

    private Activity mAppActivity;

    @Override
    public void insertAppContext(Activity activity) {
        mAppActivity = activity;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onStart() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void startActivity(Intent intent) {
        mAppActivity.startActivity(intent);
    }

    @Override
    public void setTheme(int theme) {
        mAppActivity.setTheme(theme);
    }

    @Override
    public void setRequestedOrientation(int requestedOrientation) {
        mAppActivity.setRequestedOrientation(requestedOrientation);
    }

    @Override
    public <T extends View> T findViewById(int id) {
        return mAppActivity.findViewById(id);
    }

    public void setContentView(int layoutResID) {
        mAppActivity.setContentView(layoutResID);
    }

    @Override
    public Intent getIntent() {
        return mAppActivity.getIntent();
    }

    @Override
    public Window getWindow() {
        return mAppActivity.getWindow();
    }

    public Activity getContext() {
        return mAppActivity;
    }

    @Override
    public void onPause() {
    }

    @Override
    public void finish() {
        mAppActivity.finish();
    }

    @Override
    public boolean isFinishing() {
        return mAppActivity.isFinishing();
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        mAppActivity.startActivityForResult(intent, requestCode);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return false;
    }

    @Override
    public String getPackageName() {
        return mAppActivity.getPackageName();
    }

    public Context getApplicationContext() {
        return mAppActivity.getApplicationContext();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {

    }

}
