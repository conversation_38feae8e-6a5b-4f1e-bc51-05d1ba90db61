package com.plugin.standard;

import android.app.Application;
import android.content.Context;

public class BaseApplication implements IApplicationInterface {

    private Application mApplication;

    public void insertAppContext(Application application) {
        mApplication = application;
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void attachBaseContext(Context base) {

    }

    @Override
    public Context getApplicationContext() {
        return mApplication;
    }


}
