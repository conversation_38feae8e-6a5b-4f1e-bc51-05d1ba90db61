def libraryVersion = get_libraryVersion()
def isPluginHostAar = get_isPluginHostAar()
def isMultiAAR = get_isMultiAAR()

apply plugin: 'com.android.library'
apply plugin: 'com.kezong.fat-aar'
apply plugin: "com.jfrog.artifactory"
apply plugin: 'maven-publish'

if (isMultiAAR) {
    apply from: 'multi-aar.gradle'
}


android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {

        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

repositories{
    flatDir{
        dirs 'libs'
    }
}


dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api rootProject.ext.other.okhttp
    api rootProject.ext.other.okio
    // 没有找到需要依赖fast json的, 但是重构前产物中有, 所以保留
    api rootProject.ext.other.fastjson
    api ('com.android.support:appcompat-v7:28.0.0')
    if (isPluginHostAar) {
        embed project(path: ':module_plugin:plugin_standard', configuration: 'default')
        embed project(path: ':module_plugin:plugin', configuration: 'default')
        embed project(path: ':module_api', configuration: 'default')
        embed 'com.37sy.android:social_sdk:2.0.8'
        // 第三方登录和分享
        embed project(path: ':module_third:social_sdk', configuration: 'default')
        //宿主跟插件都需要的的资源
        embed project(':module_ui_common:base')
        embed project(":module_ui_common:sq")
        //阿里人脸认证
        embed project(':module_third:ali_face:ali_face_sdk')
        // 阿里闪验
        embed project(':module_third:ali_auth:ali_auth_sdk')
        // 阿里支付
        embed project(':module_third:ali_pay:ali_pay_sdk')
        // 云闪付
        embed project(':module_third:union_pay:union_pay_sdk')
        // 穿山甲
        embed project(':module_third:bytedance_advertise:bytedance_advertise_sdk')
        // 个推模块通过切包流程特殊处理, 不附带进aar中
        // 个推模块由研发主动引入, 不附带进aar中
        embed rootProject.ext.sqsdk.push_base
    } else {
        api project(':module_api')
        api project(':module_plugin:plugin_standard')
        api project(':module_plugin:plugin')
        // 第三方登录和分享
        api 'com.37sy.android:social_sdk:2.0.8'
        api project(':module_third:social_sdk')
        //宿主跟插件都需要的的资源
        api project(':module_ui_common:base')
        api project(":module_ui_common:sq")
        //阿里人脸认证
        api project(':module_third:ali_face:ali_face_sdk')
        // 阿里闪验
        api project(':module_third:ali_auth:ali_auth_sdk')
        // 阿里支付
        api project(':module_third:ali_pay:ali_pay_sdk')
        // 云闪付
        api project(':module_third:union_pay:union_pay_sdk')
        // 穿山甲
        api project(':module_third:bytedance_advertise:bytedance_advertise_sdk')

        implementation(rootProject.ext.sqsdk.push_base) {
            exclude group: 'com.android.support', module: 'support-annotations'
        }
    }
}
if(isPluginHostAar){
    task buildSdk() {
        group 'custom'
        dependsOn 'assembleRelease'
        doLast{
            String aarSrcName = "${project.name}-release.aar"
            String aarSrcPath = "${project.buildDir}/outputs/aar/${aarSrcName}"
            String aarDstDir = "${rootProjectPath()}/archived/sdk/plugin/"
            String aarDstName = "37sy_sdk_v${libraryVersion}_${getCurrentTime}.aar"
            println("aarSrc:${aarSrcPath}")
            println("aarDst:${aarDstDir}")
            delete(aarDstDir)
            copy{
                from(aarSrcPath)
                into(aarDstDir)
                rename(aarSrcName,aarDstName)
            }
        }

    }
}


//发布aar到maven仓库
if (isMultiAAR) {
    publishing {
        publications {
            aar(MavenPublication) {
                groupId = rootProject.ext.artifactory_groupId
                artifactId "sySDKMultiPluginLib"
                version = libraryVersion
                artifact "${project.buildDir}/outputs/aar/${project.name}-release.aar"
            }
        }
    }

    artifactory {
        contextUrl = rootProject.ext.artifactory_address
        publish {
            repository {
                repoKey = rootProject.ext.artifactory_repoKey
                username = rootProject.ext.artifactory_user
                password = rootProject.ext.artifactory_password
            }
            defaults {
                publishArtifacts = true
                publications('aar')
                publishPom = true //Publish generated POM files to Artifactory (true by default)
                publishIvy = true
                //Publish generated Ivy descriptor files to Artifactory (true by default)
            }
        }
    }
}
