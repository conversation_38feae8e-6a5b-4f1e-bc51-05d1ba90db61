package com.parameters.bean;

import android.text.TextUtils;

import org.json.JSONObject;

public class WebDialogBean {
    //webview加载的url
    private String url;

    //是否展示webview的工具栏
    private boolean showToolBar;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isShowToolBar() {
        return showToolBar;
    }

    public void setShowToolBar(boolean showToolBar) {
        this.showToolBar = showToolBar;
    }


    /**
     * 将json字符串转化成实体
     * @param json
     * @return
     */
    public static WebDialogBean parseToObject(String json) {
        WebDialogBean webDialog = new WebDialogBean();
        if (TextUtils.isEmpty(json)) {
            return webDialog;
        }
        try {
            JSONObject obj = new JSONObject(json);
            webDialog.setUrl(obj.optString("url"));
            webDialog.setShowToolBar(obj.optBoolean("showToolBar"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return webDialog;
    }
}
