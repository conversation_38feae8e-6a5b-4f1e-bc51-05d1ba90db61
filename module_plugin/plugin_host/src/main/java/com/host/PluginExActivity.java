package com.host;

import android.content.Context;

import com.plugin.core.ReflectionUtils;
import com.sqwan.msdk.SQwanCore;

public class PluginExActivity extends PluginActivity {
    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(newBase);
        hookResources(newBase);
    }
    private void hookResources(Context context){
        ReflectionUtils.setFieldValue(context, "mResources", SQwanCore.getInstance().getResources(context.getResources()));
    }
}
