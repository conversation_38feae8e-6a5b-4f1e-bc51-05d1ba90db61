package com.host;

import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.support.annotation.Nullable;
import com.plugin.core.ProxyPluginActivity;
import com.plugin.core.loader.ApkClassLoader;
import com.sqwan.msdk.PluginLoader;

public class PluginActivity extends ProxyPluginActivity {

    @Override
    public Resources getResources() {
        return PluginLoader.getInstance().get().mResource;
    }

    @Override
    public ApkClassLoader getClassLoader() {
        return PluginLoader.getInstance().get().mClassLoader;
    }

    @Override
    public void startActivity(Intent intent) {
        PluginExActivityHandler.getInstance().handlerActivityIntent(this, intent, getClassLoader());
        super.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, @Nullable Bundle options) {
        PluginExActivityHandler.getInstance().handlerActivityIntent(this, intent, getClassLoader());
        super.startActivityForResult(intent, requestCode, options);
    }
}