package com.host;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.plugin.standard.BaseActivity;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-10-12 16:24
 */
public class PluginExActivityHandler {
    private static final PluginExActivityHandler ourInstance = new PluginExActivityHandler();

    public static PluginExActivityHandler getInstance() {
        return ourInstance;
    }

    private PluginExActivityHandler() {
    }
    private List<String> pluginExActivtys = null;

    public void handlerActivityIntent(Context context, Intent intent, ClassLoader classLoader) {
        if (intent.getComponent() == null) {
            return;
        }
        String className = intent.getComponent().getClassName();
        try {
            Class<?> activityClass = classLoader.loadClass(className);
            if (activityClass.getSuperclass() == BaseActivity.class) {
                intent.putExtra("activity", className);
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        startPluginActivity(context, intent);
    }

    public void startPluginActivity(Context context,Intent intent){
        String activityName = intent.getStringExtra("activity");
        if (TextUtils.isEmpty(activityName)) {
            return;
        }

        if (isPluginExActivty(activityName)) {
            intent.setClass(context, PluginExActivity.class);
            return;
        }

        String screenOrientation = intent.getStringExtra("screenOrientation");
        Class<? extends Activity> targetActivity = null;
        if (TextUtils.equals(screenOrientation, "portrait")) {
            targetActivity = PluginActivity.class;
        } else if (TextUtils.equals(screenOrientation, "landscape")) {
            targetActivity = LandscapePluginActivity.class;
        } else if (TextUtils.equals(screenOrientation, "behind")) {
            targetActivity = BehindPluginActivity.class;
        }
        if (targetActivity == null) {
            targetActivity = PluginActivity.class;
        }
        intent.setClass(context, targetActivity);
    }

    //目前通过配置写死，后续通过扫描处理
    private boolean isPluginExActivty(String activityString){
        if (pluginExActivtys==null) {
            pluginExActivtys = getPluginExActivtysAuto();
        }
        if (pluginExActivtys!=null) {
            return pluginExActivtys.contains(activityString);
        }
        return false;
    }

    /**
     * 宿主透明主题的activity
     * @return
     */
    private List<String> getPluginExActivtysAuto(){
        List<String> pluginExActivtys = new ArrayList<>();
        pluginExActivtys.add("com.sqwan.common.dialog.PlatformAnnouncementActivity");
        pluginExActivtys.add("com.sqwan.liveshow.huya.activity.ChatInputActivity");
        return pluginExActivtys;
    }
}
