package com.sqwan.msdk;

import android.content.Context;

import android.content.res.AssetManager;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020-08-11
 */
public class PropertiesUtils {

    /**
     * 取得asstes下的渠道号
     *
     * @param context
     * @return public static String getAssetsAdkey(Context context){ String
     *         adkey = ""; try { InputStreamReader inputReader = new
     *         InputStreamReader
     *         (context.getResources().getAssets().open("fg.dat"));
     *         BufferedReader bufReader = new BufferedReader(inputReader); adkey
     *         = bufReader.readLine(); bufReader.close(); adkey =
     *         adkey.trim().equals("")?"10000":adkey.trim(); } catch (Exception
     *         e) { adkey = "10000"; }
     *
     *         return adkey; }
     */

    /**
     * 取assets下的配置参数
     */
    public static Properties readPropertites(Context context, String file) {
        return readPropertites(context.getResources().getAssets(), file);
    }

    public static Properties readPropertites(AssetManager assetManager, String file) {
        Properties p = null;
        InputStream in = null;
        try {
            in = assetManager.open(file);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return p;
    }



}
