package com.sqwan.msdk;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.text.TextUtils;
import java.util.Properties;

public class BusinessUtils {

    private static final String MULTI_CONFIG = "multiconfig";

    public static int getScreenOrientation(Context context) {
        return getScreenOrientation(context.getResources().getAssets());
    }
    public static int getScreenOrientation(AssetManager assetManager) {
        Properties multiConfig = PropertiesUtils.readPropertites(assetManager, MULTI_CONFIG);
        if (multiConfig == null) {
            return Configuration.ORIENTATION_LANDSCAPE;
        }
        String isLandScape = multiConfig.getProperty("isLandScape");
        if (!TextUtils.isEmpty(isLandScape) && isLandScape.equals("0")) {
            return Configuration.ORIENTATION_PORTRAIT;
        }
        return Configuration.ORIENTATION_LANDSCAPE;
    }
}