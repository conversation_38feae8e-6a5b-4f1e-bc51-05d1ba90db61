package com.sqwan.msdk;

import android.app.Application;
import android.content.Context;
import com.plugin.core.Plugin;
import com.plugin.core.tool.PluginLog;
import com.plugin.standard.BaseApplication;
import com.sqwan.msdk.api.SQAppApi;
import com.sqwan.msdk.api.SQMediaReportInterface;
import com.sqwan.msdk.api.SQReportInterface;


public class PluginSQApplication extends BaseApplication implements SQAppApi {

    final String TAG = "【" + getClass().getSimpleName() + "】";

    private SQAppApi mAppFromPlugin;

    @Override
    public void insertAppContext(Application application) {
        PluginLog.i(TAG + "insertAppContext: " + application);
        super.insertAppContext(application);
        try {
            if (mAppFromPlugin != null) {
                mAppFromPlugin.insertAppContext(application);
            }
        } catch (Exception e) {
            PluginLog.e(TAG + "注入context异常", e);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        PluginLog.i(TAG + "onCreate");
        if (mAppFromPlugin != null) {
            mAppFromPlugin.onCreate();
        }
    }

    @Override
    public void attachBaseContext(Context base) {
        PluginLog.i(TAG + "attachBaseContext: " + base);
        super.attachBaseContext(base);
        Plugin plugin = PluginLoader.getInstance().load(base);
        if (plugin != null) {
            try {
                Class<?> appImplClass = plugin.mClassLoader.loadClass("com.sqwan.msdk.SQApplicationImpl");
                mAppFromPlugin = (SQAppApi) appImplClass.newInstance();
                PluginLog.d(TAG + "反射创建插件中的Application实例: " + mAppFromPlugin);
                mAppFromPlugin.attachBaseContext(base);
            } catch (Exception e) {
                PluginLog.e(TAG + "反射创建插件中的Application实例异常", e);
            }
        }
    }

    public void setReporter(SQReportInterface reporter) {
        PluginLog.d(TAG + "setReporter " + reporter);
        if (mAppFromPlugin != null) {
            mAppFromPlugin.setReporter(reporter);
        }
    }

    @Override
    public void setMediaReporter(SQMediaReportInterface reporter) {
        PluginLog.d(TAG + "setMediaReporter " + reporter);
        if (mAppFromPlugin != null) {
            mAppFromPlugin.setMediaReporter(reporter);
        }
    }
}
