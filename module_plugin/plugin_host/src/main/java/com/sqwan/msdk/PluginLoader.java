package com.sqwan.msdk;

import android.content.Context;
import com.plugin.core.Plugin;
import com.plugin.core.PluginManager;

/**
 * 负责加载和持有Plugin实例
 *
 * <AUTHOR>
 * @since 2024/5/17
 */
public class PluginLoader {

    private static volatile PluginLoader sInstance;

    public static PluginLoader getInstance() {
        if (sInstance == null) {
            synchronized (PluginLoader.class) {
                if (sInstance == null) {
                    sInstance = new PluginLoader();
                }
            }
        }

        return sInstance;
    }

    private PluginLoader() {
    }

    private volatile Plugin mPlugin;

    /**
     * 加载插件, 应该只调用1次
     */
    Plugin load(Context context) {
        if (mPlugin == null) {
            synchronized (PluginLoader.class) {
                if (mPlugin == null) {
                    mPlugin = PluginManager.getInstance(context).loadPlugin();
                }
            }
        }
        return mPlugin;
    }

    /**
     * 获取插件, 应该在load后才调用
     */
    public Plugin get() {
        if (mPlugin == null) {
            throw new IllegalStateException("load plugin first!");
        }
        return mPlugin;
    }
}
