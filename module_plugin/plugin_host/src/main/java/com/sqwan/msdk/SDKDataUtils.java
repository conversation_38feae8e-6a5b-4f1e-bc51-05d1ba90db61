package com.sqwan.msdk;

import android.content.Context;
import android.util.Log;
import java.lang.reflect.Method;

/**
 *
 * 读取IMEI，PID，GID，REFER， DEV
 * <AUTHOR>
 * @date 2020-08-11
 */
public class SDKDataUtils {

    private static final String SQ_PREFS = "sq_prefs";

    private static final String DEV_LOGIC_CLASS = "com.sqwan.common.dev.DevLogic";
    private static final String IMEI_LOGIC_CLASS = "com.sqwan.common.dev.ImeiLogic";
    private static final String MAC_LOGIC_CLASS = "com.sqwan.common.dev.MacLogic";


    private static final String GID = "gid"; // appid
    private static final String PID = "pid";
    private static final String REFER = "refer";

    public static String getIMEI(Context context){
        try {
            Class devClass = PluginLoader.getInstance().get().mClassLoader.loadClass(IMEI_LOGIC_CLASS);
            if (devClass != null) {
                Method sdkGetMethod = devClass.getMethod("getInstance", Context.class);
                Object devLogicObj = sdkGetMethod.invoke(null, context);
                return getValue(devClass, devLogicObj);
            } else {
                Log.e("sqsdk_m", "imei加载失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getMac(Context context) {
        try {
            Class devClass = PluginLoader.getInstance().get().mClassLoader.loadClass(MAC_LOGIC_CLASS);
            if (devClass != null) {
                Method sdkGetMethod = devClass.getMethod("getInstance", Context.class);
                Object devLogicObj = sdkGetMethod.invoke(null, context);
                return getValue(devClass, devLogicObj);
            } else {
                Log.e("sqsdk_m", "mac加载失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getDev(Context context) {
        try {
            Class devClass = PluginLoader.getInstance().get().mClassLoader.loadClass(DEV_LOGIC_CLASS);
            if (devClass != null) {
                Method sdkGetMethod = devClass.getMethod("getInstance", Context.class);
                Object devLogicObj = sdkGetMethod.invoke(null, context);
                return getValue(devClass, devLogicObj);
            } else {
                Log.e("sqsdk_m", "dev加载失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getGID(Context context) {
        return SQwanCore.getInstance().getAppConfig().getGameid();
    }

    public static String getPID(Context context) {
        return SQwanCore.getInstance().getAppConfig().getPartner();
    }

    public static String getRefer(Context context) {
        return SQwanCore.getInstance().getAppConfig().getRefer();
    }

    private static String getValue(Class clazz, Object obj) {
        try {
            Method getValueMethod = clazz.getMethod("getValue");
            String value = (String) getValueMethod.invoke(obj);
            return value;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
