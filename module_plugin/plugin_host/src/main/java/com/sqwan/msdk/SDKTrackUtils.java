package com.sqwan.msdk;

import android.util.Log;


import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * 供multi工程（主要是媒体）用的埋点方法
 */
public class SDKTrackUtils {

    private static final String TRACK_CLASS = "com.sqwan.common.track.SqTrackActionManager2";


    public static void trackAction(String event, String describe) {
        trackAction(event, describe, null);
    }

    public static void trackAction(String event, String describe, HashMap<String, String> extraPram) {
        try {
            Class trackClass = PluginLoader.getInstance().get().mClassLoader.loadClass(TRACK_CLASS);
            Method sdkGetMethod = trackClass.getMethod("getInstance");
            Object trackManagerObj = sdkGetMethod.invoke(null);
            Method trackMethod = trackClass.getMethod("trackAction", String.class, String.class, HashMap.class);
            trackMethod.invoke(trackManagerObj, event, describe, extraPram);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("sqsdk_m", "erro  SqTrackActionManager2加载失败");
        }
    }
}
