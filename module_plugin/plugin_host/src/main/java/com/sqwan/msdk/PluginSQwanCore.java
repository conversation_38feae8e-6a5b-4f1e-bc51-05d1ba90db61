package com.sqwan.msdk;

import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import com.host.PluginExActivityHandler;
import com.parameters.share.ShareMessage;
import com.plugin.core.Plugin;
import com.plugin.core.loader.ApkClassLoader;
import com.plugin.core.tool.PluginLog;
import com.plugin.sdk.BasePluginInterface;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQPushTransmitMessageListener;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.SQSdkApi;
import com.sqwan.msdk.api.SQSdkInterface;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Map;

/**
 * 插件代理类
 */
public class PluginSQwanCore extends BasePluginInterface implements SQSdkApi {

    // 角色提交相关
    public static final String INFO_SERVERID = "serverId";
    public static final String INFO_SERVERNAME = "serverName";
    // 3.7.0新增开服时间
    public static final String INFO_SERVERTIME = "serverTime";
    public static final String INFO_ROLEID = "roleId";
    public static final String INFO_ROLENAME = "roleName";
    public static final String INFO_ROLELEVEL = "roleLevel";
    public static final String INFO_BALANCE = "balance";
    public static final String INFO_PARTYNAME = "partyName";
    public static final String INFO_VIPLEVEL = "vipLevel";
    public static final String INFO_ROLE_TIME_CREATE = "roleCTime";
    public static final String INFO_ROLE_TIME_LEVEL = "roleLevelMTime";

    /** 日志等级：调试 */
    public static final int LOG_LEVEL_DEBUG = 0;
    /** 日志等级：信息 */
    public static final int LOG_LEVEL_INFO = 1;
    /** 日志等级：警告 */
    public static final int LOG_LEVEL_WARN = 2;
    /** 日志等级：错误 */
    public static final int LOG_LEVEL_ERROR = 3;

    private final String SQRESULT_LISTENER_CLASS = "com.sqwan.msdk.api.SQResultListener";

    private HashMap<String, Class> mReflectClassMap = new HashMap<>();

    private final String TAG = "SdkProxy";

    private SQSdkInterface mSdk;

    private final String SDK_CLASS = "com.sqwan.msdk.SQwanCoreImpl";

    private final String SDK_GET_METHOD = "getInstance";

    private Context mContext;

    private Resources pluginRes;

    public PluginSQwanCore() {
        // 请不要在此处初始化任何逻辑，因为 new PluginSQwanCore 的时候，插件 apk 此时可能还没有初始化成功，要考虑到这种情况
    }

    public void init(final Context context, final String appkey, final SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            mContext = context;
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method initMethod = getSQwanCoreImplClass().getMethod("init", Context.class, String.class, SQResultListenerClass);
            initMethod.invoke(sdkImplObject, mContext, appkey, listenerWrapper(listener));

            if (pluginRes != null) {
                // 重新设置屏幕方向
                Configuration configuration = pluginRes.getConfiguration();
                configuration.orientation = BusinessUtils.getScreenOrientation(context);
                pluginRes.getConfiguration().setTo(configuration);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initCore(Context cxt, String appkey, SQResultListener listener) {

    }

    @Override
    public void submitStatisticsInfo(String key, String values) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method method = getSQwanCoreImplClass().getMethod("submitStatisticsInfo", String.class, String.class);
            method.invoke(sdkImplObject, key, values);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param event    事件名称，需要是英文缩写，请勿使用汉字，如: active
     * @param describe 事件描述说明，可以是UTF-8编码的任意字符，如"调用初始化"
     * @param params   事件参数，HashMap 的 key 即是事件 key, value 是 key 对应的值
     */
    @Override
    public void track(String event, String describe, HashMap<String, String> params) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method trackMethod = getSQwanCoreImplClass().getMethod("track", String.class, String.class, HashMap.class);
            trackMethod.invoke(sdkImplObject, event, describe, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public SQSdkInterface getPlatform(Context context, InitBean bean, SQResultListener initListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return null;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method method = getSQwanCoreImplClass().getMethod("getPlatform",
                Context.class, InitBean.class, SQResultListenerClass);
            return (SQSdkInterface) method.invoke(sdkImplObject, context, bean, listenerWrapper(initListener));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void login(Context context, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method loginMethod = getSQwanCoreImplClass().getMethod("login", Context.class, SQResultListenerClass);
            loginMethod.invoke(sdkImplObject, context, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void changeAccount(Context context, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method changeAccountMethod = getSQwanCoreImplClass().getMethod("changeAccount", Context.class, SQResultListenerClass);
            changeAccountMethod.invoke(sdkImplObject, context, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setSwitchAccountListener(SQResultListener sqResultListener) {
        voidListenerMethodCall("setSwitchAccountListener", sqResultListener);
    }

    @Override
    public void setBackToGameLoginListener(SQResultListener sqResultListener) {
        voidListenerMethodCall("setBackToGameLoginListener", sqResultListener);
    }

    @Override
    public void showSQWebDialog(String s) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method showSQWebDialogMethod = getSQwanCoreImplClass().getMethod("showSQWebDialog", String.class);
            showSQWebDialogMethod.invoke(sdkImplObject, s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showSQPersonalDialog(Context context) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method showSQPersonalDialogMethod = getSQwanCoreImplClass().getMethod("showSQPersonalDialog", Context.class);
            showSQPersonalDialogMethod.invoke(sdkImplObject, context);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void logout(Context context, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method logoutMethod = getSQwanCoreImplClass().getMethod("logout", Context.class, SQResultListenerClass);
            logoutMethod.invoke(sdkImplObject, context, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showExitDailog(Context context, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method showExitDailogMethod = getSQwanCoreImplClass().getMethod("showExitDailog", Context.class, SQResultListenerClass);
            showExitDailogMethod.invoke(sdkImplObject, context, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pay(Context context, String s, String s1, String s2, String s3, String s4, String s5, String s6, String s7, int i, float v, int i1, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method payMethod = getSQwanCoreImplClass().getMethod("pay", Context.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class, int.class, float.class, int.class, SQResultListenerClass);
            payMethod.invoke(sdkImplObject, context, s, s1, s2, s3, s4, s5, s6, s7, i, v, i1, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void submitRoleInfo(HashMap<String, String> hashMap) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method submitRoleInfoMethod = getSQwanCoreImplClass().getMethod("submitRoleInfo", HashMap.class);
            submitRoleInfoMethod.invoke(sdkImplObject, hashMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void creatRoleInfo(HashMap<String, String> hashMap) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method creatRoleInfoMethod = getSQwanCoreImplClass().getMethod("creatRoleInfo", HashMap.class);
            creatRoleInfoMethod.invoke(sdkImplObject, hashMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void upgradeRoleInfo(HashMap<String, String> hashMap) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method upgradeRoleInfoMethod = getSQwanCoreImplClass().getMethod("upgradeRoleInfo", HashMap.class);
            upgradeRoleInfoMethod.invoke(sdkImplObject, hashMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void creatRole(Context context, String s) {

    }

    @Override
    public void speechInit(Context context, SQResultListener sqResultListener) {

    }

    @Override
    public void setServerInfo(String s) {

    }

    @Override
    public void poll() {

    }

    @Override
    public void joinNationalRoom(String s, int i, int i1) {

    }

    @Override
    public void joinTeamRoom(String s, int i) {

    }

    @Override
    public void quitRoom(String s, int i) {

    }

    @Override
    public void openMic() {

    }

    @Override
    public void closeMic() {

    }

    @Override
    public void openSpeaker() {

    }

    @Override
    public void closeSpeaker() {

    }

    @Override
    public void setMicLevel(int i) {

    }

    @Override
    public int getMicLevel() {
        return 0;
    }

    @Override
    public void setSpeakerVolume(int i) {

    }

    @Override
    public int getSpeakerVolume() {
        return 0;
    }

    @Override
    public boolean testMic() {
        return false;
    }

    @Override
    public void enableSpeakerOn(boolean b) {

    }

    @Override
    public void forbidMemberVoice(int i, boolean b) {

    }

    @Override
    public void onJoinRoomListener(Context context, SQResultListener sqResultListener) {

    }

    @Override
    public void onQuitRoomListener(Context context, SQResultListener sqResultListener) {

    }

    @Override
    public void onMemberVoiceListener(Context context, SQResultListener sqResultListener) {

    }

    @Override
    public void onStatusUpdateListener(Context context, SQResultListener sqResultListener) {

    }

    @Override
    public void onStart() {
        voidMethodCall("onStart");
    }

    @Override
    public void onResume() {
        voidMethodCall("onResume");
    }

    @Override
    public void onPause() {
        voidMethodCall("onPause");
    }

    @Override
    public void onStop() {
        voidMethodCall("onStop");
    }

    @Override
    public void onDestroy() {
        voidMethodCall("onDestroy");
    }

    @Override
    public void onRestart() {
        voidMethodCall("onRestart");
    }

    @Override
    public void onActivityResult(int i, int i1, Intent intent) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method onActivityResultMethod = getSQwanCoreImplClass().getMethod("onActivityResult", int.class, int.class, Intent.class);
            onActivityResultMethod.invoke(sdkImplObject, i, i1, intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onNewIntent(Intent intent) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method onNewIntentMethod = getSQwanCoreImplClass().getMethod("onNewIntent", Intent.class);
            onNewIntentMethod.invoke(sdkImplObject, intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setContext(Context context) {

    }

    @Override
    public void performFeatureBBS() {
        voidMethodCall("performFeatureBBS");
    }

    @Override
    public void performFeatureVPlayer() {
        voidMethodCall("performFeatureVPlayer");
    }

    @Override
    public void performFeature(Context context, String s, Object o, SQResultListener sqResultListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method performFeatureMethod = getSQwanCoreImplClass().getMethod("performFeature", Context.class, String.class, Object.class, SQResultListenerClass);
            performFeatureMethod.invoke(sdkImplObject, context, s, o, listenerWrapper(sqResultListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setScreenshotListener(final IScreenshotListener iScreenshotListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        ApkClassLoader classLoader = getApkClassLoader();
        if (classLoader == null) {
            return;
        }

        try {
            Class IScreenshotListenerClass = getClass("com.sqwan.msdk.api.tool.IScreenshotListener");
            Method setScreenshotListenerClass = getSQwanCoreImplClass().getMethod("setScreenshotListener", IScreenshotListenerClass);
            setScreenshotListenerClass.invoke(sdkImplObject, Proxy.newProxyInstance(classLoader, new Class[]{IScreenshotListenerClass}, new InvocationHandler() {
                @Override
                public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                    return iScreenshotListener.createScreenshot();
                }
            }));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Resources getResources(Resources hostResource) {
        if (!isSupportPlugin() || PluginLoader.getInstance().get() == null || PluginLoader.getInstance().get().getResource() == null) {
            return hostResource;
        } else {
            if (pluginRes == null) {
                pluginRes = PluginLoader.getInstance().get().getResource();
                //初始设置根据业务配置来设置横竖屏，避免第一次获取是错误的值导致UE4引擎横竖屏拉伸错误
                Configuration configuration = this.pluginRes.getConfiguration();
                configuration.orientation = BusinessUtils.getScreenOrientation(this.pluginRes.getAssets());
                this.pluginRes.getConfiguration().setTo(configuration);
            }
            return pluginRes;
        }
    }

    @Override
    public AssetManager getAssets(AssetManager hostAssetManager) {
        if (!isSupportPlugin() || PluginLoader.getInstance().get() == null || PluginLoader.getInstance().get().getResource() == null) {
            return hostAssetManager;
        }

        Resources resource = PluginLoader.getInstance().get().getResource();
        if (resource == null) {
            return hostAssetManager;
        }

        return resource.getAssets();
    }

    @Override
    public ClassLoader getClassLoader(ClassLoader hostClassLoader) {
        if (!isSupportPlugin() || PluginLoader.getInstance().get() == null || PluginLoader.getInstance().get().getClassLoader() == null) {
            return hostClassLoader;
        } else {
            return PluginLoader.getInstance().get().getClassLoader();
        }
    }

    public void reportMDev(String identify) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method reportMDevMethod = getSQwanCoreImplClass().getMethod("reportMDev", String.class);
            reportMDevMethod.invoke(sdkImplObject, identify);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void startActivity(Intent intent) {
        PluginExActivityHandler.getInstance().handlerActivityIntent(mContext, intent, PluginLoader.getInstance().get().getClassLoader());
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        PluginExActivityHandler.getInstance().handlerActivityIntent(mContext, intent, PluginLoader.getInstance().get().getClassLoader());
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method initMethod = getSQwanCoreImplClass().getMethod("onRequestPermissionsResult", int.class, String[].class, int[].class);
            initMethod.invoke(sdkImplObject, requestCode, permissions, grantResults);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setDebug(Boolean stat) {

    }

    /**
     * 打印日志
     *
     * @param logString
     */
    public static void sendLog(String logString) {
        Log.i("sqsdk_m", logString);
    }

    public static void sendLogNoDebug(String logString) {
        Log.i("sqsdk_m", logString);
    }

    public static void sendLog(String logString, int ecode) {
        Log.i("sqsdk_m", logString);
    }

    public static void sendLogBase4CP(String log) {
        Log.i("sqsdk_m", log);
    }

    public static void sendLogPlat4CP(String log) {
        Log.i("sqsdk_m", log);
    }

    public SQAppConfig getAppConfig() {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return null;
        }

        try {
            Class ISQAppConfigClass = getClass("com.sqwan.msdk.api.ISQAppConfig");
            Method initMethod = getSQwanCoreImplClass().getMethod("getAppConfig");
            Object base = initMethod.invoke(sdkImplObject);
            if (base == null) {
                return null;
            }
            Method getGameidMethod = ISQAppConfigClass.getMethod("getGameid");
            Method getPartnerMethod = ISQAppConfigClass.getMethod("getPartner");
            Method getReferMethod = ISQAppConfigClass.getMethod("getRefer");
            String gameId = (String) getGameidMethod.invoke(base);
            String partner = (String) getPartnerMethod.invoke(base);
            String refer = (String) getReferMethod.invoke(base);
            return new SQAppConfig(gameId, partner, refer);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public boolean isSupportPlugin() {
        return true;
    }

    public Class getClass(String className) {
        if (mReflectClassMap.get(className) != null) {
            return mReflectClassMap.get(className);
        } else {
            ApkClassLoader classLoader = getApkClassLoader();
            if (classLoader == null) {
                return null;
            }
            try {
                Class clazz = classLoader.loadClass(className);
                mReflectClassMap.put(className, clazz);
                return clazz;
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 动态代理生成listener
     *
     * @param listener
     * @return
     */
    private Object listenerWrapper(final SQResultListener listener) {
        ApkClassLoader classLoader = getApkClassLoader();
        if (classLoader == null) {
            return null;
        }
        Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
        return Proxy.newProxyInstance(classLoader, new Class[]{SQResultListenerClass}, new InvocationHandler() {
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                if (method.getName().equals("onSuccess")) {
                    listener.onSuccess((Bundle) args[0]);
                } else {
                    listener.onFailture((Integer) args[0], (String) args[1]);
                }
                return null;
            }
        });
    }


    private void voidMethodCall(String methodName) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method voidStringMethod = getSQwanCoreImplClass().getMethod(methodName);
            voidStringMethod.invoke(sdkImplObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void voidListenerMethodCall(String methodName, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method voidListnerMethod = getSQwanCoreImplClass().getMethod(methodName, SQResultListenerClass);
            voidListnerMethod.invoke(sdkImplObject, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void shareToWX(Context context, String title, String description, String shareUrl, String thumbUrl,
        int resourceType, SQResultListener shareListener) {
        // todo 插件sdk一直没有这个api, 如果要支持分享到微信, 还要处理微信Activity, 所以暂时保持空实现
    }

    public void share(String inviteCode, SQResultListener shareListener) {
        share(inviteCode, "", shareListener);
    }

    public void share(String inviteCode, String img_id, SQResultListener shareListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method onShareMethod = getSQwanCoreImplClass().getMethod("share", String.class, String.class, getClass(SQRESULT_LISTENER_CLASS));
            onShareMethod.invoke(sdkImplObject, inviteCode, img_id, listenerWrapper(shareListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通用分享接口
     */
    public void share(ShareMessage shareMessage, SQResultListener shareListener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method onShareMethod = getSQwanCoreImplClass().getMethod("share", ShareMessage.class, getClass(SQRESULT_LISTENER_CLASS));
            onShareMethod.invoke(sdkImplObject, shareMessage, listenerWrapper(shareListener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void log(int level, String msg) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method method = getSQwanCoreImplClass().getMethod("log", int.class, String.class);
            method.invoke(sdkImplObject, level, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showUAgreement(Context context) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method showUAgreementMethod = getSQwanCoreImplClass().getMethod("showUAgreement", Context.class);
            showUAgreementMethod.invoke(sdkImplObject, context);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {

    }

    @Override
    public boolean isSupportLiveVideo() {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return false;
        }

        try {
            Method method = getSQwanCoreImplClass().getMethod("isSupportLiveVideo");
            method.setAccessible(true);
            return (boolean) method.invoke(sdkImplObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void joinLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method joinRoomMethod = getSQwanCoreImplClass().getMethod("joinLiveshowRoom", Map.class, SQResultListenerClass);
            joinRoomMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method leaveRoomMethod = getSQwanCoreImplClass().getMethod("leaveLiveshowRoom", Map.class, SQResultListenerClass);
            leaveRoomMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method setLiveshowDestroyCallbackMethod = getSQwanCoreImplClass().getMethod("setLiveshowDestroyCallback", SQResultListenerClass);
            setLiveshowDestroyCallbackMethod.invoke(sdkImplObject, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method setLiveshowVoiceChangeCallbackkMethod = getSQwanCoreImplClass().getMethod("setLiveshowVoiceChangeCallback", SQResultListenerClass);
            setLiveshowVoiceChangeCallbackkMethod.invoke(sdkImplObject, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method performLiveshowFeatureMethod = getSQwanCoreImplClass().getMethod("performLiveshowFeature", Map.class, SQResultListenerClass);
            performLiveshowFeatureMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isSupportLiveRadio() {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return false;
        }

        try {
            Method method = getSQwanCoreImplClass().getMethod("isSupportLiveRadio");
            method.setAccessible(true);
            return (boolean) method.invoke(sdkImplObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void joinLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method joinRoomMethod = getSQwanCoreImplClass().getMethod("joinLiveRadioRoom", Map.class, SQResultListenerClass);
            joinRoomMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void leaveLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method leaveRoomMethod = getSQwanCoreImplClass().getMethod("leaveLiveRadioRoom", Map.class, SQResultListenerClass);
            leaveRoomMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setLiveRadioDestroyCallback(SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method setLiveRadioDestroyCallbackMethod = getSQwanCoreImplClass().getMethod("setLiveRadioDestroyCallback", SQResultListenerClass);
            setLiveRadioDestroyCallbackMethod.invoke(sdkImplObject, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setLiveRadioVoiceChangeCallback(SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method setLiveRadioVoiceChangeCallbackkMethod = getSQwanCoreImplClass().getMethod("setLiveRadioVoiceChangeCallback", SQResultListenerClass);
            setLiveRadioVoiceChangeCallbackkMethod.invoke(sdkImplObject, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void performLiveRadioFeature(Map<String, String> data, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method performLiveRadioFeatureMethod = getSQwanCoreImplClass().getMethod("performLiveRadioFeature", Map.class, SQResultListenerClass);
            performLiveRadioFeatureMethod.invoke(sdkImplObject, data, listenerWrapper(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void printLog(int level, String tag, String content) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method method = getSQwanCoreImplClass().getMethod("printLog", int.class, String.class, String.class);
            method.invoke(sdkImplObject, level, tag, content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ApkClassLoader getApkClassLoader() {
        Plugin plugin = PluginLoader.getInstance().get();
        if (plugin == null) {
            PluginLog.e("PluginLoader.getInstance().get() 类还没有初始化", new Exception());
            return null;
        }
        ApkClassLoader classLoader = plugin.mClassLoader;
        if (classLoader == null) {
            PluginLog.e("PluginLoader.getInstance().get().mClassLoader 还没有初始化", new Exception());
        }
        return classLoader;
    }

    private Class getSQwanCoreImplClass() {
        Class clazz = getClass(SDK_CLASS);
        if (clazz == null) {
            PluginLog.e("sdk 核心类加载失败", new Exception(SDK_CLASS + "类加载失败"));
        }
        return clazz;
    }

    private Object getSQwanCoreImpl() {
        try {
            Class<?> clazz = getSQwanCoreImplClass();
            if (clazz != null) {
                Method sdkGetMethod = clazz.getMethod(SDK_GET_METHOD);
                Object object = sdkGetMethod.invoke(null);
                return object;
            }
        } catch (Exception e) {
            PluginLog.e("sdk 核心类实例加载异常", e);
        }
        return null;
    }

    @Override
    public void setSQPushTransmitMessageListener(SQPushTransmitMessageListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Method method = sdkImplObject.getClass().getMethod(
                "setSQPushTransmitMessageListener", SQPushTransmitMessageListener.class);
            method.invoke(sdkImplObject, listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showAdReward(Context context, String params, SQResultListener listener) {
        Object sdkImplObject = getSQwanCoreImpl();
        if (sdkImplObject == null) {
            return;
        }

        try {
            Class SQResultListenerClass = getClass(SQRESULT_LISTENER_CLASS);
            Method method = sdkImplObject.getClass().getMethod(
                "showAdReward", Context.class, String.class, SQResultListenerClass);
            method.invoke(sdkImplObject, context, params, listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
