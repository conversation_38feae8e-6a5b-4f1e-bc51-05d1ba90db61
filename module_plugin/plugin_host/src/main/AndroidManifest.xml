<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.sq.plugin_host">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
  <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
  <!-- 支付宝 权限 end -->
  <!--必要权限，解决安全风险漏洞，发送和注册广播事件需要调用带有传递权限的接口-->
  <permission  android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN"
    android:protectionLevel="signature" />

  <uses-permission android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN" />

  <!-- V2.1新增，用于短信注册账号 -->
  <uses-permission android:name="android.permission.SEND_SMS" />

  <!-- target26 更新包需要 -->
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

  <uses-permission
    android:name="android.permission.QUERY_ALL_PACKAGES"
    tools:ignore="QueryAllPackagesPermission" />

  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
  <!-- sqwan End -->
  <application android:usesCleartextTraffic="true">
    <activity
      android:name="com.host.PluginActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="adjustResize" />

    <activity
      android:name="com.host.LandscapePluginActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="landscape"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="adjustResize" />

    <activity
      android:name="com.host.BehindPluginActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="behind"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="adjustResize" />

    <activity
      android:name="com.host.PluginExActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:theme="@android:style/Theme.Translucent"
      android:windowSoftInputMode="stateVisible|adjustResize" />

    <meta-data
      android:name="android.max_aspect"
      android:value="2.4" />
    <meta-data
      android:name="android.notch_support"
      android:value="true" />

    <meta-data
      android:name="BUGLESS_APP_SECRET"
      android:value="jdjfqwec-xr5g-zgfr-fdvb-zelplvzo" />
    <meta-data
      android:name="BUGLESS_APP_ID"
      android:value="40d4a67cfe" />

    <!-- liveshow start       -->
    <activity
      android:name="com.sqwan.liveshow.ui.LiveshowRoomActivity"
      android:configChanges="keyboardHidden|orientation|screenSize"
      android:screenOrientation="portrait"
      android:theme="@android:style/Theme.Black.NoTitleBar"
      android:windowSoftInputMode="adjustResize|stateHidden" />

    <!-- liveshow end       -->

    <!-- 提供给阿里闪验使用的用户协议页面 -->
    <activity
      android:name="com.sqwan.common.web.UserProtocolWebPluginActivity"
      android:configChanges="orientation|keyboardHidden|screenSize|uiMode|fontScale"
      android:theme="@style/protocol_activity_dialog">
      <intent-filter>
        <action android:name="${applicationId}.protocolWeb" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
    </activity>

    <meta-data
      android:name="com.huya.berry.LiveGlideModule"
      android:value="GlideModule" />
    <meta-data
      android:name="NS_APPID"
      android:value="live_android" />
    <meta-data
      android:name="CLIENT_TYPE"
      android:value="adr_game_sdk" />

    <!--        hy liveshow start-->
    <meta-data
      android:name="HY_CHANNEL"
      android:value="official" />
    <meta-data
      android:name="HY_APPID"
      android:value="\5127" />
    <meta-data
      android:name="HY_APPKEY"
      android:value="102196135d57d1dbfbd78bfb435d3940" />
    <meta-data
      android:name="HY_VERSION"
      android:value="2.5.5" />
    <meta-data
      android:name="FB_APPID"
      android:value="\700" />
    <!--        hy liveshow end-->

    <provider
      android:name="com.sqwan.msdk.provider.SqFileProvider"
      android:authorities="${applicationId}.provider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/provider_file"/>
    </provider>

    <!--Weixin start-->
    <activity android:name="com.social.sdk.sso.wechat.WXCallbackActivity" />
    <activity-alias
      android:name="${applicationId}.wxapi.WXEntryActivity"
      android:exported="true"
      android:configChanges="orientation|keyboardHidden|navigation|screenSize"
      android:launchMode="singleTask"
      android:taskAffinity="com.sy37.sdk.demo.diff"
      android:targetActivity="com.social.sdk.sso.wechat.WXCallbackActivity"
      android:theme="@android:style/Theme.Translucent.NoTitleBar" />
    <activity
      android:screenOrientation="portrait"
      android:name="com.social.sdk.sso.wechat.WXQRActivity"
      android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
    <!--Weixin end-->

    <!--SDK版本标识 机审过程使用，与SDK业务无关
    sdkType
    37：sqsdk
    官斗：gdsdk
    捷诚：jcsdk
    -->
    <meta-data
      android:name="__sq_sdk_type"
      android:value="sqsdk" />
    <meta-data
      android:name="__sq_sdk_version"
      android:value="3.7.3" />

    <!-- 第三方登录和分享用到, 原本属于share/account的业务模块, 但是重构前产物有, 所以保留 -->
    <meta-data
      android:name="wx_appid"
      android:value="wx2d611131c6591acd" />
    <meta-data
      android:name="wx_appkey"
      android:value="aeced70482e18d2575da3fc0e513eb55" />
    <meta-data
      android:name="qq_appid"
      android:value="tencent101495075" />
    <!-- end -->

    <provider
      android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
      android:authorities="${applicationId}.TTFileProvider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
    </provider>
  </application>

</manifest>