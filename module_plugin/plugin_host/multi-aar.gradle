def getPathByClassName(String className) {
    return className.replace(".","/")+".class"
}

/**
 * 删除multi工程冲突代码
 * 广告线不需要这段配置
 */
if (get_isMultiAAR()) {
    this.afterEvaluate {
        android.libraryVariants.all { variant ->
            variant.javaCompiler.doLast {
                println "开始操作删除文件"
                String[] strings = ["com.sqwan.msdk.SQwanCore"]
                String baseDir = project.buildDir.absolutePath + "/intermediates/javac/release/compileReleaseJavaWithJavac/classes/"
                for (String fileName : strings) {
                    File file = new File(baseDir + getPathByClassName(fileName))
                    print("文件路径： " + file.absolutePath)
                    if(file.exists()) {
                        file.delete()
                        println "删除" + fileName
                    } else {
                        println "要删除的文件不存在"
                    }
                }
            }
        }
    }
}