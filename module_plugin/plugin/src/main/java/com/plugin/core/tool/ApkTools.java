package com.plugin.core.tool;

import java.io.File;
import java.io.IOException;
import java.util.zip.ZipFile;

public class ApkTools {

    public static boolean isApkValid(String path) {
        File apkFile = new File(path);
        if (!apkFile.exists()) {
            return false;
        }
        if (!isValidZipFile(path)) {
            return false;
        }
        return true;
    }

    private static boolean isValidZipFile(String file) {
        ZipFile zf = null;
        try {
            zf = new ZipFile(file);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (zf!=null) {
                try {
                    zf.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return zf!=null;
    }

}
