package com.plugin.core.resources;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.os.Build;
import java.lang.reflect.Method;

/**
 * 宿主Resources+插件内的资源
 * <AUTHOR>
 * @date 2020-09-29
 */
public class SuperHostResources {

    private Context mContext;

    private Resources mResources;

    private Integer mPluginApkCookie;

    public SuperHostResources(Context context, String pluginPath) {
        mContext = context;
        mResources = buildHostResources(pluginPath);
    }


    private Resources buildHostResources(String pluginPath) {
        Resources hostResources;
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT) {
            try {
                AssetManager assetManager = mContext.getResources().getAssets();
                Method addAssetPathMethod = AssetManager.class.getDeclaredMethod("addAssetPath", String.class);
                addAssetPathMethod.setAccessible(true);
                mPluginApkCookie = (Integer) addAssetPathMethod.invoke(assetManager, pluginPath);
                hostResources = new Resources(assetManager, mContext.getResources().getDisplayMetrics(), mContext.getResources().getConfiguration());
            } catch (Exception e) {
                e.printStackTrace();
                hostResources = mContext.getResources();
            }
        } else {
            try {
                AssetManager assetManager = AssetManager.class.newInstance();
                Method addAssetPathMethod = AssetManager.class.getDeclaredMethod("addAssetPath", String.class);
                addAssetPathMethod.setAccessible(true);
                addAssetPathMethod.invoke(assetManager, pluginPath);
                String baseApkPath = mContext.getApplicationInfo().sourceDir;
                mPluginApkCookie = (Integer) addAssetPathMethod.invoke(assetManager, baseApkPath);
                hostResources = new Resources(assetManager, mContext.getResources().getDisplayMetrics(), mContext.getResources().getConfiguration());
            } catch (Exception e) {
                e.printStackTrace();
                hostResources = mContext.getResources();
            }
        }

        return hostResources;
    }

    public Resources get() {
        return mResources;
    }

    public Integer getPluginApkCookie() {
        return mPluginApkCookie;
    }
}
