package com.plugin.core;

import android.content.Context;
import android.text.TextUtils;
import com.plugin.core.tool.PluginLog;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020-09-24
 */
public class PluginMultiSdkManager {

    private static final String MULTI_SDK = "multi_sdk";

    private static final String PRO_KEY_SKIN = "defaultSkin";
    private static final String PRO_KEY_CONFIG = "config";
    private static final String PRO_KEY_INFO = "info";
    private static final String PRO_KEY_HOST = "host";
    private static final String PRO_KEY_TAG = "logTag";
    private static final String PRO_KEY_ACCOUNT_DIR = "accountDir";
    private static final String PRO_KEY_ACCOUNT_FILE = "accountFile";
    private static final String PRO_KEY_SCUT3 = "scut3";
    private static final String PRO_KEY_PLUGIN = "defaultPlugin";
    private static final String PRO_KEY_PLUGIN_VERSION = "defaultPluginVersion";

    private String mSkin;
    private String mConfig;
    private String mInfo;
    private String mHost;
    private String mTag;
    private String mAccountDir;
    private String mAccountFile;
    private String mDefaultPlugin;
    private int mDefaultPluginVersion;

    /**
     * 如果参数里面有这个sdk类型的配置，则需要使用 如果没有，则表示为默认37sdk，不需要传递此参数
     */
    private String mScut3;

    /**
     * 统一域名配置
     *
     */
    public static String APP_HOST = "37.com.cn";


    private static volatile PluginMultiSdkManager instance;

    private PluginMultiSdkManager() {
    }

    public static PluginMultiSdkManager getInstance() {
        if(instance == null) {
            synchronized (PluginMultiSdkManager.class) {
                if(instance == null) {
                    instance = new PluginMultiSdkManager();
                }
            }
        }
        return instance;
    }

    public void initMultiSdk(Context context) {
        Properties pro = readProperties(context);
        if(pro == null) {
            PluginLog.e("multi_sdk配置读取失败");
            return;
        }
        mSkin = pro.getProperty(PRO_KEY_SKIN);
        mConfig = pro.getProperty(PRO_KEY_CONFIG);
        mInfo = pro.getProperty(PRO_KEY_INFO);
        mHost = pro.getProperty(PRO_KEY_HOST);
        mTag = pro.getProperty(PRO_KEY_TAG);
        mAccountDir = pro.getProperty(PRO_KEY_ACCOUNT_DIR);
        mAccountFile = pro.getProperty(PRO_KEY_ACCOUNT_FILE);
        mScut3 = pro.getProperty(PRO_KEY_SCUT3);
        mDefaultPlugin = pro.getProperty(PRO_KEY_PLUGIN);
        mDefaultPluginVersion = Integer.parseInt(pro.getProperty(PRO_KEY_PLUGIN_VERSION) == null ? "0" : pro.getProperty(PRO_KEY_PLUGIN_VERSION));
        PluginLog.i("multi_sdk config: skin:" + mSkin + ", config:" + mConfig + ", info:" + mInfo + ", host:" + mHost
                + ", tag:" + mTag + ", accountDir:" + mAccountDir + ", accountFile:" + mAccountFile + ", scut3:" + mScut3
                + ", defaultPlugin: " + mDefaultPlugin
                + ", defaultPluginVersion: " + mDefaultPluginVersion);
    }


    private Properties readProperties(Context context) {
        Properties p = null;
        InputStream in = null;
        try {
            in = context.getResources().getAssets().open(MULTI_SDK);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (in!=null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return p;
    }

    public String getSkin() {
        return mSkin;
    }

    public String getConfig() {
        return mConfig;
    }

    public String getInfo() {
        return mInfo;
    }

    public String getHost() {
        return mHost;
    }

    public String getTag() {
        return mTag;
    }

    public String getAccountDir() {
        return mAccountDir;
    }

    public String getAccountFile() {
        return mAccountFile;
    }

    public String getScut3() {
        return mScut3;
    }

    public boolean isScut3() {
        return !TextUtils.isEmpty(mScut3);
    }

    public String getDefaultPlugin() {
        return mDefaultPlugin;
    }

    public int getDefaultPluginVersion() {
        return mDefaultPluginVersion;
    }
}
