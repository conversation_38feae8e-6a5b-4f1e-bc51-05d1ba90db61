package com.plugin.core.manifest;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.content.res.XmlResourceParser;
import android.text.TextUtils;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.ArrayList;

/**
 *    author : 黄锦群
 *    time   : 2022/08/11
 *    desc   : 广播接收者处理策略
 */
public final class ReceiverStrategy implements IComponentStrategy<AndroidManifestInfo.ReceiverInfo> {

   @Override
   public String getXmlTag() {
      return AndroidManifestConstants.TAG_RECEIVER;
   }

   @Override
   public AndroidManifestInfo.ReceiverInfo parseFromXml(XmlResourceParser xmlResourceParser)
           throws XmlPullParserException, IOException {

      AndroidManifestInfo.ReceiverInfo receiverInfo = new AndroidManifestInfo.ReceiverInfo();
      receiverInfo.name = xmlResourceParser.getAttributeValue(
              AndroidManifestConstants.ANDROID_NAMESPACE_URI, AndroidManifestConstants.ATTR_NAME);

      while (xmlResourceParser.next() != XmlResourceParser.END_DOCUMENT) {
         String xmlKeyName = xmlResourceParser.getName();
         if (xmlResourceParser.getEventType() != XmlResourceParser.START_TAG) {
            if (getXmlTag().equals(xmlKeyName) ||
                    AndroidManifestConstants.TAG_INTENT_FILTER.equals(xmlKeyName)) {
               // 如果已经到 receiver 标签尾部了，则直接返回
               break;
            } else {
               // 如果是其他类型的尾部标签，则不进行解析
               continue;
            }
         }

         // 如果当前标签为 intent-filter
         if (AndroidManifestConstants.TAG_INTENT_FILTER.equals(xmlKeyName)) {
            // 解析出广播接收者的优先级，默认优先级为 0
            receiverInfo.priority = xmlResourceParser.getAttributeIntValue(
                    AndroidManifestConstants.ANDROID_NAMESPACE_URI,
                    AndroidManifestConstants.ATTR_PRIORITY, 0);
         }

         // 如果当前标签为 action
         if (AndroidManifestConstants.TAG_ACTION.equals(xmlKeyName)) {
            if (receiverInfo.actions == null) {
               receiverInfo.actions = new ArrayList<>();
            }

            // 解析广播接收者的意图
            String action = xmlResourceParser.getAttributeValue(
                    AndroidManifestConstants.ANDROID_NAMESPACE_URI, AndroidManifestConstants.ATTR_NAME);
            if (!TextUtils.isEmpty(action)) {
               receiverInfo.actions.add(action);
            }
         }
      }

      return receiverInfo;
   }

   @Override
   public void initializing(Context context, ClassLoader classLoader,
                            AndroidManifestInfo.ReceiverInfo receiverInfo) {
      try {
         IntentFilter intentFiler = new IntentFilter();
         // 设置广播接收者的优先级
         intentFiler.setPriority(receiverInfo.priority);
         if (receiverInfo.actions != null && !receiverInfo.actions.isEmpty()) {
            for (String action : receiverInfo.actions) {
               // 添加广播接收者的意图
               intentFiler.addAction(action);
            }
            // 实例化广播接收者的对象
            BroadcastReceiver receiver = (BroadcastReceiver)
                    classLoader.loadClass(receiverInfo.name).newInstance();
            // 动态注册广播接收者
            context.registerReceiver(receiver, intentFiler);
         }
      } catch (Exception e) {
         e.printStackTrace();
      }
   }
}
