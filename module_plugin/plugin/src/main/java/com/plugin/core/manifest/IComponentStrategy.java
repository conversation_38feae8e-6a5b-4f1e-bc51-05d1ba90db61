package com.plugin.core.manifest;

import android.content.Context;
import android.content.res.XmlResourceParser;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;

/**
 *    author : 黄锦群
 *    time   : 2022/08/11
 *    desc   : 组件处理策略
 */
public interface IComponentStrategy<T> {

    /**
     * 获取组件在 xml 中的标识
     */
    String getXmlTag();

    /**
     * 从清单文件中解析组件
     */
    T parseFromXml(XmlResourceParser parser) throws XmlPullParserException, IOException;

    /**
     * 初始化组件
     */
    void initializing(Context context, ClassLoader classLoader, T t);
}