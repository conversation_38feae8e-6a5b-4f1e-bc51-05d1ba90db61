package com.plugin.core;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.os.SystemClock;
import android.text.TextUtils;
import com.plugin.core.loader.ApkClassLoader;
import com.plugin.core.manifest.AndroidManifestInfo;
import com.plugin.core.manifest.AndroidManifestParser;
import com.plugin.core.resources.MixResources;
import com.plugin.core.resources.SuperHostResources;
import com.plugin.core.tool.ApkTools;
import com.plugin.core.tool.PluginLog;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 占位式插件
 */
public class PluginManager {

    private final String TAG = "PluginManager";
    private static final String MULTI_SDK = "multi_sdk";

    private static PluginManager sInstance;

    private Context mContext;

    /**
     * 插件名称
     */
    private String mPluginPath;


    //下载到的最新版本
    private final String KEY_LATEST_VERSION = "plugin_latest_version";

    //multi_sdk 中 default_plugin 的 key
    private static final String PRO_KEY_DEFAULT_PLUGIN = "defaultPlugin";

    //写入当前版本
    private final String KEY_CURRENT_VERSION = "plugin_current_version";

    //热更插件加载生效
    private final String KEY_HOTTER_EFFECT = "hotter_hotter_effect";

    //回滚生效
    private final String KEY_HOTTER_ROLLBACK_EFFECT = "hotter_rollback_effect";

    // 插件加载耗时
    private static final String KEY_HOTTER_CONSUMING = "hotter_consuming";

    public static final String SQ_PLUGIN_CONFIG = "sq_plugin_config";


    private String defaultPluginApkName = "default_plugin.apk";


    private PluginManager(Context context) {
        mContext = context;
    }

    public static PluginManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new PluginManager(context);
        }
        return sInstance;
    }

    public Plugin loadPlugin() {
        PluginLog.d("开始加载插件");
        //初始化multi_sdk配置文件
        PluginMultiSdkManager.getInstance().initMultiSdk(mContext);
        //加载下发的插件
        SharedPreferences sp = mContext.getSharedPreferences(SQ_PLUGIN_CONFIG, Context.MODE_PRIVATE);
        int pluginVersion = sp.getInt(KEY_LATEST_VERSION, -1);
        int defaultPluginVersion = PluginMultiSdkManager.getInstance().getDefaultPluginVersion();
        PluginLog.d("默认插件: " + defaultPluginVersion + ", 上次插件: " + pluginVersion);
        if (pluginVersion > defaultPluginVersion) {
            String configPluginPath = generateConfigPluginPath(pluginVersion);
            File configPluginFile = new File(configPluginPath);
            if (configPluginFile.exists() && isApkValid(configPluginPath)) {
                PluginLog.i("使用下发的插件: " + pluginVersion + ", " + configPluginPath);
                int currentPluginVersion = sp.getInt(KEY_CURRENT_VERSION, -1);
                PluginLog.d("当前插件: " + currentPluginVersion + ", 即将加载的插件: " + pluginVersion);
                mPluginPath = configPluginPath;
                if (pluginVersion != currentPluginVersion) {
                    //如果插件版本号与当前插件版本不一致，埋点，用于后续初始化后再上报
                    sp.edit().putString(KEY_HOTTER_EFFECT, currentPluginVersion + "").apply();
                }
                sp.edit().putInt(KEY_CURRENT_VERSION, pluginVersion).apply();
                return loadPlugin(mPluginPath);
            }
        }
        //加载默认
        if (!TextUtils.isEmpty(PluginMultiSdkManager.getInstance().getDefaultPlugin())) {
            defaultPluginApkName = PluginMultiSdkManager.getInstance().getDefaultPlugin() + ".apk";
        }
        PluginLog.i("使用默认插件: " + defaultPluginApkName);
        mPluginPath = copyAssetPlugin(defaultPluginApkName, "plugin");
        sp.edit().putInt(KEY_CURRENT_VERSION, defaultPluginVersion).apply();
        return loadPlugin(mPluginPath);
    }


    private boolean isApkValid(String apkPath) {
        return ApkTools.isApkValid(apkPath);
    }

    private String generateConfigPluginPath(int version) {
        File pluginDirFile = mContext.getDir("plugin", Context.MODE_PRIVATE);
        return pluginDirFile.getAbsolutePath() + File.separator + "plugin_" + version + ".apk";
    }

    //加载默认plugin
    private Plugin loadPlugin(String pluginPath) {
        PluginLog.i("加载插件: " + pluginPath);
        Plugin plugin = new Plugin();
        plugin.setPluginPath(pluginPath);


        File apk = new File(pluginPath);
        String libDirPath = new File(apk.getParent(), "lib").getAbsolutePath();
        PluginLog.d("处理So");
        SoLibUtil.releaseSoFile(mContext, apk, libDirPath);

        long loadPluginApkStartTime = SystemClock.uptimeMillis();
        File file = mContext.getDir("plugin-opti", Context.MODE_PRIVATE);
        String[] interfaces = new String[]{"com.sqwan.msdk.api", "com.sqwan.msdk.api.tool", "com.sq.standard"};
        ApkClassLoader pluginClassLoader = new ApkClassLoader(pluginPath, file.getAbsolutePath(), libDirPath, mContext.getClassLoader(), interfaces);
        long loadPluginApkConsuming = SystemClock.uptimeMillis() - loadPluginApkStartTime;
        PluginLog.d("加载apk耗时" + loadPluginApkConsuming + "ms");
        SharedPreferences sp = mContext.getSharedPreferences(SQ_PLUGIN_CONFIG, Context.MODE_PRIVATE);
        sp.edit().putString(KEY_HOTTER_CONSUMING, loadPluginApkConsuming + "").apply();

        PluginLog.d("原始class loader: " + mContext.getClassLoader());
        PluginLog.d("插件class loader: " + pluginClassLoader);
        plugin.setClassLoader(pluginClassLoader);

        try {
            SuperHostResources superHostResources = new SuperHostResources(mContext, mPluginPath);
            Resources resources = new MixResources(superHostResources.get(), mContext, mPluginPath);
            plugin.setResources(resources);
            PluginLog.d("插件resources: " + resources);

            Integer pluginApkCookie = superHostResources.getPluginApkCookie();
            if (pluginApkCookie != null) {
                // 解析插件中的组件
                AndroidManifestInfo androidManifestInfo = AndroidManifestParser.parseAndroidManifest(mContext, pluginApkCookie);
                // 初始化插件中的组件
                AndroidManifestParser.initializingComponent(mContext, pluginClassLoader, androidManifestInfo);
            }
        } catch (Exception e) {
            PluginLog.e("加载插件异常", e);
        }
        PluginLog.i("加载插件完成: " + plugin);
//        new LoadedPlugin(pluginClassLoader,mContext,apk);
        return plugin;
    }

    String copyAssetPlugin(String assetName, String dirName) {
        InputStream inputStream = null;
        try {
            inputStream = mContext.getAssets().open(assetName);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (inputStream == null) {
            PluginLog.e("assets文件不存在: " + assetName);
            return null;
        }
        File pluginDirFile = mContext.getDir(dirName, Context.MODE_PRIVATE);
        if (!pluginDirFile.exists()) {
            pluginDirFile.mkdirs();
        }
        File resultFile = new File(pluginDirFile, assetName);
        writeInputStream(resultFile.getAbsolutePath(), inputStream);
        return resultFile.getAbsolutePath();
    }

    void writeInputStream(String storagePath, InputStream inputStream) {
        File file = new File(storagePath);
        FileOutputStream fos = null;
        try {
            if (!file.exists()) {
                fos = new FileOutputStream(file);
                byte[] buffer = new byte[inputStream.available()];
                int lenght;
                while ((lenght = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, lenght);
                }
                fos.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    public HashMap<String, String> getMultiSdkConfig() {
        HashMap<String, String> result = new HashMap<>();
        Properties pro = readProperties(mContext);
        if (pro == null) {
            PluginLog.e("multi_sdk配置不存在");
            return null;
        }
        for (Map.Entry<Object, Object> entry : pro.entrySet()) {
            result.put((String) entry.getKey(), (String) entry.getValue());
        }
        return result;
    }

    private Properties readProperties(Context context) {
        Properties p = null;
        InputStream in = null;
        try {
            in = context.getAssets().open(MULTI_SDK);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return p;
    }
}