package com.plugin.core.tool;

import android.util.Log;
import java.util.Locale;

/**
 * <AUTHOR>
 * @since 2024/2/6
 */
public final class PluginLog {

    public static String TAG = "SQ_Plugin";
    public static boolean DEBUG = Log.isLoggable("sysdk.debug.log.plugin", Log.DEBUG);
    public static boolean VERBOSE = Log.isLoggable("sysdk.debug.log.plugin", Log.VERBOSE);

    private PluginLog() {
    }

    public static void v(String msg) {
        if (VERBOSE) {
            Log.v(TAG, msg);
        }
    }

    public static void v(String format, Object... args) {
        if (VERBOSE) {
            Log.v(TAG, buildMessage(format, args));
        }
    }

    public static void d(String msg) {
        if (DEBUG) {
            Log.d(TAG, msg);
        }
    }

    public static void d(String format, Object... args) {
        if (DEBUG) {
            Log.d(TAG, buildMessage(format, args));
        }
    }

    public static void i(String msg) {
        if (DEBUG) {
            Log.i(TAG, msg);
        }
    }

    public static void i(String format, Object... args) {
        if (DEBUG) {
            Log.i(TAG, buildMessage(format, args));
        }
    }

    public static void w(String msg) {
        if (DEBUG) {
            Log.w(TAG, msg);
        }
    }

    public static void w(String msg, Throwable e) {
        if (DEBUG) {
            Log.w(TAG, msg, e);
        }
    }

    public static void w(Throwable tr, String format, Object... args) {
        if (DEBUG) {
            Log.w(TAG, buildMessage(format, args), tr);
        }
    }

    public static void e(String msg) {
        if (DEBUG) {
            Log.e(TAG, msg);
        }
    }

    public static void e(String msg, Throwable e) {
        if (DEBUG) {
            Log.e(TAG, msg, e);
        }
    }

    public static void e(String format, Object... args) {
        if (DEBUG) {
            Log.e(TAG, buildMessage(format, args));
        }
    }

    public static void e(Throwable tr, String format, Object... args) {
        if (DEBUG) {
            Log.e(TAG, buildMessage(format, args), tr);
        }
    }

    private static String buildMessage(String format, Object... args) {
        String msg = args == null ? format : String.format(Locale.US, format, args);
        return String.format(Locale.US, "[%d] %s", Thread.currentThread().getId(), msg);
    }
}
