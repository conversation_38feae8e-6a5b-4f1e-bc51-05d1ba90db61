package com.plugin.core;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import com.plugin.core.loader.ApkClassLoader;
import com.plugin.standard.IActivityInterface;


public class ProxyPluginActivity extends Activity {

    private final String TAG = getClass().getSimpleName();

    @Override
    public ApkClassLoader getClassLoader() {
        return null;
    }

    @Override
    public Resources getResources() {
        return super.getResources();
    }

    private IActivityInterface pluginActivity;

    @Override
    protected void onCreate( Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();

        if (intent != null && !TextUtils.isEmpty(intent.getStringExtra("activity"))) {
            try {
                pluginActivity = getClassLoader().getInterface(IActivityInterface.class, intent.getStringExtra("activity"));
                pluginActivity.insertAppContext(this);
                pluginActivity.onCreate(new Bundle());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            Log.e(TAG, "intent 中没带插件activity信息");
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (pluginActivity != null){
            pluginActivity.onStart();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (pluginActivity != null){
            pluginActivity.onResume();
        }
    }

    @Override
    public void startActivity(Intent intent) {
        super.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        super.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (pluginActivity != null) {
            pluginActivity.onPause();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (pluginActivity != null) {
            return pluginActivity.onKeyDown(keyCode, event);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (pluginActivity != null) {
            pluginActivity.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onDestroy() {
        if (pluginActivity != null) {
            pluginActivity.onDestroy();
        }
        super.onDestroy();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if(pluginActivity!=null){
            pluginActivity.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
}
