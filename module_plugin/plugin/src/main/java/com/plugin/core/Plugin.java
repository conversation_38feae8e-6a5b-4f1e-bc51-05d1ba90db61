package com.plugin.core;

import android.content.res.Resources;

import com.plugin.core.loader.ApkClassLoader;


/**
 * <AUTHOR>
 * 插件bean
 * 代表一个插件
 */
public class Plugin {

    public ApkClassLoader mClassLoader;

    public Resources mResource;

    public String mPath;

    public String getPluginPath() {
        return mPath;
    }

    public void setPluginPath(String path) {
        mPath = path;
    }

    public ClassLoader getClassLoader() {
        return mClassLoader;
    }

    public void setClassLoader(ApkClassLoader classLoader) {
        mClassLoader = classLoader;
    }

    public Resources getResource() {
        return mResource;
    }

    public void setResources(Resources resources) {
        mResource = resources;
    }
}
