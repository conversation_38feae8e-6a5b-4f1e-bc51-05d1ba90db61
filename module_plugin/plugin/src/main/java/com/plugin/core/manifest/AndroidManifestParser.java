package com.plugin.core.manifest;

import android.content.Context;
import android.content.res.XmlResourceParser;

import com.plugin.core.tool.PluginLog;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.ArrayList;

/**
 *    author : 黄锦群
 *    time   : 2022/08/11
 *    desc   : 清单文件解析器
 */
public final class AndroidManifestParser {

   /** 广播接收者组件处理策略 */
   private static final ReceiverStrategy RECEIVER_STRATEGY = new ReceiverStrategy();

   /**
    * 解析 apk 包中的清单文件
    *
    * @param context          上下文
    * @param apkCookie        要解析 apk 的 cookie
    */
   public static AndroidManifestInfo parseAndroidManifest(Context context, int apkCookie) throws IOException, XmlPullParserException {
      AndroidManifestInfo manifestInfo = new AndroidManifestInfo();
      XmlResourceParser xmlResourceParser = context.getAssets().
              openXmlResourceParser(apkCookie, AndroidManifestConstants.ANDROID_MANIFEST_FILE_NAME);

      do {
         // 当前节点必须为标签头部
         if (xmlResourceParser.getEventType() != XmlResourceParser.START_TAG) {
            continue;
         }

         // 如果解析到广播接收者
         if (RECEIVER_STRATEGY.getXmlTag().equals(xmlResourceParser.getName())) {
            // 那么就交给对应组件的策略去解析
            AndroidManifestInfo.ReceiverInfo receiverInfo = RECEIVER_STRATEGY.parseFromXml(xmlResourceParser);
            if (receiverInfo != null) {
               if (manifestInfo.receivers == null) {
                  manifestInfo.receivers = new ArrayList<>();
               }
               manifestInfo.receivers.add(receiverInfo);
            }
         }

      } while (xmlResourceParser.next() != XmlResourceParser.END_DOCUMENT);

      return manifestInfo;
   }

   /**
    * 初始化清单文件中的组件
    *
    * @param context                   应用上下文
    * @param classLoader               用于加载组件类的 ClassLoader 对象
    * @param androidManifestInfo       清单文件信息类
    */
   public static void initializingComponent(Context context, ClassLoader classLoader, AndroidManifestInfo androidManifestInfo) {
      // 初始化广播接收者
      if (androidManifestInfo.receivers != null) {
         // 遍历每个广播接收者
         for (int i = 0; i < androidManifestInfo.receivers.size(); i++) {
            AndroidManifestInfo.ReceiverInfo receiverInfo = androidManifestInfo.receivers.get(i);
            if (receiverInfo == null) {
               continue;
            }
            PluginLog.d("[Manifest]初始化广播组件: " + receiverInfo.name);
            // 初始化广播接收者
            RECEIVER_STRATEGY.initializing(context, classLoader, receiverInfo);
         }
      }
   }
}