package com.plugin.core;

import android.content.Context;
import android.text.TextUtils;
import com.plugin.core.tool.PluginLog;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * Created by znb on 2021-05-10
 */
public class SoLibUtil {


    private static final String TAG = "[So]";

    public static boolean releaseSoFile(Context context, File apkFile, String libDir) {
        File libDirFile = new File(libDir);
        if (!libDirFile.exists()) {
            libDirFile.mkdirs();
        }else{
            for (String s : libDirFile.list()) {
                new File(s).delete();
            }
        }
        String cpu_architect = ApkInfoUtil.chooseByX86andArm(context);
        PluginLog.d(TAG + "选择架构: " + cpu_architect);
        boolean isSucc = true;
        int soCount = 0;
        try {
            ZipFile zipFile = new ZipFile(apkFile);
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry zipEntry = entries.nextElement();
                if (zipEntry.isDirectory())
                    continue;
                String zipEntryName = zipEntry.getName();
                if (zipEntryName.endsWith(".so")) {
                    String[] entryNames = zipEntryName.split("/");
                    if (entryNames.length > 2) {
                        //cpu适配问题
                        String cpu_architect_dir = entryNames[1];
                        if (TextUtils.equals(cpu_architect_dir,cpu_architect)) {
                            soCount += 1;
                            isSucc = releaseSoFile(libDir, zipFile, zipEntry);
                        }
                        else{
//                            if (cpu_architect.contains("x86") && cpu_architect_dir.contains("x86") ||
//                                    cpu_architect.contains("arm") && cpu_architect_dir.contains("arm")) {
//                                Log.d(TAG, zipEntryName);
//                                Log.d(TAG, "cpu_architect= " + cpu_architect);
//                                isSucc = releaseSoFile(libDir, zipFile, zipEntry);
//                                Log.d(TAG, "copy so " + isSucc);
//                            }
                        }


                    }
                }

            }
        } catch (IOException e) {
            PluginLog.e(TAG + "so处理异常", e);
            return false;
        } finally {
            PluginLog.d(TAG + "共处理" + soCount + "个So文件");
        }
        return isSucc;

    }

    private static boolean releaseSoFile(String libDir, ZipFile zipFile, ZipEntry zipEntry) throws IOException {
        String zipEntryName = zipEntry.getName();
        InputStream inputStream = zipFile.getInputStream(zipEntry);
        File newSoFile = new File(libDir, parseSoFileName(zipEntryName));
        if (newSoFile.exists()) {
            // 文件已存在, 检查md5, 避免重复复制
            // 在多进程场景, 重复复制so有可能导致so内部报错, 例如oaid中的so
            String oldMd5 = md5(newSoFile);
            String newMd5 = md5(inputStream);
            if (!oldMd5.isEmpty() && oldMd5.equalsIgnoreCase(newMd5)) {
                PluginLog.v(TAG + zipEntryName + "已存在, 无需复制, " + oldMd5);
                return true;
            }
        }
        boolean isSucc = FileUtil.copytoFile(inputStream, newSoFile);
        if (isSucc) {
            PluginLog.v(TAG + "复制 " + zipEntryName);
        } else {
            PluginLog.e(TAG + "复制 " + zipEntryName + "失败");
        }
        return isSucc;
    }
    private static String parseSoFileName(String zipEntryName) {
        return zipEntryName.substring(zipEntryName.lastIndexOf("/") + 1);
    }

    // 理论上so处理事单线程, 所以共用bytes
    private static final byte[] BYTES = new byte[1024];

    private static String md5(File file) {
        InputStream in = null;
        try {
            in = new FileInputStream(file);
            return md5(in);
        } catch (Exception e) {
            return "";
        } finally {
            close(in);
        }
    }

    public static String md5(InputStream in) {
        DigestInputStream dis = null;
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Huh, MD5 should be supported?", e);
        }

        try {
            dis = new DigestInputStream(in, md);
            while (dis.read(BYTES) != -1);
        } catch (IOException e) {
            return "";
        } finally {
            close(dis);
        }
        byte[] hash = md.digest();
        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }

    private static void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                /* no-op */
            }
        }
    }
}

