package com.plugin.core;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;


public class FileUtil {
    private static final int MAX_BUFFER_SIZE = 10 * 1024;
    public static boolean copytoFile(InputStream in, File dest) {
        boolean isSucc = false;
        FileOutputStream fout = null;
        try {
            if (!dest.exists()) {
                dest.createNewFile();
            }

            fout = new FileOutputStream(dest);

            byte[] buf = new byte[MAX_BUFFER_SIZE];
            int len = -1;
            while ((len = in.read(buf)) > 0) {
                fout.write(buf, 0, len);
                fout.flush();
            }

            isSucc = true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {

            try {

                if (fout != null) {
                    fout.close();
                }

                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return isSucc;
    }
}
