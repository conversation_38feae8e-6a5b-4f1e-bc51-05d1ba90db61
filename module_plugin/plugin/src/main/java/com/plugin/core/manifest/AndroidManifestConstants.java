package com.plugin.core.manifest;

/**
 *    author : 黄锦群
 *    time   : 2022/08/11
 *    desc   : 清单文件契约类
 */
public final class AndroidManifestConstants {

   /** 清单文件的文件名称 */
   public static final String ANDROID_MANIFEST_FILE_NAME = "AndroidManifest.xml";

   /** Android 的命名空间 */
   public static final String ANDROID_NAMESPACE_URI = "http://schemas.android.com/apk/res/android";

   // Tags: Manifest
//   public static final String TAG_MANIFEST = "manifest";
//   public static final String TAG_SERVICE = "service";
//   public static final String TAG_PERMISSION = "permission";
//   public static final String TAG_PERMISSION_GROUP = "permission-group";
//   public static final String TAG_USES_FEATURE = "uses-feature";
//   public static final String TAG_USES_PERMISSION = "uses-permission";
//   public static final String TAG_USES_PERMISSION_SDK_23 = "uses-permission-sdk-23";
//   public static final String TAG_USES_PERMISSION_SDK_M = "uses-permission-sdk-m";
//   public static final String TAG_USES_LIBRARY = "uses-library";
//   public static final String TAG_APPLICATION = "application";
   public static final String TAG_INTENT_FILTER = "intent-filter";
//   public static final String TAG_CATEGORY = "category";
//   public static final String TAG_USES_SDK = "uses-sdk";
//   public static final String TAG_ACTIVITY = "activity";
//   public static final String TAG_ACTIVITY_ALIAS = "activity-alias";
   public static final String TAG_RECEIVER = "receiver";
//   public static final String TAG_PROVIDER = "provider";
//   public static final String TAG_GRANT_PERMISSION = "grant-uri-permission";
//   public static final String TAG_PATH_PERMISSION = "path-permission";
   public static final String TAG_ACTION = "action";
//   public static final String TAG_INSTRUMENTATION = "instrumentation";
//   public static final String TAG_META_DATA = "meta-data";
//   public static final String TAG_RESOURCE = "resource";
//   public static final String TAG_MODULE = "module";

   // Attributes: Resources
//   public static final String ATTR_ATTR = "attr";
   public static final String ATTR_NAME = "name";
//   public static final String ATTR_FRAGMENT = "fragment";
//   public static final String ATTR_TYPE = "type";
//   public static final String ATTR_PARENT = "parent";
//   public static final String ATTR_TRANSLATABLE = "translatable";
//   public static final String ATTR_COLOR = "color";
//   public static final String ATTR_DRAWABLE = "drawable";
//   public static final String ATTR_VALUE = "value";
//   public static final String ATTR_QUANTITY = "quantity";
//   public static final String ATTR_FORMAT = "format";
//   public static final String ATTR_PREPROCESSING = "preprocessing";
   public static final String ATTR_PRIORITY = "priority";
}