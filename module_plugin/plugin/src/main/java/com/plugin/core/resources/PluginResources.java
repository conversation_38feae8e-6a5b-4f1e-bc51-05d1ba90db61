package com.plugin.core.resources;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;

/**
 * <AUTHOR>
 * @date 2020-09-29
 */
public class PluginResources {

    private Context mContext;

    private String mPluginPath;

    private String mPluginPkgName;

    private Resources mResources;

    public PluginResources(Context context, String pluginPath) {
        mContext = context;
        mPluginPath = pluginPath;
        mResources = build();
    }

    public String getPkgName() {
        return mPluginPkgName;
    }

    public Resources get() {
        return mResources;
    }

    private Resources build() {
        try {
            PackageInfo packageInfo = mContext.getPackageManager().getPackageInfo(mContext.getPackageName(), PackageManager.GET_ACTIVITIES|PackageManager.GET_META_DATA
                    |PackageManager.GET_SERVICES
                    |PackageManager.GET_PROVIDERS
                    |PackageManager.GET_SIGNATURES);
            String hostPublicSourceDir = packageInfo.applicationInfo.publicSourceDir;
            String hostSourceDir = packageInfo.applicationInfo.sourceDir;
            String pkgName = packageInfo.packageName;
            packageInfo.applicationInfo.publicSourceDir = mPluginPath;
            packageInfo.applicationInfo.sourceDir = mPluginPath;
            PackageInfo pluginInfo = mContext.getPackageManager().getPackageArchiveInfo(mPluginPath, PackageManager.GET_ACTIVITIES);
            mPluginPkgName = pluginInfo.packageName;
            Resources pluginResources =  mContext.getPackageManager().getResourcesForApplication(packageInfo.applicationInfo);
            packageInfo.applicationInfo.publicSourceDir = hostPublicSourceDir;
            packageInfo.applicationInfo.sourceDir = hostSourceDir;
            packageInfo.packageName = pkgName;
            return pluginResources;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

}
