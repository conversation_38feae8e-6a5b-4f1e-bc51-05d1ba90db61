package com.sq.pluginex;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.pm.PackageParser;
import android.util.Log;

import java.io.File;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/12/9 14:51
 */
public class LoadedPlugin {
    private static final String TAG = "LoadedPlugin";
    protected final PackageParser.Package mPackage;

    public LoadedPlugin(ClassLoader classLoader, Context hostContext, File apk) {
        this.mPackage = PackageParserCompat.parsePackage(hostContext, apk, PackageParser.PARSE_MUST_BE_APK);
        // Register broadcast receivers dynamically
        for (PackageParser.Activity receiver : this.mPackage.receivers) {
            Log.i(TAG, "receiver:" + receiver.getClass().getName());
            BroadcastReceiver br = null;
            try {
                Object obj = classLoader.loadClass(receiver.getComponentName().getClassName()).newInstance();
                if (obj instanceof BroadcastReceiver) {
                    br = (BroadcastReceiver) obj;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (br != null) {
                for (PackageParser.ActivityIntentInfo aii : receiver.intents) {

                    Log.i(TAG, "registerReceiver");
                    hostContext.registerReceiver(br, aii);
                }
            }
        }
    }


}
