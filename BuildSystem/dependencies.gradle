/*
* dependencies.gradle文件配置主要包括几方面
* 1. 项目中repositories配置,注意这里只定义公共需要的,module个性化的配置可到module对应下的build.gradle配置;
* 2. android编译相关;
* 3. module使用到的library;
* 4. testing配置;
* 5. dependencies配置相关;
*/
apply from: project.rootProject.projectDir.absolutePath + "/config.gradle"
allprojects {
    repositories {
        //jcenter仓库
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        jcenter()
        google()
        //本地开发私有仓库
        maven { url "http://www.azact.com/artifactory/sy_sdk_channel_libs/" }
        maven { url "http://www.azact.com/artifactory/sy_dev_libs/" }

        // 个推仓库
        maven {
            url "https://mvn.getui.com/nexus/content/repositories/releases/"
        }
        // 华为仓库
        maven { url 'https://developer.huawei.com/repo/' }
        // 荣耀仓库
        maven { url 'https://developer.hihonor.com/repo/' }

        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
    }
}

ext {
    //项目中公共配置项可在这里设定

    //Android configuration
    androidBuildToolsVersion = "28.0.3"
    androidCompileSdkVersion = 30
    if (isHyLiveShow) {
        androidMinSdkVersion = 21
    } else {
        androidMinSdkVersion = 19
    }
    androidTargetSdkVersion = 30

    annotation = [
            inject_annotation: 'com.37sy.android:sqinject-compile:1.0.0',
    ]
    sqsdk = [
            logger    : 'com.37sy.android:logger:1.3',
            diagnostic: 'com.37sy.android.diagnostic:assistant:3.5-alpha',
            inject    : 'com.37sy.android:sqinject:1.0.0',
            volly     : 'com.37sy.android:volly:4.9-alpha',
            httpdns   : 'com.37sy.android:httpdns:1.2.1-alpha',
            report: 'com.37sy.android:report:1.1',
            bugless   : 'com.37sy.android:bugless:2.4.9',
            webview   : 'com.37sy.android:webView:1.2.0-beta.24',
            // 注意sq-plugin-config.json中的配置包含了版本号
            push_base : 'com.37sy.android:push-base:1.1.0-alpha',
            push      : 'com.37sy.android:push:1.1.0-alpha.1',
            push_getui: 'com.37sy.android:push-getui:1.1.0-alpha.1',
    ]
    google = [
            gson      : 'com.google.code.gson:gson:2.8.5',
    ]
    other = [
            okhttp: 'com.squareup.okhttp3:okhttp:3.12.13',
            // https://mvnrepository.com/artifact/com.squareup.okio/okio
            okio: 'com.squareup.okio:okio:1.17.4',
            // https://mvnrepository.com/artifact/com.alibaba/fastjson
            fastjson: 'com.alibaba:fastjson:1.2.62',
    ]
}

subprojects {
    project.configurations.all {
        resolutionStrategy {
            force 'com.android.support:support-annotations:28.0.0'
        }
    }
}


