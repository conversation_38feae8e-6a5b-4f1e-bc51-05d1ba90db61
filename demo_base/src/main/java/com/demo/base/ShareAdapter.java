package com.demo.base;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.EditText;


import java.util.List;

public class ShareAdapter extends BaseAdapter {
    private List<String> imgs;

    private Context mContext;

    private LayoutInflater inflater;

    public ShareAdapter(Context context, List<String> imgs) {
        this.mContext = context;
        this.imgs = imgs;
        inflater = LayoutInflater.from(context);
    }

    @Override
    public int getCount() {
        return imgs.size();
    }

    @Override
    public String getItem(int i) {
        return imgs.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    public void setData(List<String> datas){
        this.imgs=datas;
        notifyDataSetChanged();
    }

    @Override
    public View getView(final int i, View view, ViewGroup viewGroup) {
        final ViewHolder viewHolder;
        if (view == null) {
            view = inflater.inflate(R.layout.sy37_item_share, null);
            viewHolder = new ViewHolder();
            viewHolder.etImgId = view.findViewById(R.id.et_img_id);
            viewHolder.btnShare = view.findViewById(R.id.btn_share);
            viewHolder.btnDelete = view.findViewById(R.id.btn_delete);
            view.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) view.getTag();
        }
        String item = getItem(i);
        if(!TextUtils.isEmpty(item)){
            viewHolder.etImgId.setText(item);
        }else{
            viewHolder.etImgId.setText("");
        }
        viewHolder.btnShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickShareListener != null) {
                    mOnClickShareListener.clickShare(viewHolder.etImgId.getText().toString());
                }
            }
        });
        viewHolder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mOnClickShareListener != null) {
                    mOnClickShareListener.clickDelete(i);
                }
            }
        });
        return view;
    }

    private class ViewHolder {
        private EditText etImgId;
        private Button btnShare;
        private Button btnDelete;
    }

    private OnClickShareListener mOnClickShareListener;

    public interface OnClickShareListener {
        void clickShare(String imgId);
        void clickDelete(int pos);
    }

    public void setOnClickShareListener(OnClickShareListener onClickShareListener) {
        this.mOnClickShareListener = onClickShareListener;
    }
}
