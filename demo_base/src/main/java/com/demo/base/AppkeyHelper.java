package com.demo.base;

import android.content.Context;
import android.content.res.AssetManager;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-08-03 15:14
 */
public class AppkeyHelper {
    private static final String MULTI_SDK = "multi_sdk";
    private static final String PRO_KEY_CONFIG = "config";
    private static Map appkeys = new HashMap<Integer,String>();
    static {
        appkeys.put(1000000,"KJn383*52^&*.,");
        appkeys.put(1000001,"7Q/Rh-p_goN,zd?");
        appkeys.put(1000008,"uUKlGp6W0mE$qHnw3f+vSo1rTc4e5FIM");
        appkeys.put(1011958,"gEBn7bws0cI24R83D*jePtrp6UXK.N5S");
        appkeys.put(1012567,"slDMjCNm0na18Jdxq79HgocQTf:GvF/W");
        appkeys.put(1012578,"gEBn7bws0cI24R83D*jePtrp6UXK.N5S");
        appkeys.put(1012579,"gEBn7bws0cI24R83D*jePtrp6UXK.N5S");
        appkeys.put(1009416,"gsIfWNBvMbJr96c!uHk3jlxPDGVK8T.z");//斗罗直播
        appkeys.put(1014310,"EpFtC5Q9YbVXIv7TJijk06;3PhAOU$LR");//捷诚
        appkeys.put(1013951,"F@9hLZMlSDCsGb6/EuimBY5R3WH4XvQx");
        appkeys.put(1016033,"MnJ9KOzcrDw8yf4GCeI6.3?kPNl5Vmp0");
    }
    private static Properties readProperties(Context context) {
        Properties p = null;
        InputStream in = null;
        try {
            in = context.getResources().getAssets().open(MULTI_SDK);
            p = new Properties();
            p.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (in!=null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return p;
    }
    private static String getConfigFileName(Context context){
        Properties pro = readProperties(context);
        if(pro == null) {
            return "";
        }
        String config = pro.getProperty(PRO_KEY_CONFIG);
        return config;
    }
    public static String getAppkey(Context context) {
        AssetManager assetManager = context.getAssets();
        try {
            InputStream is = assetManager.open(getConfigFileName(context));
            XmlPullParserFactory xppf = XmlPullParserFactory.newInstance();
            XmlPullParser xpp = xppf.newPullParser();
            xpp.setInput(is, "utf-8");
            int eventType;
            eventType = xpp.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                switch (eventType) {
                    case XmlPullParser.START_TAG:
                        if (xpp.getName().equals("gameid")) {
                            String gameid = xpp.nextText().trim();
                            String appkey = (String) appkeys.get(Integer.parseInt(gameid));
                            return appkey;
                        }
                        break;
                    default:
                        break;
                }
                eventType = xpp.next();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return "";
    }
}
