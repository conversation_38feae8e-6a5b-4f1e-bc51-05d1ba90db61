package notchtools.geek.com.notchtools.core;

import android.app.Activity;
import android.graphics.Rect;
import android.support.annotation.RequiresApi;
import android.view.DisplayCutout;
import android.view.Window;
import android.view.WindowInsets;
import java.util.List;
import notchtools.geek.com.notchtools.helper.NotchStatusBarUtils;
import notchtools.geek.com.notchtools.helper.ScreenOrientationHelper;


/**
 * <AUTHOR>
 * @date 2018/11/4
 */

public abstract class AbsNotchScreenSupport implements INotchSupport {
    protected String TAG = this.getClass().getSimpleName();
    @Override
    public int getStatusHeight(Window window) {
        return NotchStatusBarUtils.getStatusBarHeight(window.getContext());
    }

    @Override
    public void fullScreenDontUseStatus(Activity activity, OnNotchCallBack notchCallBack) {
        NotchStatusBarUtils.setFullScreenWithSystemUi(activity.getWindow(), false);
        onBindCallBackWithNotchProperty(activity, notchCallBack);
    }

    @Override
    public void fullScreenDontUseStatusForPortrait(Activity activity, OnNotchCallBack notchCallBack) {
        fullScreenDontUseStatus(activity, notchCallBack);
    }

    @Override
    public void fullScreenDontUseStatusForLandscape(Activity activity, OnNotchCallBack notchCallBack) {
        NotchStatusBarUtils.setFullScreenWithSystemUi(activity.getWindow(), false);
        onBindCallBackWithNotchProperty(activity, notchCallBack);
        if (isNotchScreen(activity.getWindow())) {
            NotchStatusBarUtils.hideFakeNotchView(activity.getWindow());
        }
    }

    @Override
    public void fullScreenUseStatus(Activity activity, OnNotchCallBack notchCallBack) {
        NotchStatusBarUtils.setFullScreenWithSystemUi(activity.getWindow(), false);
        onBindCallBackWithNotchProperty(activity, getNotchHeight(activity.getWindow()), notchCallBack);
    }

    @Override
    public void translucentStatusBar(Activity activity) {
        translucentStatusBar(activity, null);
    }

    @Override
    public void translucentStatusBar(Activity activity, OnNotchCallBack onNotchCallBack) {
        NotchStatusBarUtils.hideFakeNotchView(activity.getWindow());
        NotchStatusBarUtils.setStatusBarTransparent(activity.getWindow(), onNotchCallBack);
    }

    protected void onBindCallBackWithNotchProperty(Activity activity, OnNotchCallBack notchCallBack) {
        if (notchCallBack != null) {
            NotchProperty notchProperty = new NotchProperty();
            notchProperty.setNotchHeight(getNotchHeight(activity.getWindow()));
            notchProperty.setNotch(isNotchScreen(activity.getWindow()));
            if (notchCallBack != null) {
                notchCallBack.onNotchPropertyCallback(notchProperty);
            }
        }
    }

    protected void onBindCallBackWithNotchProperty(Activity activity, int marginTop, OnNotchCallBack notchCallBack) {
        if (notchCallBack != null) {
            NotchProperty notchProperty = new NotchProperty();
            notchProperty.setNotchHeight(getNotchHeight(activity.getWindow()));
            notchProperty.setNotch(isNotchScreen(activity.getWindow()));
            notchProperty.setMarginTop(marginTop);
            if (notchCallBack != null) {
                notchCallBack.onNotchPropertyCallback(notchProperty);
            }
        }
    }

    @RequiresApi(api = 28)
    public int _getNotchHeight(Window window) {
        int notchHeight = 0;
        try {
            int screenType = ScreenOrientationHelper.currentType;
            WindowInsets rootWindowInsets = window.getDecorView().getRootWindowInsets();
            if (rootWindowInsets != null) {
                DisplayCutout cutout = rootWindowInsets.getDisplayCutout();
                if (cutout!=null) {
                    List<Rect> boundingRects = cutout.getBoundingRects();
                    if (boundingRects != null && boundingRects.size() > 0) {
                        for (Rect rect : boundingRects) {
                            if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_270) {
                                notchHeight = rect.right - rect.left;
                            } else if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_90) {
                                notchHeight = rect.right - rect.left;
                            }
                            break;
                        }
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return notchHeight;
        }
    }
    @Override
    public boolean ignoreNotchScreen(Window window,int decorViewWidth,int decorViewHeight){
        boolean ignoreNotch = false;
        try {
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.P) {
                return false;
            }
            int screenType = ScreenOrientationHelper.currentType;
            WindowInsets rootWindowInsets = window.getDecorView().getRootWindowInsets();
            if (rootWindowInsets != null) {
                DisplayCutout cutout = rootWindowInsets.getDisplayCutout();
                List<Rect> boundingRects = cutout.getBoundingRects();
                if (boundingRects != null && boundingRects.size() > 0) {
                    for (Rect rect : boundingRects) {
                        if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_270) {
                            if (rect.top == 0) {
                                ignoreNotch = true;
                            }
                        } else if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_90) {
                            if (rect.bottom == decorViewHeight) {
                                ignoreNotch = true;
                            }
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return ignoreNotch;
        }
    }
}
