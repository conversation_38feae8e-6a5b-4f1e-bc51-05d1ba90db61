apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        versionCode 1
        versionName get_libraryVersion()

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "com.android.support:support-annotations:26.0.1"
}
