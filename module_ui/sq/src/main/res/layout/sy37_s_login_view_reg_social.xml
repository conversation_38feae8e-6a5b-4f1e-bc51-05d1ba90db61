<?xml version="1.0" encoding="utf-8"?><!--    com.sy37sdk.account.ui.widget.AccountRegView-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/account_dialog_margin"
        android:layout_marginRight="@dimen/account_dialog_margin"
        android:background="@drawable/account_dialog_bg">

        <RelativeLayout
            android:id="@+id/login_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="5dp"
            android:layout_marginRight="@dimen/account_dialog_margin">

            <TextView
                android:id="@+id/reg_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:text="@string/account_phone_reg"
                android:textColor="#222121"
                android:textSize="22dp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ic_help"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_tip" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_reg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/login_title"
            android:layout_marginTop="15dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/phone_layout"
                style="@style/style_account_item_login_bg"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/style_account_reg_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:hint="@string/fg_account_phone_num_hint"
                    android:inputType="number"
                    android:maxLength="11"
                    android:maxLines="1"
                    android:singleLine="true" />

                <TextView
                    android:id="@+id/send_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="20dp"
                    android:text="@string/account_get_code"
                    android:textColor="#ff9900"
                    android:textSize="15dp" />
            </LinearLayout>

            <LinearLayout
                style="@style/style_account_item_login_bg"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_code"
                    style="@style/style_account_reg_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:hint="@string/fg_account_verifyCode_hint"
                    android:inputType="number"
                    android:singleLine="true" />
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/privacy_parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_reg">

            <include layout="@layout/sy37_s_login_view_reg_agree" />

        </RelativeLayout>

        <TextView
            android:id="@+id/reg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/privacy_parent"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="20dp"
            android:layout_marginRight="@dimen/account_dialog_margin"
            android:background="@drawable/account_reg_bg"
            android:gravity="center"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="@string/account_text_reg"
            android:textColor="#ffffff"
            android:textSize="15dp" />

        <TextView
            android:id="@+id/login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/reg"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/account_switch_login"
            android:textColor="#47a2ff"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/social_reg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/reg"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/account_social_reg"
            android:textColor="#ff9900"
            android:textSize="14dp" />
    </RelativeLayout>
</RelativeLayout>