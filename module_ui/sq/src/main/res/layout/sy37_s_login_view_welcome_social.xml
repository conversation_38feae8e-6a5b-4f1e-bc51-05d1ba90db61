<?xml version="1.0" encoding="utf-8"?><!--    com.sy37sdk.account.ui.widget.AccountRegView-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/account_dialog_margin"
        android:layout_marginRight="@dimen/account_dialog_margin"
        android:background="@drawable/account_dialog_bg">

        <RelativeLayout
            android:id="@+id/login_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="5dp"
            android:layout_marginRight="@dimen/account_dialog_margin">

            <TextView
                android:id="@+id/reg_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:text="@string/account_reg"
                android:textColor="#222121"
                android:textSize="22dp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ic_help"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_tip" />
        </RelativeLayout>

        <TextView
            android:id="@+id/select_reg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/login_title"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:text="@string/account_select_reg"
            android:textColor="#222121"
            android:textSize="14dp" />

        <LinearLayout
            android:id="@+id/ll_reg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/select_reg"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/phone_reg"
                style="@style/style_account_item_login_bg">

                <ImageView
                    android:layout_width="@dimen/ic_account_size"
                    android:layout_height="@dimen/ic_account_size"
                    android:layout_marginLeft="30dp"
                    android:src="@drawable/ic_reg_phone" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/account_phone_reg"
                    android:textColor="#222121"
                    android:textSize="18dp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/qq_login"
                style="@style/style_account_item_login_bg"
                android:layout_marginTop="10dp">

                <ImageView
                    android:layout_width="@dimen/ic_account_size"
                    android:layout_height="@dimen/ic_account_size"
                    android:layout_marginLeft="30dp"
                    android:src="@drawable/ic_reg_qq" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/account_qq_login"
                    android:textColor="#222121"
                    android:textSize="18dp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/wechat_login"
                style="@style/style_account_item_login_bg"
                android:layout_marginTop="10dp">

                <ImageView
                    android:layout_width="@dimen/ic_account_size"
                    android:layout_height="@dimen/ic_account_size"
                    android:layout_marginLeft="30dp"
                    android:src="@drawable/ic_reg_wechat" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/account_wechat_login"
                    android:textColor="#222121"
                    android:textSize="18dp" />
            </RelativeLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/account_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_reg"
            android:layout_alignParentRight="true"
            android:layout_marginTop="30dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/account_switch_login"
            android:textColor="#47a2ff"
            android:textSize="14dp" />
    </RelativeLayout>
</RelativeLayout>