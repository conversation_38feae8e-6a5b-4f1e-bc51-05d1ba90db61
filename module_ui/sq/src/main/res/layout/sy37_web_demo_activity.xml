<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingDefaultResource" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showSQNoticeDialog"
            android:text="公告弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showAuthDialog"
            android:text="实名认证弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showPolicyDialog"
            android:text="未成年人弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showRedPacketDialog"
            android:text="红包弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showSQWebContainerDialog"
            android:text="显示 Web 容器弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showSQWebContainerDialog2"
            android:text="打开账号申诉弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showUserProtocolDialog"
            android:text="打开用户协议弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showPrivacyPolicyDialog"
            android:text="打开隐私协议弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showBaseNormalDialog"
            android:text="打开适龄提示弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showPayWebPage"
            android:text="打开支付页弹窗" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showVerifyPhoneDialog"
            android:text="手机号验证 登录号" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="showCaptchaDialog"
            android:text="滑块验证" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="jsInterface"
            android:text="Js 功能验证" />

    </LinearLayout>

</ScrollView>