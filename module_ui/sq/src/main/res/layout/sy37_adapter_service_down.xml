<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" 
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    >

    <RelativeLayout
        android:id="@+id/newsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dip"
        android:layout_marginRight="5dip"
        android:layout_marginTop="5dip"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/newIcon"
            style="@style/style_service_nav_text"
            android:text="@string/fg_news"
            android:drawableLeft="@drawable/sy37_icon_kefu_news"
            />


            <TextView
                android:id="@+id/newsTitle"
                android:layout_toRightOf="@id/newIcon"
                android:layout_alignBottom="@id/newIcon"
                style="@style/style_service_nav_text_title"
                />
            <View 
                android:layout_width="match_parent"
                android:layout_height="1dip"
                android:layout_alignBottom="@id/newsTitle"
                android:layout_alignLeft="@id/newsTitle"
                android:background="@drawable/sy37_icon_list_diver"/>
    </RelativeLayout>
    
    <RelativeLayout
        android:id="@+id/strategyLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dip"
        android:layout_marginRight="5dip"
        android:layout_marginTop="5dip"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/strategyIcon"
            style="@style/style_service_nav_text"
            android:drawableLeft="@drawable/sy37_icon_kefu_strategy"
            android:text="@string/fg_strategy"
            />

            <TextView
                android:id="@+id/strategyTitle"
                android:layout_toRightOf="@id/strategyIcon"
                android:layout_alignBottom="@id/strategyIcon"
                style="@style/style_service_nav_text_title"
                 />
            <View 
                android:layout_width="match_parent"
                android:layout_height="1dip"
                android:layout_alignBottom="@id/strategyTitle"
                android:layout_alignLeft="@id/strategyTitle"
                android:background="@drawable/sy37_icon_list_diver"/>
    </RelativeLayout>
    
    <RelativeLayout
        android:id="@+id/giftLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dip"
        android:layout_marginRight="5dip"
        android:layout_marginTop="5dip"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/giftIcon"
            style="@style/style_service_nav_text"
            android:drawableLeft="@drawable/sy37_icon_kefu_gift"
            android:text="@string/fg_gift"
            />


            <TextView
                android:id="@+id/giftTitle"
                android:layout_toRightOf="@id/giftIcon"
                android:layout_alignBottom="@id/giftIcon"
                style="@style/style_service_nav_text_title"
                />
            <View 
                android:layout_width="match_parent"
                android:layout_height="1dip"
                android:layout_alignBottom="@id/giftTitle"
                android:layout_alignLeft="@id/giftTitle"
                android:background="@drawable/sy37_icon_list_diver"/>
    </RelativeLayout>


</LinearLayout>