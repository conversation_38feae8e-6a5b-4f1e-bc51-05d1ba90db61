<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:orientation="vertical">

    <View
        android:id="@+id/status_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#66000000" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_web_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:scaleType="centerInside" />

        <RelativeLayout
            android:id="@+id/sy37_m_layout_payweb_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@android:color/transparent">

            <ImageView
                android:id="@+id/sy37_m_img_payweb_close"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="1dp"
                android:src="@drawable/sy_payweb_close_new" />

        </RelativeLayout>
    </FrameLayout>
</LinearLayout>
