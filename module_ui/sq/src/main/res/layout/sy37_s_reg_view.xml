<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/regLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_below="@+id/tips_login_reg_view"
    android:gravity="center">

    <TextView
        android:id="@+id/tips_reg_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:text="@string/sy37_account_reg"
        android:textColor="@color/s_orange"
        android:textSize="@dimen/sq_title_text_size"
        android:layout_centerHorizontal="true"
        />

    <ImageView
        android:id="@+id/iv_doubt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="3dp"
        android:src="@drawable/sy37_icon_doubt"
        android:layout_alignTop="@+id/tips_reg_view"
        android:visibility="gone"
        />

    <FrameLayout
        android:id="@+id/fl_account_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tips_reg_view"
        android:layout_marginTop="10dp"
        >

        <LinearLayout
            android:id="@+id/ll_account_reg_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible"
            >

            <LinearLayout
                android:id="@+id/ll_account_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/sy37_bg_edit"
                >

                <TextView
                    android:id="@+id/text_flag_account"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:text="@string/fg_account"
                    android:textColor="@color/s_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

                <EditText
                    android:id="@+id/regName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_reg_account_hint"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:inputType="textVisiblePassword"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size"/>

            </LinearLayout>



            <LinearLayout
                android:id="@+id/ll_pwd_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/sy37_bg_edit"
                android:layout_marginTop="10dp"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:text="@string/fg_pw"
                    android:textColor="@color/s_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

                <EditText
                    android:id="@+id/regPW"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_reg_pw_hint"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:inputType="textVisiblePassword"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_phone_reg_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            >

            <LinearLayout
                android:id="@+id/ll_phone_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/sy37_bg_edit"
                >

                <EditText
                    android:id="@+id/sy37_account_edit_phoneNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_account_phone_num_hint"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:paddingLeft="15dp"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size">

                </EditText>


                <Button
                    android:id="@+id/sy37_btn_getVerifyCode"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/sy37_bg_get_verify_code"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/fg_account_get_verifyCode"
                    android:textColor="@color/sy37_text_color_change"
                    android:textSize="@dimen/sq_edt_text_size" />

            </LinearLayout>



            <LinearLayout
                android:id="@+id/ll_verify_code_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/sy37_bg_edit"
                android:layout_marginTop="10dp"
                >

                <EditText
                    android:id="@+id/sy37_account_edit_verifyCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_account_verifyCode_hint"
                    android:inputType="number"
                    android:maxLength="10"
                    android:paddingLeft="15dp"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

            </LinearLayout>


        </LinearLayout>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/sy37_reg_clause_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/fl_account_container"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/sy37_reg_clause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/sy37_check_selector"
            android:checked="false"
            android:text="@string/fg_reg_clause_tips_1"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />


        <TextView
            android:id="@+id/sy37_reg_clause_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_clause_tips_2"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/sy37_reg_protocol_txt_color"
            android:textSize="@dimen/sq_agree_text1_size" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="及"
            android:textSize="@dimen/sq_agree_text1_size"
            android:textColor="#696969"
            />

        <TextView
            android:id="@+id/tv_reg_policy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_clause_tips_3"
            android:textSize="@dimen/sq_agree_text1_size"
            android:textColor="@color/sy37_reg_protocol_txt_color"
            android:maxLines="1"
            android:ellipsize="end"
            />

    </LinearLayout>


    <Button
        android:id="@+id/regBtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sq_btn_height"
        android:layout_below="@id/sy37_reg_clause_view"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/sy37_kefu_reg"
        android:text="@string/fg_reg_account_now"
        android:textColor="@color/s_white"
        android:textSize="@dimen/sq_btn_text_size" />


    <TextView
        android:id="@+id/sy37_reg_text_has_account"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/regBtn"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_has_account"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

    <TextView
        android:id="@+id/sy37_reg_text_by_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/regBtn"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_by_phone"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

</RelativeLayout>