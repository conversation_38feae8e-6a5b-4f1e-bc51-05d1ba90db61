<?xml version="1.0" encoding="utf-8"?>
  
 <!-- 修改密码布局 -->
 <RelativeLayout  xmlns:android="http://schemas.android.com/apk/res/android"
     android:id="@+id/modifyPWLayout"
     android:layout_width="match_parent"
     android:layout_height="wrap_content"
     android:layout_below="@+id/loginLayout"
      >

     <LinearLayout
         android:id="@+id/pwBtnLayout"
         android:layout_width="match_parent"
         android:layout_height="wrap_content"
         android:background="@drawable/sy37_layout_white_bg"
         android:gravity="center_vertical"
         android:orientation="horizontal"
         >

         <ImageView
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_marginLeft="10dip"
             android:src="@drawable/sy37_change_password" />

         <TextView
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:layout_marginLeft="8.0dip"
             android:layout_weight="1.0"
             android:text="@string/fg_modify_pw"
             android:textColor="@color/s_orange"
             android:textSize="20.0dp" />

         <Button
             android:id="@+id/modifyPWback"
             style="@style/backBut"
              />
     </LinearLayout>

     <TableLayout
         android:id="@+id/modifyDesc"
         android:layout_width="match_parent"
         android:layout_height="wrap_content"
         android:layout_below="@id/pwBtnLayout"
         android:layout_gravity="center_horizontal"
         android:layout_marginTop="5dip"
         android:gravity="center"
         android:orientation="horizontal" >

         <TableRow
             android:layout_marginBottom="3dip"
             android:layout_weight="1.0"
             android:background="@drawable/sy37_bg_edit"
             android:gravity="bottom" >
             
             <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/fg_old_pw"
                    android:textColor="@color/s_gray"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:textSize="18.0dp" />
             
             <EditText
                    android:id="@+id/oldPW"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:hint="@string/fg_old_pw_hint"
                    android:textColorHint="@color/s_light_gray"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textSize="18.0dp" />
             
         </TableRow>


         <TableRow
             android:layout_marginBottom="3dip"
             android:layout_weight="1.0"
             android:background="@drawable/sy37_bg_edit"
             android:gravity="top" >

             <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/fg_new_pw"
                    android:textColor="@color/s_gray"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:textSize="18.0dp" />

             <EditText
                    android:id="@+id/newsPW"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:hint="@string/fg_new_pw_hint"
                    android:textColorHint="@color/s_light_gray"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textSize="18.0dp" />
         </TableRow>
         
         <TableRow
             android:layout_marginBottom="3dip"
             android:layout_weight="1.0"
             android:background="@drawable/sy37_bg_edit"
             android:gravity="top" >

             
             <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/fg_new_pw_conform"
                    android:textColor="@color/s_gray"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:textSize="18.0dp" />

                 <EditText
                    android:id="@+id/newsPWConform"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:hint="@string/fg_new_pw_conform_hint"
                    android:textColorHint="@color/s_light_gray"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textSize="18.0dp" />
         </TableRow>
     </TableLayout>

     <Button
         android:id="@+id/modifySub"
         android:layout_width="match_parent"
         android:layout_height="40dip"
         android:layout_below="@id/modifyDesc"
         android:layout_marginTop="5dp"
         android:background="@drawable/sy37_kefu_submit"
         android:text="@string/fg_submit"
         android:textColor="@color/s_white"
         android:textSize="23dp" />
 </RelativeLayout>
    