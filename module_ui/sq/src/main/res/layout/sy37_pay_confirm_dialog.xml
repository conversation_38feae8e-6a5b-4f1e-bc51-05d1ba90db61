<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parentLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null">

    <RelativeLayout
        android:id="@+id/contentLayout"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/sy37_shape_white_radius_4">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="交易尚未完成，继续支付？"
            android:textColor="#ff333333"
            android:textSize="16dp"
            android:layout_margin="45dp"

            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_below="@+id/tv_title"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            >

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="100dp"
                android:layout_height="35dp"
                android:background="@drawable/sy37_pay_confirm_cancel_bg"
                android:gravity="center"
                android:text="返回"
                android:textColor="@color/sy37_pay_dialog_theme_color"
                android:textSize="16dp" />


            <TextView
                android:id="@+id/tv_ensure"
                android:layout_width="100dp"
                android:layout_height="35dp"
                android:background="@drawable/sy37_pay_confirm_ensure_bg"
                android:gravity="center"
                android:layout_marginLeft="40dp"
                android:text="继续"
                android:textSize="16dp"
                android:textColor="@android:color/white"
                />

        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>