<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    >


    <TextView
        android:id="@+id/tv_charge_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sy37_pay_charge_tips"
        android:gravity="center"
        android:drawableLeft="@drawable/sy37_icon_charge_tips"
        android:drawablePadding="4dp"
        android:layout_marginTop="10dp"
        />

    <TextView
        android:id="@+id/tv_charge_desc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:textColor="@color/sy37_pay_dialog_txt_color"
        android:padding="12dp"
        android:layout_marginTop="16dp"
        />

</LinearLayout>