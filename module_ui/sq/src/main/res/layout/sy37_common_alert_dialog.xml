<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parentLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null">

    <RelativeLayout
        android:id="@+id/contentLayout"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/sy37_shape_login_dialog_bg">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="您确定退出游戏吗？"
            android:textColor="#666666"
            android:textSize="18dp" />

        <View
            android:id="@+id/v_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_below="@+id/tv_title"
            android:background="#E5E5E5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_below="@+id/v_divider"
            android:orientation="horizontal">

            <TextView
                android:textColor="#666666"
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/sy37_selector_common_btn_bg"
                android:gravity="center"
                android:text="取消"
                android:textSize="16dp" />

            <View
                android:layout_gravity="center_vertical"
                android:id="@+id/v_divider_h"
                android:layout_width="0.5dp"
                android:layout_height="30dp"
                android:layout_below="@+id/tv_title"
                android:background="#E5E5E5" />
            <TextView
                android:textColor="#F3AA3D"
                android:id="@+id/tv_ensure"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/sy37_selector_common_btn_bg"
                android:gravity="center"
                android:text="确定"
                android:textSize="16dp" />


        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>