<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="5dip" >

    <include
        android:id="@+id/netload"
        layout="@layout/sy37_netload" 
        />

    <ListView
        android:id="@+id/listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="10dip"
        android:layout_marginLeft="7dip"
        android:layout_marginRight="7dip"
        android:divider="@drawable/sy37_icon_list_diver"
        android:cacheColorHint="@android:color/transparent"
        android:scrollbars="none" >
    </ListView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="10dip"
        android:layout_marginLeft="10dip"
        android:layout_marginRight="10dip"
        android:orientation="vertical" >

        <LinearLayout
            android:id="@+id/btnLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/sy37_bg_kefu_webview_head"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="2.0dip" >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1.0"
                android:orientation="vertical" >

                <TextView
                    android:id="@+id/webviewTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dip"
                    android:layout_marginRight="5dip"
                    android:maxLines="2"
                    android:textColor="@color/s_orange"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/webviewTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dip"
                    android:layout_marginRight="5dip"
                    android:layout_weight="1.0"
                    android:maxLines="2"
                    android:textColor="#ff666666"
                    android:textSize="12dp" />
            </LinearLayout>

            <Button
                android:id="@+id/back"
                style="@style/backBut" />
        </LinearLayout>

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#00000000"
            android:scrollbars="none"
            android:visibility="gone" />
    </LinearLayout>

</LinearLayout>