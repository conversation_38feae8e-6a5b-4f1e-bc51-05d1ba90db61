<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    >

    <RelativeLayout
        android:layout_width="535dp"
        android:layout_height="335dp"
        android:orientation="vertical"
        android:background="@drawable/sy37_pay_dialog_bg"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        >

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/sy37_icon_back"
            android:layout_marginTop="8dp"
            android:scaleType="center"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/sy37_pay_dialog_txt_color"
            android:text="@string/sy37_pay_dialog_title"
            android:textSize="@dimen/sq_pay_dialog_title_size"
            android:gravity="center"
            android:layout_marginTop="12dp"
            android:layout_centerHorizontal="true"
            />

        <RelativeLayout
            android:id="@+id/pay_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width_l"
            android:layout_height="@dimen/sq_pay_dialog_content_height_l"
            android:background="@drawable/sy37_pay_dialog_bg"
            android:layout_below="@+id/tv_title"
            android:paddingTop="12dp"
            >

            <RelativeLayout
                android:id="@+id/ll_price_container"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:background="@drawable/sy37_pay_title_bg_land"
                >

                <TextView
                    android:id="@+id/tv_pay_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sq_pay_dialog_price_size"
                    android:textColor="@android:color/white"
                    android:gravity="center"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="14dp"
                    />

                <TextView
                    android:id="@+id/tv_cut_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12dp"
                    android:textColor="@android:color/white"
                    android:gravity="center"
                    android:layout_below="@+id/tv_pay_count"
                    android:layout_centerHorizontal="true"
                    android:visibility="gone"
                    />

            </RelativeLayout>


            <TextView
                android:id="@+id/tv_coupon"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/sq_pay_dialog_coupon_btn_height"
                android:text="无适用代金券"
                android:textColor="#9d9d9d"
                android:textSize="12dp"
                android:layout_below="@+id/ll_price_container"
                android:layout_marginTop="4dp"
                android:layout_marginLeft="66dp"
                android:drawableRight="@drawable/sy37_icon_coupon_more"
                android:drawablePadding="4dp"
                android:gravity="center"
                android:enabled="false"
                android:clickable="false"
                />

            <LinearLayout
                android:id="@+id/rg_pay_way"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_below="@+id/tv_coupon"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                >

                <RelativeLayout
                    android:id="@+id/rb_alipay"
                    android:layout_width="102dp"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height_l"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:focusable="true"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:id="@+id/ll_alipay_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_centerInParent="true"
                        >

                        <ImageView
                            android:id="@+id/iv_alipay_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/sy37_icon_alipay"
                            />

                        <TextView
                            android:id="@+id/tv_alipay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sy37_pway_alipay"
                            android:textSize="12dp"
                            android:textColor="@color/sy37_pay_dialog_txt_color"
                            android:layout_marginTop="4dp"
                            />

                    </LinearLayout>


                    <TextView
                        android:id="@+id/tv_alipay_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:layout_below="@+id/ll_alipay_container"
                        android:layout_centerHorizontal="true"
                        android:ellipsize="end"
                        android:maxLines="1"
                        />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rb_huabei"
                    android:layout_width="102dp"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height_l"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_marginLeft="15dp"
                    android:visibility="gone"
                    >

                    <LinearLayout
                        android:id="@+id/ll_huabei_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_centerInParent="true"
                        >

                        <ImageView
                            android:id="@+id/iv_huabei_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/sy37_icon_huabei"
                            android:padding="2dp"
                            android:scaleType="center"
                            />

                        <TextView
                            android:id="@+id/tv_huabei"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sy37_pway_huabei"
                            android:textSize="12dp"
                            android:textColor="@color/sy37_pay_dialog_txt_color"
                            android:layout_marginTop="4dp"
                            />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_huabei_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:layout_below="@+id/ll_huabei_container"
                        android:layout_centerHorizontal="true"
                        android:ellipsize="end"
                        android:maxLines="1"
                        />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rb_wechat"
                    android:layout_width="102dp"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height_l"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_marginLeft="15dp"
                    >

                    <LinearLayout
                        android:id="@+id/ll_wechat_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_centerInParent="true"
                        >

                        <ImageView
                            android:id="@+id/iv_wechat_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/sy37_icon_wechat"
                            />

                        <TextView
                            android:id="@+id/tv_wechat"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sy37_pway_wechat"
                            android:textSize="12dp"
                            android:textColor="@color/sy37_pay_dialog_txt_color"
                            android:layout_marginTop="4dp"
                            />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_wechat_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:layout_below="@+id/ll_wechat_container"
                        android:layout_centerHorizontal="true"
                        android:ellipsize="end"
                        android:maxLines="1"
                        />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rb_wallet"
                    android:layout_width="102dp"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height_l"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_marginLeft="15dp"
                    android:visibility="gone"
                    >
                    <LinearLayout
                        android:id="@+id/ll_wallet_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_centerInParent="true"
                        >
                        <ImageView
                            android:id="@+id/iv_wallet_icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/sy37_icon_wallet"

                            android:layout_centerHorizontal="true"
                            />

                        <TextView
                            android:id="@+id/tv_wallet_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sy37_pway_wallet"
                            android:textSize="12dp"
                            android:textColor="@color/sy37_pay_dialog_txt_color"
                            android:layout_centerInParent="true"
                            android:layout_marginTop="4dp"
                            />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_wallet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:textSize="8dp"
                        android:textColor="#9d9d9d"
                        android:layout_below="@+id/ll_wallet_container"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="4dp"
                        />

                </RelativeLayout>

            </LinearLayout>


            <TextView
                android:id="@+id/tv_customer"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/sq_pay_dialog_coupon_btn_height"
                android:text="人工充值"
                android:textColor="#9d9d9d"
                android:drawableLeft="@drawable/sy37_icon_pay_customer"
                android:gravity="center"
                android:layout_marginBottom="12dp"
                android:layout_marginRight="16dp"
                android:textSize="12dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                />

        </RelativeLayout>

        <include
            android:id="@+id/coupon_layout"
            layout="@layout/sy37_coupon_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width_l"
            android:layout_height="@dimen/sq_pay_dialog_content_height_l"
            android:visibility="gone"
            android:layout_below="@+id/tv_title"
            />


        <include
            android:id="@+id/charge_desc_layout"
            layout="@layout/sy37_charge_desc_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width_l"
            android:layout_height="@dimen/sq_pay_dialog_content_height_l"
            android:visibility="gone"
            android:layout_below="@+id/tv_title"
            />


    </RelativeLayout>
</LinearLayout>