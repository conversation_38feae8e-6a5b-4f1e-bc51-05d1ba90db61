<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    >

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:background="@drawable/sy37_pay_dialog_bg"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        >

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/sy37_icon_back"
            android:layout_marginTop="8dp"
            android:scaleType="center"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/sy37_pay_dialog_txt_color"
            android:textSize="@dimen/sq_pay_dialog_title_size"
            android:gravity="center"
            android:text="@string/sy37_pay_dialog_title"
            android:layout_marginTop="12dp"
            android:layout_centerHorizontal="true"
            android:textStyle="bold"
            />

        <RelativeLayout
            android:id="@+id/pay_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width"
            android:layout_height="@dimen/sq_pay_dialog_content_height"
            android:background="@drawable/sy37_pay_dialog_bg"
            android:paddingBottom="12dp"
            android:layout_below="@+id/tv_title"
            android:paddingTop="16dp"
            >


            <RelativeLayout
                android:id="@+id/ll_price_container"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:background="@drawable/sy37_pay_title_bg_port"
                >

                <TextView
                    android:id="@+id/tv_pay_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sq_pay_dialog_price_size"
                    android:textColor="@android:color/white"
                    android:gravity="center"
                    android:layout_centerInParent="true"
                    />

                <TextView
                    android:id="@+id/tv_cut_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="15dp"
                    android:textColor="@android:color/white"
                    android:gravity="center"
                    android:layout_below="@+id/tv_pay_count"
                    android:layout_centerHorizontal="true"
                    android:visibility="gone"
                    />

            </RelativeLayout>


            <TextView
                android:id="@+id/tv_coupon"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_below="@+id/ll_price_container"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="8dp"
                android:drawablePadding="4dp"
                android:drawableRight="@drawable/sy37_icon_coupon_more"
                android:gravity="center"
                android:text="无适用代金券"
                android:textColor="#9d9d9d"
                android:textSize="12dp"
                android:enabled="false"
                android:clickable="false"
                />

            <LinearLayout
                android:id="@+id/rg_pay_way"
                android:layout_below="@+id/ll_price_container"
                android:layout_width="316dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="47dp"
                android:layout_centerHorizontal="true"
                android:orientation="vertical"
                >

                <LinearLayout
                    android:id="@+id/rb_alipay"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height"
                    android:gravity="center_vertical"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:orientation="horizontal"
                    android:focusable="true"
                    android:clickable="true"
                    >

                    <ImageView
                        android:id="@+id/iv_alipay_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/sy37_icon_alipay"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sy37_pway_alipay"
                        android:textSize="12dp"
                        android:textColor="@color/sy37_pay_dialog_txt_color"
                        android:gravity="center"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:id="@+id/tv_alipay_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginLeft="8dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:maxLines="1"
                        android:ellipsize="end"
                        />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rb_huabei"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height"
                    android:gravity="center_vertical"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:layout_marginTop="6dp"
                    android:orientation="horizontal"
                    android:focusable="true"
                    android:clickable="true"
                    android:visibility="gone"
                    >

                    <ImageView
                        android:id="@+id/iv_huabei_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/sy37_icon_huabei"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sy37_pway_huabei"
                        android:textSize="12dp"
                        android:textColor="@color/sy37_pay_dialog_txt_color"
                        android:gravity="center"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:id="@+id/tv_huabei_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginLeft="8dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:maxLines="1"
                        android:ellipsize="end"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rb_wechat"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height"
                    android:gravity="center_vertical"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:layout_marginTop="6dp"
                    android:orientation="horizontal"
                    android:focusable="true"
                    android:clickable="true"
                    >

                    <ImageView
                        android:id="@+id/iv_wechat_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/sy37_icon_wechat"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sy37_pway_wechat"
                        android:textSize="12dp"
                        android:textColor="@color/sy37_pay_dialog_txt_color"
                        android:gravity="center"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:id="@+id/tv_wechat_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="10dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginLeft="8dp"
                        android:textColor="@color/sy37_pay_way_desc_color"
                        android:maxLines="1"
                        android:ellipsize="end"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rb_wallet"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sq_pay_dialog_pway_height"
                    android:gravity="center_vertical"
                    android:background="@drawable/sy37_shape_white_radius_4"
                    android:orientation="horizontal"
                    android:layout_marginTop="6dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:visibility="gone"
                    >

                    <ImageView
                        android:id="@+id/iv_wallet_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/sy37_icon_wallet"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sy37_pway_wallet"
                        android:textSize="12dp"
                        android:textColor="@color/sy37_pay_dialog_txt_color"
                        android:gravity="center"
                        android:layout_marginLeft="6dp"
                        />

                    <TextView
                        android:id="@+id/tv_wallet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="11dp"
                        android:textColor="#9d9d9d"
                        android:layout_marginLeft="12dp"
                        />

                </LinearLayout>

            </LinearLayout>


            <TextView
                android:id="@+id/tv_customer"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="12dp"
                android:drawableLeft="@drawable/sy37_icon_pay_customer"
                android:gravity="center"
                android:text="人工充值"
                android:textColor="#9d9d9d"
                android:textSize="12dp" />

        </RelativeLayout>


        <include
            android:id="@+id/coupon_layout"
            layout="@layout/sy37_coupon_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width"
            android:layout_height="@dimen/sq_pay_dialog_content_height"
            android:visibility="gone"
            android:layout_below="@+id/tv_title"
            />


        <include
            android:id="@+id/charge_desc_layout"
            layout="@layout/sy37_charge_desc_layout"
            android:layout_width="@dimen/sq_pay_dialog_content_width"
            android:layout_height="@dimen/sq_pay_dialog_content_height"
            android:visibility="gone"
            android:layout_below="@+id/tv_title"
            />

    </RelativeLayout>

</LinearLayout>