<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" 
    android:gravity="center"
	android:background="@null"
    >
    <LinearLayout 
	    android:layout_width="300dip"
	    android:layout_height="wrap_content"
	    android:orientation="vertical" 
	    >
		  <TextView
		            android:id="@+id/message"
		            android:layout_width="wrap_content"
		            android:layout_height="wrap_content"
		            android:gravity="center"
		            android:textSize="18dp"
		            android:textColor="@android:color/black"
					android:textStyle="normal"
					android:background="@drawable/sy37_update_top_bg"
		            />
		
		  <LinearLayout 
		        android:layout_width="wrap_content"
		    	android:layout_height="wrap_content"
		    	android:orientation="horizontal" 
		      >
		      
		      <Button
		            android:id="@+id/cancel"
		            android:layout_width="wrap_content"
		            android:layout_height="wrap_content"
		            android:layout_weight="1"
		            android:background="@drawable/sy37_update_btn_no"
		            
		            android:text="取消"
		            android:textSize="18dp"
		            android:textColor="@android:color/black"
		            
		            />
		     
		      <Button
		            android:id="@+id/confirm"
		            android:layout_width="wrap_content"
		            android:layout_height="wrap_content"
		            android:layout_weight="1"
		            android:background="@drawable/sy37_update_btn_ok"
		            
		            android:text="确定"
		            android:textSize="18dp"
		            android:textColor="@android:color/black"
		            
		            />
		      
		  </LinearLayout>
        
	</LinearLayout>
</LinearLayout>
