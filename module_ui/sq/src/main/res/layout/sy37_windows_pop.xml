<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sq_wm_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/sy37_pop_bg"
    android:gravity="center"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:visibility="visible" >

    <FrameLayout
        android:id="@+id/pop_user_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_user"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_user"
            android:text="@string/fg_pop_user_new" />

        <ImageView
            android:id="@+id/pop_user_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_lb_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_lb"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_card"
            android:text="@string/fg_pop_card" />

        <ImageView
            android:id="@+id/pop_lb_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_bbs_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_bbs"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_bbs"
            android:text="@string/fg_pop_bbs_new" />

        <ImageView
            android:id="@+id/pop_bbs_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_gl_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_gl"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_gl"
            android:text="@string/fg_pop_gl" />

        <ImageView
            android:id="@+id/pop_gl_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_service_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_service"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_help"
            android:text="@string/fg_pop_help" />

        <ImageView
            android:id="@+id/pop_service_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_change_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/pop_change"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_change"
            android:text="@string/fg_pop_change_new" />

        <ImageView
            android:id="@+id/pop_change_red"
            style="@style/style_pop_image_content" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/pop_screenshot_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/pop_screenshot"
            style="@style/style_pop_text_content"
            android:drawableTop="@drawable/sy37_icon_pop_screenshot"
            android:text="@string/fg_pop_screenshot"/>

    </FrameLayout>

</LinearLayout>