<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tips_login_reg_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:text="@string/fg_login_title"
        android:textColor="@color/s_orange"
        android:textSize="@dimen/sq_title_text_size" />

    <ImageView
        android:id="@+id/iv_doubt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="3dp"
        android:src="@drawable/sy37_icon_doubt"
        android:visibility="gone"
        android:layout_alignTop="@+id/tips_login_reg_view"
        />

    <include
        android:id="@+id/regLayout"
        layout="@layout/sy37_kefu_reg"
        android:visibility="gone" />

    <include
        android:id="@+id/regByPhoneLayout"
        layout="@layout/sy37_kefu_reg_by_phone"
        android:visibility="gone" />

    <include
        android:id="@+id/regByPhoneNumLayout"
        layout="@layout/sy37_kefu_reg_by_phonenum"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/loginLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_below="@+id/tips_login_reg_view"
        android:layout_marginTop="10dp"
        android:visibility="visible">

        <TableLayout
            android:id="@+id/loginInfo"

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:orientation="horizontal">

            <TableRow
                android:id="@+id/loginAccount"
                android:layout_weight="1.0"
                android:background="@drawable/sy37_bg_edit"
                android:gravity="bottom"
                android:padding="0dp">

                <TextView
                    android:id="@+id/text_flag_account"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:text="@string/fg_account"
                    android:textColor="@color/s_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

                <EditText
                    android:id="@+id/loginName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:ems="10"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_en_account_hint"
                    android:inputType="textVisiblePassword"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size">

                    <!--<requestFocus />-->
                </EditText>

                <ImageView
                    android:id="@+id/fg_select_account"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/sy37_bg_account_right_flag"
                    android:scaleType="center"
                    android:src="@drawable/sy37_user_set_down_bg_down" />
            </TableRow>

            <TableRow
                android:layout_marginTop="10dp"
                android:layout_weight="1.0"
                android:background="@drawable/sy37_bg_edit"
                android:gravity="top"
                android:padding="0dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dip"
                    android:text="@string/fg_pw"
                    android:textColor="@color/s_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

                <EditText
                    android:id="@+id/loginPW"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1.0"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/fg_en_pw_hint"
                    android:inputType="textPassword"
                    android:maxLength="20"
                    android:singleLine="true"
                    android:textColor="@color/s_light_gray"
                    android:textColorHint="@color/s_light_gray"
                    android:textSize="@dimen/sq_edt_text_size" />

                <ImageView
                    android:id="@+id/fg_pwd_login_eye"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/sy37_bg_account_right_flag"
                    android:paddingLeft="5dip"
                    android:paddingRight="5dip"
                    android:scaleType="center"
                    android:src="@drawable/sy37_user_pwd_eye_open" />

            </TableRow>
        </TableLayout>

        <!--添加忘记密码布局-->
        <LinearLayout
            android:id="@+id/sy37_login_forget_pwd_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_below="@id/loginInfo"
            android:layout_marginTop="10dp"
            android:background="@drawable/sy37_logview_forget_pwd_btn_selector"
            android:clickable="true"
            android:orientation="vertical"
            android:paddingLeft="3dp"
            android:paddingRight="3dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:gravity="center"
                android:text="忘记密码"
                android:textColor="#f27241"
                android:textSize="@dimen/sq_loginview_text3_size" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#f27241" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btnLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/sy37_login_forget_pwd_btn"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/toReg"
                android:layout_width="match_parent"
                android:layout_height="@dimen/sq_btn_height"
                android:layout_marginRight="10dip"
                android:layout_weight="1"
                android:background="@drawable/sy37_kefu_reg"
                android:gravity="center"
                android:text="@string/fg_reg_account"
                android:textColor="@color/s_white"
                android:textSize="@dimen/sq_btn_text_size" />

            <Button
                android:id="@+id/toLogin"
                android:layout_width="match_parent"
                android:layout_height="@dimen/sq_btn_height"
                android:layout_marginLeft="10dip"
                android:layout_weight="1"
                android:background="@drawable/sy37_kefu_submit"
                android:gravity="center"
                android:text="@string/fg_login_btn"
                android:textColor="@color/s_white"
                android:textSize="@dimen/sq_btn_text_size" />
        </LinearLayout>

        <com.sqwan.common.widget.MarqueeTextView
            android:id="@+id/downInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_below="@+id/btnLayout"
            android:layout_marginTop="6dp"
            android:drawableLeft="@drawable/sy37_notice_down_tips"
            android:drawablePadding="10dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:gravity="center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:paddingRight="5dip"
            android:paddingTop="2dp"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:text="@string/fg_welcome"
            android:textColor="#ff9a9a9a"
            android:textSize="16dp" />


        <!--<View-->
        <!--android:id="@+id/dividerline"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="1dp"-->
        <!--android:layout_below="@+id/downInfo"-->
        <!--android:layout_marginTop="5dp"-->
        <!--android:background="@color/s_light_gray">-->

        <!--</View>-->

        <!--<LinearLayout-->
        <!--android:id="@+id/thirdLoginContainer"-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:layout_alignParentRight="true"-->
        <!--android:layout_below="@+id/dividerline"-->
        <!--android:layout_marginTop="5dp"-->
        <!--android:gravity="center_vertical"-->
        <!--android:orientation="horizontal"-->
        <!--android:visibility="visible">-->

        <!--<TextView-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:textColor="#ff9a9a9a"-->
        <!--android:text="使用第三方账号登录"-->
        <!--android:textSize="16sp" />-->

        <!--<ImageView-->
        <!--android:id="@+id/sy37_qqloginbtn"-->
        <!--android:layout_width="35dp"-->
        <!--android:layout_height="35dp"-->
        <!--android:layout_marginLeft="25dp"-->
        <!--android:background="@drawable/qqlogin_icon"-->
        <!--android:scaleType="fitCenter" />-->

        <!--<ImageView-->
        <!--android:id="@+id/sy37_weibologinbtn"-->
        <!--android:layout_width="35dp"-->
        <!--android:layout_height="35dp"-->
        <!--android:layout_marginLeft="15dp"-->
        <!--android:background="@drawable/weibologin_icon"-->
        <!--android:scaleType="centerInside" />-->

        <!--<ImageView-->
        <!--android:id="@+id/sy37_weixinloginbtn"-->
        <!--android:layout_width="35dp"-->
        <!--android:layout_height="35dp"-->
        <!--android:layout_marginLeft="15dp"-->
        <!--android:background="@drawable/weixinlogin_icon"-->
        <!--android:scaleType="centerCrop" />-->


        <!--</LinearLayout>-->

    </RelativeLayout>

</RelativeLayout>