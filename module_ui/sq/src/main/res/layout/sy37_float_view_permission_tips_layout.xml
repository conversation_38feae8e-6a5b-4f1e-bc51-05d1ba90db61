<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_centerInParent="true"
        android:background="@drawable/sy37_permission_dialog_white_bg"
        android:padding="30dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/float_view_permission_tips_content"
            android:gravity="center"
            android:textSize="@dimen/permission_tips_content_text_size"
            android:textColor="@color/sy37_permission_dialog_content_text_color"
            android:lineSpacingExtra="10dp"
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="20dp">
            <TextView
                android:id="@+id/btn_open_permission"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="left"
                android:text="前往开启"
                android:textColor="#ff9c00"
                android:textSize="@dimen/permission_tips_content_text_size"/>
            <TextView
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="暂不开启"
                android:textColor="#222222"
                android:textSize="@dimen/permission_tips_content_text_size"/>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>