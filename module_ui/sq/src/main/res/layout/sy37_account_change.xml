<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_height="match_parent"
	android:layout_width="match_parent"
	>
	<RelativeLayout
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@android:color/transparent"
		>

		<Button
			android:id="@+id/btn_changeAccount"
			android:layout_width="90dp"
			android:layout_height="37dp"
			android:layout_alignParentRight="true"
			android:layout_alignParentTop="true"
			android:layout_marginRight="30dp"
			android:layout_marginTop="20dp"
			android:background="@drawable/sy37_bg_btn_change_account"
			android:textColor="@color/s_white"
			android:textSize="16dp"
			android:text="@string/fg_login_change_account"
			/>

		<RelativeLayout
			android:layout_width="300dp"
			android:layout_height="wrap_content"
			android:minHeight="150dp"
			android:layout_centerHorizontal="true"
			android:layout_centerVertical="true"
			android:background="@drawable/sy37_bg_layout_change_account"
			>

			<LinearLayout
				android:id="@+id/layout_change_account_title"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_centerHorizontal="true"
				android:layout_marginTop="25dp"
				android:orientation="horizontal"
				>

				<TextView
					android:id="@+id/text_account"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:maxEms="8"
					android:singleLine="true"
					android:textColor="@color/sysq_orange" />

				<TextView
					android:id="@+id/textView2"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginLeft="10dp"
					android:text="@string/fg_logining" />
			</LinearLayout>


			<ProgressBar
				android:id="@+id/loading"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginTop="20dp"
				android:layout_below="@+id/layout_change_account_title"
				android:layout_centerHorizontal="true"
				android:indeterminateDrawable="@drawable/sy37_kefu_progress"
				android:indeterminateDuration="1500"
				/>

		</RelativeLayout>

	</RelativeLayout>


</FrameLayout>
