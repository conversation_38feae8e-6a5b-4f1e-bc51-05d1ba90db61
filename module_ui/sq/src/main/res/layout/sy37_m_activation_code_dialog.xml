<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent" >

	<RelativeLayout
		android:layout_width="350dp"
		android:layout_height="wrap_content"
		android:layout_centerInParent="true"
		android:background="@color/s_white"
		android:paddingBottom="20dp"
		android:paddingLeft="20dp"
		android:paddingRight="20dp" >

		<RelativeLayout
			android:id="@+id/sy37_m_layout_accode_title"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_alignParentTop="true" >

			<TextView
				android:id="@+id/sy37_m_active_text_accode_title"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:layout_centerHorizontal="true"
				android:layout_centerVertical="true"
				android:layout_margin="10dp"
				android:gravity="center"
				android:padding="5dp"
				android:text="游戏内测中"
				android:textColor="@color/s_orange"
				android:textSize="21dp" />

			<View
				android:layout_width="match_parent"
				android:layout_height="1px"
				android:layout_below="@+id/sy37_m_active_text_accode_title"
				android:background="@color/s_light_gray" />
		</RelativeLayout>

		<ScrollView
			android:id="@+id/scrollView1"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:scrollbars="@null"
			android:layout_alignLeft="@+id/sy37_m_layout_accode_title"
			android:layout_below="@+id/sy37_m_layout_accode_title" >

			<LinearLayout
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:orientation="vertical" >

				<TextView
					android:id="@+id/sy37_m_active_text_accode_desc"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginBottom="10dp"
					android:layout_marginTop="10dp"
					android:text="请输入激活码进入游戏"
					android:textColor="@color/s_gray"
					android:textSize="16dp" />
				<RelativeLayout
					android:layout_width="match_parent"
					android:layout_height="40dp"
					android:layout_marginBottom="20dp"
					android:layout_alignParentTop="true" >

					<EditText
						android:id="@+id/sy37_m_active_edit_accode"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:background="@drawable/sy37_bg_edit"
						android:gravity="center_vertical"
						android:hint="请输入激活码"
						android:paddingLeft="15dp"
						android:paddingRight="40dip"
						android:singleLine="true"
						android:textColor="@color/s_light_gray"
						android:textColorHint="@color/s_light_gray"
						android:textSize="18.0dp" />
					
					<ImageView
						android:id="@+id/sy37_m_active_img_clean"
						android:layout_width="40dip"
						android:layout_height="40dip"
						android:layout_alignParentRight="true"
						android:layout_centerVertical="true"
						android:padding="5dip"
						android:focusable="true"
						android:visibility="gone"
						android:src="@drawable/sy37_ad_item_del" />
					
				</RelativeLayout>
                <Button
					android:id="@+id/sy37_m_active_btn_confirm"
					android:layout_width="match_parent"
					android:layout_height="40dip"
					android:layout_marginBottom="10dp"
					android:background="@drawable/sy37_kefu_reg"
					android:text="@string/fg_confirm"
					android:textColor="@color/s_white"
					android:textSize="18dp" />

				<Button
					android:id="@+id/sy37_m_active_btn_get_code"
					android:layout_width="match_parent"
					android:layout_height="40dip"
					android:layout_marginBottom="10dp"
					android:background="@drawable/sy37_kefu_submit"
					android:text="领取激活码"
					android:textColor="@color/s_white"
					android:textSize="18dp" />
			</LinearLayout>
		</ScrollView>
	</RelativeLayout>

</RelativeLayout>