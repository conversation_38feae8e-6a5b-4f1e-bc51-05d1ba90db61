<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawableRight="@drawable/sy37_icon_go"
        android:padding="10dip"
        android:singleLine="true"
        android:textColor="@color/s_gray"
        android:textSize="16dp" />
    <View 
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/sy37_icon_list_diver" 
        android:layout_below="@+id/title"
       />
    	

</RelativeLayout>