<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    >

    <RelativeLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_dialog_bg_r_5"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="12dp"
        >

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="100dp"
            android:layout_height="36dp"
            android:src="@drawable/sy37_login_logo_new"
            android:visibility="visible"
            android:layout_marginTop="8dp"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sy37_user_login"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:textStyle="bold"
            android:textColor="@color/sy37_login_title_text_color"
            android:textSize="@dimen/sq_login_title_text_size"
            android:layout_marginTop="14dp"
            />

        <ImageView
            android:id="@+id/iv_doubt"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/sy37_icon_doubt_new"
            android:layout_alignParentRight="true"
            android:layout_marginTop="4dp"
            android:layout_alignParentEnd="true"
            android:layout_alignTop="@+id/tv_title"
            />

        <LinearLayout
            android:id="@+id/ll_account_row"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@drawable/sy37_shape_white_radius_2"
            android:layout_marginTop="17dp"
            android:gravity="center_vertical"
            android:layout_below="@+id/tv_title"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="账号"
                android:textColor="@color/sy37_login_account_text_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:layout_marginLeft="21dp"
                android:layout_marginStart="21dp" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/sy37_login_dialog_bg_f9fafd"
                android:layout_marginLeft="20dp"
                android:layout_marginStart="20dp" />

            <EditText
                android:id="@+id/et_account"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingLeft="19dp"
                android:hint="@string/sy37_input_account_hint"
                android:textColorHint="@color/sy37_login_account_hint_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:background="@android:color/transparent"
                android:inputType="textVisiblePassword"
                android:maxLength="20"
                android:singleLine="true"
                android:textColor="@color/sy37_login_account_text_color"
                />

            <ImageView
                android:id="@+id/iv_select_account"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:src="@drawable/sy37_user_set_down_bg_down"
                android:scaleType="center"
                />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_pwd_row"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@drawable/sy37_shape_white_radius_2"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:layout_below="@id/ll_account_row"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="密码"
                android:textColor="@color/sy37_login_account_text_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:layout_marginLeft="21dp"
                android:layout_marginStart="21dp" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/sy37_login_dialog_bg_f9fafd"
                android:layout_marginLeft="20dp"
                android:layout_marginStart="20dp" />

            <EditText
                android:id="@+id/et_pwd"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingLeft="19dp"
                android:hint="@string/sy37_input_password_hint"
                android:textColorHint="@color/sy37_login_account_hint_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:background="@android:color/transparent"
                android:maxLength="20"
                android:singleLine="true"
                android:inputType="textPassword"
                android:textColor="@color/sy37_login_account_text_color"
                />

            <ImageView
                android:id="@+id/iv_password_hide"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:src="@drawable/sy37_user_pwd_eye_close"
                android:scaleType="center"
                />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_forgot_pwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sy37_find_account_and_pwd"
            android:textColor="@color/sy37_login_account_text_color"
            android:textSize="@dimen/sq_login_view_sub_text_size"
            android:layout_below="@+id/ll_pwd_row"
            android:layout_alignParentRight="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="8dp"
            />

        <LinearLayout
            android:id="@+id/ll_btn_row"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_below="@+id/ll_pwd_row"
            android:layout_marginTop="37dp"
            android:layout_centerHorizontal="true"
            >

            <TextView
                android:id="@+id/tv_one_key_reg"
                android:layout_width="@dimen/sq_login_button_width"
                android:layout_height="@dimen/sq_login_button_height"
                android:text="@string/fg_reg_account"
                android:textColor="@color/sy37_login_btn_text_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:gravity="center"
                android:background="@drawable/sy37_shape_login_btn_bg_orange"
                />

            <TextView
                android:id="@+id/tv_login"
                android:layout_width="@dimen/sq_login_button_width"
                android:layout_height="@dimen/sq_login_button_height"
                android:text="@string/fg_login_btn"
                android:textColor="@color/sy37_login_btn_text_color"
                android:textSize="@dimen/sq_login_account_text_size"
                android:gravity="center"
                android:background="@drawable/sy37_shape_login_btn_bg_blue"
                android:layout_marginLeft="13dp"
                />

        </LinearLayout>

        <com.sqwan.common.widget.MarqueeTextView
            android:id="@+id/tv_sub_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sy37_focus_wechat_doubt"
            android:layout_below="@id/ll_btn_row"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="14dp"
            android:textColor="@color/sy37_login_account_hint_color"
            android:textSize="@dimen/sq_login_view_sub_text_size"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:clickable="true"
            android:enabled="true"
            android:focusable="true"
            android:ellipsize="marquee"
            android:gravity="center_vertical"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            />

    </RelativeLayout>

</FrameLayout>