<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

	<style name="Widget" parent="android:Widget"></style>

	<style name="RelativeLayout_Text">
		<item name="android:layout_width">100dip</item>
		<item name="android:layout_height">wrap_content</item>
	</style>

	<style name="RelativeLayout_Text2">
		<item name="android:layout_width">110dip</item>
		<item name="android:layout_height">wrap_content</item>
	</style>

	<!-- 定义一个样式，继承Android系统的对话框样式 android:style/Theme.Dialog -->
	<style name="Theme.FloatActivity" parent="android:style/Theme.Dialog">

		<!-- float_box为我们定义的窗口背景 -->
		<item name="android:windowBackground">@drawable/sy37_float_box</item>
		<item name="android:windowNoTitle">true</item>
		<item name="android:backgroundDimEnabled">false</item>
		<!--
模糊true,清洗false
       <item name="android:layout_alignParentBottom">true</item>

		-->
	</style>

	<style name="Theme.UPPay" parent="android:style/Theme.Light">
		<item name="android:windowNoTitle">true</item>
		<item name="android:windowContentOverlay">@null</item>
	</style>

	<style name="Layout_Text">
		<item name="android:layout_width">wrap_content</item>
		<item name="android:layout_height">wrap_content</item>
		<item name="android:scrollbars">none</item>
	</style>

	<!-- 返回按钮的样式 -->
	<style name="backBut">
		<item name="android:layout_width">90dp</item>
		<item name="android:layout_height">35dp</item>
		<item name="android:layout_marginRight">15dp</item>
		<item name="android:text">@string/fg_back</item>
		<item name="android:background">@drawable/sy37_reg</item>
		<item name="android:textColor">@color/s_white</item>
		<item name="android:textSize">20.0sp</item>
		<item name="android:gravity">center</item>
	</style>
	
	<!-- 官网 新闻、攻略、礼包样式 -->
	<style name="style_service_nav_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/sy37_bg_org</item>
        <item name="android:textColor">@color/s_white</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">15dip</item>
        <item name="android:paddingRight">20dip</item>
        <item name="android:paddingTop">7dp</item>
        <item name="android:paddingBottom">7dip</item>
    </style>
    
	<style name="style_service_nav_text_title">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">10dip</item>
        <item name="android:paddingBottom">5dip</item>
        <item name="android:paddingLeft">5dip</item>
        <item name="android:textColor">@color/s_gray</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:singleLine">true</item>
    </style>
    
    <!-- 账号管理  列表样式 -->
	<style name="style_account_menu_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">6dp</item>
        <item name="android:paddingLeft">5dp</item>
    </style>
    
	<!-- 悬浮窗内容样式 -->
	<style name="style_pop_text_content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginLeft">4dp</item>
        <item name="android:layout_marginRight">4dp</item>
        <item name="android:textColor">@color/s_white</item>
        <item name="android:textSize">10sp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:drawablePadding">0dp</item>
    </style>
    
	<!-- 悬浮窗展开功能内容样式 -->
	<style name="style_pop_image_content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">-7dp</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:layout_gravity">right|top</item>
        <item name="android:visibility">gone</item>
        <item name="android:src">@drawable/sy37_icon_pop_red</item>
    </style>

    <!-- 第三方登录内容样式 -->
    <style name="style_account_item_login_bg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/account_dialog_margin</item>
        <item name="android:layout_marginRight">@dimen/account_dialog_margin</item>
        <item name="android:background">@drawable/account_reg_item_bg</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingBottom">5dp</item>
    </style>

    <style name="style_account_reg_input">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#222121</item>
        <item name="android:textColorHint">#cccccc</item>
        <item name="android:background">@null</item>
    </style>
	
</resources>