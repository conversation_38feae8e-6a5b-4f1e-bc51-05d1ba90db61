<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="Mdialog" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowFrame">@null</item> <!-- 边框 -->
        <item name="android:windowIsTranslucent">true</item> <!-- 半透明 -->
        <item name="android:windowBackground">@android:color/transparent</item> <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="FullScreenDialogStyle" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="CustomDialog">
        <!--背景颜色及和透明程度-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--是否去除标题 -->
        <item name="android:windowNoTitle">true</item>
        <!--是否去除边框-->
        <item name="android:windowFrame">@null</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsFloating">true</item>
        <!--是否模糊-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--模糊程度-->
        <item name="android:backgroundDimAmount">0.5</item>
        <!--dialog宽度-->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="TransparentActivity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="ContentOverlay" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:stretchMode">spacingWidth</item>
        <!-- item name="@android:style/Theme.Light.NoTitleBar.Fullscreen">true</item -->
        <item name="android:theme">@android:style/Theme.Light.NoTitleBar.Fullscreen</item>
    </style>

    <style name="progressBarHorizontal" parent="@android:Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/sy37_progress_horizontal</item>
        <item name="android:minHeight">15dip</item>
        <item name="android:maxHeight">20dip</item>
    </style>

    <style name="kefu_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="Dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="sy37_transparent_web_dialog_style" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="progressDialog" parent="@android:style/Theme.Dialog">
    	<item name="android:windowBackground">@android:color/transparent</item>
		<item name="android:windowNoTitle">true</item>
		<item name="android:windowIsFloating">true</item>
		<item name="android:windowContentOverlay">@null</item>
		<item name="android:background">@null</item>
		<item name="android:backgroundDimEnabled">false</item>
	</style>

      <style name="sy37_mypopwindow_anim_style">
        <item name="android:windowEnterAnimation">@anim/sy37_popwindow_bottom_in</item>
        <!-- 指定显示的动画xml -->

        <item name="android:windowExitAnimation">@anim/sy37_popwindow_bottom_out</item>
        <!-- 指定消失的动画xml -->
    </style>

    <style name="dialogWindowAnim" parent="android:Animation" mce_bogus="1">
        <item name="android:windowEnterAnimation">@anim/dialog_anim_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_anim_exit</item>
    </style>

    <style name="sy37_guide_dialog_theme" parent="@android:style/Theme.Dialog">
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>

    <style name="sy37_alert_dialog_theme" parent="@android:style/Theme.Dialog">
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>


</resources>