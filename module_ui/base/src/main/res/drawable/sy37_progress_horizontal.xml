<?xml version="1.0" encoding="utf-8"?>
	<!--
		Copyright (C) 2008 The Android Open Source Project Licensed under the
		Apache License, Version 2.0 (the "License"); you may not use this file
		except in compliance with the License. You may obtain a copy of the
		License at http://www.apache.org/licenses/LICENSE-2.0 Unless required
		by applicable law or agreed to in writing, software distributed under
		the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
		CONDITIONS OF ANY KIND, either express or implied. See the License for
		the specific language governing permissions and limitations under the
		License.
	-->

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
	<item android:id="@android:id/background" android:drawable="@drawable/sy37_icon_null">
	</item>
<!-- 	<item android:id="@android:id/secondaryProgress" android:drawable="@drawable/progressbar_indeter1"> -->
<!-- 	</item> -->
	<item android:id="@android:id/progress" android:drawable="@drawable/sy37_bg_kefu_progess_f">
	</item>
	
<!-- 	 <item android:id="@android:id/background"> -->
<!--         <shape> -->
<!--             <corners android:radius="10dip" /> -->
<!--             <gradient -->
<!--                     android:startColor="#00000000" -->
<!--                     android:centerColor="#00000000" -->
<!--                     android:centerY="0.75" -->
<!--                     android:endColor="#00000000" -->
<!--                     android:angle="270" -->
<!--             /> -->
<!--         </shape> -->
<!--     </item> -->
    
<!--     <item android:id="@android:id/secondaryProgress"> -->
<!--         <clip> -->
<!--             <shape> -->
<!--                 <corners android:radius="5dip" /> -->
<!--                 <gradient -->
<!--                         android:startColor="#80ffd300" -->
<!--                         android:centerColor="#80ffb600" -->
<!--                         android:centerY="0.75" -->
<!--                         android:endColor="#a0ffcb00" -->
<!--                         android:angle="270" -->
<!--                 /> -->
<!--             </shape> -->
<!--         </clip> -->
<!--     </item> -->
    
<!--     <item android:id="@android:id/progress"> -->
<!--         <clip> -->
<!--             <shape> -->
<!--                 <corners android:radius="10dip" /> -->
<!--                 <gradient -->
<!--                         android:startColor="#ffffd300" -->
<!--                         android:centerColor="#ffffb600" -->
<!--                         android:centerY="0.75" -->
<!--                         android:endColor="#ffffcb00" -->
<!--                         android:angle="270" -->
<!--                 /> -->
<!--             </shape> -->
<!--         </clip> -->
<!--     </item> -->
	
</layer-list>

