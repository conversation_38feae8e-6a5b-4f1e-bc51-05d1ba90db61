<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/regByPhoneNumLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_below="@+id/tips_login_reg_view"
    android:gravity="center">

    <TableLayout
        android:id="@+id/regByPhoneNumInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:orientation="horizontal">

        <TableRow
            android:layout_weight="1.0"
            android:background="@drawable/sy37_bg_edit"
            android:gravity="bottom"
            android:padding="0dp">

            <EditText
                android:id="@+id/sy37_account_edit_phoneNum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1.0"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/fg_account_phone_num_hint"
                android:inputType="phone"
                android:maxLength="11"
                android:paddingLeft="15dp"
                android:singleLine="true"
                android:textColor="@color/s_light_gray"
                android:textColorHint="@color/s_light_gray"
                android:textSize="@dimen/sq_edt_text_size">

            </EditText>


            <Button
                android:id="@+id/sy37_btn_getVerifyCode"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/sy37_bg_get_verify_code"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:text="@string/fg_account_get_verifyCode"
                android:textColor="@color/sy37_text_color_change"
                android:textSize="@dimen/sq_edt_text_size" />

        </TableRow>

        <TableRow
            android:layout_marginTop="10dp"
            android:layout_weight="1.0"
            android:background="@drawable/sy37_bg_edit"
            android:gravity="top"
            android:padding="0dp">

            <EditText
                android:id="@+id/sy37_account_edit_verifyCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1.0"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/fg_account_verifyCode_hint"
                android:inputType="number"
                android:maxLength="10"
                android:paddingLeft="15dp"
                android:singleLine="true"
                android:textColor="@color/s_light_gray"
                android:textColorHint="@color/s_light_gray"
                android:textSize="@dimen/sq_edt_text_size" />

        </TableRow>

    </TableLayout>

    <LinearLayout
        android:id="@+id/sy37_reg_by_phonenum_clause_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/regByPhoneNumInfo"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/sy37_reg_by_phonenum_clause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/sy37_check_selector"
            android:checked="true"
            android:text="@string/fg_reg_clause_tips_1"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />

        <TextView
            android:id="@+id/sy37_reg_by_phonenum_clause_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_clause_tips_2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />
    </LinearLayout>

    <Button
        android:id="@+id/sy37_account_btn_regByPhoneNum"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sq_btn_height"
        android:layout_alignLeft="@id/regByPhoneNumInfo"
        android:layout_alignRight="@id/regByPhoneNumInfo"
        android:layout_below="@id/sy37_reg_by_phonenum_clause_view"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/sy37_kefu_reg"
        android:text="@string/fg_reg_account_now"
        android:textColor="@color/s_white"
        android:textSize="@dimen/sq_btn_text_size" />

    <TextView
        android:id="@+id/sy37_reg_by_phone_text_has_account2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/sy37_account_btn_regByPhoneNum"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_has_account"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

    <TextView
        android:id="@+id/sy37_reg_text_by_email2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/sy37_account_btn_regByPhoneNum"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_by_email"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

</RelativeLayout>