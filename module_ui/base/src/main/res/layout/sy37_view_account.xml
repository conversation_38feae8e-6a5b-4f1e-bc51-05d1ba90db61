<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:id="@+id/commonLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

        <FrameLayout
            android:id="@android:id/tabcontent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            >

            <com.sqwan.common.widget.BaseViewPager
                android:id="@+id/dateViews"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ProgressBar
                android:id="@+id/waiting"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|top"
                android:layout_marginTop="30dip"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateDrawable="@drawable/sy37_kefu_progress" />
        </FrameLayout>
    </RelativeLayout>

</RelativeLayout>