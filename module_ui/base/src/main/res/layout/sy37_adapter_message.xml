<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="blocksDescendants"
    android:gravity="center_vertical"
    android:paddingRight="3dip" >

    <ImageView
        android:id="@+id/icon"
        android:layout_width="50dip"
        android:layout_height="50dip"
        android:layout_marginBottom="3dip"
        android:layout_marginRight="3dip"
        android:layout_marginTop="3dip"
        android:src="@drawable/sy37_icon_default" />

    <TextView
        android:id="@+id/desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/title"
        android:layout_below="@id/title"
        android:layout_toLeftOf="@id/time"
        android:gravity="bottom|center_vertical"
        android:lines="2"
        android:paddingLeft="5dip"
        android:textColor="@color/kefu_gray"
        android:textSize="@dimen/sq_item_text_size" />

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/desc"
        android:layout_alignParentRight="true"
        android:layout_below="@id/title"
        android:layout_marginRight="5dip"
        android:gravity="bottom|center_vertical"
        android:paddingLeft="5dip"
        android:singleLine="true"
        android:textColor="@color/kefu_gray"
        android:textSize="@dimen/sq_item_text_size" />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/icon"
        android:paddingLeft="5dip"
        android:paddingTop="3dip"
        android:singleLine="true"
        android:textColor="@color/kefu_black"
        android:textSize="@dimen/kefu_list_title" />

</RelativeLayout>