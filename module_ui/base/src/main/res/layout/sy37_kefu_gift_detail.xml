<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="250dip"
    android:layout_height="250dip"
    android:background="#00000000"
    android:gravity="center"
     >
	    
    <LinearLayout
        android:id="@+id/titleLayout"
        android:layout_width="250dip"
        android:layout_height="wrap_content"
        android:background="@drawable/sy37_bg_kefu_gift_detail"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <ImageView
            android:id="@+id/icon"
            android:layout_width="50dip"
            android:layout_height="50dip"
            android:layout_margin="5dip"
            android:src="@drawable/sy37_icon_default" />

        <com.sqwan.common.widget.MarqueeTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/s_white"
            android:textSize="18dp" />

        <ImageView
            android:id="@+id/close"
            android:layout_width="40dip"
            android:layout_height="40dip"
            android:layout_gravity="top"
            android:padding="5dip"
            android:src="@drawable/sy37_ad_item_del" />
    </LinearLayout>

    <ScrollView
    	android:id="@+id/contentLayout"
        android:layout_width="250dip"
        android:layout_height="150dip"
        android:paddingBottom="10dip"
        android:scrollbars="none"
        android:layout_below="@+id/titleLayout"
        android:background="@drawable/sy37_bg_kefu_gift_detail_content">

        <TextView
            android:id="@+id/content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="10dip"
            android:paddingLeft="10dip"
            android:paddingRight="10dip"
            android:paddingTop="7dip"
            android:textColor="@color/kefu_black"
            android:textSize="14dp" />
    </ScrollView>


    <Button
        android:id="@+id/button"
        android:layout_width="150dip"
        android:layout_height="40dp"
        android:layout_marginTop="160dip"
        android:background="@drawable/sy37_btn_commit_selector"
        android:text="复制"
        android:textColor="@color/button_checked"
        android:textSize="18dp"
        android:layout_centerHorizontal="true"
        android:visibility="gone" />
</RelativeLayout>