<?xml version="1.0" encoding="utf-8"?><!-- com.sy37sdk.account.ui.widget.AccountLoginView -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/account_dialog_margin"
        android:layout_marginRight="@dimen/account_dialog_margin"
        android:background="@drawable/account_dialog_bg">

        <RelativeLayout
            android:id="@+id/login_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="5dp"
            android:layout_marginRight="@dimen/account_dialog_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/account_user_login"
                android:textColor="#222121"
                android:textSize="22dp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ic_help"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_tip" />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/ll_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/login_title"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/phone_layout"
                style="@style/style_account_item_login_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/ic_login_phone" />

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/style_account_reg_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:digits="**********"
                    android:hint="@string/account_input_hint_phone"
                    android:maxLength="11" />

                <ImageView
                    android:id="@+id/account_list"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/ic_account_list" />
            </LinearLayout>

            <LinearLayout
                style="@style/style_account_item_login_bg"
                android:layout_marginTop="5dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    android:src="@drawable/ic_login_psd" />

                <EditText
                    android:id="@+id/et_psd"
                    style="@style/style_account_reg_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:hint="@string/account_input_hint_psd"
                    android:inputType="textPassword"
                    android:maxLines="1" />

                <ImageView
                    android:id="@+id/iv_psd"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/ic_psd_close" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_login"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/account_dialog_margin"
            android:background="@drawable/account_reg_bg"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="@string/account_text_login"
            android:textColor="#ffffff"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/reg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/login"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/account_phone_reg"
            android:textColor="#47a2ff"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/forget_psd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/login"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/account_forgot_psd"
            android:textColor="#ff9900"
            android:textSize="14dp" />

        <LinearLayout
            android:id="@+id/ll_social_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/reg"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginRight="@dimen/account_dialog_margin"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"
                android:background="#222121" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:text="@string/account_tip_social_login"
                android:textColor="#222121"
                android:textSize="14dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"
                android:background="#222121" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_social"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@+id/ll_social_tip"
            android:layout_marginLeft="@dimen/account_dialog_margin"
            android:layout_marginTop="8dp"
            android:layout_marginRight="@dimen/account_dialog_margin"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_qq_login"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_reg_qq" />

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_wechat_login"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_reg_wechat" />

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />
        </LinearLayout>

        <com.sqwan.common.widget.MarqueeTextView
            android:id="@+id/official_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_social"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:drawablePadding="10dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:gravity="center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:paddingTop="2dp"
            android:paddingRight="5dip"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:text="@string/fg_welcome"
            android:textColor="#ff9a9a9a"
            android:textSize="13dp" />
    </RelativeLayout>
</RelativeLayout>