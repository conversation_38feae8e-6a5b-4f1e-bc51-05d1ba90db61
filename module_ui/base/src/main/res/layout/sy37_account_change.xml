<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_height="match_parent"
	android:layout_width="match_parent"
	>
	<RelativeLayout
		android:layout_width="match_parent"
		android:layout_height="56dp"
		android:background="@android:color/white"
		android:gravity="center_vertical"
		>


		<ProgressBar
			android:id="@+id/loading"
			android:layout_width="16dp"
			android:layout_height="16dp"
			android:indeterminateDrawable="@drawable/kumo_icon_loading"
			android:indeterminateDuration="1500"
			android:layout_marginLeft="20dp"
			android:layout_centerVertical="true"
			/>

		<TextView
			android:id="@+id/text_account"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="jua45648213"
			android:textColor="@color/kumo_auto_login_account_txt_color"
			android:textSize="15dp"
			android:layout_toRightOf="@+id/loading"
			android:layout_marginLeft="12dp"
			android:layout_centerVertical="true"
			/>

		<TextView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:text="，正在登录中..."
			android:textColor="@color/kumo_auto_login_account_txt_color"
			android:textSize="15dp"
			android:layout_toRightOf="@id/text_account"
			android:layout_centerVertical="true"
			/>


		<Button
			android:id="@+id/btn_changeAccount"
			android:layout_width="92dp"
			android:layout_height="27dp"
			android:text="切换账号"
			android:textColor="@color/kumo_auto_login_account_txt_color"
			android:textSize="15dp"
			android:layout_alignParentRight="true"
			android:background="@drawable/kumo_get_verify_code_btn_bg"
			android:gravity="center"
			android:layout_marginRight="14dp"
			android:layout_centerVertical="true"
			android:drawableLeft="@drawable/kumo_icon_change_account"
			android:paddingLeft="6dp"
			/>


	</RelativeLayout>


</FrameLayout>
