<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	>

    <com.sqwan.common.webview.SQWebView
    	android:id="@+id/sy37_m_webview_notice"
    	android:layout_width="match_parent"
    	android:layout_height="match_parent" />

	<ImageView
		android:id="@+id/sy37_m_img_notice_close"
		android:layout_width="28dp"
		android:layout_height="28dp"
		android:layout_alignParentRight="true"
		android:layout_alignParentTop="true"
		android:layout_centerVertical="true"
		android:layout_marginTop="8dp"
		android:layout_marginRight="8dp"
		android:scaleType="fitXY"
		android:src="@drawable/sy37_icon_close"
		/>

</RelativeLayout>
