<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parentLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    >

    <RelativeLayout
        android:id="@+id/contentLayout"
        android:layout_width="350dp"
        android:layout_height="335dip"
        android:layout_centerVertical="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/sy3y_regs_bg">


        <ImageView
            android:id="@+id/regs_line"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/ltgray"
            android:layout_marginBottom="56dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true" />

        <TextView
            android:text="进入游戏"
            android:textColor="#1C86EE"
            android:textSize="23dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="15dp"
            android:id="@+id/auto_regs_gogame" />

        <ImageView
            android:src="@drawable/sy37_login_logo_new"
            android:layout_width="100dp"
            android:layout_height="50dp"
            android:layout_marginLeft="10dp"
            android:id="@+id/auto_regs_sqlogo"/>

        <TextView
            android:text="注册成功"
            android:textColor="@color/s_orange"
            android:textSize="23dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/auto_regs_title"
            android:layout_below="@+id/auto_regs_sqlogo"
            android:layout_alignLeft="@+id/auto_regs_gogame"
            android:layout_alignStart="@+id/auto_regs_gogame" />

        <LinearLayout
            android:id="@+id/reg_tips_layout"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="35dp"
            android:layout_below="@+id/regs_bg"
            android:visibility="visible">

            <TextView
                android:text="您的37手游账号已注册成功！"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:id="@+id/auto_regs_msg"
                android:textColor="@color/s_gray"
                android:layout_gravity="center"
                android:textSize="15dp"/>

            <TextView
                android:text="请截图保存"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:id="@+id/auto_regs_ssuccess"
                android:textColor="@color/s_gray"
                android:layout_gravity="center"
                android:textSize="15dp"/>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/qr_code_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/regs_bg"
            android:layout_above="@+id/regs_line"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:layout_centerHorizontal="true"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_qr_code"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:scaleType="fitXY" />

            <TextView
                android:id="@+id/tv_qr_code_content"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_below="@+id/iv_qr_code"
                android:gravity="center"
                android:textSize="10dp"
                android:maxLines="2"
                android:text="游戏下载二维码" />

            <TextView
                android:id="@+id/tv_reg_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="3dp"
                android:gravity="right"
                android:text="您的37手游账号已注册成功\n请截图保存"
                android:textColor="@color/s_gray" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/regs_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:layout_marginLeft="25dp"
            android:layout_marginRight="25dp"
            android:background="@drawable/sy3y_regs_bg_orange_bg"
            android:layout_below="@+id/auto_regs_title"
            android:layout_centerHorizontal="true">

            <TextView
                android:text="账号：37sy157485"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/auto_regs_account"
                android:gravity="center"
                android:textSize="18dp"
                android:textColor="@color/button_checked"
                android:layout_marginTop="10dp"
                />

            <TextView
                android:layout_below="@+id/auto_regs_account"
                android:text="密码:aaa"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/auto_regs_pwd"
                android:gravity="center"
                android:textSize="18dp"
                android:textColor="@color/button_checked"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                />

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>