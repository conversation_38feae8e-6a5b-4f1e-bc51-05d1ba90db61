<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:layout_gravity="center"
	>

	<FrameLayout
		android:id="@+id/fl_web_container"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:layout_marginTop="16dp"
		android:layout_marginLeft="16dp"
		android:layout_marginRight="16dp"
		android:background="@android:color/white"
		android:scaleType="centerInside"/>

    <RelativeLayout
    	android:layout_width="match_parent"
    	android:layout_height="wrap_content"
    	android:id="@+id/sy37_m_layout_payweb_title"
    	android:layout_alignParentTop="true"
    	android:background="@android:color/transparent"
    	>

        <ImageView
        	android:id="@+id/sy37_m_img_payweb_close"
        	android:layout_width="32dp"
        	android:layout_height="32dp"
        	android:layout_alignParentRight="true"
        	android:layout_centerVertical="true"
			android:padding="1dp"
        	android:src="@drawable/sy_payweb_close_new" />

    </RelativeLayout>

</FrameLayout>
