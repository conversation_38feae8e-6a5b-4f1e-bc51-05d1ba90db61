<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:gravity="center"
	android:background="#00ffffff"
	>

	<RelativeLayout
		android:id="@+id/rl_loading_img"
		android:layout_width="130dp"
		android:layout_height="130dp"
		android:padding="10dp"
		android:layout_centerInParent="true"
		android:gravity="center"
		>

	    <TextView
			android:id="@+id/msg"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_centerHorizontal="true"
			android:layout_marginTop="15dp"
			android:layout_marginBottom="15dp"
			android:gravity="center"
			android:maxLines="2"
			android:paddingLeft="3dp"
			android:ellipsize="middle"
			android:text="加载中... "
			android:textColor="#FFFFFF"
			android:textSize="14dp"
			android:visibility="visible" />
	    
		<ProgressBar
			android:id="@+id/progress"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_centerHorizontal="true"
			android:layout_below="@+id/msg"
			android:indeterminateDuration="1000" />

		
	</RelativeLayout>

</RelativeLayout>