<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@null"
    >

    <RelativeLayout
        android:layout_width="360dp"
        android:layout_height="360dp"
        android:layout_centerVertical="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/sy37_shape_white_radius_4"
        >

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/kumo_warming_update"
            android:textColor="@color/kumo_update_warming_title_color"
            android:textSize="18dp"
            android:layout_centerHorizontal="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="11dp"/>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/kumo_icon_update_close"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:layout_marginRight="12dp"
            android:layout_marginTop="12dp"
            />



        <ScrollView
            android:id="@+id/content"
            android:layout_width="328dp"
            android:layout_height="195.5dp"
            android:scrollbars="vertical"
            android:scrollbarThumbVertical="@null"
            android:scrollbarTrackVertical="@null"
            android:scrollbarStyle="insideInset"
            android:layout_centerHorizontal="true"
            android:layout_below="@id/title"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:paddingRight="3dp">

            <TextView
                android:id="@+id/update_notice"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="left|top"
                android:text="版本更新内容：\n1) 增加世界聊天等级限制及屏蔽词过滤，和谐世界聊天环境 \n2) 优化新手引导，更加流畅 \n3) 优化实时竞技体验问题 ，新版体验更加流畅 \n4) 优化强化功能 \n5) 优化角色动作 ，新版本角色攻击动作更加流畅 \n6) 修复已知bug \n7) 修复好友关系不显示的问题"
                android:textColor="#383838"
                android:textSize="12dp"
                android:textStyle="normal"
                android:paddingTop="20dp"
                android:paddingLeft="10dp"
                android:paddingBottom="20dp"/>

        </ScrollView>

        <RelativeLayout
            android:id="@+id/progress_view"
            android:layout_width="match_parent"
            android:layout_height="38.5dp"
            android:layout_below="@id/content">

            <TextView
                android:id="@+id/update_size_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:paddingTop="5dp"
                android:text="@string/kumo_loading_txt"
                android:textColor="@color/kumo_loading_txt_color"
                android:textSize="12dp"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/update_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="12dp"
                android:textStyle="normal"
                android:textColor="@color/kumo_loading_txt_color"
                android:text=" 0%"
                android:paddingTop="5dp"
                android:layout_alignParentTop="true"
                android:layout_toRightOf="@+id/update_size_tips"
                />

            <ProgressBar
                android:id="@+id/progressbar"
                android:layout_width="match_parent"
                android:layout_height="9dp"
                android:indeterminateDrawable="@android:drawable/progress_indeterminate_horizontal"
                android:indeterminateOnly="false"
                android:max="100"
                android:maxHeight="3dp"
                android:minHeight="1dp"
                android:progressDrawable="@drawable/kumo_update_progressbar"
                android:secondaryProgress="0"
                android:layout_below="@id/update_size"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"/>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_height="35dp"
            android:layout_below="@id/progress_view"
            android:orientation="horizontal"
            >

            <Button
                android:id="@+id/hide_btn"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:background="@drawable/kumo_bt_lowlight"
                android:text="取消"
                android:textColor="@color/kumo_update_cancel_txt_color"
                android:textSize="18dp"
                android:layout_marginRight="16dp"
                />

            <Button
                android:id="@+id/stop_start_btn"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:background="@drawable/kumo_bt_higlight"
                android:text="开始下载"
                android:textColor="#ffffff"
                android:textSize="18dp"
                />

        </LinearLayout>

        <TextView
            android:text="温馨提示：完成更新后 , 有更好玩的内容等着您哦"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:textColor="#5e5e5e"
            android:textSize="11dp"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="18dp"/>

    </RelativeLayout>

</RelativeLayout>