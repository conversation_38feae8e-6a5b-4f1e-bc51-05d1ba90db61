<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/walletLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <LinearLayout
            android:id="@+id/walletBtnLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/sy37_layout_white_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="2.0dip" >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15.0dip"
                android:src="@drawable/sy37_my_wallet" />

            <TextView
                android:id="@+id/listViewTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8.0dip"
                android:layout_weight="1.0"
                android:text="@string/fg_my_wallet"
                android:textColor="@color/s_orange"
                android:textSize="20dp" />

            <Button
                android:id="@+id/walletBack"
                style="@style/backBut"/>
        </LinearLayout>
        <!-- android:background="@drawable/sy37_btn_user_set_center_bg_up" -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dip"
            android:background="@drawable/sy37_bg_menu_two"
            android:orientation="vertical" >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dip"
                android:layout_marginBottom="17dp"
                android:paddingTop="3dp"
                android:gravity="center_vertical"
                android:layout_gravity="center"
                android:orientation="horizontal" >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15.0dip"
                    android:text="@string/fg_money"
                    android:textColor="@color/s_gray"
                    android:textSize="18.0dp" />

                <TextView
                    android:id="@+id/money"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0元"
                    android:textColor="@color/s_orange"
                    android:textSize="18.0dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal" >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15.0dip"
                    android:text="@string/fg_my_point"
                    android:textColor="@color/s_gray"
                    android:textSize="18.0dp" />

                <TextView
                    android:id="@+id/point"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0点"
                    android:textColor="@color/s_orange"
                    android:textSize="18.0dp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal" 
            android:layout_marginTop="10dp"
            >

            <Button
                android:id="@+id/walletSub"
                android:layout_width="match_parent"
                android:layout_height="40dip"
                android:layout_marginLeft="3.0dip"
                android:layout_marginRight="15dip"
                android:layout_weight="1"
                android:background="@drawable/sy37_kefu_submit"
                android:text="@string/fg_wallet_sub"
                android:textColor="@color/s_white"
                android:textSize="23dp" />

            <Button
                android:id="@+id/walletGift"
                android:layout_width="match_parent"
                android:layout_height="40dip"
                android:layout_marginLeft="15dip"
                android:layout_marginRight="3.0dip"
                android:layout_weight="1"
                android:background="@drawable/sy37_kefu_submit"
                android:text="@string/fg_wallet_gift"
                android:textColor="@color/s_white"
                android:textSize="23dp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dip"
            android:gravity="center_vertical"
            android:orientation="vertical" >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dip"
                android:text="@string/fg_wallet_tips_title"
                android:textColor="@color/s_gray"
                android:textSize="16.0dp" />

            <com.sqwan.common.widget.MarqueeTextView
                 android:id="@+id/walletTips"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dip"
                android:layout_marginRight="10dip"
                android:layout_marginTop="3dp"
                android:ellipsize="marquee"
                android:focusable="true"
                android:gravity="center|left"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:text="@string/fg_wallet_tips_content"
                android:textColor="@color/s_light_gray"
                android:textSize="14.0dp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>