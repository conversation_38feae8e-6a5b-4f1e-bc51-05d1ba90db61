<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/regByPhoneLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_below="@+id/tips_login_reg_view"
    android:layout_centerVertical="true"
    android:gravity="center">

    <RelativeLayout
        android:id="@+id/regByPhoneBtnLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginTop="15dp"
        android:background="@drawable/sy37_bg_reg_by_phone">

        <ImageView
            android:id="@+id/imageView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/sy37_icon_reg_phone" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="6dp"
            android:layout_toRightOf="@+id/imageView1"
            android:text="@string/fg_reg_login_now"
            android:textColor="#ffffff"
            android:textSize="25dp" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/textView1"
            android:layout_below="@+id/textView1"
            android:text="@string/fg_reg_by_phone_tips"
            android:textColor="#ffffff" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/agreecontaniner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/regByPhoneBtnLayout"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/sy37_reg_by_phone_clause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/sy37_check_selector"
            android:checked="true"
            android:text="@string/fg_reg_clause_tips_1"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />


        <TextView
            android:id="@+id/sy37_reg_by_phone_clause_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_clause_tips_2"
            android:textColor="#696969"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="@dimen/sq_agree_text1_size" />
    </LinearLayout>

    <TextView
        android:id="@+id/sy37_reg_by_phone_text_has_account"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/agreecontaniner"
        android:layout_marginTop="10dp"
        android:padding="0dp"
        android:text="@string/fg_reg_has_account"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

    <TextView
        android:id="@+id/sy37_reg_text_by_email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/agreecontaniner"
        android:layout_marginTop="10dp"
        android:padding="0dp"
        android:text="@string/fg_reg_by_email"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />


</RelativeLayout>