<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/root"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/sy37_confirm_dialog_bg"
        android:gravity="center"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="20dp"
                android:text="图片已保存到相册"
                android:textColor="@color/sy37_share_save_img"
                android:textSize="16dp" />

            <RelativeLayout
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="20dp">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_close_confirm_dialog" />
            </RelativeLayout>
        </RelativeLayout>


        <ImageView
            android:id="@+id/img_preview"
            android:layout_width="225dp"
            android:layout_height="400dp"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:id="@+id/open_platform"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/sy37_open_wx_bg"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="20dp"
            android:paddingTop="10dp"
            android:paddingRight="20dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@+id/tv_share_way"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="去微信分享给好友"
                android:textColor="@color/s_white"
                android:textSize="14dp" />
        </LinearLayout>
    </LinearLayout>


</RelativeLayout>