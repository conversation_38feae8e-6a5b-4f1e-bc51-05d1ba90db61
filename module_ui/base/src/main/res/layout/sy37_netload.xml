<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <LinearLayout
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" >
        <ProgressBar
            android:id="@+id/waiting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateDrawable="@drawable/sy37_kefu_progress"
            android:indeterminateDuration="1000" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/reload"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone" >

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/sy37_disconnected" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5.0dip"
            android:text="@string/fg_net_err"
            android:textColor="#ff293842"
            android:textSize="16.0dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5.0dip"
            android:text="@string/fg_net_again"
            android:textColor="#ffa0a0a0"
            android:textSize="12.0dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_net_click"
            android:textColor="#ffa0a0a0"
            android:textSize="12.0dp" />
    </LinearLayout>

</LinearLayout>