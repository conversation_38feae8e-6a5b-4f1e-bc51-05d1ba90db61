<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    >

    <RelativeLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_dialog_bg_r_5"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="12dp"
        >

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="100dp"
            android:layout_height="36dp"
            android:src="@drawable/sy37_login_logo_new"
            android:visibility="visible"
            android:layout_marginTop="8dp"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_account"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:textStyle="bold"
            android:textColor="@color/sy37_login_title_text_color"
            android:textSize="@dimen/sq_login_title_text_size"
            android:layout_marginTop="14dp"
            />

        <ImageView
            android:id="@+id/iv_help"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/sy37_icon_doubt_new"
            android:layout_alignParentRight="true"
            android:layout_marginTop="14dp"
            android:layout_alignParentEnd="true"
            />

        <LinearLayout
            android:id="@+id/ll_input_container"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_title"
            android:layout_marginTop="17dp"
            android:clickable="true"
            android:focusable="true"
            >

            <RelativeLayout
                android:id="@+id/rl_one_key_reg"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@drawable/sy37_shape_one_key_bg"
                android:visibility="visible"
                >

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/sy37_icon_phone_large"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9dp"
                    />

                <View
                    android:id="@+id/v_divider"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@android:color/white"
                    android:layout_marginLeft="58dp"
                    />

                <TextView
                    android:id="@+id/tv_reg_tip_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sy37_one_reg_tips_1"
                    android:textSize="@dimen/sq_login_title_text_size"
                    android:textColor="@color/sy37_login_btn_text_color"
                    android:layout_toRightOf="@+id/v_divider"
                    android:layout_marginLeft="19dp"
                    android:layout_marginTop="9dp"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sy37_one_reg_tips_2"
                    android:textSize="@dimen/sq_one_reg_tip_txt_size"
                    android:textColor="@color/sy37_login_btn_text_color"
                    android:layout_toRightOf="@+id/v_divider"
                    android:layout_marginLeft="19dp"
                    android:layout_below="@+id/tv_reg_tip_1"
                    />

            </RelativeLayout>

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_input_container"
            android:layout_marginTop="8dp"
            >

            <CheckBox
                android:id="@+id/cb_reg_clause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fg_reg_clause_tips_1"
                android:textSize="@dimen/sq_login_view_sub_text_size"
                android:textColor="@color/sy37_login_account_text_color"
                android:button="@drawable/sy37_check_selector"
                android:paddingLeft="5dp"
                android:gravity="center_vertical"
                android:checked="true"
                />

            <TextView
                android:id="@+id/tv_reg_clause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fg_reg_clause_tips_2"
                android:textSize="@dimen/sq_login_view_sub_text_size"
                android:textColor="@color/sy37_login_account_text_color"
                android:maxLines="1"
                android:ellipsize="end"
                />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_reg"
            android:layout_width="@dimen/sq_reg_button_width"
            android:layout_height="@dimen/sq_login_button_height"
            android:text="@string/fg_reg_login_now"
            android:textColor="@color/sy37_login_btn_text_color"
            android:textSize="@dimen/sq_login_account_text_size"
            android:gravity="center"
            android:background="@drawable/sy37_shape_login_btn_bg_orange"
            android:layout_marginTop="172dp"
            android:layout_centerHorizontal="true"
            />

        <TextView
            android:id="@+id/tv_has_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_has_account"
            android:textColor="@color/sy37_login_btn_bg_blue"
            android:layout_below="@+id/tv_reg"
            android:layout_marginTop="14dp"
            android:drawableLeft="@drawable/sy37_icon_has_account"
            />

        <TextView
            android:id="@+id/tv_other_reg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sy37_account_reg"
            android:textColor="@color/sy37_login_btn_bg_orange"
            android:layout_below="@+id/tv_reg"
            android:layout_marginTop="14dp"
            android:layout_alignParentRight="true"
            android:drawableLeft="@drawable/sy37_icon_reg_by_user"
            />

    </RelativeLayout>

</FrameLayout>