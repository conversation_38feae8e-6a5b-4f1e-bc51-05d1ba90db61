<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
         android:id="@+id/share_wx_view"
         android:layout_width="fill_parent"
         android:layout_height="wrap_content"
         android:gravity="center"
         android:layout_alignParentBottom="true"
         android:paddingBottom="10dp"
         android:paddingTop="10dp"
         android:background="@color/sysq_black"
         >

         <ImageView
             android:id="@+id/share_wx_sessions"
             android:layout_width="80dp"
             android:layout_height="50dp"
             android:layout_weight="1"
             android:src="@drawable/sy37_share_wx_sessions"
             />

         <ImageView
             android:id="@+id/share_wx_online"
             android:layout_width="80dp"
             android:layout_height="50dp"
             android:layout_weight="1"
              android:src="@drawable/sy37_share_wx_online"
             />
         

    </LinearLayout>
</RelativeLayout>