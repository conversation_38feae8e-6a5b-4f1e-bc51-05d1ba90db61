<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/regLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_below="@+id/tips_login_reg_view"
    android:gravity="center">

    <TableLayout
        android:id="@+id/regInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:orientation="horizontal">

        <TableRow
            android:layout_weight="1.0"
            android:background="@drawable/sy37_bg_edit"
            android:gravity="bottom"
            android:padding="0dp">

            <TextView
                android:id="@+id/text_flag_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingLeft="15dip"
                android:text="@string/fg_account"
                android:textColor="@color/s_gray"
                android:textSize="@dimen/sq_edt_text_size" />

            <EditText
                android:id="@+id/regName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1.0"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/fg_reg_account_hint"
                android:maxLength="20"
                android:singleLine="true"
                android:inputType="textVisiblePassword"
                android:textColor="@color/s_light_gray"
                android:textColorHint="@color/s_light_gray"
                android:textSize="@dimen/sq_edt_text_size">

            </EditText>

        </TableRow>

        <TableRow
            android:layout_marginTop="10dp"
            android:layout_weight="1.0"
            android:background="@drawable/sy37_bg_edit"
            android:gravity="top"
            android:padding="0dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingLeft="15dip"
                android:text="@string/fg_pw"
                android:textColor="@color/s_gray"
                android:textSize="@dimen/sq_edt_text_size" />

            <EditText
                android:id="@+id/regPW"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1.0"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/fg_reg_pw_hint"
                android:maxLength="20"
                android:singleLine="true"
                android:inputType="textVisiblePassword"
                android:textColor="@color/s_light_gray"
                android:textColorHint="@color/s_light_gray"
                android:textSize="@dimen/sq_edt_text_size" />

        </TableRow>
    </TableLayout>

    <LinearLayout
        android:id="@+id/sy37_reg_clause_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/regInfo"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <CheckBox
            android:id="@+id/sy37_reg_clause"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/sy37_check_selector"
            android:checked="true"
            android:text="@string/fg_reg_clause_tips_1"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />


        <TextView
            android:id="@+id/sy37_reg_clause_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fg_reg_clause_tips_2"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#696969"
            android:textSize="@dimen/sq_agree_text1_size" />

    </LinearLayout>


    <Button
        android:id="@+id/regBtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sq_btn_height"
        android:layout_alignLeft="@id/regInfo"
        android:layout_alignRight="@id/regInfo"
        android:layout_below="@id/sy37_reg_clause_view"
        android:layout_marginBottom="5dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/sy37_kefu_reg"
        android:text="@string/fg_reg_account_now"
        android:textColor="@color/s_white"
        android:textSize="@dimen/sq_btn_text_size" />


    <TextView
        android:id="@+id/sy37_reg_text_has_account"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/regBtn"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_has_account"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

    <TextView
        android:id="@+id/sy37_reg_text_by_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/regBtn"
        android:layout_marginTop="10dp"
        android:text="@string/fg_reg_by_phone"
        android:textColor="@color/sy37_text_reg_selector"
        android:textSize="@dimen/sq_btn_text_size" />

    <!--<TextView-->
    <!--android:id="@+id/sy37_reg_text_has_account"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_alignParentLeft="true"-->
    <!--android:layout_alignParentBottom="true"-->
    <!--android:textSize="@dimen/sq_btn_text_size" -->
    <!--android:textColor="@color/sy37_text_reg_selector"-->
    <!--android:text="@string/fg_reg_has_account"-->
    <!--/>-->

    <!--<TextView-->
    <!--android:id="@+id/sy37_reg_text_by_phone"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_alignParentRight="true"-->
    <!--android:layout_alignParentBottom="true"-->
    <!--android:text="@string/fg_reg_by_phone"-->
    <!--android:textColor="@color/sy37_text_reg_selector"-->
    <!--android:textSize="@dimen/sq_btn_text_size" />-->

</RelativeLayout>