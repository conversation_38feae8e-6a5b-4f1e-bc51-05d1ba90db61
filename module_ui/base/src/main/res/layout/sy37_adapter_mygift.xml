<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" 
    android:paddingBottom="3dip"
    android:gravity="center_vertical">
	
     <ImageView
        android:id="@+id/icon"
        android:layout_width="55dip"
        android:layout_height="55dip"
        android:layout_margin="5dip"
        android:src="@drawable/sy37_icon_gift_default" />
    
    <TextView
        android:id="@+id/title"
        android:layout_toRightOf="@id/icon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:paddingTop="5dip"
        android:textColor="@color/kefu_black"
        android:textSize="14dp"/>

    <TextView
        android:id="@+id/desc"
        android:layout_alignLeft="@id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title"
        android:maxLines="2"
        android:paddingRight="8dip"
        android:text="@string/fg_gift_card"
        android:textColor="@color/kefu_yellow"
        android:textSize="12dp" />
    <TextView
        android:id="@+id/content"
        android:layout_toRightOf="@id/desc"
          android:layout_toLeftOf="@+id/btn_copy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/title"
        android:singleLine="true"
        android:ellipsize="middle"
        android:paddingLeft="3dip"
        android:paddingRight="8dip"
        android:textColor="@color/kefu_yellow"
        android:textSize="12dp" />
	<TextView 
	     android:id="@+id/timeDesc"
	    android:layout_alignLeft="@id/title"
        android:layout_below="@id/desc"
	    android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dip"
        android:text="@string/fg_gift_time"
        android:textColor="@color/kefu_gray"
        android:textSize="12dp"/>
    <TextView
        android:id="@+id/time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dip"
        android:layout_toRightOf="@id/timeDesc"
        android:layout_toLeftOf="@+id/btn_copy"
        android:layout_below="@id/desc"
        android:gravity="left"
        android:paddingLeft="12dip"
        android:singleLine="true"
        android:ellipsize="start"
        android:textColor="@color/kefu_gray"
        android:textSize="12dp" />

    <Button
    	android:id="@+id/btn_copy"
    	android:layout_width="65.0dip"
    	android:layout_height="wrap_content"
    	android:layout_alignBaseline="@+id/content" 
    	android:layout_alignBottom="@+id/content"
    	android:layout_alignParentRight="true"
    	android:background="@drawable/sy37_kefu_submit"
    	android:layout_margin="5dp"
    	android:focusable="false"
    	android:text="@string/fg_copy"
    	android:textColor="#ffffffff"
    	android:textSize="16.0dp" />

</RelativeLayout>