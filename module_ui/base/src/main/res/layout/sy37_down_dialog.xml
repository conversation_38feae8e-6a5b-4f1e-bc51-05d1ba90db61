<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="250dip"
    android:layout_height="250dip"
    android:background="#00000000"
    android:orientation="vertical" >

    <LinearLayout
        android:id="@+id/titleLayout"
        android:layout_width="250dip"
        android:layout_height="45dip"
        android:background="@drawable/sy37_bg_kefu_gift_detail"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dip"
            android:gravity="left"
            android:singleLine="true"
            android:text="@string/fg_down_text"
            android:textColor="@color/s_white"
            android:textSize="23dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="250dip"
        android:layout_height="120dip"
        android:background="@drawable/sy37_bg_kefu_gift_detail_content"
        android:orientation="vertical"
        android:paddingBottom="10dip" >

        <TextView
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="10dip"
            android:paddingLeft="10dip"
            android:paddingRight="10dip"
            android:paddingTop="7dip"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/s_gray"
            android:textSize="18dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dip"
            android:orientation="horizontal" >

            <Button
                android:id="@+id/fg_cancel"
                android:layout_width="wrap_content"
                android:layout_height="40dip"
                android:layout_weight="1"
                android:layout_marginLeft="5dip"
                android:layout_marginRight="3dip"
                android:background="@drawable/sy37_reg"
                android:textColor="@color/s_white"
                android:textSize="23dp"
                android:text="@string/fg_cancel2" />

            <Button
                android:id="@+id/fg_confirm"
                android:layout_width="wrap_content"
                android:layout_height="40dip"
                android:layout_weight="1"
                android:layout_marginLeft="3dip"
                android:layout_marginRight="5dip"
                android:background="@drawable/sy37_kefu_submit"
                android:textColor="@color/s_white"
                android:textSize="23dp"
                android:text="@string/fg_confirm" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>