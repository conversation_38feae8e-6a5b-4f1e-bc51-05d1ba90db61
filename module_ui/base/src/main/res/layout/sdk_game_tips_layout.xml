<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/tip_bg">

    <ImageView
        android:id="@+id/bugle_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/bugle"
        android:layout_centerVertical="true"
        android:paddingLeft="8dp"/>

    <com.sqwan.msdk.views.CustomTextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@id/bugle_icon"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:textColor="#000000"
        android:singleLine="true"
        android:textSize="9dp"/>

</RelativeLayout>