<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:gravity="center" >

	<RelativeLayout
		android:layout_width="350dp"
		android:layout_height="240dp"
		android:background="@color/s_white" >

		<ImageView
			android:id="@+id/sy37_img_exit_res"
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:layout_alignParentTop="true"
			android:layout_above="@+id/layout_exit_btns"
			android:layout_centerHorizontal="true"
			android:scaleType="fitXY"
			android:adjustViewBounds="true"
			android:background="@color/s_white" />

		<LinearLayout
			android:id="@+id/layout_exit_btns"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_alignParentBottom="true"
			android:layout_centerHorizontal="true" 
			>

			<Button
				android:id="@+id/sy37_btn_exit_game"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_weight="1"
				android:background="@drawable/sy37_btn_exit"
				android:text="@string/fg_exit_game"
				android:textColor="@color/sy37_text_color_findpass"
				android:textSize="21dp" />

			<Button
				android:id="@+id/sy37_btn_back_game"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_weight="1"
				android:background="@drawable/sy37_btn_exit"
				android:text="@string/fg_more_time"
				android:textColor="@color/s_orange"
				android:textSize="21dp" />
		</LinearLayout>
	</RelativeLayout>

</RelativeLayout>