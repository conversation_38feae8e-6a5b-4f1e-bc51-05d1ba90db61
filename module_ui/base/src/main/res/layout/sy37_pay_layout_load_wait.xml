<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:gravity="center" >

	<RelativeLayout
		android:layout_width="130dp"
		android:layout_height="130dp"
		android:layout_centerInParent="true"
		android:background="@drawable/sy37_bg_progress_load"
		android:gravity="center"
		android:padding="10dp" >

		<ImageView
			android:id="@+id/pay_wait_progress"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_gravity="center"
			android:background="@drawable/sy37_pay_load_bg"
			android:scaleType="centerInside"
			android:src="@drawable/sy37_pay_load_wait" />

		<TextView
			android:id="@+id/textView1"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_below="@+id/pay_wait_progress"
			android:layout_marginTop="10dp"
			android:gravity="center"
			android:paddingLeft="10dp"
			android:text="加载中..."
			android:textColor="@color/s_white" />
	</RelativeLayout>

</RelativeLayout>