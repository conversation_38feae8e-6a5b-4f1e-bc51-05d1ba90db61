<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="260dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/kumo_white_bg_r_10">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/kumo_doubt_guide_content"
            android:gravity="center"
            android:textSize="18dp"
            android:textColor="@color/kumo_login_title_color"
            android:lineSpacingExtra="8dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            />

        <Button
            android:id="@+id/btn_copy"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:background="@drawable/kumo_shape_login_btn_bg"
            android:gravity="center"
            android:text="复制公众号名称"
            android:textColor="@android:color/white"
            android:textSize="18dp"
            android:layout_below="@+id/tv_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:src="@drawable/sy_payweb_close"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:padding="5dp"
            />

    </RelativeLayout>
</FrameLayout>
