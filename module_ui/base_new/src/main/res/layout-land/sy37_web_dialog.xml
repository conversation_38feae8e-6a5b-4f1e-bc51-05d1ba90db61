<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/keyboard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00ffffff"
    android:weightSum="100"
    android:gravity="start"
    android:orientation="horizontal"
    android:clickable="true"
    >

    <LinearLayout
        android:id="@+id/notch_black"
        android:orientation="horizontal"
        android:background="#171717"
        android:layout_width="60dp"
        android:visibility="gone"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:id="@+id/web_parent"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="100"
        android:orientation="vertical"
        >

        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="60dp"
            android:layout_gravity="center_vertical"
            >

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"
                android:textSize="18dp" />

            <ImageButton
                android:id="@+id/togame"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="12dip"
                android:layout_centerVertical="true"
                android:padding="10dip" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.sqwan.common.webview.SQWebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FFFFFF"
                android:scaleType="centerInside"
                />

            <RelativeLayout
                android:id="@+id/sy37_rl_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/sy37_iv_loading"
                    android:layout_width="66dp"
                    android:layout_height="66dp"
                    android:layout_centerInParent="true" />

                <TextView
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="10dp"
                    android:layout_below="@+id/sy37_iv_loading"
                    android:textSize="12dp"
                    android:textColor="#666666"
                    android:text="@string/sysq_loading_txt"
                    android:id="@+id/sy37_tv_loading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/sy37_m_net_error_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone"
                android:background="#FFFFFF"
                >

                <ImageView
                    android:id="@+id/sy37_iv_back"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:src="@drawable/sy37_icon_back"
                    android:layout_margin="10dp"
                    android:scaleType="centerInside"
                    />
                <include
                    layout="@layout/sy37_net_error_view_port"/>
            </LinearLayout>
        </RelativeLayout>

    </LinearLayout>


</LinearLayout>