<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:orientation="vertical">

    <View
        android:id="@+id/status_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#66000000" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_web_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:scaleType="centerInside" />

        <LinearLayout
            android:id="@+id/ll_web_error_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone" >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="网络不给力"
                android:textColor="#333333"
                android:textSize="18dp" />

            <Button
                android:id="@+id/btn_web_error_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:background="@drawable/sy37_shape_pay_refresh_button_bg"
                android:gravity="center"
                android:paddingLeft="30dp"
                android:paddingTop="5dp"
                android:paddingRight="30dp"
                android:paddingBottom="5dp"
                android:text="刷新"
                android:textColor="#f59a23"
                android:textSize="15dp" />

        </LinearLayout>

    </FrameLayout>
</LinearLayout>
