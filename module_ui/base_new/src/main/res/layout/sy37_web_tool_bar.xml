<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/sy37_web_toolbar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:background="#ffffff">

        <RelativeLayout
            android:id="@+id/web_back_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:id="@+id/iv_back_bar"
                android:layout_width="8dp"
                android:layout_height="18dp"
                android:layout_centerInParent="true"
                android:layout_weight="1"
                android:src="@drawable/sy37_web_back_disable" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/web_forward_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:id="@+id/iv_forward_bar"
                android:layout_width="8dp"
                android:layout_height="18dp"
                android:layout_centerInParent="true"
                android:layout_weight="1"
                android:src="@drawable/sy37_web_forward_disable" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/web_refresh_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:id="@+id/iv_refresh_bar"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_centerInParent="true"
                android:layout_weight="1"
                android:src="@drawable/sy37_web_refresh" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/web_close_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <ImageView
                android:id="@+id/iv_close_bar"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_centerInParent="true"
                android:layout_weight="1"
                android:src="@drawable/sy37_web_exit" />
        </RelativeLayout>

</LinearLayout>