<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/auto_login_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="27dp"
        android:background="@drawable/sy37_bg_layout_auto_login_account"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="15dp"
        android:paddingTop="8dp"
        android:paddingRight="15dp"
        android:paddingBottom="8dp">

        <TextView
            android:id="@+id/sy37_logining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/sy37_txt_login_account"
            android:textColor="@color/s_white"
            android:textSize="14dp" />

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/text_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="asdhfas"
                android:paddingRight="25dp"
                android:textColor="@color/s_white"
                android:textSize="14dp" />

            <ProgressBar
                android:layout_alignRight="@+id/text_account"
                android:id="@+id/pb_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerVertical="true"
                android:indeterminateDrawable="@drawable/sy37_kefu_progress_simple"
                android:indeterminateDuration="1500"
                android:visibility="visible" />


        </RelativeLayout>


        <TextView
            android:id="@+id/tv_changeAccount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/sy37_bg_layout_auto_login_change_account"
            android:paddingLeft="18dp"
            android:paddingTop="6dp"
            android:paddingRight="18dp"
            android:paddingBottom="6dp"
            android:text="@string/sy37_txt_login_change_account"
            android:textColor="@color/auto_login_change_account"
            android:textSize="12dp" />
    </LinearLayout>

</RelativeLayout>


