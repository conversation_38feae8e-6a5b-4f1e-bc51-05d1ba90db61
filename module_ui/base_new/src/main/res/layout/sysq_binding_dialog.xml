<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_white_radius_8"
        android:gravity="center"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="14dp"
            android:text="@string/sysq_binding_tip_title"
            android:textColor="#303133"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textColor="#606266"
            android:textSize="14dp"
            android:gravity="center"
            tools:text="请选择是否授权当前登录信息《斗罗宇宙》" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="#F0F0F0" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="取消"
                android:textColor="#606266"
                android:textSize="16dp" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#F0F0F0" />

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="确定"
                android:textColor="#4571FB"
                android:textSize="16dp" />

        </LinearLayout>

    </LinearLayout>
</FrameLayout>