<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="42dp"
	android:orientation="horizontal"
	android:gravity="center_vertical"
    >

	<ImageView
		android:id="@+id/iv_account_type"
		android:src="@drawable/sysq_ic_account"
		android:layout_marginLeft="12dp"
		android:layout_width="16dp"
		android:layout_height="16dp"/>
    <TextView
        android:id="@+id/fg_name"
		android:layout_marginLeft="12dp"
        android:layout_width="0dp"
		android:layout_weight="1"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/sysq_dialog_login_text_primary"
        android:textSize="@dimen/sysq_dialog_16_sp"
		android:focusable="false"
		/>

    <LinearLayout
        android:id="@+id/fg_delete"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:gravity="center"
        android:orientation="horizontal" >

    	    <ImageView
    	    	android:id="@+id/imageView"
    	    	android:layout_width="wrap_content"
    	    	android:layout_height="wrap_content"
    	    	android:scaleType="center"
    	    	android:src="@drawable/sysq_pop_delete" />

    </LinearLayout>

</LinearLayout>