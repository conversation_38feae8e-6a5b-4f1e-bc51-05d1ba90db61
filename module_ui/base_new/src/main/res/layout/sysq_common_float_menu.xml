<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/float_layout"
    android:layout_width="290dp"
    android:layout_height="wrap_content"
    android:background="@drawable/sysq_bg_float"
    android:orientation="vertical">
    <ImageView
      android:id="@+id/iv_background"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:scaleType="centerCrop"/>
    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical">
        <LinearLayout
          android:id="@+id/person_panel"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginLeft="15dp"
          android:layout_marginTop="11dp"
          android:gravity="center_vertical"
          android:orientation="horizontal">

            <ImageView
              android:id="@+id/iv_head"
              android:layout_width="40dp"
              android:layout_height="40dp"
              android:src="@drawable/sysq_ic_float_head" />

            <LinearLayout
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_marginLeft="7dp"
              android:orientation="vertical">

                <LinearLayout
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:gravity="center_vertical"
                  android:orientation="horizontal"
                  android:paddingRight="15dp">


                    <RelativeLayout
                      android:layout_width="0dp"
                      android:layout_height="wrap_content"
                      android:layout_weight="1">

                        <TextView
                          android:id="@+id/tv_nick"
                          android:layout_width="wrap_content"
                          android:layout_height="wrap_content"
                          android:ellipsize="end"
                          android:paddingRight="28dp"
                          android:singleLine="true"
                          android:textColor="#333333"
                          android:textSize="14dp"
                          android:text="凄美拉丝缠" />

                        <ImageView
                          android:paddingLeft="3dp"
                          android:paddingRight="3dp"
                          android:id="@+id/iv_edit"
                          android:layout_width="28dp"
                          android:layout_height="22dp"
                          android:layout_alignRight="@+id/tv_nick"
                          android:layout_centerVertical="true"
                          android:layout_marginLeft="2.5dp"
                          android:src="@drawable/sysq_ic_float_edit"
                          android:visibility="visible" />


                    </RelativeLayout>


                    <LinearLayout
                      android:visibility="gone"
                      android:id="@+id/person_layout"
                      android:layout_width="wrap_content"
                      android:layout_height="wrap_content"
                      android:layout_marginLeft="10dp"
                      android:gravity="center_vertical"
                      android:orientation="horizontal">

                        <TextView
                          android:id="@+id/tv_user_center"
                          android:textSize="12dp"
                          android:textColor="#999999"
                          android:layout_width="wrap_content"
                          android:layout_height="wrap_content"
                          android:text="个人中心" />

                        <ImageView
                          android:layout_width="11dp"
                          android:layout_height="11dp"
                          android:src="@drawable/sysq_ic_right" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                  android:layout_marginTop="2dp"
                  android:id="@+id/iv_level"
                  android:layout_width="62.5dp"
                  android:layout_height="15dp"/>

            </LinearLayout>

            <TextView
              android:id="@+id/tv_person_center"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content" />

        </LinearLayout>

        <android.support.v7.widget.RecyclerView
          android:id="@+id/rv_menu_layout"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:gravity="center"
          android:paddingTop="10dp"
          android:paddingBottom="20dp" />
    </LinearLayout>

</FrameLayout>