<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:background="@drawable/sysq_gray_corner_bg_10"
        android:orientation="vertical">

        <com.sy37sdk.order.view.ui.BackPayView
            android:id="@+id/pay_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:id="@+id/rl_not_use_coupon_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/sysq_white_corner_bg_10"
            android:paddingLeft="15dp">

            <TextView
                android:layout_centerVertical="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="不使用代金券"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="14dp"
                android:textStyle="bold" />

            <RelativeLayout
                android:padding="15dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:id="@+id/not_use_coupon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/iv_not_use_coupon"
                    android:layout_centerInParent="true"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/sysq_ic_pay_select" />
            </RelativeLayout>

        </RelativeLayout>


        <ListView
            android:id="@+id/lv_coupon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:divider="#00000000"
            android:dividerHeight="20dp"
            android:cacheColorHint="#00000000"
            android:listSelector="@android:color/transparent"
            android:scrollbars="none" />
    </LinearLayout>
</LinearLayout>