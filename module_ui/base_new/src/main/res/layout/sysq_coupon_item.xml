<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/sysq_ic_pay_coupon_left"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="15dp"
        android:paddingRight="15dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_rmb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/tv_money"
                android:layout_marginBottom="4dp"
                android:gravity="center"
                android:text="¥"
                android:textColor="@color/sysq_pay_discount_color"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/tv_money"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tv_rmb"
                android:gravity="center"
                android:textColor="@color/sysq_pay_discount_color"
                android:textSize="30dp"
                tools:text="15" />
        </RelativeLayout>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sysq_pay_trade_coupon"
            android:textColor="@color/sysq_pay_discount_color"
            android:textSize="10dp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/sysq_ic_pay_coupon_right"
        android:paddingLeft="15dp"
        android:paddingRight="15dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true">

            <TextView
                android:id="@+id/tv_min_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="14dp"
                android:textStyle="bold"
                tools:text="满30元可用" />

            <TextView
                android:id="@+id/tv_coupon_etime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_min_amount"
                android:layout_marginTop="10dp"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="12dp"
                tools:text="三天后过期" />
        </RelativeLayout>


        <ImageView
            android:id="@+id/iv_select_status"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/sysq_ic_pay_select" />

    </RelativeLayout>


</LinearLayout>