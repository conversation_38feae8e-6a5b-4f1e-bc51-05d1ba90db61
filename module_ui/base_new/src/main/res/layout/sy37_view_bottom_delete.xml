<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    >

    <LinearLayout
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@drawable/sy37_bg_bottomdelete_landscape_normal">

        <ImageView
            android:id="@+id/iv_status"
            android:src="@drawable/sy37_ic_draghide"
            android:layout_width="40dp"
            android:layout_height="40dp"/>
        <TextView
            android:id="@+id/tv_status"
            android:layout_marginTop="6dp"
            android:text="拖到此处隐藏"
            android:textColor="#ffffffff"
            android:textSize="17dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>
</LinearLayout>