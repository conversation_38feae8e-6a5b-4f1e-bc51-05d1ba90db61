<?xml version="1.0" encoding="utf-8"?>
<ScrollView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="20dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/sy37_dialog_permision_bg"
    >
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
   >


    <TextView
        android:id="@+id/tv_title"
        android:layout_marginBottom="20dp"
        android:textStyle="bold"
        android:textSize="18dp"
        tools:text="个人信息保护指引"
        android:textColor="#ff333333"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

        <RelativeLayout
            android:id="@+id/rl_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>


    <LinearLayout
        android:showDividers="middle"
        android:divider="@drawable/sy37_divider_h_20"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="14dp"
            android:gravity="center"
            android:textColor="@drawable/sy37_bg_auth_txt"
            android:minEms="7"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:text="退出游戏"
            android:background="@drawable/sy37_bg_auth_btn"
            android:id="@+id/tv_cancel"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            />
        <TextView
            android:textSize="14dp"
            android:textColor="@drawable/sy37_bg_auth_txt"
            android:gravity="center"
            android:minEms="7"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:text="好的"
            android:background="@drawable/sy37_bg_auth_btn"
            android:id="@+id/tv_ok"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            />
    </LinearLayout>
    </LinearLayout>
    </ScrollView>