<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_white_radius_4"
        android:gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fl_head_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" >

            <View
                android:layout_width="1dp"
                android:layout_height="25dp" />
        </FrameLayout>

        <TextView
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="25dp"
            android:gravity="center"
            android:lineSpacingExtra="8dp"
            android:text="@string/sysq_pay_cancel_tip"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="16dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/sysq_dialog_login_border" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingTop="12dp"
                android:paddingRight="20dp"
                android:paddingBottom="12dp"
                android:text="@string/sysq_pay_cancel_sure"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="16dp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:background="@color/sysq_dialog_login_border" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingLeft="20dp"
                android:paddingTop="12dp"
                android:paddingRight="20dp"
                android:paddingBottom="12dp"
                android:text="@string/sysq_pay_cancel_continue"
                android:textColor="@color/sysq_dialog_text_sure"
                android:textSize="16dp" />
        </LinearLayout>


    </LinearLayout>
</FrameLayout>
