<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/sy37_dialog_permision_bg"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:textColor="#ff333333"
            android:textSize="18dp"
            android:textStyle="bold"
            tools:text="个人信息保护指引" />

        <RelativeLayout
            android:id="@+id/rl_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:divider="@drawable/sy37_divider_h_20"
            android:orientation="horizontal"
            android:showDividers="middle">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/sy37_bg_auth_btn"
                android:gravity="center"
                android:minEms="7"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="退出游戏"
                android:textColor="@drawable/sy37_bg_auth_txt"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_ok"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/sy37_bg_auth_btn"
                android:gravity="center"
                android:minEms="7"
                android:paddingLeft="30dp"
                android:paddingTop="10dp"
                android:paddingRight="30dp"
                android:paddingBottom="10dp"
                android:text="好的"
                android:textColor="@drawable/sy37_bg_auth_txt"
                android:textSize="14dp" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>