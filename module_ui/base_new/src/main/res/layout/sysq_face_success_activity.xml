<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/s_white"
    android:orientation="vertical">

    <View
        android:id="@+id/status_view"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <FrameLayout
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:paddingLeft="15dp"
        android:paddingTop="7.5dp"
        android:paddingRight="15dp"
        android:paddingBottom="7.5dp">

        <ImageView
            android:layout_width="28.5dp"
            android:layout_height="28.5dp"
            android:src="@drawable/sysq_ic_face_verify_close" />
    </FrameLayout>

    <ImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="23.5dp"
        android:src="@drawable/sysq_ic_face_verify_success" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="15dp"
        android:text="@string/sysq_txt_face_verify_success"
        android:textColor="@color/sysq_dialog_login_text_primary"
        android:textSize="@dimen/sysq_text_size_18"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_time_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/sysq_txt_face_verify_success_back"
        android:textColor="#FF7722"
        android:textSize="@dimen/sysq_text_size_14" />

</LinearLayout>