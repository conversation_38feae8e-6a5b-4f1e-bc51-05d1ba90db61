<?xml version="1.0" encoding="UTF-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
  android:background="@color/sysq_white">

    <RelativeLayout
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:padding="13dp">
        <ImageView
          android:id="@+id/iv_back"
          android:layout_width="22dp"
          android:layout_height="22dp"
          android:layout_centerVertical="true"
          android:layout_alignParentEnd="true"
          android:background="@drawable/sysq_ic_close"/>

        <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="登录确认"
          android:textSize="18dp"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:layout_centerInParent="true"/>
    </RelativeLayout>

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="vertical"
      android:gravity="center_horizontal">
        <ImageView
          android:layout_width="90dp"
          android:layout_height="90dp"
          android:layout_marginTop="224dp"
          android:background="@drawable/sy37_ic_computer"/>
        <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="电脑版登录确认"
          android:textSize="17dp"
          android:layout_marginTop="19dp"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:layout_centerInParent="true"/>

        <TextView
          android:id="@+id/tv_login"
          android:layout_width="240dp"
          android:layout_height="40dp"
          android:gravity="center"
          android:layout_marginTop="149dp"
          android:textColor="@color/sysq_white"
          android:text="@string/sysq_login"
          android:textSize="16dp"
          android:background="@drawable/sy_sq_dialog_login_btn_bg"/>

        <TextView
          android:id="@+id/tv_cancel"
          android:layout_width="200dp"
          android:layout_height="40dp"
          android:gravity="center"
          android:layout_marginTop="20dp"
          android:textColor="@color/sysq_text_color_gray"
          android:text="取消登录"
          android:textSize="16dp" />
    </LinearLayout>

</FrameLayout>