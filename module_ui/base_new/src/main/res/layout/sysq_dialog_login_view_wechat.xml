<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">


    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy_sq_bg_login"
        android:paddingBottom="12dp">

        <com.sy37sdk.account.view.base.view.BackTitleView
            android:id="@+id/common_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.sy37sdk.account.view.base.view.BackTitleView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/common_title_view"
            android:paddingLeft="20dp"
            android:paddingRight="20dp">

            <RelativeLayout
                android:id="@+id/rl_wechat"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/sysq_dialog_login_green_bg">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_marginTop="1dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/sysq_ic_wechat_login" />

                    <TextView
                        android:id="@+id/tv_wechat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="6dp"
                        android:text="微信登录"
                        android:textColor="@color/sysq_white"
                        android:textSize="@dimen/sysq_dialog_login_text_primary" />
                </LinearLayout>


            </RelativeLayout>


            <TextView
                android:id="@+id/tv_other_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rl_wechat"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="18dp"
                android:layout_marginBottom="15dp"
                android:text="@string/sysq_other_login"
                android:textColor="@color/sysq_dialog_login_text_gray"
                android:textSize="@dimen/sysq_dialog_login_text_secondary" />

            <LinearLayout
                android:layout_centerInParent="true"
                android:id="@+id/ll_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_other_login"
                android:layout_marginTop="13dp">

                <CheckBox
                    android:id="@+id/cb_clause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/sysq_dialog_login_privacy_check_box"
                    android:gravity="center_vertical"
                    android:paddingLeft="5dp"
                    android:text="@string/sysq_privacy_agreement"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="12dp"
                    android:background="@android:color/transparent"
                    android:stateListAnimator="@null"/>

                <TextView
                    android:id="@+id/tv_clause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/sysq_user_agreement"
                    android:textColor="@color/sysq_dialog_login_text_accent"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="及"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                    android:id="@+id/tv_policy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/sysq_privacy_policy"
                    android:textColor="@color/sysq_dialog_login_text_accent"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>
</FrameLayout>