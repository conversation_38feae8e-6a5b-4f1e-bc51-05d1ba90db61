<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/left_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="15dp">

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="16dp"
            android:layout_height="16dp"
            tools:src="@drawable/sysq_pay_ic_help" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/sysq_dialog_login_text_primary"
        android:textSize="@dimen/sysq_dialog_16_sp"
        tools:text="@string/sysq_pay_cashier" />

    <FrameLayout
        android:id="@+id/right_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:padding="15dp">

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="16dp"
            android:layout_height="16dp"
            tools:src="@drawable/sysq_pay_ic_close" />
    </FrameLayout>
</RelativeLayout>