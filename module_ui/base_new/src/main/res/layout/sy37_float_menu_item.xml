<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/menu_parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="58dp"
        android:paddingTop="10dp"
        android:paddingBottom="6dp"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_centerHorizontal="true"
            android:id="@+id/menu_item_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="centerCrop"
            android:src="@drawable/sysq_ic_test_menu" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_red_dot"
            android:layout_toRightOf="@id/menu_item_icon"
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:background="@drawable/sysq_bg_red_dot"
            android:gravity="center"
            android:textSize="9dp"
            android:textColor="@color/sysq_white" />
        <ImageView
          android:visibility="gone"
          android:id="@+id/iv_red_dot"
          android:layout_toRightOf="@id/menu_item_icon"
          android:layout_width="8dp"
          android:layout_height="8dp"
          android:background="@drawable/sysq_bg_red_dot" />
    </RelativeLayout>


    <TextView
        android:id="@+id/menu_item_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:ellipsize="end"
        android:gravity="center"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:singleLine="true"
        android:textColor="#666666"
        android:textSize="12dp" />

</LinearLayout>