<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_white_radius_10"
        android:gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="136dp"
                android:src="@drawable/sysq_bg_face_verify" />

            <ImageView
                android:id="@+id/iv_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:src="@drawable/sysq_bg_face_verify_help" />
        </FrameLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_horizontal"
            android:text="防沉迷系统提醒"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="@dimen/sysq_text_size_18"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="23dp"
            android:layout_marginRight="23dp"
            android:text="系统判断您的账号疑似未成年操作，请完成人脸识别验证。未通过验证或者拒绝验证，该账号将无法进行游戏。"
            android:textColor="@color/sysq_text_color_gray"
            android:textSize="@dimen/sysq_dialog_16_sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="22dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_gray_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="@string/sysq_txt_cancel_face_verify"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_red_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="@string/sysq_txt_start_face_verify"
                android:textColor="@color/sysq_white"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>