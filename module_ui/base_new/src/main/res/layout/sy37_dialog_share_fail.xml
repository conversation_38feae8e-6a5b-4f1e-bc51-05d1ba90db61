<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:background="@drawable/sy37_permission_dialog_white_bg"
        android:padding="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/black"
            android:text="图片保存失败"
            android:textSize="20dp"/>

        <TextView
            android:id="@+id/tv_tips_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:lineSpacingExtra="10dp"
            android:gravity="left"
            android:text="@string/sy37_share_wx_fail_tips"
            android:textColor="#282829"
            android:textSize="16dp"/>


        <TextView
            android:id="@+id/tv_tips_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16dp"
            android:text="我知道了"
            android:padding="5dp"
            android:textColor="#3874fe"
            />
    </LinearLayout>

</RelativeLayout>