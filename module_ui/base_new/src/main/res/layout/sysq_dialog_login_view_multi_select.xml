<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">


    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="350dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@color/sysq_dialog_login_background">

        <com.sy37sdk.account.view.base.view.BackTitleView
            android:id="@+id/common_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.sy37sdk.account.view.base.view.BackTitleView>

        <LinearLayout
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/common_title_view"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingBottom="13dp"
            android:orientation="vertical">

            <TextView
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:textColor="@color/sysq_dialog_login_text_primary"
              android:textSize="13dp"
              android:text="该账号名存在对应两个账号，请确认你要登录的账号"/>

            <LinearLayout
              android:id="@+id/ll_list"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"/>

            <LinearLayout
              android:id="@+id/ll_account_error"
              android:layout_marginTop="13dp"

              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:visibility="gone">
                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:textColor="#FE673A"
                  android:textSize="13dp"
                  android:text="密码错误，您可选择登录另一个账号或"/>

                <TextView
                  android:id="@+id/tv_input"
                  android:textSize="13dp"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:textColor="#3A99FE"
                  android:text="重新输入密码"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>