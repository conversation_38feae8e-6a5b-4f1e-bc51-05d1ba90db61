<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/modify_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="310dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sysq_bg_white_corner"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="修改个人信息"
            android:textColor="#333333"
            android:textSize="18dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*"
                android:textColor="#ff6f6f" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="修改平台头像"
                android:textColor="#666666"
                android:textSize="14dp" />
        </LinearLayout>

        <android.support.v7.widget.RecyclerView
            android:overScrollMode="never"
            android:id="@+id/rv_avatar"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*"
                android:textColor="#ff6f6f" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="修改昵称"
                android:textColor="#666666"
                android:textSize="14dp" />
        </LinearLayout>

        <com.sy37sdk.account.view.base.view.ClearEditText
            android:id="@+id/et_nick"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/sysq_bg_gray_corner"
            android:hint="@string/sysq_person_edit_nick"
            android:paddingLeft="16dp"
            android:paddingTop="11dp"
            android:paddingRight="16dp"
            android:paddingBottom="11dp"
            android:singleLine="true"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textColorHint="@color/sysq_dialog_login_text_hint"
            android:textSize="@dimen/sysq_dialog_16_sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="17dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_gray_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="@string/sysq_txt_cancel"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="22dp"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_red_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="@string/sysq_txt_sure"
                android:textColor="@color/sysq_white"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>