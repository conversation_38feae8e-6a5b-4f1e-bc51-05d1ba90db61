<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignRight="@+id/rl_img"
        android:layout_marginTop="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:paddingLeft="10dp">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_close_share_dialog" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_centerHorizontal="true"
        android:id="@+id/rl_img"
        android:padding="5dp"
        android:layout_below="@+id/close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_centerInParent="true"
            android:id="@+id/imageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop" />
    </RelativeLayout>


    <LinearLayout
        android:id="@+id/ll_share_way"
        android:layout_width="wrap_content"
        android:layout_height="85dp"
        android:layout_below="@+id/rl_img"
        android:layout_alignRight="@+id/rl_img"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/share_wx"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_share_wechat" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/share_wx_circle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_share_wechat_circle" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/share_qq"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_share_qq" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/share_more"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_share_more" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>