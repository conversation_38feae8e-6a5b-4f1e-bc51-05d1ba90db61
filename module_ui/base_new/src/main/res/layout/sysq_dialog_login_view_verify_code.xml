<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="340dp"
        android:layout_height="222dp"
        android:layout_gravity="center"
        android:background="@drawable/sy_sq_bg_login">

        <com.sy37sdk.account.view.base.view.BackTitleView
            android:id="@+id/common_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.sy37sdk.account.view.base.view.BackTitleView>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/common_title_view"
            android:paddingLeft="20dp"
            android:paddingRight="20dp">

            <TextView
                android:id="@+id/tip_input_login_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="23dp"
                android:text="@string/sysq_input_verify_code"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="@dimen/sysq_dialog_login_text_accent"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tip_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tip_input_login_info"
                android:text="验证码已发送至+86 16628767654"
                android:textColor="@color/sysq_dialog_login_text_hint"
                android:textSize="@dimen/sysq_dialog_login_text_secondary"
                tools:text="验证码已发送至+86 12344444" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tip_send"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_verify_code"
                    android:layout_width="match_parent"
                    android:layout_height="42dp"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/sysq_dialog_login_gray_bg"
                    android:gravity="center_vertical"
                    android:visibility="visible">

                    <EditText
                        android:id="@+id/et_verify_code"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="10dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/sysq_input_verify_code"
                        android:inputType="number"
                        android:maxLength="6"
                        android:paddingRight="10dp"
                        android:singleLine="true"
                        android:textColorHint="@color/sysq_dialog_login_text_hint"
                        android:textSize="@dimen/sysq_dialog_login_text_primary" />

                    <TextView
                        android:id="@+id/send_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:text="@string/sysq_verify_code_again"
                        android:textColor="@color/sysq_dialog_login_text_gray"
                        android:textSize="@dimen/sysq_dialog_login_text_secondary" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_pwd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="42dp"
                        android:background="@drawable/sysq_dialog_login_gray_bg"
                        android:gravity="center_vertical">

                        <com.sy37sdk.account.view.base.view.ClearEditText
                            android:id="@+id/et_pwd"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:background="@android:color/transparent"
                            android:drawablePadding="10dp"
                            android:hint="@string/sysq_input_password_hint"
                            android:inputType="textPassword"
                            android:paddingLeft="19dp"
                            android:singleLine="true"
                            android:textColor="@color/sysq_dialog_login_text_primary"
                            android:textColorHint="@color/sysq_dialog_login_text_hint"
                            android:textSize="@dimen/sysq_dialog_login_text_primary" />

                        <ImageView
                            android:id="@+id/iv_pwd_status"
                            android:layout_width="42dp"
                            android:layout_height="match_parent"
                            android:scaleType="center"
                            android:src="@drawable/sysq_ic_pwd_hide" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_login"
                        android:layout_width="match_parent"
                        android:layout_height="38dp"
                        android:layout_below="@+id/ll_phone"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/sy_sq_dialog_login_btn_bg"
                        android:gravity="center"
                        android:text="@string/sysq_login"
                        android:textColor="@color/sysq_login_btn_text_color"
                        android:textSize="@dimen/sysq_dialog_login_text_accent" />
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp">

                    <View
                        android:id="@+id/view_division"
                        android:layout_width="0.5dp"
                        android:layout_height="17dp"
                        android:layout_centerHorizontal="true"
                        android:background="@color/sysq_dialog_login_border" />

                    <TextView
                        android:id="@+id/login_way"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignTop="@+id/view_division"
                        android:layout_marginRight="12dp"
                        android:layout_toLeftOf="@+id/view_division"
                        android:text="@string/sysq_login_pwd"
                        android:textColor="@color/sysq_dialog_login_text_accent"
                        android:textSize="@dimen/sysq_dialog_login_text_secondary" />

                    <TextView
                        android:id="@+id/login_help"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignTop="@+id/view_division"
                        android:layout_marginLeft="12dp"
                        android:layout_toRightOf="@+id/view_division"
                        android:text="@string/sysq_login_help"
                        android:textColor="@color/sysq_dialog_login_text_accent"
                        android:textSize="@dimen/sysq_dialog_login_text_secondary" />
                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>

</FrameLayout>