<?xml version="1.0" encoding="UTF-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:orientation="vertical">


  <FrameLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextureView
      android:id="@+id/scan_activity_preview"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />

    <com.sy37sdk.account.view.base.view.ScannerView
      android:id="@+id/scan_activity_mask"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />

    <ImageView
      android:id="@+id/iv_scan_rect"
      android:layout_width="263dp"
      android:layout_height="263dp"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="205dp"
      android:background="@drawable/sy37_scan_code" />

    <RelativeLayout
      android:id="@+id/rl_loading_img"
      android:layout_width="130dp"
      android:layout_height="130dp"
      android:layout_gravity="center"
      android:visibility="gone"
      android:background="@drawable/sy37_bg_progress_load"
      android:padding="10dp">

      <ProgressBar
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:indeterminateDuration="1000" />

      <TextView
        android:id="@+id/msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/progress"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="15dp"
        android:ellipsize="middle"
        android:gravity="center"
        android:maxLines="2"
        android:paddingLeft="3dp"
        android:text="识别中"
        android:textColor="#FFFFFF"
        android:textSize="14dp"
        android:visibility="visible" />
    </RelativeLayout>
  </FrameLayout>

  <RelativeLayout
    android:id="@+id/title_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="13dp">
    <ImageView
      android:id="@+id/iv_back"
      android:layout_width="22dp"
      android:layout_height="22dp"
      android:layout_centerVertical="true"
      android:background="@drawable/sysq_ic_back_white" />

    <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_centerInParent="true"
      android:text="扫一扫"
      android:textColor="@color/sysq_white"
      android:textSize="22dp" />
  </RelativeLayout>

</FrameLayout>