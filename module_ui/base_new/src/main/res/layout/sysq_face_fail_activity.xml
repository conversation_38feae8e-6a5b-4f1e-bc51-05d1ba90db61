<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/s_white"
    android:orientation="vertical">

    <View
        android:id="@+id/status_view"
        android:layout_width="match_parent"
        android:layout_height="0dp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="67.5dp"
            android:src="@drawable/sysq_ic_face_verify_fail" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="15dp"
            android:text="@string/sysq_txt_face_verify_fail"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="@dimen/sysq_text_size_18"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="33.5dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="33.5dp"
            android:lineSpacingExtra="5dp"
            android:text="@string/sysq_txt_face_verify_fail_msg"
            android:textColor="@color/sysq_text_color_gray"
            android:textSize="@dimen/sysq_dialog_16_sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="33.5dp"
            android:layout_marginTop="32.5dp"
            android:layout_marginRight="33.5dp"
            android:lineSpacingExtra="5dp"
            android:text="@string/sysq_txt_face_verify_fail_tip"
            android:textColor="#FF7722"
            android:textSize="@dimen/sysq_text_size_14" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="76dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_retry"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27.5dp"
            android:layout_marginRight="27.5dp"
            android:background="@drawable/sysq_bg_red_corner"
            android:gravity="center"
            android:paddingTop="9.5dp"
            android:paddingBottom="9.5dp"
            android:text="@string/sysq_txt_face_verify_fail_retry"
            android:textColor="@color/sysq_white"
            android:textSize="@dimen/sysq_dialog_login_text_accent" />

        <TextView
            android:id="@+id/tv_quit_verify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27.5dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="27.5dp"
            android:background="@drawable/sysq_bg_gray_corner"
            android:gravity="center"
            android:paddingTop="9.5dp"
            android:paddingBottom="9.5dp"
            android:text="@string/sysq_txt_face_verify_fail_quit"
            android:textColor="@color/sysq_dialog_login_text_secondary"
            android:textSize="@dimen/sysq_text_size_18" />


        <TextView
            android:id="@+id/tv_customer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:padding="3dp"
            android:text="@string/sysq_txt_face_verify_fail_customer"
            android:textColor="@color/sysq_text_color_blue"
            android:textSize="@dimen/sysq_dialog_16_sp" />

    </LinearLayout>
</LinearLayout>