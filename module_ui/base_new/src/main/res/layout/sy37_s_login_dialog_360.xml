<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/login_parent"
    android:background="@android:color/transparent"
    >
    <TextView
        android:id="@+id/sdkVersion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:textColor="#EFEFEF"
        android:textSize="10dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="6dp"
        />

    <ViewSwitcher
        android:id="@+id/vs_view_container"
        android:layout_gravity="center"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</RelativeLayout>