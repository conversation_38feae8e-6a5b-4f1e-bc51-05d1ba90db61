<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/s_white"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ProgressBar
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignRight="@+id/text_account"
            android:layout_centerVertical="true"
            android:indeterminateDrawable="@drawable/sy37_kefu_progress_simple"
            android:indeterminateDuration="1500"
            android:visibility="visible" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="认证中，请稍后"
            android:textColor="@color/sysq_text_color_gray"
            android:textSize="@dimen/sysq_dialog_16_sp" />


    </LinearLayout>


</FrameLayout>