<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="15dp">

    <RelativeLayout
        android:id="@+id/trade_layout"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/sysq_pay_trade_name"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/product_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="15dp"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="14dp"
            tools:text="首充礼包" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/sysq_pay_divider" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/coupon_layout"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/sysq_pay_trade_coupon"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="14dp" />


        <ImageView
            android:id="@+id/iv_more"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:src="@drawable/sysq_pay_dialog_more" />

        <TextView
            android:id="@+id/tv_cupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/iv_more"
            android:textColor="@color/sysq_pay_discount_color"
            android:textSize="14dp"
            tools:text="-￥5.00" />


        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/sysq_pay_divider" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/money_layout"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/sysq_pay_trade_money"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="14dp" />


        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingRight="15dp"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="18dp"
            tools:text="￥98" />

        <TextView
            android:id="@+id/tv_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/tv_price"
            android:layout_marginLeft="3dp"
            android:layout_marginBottom="2dp"
            android:layout_toLeftOf="@id/tv_price"
            android:textColor="@color/sysq_dialog_login_text_secondary"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="￥100" />
    </RelativeLayout>
</LinearLayout>