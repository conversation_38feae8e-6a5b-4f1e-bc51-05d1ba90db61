<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
      android:layout_width="300dp"
      android:layout_height="wrap_content"
      android:layout_gravity="center">
        <LinearLayout
          android:layout_width="300dp"
          android:layout_height="wrap_content"
          android:background="@drawable/sy37_shape_white_radius_5"
          android:gravity="center"
          android:orientation="vertical"
          tools:ignore="UselessParent">

            <TextView
              android:id="@+id/tv_title"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_marginTop="25dp"
              android:layout_marginBottom="15dp"
              android:text="@string/sysq_policy_tip_title"
              android:textColor="#333333"
              android:textSize="18dp"
              android:textStyle="bold" />

            <TextView
              android:id="@+id/tv_tip"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_marginStart="15dp"
              android:layout_marginEnd="15dp"
              android:textColor="#3D3D3D"
              android:textSize="14dp" />


            <TextView
              android:id="@+id/tv_sure"
              android:layout_width="134dp"
              android:layout_height="36dp"
              android:gravity="center"
              android:layout_marginTop="20dp"
              android:background="@drawable/sy_sq_dialog_login_btn_bg"
              android:text="@string/sysq_policy_tip_confirm"
              android:textColor="#ffffff"
              android:layout_marginBottom="17dp"
              android:textSize="14dp" />
        </LinearLayout>

        <ImageView
          android:id="@+id/iv_close"
          android:layout_width="15dp"
          android:layout_height="15dp"
          android:layout_alignParentEnd="true"
          android:background="@drawable/ic_close_confirm_dialog"
          android:layout_marginTop="10dp"
          android:layout_marginEnd="10dp" />
    </RelativeLayout>
</FrameLayout>