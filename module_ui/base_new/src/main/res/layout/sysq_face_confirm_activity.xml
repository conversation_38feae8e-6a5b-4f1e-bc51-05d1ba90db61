<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/s_white"
    android:orientation="vertical">

    <View
        android:id="@+id/status_view"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <FrameLayout
                android:id="@+id/back"
                android:layout_width="48dp"
                android:layout_height="44dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:src="@drawable/sysq_ic_back" />
            </FrameLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:paddingTop="9dp"
                android:paddingBottom="9dp"
                android:text="人脸识别验证"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="@dimen/sysq_text_size_18" />

            <FrameLayout
                android:id="@+id/help"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_alignParentRight="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:text="帮助"
                    android:textColor="@color/sysq_text_color_gray"
                    android:textSize="@dimen/sysq_dialog_16_sp" />
            </FrameLayout>

        </RelativeLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="42dp"
            android:src="@drawable/sysq_bg_face_verify_confirm" />

        <TextView
            android:layout_marginLeft="27.5dp"
            android:layout_marginRight="27.5dp"
            android:id="@+id/tv_confirm_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="46.5dp"
            android:gravity="center"
            android:text="@string/sysq_txt_face_verify_tip"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="@dimen/sysq_dialog_16_sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27.5dp"
            android:layout_marginTop="40dp"
            android:layout_marginRight="27.5dp"
            android:background="@drawable/sy37_shape_gray_radius_5"
            android:orientation="vertical"
            android:padding="15dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="    姓名："
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="XX雪"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_text_size_14" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="身份证："
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_text_size_14" />

                <TextView
                    android:id="@+id/tv_user_card"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4***************X"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_text_size_14" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="90dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_privacy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27.5dp"
            android:layout_marginRight="27.5dp"
            android:layout_marginBottom="25dp">

            <ImageView
                android:id="@+id/iv_clause"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_marginTop="2dp"
                android:src="@drawable/sysq_ic_check_face_verify_normal" />

            <TextView
                android:id="@+id/tv_clause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:lineSpacingExtra="5dp"
                android:text="@string/sysq_txt_face_verify_privacy"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="@dimen/sysq_text_size_12" />


        </LinearLayout>

        <TextView
            android:id="@+id/tv_start_verify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="27.5dp"
            android:layout_marginRight="27.5dp"
            android:background="@drawable/sysq_bg_red_corner"
            android:gravity="center"
            android:paddingTop="9.5dp"
            android:paddingBottom="9.5dp"
            android:text="开始验证"
            android:textColor="@color/sysq_white"
            android:textSize="@dimen/sysq_text_size_18" />
    </LinearLayout>


</LinearLayout>