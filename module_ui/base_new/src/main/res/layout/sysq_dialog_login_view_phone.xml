<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">


    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="340dp"
        android:layout_height="235dp"
        android:layout_gravity="center"
        android:background="@drawable/sy_sq_bg_login">

        <com.sy37sdk.account.view.base.view.BackTitleView
            android:id="@+id/common_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.sy37sdk.account.view.base.view.BackTitleView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/common_title_view"
            android:paddingLeft="20dp"
            android:paddingRight="20dp">

            <LinearLayout
                android:id="@+id/ll_phone"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="17dp"
                android:background="@drawable/sysq_dialog_login_gray_bg"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="21dp"
                    android:inputType="number"
                    android:text="+86"
                    android:textColor="@color/sysq_dialog_login_text_primary"
                    android:textSize="@dimen/sysq_dialog_login_text_primary" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="8.5dp"
                    android:layout_marginBottom="8.5dp"
                    android:background="@color/sysq_dialog_login_text_hint" />

                <com.sy37sdk.account.view.base.view.ClearEditText
                    android:id="@+id/et_phone"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="@string/sysq_login_phone_hint"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:paddingLeft="19dp"
                    android:paddingRight="10dp"
                    android:singleLine="true"
                    android:textColor="@color/sysq_dialog_login_text_primary"
                    android:textColorHint="@color/sysq_dialog_login_text_hint"
                    android:textSize="@dimen/sysq_dialog_login_text_primary" />

            </LinearLayout>


            <TextView
                android:id="@+id/tv_get_verify_code"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_below="@+id/ll_phone"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="16dp"
                android:background="@drawable/sy_sq_dialog_login_btn_bg"
                android:gravity="center"
                android:text="@string/sysq_obtain_verify_code"
                android:textColor="@color/sysq_login_btn_text_color"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />

            <LinearLayout
                android:id="@+id/ll_privacy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_get_verify_code"
                android:layout_marginTop="13dp">

                <CheckBox
                    android:id="@+id/cb_clause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/sysq_dialog_login_privacy_check_box"
                    android:gravity="center_vertical"
                    android:paddingLeft="5dp"
                    android:text="@string/sysq_privacy_agreement"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="12dp"
                    android:background="@android:color/transparent"
                    android:stateListAnimator="@null"/>

                <TextView
                    android:id="@+id/tv_clause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/sysq_user_agreement"
                    android:textColor="@color/sysq_dialog_login_text_accent"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="及"
                    android:textColor="@color/sysq_dialog_login_text_secondary"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                    android:id="@+id/tv_policy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/sysq_privacy_policy"
                    android:textColor="@color/sysq_dialog_login_text_accent"
                    android:textSize="@dimen/sysq_dialog_login_text_tiny" />

            </LinearLayout>

            <TextView
              android:id="@+id/tv_forget_pwd"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_below="@+id/ll_privacy"
              android:layout_marginTop="20dp"
              android:text="@string/sysq_forget_pwd"
              android:textColor="@color/sy_sq_dialog_text_primary"
              android:textSize="@dimen/sysq_dialog_login_text_secondary" />

            <TextView
                android:id="@+id/tv_quick_start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_privacy"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp"
                android:text="@string/sysq_quick_start_game"
                android:textColor="@color/sy_sq_dialog_text_primary"
                android:textSize="@dimen/sysq_dialog_login_text_secondary" />

            <TextView
                android:id="@+id/tv_account_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_privacy"
                android:layout_alignParentRight="true"
                android:layout_marginTop="20dp"
                android:text="@string/sysq_account_pwd_login"
                android:textColor="@color/sy_sq_dialog_text_primary"
                android:textSize="@dimen/sysq_dialog_login_text_secondary" />
        </RelativeLayout>
    </RelativeLayout>
</FrameLayout>