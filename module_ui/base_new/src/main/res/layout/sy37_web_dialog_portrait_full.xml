<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/keyboard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#66000000"
    android:clickable="true">

    <LinearLayout
        android:id="@+id/web_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:minHeight="60dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"
                android:textSize="18dp" />

            <ImageButton
                android:id="@+id/togame"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dip"
                android:padding="10dip" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.sqwan.common.webview.SQWebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FFFFFF"
                android:scaleType="centerInside" />

            <RelativeLayout
                android:id="@+id/sy37_rl_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/sy37_iv_loading"
                    android:layout_width="66dp"
                    android:layout_height="66dp"
                    android:layout_centerInParent="true" />

                <TextView
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="10dp"
                    android:layout_below="@+id/sy37_iv_loading"
                    android:textSize="12dp"
                    android:textColor="#666666"
                    android:text="@string/sysq_loading_txt"
                    android:id="@+id/sy37_tv_loading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/sy37_m_net_error_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#FFFFFF"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/sy37_iv_back"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:padding="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/sy37_icon_back" />

                <include layout="@layout/sy37_net_error_view_port" />
            </LinearLayout>
        </RelativeLayout>

        <com.sqwan.common.web.WebViewToolBar
            android:id="@+id/web_tool_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"/>

    </LinearLayout>

    <FrameLayout
      android:id="@+id/fl_video_container"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:visibility="gone"/>
</RelativeLayout>