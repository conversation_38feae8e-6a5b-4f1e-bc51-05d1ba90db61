<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/root"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/sy37_confirm_dialog_bg"
        android:gravity="center"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_sy37_announcement_title"
                android:layout_centerInParent="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:textColor="@color/sy37_share_save_img"
                android:textSize="16dp"/>

            <RelativeLayout
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="20dp">

                <ImageView
                    android:id="@+id/iv_sy37_announcement_close"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_close_confirm_dialog" />
            </RelativeLayout>
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_sy37_announcement_content"
            android:textSize="14dp"
            android:layout_gravity="center"
            android:maxWidth="250dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/ll_sy37_announcement_ensure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/account_reg_bg"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="20dp"
            android:paddingTop="5dp"
            android:paddingRight="20dp"
            android:paddingBottom="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:text="我知道了"
                android:textColor="@color/s_white"
                android:textSize="14dp" />
        </LinearLayout>
    </LinearLayout>


</RelativeLayout>