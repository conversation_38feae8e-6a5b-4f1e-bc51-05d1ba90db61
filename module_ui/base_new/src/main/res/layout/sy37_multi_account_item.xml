<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingStart="10dp"
  android:paddingEnd="12dp"
  android:paddingTop="12dp"
  android:background="@drawable/sy37_shape_white_radius_10">
    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical">

      <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="最近玩过："
          android:textSize="12dp"
          android:textColor="@color/sysq_text_color_gray"
          android:layout_marginEnd="8dp" />

        <TextView
          android:id="@+id/tv_game_name"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="精灵盛典"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:textSize="13dp" />
      </LinearLayout>
      <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="角色信息："
          android:textSize="12dp"
          android:textColor="@color/sysq_text_color_gray"
          android:layout_marginEnd="8dp" />

        <TextView
          android:id="@+id/tv_role_server"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:maxLines="1"
          android:maxWidth="120dp"
          android:ellipsize="end"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:textSize="13dp" />

        <TextView
          android:id="@+id/tv_role_level"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:maxLines="1"
          android:layout_marginStart="2dp"
          android:maxWidth="150dp"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:textSize="13dp" />
      </LinearLayout>
      <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="最近登录："
          android:textSize="12dp"
          android:textColor="@color/sysq_text_color_gray"
          android:layout_marginEnd="8dp" />

        <TextView
          android:id="@+id/tv_login_time"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textColor="@color/sysq_dialog_login_text_primary"
          android:textSize="13dp" />

        <TextView
          android:id="@+id/tv_new"
          android:layout_width="34dp"
          android:layout_height="16dp"
          android:textColor="#FE673A"
          android:background="@drawable/sy37_bg_round_149"
          android:textSize="11dp"
          android:visibility="gone"
          android:layout_marginStart="5dp"
          android:gravity="center"
          android:text="最新"/>
      </LinearLayout>

    </LinearLayout>

    <TextView
      android:id="@+id/tv_confirm"
      android:layout_width="55dp"
      android:layout_height="25dp"
      android:text="确定"
      android:textColor="@color/s_white"
      android:gravity="center"
      android:background="@drawable/sy37_confirm_bg_round_13"
      android:layout_centerVertical="true"
      android:layout_alignParentEnd="true"/>
</RelativeLayout>