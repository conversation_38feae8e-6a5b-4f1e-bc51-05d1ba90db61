<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy37_shape_white_radius_5"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="15dp"
            android:text="@string/guarantee_dialog_title"
            android:textColor="#333333"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            android:textColor="#666666"
            android:textSize="16dp"
            tools:text="@string/guarantee_dialog_tip_limit" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="20dp"
            android:background="#e5e5e5" />

        <TextView
            android:id="@+id/tv_sure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:text="@string/guarantee_dialog_btn_txt"
            android:textColor="#ff4823"
            android:textSize="16dp" />
    </LinearLayout>
</FrameLayout>