<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llPolicy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvProtol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="为方便玩家登录，本游戏提供快捷查询历史账号服务，会向您申请获取存储权限和设备权限（部分机型称为“电话权限”）。"
            android:textColor="#333333"
            android:textSize="14dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="您可以自主选择是否授权，拒绝授权将无法提供查询历史账号服务，但不影响游戏的正常运行。"
            android:textColor="#333333"
            android:textSize="14dp" />

    </LinearLayout>
</LinearLayout>