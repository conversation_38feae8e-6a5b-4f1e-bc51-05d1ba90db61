<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="340dp"
        android:layout_height="240dp"
        android:layout_gravity="center"
        android:background="@drawable/sy_sq_bg_login">

        <com.sy37sdk.account.view.base.view.BackTitleView
            android:id="@+id/common_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        </com.sy37sdk.account.view.base.view.BackTitleView>

        <RelativeLayout
            android:layout_below="@+id/common_title_view"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/ll_account_row"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="17dp"
                android:background="@drawable/sysq_dialog_login_gray_bg"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/iv_account_type"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginLeft="12dp"
                    android:src="@drawable/sysq_ic_account" />

                <TextView
                    android:id="@+id/tv_account"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="12dp"
                    android:paddingTop="15dp"
                    android:paddingBottom="15dp"
                    android:singleLine="true"
                    android:textColor="@color/sysq_dialog_login_text_primary"
                    android:textColorHint="@color/sysq_dialog_login_text_hint"
                    android:textSize="@dimen/sysq_dialog_login_text_primary" />

                <ImageView
                    android:id="@+id/iv_select_account"
                    android:layout_width="42dp"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:src="@drawable/sysq_ic_account_list_down" />
            </LinearLayout>



            <TextView
                android:id="@+id/tv_login"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_below="@id/ll_account_row"
                android:layout_marginTop="16dp"
                android:background="@drawable/sy_sq_dialog_login_btn_bg"
                android:gravity="center"
                android:text="@string/sysq_login"
                android:textColor="@color/sy_sq_login_btn_text_color"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />


            <LinearLayout
              android:id="@+id/ll_privacy"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_below="@+id/tv_login"
              android:layout_marginTop="13dp">

                <CheckBox
                  android:id="@+id/cb_clause"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:button="@drawable/sysq_dialog_login_privacy_check_box"
                  android:gravity="center_vertical"
                  android:paddingLeft="5dp"
                  android:text="@string/sysq_privacy_agreement"
                  android:textColor="@color/sysq_dialog_login_text_secondary"
                  android:textSize="12dp"
                  android:background="@android:color/transparent"
                  android:stateListAnimator="@null"/>

                <TextView
                  android:id="@+id/tv_clause"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:ellipsize="end"
                  android:maxLines="1"
                  android:text="@string/sysq_user_agreement"
                  android:textColor="@color/sysq_dialog_login_text_accent"
                  android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="及"
                  android:textColor="@color/sysq_dialog_login_text_secondary"
                  android:textSize="@dimen/sysq_dialog_login_text_tiny" />

                <TextView
                  android:id="@+id/tv_policy"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:ellipsize="end"
                  android:maxLines="1"
                  android:text="@string/sysq_privacy_policy"
                  android:textColor="@color/sysq_dialog_login_text_accent"
                  android:textSize="@dimen/sysq_dialog_login_text_tiny" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_other_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ll_privacy"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="15dp"
                android:text="@string/sysq_other_account_login"
                android:textColor="@color/sy_sq_dialog_text_primary"
                android:textSize="@dimen/sysq_dialog_login_text_secondary" />

            <TextView
              android:id="@+id/tv_forget_pwd"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_below="@+id/ll_privacy"
              android:layout_marginTop="15dp"
              android:text="@string/sysq_forget_pwd"
              android:textColor="@color/sy_sq_dialog_text_primary"
              android:textSize="@dimen/sysq_dialog_login_text_secondary" />
        </RelativeLayout>
    </RelativeLayout>

</FrameLayout>