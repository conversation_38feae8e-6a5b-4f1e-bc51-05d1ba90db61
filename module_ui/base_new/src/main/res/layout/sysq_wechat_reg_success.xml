<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sy_sq_bg_login"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:text="@string/sysq_wechat_reg_success_title"
                android:textColor="@color/sysq_dialog_login_text_primary"
                android:textSize="19dp"
                android:textStyle="bold" />

            <RelativeLayout
                android:id="@+id/view_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/btn_close"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_centerInParent="true"
                    android:scaleType="fitCenter"
                    android:src="@drawable/sysq_ic_close" />
            </RelativeLayout>
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/ll_account_row"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginTop="17dp"
            android:background="@drawable/sysq_dialog_login_gray_bg"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:src="@drawable/sysq_ic_account" />


            <TextView
                android:id="@+id/tv_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="15dp"
                tools:text="zz123456789z" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_pwd_row"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_below="@id/ll_account_row"
            android:layout_marginTop="12dp"
            android:background="@drawable/sysq_dialog_login_gray_bg"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:src="@drawable/sysq_ic_pwd" />


            <TextView
                android:id="@+id/tv_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:ellipsize="end"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="15dp"
                tools:text="zz12345678" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_enter_game"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="20dp"
            android:background="@drawable/sy_sq_dialog_login_btn_bg"
            android:gravity="center"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:text="@string/sysq_txt_sure"
            android:textColor="@color/sysq_white"
            android:textSize="16dp" />

        <TextView
            android:id="@+id/tv_no_qr_success"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:text="@string/sysq_wechat_reg_success_tip"
            android:textColor="#FF6A25"
            android:textSize="12dp" />
    </LinearLayout>

</FrameLayout>