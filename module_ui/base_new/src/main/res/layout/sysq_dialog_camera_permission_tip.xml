<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/modify_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sysq_bg_white_corner"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="没有权限获取相机摄像头"
            android:textColor="#333333"
            android:textSize="18dp" />

       <TextView
           android:lineSpacingExtra="5dp"
           android:textSize="@dimen/sysq_dialog_16_sp"
           android:textColor="@color/sysq_text_color_gray"
           android:layout_marginTop="10dp"
           android:text="@string/sysq_txt_face_verify_permission_tip"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_gray_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="取消"
                android:textColor="@color/sysq_dialog_login_text_secondary"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />

            <TextView
                android:id="@+id/tv_sure"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="22dp"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_red_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="设置"
                android:textColor="@color/sysq_white"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>