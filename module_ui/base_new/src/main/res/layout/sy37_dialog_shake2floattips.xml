<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/sy37_shape_white_radius_4"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:gravity="center_vertical"
        android:layout_marginTop="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:src="@drawable/sy37_ic_float_shake_tips"
            android:layout_width="24dp"
            android:layout_height="24dp"/>
        <TextView
            android:id="@+id/tv_tips"
            android:layout_marginLeft="10dp"
            tools:text="摇一摇显示悬浮球"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_float_shake_status"
        android:gravity="center_vertical"
        android:layout_marginTop="25dp"
        android:layout_marginBottom="25dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:src="@drawable/sy37_btn_float_shake_status"
            android:layout_width="20dp"
            android:layout_height="20dp"/>
        <TextView
            android:textSize="13dp"
            android:layout_marginLeft="10dp"
            android:text="不再提示"
            android:textColor="#ff999999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
    <View
        android:background="@android:color/darker_gray"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"/>

    <TextView
        android:textColor="#ffffa100"
        android:id="@+id/btnOk"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:text="知道了"
        android:textSize="15dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</LinearLayout>