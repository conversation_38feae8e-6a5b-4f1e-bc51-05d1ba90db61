<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:gravity="center"
	android:background="#00ffffff"
	>

	<RelativeLayout
		android:id="@+id/rl_loading_img"
		android:layout_width="100dp"
		android:layout_height="100dp"
		android:background="@drawable/sy37_base_common_loading_bg"
		android:gravity="center">

		<ProgressBar
			android:id="@+id/progress"
			android:layout_width="32dp"
			android:layout_height="32dp"
			android:layout_centerHorizontal="true"
			android:layout_centerVertical="true"
			android:indeterminateBehavior="repeat"
			android:indeterminateDrawable="@drawable/sy37_base_common_loading_bar_anim"
			android:indeterminateDuration="1500" />

		<TextView
			android:id="@+id/msg"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_below="@+id/progress"
			android:layout_centerHorizontal="true"
			android:layout_marginTop="15dp"
			android:ellipsize="middle"
			android:gravity="center"
			android:maxLines="2"
			android:paddingLeft="3dp"
			android:text="加载中"
			android:textColor="#FFFFFF"
			android:textSize="16dp"
			android:visibility="visible" />
	</RelativeLayout>

</RelativeLayout>