<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_pay_way"
            android:layout_width="22dp"
            android:layout_height="22dp"
            tools:src="@drawable/sysq_ic_pay_wechat" />

        <TextView
            android:id="@+id/tv_pay_way"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7.5dp"
            android:textColor="@color/sysq_dialog_login_text_primary"
            android:textSize="14dp"
            tools:text="微信支付" />

        <TextView
            android:id="@+id/tv_pay_promote"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="20dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/sysq_pay_discount_color"
            android:textSize="12dp"
            tools:text="活动文案不超过8个字" />

        <ImageView
            android:id="@+id/iv_select_status"
            android:layout_width="20dp"
            android:layout_height="20dp"
            tools:src="@drawable/sysq_ic_pay_selected" />
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="29.5dp"
        android:background="@color/sysq_pay_divider" />
</LinearLayout>