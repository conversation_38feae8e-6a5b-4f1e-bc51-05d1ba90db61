<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="155dp"
    android:layout_height="wrap_content"
    android:gravity="center">
    <RelativeLayout
        android:id="@+id/view_show"
        android:layout_width="155dp"
        android:layout_height="42dp">
    <LinearLayout
        android:paddingBottom="5dp"
        android:gravity="center_vertical"
        android:background="@drawable/sy37_bg_authcountdown_show"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:ellipsize="end"
            android:singleLine="true"
            android:id="@+id/tvAuthCd"
            android:paddingLeft="5dp"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="试玩：120分钟"
            android:textColor="#ffffffff"
            android:textSize="14dp"
            />
        <TextView
            android:id="@+id/tvAuth"
            android:paddingRight="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sysq_name_auth"
            android:textColor="#ffffad0e"
            android:textSize="14dp"
            />
    </LinearLayout>
        <View
            android:id="@+id/view_show_arrow"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_width="70dp"
            android:layout_height="20dp"/>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/view_hide"
        android:layout_centerHorizontal="true"
        android:layout_width="70dp"
        android:layout_height="20dp">
        <View
            android:background="@drawable/sy37_bg_authcountdown_hide"
            android:layout_centerHorizontal="true"
            android:layout_width="48dp"
            android:layout_height="10dp"/>
    </RelativeLayout>

</RelativeLayout>