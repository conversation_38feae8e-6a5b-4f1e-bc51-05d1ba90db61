<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/sysq_bg_white_corner"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:textColor="#333333"
            android:textSize="18dp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="我是对话框的标题"
            tools:visibility="visible" />

       <TextView
           android:id="@+id/tv_message"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:layout_marginStart="20dp"
           android:layout_marginTop="20dp"
           android:layout_marginEnd="20dp"
           android:lineSpacingExtra="5dp"
           android:textColor="#666666"
           android:textSize="16dp"
           tools:text="我是对话框的内容我是对话框的内容我是对话框的内容我是对话框的内容我是对话框的内容"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_gray_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="取消"
                android:textColor="#999999"
                android:textSize="17dp" />

            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/sysq_bg_red_corner"
                android:gravity="center"
                android:paddingTop="11dp"
                android:paddingBottom="11dp"
                android:text="确认"
                android:textColor="@color/sysq_white"
                android:textSize="@dimen/sysq_dialog_login_text_accent" />
        </LinearLayout>

    </LinearLayout>

</FrameLayout>