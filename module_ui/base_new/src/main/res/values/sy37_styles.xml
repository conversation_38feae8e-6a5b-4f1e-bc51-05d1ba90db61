<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="FullScreenDialogTransparentStyle" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <!--透明背景-->
        <item name="android:background">@android:color/transparent</item>
<!--        <item name="android:windowSoftInputMode">stateAlwaysVisible</item>&lt;!&ndash;显示软件盘&ndash;&gt;-->
    </style>

</resources>