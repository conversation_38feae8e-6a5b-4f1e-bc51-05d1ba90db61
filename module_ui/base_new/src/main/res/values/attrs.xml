<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RoundedImageView">
        <attr name="corner_radius" format="dimension" />
        <attr name="border_width" format="dimension" />
        <attr name="border_color" format="color" />
        <attr name="mutate_background" format="boolean" />
        <attr name="oval" format="boolean" />
        <attr name="android:scaleType" />
    </declare-styleable>
    <item name="authsdk_checkbox_view" type="id"/>
    <item name="authsdk_login_view" type="id"/>
    <item name="authsdk_logorl_view" type="id"/>
    <item name="authsdk_nologobg_view" type="id"/>
    <item name="authsdk_number_view" type="id"/>
    <item name="authsdk_privacy_body_view" type="id"/>
    <item name="authsdk_privacy_protocol_view" type="id"/>
    <item name="authsdk_privacy_title_view" type="id"/>
    <item name="authsdk_protocol_view" type="id"/>
    <item name="authsdk_switch_view" type="id"/>
    <item name="authsdk_title_view" type="id"/>
    <item name="pns_nav_return" type="id"/>
    <item name="pns_nav_title" type="id"/>
    <item name="pns_optional_layer_container" type="id"/>
    <item name="pns_protocol_checkbox" type="id"/>
    <item name="pns_protocol_textview" type="id"/>
</resources>