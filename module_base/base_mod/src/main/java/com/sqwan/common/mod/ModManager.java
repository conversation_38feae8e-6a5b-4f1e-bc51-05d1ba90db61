package com.sqwan.common.mod;

import android.content.Context;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/28
 */
public class ModManager {

    private static volatile ModManager sInstance;

    public static ModManager getInstance() {
        if (sInstance == null) {
            synchronized (ModManager.class) {
                if (sInstance == null) {
                    sInstance = new ModManager();
                }
            }
        }

        return sInstance;
    }

    private final Map<Class<? extends IModBase>, IModBase> mModMap = new HashMap<>();
    private final Map<Class<? extends IModBase>, ModConfig> mModConfigMap = new HashMap<>();
    private Context mContext;

    private ModManager() {
    }

    public void clearMod() {
        mModConfigMap.clear();
    }

    public void putMod(Class<? extends IModBase> key, ModConfig config) {
        if (key != null && config != null) {
            mModConfigMap.put(key, config);
        }
    }

    public void loadMod(Context context) {
        mContext = context;
        for (Map.Entry<Class<? extends IModBase>, ModConfig> entry : mModConfigMap.entrySet()) {
            getMod(entry.getKey());
        }
    }

    @Nullable
    public IModBase getMod(Class<? extends IModBase> modClazz) {
        IModBase modInstance = mModMap.get(modClazz);
        if (modInstance != null) {
            // 缓存
            return modInstance;
        }
        ModConfig modConfig = mModConfigMap.get(modClazz);
        if (modConfig == null) {
            SQLog.w("模块 " + modClazz.getName() + " 不存在");
            return null;
        }
        String className = modConfig.modImpString;
        boolean initWithContext = modConfig.initWithContext;
        if (TextUtils.isEmpty(className)) {
            throw new IllegalStateException("未找到模块配置!");
        }
        try {
            Class<?> modClass = mContext.getClassLoader().loadClass(className);
            Constructor<?> constructor;
            if (!initWithContext) {
                constructor = modClass.getDeclaredConstructor();
                constructor.setAccessible(true);
                modInstance = (IModBase) constructor.newInstance();
            } else {
                constructor = modClass.getDeclaredConstructor(Context.class);
                constructor.setAccessible(true);
                modInstance = (IModBase) constructor.newInstance(mContext);
            }
            SQLog.i("模块 " + modClass.getName() + " 存在");
            mModMap.put(modClazz, modInstance);
        } catch (Throwable e) {
            SQLog.e("模块 " + className + " 创建异常", e);
        }
        return modInstance;
    }

    public static class ModConfig {

        final String modImpString;
        final boolean initWithContext;

        public ModConfig(String modImpString) {
            this(modImpString, false);
        }

        public ModConfig(String modImpString, boolean initWithContext) {
            this.modImpString = modImpString;
            this.initWithContext = initWithContext;
        }
    }
}
