package com.sqwan.order.base;

import android.support.annotation.NonNull;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

public class PayInfoModel {

    /**
     * CP订单ID（必传）
     */
    private String mDoid;

    /**
     * CP商品名
     */
    private String mDpt;

    /**
     * Cp游戏服ID (必传)
     */
    private String mDsid;

    /**
     * CP游戏服名称(必传)
     */
    private String mDsName;

    /**
     * CP扩展回调参数(必传)
     */
    private String mDext;

    /**
     * CP角色ID(必传)
     */
    private String mDrid;

    /**
     * CP角色名(必传)
     */
    private String mDrName;

    /**
     * CP角色等级(必传)
     */
    private int mDrLevel;

    /**
     * CP金额(必传)
     */
    private float mDmoney;

    /**
     * CP兑换比率(1元兑换率默认1:10)(必传)
     */
    private int mDradio;

    /**
     * CP货币名称
     */
    private String mDcn;

    public String getOrderId() {
        return mDoid;
    }

    public PayInfoModel setOrderId(String orderId) {
        this.mDoid = orderId;
        return this;
    }

    public String getProductName() {
        return mDpt;
    }

    public PayInfoModel setProductName(String productName) {
        this.mDpt = productName;
        return this;
    }

    public String getServerId() {
        return mDsid;
    }

    public PayInfoModel setServerId(String serverId) {
        this.mDsid = serverId;
        return this;
    }

    public String getServerName() {
        return mDsName;
    }

    public PayInfoModel setServerName(String serverName) {
        this.mDsName = serverName;
        return this;
    }

    public String getExtend() {
        return mDext;
    }

    public PayInfoModel setExtend(String ext) {
        this.mDext = ext;
        return this;
    }

    public String getRoleId() {
        return mDrid;
    }

    public PayInfoModel setRoleId(String roleId) {
        this.mDrid = roleId;
        return this;
    }

    public String getRoleName() {
        return mDrName;
    }

    public PayInfoModel setRoleName(String roleName) {
        this.mDrName = roleName;
        return this;
    }

    public int getRoleLevel() {
        return mDrLevel;
    }

    public PayInfoModel setRoleLevel(int level) {
        this.mDrLevel = level;
        return this;
    }

    public float getMoney() {
        return mDmoney;
    }

    public PayInfoModel setMoney(float money) {
        this.mDmoney = money;
        return this;
    }

    public int getRadio() {
        return mDradio;
    }

    public PayInfoModel setRadio(int radio) {
        this.mDradio = radio;
        return this;
    }

    public String getCurrencyName() {
        return mDcn;
    }

    public PayInfoModel setCurrencyName(String currencyName) {
        this.mDcn = currencyName;
        return this;
    }

    @Override
    @NonNull
    public String toString() {
        return "  orderId=" + getOrderId() + "\n"
            + "  productName=" + getProductName() + "\n"
            + "  serverId=" + getServerId() + "\n"
            + "  serverName=" + getServerName() + "\n"
            + "  extend=" + getExtend() + "\n"
            + "  roleId=" + getRoleId() + "\n"
            + "  roleName=" + getRoleName() + "\n"
            + "  roleLevel=" + getRoleLevel() + "\n"
            + "  money=" + getMoney() + "\n"
            + "  currencyName=" + getCurrencyName() + "\n"
            + "  radio=" + getRadio();
    }

    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("doid", getOrderId());
        map.put("dpt", getProductName());
        map.put("dcn", getCurrencyName());
        map.put("dsid", getServerId());
        map.put("dsname", getServerName());
        map.put("dext", getExtend());
        map.put("drid", getRoleId());
        map.put("drname", getRoleName());
        map.put("drlevel", String.valueOf(getRoleLevel()));
        map.put("dmoney", String.valueOf(getMoney()));
        map.put("dradio", String.valueOf(getRadio()));
        return map;
    }

    @NonNull
    public String toJson() {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("doid", getOrderId());
            jsonObject.put("dpt", getProductName());
            jsonObject.put("dcn", getCurrencyName());
            jsonObject.put("dsid", getServerId());
            jsonObject.put("dsname", getServerName());
            jsonObject.put("dext", getExtend());
            jsonObject.put("drid", getRoleId());
            jsonObject.put("drname", getRoleName());
            jsonObject.put("drlevel", getRoleLevel());
            jsonObject.put("money", getMoney());
            jsonObject.put("dradio", getRadio());
            return jsonObject.toString();
        } catch (Exception e) {
            // 不可能
            return "";
        }
    }

    @NonNull
    public static PayInfoModel fromJson(String json) {
        PayInfoModel order = new PayInfoModel();
        try {
            JSONObject jsonObject = new JSONObject(json);
            order.setOrderId(jsonObject.optString("doid"));
            order.setProductName(jsonObject.optString("dpt"));
            order.setCurrencyName(jsonObject.optString("dcn"));
            order.setServerId(jsonObject.optString("dsid"));
            order.setServerName(jsonObject.optString("dsname"));
            order.setExtend(jsonObject.optString("dext"));
            order.setRoleId(jsonObject.optString("drid"));
            order.setRoleName(jsonObject.optString("drname"));
            order.setRoleLevel(jsonObject.optInt("drlevel"));
            order.setMoney((float) jsonObject.optDouble("money"));
            order.setRadio(jsonObject.optInt("dradio"));
        } catch (Exception e) {
            /* no-op */
        }
        return order;
    }
}
