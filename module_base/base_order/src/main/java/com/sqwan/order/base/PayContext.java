package com.sqwan.order.base;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.sdk.tool.util.SqLogUtil;
import java.util.UUID;
import org.json.JSONObject;

/**
 * 记录支付过程的上下文
 *
 * <AUTHOR>
 * @since 2024/7/15
 */
public class PayContext {

    private static final String TAG = "【Pay Ctx】";
    @NonNull
    private final String mSession;
    /**
     * 触发支付流程的动作
     */
    @NonNull
    private final String mAction;
    /**
     * 实际的支付方式, 和前端有对应关系
     */
    @NonNull
    private PayWay mPayWay = PayWay.UNKNOWN;
    /**
     * 订单信息
     */
    @Nullable
    private PayInfoModel mPayInfo;

    /**
     * 支付过程中的一些数据，上报需要用
     */
    @Nullable
    private PayExtraInfo mPayExtraInfo;

    /**
     * 下单接口返回的moid
     */
    private String mMoid;
    /**
     * 下单接口的原始data数据
     */
    private String mRawOrderData;
    /**
     * 下单接口的data数据json, 避免重复解析json
     */
    private JSONObject mOrderDataJson;
    /**
     * 1: h5
     * 2: 原生支付
     */
    private String mPayChannel;

    /**
     * 取消支付的路径, 上报用
     */
    private String mCancelWay;

    public PayContext(@NonNull String action) {
        mSession = UUID.randomUUID().toString();
        mAction = action;
    }

    public void setPayWay(@NonNull PayWay payWay) {
        mPayWay = payWay;
    }

    public void setPayInfo(@NonNull PayInfoModel payInfo) {
        if (mPayInfo != null && mPayInfo != payInfo) {
            SqLogUtil.w(TAG + "重复设置PayInfo");
        }
        mPayInfo = payInfo;
    }

    @Nullable
    public PayInfoModel getPayInfo() {
        return mPayInfo;
    }

    @NonNull
    public PayWay getPayWay() {
        return mPayWay;
    }

    @NonNull
    public String getAction() {
        return mAction;
    }

    @NonNull
    public String getSession() {
        return mSession;
    }

    public String getMoid() {
        return mMoid;
    }

    public void setMoid(String moid) {
        mMoid = moid;
    }

    public String getRawOrderData() {
        return mRawOrderData;
    }

    public void setRawOrderData(String rawOrderData) {
        mRawOrderData = rawOrderData;
    }

    public JSONObject getOrderDataJson() {
        return mOrderDataJson;
    }

    public void setOrderDataJson(JSONObject orderDataJson) {
        mOrderDataJson = orderDataJson;
    }

    public String getPayChannel() {
        return mPayChannel;
    }

    public void setPayChannel(String payChannel) {
        mPayChannel = payChannel;
    }

    public String getCancelWay() {
        return mCancelWay;
    }

    public void setCancelWay(String cancelWay) {
        mCancelWay = cancelWay;
    }

    @Nullable
    public PayExtraInfo getPayExtraInfo() {
        return mPayExtraInfo;
    }

    public void setPayExtraInfo(PayExtraInfo payExtraInfo) {
        mPayExtraInfo = payExtraInfo;
    }
}
