package com.sqwan.order.base;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.track.SqTrackKey;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

/**
 * 辅助构建上报的data字段
 *
 * <AUTHOR>
 * @since 2024/9/9
 */
public class ReportDataBuilder {

    public final Map<String, String> map = new HashMap<>();

    public ReportDataBuilder payInfo(@Nullable PayInfoModel payInfo) {
        if (payInfo != null) {
            put(SqTrackKey.order_amount, payInfo.getMoney());
            put("order_currency", payInfo.getCurrencyName());
            put(SqTrackKey.cp_order_id, payInfo.getOrderId());

            put("product_name", payInfo.getProductName());
            put("role_id", payInfo.getRoleId());
            put("role_name", payInfo.getRoleName());
            put("server_id", payInfo.getServerId());
            put("server_name", payInfo.getServerName());
            put("cp_extend", payInfo.getExtend());
        }
        return this;
    }

    public ReportDataBuilder payExtraInfo(@Nullable PayExtraInfo extraInfo) {
        if (extraInfo != null) {
            put(SqTrackKey.pay_amount, extraInfo.payAmount);
            put(SqTrackKey.pay_version, extraInfo.payVersion);
            put(SqTrackKey.is_vouchers, extraInfo.isVouchers);
            String vouchersId = extraInfo.vouchersId;
            put(SqTrackKey.vouchers_id, TextUtils.isEmpty(vouchersId) || vouchersId.equals("[]") ? "0" : vouchersId);
        }
        return this;
    }

    public ReportDataBuilder payCtx(@Nullable PayContext payCtx) {
        if (payCtx == null) {
            return this;
        }
        put(SqTrackKey.pay_session, payCtx.getSession());
        put("pay_start_action", payCtx.getAction());
        put(SqTrackKey.pay_channels, payCtx.getPayChannel());
        orderId(payCtx.getMoid());
        payInfo(payCtx.getPayInfo());
        payExtraInfo(payCtx.getPayExtraInfo());
        payWay(payCtx.getPayWay());
        return this;
    }

    /**
     * moid
     */
    public ReportDataBuilder orderId(@Nullable String orderId) {
        if (orderId != null) {
            put(SqTrackKey.order_id, orderId);
        }
        return this;
    }

    public ReportDataBuilder payWay(@Nullable PayWay payWay) {
        if (payWay != null) {
            put(SqTrackKey.pay_method, String.valueOf(payWay.type));
            put(SqTrackKey.pay_method_name, payWay.desc);
        }
        return this;
    }

    public ReportDataBuilder error(@Nullable String error) {
        if (!TextUtils.isEmpty(error)) {
            put(SqTrackKey.reason_fail, error);
        }
        return this;
    }

    public ReportDataBuilder error(@Nullable SqPayError error) {
        if (error != null) {
            put(SqTrackKey.fail_code, error.code);
            put(SqTrackKey.sub_error_code, error.originCode);
            put(SqTrackKey.reason_fail, error.msg);
            put(SqTrackKey.sub_error_msg, error.originMsg);
        }
        return this;
    }

    public ReportDataBuilder timing(String timing) {
        return put("timing", timing);
    }

    public ReportDataBuilder ext(String data) {
        return put("ext_data", data);
    }

    public ReportDataBuilder put(String name, @Nullable Object value) {
        if (value == null || TextUtils.isEmpty(name)) {
            return this;
        }
        try {
            String str = String.valueOf(value);
            if (TextUtils.isEmpty(str)) {
                return this;
            }
            if (map.containsKey(name) && !str.equals(map.get(name))) {
                SQLog.w("【Pay Report】覆盖上报参数" + name + ", " + map.get(name) + " ==> " + str);
            }
            map.put(name, str);
        } catch (Exception e) {
            /* no-op */
        }
        return this;
    }

    @NonNull
    @Override
    public String toString() {
        return new JSONObject(map).toString();
    }
}
