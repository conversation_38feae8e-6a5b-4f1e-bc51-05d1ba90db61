package com.sqwan.order.base;

import android.os.Parcelable;
import android.support.annotation.IntRange;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.base.SqError;
import com.sqwan.common.constants.SqErrorCode;

/**
 * 支付异常
 * 和海外保持一致
 *
 * <AUTHOR>
 * @since 2022/7/11
 */
public class SqPayError extends SqError implements Parcelable {

    public SqPayError(int code, @NonNull String msg) {
        super(code, msg);
    }

    public SqPayError(int code, @NonNull String msg, int originCode) {
        super(code, msg, originCode, msg);
    }

    public SqPayError(int code, @NonNull String msg, int originCode, @Nullable String originMsg) {
        super(code, msg, originCode, originMsg);
    }

    public SqPayError(SqError error) {
        super(error);
    }

    /**
     * response code != 0s
     */
    public static SqPayError httpRespError(int code, int state, String msg) {
        return new SqPayError(SqError.serverError(code, state, msg));
    }

    /**
     * 接口异常
     */
    public static SqPayError httpReqError(int code, VolleyError error) {
        return new SqPayError(SqError.httpError(code, error));
    }

    /**
     * 内部错误, 一般是内部校验问题, 如参数错误
     */
    public static final int MODULE_INTERNAL = 1;
    /**
     * 接口相关错误, 一般是后端的问题
     */
    public static final int MODULE_HTTP = 2;
    /**
     * 第三方错误, 一般是第三方sdk的问题
     */
    public static final int MODULE_THIRD = 3;

    /**
     * @param module 0-9
     * @param code 两位数
     */
    private static int code(@IntRange(from = 0, to = 9) int module, @IntRange(from = 0, to = 99) int code) {
        return SqErrorCode.code(SqErrorCode.MODULE_PAY, module * 100 + code);
    }

    /*  sdk内部异常错误码  */
    /**
     * 不支持的操作
     */
    public static final int ERROR_SDK_UNSUPPORTED = code(MODULE_INTERNAL, 1);
    /**
     * 金额异常
     */
    public static final int ERROR_SDK_PRICE = code(MODULE_INTERNAL, 10);
    /**
     * 内嵌支付, url异常
     */
    public static final int ERROR_SDK_INVALID_PAY_URL = code(MODULE_INTERNAL, 11);
    /**
     * 用户信息异常
     */
    public static final int ERROR_SDK_USER_ERROR = code(MODULE_INTERNAL, 12);
    /**
     * 选择支付类型异常
     */
    public static final int ERROR_SDK_INVALID_PAY_TYPE = code(MODULE_INTERNAL, 13);
    /**
     * 支付时触发消耗商品, 消耗成功, 认为支付失败
     */
    public static final int ERROR_SDK_CONSUME_SUCCESS_AT_PAY = code(MODULE_INTERNAL, 14);
    /**
     * 支付时触发消耗商品, 但消耗失败, 认为支付失败
     */
    public static final int ERROR_SDK_CONSUME_FAIL_AT_PAY = code(MODULE_INTERNAL, 15);
    /**
     * 用户登记年龄失败
     */
    public static final int ERROR_SDK_CONFIG_AGE = code(MODULE_INTERNAL, 16);
    /**
     * 充值限额提示, 用户取消
     */
    public static final int ERROR_SDK_RECHARGE_TIP = code(MODULE_INTERNAL, 17);
    /**
     * 内嵌支付异常
     */
    public static final int ERROR_SDK_INLINE = code(MODULE_INTERNAL, 18);
    /**
     * 校验发货url异常
     */
    public static final int ERROR_SDK_VERIFY_URL = code(MODULE_INTERNAL, 19);
    /**
     * 禁用支付
     */
    public static final int ERROR_SDK_DISABLE_PAY = code(MODULE_INTERNAL, 20);
    /**
     * 实名认证拦截
     */
    public static final int ERROR_SDK_AUTH = code(MODULE_INTERNAL, 21);
    /**
     * 防沉迷拦截
     */
    public static final int ERROR_SDK_ADDITION = code(MODULE_INTERNAL, 22);
    /**
     * 原生支付异常
     */
    public static final int ERROR_SDK_NATIVE = code(MODULE_INTERNAL, 23);
    /*  http异常错误码  */
    /**
     * 年龄接口异常
     */
    public static final int ERROR_HTTP_AGE_CONFIG = code(MODULE_HTTP, 10);
    /**
     * 充值限制接口异常
     */
    public static final int ERROR_HTTP_RECHARGE_LIMIT = code(MODULE_HTTP, 11);
    /**
     * 创建订单接口异常
     */
    public static final int ERROR_HTTP_CREATE_ORDER = code(MODULE_HTTP, 12);
    /**
     * 确认订单接口异常
     */
    public static final int ERROR_HTTP_CONFIRM_ORDER = code(MODULE_HTTP, 13);
    /**
     * 查询订单接口异常
     */
    public static final int ERROR_HTTP_FIND_ORDER = code(MODULE_HTTP, 14);
    /**
     * 校验发货订单接口异常
     */
    public static final int ERROR_HTTP_VERIFY = code(MODULE_HTTP, 15);
    /**
     * 谷歌积分下单接口异常
     */
    public static final int ERROR_HTTP_GPP_ORDER = code(MODULE_HTTP, 16);

    /*  第三方sdk异常错误码  */
    /**
     * 第三方sdk初始化失败
     */
    public static final int ERROR_THIRD_INIT = code(MODULE_THIRD, 10);
    /**
     * 第三方sdk查询商品失败
     */
    public static final int ERROR_THIRD_QUERY_DETAIL = code(MODULE_THIRD, 11);
    /**
     * 第三方sdk购买商品失败
     */
    public static final int ERROR_THIRD_PURCHASE = code(MODULE_THIRD, 12);
    /**
     * 第三方sdk查询未消耗商品失败
     */
    public static final int ERROR_THIRD_QUERY_PURCHASE = code(MODULE_THIRD, 13);
    /**
     * 第三方sdk消耗商品失败
     */
    public static final int ERROR_THIRD_CONSUME = code(MODULE_THIRD, 14);
}
