package com.sqwan.order.base;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.logger.SQLog;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

/**
 * 支付类型
 *
 * <AUTHOR>
 * @since 2022/7/7
 * @noinspection unused
 */
public class PayWay {

    public static final PayWay UNKNOWN = new PayWay(0, "unknown");
    /**
     * 微信支付
     */
    public static final PayWay WECHAT = new PayWay(1, "wxpay");
    /**
     * 支付宝支付
     */
    public static final PayWay ALI = new PayWay(2, "alipay");
    /**
     * 钱包支付
     */
    public static final PayWay WALLET = new PayWay(3, "wtpay");
    /**
     * 花呗
     */
    public static final PayWay HUA_BEI = new PayWay(4, "hbpay");
    /**
     * 人工充值
     */
    public static final PayWay LABOR = new PayWay(5, "labor");
    /**
     * 云闪付
     */
    public static final PayWay UNION = new PayWay(8, "union");
    /**
     * 抖音
     */
    public static final PayWay DOU_YIN_H5 = new PayWay(9, "douyin_h5");

    public final int type;
    @NonNull
    public final String desc;

    public PayWay(int type, @NonNull String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * {@link #name}首字母大写
     */
    public String capitalize() {
        final String str = desc.toLowerCase(Locale.US);
        int strLen;
        if ((strLen = str.length()) == 0) {
            return str;
        }

        final char firstChar = str.charAt(0);
        final char newChar = Character.toTitleCase(firstChar);
        if (firstChar == newChar) {
            // already capitalized
            return str;
        }

        char[] newChars = new char[strLen];
        newChars[0] = newChar;
        str.getChars(1, strLen, newChars, 1);
        return String.valueOf(newChars);
    }

    /**
     * 获取和后端关联的pay way字符串
     */
    public String getHttpPayWay() {
        return desc;
    }

    private static final List<PayWay> VALUES = new ArrayList<PayWay>() {
        {
            add(UNKNOWN);
            add(WECHAT);
            add(ALI);
            add(WALLET);
            add(HUA_BEI);
            add(LABOR);
            add(UNION);
            add(DOU_YIN_H5);
        }
    };

    public static void register(PayWay type) {
        if (VALUES.contains(type)) {
            SQLog.wt("sqsdk", "重复注册支付类型" + type);
            return;
        }
        SQLog.dt("sqsdk", "注册支付类型" + type);
        VALUES.add(type);
    }

    public static Collection<PayWay> values() {
        return VALUES;
    }

    @NonNull
    public static PayWay get(@Nullable String name) {
        if (name != null) {
            for (PayWay v : PayWay.values()) {
                if (v.desc.equalsIgnoreCase(name)) {
                    return v;
                }
            }
        }

        return UNKNOWN;
    }

    @NonNull
    public static PayWay get(int type) {
        for (PayWay v : PayWay.values()) {
            if (v.type == type) {
                return v;
            }
        }

        return UNKNOWN;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        PayWay payType = (PayWay) o;

        if (type != payType.type) {
            return false;
        }
        return desc.equals(payType.desc);
    }

    @Override
    public int hashCode() {
        int result = type;
        result = 31 * result + desc.hashCode();
        return result;
    }

    @NonNull
    @Override
    public String toString() {
        return desc;
    }
}
