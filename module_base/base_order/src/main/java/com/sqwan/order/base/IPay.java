package com.sqwan.order.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2024/7/15
 */
public interface IPay {

    void init(@NonNull Context context);

    void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @Nullable PayCallback callback);

    interface PayCallback {

        void onSuccess(@NonNull PayContext payCtx);

        void onCancel(@NonNull PayContext payCtx);

        void onFailed(@NonNull PayContext payCtx, @NonNull SqPayError error);
    }

    class UIPayCallback implements PayCallback {

        private static final Handler sHandler = new Handler(Looper.getMainLooper());
        final PayCallback callback;

        @NonNull
        public static PayCallback wrap(@Nullable PayCallback callback) {
            if (callback instanceof UIPayCallback) {
                return callback;
            } else {
                return new UIPayCallback(callback);
            }
        }

        public UIPayCallback(@Nullable PayCallback callback) {
            this.callback = callback;
        }

        @Override
        public void onSuccess(@NonNull PayContext payCtx) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onSuccess(payCtx);
                } else {
                    sHandler.post(() -> callback.onSuccess(payCtx));
                }
            }
        }

        @Override
        public void onCancel(@NonNull PayContext payCtx) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onCancel(payCtx);
                } else {
                    sHandler.post(() -> callback.onCancel(payCtx));
                }
            }
        }

        @Override
        public void onFailed(@NonNull PayContext payCtx, @NonNull SqPayError error) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onFailed(payCtx, error);
                } else {
                    sHandler.post(() -> callback.onFailed(payCtx, error));
                }
            }
        }

        private static boolean isMainThread() {
            return Looper.getMainLooper() == Looper.myLooper();
        }
    }
}
