<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal" >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical" >

                <Button
                    android:id="@+id/loginBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Login(登录)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/changeAccountBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="ChangeAccount(切换账号)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/payBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Pay(定额)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/payBtn2"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Pay(不定额)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/logoutBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Logout(不再使用)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/showExit"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showExitDailog(退出游戏对话框)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/showSQWebBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showSQWeb"
                    android:textSize="12dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical" >

                <Button
                    android:id="@+id/creatRole"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="creatRole(不再使用)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/creatRoleBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="creatRoleInfo(创建角色-提交信息)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/submitDataBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="submitRoleInfo(角色进服-提交信息)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/upgradeDataBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:text="upgradeRoleInfo(角色升级-提交信息)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/submitStatisticsInfo"
                    android:layout_width="200dp"
                    android:visibility="gone"
                    android:layout_height="wrap_content"
                    android:text="submitStatisticsInfo(提交统计数据)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/getappconfigBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="getConfig(获取配置)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/wxShareBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="wxShare"
                    android:visibility="gone"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/showPersonalBtn"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="showPersonalWeb"
                    android:textSize="12dp" />


                <Button
                    android:id="@+id/joinRoom"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="joinRoom(进入语音房间)"
                    android:textSize="12dp" />

                <Button
                    android:id="@+id/share_img"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="分享"
                    android:textSize="12dp" />

            </LinearLayout>


        </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="6dp"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="订单金额："
            android:textColor="#121212"
            android:textSize="18dp"
            />

        <EditText
            android:id="@+id/et_money"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:text="1"
            android:gravity="center_horizontal"
            />

    </LinearLayout>


</LinearLayout>