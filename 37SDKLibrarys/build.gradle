import com.google.gson.Gson
import org.dom4j.Document
import org.dom4j.DocumentException
import org.dom4j.Element
import org.dom4j.io.SAXReader
import java.util.regex.Matcher
import java.util.regex.Pattern

apply plugin: 'com.android.library'
apply plugin: "com.jfrog.artifactory"
apply plugin: 'maven-publish'

//发布library库的版本号,每次更新时候需要修改此变量
def libraryVersion = get_libraryVersion()

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion 15
        targetSdkVersion 30

        //android 6.0(api 23) SDK以及以上，不再提供org.apache.http.*(只保留几个类).
        useLibrary 'org.apache.http.legacy'

        // sdk版本设定
        versionCode 20
        versionName libraryVersion

        //不使用java8进行编译
        //    jackOptions {
        //      enabled true
        //    }
    }

    // 配置结构
    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java']
            resources.srcDirs = ['src/main/resources']
            aidl.srcDirs = ['src/main/aidl']
            renderscript.srcDirs = ['src/maom']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDir 'src/main/jniLibs'
        }
    }

    lintOptions {
        quiet true
        abortOnError false
        ignoreWarnings true
        disable 'InvalidPackage' //Some libraries have issues with this.
        disable 'OldTargetApi' //Lint gives this warning but SDK 20 would be Android L Beta.
        disable 'IconDensities' //For testing purpose. This is safe to remove.
        disable 'IconMissingDensityFolder' //For testing purpose. This is safe to remove.
    }

    compileOptions {
        encoding "UTF-8"
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/dependencies.txt'
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/com.android.tools/proguard/coroutines.pro'
    }
}

dependencies {
    api fileTree(include: ['*.jar','*.aar'], dir: 'libs')
    api ('com.android.support:support-v4:26.0.0')
    // 没有找到需要依赖fast json的, 但是重构前产物中有, 所以保留
    api rootProject.ext.other.fastjson
}

publishing {
    publications {
        aar(MavenPublication) {
            groupId = rootProject.ext.artifactory_groupId
            artifactId "sySDKLibraryRes"
            version = libraryVersion
            artifact "${project.buildDir}/outputs/aar/${project.name}-release.aar"
        }
    }
}

artifactory {
    contextUrl = rootProject.ext.artifactory_address
    publish {
        repository {
            repoKey = rootProject.ext.artifactory_repoKey
            username = rootProject.ext.artifactory_user
            password = rootProject.ext.artifactory_password
        }
        defaults {
            publishArtifacts = true
            publications('aar')
            publishPom = true //Publish generated POM files to Artifactory (true by default)
            publishIvy = true
            //Publish generated Ivy descriptor files to Artifactory (true by default)
        }
    }
}

//build和clean后执行
task generateSqR {
    doLast {
        println "执行阶段执行完毕"
        def packageName = "com/sqwan/sdk/libs"
        def RFilePath = getBuildDir().absolutePath + "/generated/source/r/debug/" + packageName + "/R.java"
        println "源文件：" + RFilePath
        def rFileContent = getContent(RFilePath)
        if (rFileContent == null){
            println 'R文件为空！'
            return
        }
        Pattern pattern = Pattern.compile("int (.*?)=(.*?);")
        Matcher matcher = pattern.matcher(rFileContent)
        // 将原先的R文件的int换成String，并将其值使用变量名赋值
        while (matcher.find()){
//        println matcher.group()
            String replace = "String " + matcher.group(1) + " = \"" + matcher.group(1) + "\";"
            rFileContent = rFileContent.replaceAll(matcher.group(), replace)
        }
        // 修改R文件的类名为SqResource
        def newClassName = "SqR"
        def newClassPath = rootProject.getProjectDir().getAbsolutePath() + "/37sdkcommon/src/main/java/" + packageName + File.separator + newClassName + ".java"
        println "输出文件：" + newClassPath
        rFileContent = rFileContent.replaceAll("class R", "class " + newClassName)
        write(newClassPath, rFileContent)
        println "自动生成SqR文件成功！"
    }
}

//读取文件内容
def getContent(String path){
    try{
        def file = file(path)
        return file.exists() ? file.text : null
    }catch(GradleException e){
        println 'file not found'
    }
    return null
}

//写文件
void write(String filePath, String content) {
    File file = new File(filePath)
    if (!file.exists()){
        file.getParentFile().mkdirs()
        file.createNewFile()
    }
    BufferedWriter bw = null;

    try {
        // 根据文件路径创建缓冲输出流
        bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8"));
        // 将内容写入文件中
        bw.write(content);
        bw.flush();
    } catch (Exception e) {
        e.printStackTrace();
    } finally {
        // 关闭流
        if (bw != null) {
            try {
                bw.close();
            } catch (IOException e) {
                e.printStackTrace();
                bw = null;
            }
        }
    }
}

this.afterEvaluate {
    //需要写入assets目录，所以执行时机放在packageReleaseAssets之后
    tasks['packageReleaseAssets'].doLast {
        println("-------------------------start generate the res config json---------------------------")
        File resDis = new File("build/intermediates/packaged_res/release")
        println(resDis.getAbsolutePath())
        FileModel fileModel = new FileModel()
        if(resDis.exists()) {
            File[] subFiles = resDis.listFiles()
            SAXReader saxReader = new SAXReader()
            for (File file : subFiles) {
                if(file.getName().contains("values")) {
                    File[] values = file.listFiles()
                    for(File valueFile : values) {
                        try {
                            Document valueDoc = saxReader.read(valueFile)
                            Element rootEle = valueDoc.getRootElement()
                            List<Element> val = rootEle.elements()
                            val.each {
                                String type = it.getName()
                                Set<String> vals = fileModel.values.get(type)
                                if(vals == null) {
                                    vals = new HashSet<>()
                                    fileModel.values.put(type, vals)
                                }
                                vals.add(it.attribute("name").getValue())
                            }
                        } catch (DocumentException e) {
                            e.printStackTrace()
                        }
                    }
                } else {
                    String name = file.getName()
                    if(name == ".DS_Store") {
                        continue
                    }
                    System.out.println(name)
                    Set<String> files = fileModel.files.get(name)
                    if(files == null){
                        files = new HashSet<>()
                        fileModel.files.put(name, files)
                    }
                    File[] resFiles = file.listFiles()
                    for (File res : resFiles) {
                        if(res.getName() == ".DS_Store") {
                            continue
                        }
                        files.add(res.getName())
                    }
                }
            }
            System.out.println(fileModel.values.size())
            Gson gson = new Gson()
            System.out.println(gson.toJson(fileModel))

            String jsonFilePath = "build/intermediates/library_assets/release/packageReleaseAssets/out/sdk_res.json"
            File file = new File(jsonFilePath)
            if(!file.exists()) {
                file.createNewFile()
            }
            FileWriter writer
            try {
                writer = new FileWriter(jsonFilePath)
                String content = gson.toJson(fileModel).replace("{", "{\n").replace(",",",\n").replace("}","}\n")
                writer.write(content)
                writer.flush()
                writer.close()
            } catch (IOException e) {
                e.printStackTrace()
            }

            println "开始操作删除文件"
            String[] strings = ["com.sqwan.sdk.libs.BuildConfig"]
            String baseDir = buildDir.getAbsolutePath() + "/intermediates/javac/release/compileReleaseJavaWithJavac/classes/"
            for (String fileName : strings) {
                File f = new File(baseDir + getPathByClassName(fileName))
                println f.getAbsolutePath()
                if (f.exists()) {
                    f.delete()
                    println "删除" + fileName
                } else {
                    println "要删除的文件不存在"
                }
            }
        }

        println("------------------------- end generate the res config json ---------------------------")
    }
}


def getPathByClassName(String className) {
    return className.replace(".","/")+".class"
}

class FileModel {
    Map<String, Set<String>> files = new HashMap<>();
    Map<String, Set<String>> values = new HashMap<>();
}