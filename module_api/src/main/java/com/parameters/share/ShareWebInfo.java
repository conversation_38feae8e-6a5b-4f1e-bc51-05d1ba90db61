package com.parameters.share;

import android.graphics.Bitmap;

public class ShareWebInfo implements IShareInfo {
    private String title;
    private String desc;
    private String pageUrl;
    private Bitmap thumbBmp ;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public Bitmap getThumbBmp() {
        return thumbBmp;
    }

    public void setThumbBmp(Bitmap thumbBmp) {
        this.thumbBmp = thumbBmp;
    }

    @Override
    public int classify() {
        return ShareClassify.SHARE_WEB;
    }
}
