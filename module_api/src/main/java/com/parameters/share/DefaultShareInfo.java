package com.parameters.share;


public class DefaultShareInfo implements IShareInfo {
    //邀请码
    private String inviteCode;
    //图片id
    private String imgId;

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public String getImgId() {
        return imgId;
    }

    public void setImgId(String imgId) {
        this.imgId = imgId;
    }


    @Override
    public int classify() {
        return ShareClassify.SHARE_DEFAULT;
    }
}
