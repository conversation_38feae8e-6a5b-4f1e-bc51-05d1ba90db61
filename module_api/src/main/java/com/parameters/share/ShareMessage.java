package com.parameters.share;

public class ShareMessage {
    //分享实体
    private IShareInfo shareMessage;
    //分享平台
    private int platform;
    //是否跳过分享预览页面
    private boolean skipPreview;

    public IShareInfo getShareMessage() {
        return shareMessage;
    }

    public void setShareMessage(IShareInfo shareMessage) {
        this.shareMessage = shareMessage;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public boolean isSkipPreview() {
        return skipPreview;
    }

    public void setSkipPreview(boolean skipPreview) {
        this.skipPreview = skipPreview;
    }
}
