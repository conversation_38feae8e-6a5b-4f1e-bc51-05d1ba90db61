package com.parameters.share;

import android.graphics.Bitmap;

import org.json.JSONObject;

public class WebShare {

    public static final int JS_SHARE_TYPE_IMG = 1;

    public static final int JS_SHARE_TYPE_H5 = 2;

    public static final String JS_SHARE_CHANNEL_WECAHT = "wechat";

    public static final String JS_SHARE_CHANNEL_MOMENT = "moments";

    public static final String JS_SHARE_CHANNEL_QQ = "qq";

    //分享类型（1：图片，  2：h5）
    private int type;
    //分享平台
    private String way;
    //标题
    private String title;
    //描述
    private String desc;
    //h5链接
    private String landingPageUrl;
    //图片url
    private String img;

    private Bitmap bitmap;


    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getWay() {
        return way;
    }

    public void setWay(String way) {
        this.way = way;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLandingPageUrl() {
        return landingPageUrl;
    }

    public void setLandingPageUrl(String landingPageUrl) {
        this.landingPageUrl = landingPageUrl;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    /**
     * json转bean
     *
     * @param json
     * @return
     */
    public static WebShare parseFromJson(String json) {
        WebShare webShare = new WebShare();
        try {
            JSONObject obj = new JSONObject(json);
            final int type = obj.optInt("type");
            final String channel = obj.optString("way");
            final String title = obj.optString("title");
            final String desc = obj.optString("desc");
            final String link = obj.optString("landingPageUrl");
            String imgUrl = obj.optString("img");
            webShare.setWay(channel);
            webShare.setType(type);
            webShare.setTitle(title);
            webShare.setDesc(desc);
            webShare.setLandingPageUrl(link);
            webShare.setImg(imgUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return webShare;
    }

    public static String parseToJson(WebShare webShare) {
        String result = "";
        try {
            JSONObject object = new JSONObject();
            object.putOpt("type", webShare.getType());
            object.putOpt("way", webShare.getWay());
            object.putOpt("title", webShare.getTitle());
            object.putOpt("desc", webShare.getDesc());
            object.putOpt("landingPageUrl", webShare.getLandingPageUrl());
            object.putOpt("img", webShare.getImg());
            result = object.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
