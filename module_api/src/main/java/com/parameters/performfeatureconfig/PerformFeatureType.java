package com.parameters.performfeatureconfig;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-04 11:06
 */
public interface PerformFeatureType {
    String TYPE_RESTART = "restart";
    String TYPE_SHOWTRANSPARENTWEBDIALOG = "showTransparentWebDialog";
    String TYPE_SHARE = "share";
    String TYPE_GAMEBEHAVIOR = "gameBehavior";
    String TYPE_AUTHRESULTCHECK = "authResultCheck";
    String TYPE_SHOW_WEB_DIALOG="showWebDialog";
    String TYPE_GET_SUPPLIER_ID="getSupplierId";

    //获取适龄提醒图标
    String TYPE_AGE_APPROPRIATE_ICON = "age_appropriate_icon";

    //展示适龄提醒webview页面
    String TYPE_SHOW_AGE_APPROPRIATE = "showAgeAppropriate";

    //上报购买数据
    String TYPE_REPORT_PURCHASE_DATA = "reportPurchaseData";

    //扫码登录
    String TYPE_SCAN_LOGIN = "scan_login";

    //是否支持扫码登录
    String TYPE_SUPPORT_SCAN_LOGIN = "support_scan_login";

    //好评跳转
    String TYPE_SHOW_GOOD_REVIEW = "showGoodReview";

    //政策双清单
    String TYPE_POLICY = "policy";

}
