package com.parameters.performfeatureconfig;


import com.sqwan.msdk.api.SQResultListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-05 10:52
 */
public class PerformFeature {
    public String type;
    public Object data;
    public SQResultListener sqResultListener;

    public PerformFeature(String type, Object data, SQResultListener sqResultListener) {
        this.type = type;
        this.data = data;
        this.sqResultListener = sqResultListener;
    }

    public static Map<String, PerformFeature> map = new HashMap<>();
}
