package com.sqwan.msdk.api;

public class RegisterReportBean {

    public RegisterReportBean(String type, boolean isSuccess) {
        mType = type;
        this.isSuccess = isSuccess;
    }

    private String mType;

    private boolean isSuccess;

    public String getType() {
        return mType;
    }

    public void setType(String mType) {
        this.mType = mType;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

}
