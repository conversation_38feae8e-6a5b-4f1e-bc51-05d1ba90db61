package com.sqwan.msdk.api;

import android.content.Context;
import java.util.Map;

public interface SQReportInterface {

    /**
     * 应用激活事件
     * @param context
     */
    void init(Context context);

    /**
     * 权限申请之后
     */
    void afterPermission(Context context);

    /**
     * 账号注册事件上报
     * @param bean
     */
    void eventRegister(RegisterReportBean bean);

    /**
     * 支付事件上报
     * @param bean
     */
    void eventPurchase(PurchaseReportBean bean);

    /**
     * 事件上报
     * @param json
     */
    void eventCpPay(String json);


    /**
     * 事件上报
     * @param event
     * @param params
     */
    void report(String event, Map<String, String> params);

}
