package com.sqwan.msdk.api.tool;

import com.sqwan.msdk.api.SQResultListener;

import java.util.Map;

/**
 *    author : znb
 *    time   : 2021/05/10
 *    desc   : 直播接口
 */
public interface ILiveshow {

    /**
     * 是否支持直播功能
     */
    boolean isSupportLiveVideo();

    /**
     * 加入直播间
     */
    void joinLiveshowRoom(Map<String, String> data, SQResultListener listener);

    /**
     * 离开直播间
     */
    void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener);

    /**
     * 监听直播声音变化
     */
    void setLiveshowVoiceChangeCallback(SQResultListener listener);

    /**
     * 监听直播关闭
     */
    void setLiveshowDestroyCallback(SQResultListener listener);

    /**
     * 执行直播扩展方法
     */
    void performLiveshowFeature(Map<String, String> data, SQResultListener listener);
}
