package com.sqwan.msdk.api;

public class InitBean {


    public int usesdk = 0;

    public String appid = "";
    // appid == gid

    public String appkey = "";
    //key

    public String refer = "";
    //adkey == refer

    public int landScape;
    //是否横屏显示 1-横屏；

    public int isFixed;
    //是否定额，1-定额

    public String merchantId;
    //dcn

    public String serverSeqNum;
    //dcn

    public String gameId;
    //uc

    public String serverId;
    //uc

    public int ucDebug;
    //是否打开uc的debug模式，0-不打开，1打开

    public String rate;
    //oppo

    public String gameName;
    //anzhi

    public String channel;
    //anzhi

    public int debug;
    //1-显示，非1，不显示

    public int testTag;
    //1-显示测试信息，非1 ，不显示测试信息

    public String payid;
    //应用汇

    public String paykey;
    //应用汇

    public int isTencentPayTest;
    //tencent是否为沙箱测试环境

    public String wxAppid;
    //tencent 微信appid

    public String wxAppkey;
    //tencent 微信appkey

    public int isSplashShow = 0;
    //显示闪屏。否则不显示.如果切了，则不显示

    public int useSQExit = 1;
    //1显示37的退出框，否则使用原生的退出框。某些CPS的渠道需要

    public int usePlatformExit = 0;
    //1第三方有退出框则调用，1第三方没有退出框，则调用一个原生的退出框。

    public int isPushDelay = 1;
    //1:表示开启6个小时的延迟判断，否则不开启

    public int isSDK202 = 0;
    //1:表示是老版本的2.0.2的SDK，则在登录成功之后，返回uid和uname,默认是0

    public int isSDK210 = 0;
    //1:表示是老版本的2.1.0的SDK，则在不做支付参数的验证,默认是0

    public int isLogDetect = 0;

    public int getIsTencentPayTest() {
        return isTencentPayTest;
    }

    public void setIsTencentPayTest(int isTencentPayTest) {
        this.isTencentPayTest = isTencentPayTest;
    }


    public String getWxAppid() {
        return wxAppid;
    }

    public void setWxAppid(String wxAppid) {
        this.wxAppid = wxAppid;
    }

    public String getWxAppkey() {
        return wxAppkey;
    }

    public void setWxAppkey(String wxAppkey) {
        this.wxAppkey = wxAppkey;
    }


    public int getUsesdk() {
        return usesdk;
    }

    public void setUsesdk(int usesdk) {
        this.usesdk = usesdk;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getServerSeqNum() {
        return serverSeqNum;
    }

    public void setServerSeqNum(String serverSeqNum) {
        this.serverSeqNum = serverSeqNum;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getLandScape() {
        return landScape;
    }

    public void setLandScape(int landScape) {
        this.landScape = landScape;
    }

    public int getIsFixed() {
        return isFixed;
    }

    public void setIsFixed(int isFixed) {
        this.isFixed = isFixed;
    }

    public int getDebug() {
        return debug;
    }

    public void setDebug(int debug) {
        this.debug = debug;
    }

    public int getTestTag() {
        return testTag;
    }

    public void setTestTag(int testTag) {
        this.testTag = testTag;
    }

    public int getUcDebug() {
        return ucDebug;
    }

    public void setUcDebug(int ucDebug) {
        this.ucDebug = ucDebug;
    }

    public String getPayid() {
        return payid;
    }

    public void setPayid(String payid) {
        this.payid = payid;
    }

    public String getPaykey() {
        return paykey;
    }

    public void setPaykey(String paykey) {
        this.paykey = paykey;
    }

    public String getRefer() {
        return refer;
    }

    public void setRefer(String refer) {
        this.refer = refer;
    }

    public int getIsSplashShow() {
        return isSplashShow;
    }

    public void setIsSplashShow(int isSplashShow) {
        this.isSplashShow = isSplashShow;
    }

    public int getUseSQExit() {
        return useSQExit;
    }

    public void setUseSQExit(int useSQExit) {
        this.useSQExit = useSQExit;
    }

    public int getUsePlatformExit() {
        return usePlatformExit;
    }

    public void setUsePlatformExit(int usePlatformExit) {
        this.usePlatformExit = usePlatformExit;
    }

    public int getIsPushDelay() {
        return isPushDelay;
    }

    public void setIsPushDelay(int isPushDelay) {
        this.isPushDelay = isPushDelay;
    }

    public int getIsSDK202() {
        return isSDK202;
    }

    public void setIsSDK202(int isSDK202) {
        this.isSDK202 = isSDK202;
    }

    public int getIsSDK210() {
        return isSDK210;
    }

    public void setIsSDK210(int isSDK210) {
        this.isSDK210 = isSDK210;
    }

    public void setIsLogDetect(int logDetect){
        this.isLogDetect=logDetect;
    }

    public int setIsLogDetect(){
        return isLogDetect;
    }
}
