package com.sqwan.msdk.api;

public class SQAppConfig implements ISQAppConfig {

    public SQAppConfig() {

    }

    private String gameid;

    private String partner;

    private String refer;

    public SQAppConfig(String gameId, String partner, String refer) {
        this.gameid = gameId;
        this.partner = partner;
        this.refer = refer;
    }

    @Override
    public String getGameid() {
        return gameid;
    }

    @Override
    public String getPartner() {
        return partner;
    }

    @Override
    public String getRefer() {
        return refer;
    }
}
