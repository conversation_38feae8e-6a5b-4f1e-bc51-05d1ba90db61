package com.sqwan.msdk.api.tool;

import android.content.Context;

import com.sqwan.msdk.api.SQResultListener;

/**
 * 扩展接口
 *
 * <AUTHOR>
 * @date 2018/10/17
 */
public interface IExtensionInterface {

    /**
     * 应用宝BBS论坛
     */
    void performFeatureBBS();

    /**
     * 应用宝V+特权
     */
    void performFeatureVPlayer();

    /**
     * 扩展接口
     * @param context  上下文
     * @param type   具体的业务类型
     * @param data   业务需要的数据
     * @param listener  回调
     */
    void performFeature(Context context, String type, Object data, SQResultListener listener);

}
