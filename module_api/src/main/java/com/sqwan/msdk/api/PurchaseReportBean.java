package com.sqwan.msdk.api;

/**
 * <AUTHOR>
 * 上报给媒体的支付相关数据Bean
 */
public class PurchaseReportBean {

    /**
     * 道具类型
     */
    private String mProductType = "be_null";

    /**
     * 道具名称
     */
    private String mProductName = "be_null";

    /**
     * 道具ID
     */
    private String mProductId = "be_null";

    /**
     * 道具数量
     */
    private int mCount;

    /**
     * 支付渠道
     */
    private String mChannel = "be_null";

    /**
     * 币种
     */
    private String mCurrency = "be_null";

    /**
     * 是否支付成功
     */
    private boolean isSuccess;

    /**
     * 价格
     */
    private int mPrice;

    /**
     * 单号
     * @return
     */
    private String mOrderId = "be_null";

    public String getProductType() {
        return mProductType;
    }

    public PurchaseReportBean setProductType(String mProductType) {
        this.mProductType = mProductType;
        return this;
    }

    public String getProductName() {
        return mProductName;
    }

    public PurchaseReportBean setProductName(String mProductName) {
        this.mProductName = mProductName;
        return this;
    }

    public String getProductId() {
        return mProductId;
    }

    public PurchaseReportBean setProductId(String mProductId) {
        this.mProductId = mProductId;
        return this;
    }

    public int getCount() {
        return mCount;
    }

    public PurchaseReportBean setCount(int mCount) {
        this.mCount = mCount;
        return this;
    }

    public String getChannel() {
        return mChannel;
    }

    public PurchaseReportBean setChannel(String mChannel) {
        this.mChannel = mChannel;
        return this;
    }

    public String getCurrency() {
        return mCurrency;
    }

    public PurchaseReportBean setCurrency(String mCurrency) {
        this.mCurrency = mCurrency;
        return this;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public PurchaseReportBean setSuccess(boolean success) {
        isSuccess = success;
        return this;
    }

    public int getPrice() {
        return mPrice;
    }

    public PurchaseReportBean setPrice(int mPrice) {
        this.mPrice = mPrice;
        return this;
    }

    public String getOrderId(){
        return mOrderId;
    }

    public PurchaseReportBean setOrderId(String orderId) {
        this.mOrderId = orderId;
        return this;
    }

    @Override
    public String toString() {
        return "PurchaseReportBean{" +
            "mProductType='" + mProductType + '\'' +
            ", mProductName='" + mProductName + '\'' +
            ", mProductId='" + mProductId + '\'' +
            ", mCount=" + mCount +
            ", mChannel='" + mChannel + '\'' +
            ", mCurrency='" + mCurrency + '\'' +
            ", isSuccess=" + isSuccess +
            ", mPrice=" + mPrice +
            ", mOrderId='" + mOrderId + '\'' +
            '}';
    }
}
