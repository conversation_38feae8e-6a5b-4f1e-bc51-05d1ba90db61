package com.sqwan.msdk.api;

import android.app.Application;
import android.content.Context;

/**
 * Application的对外接口
 *
 * <AUTHOR>
 * @since 2024/5/17
 */
public interface SQAppApi {

    /**
     * 旧宿主会调用这个方法
     * 并且在{@link #onCreate()}之前调用
     */
    void insertAppContext(Application application);

    Context getApplicationContext();

    /**
     * 关键重写方法
     */
    void attachBaseContext(Context base);

    /**
     * 关键重写方法
     */
    void onCreate();

    /**
     * 给媒体渠道使用
     */
    void setReporter(SQReportInterface reporter);

    /**
     * 给媒体渠道使用
     */
    void setMediaReporter(SQMediaReportInterface reporter);
}
