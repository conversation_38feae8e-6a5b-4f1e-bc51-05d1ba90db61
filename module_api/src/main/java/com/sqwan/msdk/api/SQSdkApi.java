package com.sqwan.msdk.api;

import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Resources;
import com.parameters.share.ShareMessage;
import com.sqwan.msdk.api.tool.ILiveRadio;
import com.sqwan.msdk.api.tool.ILiveshow;
import java.util.HashMap;

/**
 * 所有对外的接口
 *
 * <AUTHOR>
 * @since 2024/5/16
 */
public interface SQSdkApi extends
    ILiveshow, ILiveRadio,
    SQSdkInterface {

    void startActivity(Intent intent);

    void startActivityForResult(Intent intent, int requestCode);

    Resources getResources(Resources hostResource);

    AssetManager getAssets(AssetManager hostAssetManager);

    ClassLoader getClassLoader(ClassLoader hostClassLoader);

    boolean isSupportPlugin();

    void track(String event, String describe, HashMap<String, String> params);

    /**
     * 提供给联运渠道用的
     */
    SQSdkInterface getPlatform(Context context, InitBean bean, SQResultListener initListener);

    /**
     * 37平台初始化
     */
    void init(Context cxt, String appkey, SQResultListener listener);

    /**
     * 权限申请之后
     */
    void initCore(Context cxt, String appkey, SQResultListener listener);

    /**
     * 提交统计信息接口
     */
    void submitStatisticsInfo(String key, String values);

    SQAppConfig getAppConfig();

    /**
     * 联运渠道也有用到
     */
    void reportMDev(String identify);


    /**
     * 分享到微信
     *
     * @param title 分享的标题
     * @param description 分享的内容
     * @param shareUrl 跳转到WEB的网络URL地址
     * @param thumbUrl 分享的图片资源URL地址
     * @param resourceType 1:分享图片 0:分享WEB链接
     */
    void shareToWX(Context context, String title, String description, String shareUrl, String thumbUrl,
        int resourceType, SQResultListener shareListener);

    /**
     * 分享，走后台配置
     *
     * @param inviteCode 邀请码
     * @param shareListener 分享回调
     */
    void share(String inviteCode, SQResultListener shareListener);

    /**
     * 分享，走后台配置
     *
     * @param inviteCode 邀请码
     * @param img_id 图片id
     * @param shareListener 分享回调
     */
    void share(String inviteCode, String img_id, SQResultListener shareListener);

    /**
     * 通用分享接口
     */
    void share(ShareMessage shareMessage, SQResultListener shareListener);

    /**
     * 通过sdk打印日志
     * @param level eg: {@link android.util.Log#DEBUG}
     */
    void log(int level, String msg);

    /**
     * 监听推送的透传信息
     */
    void setSQPushTransmitMessageListener(SQPushTransmitMessageListener listener);

    /**
     * 广告视频接口
     */
    void showAdReward(Context context, String params, SQResultListener listener);
}
