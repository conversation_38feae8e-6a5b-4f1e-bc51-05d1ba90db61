package com.sqwan.msdk.api.tool;

import com.sqwan.msdk.api.SQResultListener;

import java.util.Map;

/**
 *    author : 黄锦群
 *    time   : 2022/08/25
 *    desc   : 电台直播接口
 */
public interface ILiveRadio {

    /**
     * 是否支持电台功能
     */
    boolean isSupportLiveRadio();

    /**
     * 加入电台
     */
    void joinLiveRadioRoom(Map<String, String> data, SQResultListener listener);

    /**
     * 离开电台
     */
    void leaveLiveRadioRoom(Map<String, String> data, SQResultListener listener);

    /**
     * 监听电台声音变化
     */
    void setLiveRadioVoiceChangeCallback(SQResultListener listener);

    /**
     * 监听电台关闭
     */
    void setLiveRadioDestroyCallback(SQResultListener listener);

    /**
     * 执行电台扩展方法
     */
    void performLiveRadioFeature(Map<String, String> data, SQResultListener listener);
}