package com.sqwan.msdk.api;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import com.sqwan.msdk.api.tool.IExtensionInterface;
import com.sqwan.msdk.api.tool.Tool;
import java.util.HashMap;

public interface SQSdkInterface extends Tool, IExtensionInterface {

	/**
	 * 登陆接口
	 *
	 * @param context
	 * @param listener
	 */
	void login(Context context, SQResultListener listener);

	/**
	 * 主动调用切换账号接口
	 *
	 * @param context
	 * @param listener
	 */
	void changeAccount(Context context,
                       SQResultListener listener);

	/**
	 * 从悬浮窗中切换账号接口
	 *
	 * @param listener
	 */
	void setSwitchAccountListener(SQResultListener listener);

	/**
	 * 回到游戏登录界面窗口
	 *
	 * @param listener
	 */
	void setBackToGameLoginListener(SQResultListener listener);

	/**
	 * 使用37Web容器打开指定URL
	 *
	 * @param url
	 */
	void showSQWebDialog(String url);

	/**
	 * 实名制弹窗
	 *
	 */
	void showSQPersonalDialog(Context context);

	/**
	 * 退出接口,不推荐使用
	 *
	 * @param context
	 * @param listener
	 */
	void logout(Context context, SQResultListener listener);

	/**
	 * 退出游戏对话框
	 *
	 * @param context
	 * @param listener
	 */
	void showExitDailog(Context context,
                        SQResultListener listener);

	/**
	 * 支付方法 标*号的为必传参数
	 *
	 * @param context
	 * @param doid
	 *            (*必填)
	 * @param dpt
	 *            (*必填)
	 * @param dcn
	 *            (*必填)
	 * @param dsid
	 *            (*必填)
	 * @param dsname
	 *            (*必填)
	 * @param dext
	 *            (*必填)
	 * @param drid
	 *            (*必填)
	 * @param drname
	 *            (*必填)
	 * @param drlevel
	 *            (*必填)
	 * @param dmoney
	 *            (*必填)
	 * @param dradio
	 *            (*必填)
	 * @param payListener
	 *            (*必填)
	 */
	void pay(Context context, String doid, String dpt,
             String dcn, String dsid, String dsname, String dext, String drid,
             String drname, int drlevel, float dmoney, int dradio,
             SQResultListener payListener);

	/**
	 * 提交角色信息接口（可以提交指定格式数据）
	 *
	 * @param infos
	 */
	void submitRoleInfo(HashMap<String, String> infos);// 进入服务器时调用

	void creatRoleInfo(HashMap<String, String> infos);// 创建角色时调用

	void upgradeRoleInfo(HashMap<String, String> infos);// 角色升级时调用

	/**
	 * 创建角色接口-不再建议使用 2015年03月03日 针对PPS渠道新增
	 *
	 * @param serverId
	 *            区服ID
	 */
	void creatRole(Context context, String serverId);

	/***
	 * 语音接口
	 */

	/**
	 * 实例化
	 *
	 * @param context
	 * @param listener
	 */
	void speechInit(Context context, SQResultListener listener);

	/**
	 * 设置海外服务器配置
	 * @param url 服务器地址
	 */
	void setServerInfo(String url);

	/**
	 * 回调函数驱动－主循环
	 */
	void poll();

	/**
	 * 进入国战房间
	 * @param roomName   房间名称
	 * @param role       角色id
	 * @param msTimeout  加入房间的超时时间，单位是毫秒
	 */
	void joinNationalRoom(String roomName, int role, int msTimeout);

	/**
	 * 进入组队房间
	 * @param roomName  房间名称
	 * @param msTimeout 加入房间的超时时间，单位是毫秒
	 */
	void joinTeamRoom(String roomName, int msTimeout);

	/**
	 * 退出房间
	 * @param roomName  房间名称
	 * @param msTimeout 退出房间的超时时间，单位是毫秒
	 */
	void quitRoom(String roomName, int msTimeout);

    /**
     * 打印日志
     */
    void printLog(int level, String tag, String content);

	/**
	 * 打开麦克风
	 */
	void openMic();

	/**
	 * 关闭麦克风
	 */
	void closeMic();

	/**
	 * 打开扬声器
	 */
	void openSpeaker();

	/**
	 * 关闭扬声器
	 */
	void closeSpeaker();

	/**
	 * 设置麦克风的音量
	 * paramInt  音量值
	 */
	void setMicLevel(int paramInt);

	/**
	 * 获取麦克风的音量
	 */
	int getMicLevel();

	/**
	 * 设置话筒的音量
	 * @param paramInt  音量值
	 */
	void setSpeakerVolume(int paramInt);

	/**
	 * 获取话筒的音量
	 */
	int getSpeakerVolume();

	/**
	 * 测试麦克风是否可用
	 */
	boolean testMic();

	/**
	 * 是否开关扬声器
	 * @param paramBoolean yes-打开   no-关闭
	 */
	void enableSpeakerOn(boolean paramBoolean);

	/**
	 * 禁止某成员语音
	 * @param paramInt
	 * @param paramBoolean
	 */
	void forbidMemberVoice(int paramInt, boolean paramBoolean);

	/**
	 * 进入房间回调
	 * @param context
	 * @param listener
	 */
	void onJoinRoomListener(Context context, SQResultListener listener);

	/**
	 * 退出房间回调
	 * @param context
	 * @param listener
	 */
	void onQuitRoomListener(Context context, SQResultListener listener);

	/**
	 * 成员状态回调-当房间中的其他成员开始说话或者停止说话的时候，
	 * 通过该回调进行通知
	 * @param context
	 * @param listener
	 */
	void onMemberVoiceListener(Context context, SQResultListener listener);

	/**
	 * 断网之后的回调－断网三分钟之后触发
	 * @param context
	 * @param listener
	 */
	void onStatusUpdateListener(Context context, SQResultListener listener);

	/**
	 * 周期方法
	 */
	void onStart();

	void onResume();// 必接

	void onPause();// 必接

	void onStop();// 必接

	void onDestroy();

	void onRestart();

	void onActivityResult(int requestCode, int resultCode,
                          Intent data);// 必接

	void onNewIntent(Intent intent);// 必接

	void setContext(Context context);// 必接

	void showUAgreement(Context context);

	void onConfigurationChanged(Configuration newConfig);

	void onWindowFocusChanged(boolean hasFocus);

	void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults);


}
