package com.sqwan.msdk;

import android.app.Activity;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;

public class SQActivity extends Activity {

    @Override
    protected void onStart() {
        super.onStart();
        SQwanCore.getInstance().onStart();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        SQwanCore.getInstance().onRestart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        SQwanCore.getInstance().onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        SQwanCore.getInstance().onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
        SQwanCore.getInstance().onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SQwanCore.getInstance().onDestroy();
    }

    @Override
    public Resources getResources() {
        return SQwanCore.getInstance().getResources(super.getResources());
    }

    @Override
    public AssetManager getAssets() {
        return SQwanCore.getInstance().getAssets(super.getAssets());
    }

    @Override
    public ClassLoader getClassLoader() {
        return SQwanCore.getInstance().getClassLoader(super.getClassLoader());
    }

    @Override
    public void startActivity(Intent intent) {
        SQwanCore.getInstance().startActivity(intent);
        super.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        SQwanCore.getInstance().startActivityForResult(intent, requestCode);
        super.startActivityForResult(intent, requestCode);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SQwanCore.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SQwanCore.getInstance().onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        SQwanCore.getInstance().onNewIntent(intent);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        SQwanCore.getInstance().onConfigurationChanged(newConfig);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        SQwanCore.getInstance().onWindowFocusChanged(hasFocus);
    }
}