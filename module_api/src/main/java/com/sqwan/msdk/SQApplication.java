package com.sqwan.msdk;

import android.app.Application;
import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.util.Log;
import com.sqwan.msdk.api.SQAppApi;
import com.sqwan.msdk.api.SQMediaReportInterface;
import com.sqwan.msdk.api.SQReportInterface;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class SQApplication extends Application implements SQAppApi {

    private final SQAppApi mDelegate = getDelegate();

    /**
     * 旧产物中有, 保留, 避免兼容问题
     */
    private static Object sInstance;

    /**
     * 旧产物中有, 保留, 避免兼容问题
     */
    @Deprecated
    public static Object getInstance() {
        return sInstance;
    }

    @Override
    public void insertAppContext(Application application) {
        // 这个类是起始类, 所以不会有人调用它的insertAppContext, 忽略
    }

    @Override
    public void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        sInstance = this;
        assert mDelegate != null;
        mDelegate.attachBaseContext(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        assert mDelegate != null;
        mDelegate.insertAppContext(this);
        mDelegate.onCreate();
    }

    @Override
    public void setReporter(SQReportInterface reporter) {
        assert mDelegate != null;
        mDelegate.setReporter(reporter);
    }

    @Override
    public void setMediaReporter(SQMediaReportInterface reporter) {
        assert mDelegate != null;
        mDelegate.setMediaReporter(reporter);
    }

    /**
     * 考虑插件宿主
     */
    @Override
    public Resources getResources() {
        return SQwanCore.getInstance().getResources(super.getResources());
    }

    /**
     * 考虑插件宿主
     */
    @Override
    public AssetManager getAssets() {
        return SQwanCore.getInstance().getAssets(super.getAssets());
    }

    /**
     * 考虑插件宿主
     */
    @Override
    public ClassLoader getClassLoader() {
        return SQwanCore.getInstance().getClassLoader(super.getClassLoader());
    }

    private SQAppApi getDelegate() {
        // 注意反射的类需要和本类在同一个class loader内
        ClassLoader classLoader = getClass().getClassLoader();
        if (classLoader == null) {
            Log.e("sqsdk", "无法获取class loader");
            return null;
        }
        String className = getDelegateClassName();
        Class<?> clazz;
        try {
            clazz = classLoader.loadClass(className);
        } catch (Throwable e) {
            Log.e("sqsdk", "无法加载目标类: " + className + ", " + e);
            return null;
        }
        Method instanceMethod;
        try {
            instanceMethod = clazz.getMethod("getInstance");
            Object obj = instanceMethod.invoke(null);
            if (obj instanceof SQAppApi) {
                return (SQAppApi) obj;
            } else {
                Log.e("sqsdk", "getInstance返回的实例类型异常: " + obj);
            }
        } catch (Throwable e) {
            Log.w("sqsdk", "无法通过getInstance获取目标类实例, " + e);
        }
        try {
            Object obj = clazz.newInstance();
            if (obj instanceof SQAppApi) {
                return (SQAppApi) obj;
            } else {
                Log.e("sqsdk", "无参数构造函数返回的实例类型异常: " + obj);
            }
        } catch (Throwable e) {
            Log.w("sqsdk", "无法通过newInstance获取目标类实例, " + e);
        }

        return null;
    }

    private String getDelegateClassName() {
        ClassLoader classLoader = getClass().getClassLoader();
        if (classLoader != null) {
            try {
                // 通过指定类的指定属性获取代理类配置
                Class<?> clazz = classLoader.loadClass("com.sqwan.msdk._Delegate");
                Field field = clazz.getDeclaredField("APP_NAME");
                field.setAccessible(true);
                Object obj = field.get(null);
                if (obj instanceof String) {
                    Log.d("sqsdk", "使用代理类: " + obj);
                    return (String) obj;
                }
            } catch (ClassNotFoundException e) {
                // 没有配置类, 忽略
            } catch (Throwable e) {
                Log.e("sqsdk", "获取代理类名失败, " + e);
            }
        } else {
            Log.e("sqsdk", "无法获取class loader");
        }

        // 默认使用普通的代理类
        // 注意: 这个类名是历史类名, 不能修改
        Log.d("sqsdk", "SQApplication使用默认代理类");
        return "com.sqwan.msdk.SQApplicationImpl";
    }
}
