package com.sqwan.msdk;

import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.Log;
import com.parameters.share.ShareMessage;
import com.sqwan.msdk.api.InitBean;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.api.SQPushTransmitMessageListener;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.api.SQSdkApi;
import com.sqwan.msdk.api.SQSdkInterface;
import com.sqwan.msdk.api.tool.IScreenshotListener;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class SQwanCore implements SQSdkApi {

    // 角色提交相关
    public static final String INFO_SERVERID = "serverId";
    public static final String INFO_SERVERNAME = "serverName";
    // 3.7.0新增开服时间
    public static final String INFO_SERVERTIME = "serverTime";
    public static final String INFO_ROLEID = "roleId";
    public static final String INFO_ROLENAME = "roleName";
    public static final String INFO_ROLELEVEL = "roleLevel";
    public static final String INFO_BALANCE = "balance";
    public static final String INFO_PARTYNAME = "partyName";
    public static final String INFO_VIPLEVEL = "vipLevel";
    public static final String INFO_ROLE_TIME_CREATE = "roleCTime";
    public static final String INFO_ROLE_TIME_LEVEL = "roleLevelMTime";

    // 用户信息
    public static final String LOGIN_KEY_TOKEN = "token";
    public static final String LOGIN_KEY_USERID = "userid";
    public static final String LOGIN_KEY_USERNAME = "username";
    public static final String LOGIN_KEY_GID = "gid";
    public static final String LOGIN_KEY_PID = "pid";
    // 公告弹窗s
    public static final String LOGIN_KEY_NURL = "nurl";
    // 激活码
    public static final String LOGIN_KEY_BETA = "beta";
    public static final String TOKEN = "token";

    /**
     * 日志等级：调试
     */
    public static final int LOG_LEVEL_DEBUG = 0;
    /**
     * 日志等级：信息
     */
    public static final int LOG_LEVEL_INFO = 1;
    /**
     * 日志等级：警告
     */
    public static final int LOG_LEVEL_WARN = 2;
    /**
     * 日志等级：错误
     */
    public static final int LOG_LEVEL_ERROR = 3;

    //初始化回调参数key
    private static final String IMEI = "imei";
    private static final String IS_UPDATE = "is_update";
    private static final String UPDATE_TYPE = "update_type";

    // 平台
    public static final int Platform_SQwan = 1;

    public static SQwanCore instance;
    public static final byte[] lock = new byte[0];

    /**
     * 获取SQwanCore单例
     */
    public static SQwanCore getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new SQwanCore();
                }
            }
        }
        return instance;
    }

    private SQwanCore() {
        SQSdkApi delegate = getDelegate();
        if (delegate == null) {
            throw new NullPointerException("Delegate is null");
        }

        mDelegate = delegate;
    }

    private SQSdkApi getDelegate() {
        // 注意反射的类需要和本类在同一个class loader内
        ClassLoader classLoader = getClass().getClassLoader();
        if (classLoader == null) {
            Log.e("sqsdk", "无法获取class loader");
            return null;
        }
        String className = getDelegateClassName();
        Class<?> clazz;
        try {
            clazz = classLoader.loadClass(className);
        } catch (Throwable e) {
            Log.e("sqsdk", "无法加载目标类: " + className + ", " + e);
            return null;
        }
        Method instanceMethod;
        try {
            instanceMethod = clazz.getMethod("getInstance");
            Object obj = instanceMethod.invoke(null);
            if (obj instanceof SQSdkApi) {
                return (SQSdkApi) obj;
            } else {
                Log.e("sqsdk", "getInstance返回的实例类型异常: " + obj);
            }
        } catch (Throwable e) {
            Log.w("sqsdk", "无法通过getInstance获取目标类实例, " + e);
        }
        try {
            Object obj = clazz.newInstance();
            if (obj instanceof SQSdkApi) {
                return (SQSdkApi) obj;
            } else {
                Log.e("sqsdk", "无参数构造函数返回的实例类型异常: " + obj);
            }
        } catch (Throwable e) {
            Log.w("sqsdk", "无法通过newInstance获取目标类实例, " + e);
        }

        return null;
    }

    private String getDelegateClassName() {
        ClassLoader classLoader = getClass().getClassLoader();
        if (classLoader != null) {
            try {
                // 通过指定类的指定属性获取代理类配置
                Class<?> clazz = classLoader.loadClass("com.sqwan.msdk._Delegate");
                Field field = clazz.getDeclaredField("CORE_NAME");
                field.setAccessible(true);
                Object obj = field.get(null);
                if (obj instanceof String) {
                    Log.d("sqsdk", "使用代理类: " + obj);
                    return (String) obj;
                }
            } catch (ClassNotFoundException e) {
                // 没有配置类, 忽略
            } catch (Throwable e) {
                Log.e("sqsdk", "获取代理类名失败, " + e);
            }
        } else {
            Log.e("sqsdk", "无法获取class loader");
        }

        // 默认使用普通的代理类
        // 注意: 这个类名是历史类名, 不能修改
        Log.d("sqsdk", "SQwanCore使用默认代理类");
        return "com.sqwan.msdk.SQwanCoreImpl";
    }

    private final SQSdkApi mDelegate;

    @Override
    public SQSdkInterface getPlatform(Context context, InitBean bean, SQResultListener initListener) {
        return mDelegate.getPlatform(context, bean, initListener);
    }

    @Override
    public void init(Context cxt, String appkey, SQResultListener listener) {
        mDelegate.init(cxt, appkey, listener);
    }

    @Override
    public void initCore(Context cxt, String appkey, SQResultListener listener) {
        mDelegate.initCore(cxt, appkey, listener);
    }

    @Override
    public SQAppConfig getAppConfig() {
        return mDelegate.getAppConfig();
    }

    @Override
    public void track(String event, String describe, HashMap<String, String> params) {
        mDelegate.track(event, describe, params);
    }

    @Override
    public void login(Context context, SQResultListener loginListener) {
        mDelegate.login(context, loginListener);
    }

    @Override
    public void pay(Context context, String doid, String dpt, String dcn, String dsid,
        String dsname, String dext, String drid, String drname, int drlevel,
        float dmoney, int dradio, SQResultListener payListener) {
        mDelegate.pay(context, doid, dpt, dcn, dsid, dsname, dext, drid, drname, drlevel, dmoney, dradio, payListener);
    }

    @Override
    public void setSwitchAccountListener(SQResultListener listener) {
        mDelegate.setSwitchAccountListener(listener);
    }

    @Override
    public void setBackToGameLoginListener(SQResultListener listener) {
        mDelegate.setBackToGameLoginListener(listener);
    }

    @Override
    public void showSQWebDialog(String url) {
        mDelegate.showSQWebDialog(url);
    }

    @Override
    public void showSQPersonalDialog(Context context) {
        mDelegate.showSQPersonalDialog(context);
    }

    @Override
    public void changeAccount(Context context, SQResultListener listener) {
        mDelegate.changeAccount(context, listener);
    }

    @Override
    public void shareToWX(Context context, String title, String description, String shareUrl, String thumbUrl,
        int resourceType, SQResultListener shareListener) {
        mDelegate.shareToWX(context, title, description, shareUrl, thumbUrl, resourceType, shareListener);
    }

    @Override
    public void share(String inviteCode, SQResultListener shareListener) {
        mDelegate.share(inviteCode, shareListener);
    }

    @Override
    public void share(String inviteCode, String img_id, SQResultListener shareListener) {
        mDelegate.share(inviteCode, img_id, shareListener);
    }

    @Override
    public void share(ShareMessage shareMessage, SQResultListener shareListener) {
        mDelegate.share(shareMessage, shareListener);
    }

    @Override
    public void log(int level, String msg) {
        mDelegate.log(level, msg);
    }

    @Override
    public void setSQPushTransmitMessageListener(SQPushTransmitMessageListener listener) {
        mDelegate.setSQPushTransmitMessageListener(listener);
    }

    @Override
    public void showAdReward(Context context, String params, SQResultListener listener) {
        mDelegate.showAdReward(context, params, listener);
    }

    @Override
    public void showExitDailog(Context context, SQResultListener listener) {
        mDelegate.showExitDailog(context, listener);
    }

    @Override
    public void logout(Context context, SQResultListener logoutListener) {
        mDelegate.logout(context, logoutListener);
    }

    @Override
    public void creatRoleInfo(HashMap<String, String> infos) {
        mDelegate.creatRoleInfo(infos);
    }

    @Override
    public void upgradeRoleInfo(HashMap<String, String> infos) {
        mDelegate.upgradeRoleInfo(infos);
    }

    @Override
    public void submitRoleInfo(HashMap<String, String> infos) {
        mDelegate.submitRoleInfo(infos);
    }

    @Override
    public void submitStatisticsInfo(String key, String values) {
        mDelegate.submitStatisticsInfo(key, values);
    }

    @Override
    public void creatRole(Context context, String serverId) {
        mDelegate.creatRole(context, serverId);
    }

    @Override
    public void onStart() {
        mDelegate.onStart();
    }

    @Override
    public void onRestart() {
        mDelegate.onRestart();
    }

    @Override
    public void onResume() {
        mDelegate.onResume();
    }

    @Override
    public void onPause() {
        mDelegate.onPause();
    }

    @Override
    public void onStop() {
        mDelegate.onStop();
    }

    @Override
    public void onDestroy() {
        mDelegate.onDestroy();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        mDelegate.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onNewIntent(Intent intent) {
        mDelegate.onNewIntent(intent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        mDelegate.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        mDelegate.onConfigurationChanged(newConfig);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        mDelegate.onWindowFocusChanged(hasFocus);
    }

    @Override
    public void reportMDev(String identify) {
        mDelegate.reportMDev(identify);
    }

    @Override
    public void setContext(Context cxt) {
        mDelegate.setContext(cxt);
    }

    @Override
    public void speechInit(Context context, SQResultListener listener) {
        mDelegate.speechInit(context, listener);
    }

    @Override
    public void setServerInfo(String url) {
        mDelegate.setServerInfo(url);
    }

    @Override
    public void poll() {
        mDelegate.poll();
    }

    @Override
    public void joinNationalRoom(String roomName, int role, int msTimeout) {
        mDelegate.joinNationalRoom(roomName, role, msTimeout);
    }

    @Override
    public void joinTeamRoom(String roomName, int msTimeout) {
        mDelegate.joinTeamRoom(roomName, msTimeout);
    }

    @Override
    public void quitRoom(String roomName, int msTimeout) {
        mDelegate.quitRoom(roomName, msTimeout);
    }

    @Override
    public void openMic() {
        mDelegate.openMic();
    }

    @Override
    public void closeMic() {
        mDelegate.closeMic();
    }

    @Override
    public void openSpeaker() {
        mDelegate.openSpeaker();
    }

    @Override
    public void closeSpeaker() {
        mDelegate.closeSpeaker();
    }

    @Override
    public void setMicLevel(int paramInt) {
        mDelegate.setMicLevel(paramInt);
    }

    @Override
    public int getMicLevel() {
        return mDelegate.getMicLevel();
    }

    @Override
    public void setSpeakerVolume(int paramInt) {
        mDelegate.setSpeakerVolume(paramInt);
    }

    @Override
    public int getSpeakerVolume() {
        return mDelegate.getSpeakerVolume();
    }

    @Override
    public boolean testMic() {
        return mDelegate.testMic();
    }

    @Override
    public void enableSpeakerOn(boolean paramBoolean) {
        mDelegate.enableSpeakerOn(paramBoolean);
    }

    @Override
    public void forbidMemberVoice(int paramInt, boolean paramBoolean) {
        mDelegate.forbidMemberVoice(paramInt, paramBoolean);
    }

    @Override
    public void onJoinRoomListener(Context context, SQResultListener listener) {
        mDelegate.onJoinRoomListener(context, listener);
    }

    @Override
    public void onQuitRoomListener(Context context, SQResultListener listener) {
        mDelegate.onQuitRoomListener(context, listener);
    }

    @Override
    public void onMemberVoiceListener(Context context, SQResultListener listener) {
        mDelegate.onMemberVoiceListener(context, listener);
    }

    @Override
    public void onStatusUpdateListener(Context context, SQResultListener listener) {
        mDelegate.onStatusUpdateListener(context, listener);
    }

    @Override
    public void performFeatureBBS() {
        mDelegate.performFeatureBBS();
    }

    @Override
    public void performFeatureVPlayer() {
        mDelegate.performFeatureVPlayer();
    }

    @Override
    public void performFeature(Context context, String type, Object data,
        SQResultListener listener) {
        mDelegate.performFeature(context, type, data, listener);
    }

    @Override
    public boolean isSupportPlugin() {
        return mDelegate.isSupportPlugin();
    }

    @Override
    public void setScreenshotListener(IScreenshotListener listener) {
        mDelegate.setScreenshotListener(listener);
    }

    @Override
    public void showUAgreement(Context context) {
        mDelegate.showUAgreement(context);
    }

    @Override
    public void printLog(int level, String tag, String content) {
        mDelegate.printLog(level, tag, content);
    }

    @Override
    public boolean isSupportLiveVideo() {
        return mDelegate.isSupportLiveVideo();
    }

    @Override
    public void joinLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        mDelegate.joinLiveshowRoom(data, listener);
    }

    @Override
    public void leaveLiveshowRoom(Map<String, String> data, SQResultListener listener) {
        mDelegate.leaveLiveshowRoom(data, listener);
    }

    @Override
    public void setLiveshowDestroyCallback(SQResultListener listener) {
        mDelegate.setLiveshowDestroyCallback(listener);
    }

    @Override
    public void setLiveshowVoiceChangeCallback(SQResultListener listener) {
        mDelegate.setLiveshowVoiceChangeCallback(listener);
    }

    @Override
    public void performLiveshowFeature(Map<String, String> data, SQResultListener listener) {
        mDelegate.performLiveshowFeature(data, listener);
    }

    @Override
    public boolean isSupportLiveRadio() {
        return mDelegate.isSupportLiveRadio();
    }

    @Override
    public void joinLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        mDelegate.joinLiveRadioRoom(data, listener);
    }

    @Override
    public void leaveLiveRadioRoom(Map<String, String> data, SQResultListener listener) {
        mDelegate.leaveLiveRadioRoom(data, listener);
    }

    @Override
    public void setLiveRadioDestroyCallback(SQResultListener listener) {
        mDelegate.setLiveRadioDestroyCallback(listener);
    }

    @Override
    public void setLiveRadioVoiceChangeCallback(SQResultListener listener) {
        mDelegate.setLiveRadioVoiceChangeCallback(listener);
    }

    @Override
    public void performLiveRadioFeature(Map<String, String> data, SQResultListener listener) {
        mDelegate.performLiveRadioFeature(data, listener);
    }

    @Override
    public void startActivity(Intent intent) {
        mDelegate.startActivity(intent);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        mDelegate.startActivityForResult(intent, requestCode);
    }

    @Override
    public Resources getResources(Resources hostResource) {
        return mDelegate.getResources(hostResource);
    }

    @Override
    public AssetManager getAssets(AssetManager hostAssetManager) {
        return mDelegate.getAssets(hostAssetManager);
    }

    @Override
    public ClassLoader getClassLoader(ClassLoader hostClassLoader) {
        return mDelegate.getClassLoader(hostClassLoader);
    }

    @Deprecated
    public static void sendLog(String logString) {
        getInstance().log(Log.WARN, logString);
    }

    @Deprecated
    public static void sendLogNoDebug(String logString) {
        getInstance().log(Log.DEBUG, logString);
    }

    @Deprecated
    public static void sendLog(String logString, int ecode) {
        getInstance().log(Log.ERROR, "CODE:" + ecode + " " + logString);
    }

    @Deprecated
    public static void sendLogBase4CP(String log) {
        getInstance().log(Log.WARN, "BaseSQwanCore-->" + log);
    }

    @Deprecated
    public static void sendLogPlat4CP(String log) {
        getInstance().log(Log.WARN, "Platform-->" + log);
    }

    @Deprecated
    public void setDebug(Boolean stat) {

    }

    @Deprecated
    public void showTestToast(String msg) {

    }
}
