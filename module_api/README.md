# 对外API类

## com.sqwan.msdk

### `SQActivity`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `SQApplication`

宿主中有, 插件apk中没有
插件apk中会被transform重命名的`SQApplicationImpl`, 宿主会通过反射调用`SQApplicationImpl`

#### 2024/05/17

`SQApplication`通过反射指定类获取代理类名创建代理类实例, 转发请求, 宿主和插件apk通过指定代理类名来切换逻辑
插件apk中直接使用`SQApplicationImpl`, 不再通过transform重命名

### `SQwanCore`

宿主中有, 插件apk中没有
插件apk中有被transform重命名的`SQwanCoreImpl`, 宿主会通过反射调用`SQwanCoreImpl`

#### 2024/05/17

`SQwanCore`通过反射指定类获取代理类名创建代理类实例, 转发请求, 宿主和插件apk通过指定代理类名来切换逻辑
插件apk中直接使用`SQwanCoreImpl`, 不再通过transform重命名

## com.sqwan.msdk.api

### `InitBean`

#### 2024/05/17

历史版本中只存在于插件apk中, 调整后, 宿主和插件apk中都存在, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `ISQAppConfig`

#### 2024/05/17

历史版本中只存在于插件apk中, 调整后, 宿主和插件apk中都存在, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类


### `PurchaseReportBean`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `RegisterReportBean`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `SQAppApi`

#### 2024/05/17

新增app api类, 用来解耦接口和实现, 方便`SQApplication`可以使用接口持有代理类实例

宿主中有, 插件apk中也有, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `SQAppConfig`

宿主中有, 插件apk中没有
插件apk中有被transform重命名的`SQAppConfigImpl`, 继承了`ISQAppConfig`, 宿主中的`SQAppConfig`没有继承`ISQAppConfig`

#### 2024/05/17

调整后, 宿主中的`SQAppConfig`也会继承`ISQAppConfig`
插件apk中不再通过transform重命名, 但是保留`SQwanCoreImpl`, 用于返回给宿主, 因为
对于旧宿主-新插件, 如果新插件中直接使用`SQAppConfig`, 此时旧宿主也存在该类, 会使用旧宿主的类, 但是旧宿主的`SQAppConfig`没有继承`ISQAppConfig`, 会导致报错, 所以要新插件apk中保留`SQwanCoreImpl`, 确保返回给旧宿主的类有继承继承`ISQAppConfig`
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类, 会继承`ISQAppConfig`

### `SQMediaReportInterface`

宿主中有, 插件apk中也有, 实际使用时总是会使用宿主中的

#### 2024/05/17

总是会使用宿主中的类, 所以移除插件apk中的`SQMediaReportInterface`

### `SQPushTransmitMessageListener`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `SQReportInterface`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `SQResultListener`

宿主中有, 插件apk中没有
会在transform过程中被删除


### `SQSdkApi`

#### 2024/05/17

新增对外api类, 包含所有对外方法, 用来解耦接口和实现, 方便使用`SQwanCore`可以使用接口持有代理类实例
这个api类没有修改/删除`SQwanCore`的方法, 理论上不会有调用问题

宿主中有, 插件apk中也有, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `SQSdkInterface`

宿主中有, 插件apk中没有
会在transform过程中被删除

## com.sqwan.msdk.api.tool

### `IExtensionInterface`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `ILiveRadio`

宿主中有, 插件apk中没有
插件apk中有同名类, 但是包路径不同, 插件apk的SQwanCore实际包含了这个接口的所有方法

### `ILiveshow`

宿主中有, 插件apk中没有
插件apk中代码中没有这个类, 但有同名类(包路径不同), 插件apk的SQwanCore实际包含了这个接口的所有方法

### `IOtherApi`

#### 2024/05/17

1. 包含了旧代码中对外, 但是不属于任何接口的方法
2. 包含了插件apk中有, 但是旧宿主中没有的接口方法, 所以会导致宿主的`SQwanCore`新增对外方法

宿主中有, 插件apk中也有, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `IPluginFunctionInterface`

#### 2024/05/17

和`BasePluginInterface`完全一样, 为了让`SQSdkApi`可以继承, 所以新增这个接口

宿主中有, 插件apk中也有, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `IScreenshotListener`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `IShareApi`

#### 2024/05/17

包含旧代码中对外, 但是不属于任何接口的分享相关方法
旧宿主中没有`shareToWX`方法, 因此会导致宿主的`SQwanCore`新增该方法, 同时因为这个方法涉及`Activity`, 需要额外处理, 所以宿主中的该方法暂时为空实现

宿主中有, 插件apk中也有, 因为
对于旧宿主-新插件, 旧宿主没有该类, 如果新插件apk也没有该类, 则会报错, 所以新插件apk也需要保留该类, 这种场景下, 会加载插件apk中的类
对于新宿主-新插件, 宿主和插件都有该类, 总会加载宿主中的类

### `Tool`

宿主中有, 插件apk中没有
会在transform过程中被删除

## com.parameters.performfeatureconfig

### `PerformFeature`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `PerformFeatureKey`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `PerformFeatureType`

宿主中有, 插件apk中没有
会在transform过程中被删除

## com.parameters.share

### `DefaultShareInfo`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `IShareInfo`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `ShareImageInfo`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `ShareMessage`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `SharePlatform`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `ShareTextInfo`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `ShareWebInfo`

宿主中有, 插件apk中没有
会在transform过程中被删除

### `WebShare`

宿主中有, 插件apk中没有
会在transform过程中被删除