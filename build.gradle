
buildscript {
    repositories {
        maven{ url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        maven {
            url uri('./gradlerepo')
        }
        google()
        jcenter()
        maven { url "http://www.azact.com/artifactory/sy_dev_libs/" }

    }

        dependencies {
            classpath 'com.android.tools.build:gradle:3.6.4'

            //Check for the latest version here: http://plugins.gradle.org/plugin/com.jfrog.artifactory
            classpath "org.jfrog.buildinfo:build-info-extractor-gradle:4.16.1"
            classpath fileTree(include: ['*.jar'], dir: '.')
            classpath files('PluginResTool.jar')
//            classpath project(':lib')
//            classpath (':lib')
//            classpath 'com.sq.gradle:buildSrc:1.0.6'
            //gradle插件
            classpath 'com.37sy.android:sqinject-gradle-plugin:1.0.0'
            classpath 'com.37sy.android:sqfix-gradle-plugin:1.0.6:all'
            classpath 'com.kezong:fat-aar:1.2.15'
            classpath "com.github.jengelman.gradle.plugins:shadow:4.0.2"
            classpath 'coder.siy:exclude-dependencies-plugin:1.0.0'
        }
}

plugins {
    id "org.sonarqube" version "3.0"
}

apply from: 'BuildSystem/dependencies.gradle'
apply from: 'BuildSystem/artifact_config.gradle'
apply from: 'common.gradle'
apply from: 'config.gradle'
apply from: 'multi.gradle'
apply from: "gradleconfig/path.gradle"
apply from: "sdkVersion.gradle"

task clean(type: Delete) {
    delete rootProject.buildDir
}



allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }

}
