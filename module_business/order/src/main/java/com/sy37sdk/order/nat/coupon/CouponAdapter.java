package com.sy37sdk.order.nat.coupon;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.order.nat.bean.Coupon;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class CouponAdapter extends BaseAdapter {

    private Context context;
    private List<Coupon> coupons;
    private SelectListener selectListener;
    @SuppressLint("SimpleDateFormat")
    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");


    public CouponAdapter(Context context) {
        this.context = context;
    }

    public CouponAdapter(Context context, List<Coupon> coupons) {
        this.context = context;
        this.coupons = coupons;
    }

    public void setDatas(List<Coupon> coupons) {
        this.coupons = coupons;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return coupons != null ? coupons.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return coupons.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context)
                    .inflate(SqResUtils.getIdByName("sysq_coupon_item", "layout", context), null);
            viewHolder = new ViewHolder();
            viewHolder.tvMoney = convertView.findViewById(SqResUtils.getIdByName("tv_money", "id", context));
            viewHolder.tvMinAmount = convertView.findViewById(SqResUtils.getIdByName("tv_min_amount", "id", context));
            viewHolder.tvEndTime = convertView.findViewById(SqResUtils.getIdByName("tv_coupon_etime", "id", context));
            viewHolder.ivSelect = convertView.findViewById(SqResUtils.getIdByName("iv_select_status", "id", context));
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        Coupon coupon = coupons.get(position);
        viewHolder.tvMoney.setText(String.valueOf((int) coupon.getAmount()));
        viewHolder.tvEndTime.setText(parseDate(coupon.getEtime()) + "到期");
        viewHolder.tvMinAmount.setText("满" + coupon.getMinAmount() + "元可用");
        viewHolder.ivSelect.setImageResource(coupon.isSelect() ? SqResUtils.getDrawableId(context, "sysq_ic_pay_selected") : SqResUtils.getDrawableId(context, "sysq_ic_pay_select"));
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (selectListener != null) {
                    selectListener.onSelect(position);
                }
            }
        });
        return convertView;
    }

    private static class ViewHolder {
        private TextView tvMoney;
        private TextView tvMinAmount;
        private TextView tvEndTime;
        private ImageView ivSelect;
    }

    private String parseDate(long etime) {
        // 如果小于24小时，返回今天
        if ((etime - System.currentTimeMillis()) < (24 * 60 * 60 * 1000)) {
            return "今日";
        }
        // 其余的格式化输出返回.
        return simpleDateFormat.format(new Date(etime));
    }

    public void setSelectListener(SelectListener selectListener) {
        this.selectListener = selectListener;
    }

    public interface SelectListener {
        void onSelect(int coupon);
    }
}
