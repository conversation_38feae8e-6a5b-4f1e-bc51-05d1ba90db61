package com.sy37sdk.order.nat.trade;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.web.SY37web;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.order.SqR;
import com.sy37sdk.order.nat.bean.Coupon;
import com.sy37sdk.order.nat.BasePayView;
import com.sy37sdk.order.nat.INativePayDialog;
import com.sy37sdk.order.nat.PayBundleKey;
import com.sy37sdk.order.view.PayConfirmDialog;
import com.sy37sdk.order.nat.PayPageSwitcher;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品购买页面
 */
public class TradeView extends BasePayView implements ITradeView {

    private ITradePresenter tradePresenter;

    private Context mContext;

    private View couponLayout;

    private View allPayWay;

    private ListView lvPayWay;

    private TextView tvProductName;

    private TextView tvCoupon;

    private TextView tvPrice, tvOriginalPrice;

    private TextView tvPay;

    private PayWayAdapter payWayAdapter;

    private int selectPayWayPos;

    private boolean isWxPayClick;

    public TradeView(Context context, INativePayDialog nativePayDialog) {
        super(context);
        this.mContext = context;
        this.nativePayDialog = nativePayDialog;
    }

    @Override
    public void setPresenter(ITradePresenter presenter) {
        this.tradePresenter = presenter;
    }


    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        LogUtil.i("onSwitched TradeView " + fromIndex + "   " + toIndex);
        if (bundle == null) {
            return;
        }
        boolean useCoupon = bundle.getBoolean(PayBundleKey.KEY_USE_COUPON);
        if (useCoupon) {
            Coupon coupon = bundle.getParcelable(PayBundleKey.KEY_SELECT_COUPON);
            tradePresenter.selectCoupon(coupon);
        } else {
            tradePresenter.selectNoneCoupon();
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        tradePresenter.initData();
    }

    @Override
    public String getTitle() {
        return SqResUtils.getStringByName(mContext, "sysq_pay_cashier");
    }

    @Override
    public void initView() {
        tvPay = getViewByName(rootView, "tv_pay_sure");
        couponLayout = getViewByName(rootView, "coupon_layout");
        allPayWay = getViewByName(rootView, "pay_way_more_layout");
        tvProductName = getViewByName(rootView, "product_name");
        tvPrice = getViewByName(rootView, "tv_price");
        tvOriginalPrice = getViewByName(rootView, "tv_original_price");
        tvOriginalPrice.setPaintFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        tvCoupon = getViewByName(rootView, "tv_cupon");
        lvPayWay = getViewByName(rootView, "lv_pay_way");
        lvPayWay.setCacheColorHint(0xffffffff);
        lvPayWay.setDivider(null);
        lvPayWay.setFocusable(false);
        lvPayWay.setVerticalScrollBarEnabled(false);
        payWayAdapter = new PayWayAdapter(mContext);
        payWayAdapter.setIsShowDivider(!isLandscape());
        lvPayWay.setAdapter(payWayAdapter);
        setLeftVisible(!ConfigManager.getInstance(getContext()).isSplashSDK());
    }


    @Override
    public void initEvent() {
        couponLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (tradePresenter.getCoupons() != null && tradePresenter.getCoupons().size() > 0) {
                    Bundle bundle = new Bundle();
                    bundle.putParcelableArrayList(PayBundleKey.KEY_COUPON, tradePresenter.getCoupons());
                    nativePayDialog.onSwitch(PayPageSwitcher.PAGE_COUPON, bundle);
                }
            }
        });
        allPayWay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                tradePresenter.showAllPayWay();
            }
        });
        payWayAdapter.setSelectListener(new PayWayAdapter.SelectListener() {
            @Override
            public void select(int pos) {
                refreshPayWays(pos);
            }
        });
        tvPay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                String pway = getSelectPayWay() != null ? getSelectPayWay().getWay() : NativePayWay.PAY_WAY_WECHAT;
                if (pway.equals(NativePayWay.PAY_WAY_WECHAT) && !isWxPayClick) {
                    isWxPayClick = true;
                }
                tradePresenter.pay(pway);
            }
        });
    }

    public NativePayWay getSelectPayWay() {
        List<NativePayWay> datas = payWayAdapter.getDatas();
        if (datas != null && datas.size() > 0) {
            NativePayWay nativePayWay = datas.get(selectPayWayPos);
            return nativePayWay;
        }
        return null;
    }


    private void refreshPayWays(int pos) {
        List<NativePayWay> datas = payWayAdapter.getDatas();
        if (datas != null && datas.size() > 0) {
            for (int i = 0; i < datas.size(); i++) {
                NativePayWay nativePayWay = datas.get(i);
                nativePayWay.setSelected(i == pos);
            }
            payWayAdapter.setDatas(datas);
            selectPayWayPos = pos;
        }
    }

    @Override
    public String getLayoutResName() {
        return isLandscape() ? "sysq_pay_dialog_landscape" : "sysq_pay_dialog_portrait";
    }

    @Override
    public void showProductName(String productName) {
        if (tvProductName != null) {
            tvProductName.setText(productName);
        }
    }

    @Override
    public void showProductPrice(String price) {
        if (tvPrice != null) {
            tvPrice.setText("￥" + price);
        }
        if (tvPay != null) {
            tvPay.setText("确认支付 ￥" + price);
        }
    }


    @Override
    public void showOriginalPrice(String originalPrice) {
        if (tvOriginalPrice != null) {
            tvOriginalPrice.setText("￥" + originalPrice);
            tvOriginalPrice.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showOriginalNonePrice() {
        if (tvOriginalPrice != null) {
            tvOriginalPrice.setVisibility(View.GONE);
        }
    }

    @Override
    public void showCoupon(String coupon) {
        if (tvCoupon != null) {
            tvCoupon.setText("-￥" + coupon);
            tvCoupon.setTextColor(Color.parseColor("#F54B4B"));
        }
    }

    @Override
    public void showCouponNone() {
        if (tvCoupon != null) {
            tvCoupon.setText("不使用代金券");
            tvCoupon.setTextColor(Color.parseColor("#999999"));
        }
    }

    @Override
    public void showPayWay(List<NativePayWay> nativePayWays, boolean more) {
        if (nativePayWays != null && nativePayWays.size() > 0) {
            allPayWay.setVisibility(more ? VISIBLE : GONE);
            payWayAdapter.setDatas(nativePayWays);
            refreshPayWays(0);
        }
    }

    @Override
    public void showAllPayWay(ArrayList<NativePayWay> nativePayWays) {
        NativePayWay lastPayWay = payWayAdapter.getDatas().get(selectPayWayPos);
        if (lastPayWay != null) {
            int index = nativePayWays.indexOf(lastPayWay);
            if (index != -1) {
                //找到已经上次选中的方式，并更新
                selectPayWayPos = index;
            }
        }
        allPayWay.setVisibility(View.GONE);
        payWayAdapter.setDatas(nativePayWays);

    }

    @Override
    public void selectPayWay(int position) {
        refreshPayWays(position);
        lvPayWay.setSelection(position);
        lvPayWay.smoothScrollToPosition(position);
    }


    @Override
    public void showCoupons() {
        couponLayout.setVisibility(VISIBLE);
    }


    @Override
    public void onBackPressed() {
        LogUtil.i("TradeView onBackPressed ");
        new PayConfirmDialog.Builder(mContext).setTitle(SqResUtils.getStringByName(mContext, "sysq_pay_cancel_tip"))
                .setCancelable(false)
                .setNegativeButton(SqResUtils.getStringByName(mContext, "sysq_pay_cancel_sure"), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (nativePayDialog != null) {
                            nativePayDialog.closeAccountDialog(true);
                        }
                    }
                }).setPositiveButton(SqResUtils.getStringByName(mContext, "sysq_pay_cancel_continue"), null)
                .show();
    }


    @Override
    protected void leftViewClicked() {
        showPayConfirmDialog(null, null, null);
    }


    @Override
    public void startLabor() {
        if (nativePayDialog != null) {
            Bundle bundle = new Bundle();
            bundle.putString(PayBundleKey.KEY_LABOR_CONTENT, getSelectPayWay() != null ? getSelectPayWay().getPWayTip() : "");
            nativePayDialog.onSwitch(PayPageSwitcher.PAGE_LOBAR, bundle);
        }
    }

    @Override
    public void setWxPay(boolean toWxPay) {
        isWxPayClick = toWxPay;
    }

    @Override
    public void onPayViewWindowFocusChanged(boolean hasFocus) {
        if (hasFocus && isWxPayClick) {
            tradePresenter.checkWxPay();
        }
    }

    @Override
    public void paySuccess() {
        showToast("支付成功！");
        if (isWxPayClick) {
            isWxPayClick = false;
        }
        tradePresenter.paySuccess();
        if (nativePayDialog != null) {
            nativePayDialog.closeAccountDialog(false);
        }
    }

    @Override
    public void onFailure(int code, String msg) {
        if (isWxPayClick) {
            isWxPayClick = false;
        }
        tradePresenter.onFailure(code, msg);
        View headFailureView = LayoutInflater.from(this.mContext).inflate(SqResUtils.getLayoutId(this.mContext, SqR.layout.sysq_pay_dialog_head_failure), null);
        OnClickListener onClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (nativePayDialog != null) {
                    nativePayDialog.closeAccountDialog(false);
                }
            }
        };
        boolean isSplashSDK = ConfigManager.getInstance(getContext()).isSplashSDK();
        if (isSplashSDK) {
            //简版失败则直接关闭弹窗
            ToastUtil.showToast(msg);
            if (nativePayDialog != null) {
                nativePayDialog.closeAccountDialog(false);
            }
        } else {
            //正版弹支付失败提示框
            showPayConfirmDialog(headFailureView, onClickListener, onClickListener);
        }

    }

    /**
     * 显示支付的确认信息
     *
     * @param headView              顶部的view,用于显示支付识别的。
     * @param negativeClickListener 取消后的操作
     * @param positiveClickListener 复制公众号的后续操作
     */
    private void showPayConfirmDialog(final View headView, final View.OnClickListener negativeClickListener, final View.OnClickListener positiveClickListener) {
        boolean isSplashSDK = ConfigManager.getInstance(getContext()).isSplashSDK();
        final String platformName = isSplashSDK ? "玩心俱乐部" : "37手游俱乐部";
        String content = "支付问题联系客服\n" + "关注公众号【" + platformName + "】";
        new PayConfirmDialog.Builder(mContext)
                .setHeadView(headView)
                .setTitle(content)
                .setCancelable(false)
                .setNegativeButton("取消", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (null != negativeClickListener) {
                            negativeClickListener.onClick(v);
                        }
                    }
                })
                .setPositiveButton("复制公众号", new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        SY37web.copyString2System(mContext, platformName, "复制成功");
                        if (null != positiveClickListener) {
                            positiveClickListener.onClick(v);
                        }
                    }
                })
                .show();
    }
}
