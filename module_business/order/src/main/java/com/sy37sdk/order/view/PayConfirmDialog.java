package com.sy37sdk.order.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;

/**
 * <AUTHOR>
 * @date 2020-05-22
 */
public class PayConfirmDialog extends BaseDialog {

    private View headView;
    private String title;
    private String mNegativeButtonText;
    private String mPositiveButtonText;
    private View.OnClickListener mNegativeButtonListener;
    private View.OnClickListener mPositiveButtonListener;

    public PayConfirmDialog(Context context) {
        super(context);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(getContext(), "sysq_pay_tip_dialog"));
        FrameLayout flHeadContainer = findViewById(getIdByName("fl_head_container", "id"));
        TextView tvContent = findViewById(getIdByName("tv_content", "id"));
        TextView tvEnsure = findViewById(getIdByName("tv_confirm", "id"));
        TextView tvCancel = findViewById(getIdByName("tv_cancel", "id"));
        if (null != headView) {
            flHeadContainer.removeAllViews();
            flHeadContainer.addView(headView, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        }
        tvContent.setText(title);
        tvEnsure.setText(mPositiveButtonText);
        tvEnsure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPositiveButtonListener != null) {
                    mPositiveButtonListener.onClick(v);
                }
                dismiss();
            }
        });
        tvCancel.setText(mNegativeButtonText);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNegativeButtonListener != null) {
                    mNegativeButtonListener.onClick(v);
                }
                dismiss();
            }
        });
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setNegativeButtonText(String negativeButtonText) {
        this.mNegativeButtonText = negativeButtonText;
    }

    public void setPositiveButtonListener(View.OnClickListener positiveButtonListener) {
        this.mPositiveButtonListener = positiveButtonListener;
    }

    public void setNegativeButtonListener(View.OnClickListener negativeButtonListener) {
        this.mNegativeButtonListener = negativeButtonListener;
    }

    public void setPositiveButtonText(String positiveButtonText) {
        this.mPositiveButtonText = positiveButtonText;
    }

    public void setHeadView(View headView) {
        this.headView = headView;
    }

    public static final class Builder {

        private Context mContext;
        private View headView;
        private String title;
        private String mNegativeButtonText;
        private String mPositiveButtonText;
        private View.OnClickListener mNegativeButtonListener;
        private View.OnClickListener mPositiveButtonListener;
        private boolean mCancelable;

        public Builder(Context context) {
            this.mContext = context;
        }

        public Builder setHeadView(View headView) {
            this.headView = headView;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setPositiveButton(String text, final View.OnClickListener listener) {
            this.mPositiveButtonText = text;
            this.mPositiveButtonListener = listener;
            return this;
        }

        public Builder setNegativeButton(String text, final View.OnClickListener listener) {
            this.mNegativeButtonText = text;
            this.mNegativeButtonListener = listener;
            return this;
        }

        public Builder setCancelable(boolean cancelable) {
            this.mCancelable = cancelable;
            return this;
        }

        public void show() {
            final PayConfirmDialog dialog = new PayConfirmDialog(mContext);
            dialog.setTitle(title);
            dialog.setNegativeButtonText(mNegativeButtonText);
            dialog.setNegativeButtonListener(mNegativeButtonListener);
            dialog.setPositiveButtonListener(mPositiveButtonListener);
            dialog.setPositiveButtonText(mPositiveButtonText);
            dialog.setCancelable(mCancelable);
            dialog.setHeadView(headView);
            dialog.show();
        }
    }
}
