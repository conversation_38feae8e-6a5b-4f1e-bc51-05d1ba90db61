package com.sy37sdk.order.nat.trade;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/28
 * 钱包
 */
public class Wallet {

    /**
     * 钱包余额
     */
    private int ub;

    /**
     * 暂未用到的字段
     */
    private int up;


    public int getUb() {
        return ub;
    }

    public int getUp() {
        return up;
    }

    @Override
    public String toString() {
        return "ub: " + ub + ", up: " + up;
    }

    public static Wallet fromJson(String json) throws JSONException {
        Wallet wallet = new Wallet();
        JSONObject walletJson = new JSONObject(json);
        wallet.ub = walletJson.optInt("ub");
        wallet.up = walletJson.optInt("up");
        return wallet;
    }
}
