package com.sy37sdk.order.nat.trade;

import com.sy37sdk.order.nat.IPayView;
import java.util.ArrayList;
import java.util.List;

public interface ITradeView extends IPayView<ITradePresenter> {

    void showProductName(String productName);

    void showProductPrice(String price);

    void showOriginalPrice(String originalPrice);

    void showOriginalNonePrice();

    void showCoupon(String coupon);

    void showCouponNone();

    void showPayWay(List<NativePayWay> nativePayWays, boolean more);

    void showAllPayWay(ArrayList<NativePayWay> nativePayWays);

    void selectPayWay(int position);

    void showCoupons();

    void startLabor();

    void setWxPay(boolean toWxPay);

}
