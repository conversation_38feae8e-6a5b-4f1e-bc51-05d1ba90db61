package com.sy37sdk.order.nat.trade;


import android.support.annotation.Nullable;

public class NativePayWay {
    /**
     * 支付宝
     */
    public static final String PAY_WAY_ALIPAY = "alipay";

    /**
     * 微信支付
     */
    public static final String PAY_WAY_WECHAT = "wxpay";

    /**
     * 花呗支付
     */
    public static final String PAY_WAY_HUABEI = "hbpay";

    /**
     * 钱包支付
     */
    public static final String PAY_WAY_WALLET = "wtpay";

    /**
     * 人工充值
     */
    public static final String PAY_WAY_LABOR = "labor";


    /**
     * 支付方式的key
     */
    public static final String PWAY_KEY_ALIPAY = "alipay";
    public static final String PWAY_KEY_WECHAT = "wechat";
    public static final String PWAY_KEY_WALLET = "wallet";
    public static final String PWAY_KEY_HUABEI = "huabei";

    /**
     * 开启
     */
    private static final int OPEN = 1;

    /**
     * 关闭
     */
    private static final int CLOSE = 0;

    //支付方式key
    private String key;
    //支付方式
    private String wayChinese;
    //支付方式
    private String way;
    //是否开启
    private String open;
    //促销活动提示
    private String promote;
    //支付的图标
    private String resId;
    //是否被选中
    private boolean isSelected;
    //支付的说明
    private String pWayTip;
    //额外信息(比如：钱包的余额)
    private String extra;

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getResId() {
        return resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }

    public String getPWayTip() {
        return pWayTip;
    }

    public void setPWayTip(String pWayTip) {
        this.pWayTip = pWayTip;
    }

    public String getPromote() {
        return promote;
    }

    public void setPromote(String promote) {
        this.promote = promote;
    }

    public String getWayChinese() {
        return wayChinese;
    }

    public void setWayChinese(String wayChinese) {
        this.wayChinese = wayChinese;
    }

    public String getWay() {
        return way;
    }

    public void setWay(String way) {
        this.way = way;
    }

    public String getOpen() {
        return open;
    }

    public void setOpen(String open) {
        this.open = open;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    @Override
    public String toString() {
        return "NativePayWay{" +
                "way='" + wayChinese + '\'' +
                ", open='" + open + '\'' +
                '}';
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj instanceof NativePayWay) {
            return this.key.equals(((NativePayWay) obj).getKey());
        } else {
            return super.equals(obj);
        }
    }
}
