package com.sy37sdk.order.nat.pay;

import org.json.JSONObject;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-06-11
 */
public class Result {

    /**
     * 订单支付成功
     */
    public static final String STATUS_CODE_SUCCESS = "9000";
    /**
     * 用户中途取消
     */
    public static final String STATUS_CODE_CANCEL = "6001";

    /**
     * 自定义，用来表示H5支付返回url不为空的情况
     */
    public static final int STATUS_CODE_URL = 8989;


    private String resultStatus;
    private String result;
    private String memo;
    private String returnUrl;

    public Result(Object data) {
        if (data instanceof Map) {
            JSONObject jsonObject = new JSONObject((Map) data);
            resultStatus = jsonObject.optString("resultStatus");
            result = jsonObject.optString("result");
            memo = jsonObject.optString("memo");
            returnUrl = jsonObject.optString("returnUrl");
        }
    }

    public String getResultStatus() {
        return resultStatus;
    }

    public String getMemo() {
        return memo;
    }

    public String getResult() {
        return result;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

}
