package com.sy37sdk.order.nat.bean;

import android.os.Parcel;
import android.os.Parcelable;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/3/1
 */
public class Order implements Parcelable {

    private String moid;

    private float money;

    private String dsid;

    private String dpt;

    /**
     * CP订单ID
     */
    private String doid;

    /**
     * CP扩展回调参数
     */
    private String dext;

    /**
     * CP角色ID
     */
    private String drid;

    /**
     * CP角色名
     */
    private String drname;

    /**
     * cp货币名称
     */
    private String dcn;

    private int drlevel;

    /**
     * CP兑换比率
     */
    private int dradio;

    /**
     * type == 1 洋葱头支付
     * type == 0 其它支付
     */
    private int type;

    //实付金额
    private String payAmount = "";
    //是否使用了代金券 1是 0否
    private String isVouchers = "0";
    //代金券id
    private String vouchersId = "";
    //支付渠道
    private String payChannel = "1";
    //支付方式
    private String payMethod = "1";

    public Order() {
    }

    protected Order(Parcel in) {
        moid = in.readString();
        money = in.readFloat();
        dsid = in.readString();
        dpt = in.readString();
        doid = in.readString();
        dext = in.readString();
        drid = in.readString();
        drname = in.readString();
        dcn = in.readString();
        drlevel = in.readInt();
        dradio = in.readInt();
        type = in.readInt();
        payAmount = in.readString();
        isVouchers = in.readString();
        vouchersId = in.readString();
        payChannel = in.readString();
        payMethod = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(moid);
        dest.writeFloat(money);
        dest.writeString(dsid);
        dest.writeString(dpt);
        dest.writeString(doid);
        dest.writeString(dext);
        dest.writeString(drid);
        dest.writeString(drname);
        dest.writeString(dcn);
        dest.writeInt(drlevel);
        dest.writeInt(dradio);
        dest.writeInt(type);
        dest.writeString(payAmount);
        dest.writeString(isVouchers);
        dest.writeString(vouchersId);
        dest.writeString(payChannel);
        dest.writeString(payMethod);

    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Order> CREATOR = new Creator<Order>() {
        @Override
        public Order createFromParcel(Parcel in) {
            return new Order(in);
        }

        @Override
        public Order[] newArray(int size) {
            return new Order[size];
        }
    };

    public int getDrlevel() {
        return drlevel;
    }

    public void setDrlevel(int drlevel) {
        this.drlevel = drlevel;
    }

    public int getDradio() {
        return dradio;
    }

    public void setDradio(int dradio) {
        this.dradio = dradio;
    }

    public String getDcn() {
        return dcn;
    }

    public void setDcn(String dcn) {
        this.dcn = dcn;
    }

    public String getMoid() {
        return moid;
    }

    public void setMoid(String moid) {
        this.moid = moid;
    }

    public float getMoney() {
        return money;
    }

    public void setMoney(float money) {
        this.money = money;
    }

    public String getDsid() {
        return dsid;
    }

    public void setDsid(String dsid) {
        this.dsid = dsid;
    }

    public String getDpt() {
        return dpt;
    }

    public void setDpt(String dpt) {
        this.dpt = dpt;
    }


    public String getDoid() {
        return doid;
    }

    public void setDoid(String doid) {
        this.doid = doid;
    }

    public String getDext() {
        return dext;
    }

    public void setDext(String dext) {
        this.dext = dext;
    }

    public String getDrid() {
        return drid;
    }

    public void setDrid(String drid) {
        this.drid = drid;
    }

    public String getDrname() {
        return drname;
    }

    public void setDrname(String drname) {
        this.drname = drname;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    @Override
    public String toString() {
        return "Order{" +
                "moid='" + moid + '\'' +
                ", money=" + money +
                ", dsid='" + dsid + '\'' +
                ", dpt='" + dpt + '\'' +
                ", doid='" + doid + '\'' +
                ", dext='" + dext + '\'' +
                ", drid='" + drid + '\'' +
                ", drname='" + drname + '\'' +
                ", dcn='" + dcn + '\'' +
                ", drlevel=" + drlevel +
                ", dradio=" + dradio +
                ", type=" + type +
                ", payAmount='" + payAmount + '\'' +
                ", isVouchers='" + isVouchers + '\'' +
                ", vouchersId='" + vouchersId + '\'' +
                ", payChannel='" + payChannel + '\'' +
                ", payMethod='" + payMethod + '\'' +
                '}';
    }

    public String getIsVouchers() {
        return isVouchers;
    }

    public void setIsVouchers(String isVouchers) {
        this.isVouchers = isVouchers;
    }

    public String getVouchersId() {
        return vouchersId;
    }

    public void setVouchersId(String vouchersId) {
        this.vouchersId = vouchersId;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public static String objectToJsonStr(Order order) {
        String str = "";
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("moid", order.getMoid());
            jsonObject.put("dcn", order.getDcn());
            jsonObject.put("dext", order.getDext());
            jsonObject.put("doid", order.getDoid());
            jsonObject.put("dpt", order.getDpt());
            jsonObject.put("dradio", order.getDradio());
            jsonObject.put("drid", order.getDrid());
            jsonObject.put("drlevel", order.getDrlevel());
            jsonObject.put("drname", order.getDrname());
            jsonObject.put("dsid", order.getDsid());
            jsonObject.put("money", order.getMoney());
            jsonObject.put("type", order.getType());
            jsonObject.put("payAmount", order.getPayAmount());
            jsonObject.put("isVouchers", order.getIsVouchers());
            jsonObject.put("vouchersId", order.getVouchersId());
            jsonObject.put("payMethod", order.getPayMethod());
            jsonObject.put("payChannel", order.getPayChannel());
            str = jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    public static Order jsonToObject(String json) {
        Order order = new Order();
        try {
            JSONObject jsonObject = new JSONObject(json);
            order.setMoid(jsonObject.optString("moid"));
            order.setDcn(jsonObject.optString("dcn"));
            order.setDext(jsonObject.optString("dext"));
            order.setDoid(jsonObject.optString("doid"));
            order.setDpt(jsonObject.optString("dpt"));
            order.setDradio(jsonObject.optInt("dradio"));
            order.setDrid(jsonObject.optString("drid"));
            order.setDrlevel(jsonObject.optInt("drlevel"));
            order.setDsid(jsonObject.optString("dsid"));
            order.setDrname(jsonObject.optString("drname"));
            order.setMoney((float) jsonObject.optDouble("money"));
            order.setType(jsonObject.optInt("type"));
            order.setPayAmount(jsonObject.optString("payAmount"));
            order.setIsVouchers(jsonObject.optString("isVouchers"));
            order.setVouchersId(jsonObject.optString("vouchersId"));
            order.setPayMethod(jsonObject.optString("payMethod"));
            order.setPayChannel(jsonObject.optString("payChannel"));

        } catch (Exception e) {
            e.printStackTrace();
        }
        return order;
    }
}
