package com.sy37sdk.order.nat;

import android.content.Context;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.OrderRequestManager;
import com.sy37sdk.order.OrderUrl;
import com.sy37sdk.order.PayVersionUtil;
import com.sy37sdk.order.nat.bean.Order;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public class NatRequestManager {

    private final Context mContext;

    public NatRequestManager(Context context) {
        mContext = context;
    }

    public void getAvailablePways(String moid, final SqHttpCallback<String> callback) {
        SqRequest.of(OrderUrl.AVAILABLE_PAY_WAYS)
            .params(OrderRequestManager.generateCommonParamsOfPay(mContext, moid))
            .addParam("moid", moid)
            .post(callback, String.class);
    }

    /**
     * 请求钱包余额
     */
    public void getWalletBalance(String moid, SqHttpCallback<String> callback) {
        SqRequest.of(OrderUrl.WALLET_BALANCE)
            .params(OrderRequestManager.generateCommonParamsOfPay(mContext, moid))
            .addParam("moid", moid)
            .post(callback, String.class);
    }

    /**
     * 代金券
     */
    public void getCoupon(String moid, float money, String dsid, int useStates, SqHttpCallback<String> callback) {
        SqRequest.of(OrderUrl.GET_COUPON)
            .params(OrderRequestManager.generateCommonParamsOfPay(mContext, moid))
            .addParam("usestatus", useStates + "")
            .addParam("money", money + "")
            .addParam("moid", moid)
            .addParam("dsid", dsid)
            .post(callback, String.class);
    }

    public void pay(float money, String pway, String moid, boolean isHuabei, String couponCode,
        SqHttpCallback<String> callback) {
        SqRequest request = SqRequest.of(OrderUrl.COMMON_PAY)
            .params(OrderRequestManager.generateCommonParamsOfPay(mContext, moid))
            .addParam("money", money + "")
            .addParam("pway", pway)
            .addParam("moid", moid)
            .addParam("payVersion", PayVersionUtil.PAY_VERSION);
        if (isHuabei) {
            // 如果是花呗，支付方式为alipay, 增加huabei=1参数
            request.addParam("huabei", "1");
        }
        if (!TextUtils.isEmpty(couponCode)) {
            request.addParam("couponCode", couponCode);
        }
        request.post(callback, String.class);
    }

    public void wtPay(@NonNull Order order, SqHttpCallback<Void> callback) {
        String moid = order.getMoid();
        SqRequest.of(OrderUrl.WALLET_PAY)
            .params(OrderRequestManager.generateCommonParamsOfPay(mContext, moid))
            .addParam("money", order.getMoney() + "")
            .addParam("moid", moid)
            .addParam("dsid", order.getDsid())
            .addParam("pway", PayWay.WALLET.getHttpPayWay())
            .addParam("doid", order.getDoid())
            .addParam("dext", order.getDext())
            .addParam("drid", order.getDrid())
            .addParam("drname", order.getDrname())
            .addParam("drlevel", order.getDrlevel() + "")
            .post(callback, Void.class);
    }
}
