package com.sy37sdk.order.nat;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import com.sqwan.common.mvp.IPageSwitcher;
import com.sy37sdk.order.PayConstants;


public abstract class BaseNativePayPresenter<K extends IBasePayView> extends BasePayPresenter<K> {

    private IPageSwitcher pageSwitcher;

    protected INativePayListener mPayListener;

    public BaseNativePayPresenter(Context context, K view) {
        super(context, view);
    }

    public void setPageSwitcher(IPageSwitcher pageSwitcher) {
        this.pageSwitcher = pageSwitcher;
    }

    public void setPayListener(INativePayListener payListener) {
        mPayListener = payListener;
    }

    public void onSwitch(int page) {
        if (pageSwitcher != null) {
            pageSwitcher.onSwitch(page);
        }
    }

    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        ((IPageSwitchView) mView).onSwitched(fromIndex, toIndex, bundle);
    }

    public void onBackPressed() {
        ((IPageSwitchView) mView).onBackPressed();
    }

    public void onPayViewWindowFocusChanged(boolean hasFocus) {
        ((IPageSwitchView) mView).onPayViewWindowFocusChanged(hasFocus);
    }

    @Override
    public String payChannel() {
        return PayConstants.PAY_CHANNEL_NATIVE;
    }

    public abstract View getView();

}
