package com.sy37sdk.order.nat;

import android.webkit.WebView;
import com.sy37sdk.order.nat.bean.Order;
import com.sqwan.common.mvp.IPresenter;

/**
 * <AUTHOR>
 * @date 2020-04-21
 */
public interface IBasePayPresenter extends IPresenter {

    void setOrder(Order order);

    /**
     * 上报支付结果
     */
    void reportPay(boolean success);


    /**
     * 设置微信订单id
     * @param uuid
     */
    void setWxOrderId(String uuid);

    /**
     * 设置支付宝订单id
     * @param uuid
     */
    void setAliOrderId(String uuid);

    /**
     * 设置抖音订单 id
     */
    void setDouYinOrderId(String douYinOrderId);

    /**
     * 跳转微信支付
     */
    void toWxPay(String url);

    /**
     * 跳转支付宝支付
     */
    void toAlipay(String orderInfo, boolean isNative);

    /**
     * 跳转到云闪付支付
     *
     * @param tn            云闪付订单号
     */
    void toUnionPay(String tn);

    /**
     * 跳转到抖音支付
     *
     * @param url           调整的 url
     * @param source        支付的来源
     */
    void toDouYinWebPay(WebView webView, String url, String source);

    /**
     * 通过 scheme 协议跳转到抖音支付页
     */
    void toDouYinPaySchemePage(String schemeUrl);

    /**
     * url 是否为抖音支付 scheme 协议的前缀
     */
    boolean isMatchDouYinPaySchemePrefix(String url);

    /**
     * 查询微信支付结果
     */
    void checkWxPay();

    /**
     * 查询抖音支付结果
     */
    void checkDouYinWebPay();

    /**
     * 设置当前支付方式
     */
    void setCurrentPayMethod(String currentPayMethod);
}
