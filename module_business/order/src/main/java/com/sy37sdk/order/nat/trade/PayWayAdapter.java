package com.sy37sdk.order.nat.trade;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;

import java.util.List;

public class PayWayAdapter extends BaseAdapter {
    private Context context;
    private List<NativePayWay> nativePayWays;
    private SelectListener selectListener;
    private boolean isShowDivider = true;

    public PayWayAdapter(Context context) {
        this.context = context;
    }

    public PayWayAdapter(Context context, List<NativePayWay> nativePayWays) {
        this.context = context;
        this.nativePayWays = nativePayWays;
    }

    public void setDatas(List<NativePayWay> nativePayWays) {
        this.nativePayWays = nativePayWays;
        notifyDataSetChanged();
    }

    public List<NativePayWay> getDatas() {
        return this.nativePayWays;
    }

    @Override
    public int getCount() {
        return nativePayWays != null ? nativePayWays.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return nativePayWays.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            convertView = LayoutInflater.from(context)
                    .inflate(SqResUtils.getIdByName("sysq_item_pay_way", "layout", context), null);
            viewHolder = new ViewHolder();
            viewHolder.tvPayWay = convertView.findViewById(SqResUtils.getIdByName("tv_pay_way", "id", context));
            viewHolder.tvPayPromote = convertView.findViewById(SqResUtils.getIdByName("tv_pay_promote", "id", context));
            viewHolder.ivPayWay = convertView.findViewById(SqResUtils.getIdByName("iv_pay_way", "id", context));
            viewHolder.ivSelectStatus = convertView.findViewById(SqResUtils.getIdByName("iv_select_status", "id", context));
            viewHolder.divider = convertView.findViewById(SqResUtils.getIdByName("divider", "id", context));
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        NativePayWay nativePayWay = nativePayWays.get(position);
        viewHolder.tvPayWay.setText(nativePayWay.getWayChinese());
        //有优惠信息则展示优惠文案，没有则展示附加信息
        if (TextUtils.isEmpty(nativePayWay.getPromote())) {
            viewHolder.tvPayPromote.setText(nativePayWay.getExtra());
            viewHolder.tvPayPromote.setTextColor(Color.parseColor("#999999"));
        } else {
            viewHolder.tvPayPromote.setText(nativePayWay.getPromote());
            viewHolder.tvPayPromote.setTextColor(Color.parseColor("#F54B4B"));
        }
        viewHolder.ivPayWay.setImageResource(SqResUtils.getDrawableId(context, nativePayWay.getResId()));
        if (nativePayWay.isSelected()) {
            viewHolder.ivSelectStatus.setImageResource(SqResUtils.getDrawableId(context, "sysq_ic_pay_selected"));
        } else {
            viewHolder.ivSelectStatus.setImageResource(SqResUtils.getDrawableId(context, "sysq_ic_pay_select"));
        }
        if (isShowDivider && position < nativePayWays.size() - 1) {
            viewHolder.divider.setVisibility(View.VISIBLE);
        } else {
            viewHolder.divider.setVisibility(View.GONE);
        }
        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (selectListener != null) {
                    selectListener.select(position);
                }
            }
        });
        return convertView;
    }

    public void setIsShowDivider(boolean isShowDivider) {
        this.isShowDivider = isShowDivider;
    }

    private static class ViewHolder {
        private TextView tvPayWay, tvPayPromote;
        private ImageView ivPayWay, ivSelectStatus;
        private View divider;
    }


    public void setSelectListener(SelectListener selectListener) {
        this.selectListener = selectListener;
    }

    public interface SelectListener {
        void select(int index);
    }
}
