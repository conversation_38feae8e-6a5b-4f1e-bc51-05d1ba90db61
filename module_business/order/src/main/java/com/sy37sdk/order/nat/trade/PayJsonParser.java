package com.sy37sdk.order.nat.trade;

import android.text.TextUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

public class PayJsonParser {
    private static HashMap<String, String> wayMap;
    private static HashMap<String, String> wayChineseMap;
    private static HashMap<String, String> wayRes;

    static {
        //支付方式
        wayMap = new HashMap<>();
        wayMap.put(NativePayWay.PWAY_KEY_ALIPAY, NativePayWay.PAY_WAY_ALIPAY);
        wayMap.put(NativePayWay.PWAY_KEY_HUABEI, NativePayWay.PAY_WAY_HUABEI);
        wayMap.put(NativePayWay.PWAY_KEY_WECHAT, NativePayWay.PAY_WAY_WECHAT);
        wayMap.put(NativePayWay.PWAY_KEY_WALLET, NativePayWay.PAY_WAY_WALLET);

        //支付方式-中文
        wayChineseMap = new HashMap<>();
        wayChineseMap.put(NativePayWay.PWAY_KEY_ALIPAY, "支付宝");
        wayChineseMap.put(NativePayWay.PWAY_KEY_HUABEI, "花呗");
        wayChineseMap.put(NativePayWay.PWAY_KEY_WECHAT, "微信支付");
        wayChineseMap.put(NativePayWay.PWAY_KEY_WALLET, "钱包支付");

        //支付图标
        wayRes = new HashMap<>();
        wayRes.put(NativePayWay.PWAY_KEY_ALIPAY, "sysq_ic_pay_ali");
        wayRes.put(NativePayWay.PWAY_KEY_HUABEI, "sysq_ic_pay_huabei");
        wayRes.put(NativePayWay.PWAY_KEY_WECHAT, "sysq_ic_pay_wechat");
        wayRes.put(NativePayWay.PWAY_KEY_WALLET, "sysq_ic_pay_wallet");
    }


    /**
     * 解析支付方式
     *
     * @param pWayJson
     * @return
     */
    public static ArrayList<NativePayWay> parsePWay(String pWayJson) {
        ArrayList<NativePayWay> nativePayWays = new ArrayList<>();
        try {
            JSONObject obj = new JSONObject(pWayJson);
            JSONObject promoteObj = optJSONObject(obj, "promoteTips");
            JSONObject payDiscountObj = optJSONObject(obj, "pwaysDiscount");
            JSONObject payTip = optJSONObject(obj, "pwaysTips");
            JSONObject pWayObj = optJSONObject(obj, "pways");
            Iterator<String> payIterator = pWayObj.keys();
            if (payIterator != null) {
                while (payIterator.hasNext()) {
                    String payWayKey = payIterator.next();
                    // 0：关闭 1：打开 2：折叠
                    String open = pWayObj.optString(payWayKey);
                    boolean payWayOpen = TextUtils.equals(open,"1") || TextUtils.equals(open,"2");
                    if (payWayOpen) {
                        String wayByKey = getWayByKey(payWayKey);
                        if (!TextUtils.isEmpty(wayByKey)) {
                            NativePayWay nativePayWay = new NativePayWay();
                            nativePayWay.setKey(payWayKey);
                            nativePayWay.setOpen(open);
                            nativePayWay.setWayChinese(getWayChineseByKey(payWayKey));
                            nativePayWay.setWay(wayByKey);
                            nativePayWay.setPromote(promoteObj.optString(payWayKey));
                            nativePayWay.setResId(getResByKey(payWayKey));
                            nativePayWay.setPWayTip(payTip.optString(payWayKey));
                            nativePayWays.add(nativePayWay);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return nativePayWays;
    }

    public static String getWayChineseByKey(String key) {
        return wayChineseMap.get(key);
    }

    public static String getResByKey(String res) {
        return wayRes.get(res);
    }

    public static String getWayByKey(String key) {
        return wayMap.get(key);
    }

    private static JSONObject optJSONObject(JSONObject obj, String key) {
        JSONObject jsonObject = obj.optJSONObject(key);
        if (jsonObject == null) {
            return new JSONObject();
        }
        return jsonObject;
    }
}
