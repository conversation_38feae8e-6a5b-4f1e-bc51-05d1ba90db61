package com.sy37sdk.order.third.ali;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.alipay.sdk.app.PayTask;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.JsonMap;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.third.CheckOrderManager;
import com.sy37sdk.order.third.CheckOrderManager.CheckOrderCallback;
import com.sy37sdk.order.third.IPayWay;
import com.sy37sdk.order.third.ThirdPayManager;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import org.json.JSONObject;

/**
 * <a
 * href="https://opendocs.alipay.com/open/204/105302?pathHash=eab39489#%E8%BF%94%E5%9B%9E%E7%BB%93%E6%9E%9C%E7%A4%BA%E4%BE%8B%EF%BC%88iOS%7CAndroid%EF%BC%89">...</a>
 *
 * <AUTHOR>
 * @since 2024/8/7
 */
public class AliPayWay implements IPayWay {

    /**
     * 后端接口用
     */
    private static final String HTTP_PAY_WAY = "alipay";

    public static final int ERROR_PARAM = 201;
    public static final int ERROR_ORDER_ID = 202;
    public static final int ERROR_TRADE = 203;
    public static final int ERROR_INVALID_RESULT = 204;

    static final Executor WORKER = Executors.newSingleThreadExecutor();

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    public void init(@NonNull Context context) {

    }

    @Override
    public void onStart(@NonNull Activity activity) {

    }

    @Override
    public void onResume(@NonNull Activity activity) {
    }

    @Override
    public void onPause(@NonNull Activity activity) {

    }

    @Override
    public void onStop(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data) {

    }

    /**
     * @see ThirdPayManager#EXTRA_ORDER_ID 支付宝订单号
     * @see ThirdPayManager#EXTRA_TRADE_INFO 支付宝订单信息
     */
    @Override
    public void pay(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable Bundle extra,
        @Nullable PayWayCallback callback) {
        SQLog.d(TAG + "[" + getSimpleName() + "]支付:\n" + order);
        SQLog.d(TAG + "[" + getSimpleName() + "]extra: " + extra);
        PayWayCallback uiCallback = UIPayWayCallback.wrap(callback);
        if (extra == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无额外参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "无参数");
            return;
        }

        // 查单用的订单号
        String uuid = extra.getString(ThirdPayManager.EXTRA_ORDER_ID);
        if (TextUtils.isEmpty(uuid)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无uuid, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_ORDER_ID, "订单号异常");
            return;
        }

        assert uuid != null;
        SQLog.d(TAG + "[" + getSimpleName() + "]uuid=" + uuid);

        String trade = extra.getString(ThirdPayManager.EXTRA_TRADE_INFO);
        if (TextUtils.isEmpty(trade)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无tradeInfo, 无法支付");
            if (callback != null) {
                uiCallback.onFailed(getName(), order, ERROR_TRADE, "支付参数异常");
            }
            return;
        }

        // 必须异步调用
        WORKER.execute(() -> {
            SQLog.d(TAG + "[" + getSimpleName() + "]调起支付宝, trade=" + trade);
            PayTask payTask = new PayTask(activity);
            Map<String, String> result = payTask.payV2(trade, true);
            SQLog.d(TAG + "[" + getSimpleName() + "]支付回调结果: " + result + ", uuid=" + uuid);
            // 切到主线程处理回调
            mHandler.post(() -> handlePayResult(order, uuid, new AliPayResult(result), uiCallback));
        });
    }

    private void handlePayResult(@NonNull PayOrderModel order, @NonNull String uuid, @NonNull AliPayResult result,
        @NonNull PayWayCallback callback) {
        if (!result.isValid()) {
            SQLog.e(TAG + "[" + getSimpleName() + "]结果解析异常, 支付失败: " + result);
            JsonMap map = new JsonMap();
            map.put("order_id", order.getMoid());
            map.put("third_order_id", uuid);
            map.put("result_map", String.valueOf(result.raw));
            BuglessAction.reportCatchException(new IllegalStateException("支付宝支付结果解析异常"),
                map.toString(), BuglessAction.PAY_ERROR);
            callback.onFailed(getName(), order, ERROR_INVALID_RESULT, "支付结果解析异常");
            return;
        }
        if (result.isSuccess()) {
            SQLog.i(TAG + "[" + getSimpleName() + "]支付成功, 检查支付状态");
            CheckOrderManager.getInstance().check(order, uuid, HTTP_PAY_WAY, new CheckOrderCallback() {
                @Override
                public void onSuccess(@NonNull PayOrderModel order, String thirdOrderId) {
                    callback.onSuccess(getName(), order);
                }

                @Override
                public void onFailure(@NonNull PayOrderModel order, String thirdOrderId, int code, String msg) {
                    SQLog.w(TAG + "[" + getSimpleName() + "]支付成功, 查单失败, 返回失败, uuid=" + thirdOrderId);
                    JsonMap map = new JsonMap();
                    map.put("order_id", order.getMoid());
                    map.put("third_order_id", thirdOrderId);
                    map.put("code", code);
                    map.put("msg", msg);
                    BuglessAction.reportCatchException(new IllegalStateException("支付宝支付成功, 查单失败"),
                        map.toString(), BuglessAction.PAY_ERROR);
                    callback.onFailed(getName(), order, code, msg);
                }
            });
        } else if (result.isCancel()) {
            SQLog.w(TAG + "[" + getSimpleName() + "]用户取消支付");
            // 支付取消
            callback.onCancel(getName(), order);
        } else {
            SQLog.e(TAG + "[" + getSimpleName() + "]支付失败: " + result);
            // 支付失败
            callback.onFailed(getName(), order, result.getErrorCode(), result.getErrorMsg());
        }
    }

    @NonNull
    @Override
    public PayWay getName() {
        return PayWay.ALI;
    }

    static class AliPayResult {

        static final String CODE_SUCCESS = "9000";
        static final String CODE_CANCEL = "6001";

        final Map<String, String> raw;
        // 结果码
        final String resultStatus;
        // 处理结果
        final String resultJsonStr;
        // 描述信息
        final String memo;

        AliPayResult(Map<String, String> result) {
            raw = result;
            if (result != null) {
                resultStatus = result.get("resultStatus");
                resultJsonStr = result.get("result");
                memo = result.get("memo");
            } else {
                resultStatus = null;
                resultJsonStr = null;
                memo = null;
            }
        }

        boolean isValid() {
            return !TextUtils.isEmpty(resultStatus);
        }

        boolean isSuccess() {
            return CODE_SUCCESS.equals(resultStatus);
        }

        boolean isCancel() {
            return CODE_CANCEL.equals(resultStatus);
        }

        int getErrorCode() {
            try {
                // 理论上应该可以转int
                return Integer.parseInt(resultStatus);
            } catch (Exception e) {
                return -1;
            }
        }

        String getErrorMsg() {
            String errorMsg = null;
            if (!TextUtils.isEmpty(resultJsonStr)) {
                JSONObject resultJson;
                try {
                    // 优先取json中的具体错误信息
                    resultJson = new JSONObject(resultJsonStr);
                    JSONObject response = resultJson.optJSONObject("alipay_trade_app_pay_response");
                    if (response != null) {
                        String msg = response.optString("msg");
                        String subMsg = response.optString("sub_msg");
                        if (!TextUtils.isEmpty(msg)) {
                            if (!TextUtils.isEmpty(subMsg)) {
                                errorMsg = msg + "/" + subMsg;
                            } else {
                                errorMsg = msg;
                            }
                        }
                    }
                } catch (Exception e) {
                    /* no-op */
                }
            }

            if (!TextUtils.isEmpty(errorMsg)) {
                return errorMsg;
            }

            if (!TextUtils.isEmpty(memo)) {
                // 取memo的描述信息
                return memo;
            }

            // 直接使用错误码
            return TextUtils.isEmpty(resultStatus) ? "unknown" : resultStatus;
        }

        @Override
        @NonNull
        public String toString() {
            return "resultStatus=" + resultStatus + ", resultJson=" + resultJsonStr + ", memo=" + memo;
        }
    }

    private String getSimpleName() {
        return "Ali";
    }
}
