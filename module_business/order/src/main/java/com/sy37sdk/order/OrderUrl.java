package com.sy37sdk.order;

import android.text.TextUtils;

import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.msdk.config.MultiSdkManager;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public class OrderUrl {

    public static final String KEY_AVAILABLE_PWAYS = "available_pways";
    public static final String KEY_WALLET_BALANCE = "wallet_balance";
    public static final String KEY_GET_COUPON = "get_coupon";
    public static final String KEY_WALLET_PAY = "wallet_pay";
    public static final String KEY_S_PAY = "s_pay";
    public static final String KEY_ORDER_STATUS = "order_status";

    /**
     * 支付方式以及折扣接口
     */

    @UrlUpdate(value = KEY_AVAILABLE_PWAYS, xValue = "x_available_pways")
    public static String AVAILABLE_PAY_WAYS = "http://mpay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/getAvailablePways";

    /**
     * 钱包余额
     */
    @UrlUpdate(value = KEY_WALLET_BALANCE, xValue = "x_wallet_balance")
    public static String WALLET_BALANCE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/piwt/";

    /**
     * 代金券
     */
    @UrlUpdate(value = KEY_GET_COUPON, xValue = "x_get_coupon")
    public static String GET_COUPON = "http://spay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/coupon/get";

    /**
     * 钱包支付
     */
    @UrlUpdate(value = KEY_WALLET_PAY, xValue = "x_wallet_pay")
    public static String WALLET_PAY = "http://spay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/callback/wtpay/";

    /**
     * 其它方式支付
     */
    @UrlUpdate(value = KEY_S_PAY, xValue = "x_order_s")
    public static String COMMON_PAY = "http://spay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/order";

    /**
     * 支付结果查询
     */
    @UrlUpdate(value = KEY_ORDER_STATUS, xValue = "x_order_status")
    public static String PAY_CHECK = "http://spay-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/ordersearch/";


    /**
     * web支付页面跳转链接 由 m层下单后接口返回更新
     */
    public static String H5_PAY = "http://" + MultiSdkManager.APP_HOST + "/sdk/pay/";



//    public static void refreshUrls(String data) {
//        if(TextUtils.isEmpty(data)) {
//            return;
//        }
//        try {
//            JSONObject jsonObject = new JSONObject(data);
//            AVAILABLE_PAY_WAYS = jsonObject.optString("available_pways", AVAILABLE_PAY_WAYS);
//            WALLET_BALANCE = jsonObject.optString("wallet_balance", WALLET_BALANCE);
//            GET_COUPON = jsonObject.optString("get_coupon", GET_COUPON);
//            WALLET_PAY = jsonObject.optString("wallet_pay", WALLET_PAY);
//            COMMON_PAY = jsonObject.optString("s_pay", COMMON_PAY);
//            PAY_CHECK = jsonObject.optString("order_status", PAY_CHECK);
//        } catch (JSONException e) {
//            LogUtil.e("解析支付页面链接失败！");
//            e.printStackTrace();
//        }
//    }

}
