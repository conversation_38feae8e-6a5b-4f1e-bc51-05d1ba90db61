package com.sy37sdk.order.nat;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import com.sqwan.common.mvp.ILoadView;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.view.BaseView;
import com.sy37sdk.order.view.PayConfirmDialog;
import com.sy37sdk.order.view.ui.BackPayView;

public abstract class BasePayView extends BaseView implements IPageSwitchView, ILoadView, IBasePayView {

    private BackPayView backPayView;

    protected View rootView;

    protected INativePayDialog nativePayDialog;

    public BasePayView(Context context) {
        super(context);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        initRootView();
        initView();
        initEvent();
    }

    private void initRootView() {
        if (rootView != null) {
            return;
        }
        rootView = LayoutInflater.from(getContext()).inflate(getIdByName(getLayoutResName(), "layout"), null);
        addView(rootView);
        initBackTitleView();
    }

    private void initBackTitleView() {
        backPayView = rootView.findViewById(SqResUtils.getIdByName("pay_title_view", "id", getContext()));
        backPayView.setOnClickBackViewListener(new BackPayView.OnClickBackViewListener() {
            @Override
            public void clickLeft() {
                leftViewClicked();
            }

            @Override
            public void clickRight() {
                new PayConfirmDialog.Builder(getContext()).setTitle(SqResUtils.getStringByName(getContext(), "sysq_pay_cancel_tip"))
                        .setCancelable(false)
                        .setNegativeButton(SqResUtils.getStringByName(getContext(), "sysq_pay_cancel_sure"), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (nativePayDialog != null) {
                                    nativePayDialog.closeAccountDialog(true);
                                }
                            }
                        }).setPositiveButton(SqResUtils.getStringByName(getContext(), "sysq_pay_cancel_continue"), null)
                        .show();
            }
        });
        setLeftResDrawable("sysq_pay_ic_help");
        setRightResDrawable("sysq_pay_ic_close");
        backPayView.setTitle(getTitle());
    }

    protected void setLeftResDrawable(String resDrawable) {
        if (backPayView != null) {
            backPayView.setLeftResDrawable(resDrawable);
        }
    }

    protected void setRightResDrawable(String resDrawable) {
        if (backPayView != null) {
            backPayView.setRightResDrawable(resDrawable);
        }
    }

    protected void setLeftVisible(boolean visible){
        if(backPayView!=null){
            backPayView.setLeftVisible(visible);
        }
    }

    public abstract String getTitle();

    public abstract void initView();

    public abstract void initEvent();

    public abstract String getLayoutResName();


    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {

    }

    @Override
    public void onBackPressed() {

    }

    @Override
    public void showLoading() {
        if (nativePayDialog != null) {
            nativePayDialog.showLoading();
        }
    }

    @Override
    public void hideLoading() {
        if (nativePayDialog != null) {
            nativePayDialog.hideLoading();
        }
    }

    protected void leftViewClicked() {

    }

    @Override
    public void showToast(String msg) {
        ToastUtil.showToast(getContext(), msg);
    }

    @Override
    public void paySuccess() {

    }

    @Override
    public void onFailure(int code, String msg) {

    }

    @Override
    public void onPayViewWindowFocusChanged(boolean hasFocus) {

    }

    protected boolean isLandscape() {
        Configuration configuration = getContext().getResources().getConfiguration();
        int orientation = configuration.orientation;
        return orientation == Configuration.ORIENTATION_LANDSCAPE;
    }
}
