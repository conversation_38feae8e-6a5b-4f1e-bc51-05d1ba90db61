package com.sy37sdk.order.view.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;

/**
 * 带返回、关闭的标题支付view
 */
public class BackPayView extends RelativeLayout {
    private View rootView;

    private View leftLayout, rightLayout;

    private ImageView ivLeft, ivRight;

    private Context mContext;

    private TextView tvTitle;

    private OnClickBackViewListener mOnClickBackViewListener;

    public BackPayView(Context context) {
        this(context, null);
    }

    public BackPayView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BackPayView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }


    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        rootView = View.inflate(mContext, SqResUtils.getIdByName("sysq_pay_dialog_action_bar", "layout", mContext), this);
        initView();
        initEvent();
    }

    private void initView() {
        leftLayout = rootView.findViewById(SqResUtils.getIdByName("left_layout", "id", mContext));
        rightLayout = rootView.findViewById(SqResUtils.getIdByName("right_layout", "id", mContext));
        tvTitle = rootView.findViewById(SqResUtils.getIdByName("tv_title", "id", mContext));
        ivLeft = rootView.findViewById(SqResUtils.getIdByName("iv_left", "id", mContext));
        ivRight = rootView.findViewById(SqResUtils.getIdByName("iv_right", "id", mContext));
    }

    private void initEvent() {
        leftLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickBackViewListener != null) {
                    mOnClickBackViewListener.clickLeft();
                }
            }
        });

        rightLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickBackViewListener != null) {
                    mOnClickBackViewListener.clickRight();
                }
            }
        });
    }


    public void setLeftResDrawable(String resDrawable) {
        if (ivLeft != null) {
            ivLeft.setImageResource(SqResUtils.getDrawableId(mContext, resDrawable));
        }
    }

    public void setRightResDrawable(String resDrawable) {
        if (ivRight != null) {
            ivRight.setImageResource(SqResUtils.getDrawableId(mContext, resDrawable));
        }
    }

    public void setTitle(String title) {
        if (tvTitle != null) {
            tvTitle.setText(title);
        }
    }

    public interface OnClickBackViewListener {

        void clickLeft();

        void clickRight();
    }

    public void setOnClickBackViewListener(OnClickBackViewListener onClickTitleViewListener) {
        this.mOnClickBackViewListener = onClickTitleViewListener;
    }

    public void setLeftVisible(boolean visible) {
        if (leftLayout != null) {
            leftLayout.setVisibility(visible ? VISIBLE : GONE);
        }
    }
}
