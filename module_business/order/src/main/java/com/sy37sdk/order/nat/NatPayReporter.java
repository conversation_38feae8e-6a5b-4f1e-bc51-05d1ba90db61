package com.sy37sdk.order.nat;

import android.text.TextUtils;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import java.util.HashMap;

public class NatPayReporter {

    /**
     * 进入支付页
     *
     * @param orderId
     * @param orderAmount
     * @param payChannel
     * @param payVersion
     */
    public static void trackPayInit(String orderId, String orderAmount, String payChannel, String payVersion) {
        HashMap<String, String> payInitMap = new HashMap<>();
        payInitMap.put(SqTrackKey.order_id, orderId);
        payInitMap.put(SqTrackKey.order_amount, orderAmount);
        payInitMap.put(SqTrackKey.pay_channels, payChannel);
        payInitMap.put(SqTrackKey.pay_version, payVersion);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.pay_init, payInitMap);

    }

    /**
     * 点击支付
     *
     * @param orderId
     * @param orderAmount
     * @param payAmount
     * @param payChannel
     * @param payMethod
     * @param payVersion
     * @param isVouchers
     * @param vouchersId
     */
    public static void trackPayClick(String orderId, String orderAmount, String payAmount, String payChannel, String payMethod, String payVersion, String isVouchers, String vouchersId) {
        HashMap<String, String> payClickMap = new HashMap<>();
        payClickMap.put(SqTrackKey.order_id, orderId);
        payClickMap.put(SqTrackKey.order_amount, orderAmount);
        payClickMap.put(SqTrackKey.pay_amount, payAmount);
        payClickMap.put(SqTrackKey.pay_channels, payChannel);
        payClickMap.put(SqTrackKey.pay_method, payMethod);
        payClickMap.put(SqTrackKey.pay_version, payVersion);
        payClickMap.put(SqTrackKey.is_vouchers, isVouchers);
        payClickMap.put(SqTrackKey.vouchers_id, TextUtils.isEmpty(vouchersId) || vouchersId.equals("[]") ? "0" : vouchersId);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.pay_click, payClickMap);
    }

    /**
     * 关闭支付页
     *
     * @param orderId
     * @param orderAmount
     * @param payAmount
     * @param payChannel
     * @param payMethod
     * @param payVersion
     * @param isVouchers
     * @param vouchersId
     */
    public static void trackPayClose(String orderId, String orderAmount, String payAmount, String payChannel, String payMethod, String payVersion, String isVouchers, String vouchersId) {
        HashMap<String, String> payClickMap = new HashMap<>();
        payClickMap.put(SqTrackKey.order_id, orderId);
        payClickMap.put(SqTrackKey.order_amount, orderAmount);
        payClickMap.put(SqTrackKey.pay_amount, payAmount);
        payClickMap.put(SqTrackKey.pay_channels, payChannel);
        payClickMap.put(SqTrackKey.pay_method, payMethod);
        payClickMap.put(SqTrackKey.pay_version, payVersion);
        payClickMap.put(SqTrackKey.is_vouchers, isVouchers);
        payClickMap.put(SqTrackKey.vouchers_id, TextUtils.isEmpty(vouchersId) || vouchersId.equals("[]") ? "0" : vouchersId);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.pay_close_click, payClickMap);
    }
}
