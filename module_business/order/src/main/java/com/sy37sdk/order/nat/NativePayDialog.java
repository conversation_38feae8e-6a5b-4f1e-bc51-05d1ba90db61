package com.sy37sdk.order.nat;

import android.content.Context;
import android.os.Bundle;
import android.support.v4.view.ViewPager;

import com.sy37sdk.order.nat.bean.Order;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.widget.BaseViewPager;
import com.sy37sdk.order.PayConstants;
import com.sy37sdk.order.PayVersionUtil;
import com.sy37sdk.order.nat.coupon.CouponView;
import com.sy37sdk.order.nat.trade.TradeView;
import com.sy37sdk.order.nat.coupon.CouponPresenter;
import com.sy37sdk.order.nat.trade.TradePayPresenter;

import java.util.ArrayList;
import java.util.List;

/**
 * 原生支付页
 */
public class NativePayDialog extends BaseDialog implements INativePayDialog {

    private Context mContext;

    private INativePayListener mPayListener;

    private Order order;

    private BaseViewPager viewPager;

    private PayPageSwitcher payPageSwitcher;

    private List<BaseNativePayPresenter> mPayPagePresenterList;

    private TradePayPresenter tradeViewPayPresenter;

    private CouponPresenter couponPresenter;


    private PayPageAdapter payPageAdapter;

    private int mFromIndex, mToIndex;

    private Bundle mBundle;

    public NativePayDialog(Context context, Order order, INativePayListener listener) {
        super(context);
        this.mContext = context;
        this.mPayListener = listener;
        this.order = order;
    }


    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sysq_pay_dialog", "layout"));
        initView();
        if (null != this.order) {
            NatPayReporter.trackPayInit(order.getMoid(), order.getMoney() + "", PayConstants.PAY_CHANNEL_NATIVE, PayVersionUtil.nativeVersion);
        }
    }

    private void initView() {
        viewPager = findViewById(getIdByName("pay_dialog_content", "id"));
        viewPager.setPagingEnabled(false);
        payPageSwitcher = new PayPageSwitcher(viewPager);
        initPageViewList();
        payPageAdapter = new PayPageAdapter(mPayPagePresenterList);
        viewPager.setAdapter(payPageAdapter);
        viewPager.setOffscreenPageLimit(mPayPagePresenterList.size());
        payPageSwitcher.setPageScrollListener(new PayPageSwitcher.IPageScrollListener() {

            @Override
            public void scroll(int fromIndex, int toIndex, Bundle bundle) {
                mFromIndex = fromIndex;
                mToIndex = toIndex;
                mBundle = bundle;
                LogUtil.i("scroll 页面切换 from " + mFromIndex + " to " + mToIndex);
            }
        });
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {
                LogUtil.i("onPageScrolled " + i);
                BaseNativePayPresenter nativePayPresenter = mPayPagePresenterList.get(i);
                nativePayPresenter.onSwitched(mFromIndex, mToIndex, mBundle);
            }

            @Override
            public void onPageSelected(int i) {
                LogUtil.i("onPageSelected " + i);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
    }

    /**
     * 初始化支付相关各个界面以及设置回调
     */
    private void initPageViewList() {
        mPayPagePresenterList = new ArrayList<>();
        //商品支付
        tradeViewPayPresenter = new TradePayPresenter(mContext, new TradeView(mContext, this));
        tradeViewPayPresenter.setPageSwitcher(payPageSwitcher);
        tradeViewPayPresenter.setPayListener(mPayListener);
        tradeViewPayPresenter.setOrder(order);
        mPayPagePresenterList.add(PayPageSwitcher.PAGE_TRADE, tradeViewPayPresenter);
        //代金券
        couponPresenter = new CouponPresenter(mContext, new CouponView(mContext, this));
        couponPresenter.setPageSwitcher(payPageSwitcher);
        couponPresenter.setPayListener(mPayListener);
        mPayPagePresenterList.add(PayPageSwitcher.PAGE_COUPON, couponPresenter);
    }

    @Override
    public void onBackPressed() {
        LogUtil.i("onBackPressed " + mToIndex);
        BaseNativePayPresenter accountPagerPresenter = mPayPagePresenterList.get(mToIndex);
        accountPagerPresenter.onBackPressed();
    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        LogUtil.i("onWindowFocusChanged " + mToIndex);
        BaseNativePayPresenter accountPagerPresenter = mPayPagePresenterList.get(mToIndex);
        accountPagerPresenter.onPayViewWindowFocusChanged(hasFocus);
    }

    @Override
    public void onSwitch(int index, Bundle bundle) {
        if (payPageSwitcher != null) {
            payPageSwitcher.onSwitch(index, bundle);
        }
    }

    @Override
    public void closeAccountDialog(boolean clickCancel) {
        if (clickCancel) {
            NatPayReporter.trackPayClose(order.getMoid(), order.getMoney() + "", order.getPayAmount(), PayConstants.PAY_CHANNEL_NATIVE, order.getPayMethod(), PayVersionUtil.nativeVersion, order.getIsVouchers(), order.getVouchersId());
            if (mPayListener != null) {
                mPayListener.onFailure(205, "取消支付【20005】");
            }
            if (tradeViewPayPresenter != null) {
                tradeViewPayPresenter.reportPay(false);
            }
        }
        dismiss();
    }
}
