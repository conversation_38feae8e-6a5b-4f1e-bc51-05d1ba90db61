package com.sy37sdk.order;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.eventbus.annotation.Subscribe;
import com.sq.eventbus.core.EventBus;
import com.sq.sdk.tool.util.EncodeUtil;
import com.sq.sdk.tool.util.SqDeviceUtil;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.base.SqError;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.eventbus.SActiveEvent;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.order.IOrderMod;
import com.sqwan.common.route.FunctionRouter;
import com.sqwan.common.route.FunctionRouter.Func;
import com.sqwan.common.route.FunctionRouterManager;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.Base64;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.common.util.ZipUtil;
import com.sqwan.order.base.IPay.PayCallback;
import com.sqwan.order.base.IPay.UIPayCallback;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayExtraInfo;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.PayWay;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.order.eventbus.EventBusIndex;
import com.sy37sdk.order.nat.INativePayListener;
import com.sy37sdk.order.nat.NativePayDialog;
import com.sy37sdk.order.nat.bean.Order;
import com.sy37sdk.order.third.ali.AliPay;
import com.sy37sdk.order.web.PayWebPage;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/3/1
 */
public class OrderModImpl implements IOrderMod {

    private static final String TAG = "【Pay Mod】";

    private final Context mContext;
    private final AliPay mAliPay = new AliPay();

    private PayPack mCurrentPay;

    public OrderModImpl(Context context) {
        mContext = context;
        EventBus.getDefault().addIndex(new EventBusIndex());
        EventBus.getDefault().register(this);
        registerPayFuncToRouter();
    }

    /**
     * 提供给js调用的, 通用的阿里支付方法, 注意和常规支付的阿里支付区分
     */
    private void registerPayFuncToRouter() {
        FunctionRouterManager.getInstance().register((activity, key, data) -> {
            if (Func.FUNC_UNIVERSAL_ALI_PAY.equals(key)) {
                String orderId = null;
                String tradeInfo = null;
                if (data != null) {
                    String jsonStr = data.getString(FunctionRouter.KEY_DATA);
                    try {
                        JSONObject json = new JSONObject(jsonStr);
                        orderId = json.optString("uuid");
                        String tradeCode = json.optString("trade");
                        // js参数有做base64加密, 所以要解密
                        tradeInfo = new String(Base64.decode(tradeCode));
                    } catch (Exception e) {
                        /* no-op */
                    }
                }
                mAliPay.pay(activity, orderId, tradeInfo, null);
            }
        });
    }

    @Override
    public void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @Nullable PayCallback callback) {
        SQLog.d(TAG + "支付\n" + payInfo);
        PayConfig config = new PayConfig(activity, payContext.getOrderDataJson());
        SQLog.d(TAG + "支付配置: " + config);
        PayCallback uiCallback = UIPayCallback.wrap(callback);
        mCurrentPay = new PayPack(payContext, payInfo, config, uiCallback);

        // 如果无下单信息, 表示是充值到钱包, 使用h5支付
        if (config.isAppPay) {
            SQLog.d(TAG + "走原生支付");
            payContext.setPayChannel(PayConstants.PAY_CHANNEL_NATIVE);
            Order order = convertToOrder(payInfo);
            order.setMoid(payContext.getMoid());
            NativePayDialog payDialog = new NativePayDialog(activity, order,
                new INativePayListener() {
                    @Override
                    public void onSuccess() {
                        SQLog.i(TAG + "原生支付成功");
                        //order经过透传，在内部被赋值一些信息
                        payContext.setPayExtraInfo(new PayExtraInfo(order.getPayAmount(), order.getIsVouchers(), order.getVouchersId(), PayVersionUtil.nativeVersion));
                        uiCallback.onSuccess(payContext);
                    }

                    @Override
                    public void onFailure(int code, String message) {
                        payContext.setPayExtraInfo(new PayExtraInfo(order.getPayAmount(), order.getIsVouchers(), order.getVouchersId(), PayVersionUtil.nativeVersion));
                        if (code == 205) {
                            SQLog.w(TAG + "原生支付回调取消支付, code=" + code + ", msg=" + message);
                            uiCallback.onCancel(payContext);
                        } else {
                            SQLog.e(TAG + "原生支付失败, code=" + code + ", msg=" + message);
                           uiCallback.onFailed(payContext, new SqPayError(SqPayError.ERROR_SDK_NATIVE, message, code));
                        }
                    }
                });
            payDialog.show();
        } else {
            SQLog.d(TAG + "走网页支付");
            payContext.setPayChannel(PayConstants.PAY_CHANNEL_WEB);
            try {
                String payUrl = generatePayUrl(payContext, payInfo, config);
                SQLog.d(TAG + "支付url: " + payUrl);
                // 等待onActivityResult
                PayWebPage.startForResult(activity, payContext.getSession(), payUrl, payInfo, payContext.getMoid());
            } catch (Exception e) {
                SQLog.e(TAG + "支付页跳转失败", e);
                BuglessAction.reportCatchException(e, "支付页跳转失败", BuglessAction.PAY_DIALOG_SHOW_FAIL);
                uiCallback.onFailed(payContext, new SqPayError(
                    SqPayError.ERROR_SDK_INLINE, "支付异常", -1));
            }
        }
    }

    @Subscribe()
    public void onActivityResult(OnActivityResultEvent event) {
        int requestCode = event.getRequestCode();
        int resultCode = event.getResultCode();
        if (requestCode != PayWebPage.REQUEST_CODE || resultCode != PayWebPage.RESULT_PAY_CODE) {
            // 不是约定的code, 忽略
            return;
        }
        SQLog.d(TAG + "onActivityResult");
        Intent intent = event.getIntent();
        if (intent == null) {
            SQLog.w(TAG + "无intent, 忽略");
            return;
        }
        final PayPack payPack = mCurrentPay;
        if (payPack == null) {
            SQLog.w(TAG + "当前无支付, 忽略");
            return;
        }
        SQLog.d(TAG + "当前支付信息: " + payPack);
        // 只有h5支付才走该回调
        if (payPack.payConfig.isAppPay) {
            SQLog.w(TAG + "当前是原生支付, 忽略");
            return;
        }
        // 从支付结果中获取支付类型, 是数字
        int payWayInt = intent.getIntExtra(PayWebPage.BUNDLE_RESULT_PAY_WAY_INT, PayWay.UNKNOWN.type);
        PayWay payWay = PayWay.get(payWayInt);

        //另外拼接一些支付信息
        String payAmount = intent.getStringExtra(PayWebPage.BUNDLE_RESULT_PAY_AMOUNT);
        String vouchersId = intent.getStringExtra(PayWebPage.BUNDLE_RESULT_PAY_VOUCHERS_ID);
        String isVouchers = intent.getStringExtra(PayWebPage.BUNDLE_RESULT_PAY_IS_VOUCHERS);
        String payVersion = intent.getStringExtra(PayWebPage.BUNDLE_RESULT_PAY_VERSION);

        payPack.payContext.setPayWay(payWay);
        payPack.payContext.setPayExtraInfo(new PayExtraInfo(payAmount, isVouchers, vouchersId, payVersion));
        int payStatus = intent.getIntExtra(PayWebPage.BUNDLE_RESULT_PAY, PayWebPage.CODE_PAY_FAILURE);
        if (payStatus == PayWebPage.CODE_PAY_SUCCESS) {
            SQLog.i(TAG + "网页支付成功");
            if (payPack.callback != null) {
                payPack.callback.onSuccess(payPack.payContext);
            }
        } else if (payStatus == PayWebPage.CODE_PAY_CANCEL) {
            String cancelWay = intent.getStringExtra(PayWebPage.BUNDLE_KEY_RESULT_CANCEL_WAY);
            if (TextUtils.isEmpty(cancelWay)) {
                cancelWay = "unknown";
            }
            payPack.payContext.setCancelWay(cancelWay);
            SQLog.w(TAG + "网页支付回调取消支付");
            if (payPack.callback != null) {
                payPack.callback.onCancel(payPack.payContext);
            }
        } else {
            SqPayError payError;
            // 传递过来会被转成SqError
            SqError error = intent.getParcelableExtra(PayWebPage.BUNDLE_KEY_RESULT_PAY_ERROR);
            if (error == null) {
                payError = new SqPayError(SqPayError.ERROR_SDK_INLINE, "支付异常", 0);
            } else {
                payError = new SqPayError(error);
            }
            SQLog.e(TAG + "网页支付失败, " + error);
            if (payPack.callback != null) {
                payPack.callback.onFailed(payPack.payContext, payError);
            }
        }
    }

    @Subscribe()
    public void onSActiveEvent(SActiveEvent event) {
        SQLog.d(TAG + "onSActiveEvent");
        String data = event.getData();
        if (TextUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject sJson = new JSONObject(data);
            JSONObject uJson = sJson.getJSONObject("u");
            String payUrl = uJson.getString("pay");
            if (!TextUtils.isEmpty(payUrl)) {
                SQLog.i(TAG + "更新网页支付链接: " + payUrl);
                OrderUrl.H5_PAY = payUrl;
            }
        } catch (JSONException e) {
            SQLog.w(TAG + "获取网页支付链接失败, 使用默认的网页支付链接");
        }
    }


    private String generatePayUrl(@NonNull PayContext payContext, @NonNull PayInfoModel order,
        @NonNull PayConfig config) {
        String payUrl;
        if (!TextUtils.isEmpty(config.payUrl)) {
            payUrl = config.payUrl;
        } else {
            SQLog.w(TAG + "使用默认H5: " + OrderUrl.H5_PAY);
            payUrl = OrderUrl.H5_PAY;
        }
        String token = "";
        String uid = "";
        String uname = "";
        IAccountMod accountMod = ModHelper.get(IAccountMod.class);
        if (accountMod != null) {
            token = accountMod.getToken();
            uid = accountMod.getUid();
            uname = accountMod.getUname();
        }
        String moid = payContext.getMoid() == null ? "" : payContext.getMoid();
        Bundle params = OrderRequestManager.addCommonParamsOfPay(mContext, new Bundle(), uid == null ? "" : uid, moid);
        params.putString("doid", order.getOrderId());
        params.putString("dpt", order.getProductName());
        params.putString("dcn", order.getCurrencyName());
        params.putString("dsid", order.getServerId());
        params.putString("dext", order.getExtend());
        params.putString("drname", order.getRoleName());
        params.putString("drid", order.getRoleId());
        params.putString("drlevel", "" + order.getRoleLevel());
        params.putString("dmoney", "" + order.getMoney());
        params.putString("dradio", "" + order.getRadio());
        params.putString("moid", moid);
        params.putString("token", token == null ? "" : token);
        params.putString("uid", uid == null ? "" : uid);
        params.putString("uname", uname == null ? "" : uname);
        params.putString("ig", "1");
        params.putString("os", "1");
        params.putString("os_desc", DeviceUtils.getOs());
        params.putString("code", String.valueOf(config.payCode));
        params.putString("sversion", VersionUtil.sdkVersion);
        params.putString("version", SqDeviceUtil.getVersionName(mContext));
        params.putString("haswx", (SqTrackUtil.checkAppInstalled(mContext, "com.tencent.mm") ? 1 : 0) + "");
        params.putString("pway", config.payWay);
        params.putString("gwversion", VersionUtil.gwversion);
        // 宿主版本号
        params.putString("host_sdk_version", VersionUtil.getOriginalVersion());
        params.putString("payVersion", PayVersionUtil.PAY_VERSION);
        return payUrl + (payUrl.contains("?") ? "&" : "?") + EncodeUtil.encodeUrl(params);
    }

    private static class PayConfig {

        boolean isAppPay = false;
        int payCode = 0;
        String payUrl = "";
        String payWay = "";

        public PayConfig(Context context, @Nullable JSONObject orderJson) {
            if (orderJson == null) {
                return;
            }
            isAppPay = orderJson.optInt("appPay", 0) == 1;
            payWay = orderJson.optString("pway", "");
            if (orderJson.has("sdata")) {
                String encryptedData = orderJson.optString("sdata");
                if (!TextUtils.isEmpty(encryptedData)) {
                    String data = ZipUtil.sqUnZip(context, encryptedData);
                    SQLog.d(TAG + "sdata=" + data);
                    JSONObject sdataJson;
                    try {
                        sdataJson = new JSONObject(data);
                    } catch (JSONException e) {
                        return;
                    }
                    payCode = sdataJson.optInt("code", 0);
                    payUrl = sdataJson.optString("pay", "");
                }
            }
        }

        @Override
        @NonNull
        public String toString() {
            return "isAppPay=" + isAppPay + ", payCode=" + payCode +
                ", payUrl=" + payUrl + ", payWay=" + payWay;
        }
    }

    private static class PayPack {

        @NonNull
        final PayContext payContext;
        @Nullable
        final PayInfoModel payInfo;
        @NonNull
        final PayConfig payConfig;
        @Nullable
        final PayCallback callback;

        PayPack(@NonNull PayContext payContext, @Nullable PayInfoModel payInfo,
            @NonNull PayConfig payConfig, @Nullable PayCallback callback) {
            this.payContext = payContext;
            this.payInfo = payInfo;
            this.payConfig = payConfig;
            this.callback = callback;
        }

        @Override
        @NonNull
        public String toString() {
            String ret = "[" + payContext.getSession() + "]";
            if (payInfo != null) {
                ret = ret + "order=" + payInfo.getOrderId() + ", ";
            }
            return ret + "moid=" + payContext.getMoid() + ", callback=" + callback;
        }
    }

    @NonNull
    private Order convertToOrder(@NonNull PayInfoModel info) {
        Order order = new Order();
        order.setDoid(info.getOrderId());
        order.setDsid(info.getServerId());
        order.setMoney(info.getMoney());
        order.setDext(info.getExtend());
        order.setDrid(String.valueOf(info.getRoleId()));
        order.setDrname(info.getRoleName());
        order.setDcn(info.getCurrencyName());
        order.setDrlevel(info.getRoleLevel());
        order.setDradio(info.getRadio());
        order.setDpt(info.getProductName());
        return order;
    }
}
