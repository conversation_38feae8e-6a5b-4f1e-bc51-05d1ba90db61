package com.sy37sdk.order.nat.pay;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import com.alipay.sdk.app.H5PayCallback;
import com.alipay.sdk.app.PayTask;
import com.alipay.sdk.util.H5PayResultModel;
import com.sqwan.common.util.LogUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-06-11
 */
public class Apay {

    private static Apay mInstance;
    private Handler resultHandler;
    private static final int SDK_PAY_FLAG = 1;
    private static AliCallback callback;

    private Apay() {
        resultHandler = new ResultHandler(Looper.getMainLooper());
    }

    public static synchronized Apay getInstance() {
        if(mInstance == null) {
            mInstance = new Apay();
        }
        return mInstance;
    }

    public void pay(final Activity activity, final String orderInfo, AliCallback listener, final boolean loading)  {
        callback = listener;
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                PayTask payTask = new PayTask(activity);
                Map<String, String> result = payTask.payV2(orderInfo, loading);
                LogUtil.i("apay", result.toString());
                Message msg = new Message();
                msg.what = SDK_PAY_FLAG;
                msg.obj = result;
                resultHandler.sendMessage(msg);
            }
        };
        Thread thread = new Thread(runnable);
        thread.start();
    }

    public boolean interceptorWithUrl(final Activity activity, String url, boolean loading, final AliCallback listener) {
        callback = listener;
        final PayTask task = new PayTask(activity);
        boolean isIntercepted = task.payInterceptorWithUrl(url, loading, new H5PayCallback() {
            @Override
            public void onPayResult(H5PayResultModel resultModel) {
                String resultCode = resultModel.getResultCode();
                String url = resultModel.getReturnUrl();
                Map<String,String> result = new HashMap<>();
                result.put("resultStatus", resultCode);
                result.put("returnUrl", url);
                result.put("memo", "取消支付");
                Message msg = new Message();
                msg.what = SDK_PAY_FLAG;
                msg.obj = result;
                resultHandler.sendMessage(msg);
            }
        });
        return isIntercepted;
    }


    static class ResultHandler extends Handler {

        public ResultHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if(msg.what == SDK_PAY_FLAG) {
                Result result = new Result(msg.obj);
                if(callback != null) {
                    if(result.getResultStatus().equals(Result.STATUS_CODE_SUCCESS)) {
                        callback.onSuccess();
                    } else if(!TextUtils.isEmpty(result.getReturnUrl())) {
                        callback.onFailure(Result.STATUS_CODE_URL, "支付宝支付链接异常，请重试或联系客服【20008】");
                    } else {
                        callback.onFailure(-1, result.getMemo());
                    }
                }
            }
        }
    }

    public interface AliCallback {
        void onSuccess();
        void onFailure(int code, String msg);
    }

}
