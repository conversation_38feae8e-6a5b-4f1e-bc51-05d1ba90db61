package com.sy37sdk.order.third.douyin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.WebView;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.JsonMap;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.third.CheckOrderManager;
import com.sy37sdk.order.third.CheckOrderManager.CheckOrderCallback;
import com.sy37sdk.order.third.CheckablePayWay;
import com.sy37sdk.order.third.IWebPayWay;
import com.sy37sdk.order.third.ThirdPayManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
public class DouYinPayWay implements IWebPayWay, CheckablePayWay {

    /**
     * 后端接口用
     */
    private static final String HTTP_PAY_WAY = "douyin_h5";

    public static final int ERROR_UNSUPPORTED = 300;
    public static final int ERROR_PARAM = 301;
    public static final int ERROR_ORDER_ID = 302;
    public static final int ERROR_NO_ORDER = 303;
    public static final int ERROR_ORDER_NOT_MATCH = 304;
    public static final int ERROR_URL = 305;
    public static final int ERROR_GO_PAY_PAGE = 306;

    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final WebView mWebView;
    @Nullable
    private DouYinOrder mCurrentOrder;

    private final List<String> mSchemePrefix = new ArrayList<>();

    public DouYinPayWay(WebView webView) {
        mWebView = webView;
        // 抖音：ttcjpay://dypay/cashier 和 dypay1128://dypay/cashier
        mSchemePrefix.add("ttcjpay://dypay/cashier");
        mSchemePrefix.add("dypay1128://dypay/cashier");
        // 抖音极速版：ttcjpay://dypay/awemelite 和 dypay2329://dypay/cashier
        mSchemePrefix.add("ttcjpay://dypay/awemelite");
        mSchemePrefix.add("dypay2329://dypay/cashier");
        // 抖音火山版：dypay8663://dypay/cashier
        // 抖音官方的人说目前暂时不支持抖音火山版调起支付，但是后续会支持
        mSchemePrefix.add("dypay8663://dypay/cashier");
    }

    @Override
    public void init(@NonNull Context context) {

    }

    @Override
    public void onStart(@NonNull Activity activity) {

    }

    @Override
    public void onResume(@NonNull Activity activity) {
        SQLog.v(TAG + "[" + getSimpleName() + "]onResume, " + mCurrentOrder);
        // 从支付页回来, 查单
        checkOrder(true);
    }

    @Override
    public void onPause(@NonNull Activity activity) {

    }

    @Override
    public void onStop(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data) {

    }

    /**
     * @see ThirdPayManager#EXTRA_ORDER_ID 抖音订单号
     * @see ThirdPayManager#EXTRA_REFERER refer
     * @see ThirdPayManager#EXTRA_PAY_URL 跳转链接
     */
    @Override
    public void pay(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable Bundle extra,
        @Nullable PayWayCallback callback) {
        SQLog.d(TAG + "[" + getSimpleName() + "]支付:\n" + order);
        SQLog.d(TAG + "[" + getSimpleName() + "]extra: " + extra);
        InternalCallback uiCallback = wrapCallback(callback);
        if (extra == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无额外参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "无参数");
            return;
        }
        // 查单用的订单号
        String uuid = extra.getString(ThirdPayManager.EXTRA_ORDER_ID);
        if (TextUtils.isEmpty(uuid)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无uuid, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_ORDER_ID, "订单号异常");
            return;
        }

        assert uuid != null;

        DouYinOrder oldOrder = mCurrentOrder;
        if (oldOrder != null) {
            SQLog.w(TAG + "[" + getSimpleName() + "]覆盖订单: " + oldOrder.order.getMoid());
            JsonMap map = new JsonMap();
            map.put("old_moid", oldOrder.order.getMoid());
            map.put("old_uuid", oldOrder.uuid);
            map.put("new_moid", order.getMoid());
            map.put("new_uuid", uuid);
            BuglessAction.reportCatchException(new IllegalStateException("抖音覆盖订单"),
                map.toString(), BuglessAction.PAY_ERROR);
        }

        mCurrentOrder = new DouYinOrder(uuid, order, uiCallback);
        String refer = extra.getString(ThirdPayManager.EXTRA_REFERER);
        // 交易链接
        String payUrl = extra.getString(ThirdPayManager.EXTRA_PAY_URL);
        if (!TextUtils.isEmpty(refer) && !TextUtils.isEmpty(payUrl)) {
            assert payUrl != null;
            SQLog.d(TAG + "[" + getSimpleName() + "]重定向到支付链接: " + payUrl + ", refer=" + refer);
            Map<String, String> extraHeaders = new HashMap<>();
            extraHeaders.put("referer", refer);
            // 会走到interceptorWithUrl
            mHandler.post(() -> mWebView.loadUrl(payUrl, extraHeaders));
        } else {
            SQLog.e(TAG + "[" + getSimpleName() + "]无有效参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "参数异常");
        }
    }

    @NonNull
    @Override
    public PayWay getName() {
        return PayWay.DOU_YIN_H5;
    }

    @Override
    public boolean interceptorWithUrl(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable String url,
        @Nullable Bundle extra, @Nullable PayWayCallback callback) {
        if (url == null || !isMatchDouYinPaySchemePrefix(url)) {
            return false;
        }
        InternalCallback uiCallback = wrapCallback(callback);
        SQLog.i(TAG + "[" + getSimpleName() + "]匹配到抖音支付协议: " + url);
        DouYinOrder currentOrder = mCurrentOrder;
        if (currentOrder == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无前置订单, 直接支付失败");
            uiCallback.onFailed(getName(), order, ERROR_NO_ORDER, "无订单");
        } else if (!currentOrder.match(order)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]订单对应不上, 当前:"
                + currentOrder.order.getMoid() + ", 实际:" + order.getMoid() + ", 直接支付失败");
            JsonMap map = new JsonMap();
            map.put("expect_order_id", currentOrder.order.getMoid());
            map.put("exact_order_id", order.getMoid());
            BuglessAction.reportCatchException(new IllegalStateException("抖音订单不对应"),
                map.toString(), BuglessAction.PAY_ERROR);
            uiCallback.onFailed(getName(), order, ERROR_ORDER_NOT_MATCH, "订单异常");
        } else {
            currentOrder.callback = uiCallback;
            payByUrl(activity, order, url, uiCallback);
        }
        return true;
    }

    private void payByUrl(@NonNull Activity activity, @NonNull PayOrderModel order, @NonNull String url,
        @NonNull PayWayCallback callback) {
        if (!isMatchDouYinPaySchemePrefix(url)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]非抖音url, 无法支付: " + url);
            BuglessAction.reportCatchException(new IllegalStateException("异常的抖音url"),
                url, BuglessAction.PAY_ERROR);
            callback.onFailed(getName(), order, ERROR_URL, "scheme异常");
            return;
        }
        Intent intent = createIntent(activity, url);
        if (intent == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]未安装抖音, 无法支付");
            callback.onFailed(getName(), order, ERROR_UNSUPPORTED, "未安装");
        } else {
            SQLog.d(TAG + "[" + getSimpleName() + "]跳转支付界面");
            try {
                // 进入支付页, 后续手动查单
                activity.startActivity(intent);
            } catch (Exception e) {
                BuglessAction.reportCatchException(e, "跳转抖音支付界面失败", BuglessAction.PAY_WEB_PAGE_ERROR);
                SQLog.w(TAG + "[" + getSimpleName() + "]跳转抖音支付界面失败, 无法支付");
                callback.onFailed(getName(), order, ERROR_GO_PAY_PAGE, "无法跳转抖音");
            }
        }
    }

    private void checkOrder(boolean retry) {
        DouYinOrder currentOrder = mCurrentOrder;
        if (currentOrder == null) {
            return;
        }

        String uuid = currentOrder.uuid;
        PayOrderModel order = currentOrder.order;
        PayWayCallback callback = currentOrder.callback;
        SQLog.d(TAG + "[" + getSimpleName() + "]检查订单, moid=" + order.getMoid() + ", uuid=" + uuid);
        CheckOrderManager.getInstance().check(order, uuid, HTTP_PAY_WAY, retry, new CheckOrderCallback() {
            @Override
            public void onSuccess(@NonNull PayOrderModel o, String thirdOrderId) {
                SQLog.i(TAG + "[" + getSimpleName() + "]订单支付成功, uuid=" + thirdOrderId);
                callback.onSuccess(getName(), order);
            }

            @Override
            public void onFailure(@NonNull PayOrderModel o, String thirdOrderId, int code, String msg) {
                SQLog.w(TAG + "[" + getSimpleName() + "]订单未支付成功, code=" + code + ", msg=" + msg
                    + ", uuid=" + thirdOrderId);
                if (code == ERROR_CANCEL) {
                    callback.onCancel(getName(), order);
                } else {
                    callback.onFailed(getName(), order, code, msg);
                }
            }
        });
    }

    @Override
    public void check() {
        // 手动查单, 不重试
        checkOrder(false);
    }

    private static class DouYinOrder {

        /**
         * 抖音的订单号
         */
        @NonNull
        final String uuid;
        @NonNull
        final PayOrderModel order;
        @NonNull
        InternalCallback callback;

        private DouYinOrder(@NonNull String uuid, @NonNull PayOrderModel order, @NonNull InternalCallback callback) {
            this.uuid = uuid;
            this.order = order;
            this.callback = callback;
        }

        boolean match(@Nullable PayOrderModel that) {
            if (that == null) {
                return false;
            }
            String myOrderId = order.getPayInfoModel().getOrderId();
            String thatOrderId = that.getPayInfoModel().getOrderId();
            return myOrderId != null && myOrderId.equals(thatOrderId);
        }
    }

    @NonNull
    private InternalCallback wrapCallback(PayWayCallback callback) {
        if (callback instanceof InternalCallback) {
            return (InternalCallback) callback;
        } else {
            return new InternalCallback(callback);
        }
    }

    /**
     * 回调后重置当前订单
     */
    private class InternalCallback extends UIPayWayCallback {

        public InternalCallback(@Nullable PayWayCallback callback) {
            super(callback);
        }

        @Override
        public void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("success");
            super.onSuccess(payWay, order);
        }

        @Override
        public void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("cancel");
            super.onCancel(payWay, order);
        }

        @Override
        public void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message) {
            resetOrder("failed");
            super.onFailed(payWay, order, code, message);
        }

        private void resetOrder(String scene) {
            DouYinOrder order = mCurrentOrder;
            if (order != null) {
                SQLog.d(TAG + "[" + getSimpleName() + "]" + order.order.getMoid() + "订单回调(" + scene + ")");
            }
            mCurrentOrder = null;
        }
    }

    /**
     * 非空时保证可以跳转
     */
    @Nullable
    private Intent createIntent(Context context, String url) {
        try {
            Uri.parse(url);
        } catch (Exception e) {
            SQLog.e(TAG + "[" + getSimpleName() + "]url解析异常, 无法支付: " + url);
            BuglessAction.reportCatchException(new IllegalStateException("异常的抖音url"),
                url, BuglessAction.PAY_ERROR);
            return null;
        }

        // 提取路径, 后续用来遍历
        String pathAndParameter = null;
        for (String schemePrefix : mSchemePrefix) {
            if (!url.startsWith(schemePrefix)) {
                continue;
            }
            pathAndParameter = url.replace(schemePrefix, "");
            SQLog.d(TAG + "[" + getSimpleName() + "]匹配到" + schemePrefix + ", 提取路径和参数: " + pathAndParameter);
            break;
        }

        if (TextUtils.isEmpty(pathAndParameter)) {
            // 没有匹配提取到路径, 表明url的scheme不对
            SQLog.w(TAG + "[" + getSimpleName() + "]非抖音url: " + url);
            BuglessAction.reportCatchException(new IllegalStateException("异常的抖音url"),
                url, BuglessAction.PAY_ERROR);
            return null;
        }

        Intent intent = new Intent(Intent.ACTION_VIEW);
        if (canHandleUrl(context, url)) {
            // 原始url能跳转
            SQLog.d(TAG + "[" + getSimpleName() + "]使用原始url跳转: " + url);
            intent.setData(Uri.parse(url));
            return intent;
        } else {
            // 原始url不能跳转, 替换scheme尝试
            SQLog.w(TAG + "[" + getSimpleName() + "]原始url无法跳转, 尝试匹配其他scheme");
            for (String schemePrefix : mSchemePrefix) {
                String newSchemeUrl = schemePrefix + pathAndParameter;
                SQLog.d(TAG + "[" + getSimpleName() + "]尝试: " + newSchemeUrl);
                intent.setData(Uri.parse(newSchemeUrl));
                if (canHandleIntent(context, intent)) {
                    SQLog.i(TAG + "[" + getSimpleName() + "]匹配: " + newSchemeUrl);
                    return intent;
                }
            }
        }

        SQLog.w(TAG + "[" + getSimpleName() + "]无应用可以处理跳转" + url);
        return null;
    }

    private boolean isMatchDouYinPaySchemePrefix(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        for (String schemePrefix : mSchemePrefix) {
            if (url.startsWith(schemePrefix)) {
                return true;
            }
        }
        return false;
    }

    private static boolean canHandleUrl(Context context, String url) {
        Uri uri;
        try {
            uri = Uri.parse(url);
        } catch (Exception e) {
            return false;
        }
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(uri);
        return canHandleIntent(context, intent);
    }

    private static boolean canHandleIntent(Context context, Intent intent) {
        PackageManager pm = context.getPackageManager();
        List<ResolveInfo> resolveInfoList = pm.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        return !resolveInfoList.isEmpty();
    }

    private String getSimpleName() {
        return "DouYin";
    }
}
