package com.sy37sdk.order;

import com.sq.tool.logger.SQLog;
import com.sqwan.msdk.SQReportCore;
import com.sqwan.msdk.api.PurchaseReportBean;
import com.sqwan.order.base.PayInfoModel;

public class OrderTrackManager {

    /**
     * 上报给媒体
     */
    public static void reportToMedia(PayInfoModel info, String moid, boolean success) {
        if (info == null) {
            return;
        }
        PurchaseReportBean purchaseReportBean = new PurchaseReportBean();
        purchaseReportBean.setCurrency("RMB");
        purchaseReportBean.setProductName(info.getProductName());
        purchaseReportBean.setPrice((int) info.getMoney());
        purchaseReportBean.setOrderId(moid);
        purchaseReportBean.setCount(1);
        purchaseReportBean.setSuccess(success);
        SQLog.i("支付结果上报媒体: " + purchaseReportBean);
        SQReportCore.getInstance().eventPurchase(purchaseReportBean);
    }
}
