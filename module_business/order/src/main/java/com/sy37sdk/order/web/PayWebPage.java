package com.sy37sdk.order.web;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Toast;
import com.plugin.standard.RealBaseActivity;
import com.sq.tool.logger.SQLog;
import com.sq.webview.SimpleWebHook;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.util.Base64;
import com.sqwan.common.util.CutoutUtil;
import com.sqwan.common.util.EncryptUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.webview.SQCommonJsInterface;
import com.sqwan.common.webview.SQWeb;
import com.sqwan.common.webview.SQWebView;
import com.sqwan.common.webview.UrlWebHook;
import com.sqwan.order.base.PayInfoModel;
import com.sqwan.order.base.PayWay;
import com.sqwan.order.base.SqPayError;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.PayVersionUtil;
import com.sy37sdk.order.third.IPayWay.PayWayCallback;
import com.sy37sdk.order.third.ThirdPayManager;
import com.sy37sdk.order.view.PayConfirmDialog.Builder;
import java.util.HashMap;
import java.util.Map;
import notchtools.geek.com.notchtools.NotchTools;
import org.json.JSONObject;

/**
 * author : 王琪
 * time   : 2021/12/31
 * desc   : 支付页弹窗
 */
public class PayWebPage extends RealBaseActivity {

    private static final String TAG = "【Pay H5】";
    public static final int REQUEST_CODE = 100311;
    public static final int RESULT_PAY_CODE = 0;
    public static final int CODE_PAY_FAILURE = 0;
    public static final int CODE_PAY_SUCCESS = 1;
    public static final int CODE_PAY_CANCEL = 2;
    public static final String BUNDLE_RESULT_PAY = "pay_result";
    public static final String BUNDLE_RESULT_PAY_WAY_INT = "pay_way_int";
    public static final String BUNDLE_RESULT_PAY_AMOUNT = "pay_amount";
    public static final String BUNDLE_RESULT_PAY_VOUCHERS_ID = "pay_vouchers_id";
    public static final String BUNDLE_RESULT_PAY_IS_VOUCHERS = "pay_is_vouchers";
    public static final String BUNDLE_RESULT_PAY_VERSION = "pay_version";
    public static final String BUNDLE_KEY_RESULT_PAY_ERROR = "pay_result_error";
    public static final String BUNDLE_KEY_RESULT_CANCEL_WAY = "pay_result_cancel_way";

    /**
     * js协议, 云闪付功能
     */
    private static final String FUNCTION_NAME_UNION_PAY = "UnionPay";

    /**
     * js协议, 抖音支付功能
     */
    private static final String FUNCTION_NAME_DOU_YIN_WEB_PAY = "DouYinWebPay";

    private static final String BUNDLE_KEY_URL = "url";
    private static final String BUNDLE_KEY_PAY_INFO = "pay_info";
    private static final String BUNDLE_KEY_MOID = "moid";
    private static final String BUNDLE_KEY_PAY_SESSION = "pay_session";
    private SQWebView webView;

    // 支付页10s超时
    private static final int PAY_WEB_LOAD_TIMEOUT = 10000;

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private boolean isLoading;

    private String url;

    /**
     * 主要订单信息
     */
    private PayOrderModel order;

    /**
     * js会传递额外的支付信息
     */
    private final OrderExtraInfo orderExtraInfo = new OrderExtraInfo();

    private View errorLayout;

    private LoadingDialog loadingDialog;

    private ThirdPayManager mThirdPayManager;

    /**
     * @param payUrl 支付页链接, 理论上不为空
     * @param payInfo 基础支付信息
     * @param moid 下单后的订单号
     */
    public static void startForResult(@NonNull Activity activity, String paySession,
        @NonNull String payUrl,
        @Nullable PayInfoModel payInfo, @Nullable String moid) {
        Intent intent = new Intent(activity, PayWebPage.class);
        intent.putExtra(PayWebPage.BUNDLE_KEY_PAY_SESSION, paySession);
        intent.putExtra(PayWebPage.BUNDLE_KEY_URL, payUrl);
        if (payInfo != null) {
            intent.putExtra(PayWebPage.BUNDLE_KEY_PAY_INFO, payInfo.toJson());
        } else {
            intent.putExtra(PayWebPage.BUNDLE_KEY_PAY_INFO, "");
        }
        intent.putExtra(PayWebPage.BUNDLE_KEY_MOID, moid);
        // 等待onActivityResult
        activity.startActivityForResult(intent, PayWebPage.REQUEST_CODE);
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        SQLog.d(TAG + "打开h5支付页");
        initSystemUI();
        setContentView(getIdByName("sysq_m_payweb_dialog", "layout"));

        if (getIntent() != null && getIntent().getExtras() != null) {
            Bundle bundleExtra = getIntent().getExtras();
            String paySession = bundleExtra.getString(BUNDLE_KEY_PAY_SESSION);
            String payInfoJson = bundleExtra.getString(BUNDLE_KEY_PAY_INFO);
            SQLog.d(TAG + "pay session: " + paySession);
            SQLog.d(TAG + "bundle pay info: " + payInfoJson);
            PayInfoModel payInfo = PayInfoModel.fromJson(payInfoJson);
            String moid = bundleExtra.getString(BUNDLE_KEY_MOID);

            SQLog.d(TAG + "pay info: " + payInfo);
            SQLog.d(TAG + "moid: " + moid);
            order = new PayOrderModel(payInfo);
            order.setPaySession(paySession);
            order.setMoid(moid);
            url = bundleExtra.getString(BUNDLE_KEY_URL);
        }

        SQLog.d(TAG + "url=" + url);

        initView();

        mThirdPayManager = new ThirdPayManager(webView);
        mThirdPayManager.init(getContext());

        webView.loadUrl(url);
        showWaitDialog();
    }

    private void initSystemUI() {
        if (getContext() != null && getContext() instanceof Activity) {
            Activity context = myself();
            context.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);// 竖屏
            context.requestWindowFeature(Window.FEATURE_NO_TITLE);
        }
        getWindow().setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        getWindow().getDecorView()
            .setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }

        // 隐藏导航栏
        getWindow().getDecorView()
            .setOnSystemUiVisibilityChangeListener(visibility -> StatusBarUtil.hideSystemUI(getWindow()));
    }

    private void initView() {
        View statusView = findViewById(getIdByName("status_view", "id"));
        FrameLayout flWebContainer = findViewById(getIdByName("fl_web_container", "id"));
        errorLayout = findViewById(getIdByName("ll_web_error_layout", "id"));
        View retryButton = findViewById(getIdByName("btn_web_error_retry", "id"));
        if (retryButton != null) {
            retryButton.setOnClickListener(v -> webView.reload());
        }
        initWebView(getContext());
        flWebContainer.addView(webView);
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) statusView.getLayoutParams();
        int statusHeight = NotchTools.getFullScreenTools().getStatusHeight(getWindow());
        boolean hasNotchScreen = true;
        if (getContext() != null && getContext() instanceof Activity) {
            hasNotchScreen = CutoutUtil.hasNotchScreen(myself());
        }
        // 如果是刘海屏则偏移状态栏高度+60
        if (hasNotchScreen) {
            params.height = statusHeight + 60;
        } else {
            // 否则不偏移
            params.height = 0;
        }
        statusView.setLayoutParams(params);
    }

    private void initWebView(Context context) {
        webView = new SQWebView(context);
        SQWeb.getInstance().bind(webView)
            .replace(new PayUrlWebHook(), UrlWebHook.class)
            .disableLocalH5()
            .addWebHook(new PayWebChromeClient())
            .addWebHook(new PayWebViewClient());
        webView.setBackgroundColor(Color.WHITE);
        WebSettings ws = webView.getSettings();
        ws.setRenderPriority(WebSettings.RenderPriority.HIGH);
        // H5缓存
        ws.setAppCacheMaxSize(1024 * 1024 * 5);
        String appCachePath = context.getApplicationContext().getCacheDir().getAbsolutePath();
        ws.setAppCachePath(appCachePath);
        ws.setAppCacheEnabled(true);
        // 网页缓存
        ws.setCacheMode(WebSettings.LOAD_NO_CACHE);
        ws.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        webView.clearCache(false);

        webView.addJavascriptInterface(new CustomJsObj(webView), SQCommonJsInterface.INTERFACE_NAME);
    }

    private void callJsPurchaseParameters(@Nullable final ValueCallback<String> callback) {
        mHandler.post(() -> {
            if (webView == null) {
                return;
            }
            SQLog.d(TAG + "调用JS获取支付信息");
            webView.evaluateJavascript("javascript:window.getPurchaseParameters('')",
                value -> {
                    if (TextUtils.isEmpty(value)
                        || "null".equalsIgnoreCase(value)
                        || "undefined".equalsIgnoreCase(value)) {
                        // 过滤异常的值
                        return;
                    }
                    SQLog.d(TAG + "支付信息onReceiveValue " + value);
                    parsePurchaseParameters(value, true);
                    if (callback != null) {
                        callback.onReceiveValue(value);
                    }
                });
        });
    }

    private Intent payFailIntent(@NonNull SqPayError error) {
        Intent intent = new Intent();
        intent.putExtra(BUNDLE_RESULT_PAY, CODE_PAY_FAILURE);
        intent.putExtra(BUNDLE_RESULT_PAY_WAY_INT, orderExtraInfo.payMethod);
        intent.putExtra(BUNDLE_KEY_RESULT_PAY_ERROR, error);
        appendOrderExtra(intent);
        return intent;
    }

    private Intent paySuccessIntent() {
        Intent intent = new Intent();
        intent.putExtra(BUNDLE_RESULT_PAY, CODE_PAY_SUCCESS);
        appendOrderExtra(intent);
        return intent;
    }

    private Intent payCancelIntent(String way) {
        Intent intent = new Intent();
        intent.putExtra(BUNDLE_RESULT_PAY, CODE_PAY_CANCEL);
        intent.putExtra(BUNDLE_KEY_RESULT_CANCEL_WAY, way);
        intent.putExtra(BUNDLE_RESULT_PAY_WAY_INT, orderExtraInfo.payMethod);
        appendOrderExtra(intent);
        return intent;
    }

    private void appendOrderExtra(Intent intent) {
        intent.putExtra(BUNDLE_RESULT_PAY_WAY_INT, orderExtraInfo.payMethod);
        intent.putExtra(BUNDLE_RESULT_PAY_AMOUNT, orderExtraInfo.payAmount);
        intent.putExtra(BUNDLE_RESULT_PAY_VOUCHERS_ID, orderExtraInfo.vouchersId);
        intent.putExtra(BUNDLE_RESULT_PAY_IS_VOUCHERS, orderExtraInfo.isVouchers);
        intent.putExtra(BUNDLE_RESULT_PAY_VERSION, orderExtraInfo.payVersion);
    }


    private final PayWayCallback mInternalPayWayCallback = new PayWayCallback() {
        @Override
        public void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            dismiss(paySuccessIntent());
        }

        @Override
        public void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            dismiss(payCancelIntent(payWay.desc));
        }

        @Override
        public void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message) {
            dismiss(payFailIntent(new SqPayError(SqPayError.ERROR_THIRD_PURCHASE, message, code)));
        }
    };

    private void dismiss(Intent intent) {
        myself().setResult(RESULT_PAY_CODE, intent);
        finish();
    }

    private void showToast(String msg) {
        ToastUtil.showToast(getContext(), msg);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            if (webView.canGoBack()) {
                webView.goBack();
            } else {
                showExitPayDialog("backKey");
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showExitPayDialog(String way) {
        SQLog.w(TAG + "显示退出弹窗");
        mThirdPayManager.checkPay(order);
        callJsPurchaseParameters(null);
        new Builder(getContext()).setTitle("交易尚未完成，继续支付？")
            .setCancelable(false)
            .setNegativeButton("返回", v -> {
                SQLog.w(TAG + "手动退出支付页, 取消支付");
                H5PayReporter.trackPayClose(url, order, orderExtraInfo);
                dismiss(payCancelIntent(way));
            })
            .setPositiveButton("确定", null)
            .show();
    }

    private void showWaitDialog() {
        showLoading();
        mHandler.postDelayed(payNetErrorRunnable, PAY_WEB_LOAD_TIMEOUT);
    }

    private final Runnable payNetErrorRunnable = new Runnable() {
        @Override
        public void run() {
            if (isLoading) {
                SQLog.e(TAG + "超过五秒loading。。。网络问题");
                H5PayReporter.trackPayNetError(url, order, orderExtraInfo);
                hideLoading();
                showToast("网络不给力");
            }
        }
    };

    protected int getIdByName(String name, String type) {
        return SqResUtils.getIdByName(name, type, getContext());
    }

    private void showLoading() {
        SQLog.d(TAG + "显示loading");
        if (loadingDialog == null) {
            loadingDialog = new LoadingDialog(getContext());
            loadingDialog.setCancelable(false);
        }
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
        isLoading = true;
    }

    private void hideLoading() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
        isLoading = false;
    }

    private void setLoadingMsg(String msg) {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.setMessage(msg);
        }
    }

    public class PayWebViewClient extends SimpleWebHook {

        private boolean loadingFail;

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            loadingFail = false;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            SQLog.v(TAG + "onPageFinished: " + url);
            hideLoading();
            callJsPurchaseParameters(value -> {
                SQLog.d(TAG + "onReceiveValue 网页加载完成，上报进入支付页");
                H5PayReporter.trackPayInit(url, order, orderExtraInfo);
            });

            if (errorLayout == null) {
                return;
            }

            if (loadingFail) {
                errorLayout.setVisibility(View.VISIBLE);
            } else {
                errorLayout.setVisibility(View.GONE);
            }
        }

        @Override
        public void onReceivedError(WebView webView, String url, int errorCode, String description) {
            hideLoading();
            showToast(description);
            loadingFail = true;
        }

    }

    private class PayUrlWebHook extends UrlWebHook {

        @Override
        public boolean shouldOverrideUrlLoading(final WebView view, String url) {
            SQLog.v(TAG + "shouldOverrideUrlLoading: " + url);
            if (mThirdPayManager.interceptorWithUrl(myself(),
                order, url, null, mInternalPayWayCallback)) {
                return true;
            }
            return super.shouldOverrideUrlLoading(view, url);
        }
    }


    class PayWebChromeClient extends SimpleWebHook {

        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            setLoadingMsg("加载中..." + newProgress + "%");
        }
    }

    public class CustomJsObj extends SQCommonJsInterface {

        public CustomJsObj(@NonNull SQWebView webView) {
            super(webView);
        }

        @JavascriptInterface
        public void checkWX() {
            SQLog.d(TAG + "JS触发微信查单");
            mHandler.post(() -> {
                mThirdPayManager.checkPay(order);
            });
        }

        @JavascriptInterface
        public void closePay() {
            closePay("" + CODE_PAY_FAILURE);
        }

        /**
         * 关闭支付
         * 特别注意，js调用android参数应该为string型，如果为int则默认所有传值都为0
         */
        @JavascriptInterface
        public void closePay(final String flag) {
            SQLog.d(TAG + "JS触发关闭支付, flag=" + flag);
            mHandler.post(() -> {
                int resCode;
                try {
                    resCode = Integer.parseInt(flag);
                } catch (Exception e) {
                    resCode = CODE_PAY_FAILURE;
                }
                if (resCode == CODE_PAY_SUCCESS) {
                    SQLog.i(TAG + "JS返回支付成功");
                    H5PayReporter.trackJsPaySuccess(order, orderExtraInfo);
                    dismiss(paySuccessIntent());
                } else {
                    SQLog.w(TAG + "JS关闭支付");
                    showExitPayDialog("jsClosePay");
                }
            });
        }

        /**
         * 支付宝支付
         *
         * @param json 支付信息
         */
        @JavascriptInterface
        public void enAliPay(String json) {
            SQLog.i(TAG + "JS触发支付宝支付: " + json);
            mHandler.post(() -> {
                try {
                    JSONObject obj = new JSONObject(json);
                    String uuid = obj.optString("uuid");
                    String tradeCode = obj.optString("trade");
                    final String trade = new String(Base64.decode(tradeCode));

                    HashMap<String, String> payTrackParam = new HashMap<>();
                    payTrackParam.put("payUrl", trade);
                    payTrackParam.put("uuid", uuid);
                    H5PayReporter.trackPayAli(order, payTrackParam);

                    orderExtraInfo.payMethod = PayWay.ALI.type;

                    Bundle extra = new Bundle();
                    extra.putString(ThirdPayManager.EXTRA_ORDER_ID, uuid);
                    extra.putString(ThirdPayManager.EXTRA_TRADE_INFO, trade);
                    mThirdPayManager.pay(myself(), PayWay.ALI, order, extra, mInternalPayWayCallback);
                } catch (Exception e) {
                    BuglessAction.reportCatchException(e, json, BuglessAction.S_PAY_FAIL);
                    dismiss(payFailIntent(new SqPayError(SqPayError.ERROR_SDK_INLINE,
                        "支付宝支付数据异常，请重试或联系客服", PayWay.ALI.type)));
                }
            });
        }

        /**
         * 微信支付
         *
         * @param json 参数命令行，json格式
         */
        @JavascriptInterface
        public void enWX(String json) {
            SQLog.i(TAG + "JS触发微信支付: " + json);
            mHandler.post(() -> {
                try {
                    JSONObject obj = new JSONObject(json);
                    String uuid = obj.optString("uuid");
                    String wxReferer = EncryptUtil.decrypt(obj.optString("wx_referer"));
                    final String payUrl = obj.optString("mweb_url");

                    Map<String, String> payTrackParam = new HashMap<>();
                    payTrackParam.put("en_wx", json);
                    payTrackParam.put("uuid", uuid);
                    if (!TextUtils.isEmpty(wxReferer) && !TextUtils.isEmpty(payUrl)) {
                        // 新的正规的微信h5支付
                        payTrackParam.put("wx_standard", "true");
                    } else {
                        // 旧版非官方的微信h5支付
                        payTrackParam.put("wx_standard", "false");
                    }
                    H5PayReporter.trackPayWechat(order, payTrackParam);

                    orderExtraInfo.payMethod = PayWay.WECHAT.type;

                    Bundle extra = new Bundle();
                    extra.putString(ThirdPayManager.EXTRA_PAY_URL, payUrl);
                    extra.putString(ThirdPayManager.EXTRA_REFERER, wxReferer);
                    extra.putString(ThirdPayManager.EXTRA_ORDER_ID, uuid);
                    extra.putString(ThirdPayManager.EXTRA_TRADE_INFO, obj.optString("trade"));
                    mThirdPayManager.pay(myself(), PayWay.WECHAT, order, extra, mInternalPayWayCallback);
                } catch (Exception e) {
                    BuglessAction.reportCatchException(e, json, BuglessAction.S_PAY_FAIL);
                    dismiss(payFailIntent(new SqPayError(SqPayError.ERROR_SDK_INLINE,
                        "微信支付数据异常，请重试或联系客服", PayWay.WECHAT.type)));
                }
            });
        }

        /**
         * 云闪付支付
         *
         * @param json 参数命令行，json格式
         */
        @JavascriptInterface
        public void enUnionPay(String json) {
            SQLog.i(TAG + "JS触发云闪付支付: " + json);
            mHandler.post(() -> {
                try {
                    JSONObject obj = new JSONObject(json);
                    String tn = obj.optString("tn");

                    HashMap<String, String> payTrackParam = new HashMap<>();
                    payTrackParam.put("tn", tn);
                    H5PayReporter.trackPayUnion(order, payTrackParam);

                    orderExtraInfo.payMethod = PayWay.UNION.type;

                    Bundle extra = new Bundle();
                    extra.putString(ThirdPayManager.EXTRA_ORDER_ID, tn);
                    mThirdPayManager.pay(myself(), PayWay.UNION, order, extra, mInternalPayWayCallback);
                } catch (Exception e) {
                    BuglessAction.reportCatchException(e, json, BuglessAction.S_PAY_FAIL);
                    dismiss(payFailIntent(new SqPayError(SqPayError.ERROR_SDK_INLINE,
                        "云闪付支付数据异常，请重试或联系客服", PayWay.UNION.type)));
                }
            });
        }

        /**
         * 抖音支付
         *
         * @param json 参数命令行，json格式
         */
        @JavascriptInterface
        public void enDouYinWebPay(String json) {
            SQLog.i(TAG + "JS触发抖音支付: " + json);
            mHandler.post(() -> {
                try {
                    JSONObject obj = new JSONObject(json);
                    // 交易链接
                    String trade = obj.optString("trade");
                    // 请求来源
                    String referer = obj.optString("referer");
                    // 订单号
                    String uuid = obj.optString("uuid");

                    HashMap<String, String> payTrackParam = new HashMap<>();
                    payTrackParam.put("payUrl", trade);
                    payTrackParam.put("referer", referer);
                    payTrackParam.put("uuid", uuid);
                    H5PayReporter.trackPayDouYin(order, payTrackParam);

                    orderExtraInfo.payMethod = PayWay.DOU_YIN_H5.type;

                    Bundle extra = new Bundle();
                    extra.putString(ThirdPayManager.EXTRA_REFERER, referer);
                    extra.putString(ThirdPayManager.EXTRA_ORDER_ID, uuid);
                    extra.putString(ThirdPayManager.EXTRA_PAY_URL, trade);
                    mThirdPayManager.pay(myself(), PayWay.DOU_YIN_H5, order, extra,
                        mInternalPayWayCallback);
                } catch (Exception e) {
                    BuglessAction.reportCatchException(e, json, BuglessAction.S_PAY_FAIL);
                    dismiss(payFailIntent(new SqPayError(SqPayError.ERROR_SDK_INLINE,
                        "抖音支付数据异常，请重试或联系客服", PayWay.DOU_YIN_H5.type)));
                }
            });
        }

        /**
         * 点击支付时传
         */
        @JavascriptInterface
        public void purchaseParameters(String data) {
            SQLog.i(TAG + "JS更新订单信息: " + data);
            parsePurchaseParameters(data);
            // 上报点击支付埋点
            try {
                JSONObject jsonObject = new JSONObject(data);
                String payMethod = jsonObject.optString("payMethod");
                // 如果没有payMethod，则是页面初始化
                if (TextUtils.isEmpty(payMethod)) {
                    return;
                }
                // 有payMethod，则是点击确认支付
                H5PayReporter.trackPayClick(order, orderExtraInfo);
            } catch (Exception e) {
                SQLog.e(TAG + "purchaseParameters json解析异常: " + data, e);
            }
        }

        @JavascriptInterface
        @Override
        public int checkFeatureStatus(String json) {
            int status = super.checkFeatureStatus(json);
            if (status == SQCommonJsInterface.FEATURE_STATUS_OK) {
                return status;
            }
            boolean isSupport = false;
            try {
                JSONObject jsonObject = new JSONObject(json);
                String featureName = jsonObject.optString("featureName");
                if (FUNCTION_NAME_UNION_PAY.equals(featureName)) {
                    isSupport = mThirdPayManager.isSupport(PayWay.UNION);
                    SQLog.d(TAG + "JS查询当前版本是否支持云闪付功能: " + isSupport);
                } else if (FUNCTION_NAME_DOU_YIN_WEB_PAY.equals(featureName)) {
                    isSupport = mThirdPayManager.isSupport(PayWay.DOU_YIN_H5);
                    SQLog.d(TAG + "JS查询当前版本是否支持抖音支付功能: " + isSupport);
                }
            } catch (Exception e) {
                SQLog.e(TAG + "checkFeatureStatus json解析异常: " + json, e);
            }
            return isSupport ? FEATURE_STATUS_OK : FEATURE_STATUS_NO;
        }
    }

    private void parsePurchaseParameters(String purchaseParametersJson) {
        parsePurchaseParameters(purchaseParametersJson, false);
    }

    private void parsePurchaseParameters(String purchaseParametersJson, boolean urlEncode) {
        try {
            if (urlEncode) {
                purchaseParametersJson = purchaseParametersJson.substring(1, purchaseParametersJson.length() - 1)
                    .replaceAll("\\\\", "");
            }
            JSONObject jsonObject = new JSONObject(purchaseParametersJson);
            String payAmount = jsonObject.optString("payAmount");
            String isVouchers = jsonObject.optString("isVouchers");
            String vouchersId = jsonObject.optString("vouchersId");
            String payMethod = jsonObject.optString("payMethod");
            String payVersion = jsonObject.optString("payVersion");

            PayVersionUtil.webVersion = payVersion;

            if (!TextUtils.isEmpty(payMethod)) {
                orderExtraInfo.payAmount = payAmount;
                orderExtraInfo.isVouchers = isVouchers;
                orderExtraInfo.vouchersId = vouchersId;
                try {
                    orderExtraInfo.payMethod = Integer.parseInt(payMethod);
                } catch (Exception e) {
                    BuglessAction.reportCatchException(e, purchaseParametersJson, BuglessAction.PAY_ERROR);
                }
                orderExtraInfo.payVersion = payVersion;

                SQLog.i(TAG + "更新订单信息: " + jsonObject);
            }
        } catch (Exception e) {
            SQLog.e(TAG + "解析支付信息异常: " + purchaseParametersJson, e);
            BuglessAction.reportCatchException(e, purchaseParametersJson, BuglessAction.PAY_ERROR);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (mThirdPayManager != null) {
            mThirdPayManager.onStart(myself());
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mThirdPayManager != null) {
            mThirdPayManager.onResume(myself());
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mThirdPayManager != null) {
            mThirdPayManager.onPause(myself());
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mThirdPayManager != null) {
            mThirdPayManager.onStop(myself());
        }
    }

    @Override
    public void onDestroy() {
        try {
            if (webView != null) {
                webView.onDestroy();
            }
            if (loadingDialog != null) {
                loadingDialog.dismiss();
            }
        } catch (Exception e) {
            /* no-op */
        }
        super.onDestroy();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        OnActivityResultEvent onActivityResultEvent = new OnActivityResultEvent(requestCode, resultCode, data);
        EventDispatcher.getInstance().dispatcherActivityResultListener(onActivityResultEvent);
        if (mThirdPayManager != null) {
            mThirdPayManager.onActivityResult(myself(), requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode != PermissionHelper.SQ_REQUEST_PERMISSION_CODE) {
            return;
        }
        PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    /**
     * 会被替换继承类, 不能直接当做Activity使用
     *
     * @see RealBaseActivity
     * @see com.plugin.standard.BaseActivity
     */
    private Activity myself() {
        return (Activity) getContext();
    }

    /**
     * 网页传递过来的额外支付信息, 上报用
     */
    public static class OrderExtraInfo {

        // 实付金额
        public String payAmount = "";
        // 是否使用了代金券 1是 0否
        public String isVouchers = "0";
        // 代金券id
        public String vouchersId = "";
        /**
         * 支付方式, 对应
         *
         * @see PayWay
         */
        public int payMethod = PayWay.UNKNOWN.type;
        // 网页支付版本号
        public String payVersion = "3.1";
    }
}
