package com.sy37sdk.order.nat.bean;

import android.os.Parcel;
import android.os.Parcelable;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public class Coupon implements Parcelable {

    private String code;
    private String uname;
    private int status;
    private String etime;
    private int useStatus;
    private int pidgidstatus;
    private String name;
    private float amount;
    private int minAmount;
    private int type;
    private int moneyStatus;
    private boolean isSelect;

    /***
     *  是否可用
     */
    public boolean isAvailable() {
        return pidgidstatus == 1 && moneyStatus == 1;
    }

    public String getName() {
        return name;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public boolean isSelect() {
        return isSelect;
    }

    public String getCode() {
        return code;
    }

    public float getAmount() {
        return amount;
    }

    public int getMinAmount() {
        return minAmount;
    }

    public long getEtime() {
        return Date.parse(etime);
    }

    @Override
    public String toString() {
        return "{code:" + code
                + ", uname:" + uname
                + ", status:" + status
                + ", etime:" + etime
                + ", useStatus:" + useStatus
                + ", pidgidstatus:" + pidgidstatus
                + ", name:" + name
                + ", amount:" + amount
                + ", minAmount:" + minAmount
                + ", type:" + type
                + ", moneyStatus:" + moneyStatus
                + ", isAvailable:" + isAvailable()
                + "}";
    }

    public static List<Coupon> fromJson(String json) throws JSONException {
        List<Coupon> coupons = new ArrayList<>();
        JSONObject jsonObject = new JSONObject(json);
        if(jsonObject.has("coupons")) {
            JSONArray jsonArray = jsonObject.getJSONArray("coupons");
            for(int index = 0; index < jsonArray.length(); index++) {
                JSONObject couponJson = jsonArray.getJSONObject(index);
                Coupon coupon = new Coupon();
                coupon.code = couponJson.optString("CODE");
                coupon.uname = couponJson.optString("UNAME");
                coupon.status = couponJson.optInt("STATUS");
                coupon.etime = couponJson.optString("ETIME");
                coupon.useStatus = couponJson.optInt("USESTATUS");
                coupon.pidgidstatus = couponJson.optInt("PIDGIDSTATUS");
                coupon.name = couponJson.optString("NAME");
                coupon.amount = (float) couponJson.optDouble("AMOUNT");
                coupon.minAmount = couponJson.optInt("MIN_AMOUNT");
                coupon.type = couponJson.optInt("TYPE");
                coupon.moneyStatus = couponJson.optInt("MONEYSTATUS");
                coupons.add(coupon);
            }
        }
        return coupons;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.code);
        dest.writeString(this.uname);
        dest.writeInt(this.status);
        dest.writeString(this.etime);
        dest.writeInt(this.useStatus);
        dest.writeInt(this.pidgidstatus);
        dest.writeString(this.name);
        dest.writeFloat(this.amount);
        dest.writeInt(this.minAmount);
        dest.writeInt(this.type);
        dest.writeInt(this.moneyStatus);
        dest.writeByte(this.isSelect ? (byte) 1 : (byte) 0);
    }

    public void readFromParcel(Parcel source) {
        this.code = source.readString();
        this.uname = source.readString();
        this.status = source.readInt();
        this.etime = source.readString();
        this.useStatus = source.readInt();
        this.pidgidstatus = source.readInt();
        this.name = source.readString();
        this.amount = source.readFloat();
        this.minAmount = source.readInt();
        this.type = source.readInt();
        this.moneyStatus = source.readInt();
        this.isSelect = source.readByte() != 0;
    }

    public Coupon() {
    }

    protected Coupon(Parcel in) {
        this.code = in.readString();
        this.uname = in.readString();
        this.status = in.readInt();
        this.etime = in.readString();
        this.useStatus = in.readInt();
        this.pidgidstatus = in.readInt();
        this.name = in.readString();
        this.amount = in.readFloat();
        this.minAmount = in.readInt();
        this.type = in.readInt();
        this.moneyStatus = in.readInt();
        this.isSelect = in.readByte() != 0;
    }

    public static final Parcelable.Creator<Coupon> CREATOR = new Parcelable.Creator<Coupon>() {
        @Override
        public Coupon createFromParcel(Parcel source) {
            return new Coupon(source);
        }

        @Override
        public Coupon[] newArray(int size) {
            return new Coupon[size];
        }
    };
}
