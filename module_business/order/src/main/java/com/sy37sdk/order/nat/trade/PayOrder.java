package com.sy37sdk.order.nat.trade;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/28
 */
public class PayOrder {

    private List<String> para;

    private String action;

    private String method;

    private String tn;

    private String trade;

    private String uuid;

    private int refundswitch;

    //使用webView拉起微信支付的url
    private String mwebUrl;

    //使用webView拉起微信支付的url时需携带的参数
    private String wxReferer;

    public List<String> getPara() {
        return para;
    }

    public String getAction() {
        return action;
    }

    public String getMethod() {
        return method;
    }

    public String getTn() {
        return tn;
    }

    public String getTrade() {
        return trade;
    }

    public String getMwebUrl() {
        return mwebUrl;
    }

    public String getWxReferer() {
        return wxReferer;
    }

    public String getUuid() {
        return uuid;
    }

    public int getRefundswitch() {
        return refundswitch;
    }

    @Override
    public String toString() {
        return "action:" + action
                + ", method:" + method
                + ", tn:" + tn
                + ", trade:" + trade
                + ", uuid:" + uuid
                + ", refundswitch:" + refundswitch;
    }

    public static PayOrder fromJson(String json) throws JSONException {
        PayOrder order = new PayOrder();
        JSONObject jsonObject = new JSONObject(json);
        order.action = jsonObject.optString("action");
        order.method = jsonObject.optString("method");
        order.tn = jsonObject.optString("tn");
        order.trade = jsonObject.optString("trade");
        order.uuid = jsonObject.optString("uuid");
        order.refundswitch = jsonObject.optInt("refundswitch");
        order.mwebUrl = jsonObject.optString("mweb_url");
        order.wxReferer = jsonObject.optString("wx_referer");
        return order;
    }

}
