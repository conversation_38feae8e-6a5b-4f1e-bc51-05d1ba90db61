package com.sy37sdk.order.nat.trade;

import android.content.Context;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebView;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sy37sdk.order.nat.bean.Order;
import com.sqwan.common.util.Base64;
import com.sqwan.common.util.EncryptUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SDKError;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.webview.SQWeb;
import com.sqwan.common.webview.SQWebView;
import com.sqwan.common.webview.UrlWebHook;
import com.sy37sdk.order.nat.NatPayReporter;
import com.sy37sdk.order.PayConstants;
import com.sy37sdk.order.PayVersionUtil;
import com.sy37sdk.order.nat.bean.Coupon;
import com.sy37sdk.order.nat.BaseNativePayPresenter;
import com.sy37sdk.order.view.PayConfirmDialog;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TradePayPresenter extends BaseNativePayPresenter<ITradeView> implements ITradePresenter {

    private static final String LAST_SUCCESS_PAY_WAY = "LAST_SUCCESS_PAY_WAY";

    // 默认显示的充值方式的数量
    private static final int DEFAULT_SHOW_PAY_WAY_NUMBER = 2;

    OrderData orderLogic;

    Order mOrder;

    ArrayList<Coupon> mCoupons;

    ArrayList<NativePayWay> nativePayWays;

    private Coupon selectCoupon;

    private String pway;

    /**
     * 微信支付订单ID
     */
    String wxOrderId;

    public TradePayPresenter(Context context, ITradeView view) {
        super(context, view);
        view.setPresenter(this);
        orderLogic = OrderData.getInstance(context);
    }

    public void setOrder(Order order) {
        this.mOrder = order;
        super.setOrder(order);
    }

    @Override
    public int getBuglessPayErrorActionType() {
        return BuglessAction.PAY_NATIVE_PAGE_ERROR;
    }

    @Override
    public void initData() {
        super.initData();
        showView();
        initPayWay();
        //2022.7.13 屏蔽原生支付的代金券
//        initCoupon();
    }

    /**
     * 默认支付方式
     *
     * @return
     */
    private ArrayList<NativePayWay> defaultPayWays() {
        ArrayList<NativePayWay> defaultPayWays = new ArrayList<>();
        NativePayWay wxWay = defaultNativePayWay(NativePayWay.PWAY_KEY_WECHAT);
        defaultPayWays.add(wxWay);
        NativePayWay zfbWay = defaultNativePayWay(NativePayWay.PWAY_KEY_ALIPAY);
        defaultPayWays.add(zfbWay);
        return defaultPayWays;
    }

    /**
     * 创建默认的支付item
     *
     * @param payWayKey 支付方式
     * @return 默认支付的item
     */
    private NativePayWay defaultNativePayWay(String payWayKey) {
        NativePayWay nativePayWay = new NativePayWay();
        nativePayWay.setKey(payWayKey);
        // 默认显示
        nativePayWay.setOpen("1");
        // 文字
        nativePayWay.setWayChinese(PayJsonParser.getWayChineseByKey(payWayKey));
        // 支付方式
        nativePayWay.setWay(PayJsonParser.getWayByKey(payWayKey));
        // 默认显示空
        nativePayWay.setPromote("");
        // 资源id
        nativePayWay.setResId(PayJsonParser.getResByKey(payWayKey));
        // 支付的说明
        nativePayWay.setPWayTip("");
        return nativePayWay;
    }

    private void showView() {
        if (mView != null && mOrder != null) {
            mView.showProductName(mOrder.getDpt());
            mView.showProductPrice(mOrder.getMoney() + "");
        }
    }

    @Override
    public View getView() {
        return (View) mView;
    }

    @Override
    public void pay(String payWay) {
        String payMethod = null;

        switch (payWay) {
            case NativePayWay.PAY_WAY_ALIPAY:
                payMethod = PayConstants.PAY_METHOD_ZFB;
                break;
            case NativePayWay.PAY_WAY_WECHAT:
                payMethod = PayConstants.PAY_METHOD_WECHAT;
                break;
            case NativePayWay.PAY_WAY_HUABEI:
                payMethod = PayConstants.PAY_METHOD_HUABEI;
                break;
            case NativePayWay.PAY_WAY_WALLET:
                payMethod = PayConstants.PAY_METHOD_WALLET;
                break;
            case NativePayWay.PAY_WAY_LABOR:
                payMethod = PayConstants.PAY_WAY_LABOR;
                break;
            default:
                payMethod = PayConstants.PAY_WAY_UNKNOWN;
                break;
        }
        if (mOrder != null && !TextUtils.isEmpty(payMethod)) {
            if (selectCoupon != null) {
                mOrder.setPayAmount((mOrder.getMoney() - selectCoupon.getAmount()) + "");
                mOrder.setIsVouchers("1");
                mOrder.setVouchersId(selectCoupon.getCode());
            } else {
                mOrder.setPayAmount(mOrder.getMoney() + "");
                mOrder.setIsVouchers("0");
                mOrder.setVouchersId("0");
            }
            mOrder.setPayMethod(payMethod);
            setCurrentPayMethod(payMethod);
            mOrder.setPayChannel(PayConstants.PAY_CHANNEL_NATIVE);
            setOrder(mOrder);
            NatPayReporter.trackPayClick(mOrder.getMoid(), mOrder.getMoney() + "", mOrder.getPayAmount(), PayConstants.PAY_CHANNEL_NATIVE, currentPayMethod, PayVersionUtil.nativeVersion, mOrder.getIsVouchers(), mOrder.getVouchersId());
        }
        pway = payWay;
        if (pway.equals(NativePayWay.PAY_WAY_WALLET)) {
            //如果选择了代金券,弹提示
            if (selectCoupon != null) {
                new PayConfirmDialog.Builder(context).setTitle("使用钱包支付将不可享受代金券优惠，当前订单将按原价支付，是否继续支付？")
                        .setCancelable(false)
                        .setNegativeButton("返回", null)
                        .setPositiveButton("继续", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                walletPay();
                            }
                        }).show();
            } else {
                walletPay();
            }
        } else if (payWay.equals(NativePayWay.PAY_WAY_LABOR)) {
            mView.startLabor();
        } else {
            otherPay(pway);
        }
    }

    private void walletPay() {
        orderLogic.walletPay(mOrder, new OrderData.PayCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean success) {
                        reportPay(true);
                        if (mView != null) {
                            mView.showToast("支付成功！");
                            mView.paySuccess();
                        }
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        LogUtil.e("支付失败code:" + code + ",msg:" + msg);
                        reportPay(false);
                        BuglessAction.reportCatchException(new Exception(), "检查到 " + pway + " 订单支付失败，code = " + SDKError.PAY_FAIL.code + "，msg = " + SDKError.PAY_FAIL.message, getBuglessPayErrorActionType());
                        if (mView != null) {
                            mView.showToast(msg);
                        }
                    }
                });
    }

    private void otherPay(String py) {
        boolean isHuabei = py.equals(NativePayWay.PAY_WAY_HUABEI);
        // 如果是花呗的话，支付方式传 alipay
        final String pway = isHuabei ? NativePayWay.PAY_WAY_ALIPAY : py;
        String couponCode = selectCoupon != null ? selectCoupon.getCode() : "";
        orderLogic.otherPay(mOrder.getMoney(), pway, mOrder.getMoid(), isHuabei, couponCode, new OrderData.PayCallback<PayOrder>() {
            @Override
            public void onSuccess(PayOrder payOrder) {
                LogUtil.i("下单成功：" + payOrder.toString());
                if (pway.equals(NativePayWay.PAY_WAY_ALIPAY)) {
                    String orderInfo = new String(Base64.decode(payOrder.getTrade()));
                    setAliOrderId(payOrder.getUuid());
                    LogUtil.i("alipay:" + orderInfo);
                    toAlipay(orderInfo, true);
                } else if (pway.equals(NativePayWay.PAY_WAY_WECHAT)) {
                    String url = payOrder.getMwebUrl();
                    wxOrderId = payOrder.getUuid();
                    // 设置uuid做检查的。
                    setWxOrderId(wxOrderId);
                    LogUtil.i("wechat-->url:" + url + ", uuid:" + wxOrderId);
                    toWxPayByWebView(url, payOrder.getWxReferer());
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.i("下单失败code:" + code + ", msg: " + msg);
                if (mView != null) {
                    mView.setWxPay(false);
                    mView.showToast(msg);
                }
            }
        });
    }

    //通过webUrl来跳转微信支付
    private void toWxPayByWebView(String url,String wxReferer) {
        HashMap<String, String> extraHeaders = new HashMap<>();
        extraHeaders.put("referer", EncryptUtil.decrypt(wxReferer));
        SQWebView sqWebView = new SQWebView(context);
        SQWeb.getInstance().bind(sqWebView)
            .disableLocalH5()
            .replace(new TradeUrlWebHook(), UrlWebHook.class);
        sqWebView.loadUrl(url, extraHeaders);
    }

    @Override
    public ArrayList<Coupon> getCoupons() {
        return mCoupons;
    }

    @Override
    public void selectCoupon(Coupon coupon) {
        if (coupon != null) {
            setCoupon(coupon);
        }
    }

    @Override
    public void selectNoneCoupon() {
        selectCoupon = null;
        if (mView != null) {
            mView.showCouponNone();
            mView.showOriginalNonePrice();
            mView.showProductPrice(mOrder.getMoney() + "");
        }
    }

    @Override
    public void showAllPayWay() {
        if (mView != null) {
            mView.showAllPayWay(nativePayWays);
        }
    }

    @Override
    public void selectPayWay(int pos) {

    }

    @Override
    public void paySuccess() {
        if (mPayListener != null) {
            mPayListener.onSuccess();
        }
        saveSuccessPayWay(pway);
    }

    @Override
    public void onFailure(int code, String msg) {
        if (mPayListener != null) {
            mPayListener.onFailure(code, msg);
        }
    }

    /**
     * 初始化支付方式
     */
    private void initPayWay() {
        if (mView != null) {
            mView.showLoading();
        }
        orderLogic.getAvailableWays(mOrder.getMoid(), new OrderData.PayCallback<String>() {
            @Override
            public void onSuccess(String pWayJson) {
                if (mView != null) {
                    mView.hideLoading();
                }
                nativePayWays = PayJsonParser.parsePWay(pWayJson);
                initWalletPayWay();
            }

            @Override
            public void onFailure(int code, String msg) {
                if (mView != null) {
                    mView.hideLoading();
                }
                LogUtil.i("初始化支付方式失败,展示默认的支付方式 code:" + code + ", msg:" + msg);
                filterShowPayWay(defaultPayWays());
            }
        });
    }

    private void initWalletPayWay() {
        if (nativePayWays == null || nativePayWays.isEmpty()) {
            filterShowPayWay(defaultPayWays());
            return;
        }
        boolean hasWalletPayWay = false;
        for (NativePayWay nativePayWay : nativePayWays) {
            if (nativePayWay.getKey().equals(NativePayWay.PWAY_KEY_WALLET)) {
                hasWalletPayWay = true;
                break;
            }
        }
        if (hasWalletPayWay) {
            orderLogic.getWalletBalance(mOrder.getMoid(), new OrderData.PayCallback<Wallet>() {
                @Override
                public void onSuccess(Wallet wallet) {
                    if (wallet.getUb() < mOrder.getMoney()) {
                        excludeWalletPayWay();
                    } else {
                        addWalletInfo(wallet);
                        payWayLoaded();
                    }
                }

                @Override
                public void onFailure(int code, String msg) {
                    excludeWalletPayWay();
                }
            });
        }
    }

    private void addWalletInfo(Wallet wallet) {
        if (nativePayWays == null || nativePayWays.isEmpty()) {
            return;
        }
        for (NativePayWay nativePayWay : nativePayWays) {
            if (nativePayWay.getKey().equals(NativePayWay.PWAY_KEY_WALLET)) {
                nativePayWay.setExtra("余额: " + wallet.getUb());
                break;
            }
        }
    }

    private void excludeWalletPayWay() {
        if (nativePayWays == null || nativePayWays.isEmpty()) {
            filterShowPayWay(defaultPayWays());
            return;
        }
        ArrayList<NativePayWay> tempPayWays = new ArrayList<>();
        for (NativePayWay nativePayWay : nativePayWays) {
            if (!nativePayWay.getKey().equals(NativePayWay.PWAY_KEY_WALLET)) {
                tempPayWays.add(nativePayWay);
            }
        }
        nativePayWays = tempPayWays;
        payWayLoaded();
    }

    private void payWayLoaded() {
        filterShowPayWay(nativePayWays);
    }

    /**
     * 获取代金券信息
     */
    private void initCoupon() {
        orderLogic.getCoupons(mOrder.getMoid(), mOrder.getMoney(), mOrder.getDsid()
                , new OrderData.PayCallback<List<Coupon>>() {
                    @Override
                    public void onSuccess(List<Coupon> coupons) {
                        if (coupons != null && !coupons.isEmpty()) {
                            mCoupons = new ArrayList<>();
                            for (Coupon coupon : coupons) {
                                if (coupon.isAvailable()) {
                                    mCoupons.add(coupon);
                                }
                            }
                            if (!mCoupons.isEmpty()) {
                                if (mView != null) {
                                    mView.showCoupons();
                                    Coupon coupon = mCoupons.get(0);
                                    setCoupon(coupon);
                                }
                            }
                        }
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        LogUtil.i("初始化代金券失败 code:" + code + ", msg:" + msg);
                    }
                });
    }

    public void setCoupon(Coupon coupon) {
        selectCoupon = coupon;
        if (mView != null) {
            float originalPrice = mOrder.getMoney();
            float price = originalPrice - coupon.getAmount();
            mView.showCoupon(coupon.getAmount() + "");
            mView.showOriginalPrice(originalPrice + "");
            mView.showProductPrice(price + "");
        }
    }

    /**
     * 保存支付成功的充值方式
     *
     * @param payWay 充值方式
     */
    private void saveSuccessPayWay(@NonNull String payWay) {
        SpUtils.get(this.context).put(LAST_SUCCESS_PAY_WAY, payWay);
    }

    /**
     * 读取最后一次的充值方式
     *
     * @return 充值方式
     */
    private String lastPayWay() {
        return SpUtils.get(this.context).getString(LAST_SUCCESS_PAY_WAY, "");
    }

    /**
     * 过滤真实现实的支付列表
     *
     * @param nativePayWays 可以显示的所有的支付方式
     */
    private void filterShowPayWay(ArrayList<NativePayWay> nativePayWays) {
        if (this.mView == null || nativePayWays == null || nativePayWays.size() == 0) {
            return;
        }
        int lastPayWayIndex = 0;
        // 获取最后一次充值的方式，如果不为空则去查找对应的位置
        String lastPayWay = lastPayWay();
        if (!TextUtils.isEmpty(lastPayWay)) {
            for (int i = 0; i < nativePayWays.size(); i++) {
                if (nativePayWays.get(i).getWay().equalsIgnoreCase(lastPayWay)) {
                    lastPayWayIndex = i;
                    break;
                }
            }
        }

        List<NativePayWay> realNativePayWays;
        boolean more = (lastPayWayIndex < DEFAULT_SHOW_PAY_WAY_NUMBER) && (nativePayWays.size() > DEFAULT_SHOW_PAY_WAY_NUMBER);
        if (more) {
            realNativePayWays = nativePayWays.subList(0, DEFAULT_SHOW_PAY_WAY_NUMBER);
        } else {
            realNativePayWays = nativePayWays;
        }

        //将上次支付的方式置顶
        List<NativePayWay> resultNativePayWays = new ArrayList<>();
        if (realNativePayWays != null && realNativePayWays.size() > 0) {
            for (int i = 0; i < realNativePayWays.size(); i++) {
                if (lastPayWayIndex == i) {
                    resultNativePayWays.add(0, realNativePayWays.get(i));
                } else {
                    resultNativePayWays.add(realNativePayWays.get(i));
                }
            }
        }

        // 上次充值的选项，不在默认显示的范围的时候，直接全部显示
        this.mView.showPayWay(resultNativePayWays, more);
        // 选中到
        this.mView.selectPayWay(0);
    }

    private class TradeUrlWebHook extends UrlWebHook {

        public boolean shouldOverrideUrlLoading(final WebView view, String url) {
            if (url.startsWith("weixin://wap/pay")) {
                SQLog.i("shouldOverrideUrlLoading: 匹配到微信支付协议");
                toWxPay(url);
                return true;
            }
            return super.shouldOverrideUrlLoading(view, url);
        }
    }
}
