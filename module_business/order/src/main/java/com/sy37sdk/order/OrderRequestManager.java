package com.sy37sdk.order;

import android.content.Context;
import android.os.Bundle;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqnetwork.voly.VolleyLog;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public class OrderRequestManager {

    private final Context mContext;

    public OrderRequestManager(Context context) {
        mContext = context;
    }

    /**
     * @param moid 并非37的订单号， 而是支付渠道的单号。 如微信的uuid
     */
    public void checkPay(String moid, String pway, SqHttpCallback<Void> callback) {
        SqRequest.of(OrderUrl.PAY_CHECK)
            .params(generateCommonParamsOfPay(mContext, moid))
            .addParam("uuid", moid)
            .addParam("pway", pway)
            .post(callback, Void.class);
    }

    public static Map<String, String> generateCommonParamsOfPay(Context context, String moid) {
        Map<String, String> params = new HashMap<>();
        SQAppConfig appConfig = ConfigManager.getInstance(context).getSQAppConfig();
        String gid = appConfig.getGameid();
        String pid = appConfig.getPartner();
        String key = ConfigManager.getInstance(context).getAppKey();
        String refer = appConfig.getRefer();
        String time = System.currentTimeMillis() / 1000 + "";
        String uid = ModHelper.get(IAccountMod.class).getUid();
        String signStr = pid + gid + time + key + uid + moid;
        String sign = MD5Util.Md5(signStr).toLowerCase();
        if (VolleyLog.VERBOSE) {
            VolleyLog.i("[Vn]签名原串: %s\n签名结果: %s", signStr, sign);
        }
        params.put("gid", gid);
        params.put("pid", pid);
        params.put("refer", refer);
        params.put("dev", DevLogic.getInstance(context).getValue());
        params.put("time", time);
        params.put("sign", sign);
        params.put("scut", ConfigManager.getInstance(context).getLoginCode() + "");
        params.put("token", ModHelper.get(IAccountMod.class).getToken());
        params.put("gwversion", VersionUtil.gwversion);
        params.put("sversion", VersionUtil.sdkVersion);
        // 宿主版本号
        params.put("host_sdk_version", VersionUtil.getOriginalVersion());
        return params;
    }


    public static Bundle addCommonParamsOfPay(Context context, Bundle params, String s1, String s2) {

        SQAppConfig appConfig = ConfigManager.getInstance(context).getSQAppConfig();

        String gid = appConfig.getGameid();
        String pid = appConfig.getPartner();
        String key = ConfigManager.getInstance(context).getAppKey();
        String refer = appConfig.getRefer();
        String dev = "" + DevLogic.getInstance(context).getValue();
        String time = "" + System.currentTimeMillis() / 1000;
        String comstr = pid + gid + time + key;
        //串签名
        String signStr = MD5Util.Md5(comstr + s1 + s2).toLowerCase();

        params.putString("gid", gid);
        params.putString("pid", pid);
        params.putString("refer", refer);
        params.putString("dev", dev);
        params.putString("time", time);
        params.putString("sign", signStr);
        params.putString("scut", ConfigManager.getInstance(context).getLoginCode() + "");
        if (MultiSdkManager.getInstance().isScut3()) {
            params.putString("scut3", MultiSdkManager.getInstance().getScut3());
        }
        return params;
    }

}
