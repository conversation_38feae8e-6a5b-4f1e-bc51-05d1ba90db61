package com.sy37sdk.order.third.wechat;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.WebView;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.JsonMap;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.third.CheckOrderManager;
import com.sy37sdk.order.third.CheckOrderManager.CheckOrderCallback;
import com.sy37sdk.order.third.CheckablePayWay;
import com.sy37sdk.order.third.IWebPayWay;
import com.sy37sdk.order.third.ThirdPayManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
public class WeChatPayWay implements IWebPayWay, CheckablePayWay {

    /**
     * 后端接口用
     */
    private static final String HTTP_PAY_WAY = "wxpay";

    public static final int ERROR_UNSUPPORTED = 100;
    public static final int ERROR_PARAM = 101;
    public static final int ERROR_ORDER_ID = 102;
    public static final int ERROR_NO_ORDER = 103;
    public static final int ERROR_ORDER_NOT_MATCH = 104;
    public static final int ERROR_URL = 105;
    public static final int ERROR_GO_PAY_PAGE = 106;

    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final WebView mWebView;
    @Nullable
    private WeChatOrder mCurrentOrder;

    public WeChatPayWay(WebView webView) {
        mWebView = webView;
    }

    @Override
    public void init(@NonNull Context context) {

    }

    @Override
    public void onStart(@NonNull Activity activity) {

    }

    @Override
    public void onResume(@NonNull Activity activity) {
        SQLog.v(TAG + "[" + getSimpleName() + "]onResume, " + mCurrentOrder);
        // 从微信支付页回来, 查单
        checkOrder(true);
    }

    @Override
    public void onPause(@NonNull Activity activity) {

    }

    @Override
    public void onStop(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data) {

    }

    /**
     * @see ThirdPayManager#EXTRA_ORDER_ID 微信订单号
     * @see ThirdPayManager#EXTRA_REFERER refer
     * @see ThirdPayManager#EXTRA_PAY_URL 新的跳转链接
     * @see ThirdPayManager#EXTRA_TRADE_INFO 旧的跳转链接
     */
    @Override
    public void pay(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable Bundle extra,
        @Nullable PayWayCallback callback) {
        SQLog.d(TAG + "[" + getSimpleName() + "]支付:\n" + order);
        SQLog.d(TAG + "[" + getSimpleName() + "]extra: " + extra);
        InternalCallback uiCallback = wrapCallback(callback);
        if (extra == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无额外参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "无参数");
            return;
        }
        // 查单用的订单号
        String uuid = extra.getString(ThirdPayManager.EXTRA_ORDER_ID);
        if (TextUtils.isEmpty(uuid)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无uuid, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_ORDER_ID, "订单号异常");
            return;
        }

        assert uuid != null;

        WeChatOrder oldOrder = mCurrentOrder;
        if (oldOrder != null) {
            SQLog.w(TAG + "[" + getSimpleName() + "]覆盖订单: " + oldOrder.order.getMoid());
            JsonMap map = new JsonMap();
            map.put("old_moid", oldOrder.order.getMoid());
            map.put("old_uuid", oldOrder.uuid);
            map.put("new_moid", order.getMoid());
            map.put("new_uuid", uuid);
            BuglessAction.reportCatchException(new IllegalStateException("微信覆盖订单"),
                map.toString(), BuglessAction.PAY_ERROR);
        }

        mCurrentOrder = new WeChatOrder(uuid, order, uiCallback);
        String payUrl = extra.getString(ThirdPayManager.EXTRA_PAY_URL, "");
        String refer = extra.getString(ThirdPayManager.EXTRA_REFERER);
        String trade = extra.getString(ThirdPayManager.EXTRA_TRADE_INFO);
        if (!TextUtils.isEmpty(refer) && !TextUtils.isEmpty(payUrl)) {
            SQLog.d(TAG + "[" + getSimpleName() + "]重定向到支付链接: " + payUrl + ", refer=" + refer);
            // 新的正规的微信h5支付
            Map<String, String> extraHeaders = new HashMap<>();
            extraHeaders.put("referer", refer);
            // 会走到interceptorWithUrl
            mHandler.post(() -> mWebView.loadUrl(payUrl, extraHeaders));
        } else if (!TextUtils.isEmpty(trade)) {
            // 旧版非官方的微信h5支付
            String prepayUrl = trade + "&type=android";
            SQLog.w(TAG + "[" + getSimpleName() + "](旧)支付链接: " + prepayUrl);
            payByUrl(activity, order, prepayUrl, uiCallback);
        } else {
            SQLog.e(TAG + "[" + getSimpleName() + "]无有效参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "参数异常");
        }
    }

    @NonNull
    @Override
    public PayWay getName() {
        return PayWay.WECHAT;
    }

    @Override
    public boolean interceptorWithUrl(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable String url,
        @Nullable Bundle extra, @Nullable PayWayCallback callback) {
        if (url == null || !url.startsWith("weixin://wap/pay")) {
            return false;
        }
        InternalCallback uiCallback = wrapCallback(callback);
        SQLog.i(TAG + "[" + getSimpleName() + "]匹配到微信支付协议: " + url);
        WeChatOrder currentOrder = mCurrentOrder;
        if (currentOrder == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无前置订单, 直接支付失败");
            uiCallback.onFailed(getName(), order, ERROR_NO_ORDER, "无订单");
        } else if (!currentOrder.match(order)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]订单对应不上, 当前:"
                + currentOrder.order.getMoid() + ", 实际:" + order.getMoid() + ", 直接支付失败");
            JsonMap map = new JsonMap();
            map.put("expect_order_id", currentOrder.order.getMoid());
            map.put("exact_order_id", order.getMoid());
            BuglessAction.reportCatchException(new IllegalStateException("微信订单不对应"),
                map.toString(), BuglessAction.PAY_ERROR);
            uiCallback.onFailed(getName(), order, ERROR_ORDER_NOT_MATCH, "订单异常");
        } else {
            currentOrder.callback = uiCallback;
            payByUrl(activity, order, url, uiCallback);
        }
        return true;
    }

    private void payByUrl(@NonNull Activity activity, @NonNull PayOrderModel order, @NonNull String url,
        @NonNull PayWayCallback callback) {
        if (!url.startsWith("weixin")) {
            SQLog.e(TAG + "[" + getSimpleName() + "]非微信url, 无法支付: " + url);
            BuglessAction.reportCatchException(new IllegalStateException("异常的微信url"),
                url, BuglessAction.PAY_ERROR);
            callback.onFailed(getName(), order, ERROR_URL, "scheme异常");
            return;
        }
        Uri uri;
        try {
            uri = Uri.parse(url);
        } catch (Exception e) {
            SQLog.e(TAG + "[" + getSimpleName() + "]url解析异常, 无法支付: " + url);
            BuglessAction.reportCatchException(new IllegalStateException("异常的微信url"),
                url, BuglessAction.PAY_ERROR);
            callback.onFailed(getName(), order, ERROR_URL, "URL异常");
            return;
        }
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(uri);
        PackageManager packageManager = activity.getPackageManager();
        List<ResolveInfo> resolveInfoList = packageManager.queryIntentActivities(intent,
            PackageManager.MATCH_DEFAULT_ONLY);
        if (!resolveInfoList.isEmpty()) {
            SQLog.d(TAG + "[" + getSimpleName() + "]跳转支付界面");
            try {
                // 进入微信, 后续手动查单
                activity.startActivity(intent);
            } catch (Exception e) {
                BuglessAction.reportCatchException(e, "跳转微信支付界面失败", BuglessAction.PAY_WEB_PAGE_ERROR);
                SQLog.w(TAG + "[" + getSimpleName() + "]跳转微信支付界面失败, 无法支付");
                callback.onFailed(getName(), order, ERROR_GO_PAY_PAGE, "无法跳转微信");
            }
        } else {
            SQLog.w(TAG + "[" + getSimpleName() + "]未安装微信, 无法支付");
            callback.onFailed(getName(), order, ERROR_UNSUPPORTED, "未安装");
        }
    }

    private void checkOrder(boolean retry) {
        WeChatOrder currentOrder = mCurrentOrder;
        if (currentOrder == null) {
            return;
        }

        String uuid = currentOrder.uuid;
        PayOrderModel order = currentOrder.order;
        PayWayCallback callback = currentOrder.callback;
        SQLog.d(TAG + "[" + getSimpleName() + "]检查订单, moid=" + order.getMoid() + ", uuid=" + uuid);
        CheckOrderManager.getInstance().check(order, uuid, HTTP_PAY_WAY, retry, new CheckOrderCallback() {
            @Override
            public void onSuccess(@NonNull PayOrderModel o, String thirdOrderId) {
                SQLog.i(TAG + "[" + getSimpleName() + "]订单支付成功, uuid=" + thirdOrderId);
                callback.onSuccess(getName(), order);
            }

            @Override
            public void onFailure(@NonNull PayOrderModel o, String thirdOrderId, int code, String msg) {
                SQLog.w(TAG + "[" + getSimpleName() + "]订单未支付成功, code=" + code + ", msg=" + msg
                    + ", uuid=" + thirdOrderId);
                if (code == ERROR_CANCEL) {
                    callback.onCancel(getName(), order);
                } else {
                    callback.onFailed(getName(), order, code, msg);
                }
            }
        });
    }

    @Override
    public void check() {
        // 手动查单, 不重试
        checkOrder(false);
    }

    private static class WeChatOrder {

        /**
         * 微信的订单号
         */
        @NonNull
        final String uuid;
        @NonNull
        final PayOrderModel order;
        @NonNull
        InternalCallback callback;

        private WeChatOrder(@NonNull String uuid, @NonNull PayOrderModel order, @NonNull InternalCallback callback) {
            this.uuid = uuid;
            this.order = order;
            this.callback = callback;
        }

        boolean match(@Nullable PayOrderModel that) {
            if (that == null) {
                return false;
            }
            String myOrderId = order.getPayInfoModel().getOrderId();
            String thatOrderId = that.getPayInfoModel().getOrderId();
            return myOrderId != null && myOrderId.equals(thatOrderId);
        }
    }

    @NonNull
    private InternalCallback wrapCallback(PayWayCallback callback) {
        if (callback instanceof InternalCallback) {
            return (InternalCallback) callback;
        } else {
            return new InternalCallback(callback);
        }
    }

    /**
     * 回调后重置当前订单
     */
    private class InternalCallback extends UIPayWayCallback {

        public InternalCallback(@Nullable PayWayCallback callback) {
            super(callback);
        }

        @Override
        public void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("success");
            super.onSuccess(payWay, order);
        }

        @Override
        public void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("cancel");
            super.onCancel(payWay, order);
        }

        @Override
        public void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message) {
            resetOrder("failed");
            super.onFailed(payWay, order, code, message);
        }

        private void resetOrder(String scene) {
            WeChatOrder order = mCurrentOrder;
            if (order != null) {
                SQLog.d(TAG + "[" + getSimpleName() + "]" + order.order.getMoid() + "订单回调(" + scene + ")");
            }
            mCurrentOrder = null;
        }
    }

    private String getSimpleName() {
        return "WeChat";
    }
}
