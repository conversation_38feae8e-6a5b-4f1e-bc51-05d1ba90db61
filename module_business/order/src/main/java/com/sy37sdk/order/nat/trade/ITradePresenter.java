package com.sy37sdk.order.nat.trade;

import com.sqwan.common.mvp.IPresenter;
import com.sy37sdk.order.nat.bean.Coupon;

import java.util.ArrayList;

public interface ITradePresenter extends IPresenter {

    void pay(String payWay);

    ArrayList<Coupon> getCoupons();

    void selectCoupon(Coupon coupon);

    void selectNoneCoupon();

    void showAllPayWay();

    void selectPayWay(int pos);

    /**
     * 查询微信支付结果
     */
    void checkWxPay();

    void onFailure(int code,String  msg);

    void paySuccess();
}
