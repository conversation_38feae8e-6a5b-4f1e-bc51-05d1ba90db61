package com.sy37sdk.order.third;

import android.app.Activity;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.webkit.WebView;
import com.sy37sdk.order.PayOrderModel;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
public interface IWebPayWay extends IPayWay {

    /**
     * @see android.webkit.WebViewClient#shouldOverrideUrlLoading(WebView, String)
     */
    boolean interceptorWithUrl(@NonNull Activity activity, @NonNull PayOrderModel order,
        @Nullable String url, @Nullable Bundle extra, @Nullable PayWayCallback callback);
}
