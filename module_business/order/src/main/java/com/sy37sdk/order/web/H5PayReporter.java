package com.sy37sdk.order.web;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.order.base.PayInfoModel;
import com.sy37sdk.order.PayConstants;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.web.PayWebPage.OrderExtraInfo;
import java.util.HashMap;
import java.util.Map;

public class H5PayReporter {

    /**
     * 支付网页加载异常
     */
    public static void trackPayNetError(String url, PayOrderModel order, OrderExtraInfo extraInfo) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extraInfo);
        params.put("pay_url", url);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_NET_ERROR, params);
    }

    /**
     * 进入支付页
     */
    public static void trackPayInit(String url, PayOrderModel order, OrderExtraInfo extraInfo) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extraInfo);
        params.put("pay_url", url);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.pay_init, params);
    }

    /**
     * 点击支付
     */
    public static void trackPayClick(PayOrderModel order, OrderExtraInfo extraInfo) {
        report(SqTrackAction2.pay_click, order, extraInfo);
    }

    /**
     * h5微信支付
     */
    public static void trackPayWechat(PayOrderModel order, Map<String, String> extra) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extra);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_WX, params);
    }

    /**
     * h5抖音支付
     */
    public static void trackPayDouYin(PayOrderModel order, Map<String, String> extra) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extra);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_DOU_YIN, params);
    }

    /**
     * h5云闪付
     */
    public static void trackPayUnion(PayOrderModel order, Map<String, String> extra) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extra);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_UNION, params);
    }

    /**
     * h5支付宝支付
     */
    public static void trackPayAli(PayOrderModel order, Map<String, String> extra) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extra);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_ALI, params);
    }

    /**
     * js主动调用支付成功
     */
    public static void trackJsPaySuccess(PayOrderModel order, OrderExtraInfo extra) {
        report(SqTrackAction2.PAY_H5_JS_SUCCESS, order, extra);
    }

    /**
     * 关闭支付页
     */
    public static void trackPayClose(String url, PayOrderModel order, OrderExtraInfo extraInfo) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extraInfo);
        params.put("pay_url", url);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.pay_close_click, params);
    }

    /**
     * 重试查单, 支付成功
     */
    public static void trackRetryCheckSuccess(PayOrderModel order, String thirdOrderId, String payWay) {
        HashMap<String, String> params = createParams(order);
        params.put(SqTrackKey.third_order_id, thirdOrderId);
        params.put(SqTrackKey.pay_method_name, payWay);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_RETRY_CHECK_SUCCESS, params);
    }

    private static void report(@NonNull SqTrackAction2 action, PayOrderModel order, OrderExtraInfo extra) {
        HashMap<String, String> params = createParams(order);
        appendExtra(params, extra);
        SqTrackActionManager2.getInstance().trackAction(action, params);
    }

    private static HashMap<String, String> createParams(PayOrderModel order) {
        HashMap<String, String> params = new HashMap<>();
        if (order == null) {
            return params;
        }
        params.put(SqTrackKey.pay_session, order.getPaySession());
        params.put(SqTrackKey.order_id, order.getMoid());
        params.put(SqTrackKey.pay_channels, PayConstants.PAY_CHANNEL_WEB);

        PayInfoModel info = order.getPayInfoModel();
        params.put(SqTrackKey.cp_order_id, info.getOrderId());
        params.put(SqTrackKey.order_amount, info.getMoney() + "");
        return params;
    }

    private static void appendExtra(Map<String, String> params, OrderExtraInfo extra) {
        if (extra == null) {
            return;
        }
        params.put(SqTrackKey.pay_amount, extra.payAmount);
        params.put(SqTrackKey.pay_method, String.valueOf(extra.payMethod));
        params.put(SqTrackKey.pay_version, extra.payVersion);
        params.put(SqTrackKey.is_vouchers, extra.isVouchers);
        String vouchersId = extra.vouchersId;
        params.put(SqTrackKey.vouchers_id, TextUtils.isEmpty(vouchersId) || vouchersId.equals("[]") ? "0" : vouchersId);
    }

    private static void appendExtra(Map<String, String> params, Map<String, String> extra) {
        if (extra == null || extra.isEmpty()) {
            return;
        }
        params.putAll(extra);
    }
}
