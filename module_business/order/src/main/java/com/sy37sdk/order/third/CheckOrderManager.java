package com.sy37sdk.order.third;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.order.OrderRequestManager;
import com.sy37sdk.order.OrderTrackManager;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.web.H5PayReporter;

/**
 * <AUTHOR>
 * @since 2024/8/8
 */
public class CheckOrderManager {

    private static final String TAG = "【Pay Check】";
    private static volatile CheckOrderManager sInstance;
    /**
     * 设置延迟多少秒后进行重试
     */
    private final static int RETRY_CHECK_DELAY = 60 * 1000;

    public static CheckOrderManager getInstance() {
        if (sInstance == null) {
            synchronized (CheckOrderManager.class) {
                if (sInstance == null) {
                    sInstance = new CheckOrderManager();
                }
            }
        }

        return sInstance;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private OrderRequestManager mRequestManager;

    private CheckOrderManager() {
    }

    @Nullable
    private OrderRequestManager rm() {
        if (mRequestManager != null) {
            return mRequestManager;
        }
        Context context = SQContextWrapper.getApplicationContext();
        if (context != null) {
            mRequestManager = new OrderRequestManager(context);
        }
        return mRequestManager;
    }

    public void check(@NonNull PayOrderModel order, String thirdOrderId, String payWay,
        @Nullable CheckOrderCallback callback) {
        check(order, thirdOrderId, payWay, true, callback);
    }

    /**
     * @param retry 未支付是否重试, 用于补充上报支付成功给媒体
     */
    public void check(@NonNull PayOrderModel order, String thirdOrderId, String payWay, boolean retry,
        @Nullable CheckOrderCallback callback) {
        OrderRequestManager rm = rm();
        if (rm == null) {
            SQLog.e(TAG + "rm异常, 无法查询支付状态, moid=" + order.getMoid()
                + ", " + thirdOrderId + "(" + payWay + ")");
            if (callback != null) {
                callback.onFailure(order, thirdOrderId, CheckOrderCallback.ERROR_CONTEXT_NULL, "无法查询支付状态");
            }
            return;
        }
        if (TextUtils.isEmpty(thirdOrderId) || TextUtils.isEmpty(payWay)) {
            SQLog.w(TAG + "无法查询支付状态, moid=" + order.getMoid()
                + ", " + thirdOrderId + "(" + payWay + ")");
            if (callback != null) {
                callback.onFailure(order, thirdOrderId, CheckOrderCallback.ERROR_PARAM, "无法查询支付状态");
            }
            return;
        }
        SQLog.d(TAG + "查询支付状态, moid=" + order.getMoid() + ", " + thirdOrderId + "(" + payWay + ")");
        rm.checkPay(thirdOrderId, payWay, new SqHttpCallback<Void>() {
            @Override
            public void onSuccess(Void unused) {
                SQLog.i(TAG + order.getMoid() + "已支付成功");
                if (callback != null) {
                    callback.onSuccess(order, thirdOrderId);
                }
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.w(TAG + order.getMoid() + "未支付成功, code=" + state + ", msg=" + msg);
                if (retry) {
                    // 过一会重试, 但是不需要回调
                    retryCheckAfterDelay(order, thirdOrderId, payWay);
                }
                if (state == -1) {
                    // -1认为是取消支付
                    if (callback != null) {
                        callback.onFailure(order, thirdOrderId, CheckOrderCallback.ERROR_CANCEL, "订单未支付");
                    }
                } else {
                    if (callback != null) {
                        callback.onFailure(order, thirdOrderId, state, msg);
                    }
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.w(TAG + order.getMoid() + "查询失败, code=" + code + ", msg=" + errorMsg);
                if (callback != null) {
                    callback.onFailure(order, thirdOrderId, code, errorMsg);
                }
            }
        });
    }


    private void retryCheckAfterDelay(@NonNull PayOrderModel order, String thirdOrderId, String payWay) {
        SQLog.d(TAG + "等待重试查询, moid=" + order.getMoid() + ", delay: " + RETRY_CHECK_DELAY);
        mHandler.postDelayed(() -> {
            SQLog.w(TAG + "重试查询支付状态, moid=" + order.getMoid() + ", " + thirdOrderId + "(" + payWay + ")");
            OrderRequestManager rm = rm();
            if (rm == null) {
                SQLog.e(TAG + "rm异常, 无法查询支付状态, moid="
                    + order.getMoid() + ", " + thirdOrderId + "(" + payWay + ")");
                return;
            }
            rm.checkPay(thirdOrderId, payWay, new SqHttpCallback<Void>() {
                @Override
                public void onSuccess(Void unused) {
                    SQLog.i(TAG + order.getMoid() + "支付成功(重试), 补充上报媒体");
                    // 补充上报
                    H5PayReporter.trackRetryCheckSuccess(order, thirdOrderId, payWay);
                    OrderTrackManager.reportToMedia(order.getPayInfoModel(), order.getMoid(), true);
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    SQLog.w(TAG + order.getMoid() + "未支付成功(重试), code=" + state + ", msg=" + msg);
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    SQLog.w(TAG + order.getMoid() + "查询失败(重试), code=" + code + ", msg=" + errorMsg);
                }
            });
        }, RETRY_CHECK_DELAY);
    }

    public interface CheckOrderCallback {

        int ERROR_CONTEXT_NULL = -31;
        int ERROR_PARAM = -32;
        int ERROR_CANCEL = -333;

        void onSuccess(@NonNull PayOrderModel order, String thirdOrderId);

        void onFailure(@NonNull PayOrderModel order, String thirdOrderId, int code, String msg);
    }
}
