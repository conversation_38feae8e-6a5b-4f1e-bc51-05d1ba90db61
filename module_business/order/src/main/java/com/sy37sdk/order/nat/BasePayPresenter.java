package com.sy37sdk.order.nat;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.WebView;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tools.Logger;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.ActivityResultListener;
import com.sqwan.base.EventDispatcher;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sy37sdk.order.nat.bean.Order;
import com.sqwan.common.mvp.BasePresenter;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.CheckClassUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SDKError;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.OrderRequestManager;
import com.sy37sdk.order.nat.pay.Apay;
import com.unionpay.UPPayAssistEx;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public abstract class BasePayPresenter<G extends IBasePayView> extends BasePresenter<G> implements IBasePayPresenter {

    private static final String TAG = "BasePayPresenter";

    /** 设置延迟多少秒后进行重试 */
    private final static int PAY_RETRY_TIME = 60 * 1000;

    private OrderRequestManager mOrderRequestManager;
    Order mOrder;
    private Handler mHandler;

    protected String currentPayMethod = "";

    /**
     * 微信支付订单ID
     */
    String wxOrderId;

    /**
     * 支付宝的订单id
     */
    String alipayOrderId;

    /**
     * 抖音的订单id
     */
    String douYinOrderId;

    /**
     * 默认支付宝
     */
    String currentPayWay = PayWay.ALI.getHttpPayWay();

    /** 是否重试过查询结果，若为true，则不再重试 */
    private boolean retryPayCallbackFlag = false;

    private final List<String> douYinSchemePrefix = new ArrayList<>();

    public BasePayPresenter(Context context, G payView) {
        super(context, payView);
        mOrderRequestManager = new OrderRequestManager(context);
        mHandler = new Handler(Looper.getMainLooper());

        // 抖音：ttcjpay://dypay/cashier 和 dypay1128://dypay/cashier
        douYinSchemePrefix.add("ttcjpay://dypay/cashier");
        douYinSchemePrefix.add("dypay1128://dypay/cashier");
        // 抖音极速版：ttcjpay://dypay/awemelite 和 dypay2329://dypay/cashier
        douYinSchemePrefix.add("ttcjpay://dypay/awemelite");
        douYinSchemePrefix.add("dypay2329://dypay/cashier");
        // 抖音火山版：dypay8663://dypay/cashier
        // 抖音官方的人说目前暂时不支持抖音火山版调起支付，但是后续会支持
        douYinSchemePrefix.add("dypay8663://dypay/cashier");
    }

    @Override
    public void setOrder(Order order) {
        this.mOrder = order;
    }


    @Override
    public void toWxPay(String url) {
        if (url.startsWith("weixin")) {
            LogUtil.i("start weixin pay --> " + url);
            Intent intent = new Intent();
            intent.setAction("android.intent.action.VIEW");
            intent.setData(Uri.parse(url));
            PackageManager packageManager = context.getPackageManager();
            List<ResolveInfo> resolveInfos = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
            if (resolveInfos != null && !resolveInfos.isEmpty()) {
                try {
                    context.startActivity(intent);
                } catch (Exception e) {
                    String msg = "跳转微信支付界面失败";
                    LogUtil.e(TAG, msg, e);
                    BuglessAction.reportCatchException(e, msg, getBuglessPayErrorActionType());
                }
            } else {
                if (mView != null) {
                    mView.showToast("未安装微信");
                }
            }
        }
    }

    @Override
    public void toAlipay(String orderInfo, final boolean isNative) {
        LogUtil.i("orderinfo:" + orderInfo + " 支付宝订单号：" + alipayOrderId);
        Apay.getInstance().pay((Activity) context, orderInfo, new Apay.AliCallback() {

            @Override
            public void onSuccess() {
                LogUtil.i("支付宝支付成功");
                // 如果获取到了orderId 优先调用接口去查询，如果没有id，走以前的逻辑
                if (!TextUtils.isEmpty(alipayOrderId)) {
                    checkAlipay();
                    return;
                }
                reportPay(true);
                if (mView != null) {
                    mView.paySuccess();
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.i("alipay -> code:" + code + ", msg:" + msg);
                // 如果获取到了orderId 优先调用接口去查询，如果没有id，走以前的逻辑
                if (!TextUtils.isEmpty(alipayOrderId)) {
                    checkAlipay();
                    return;
                }
                reportPay(false);
                BuglessAction.reportCatchException(new Exception(), "支付宝回调支付失败，code = " + code + "，msg = " + msg, getBuglessPayErrorActionType());
                if (mView != null) {
                    mView.onFailure(code, msg);
                }
            }
        }, true);
    }

    @Override
    public void toUnionPay(String tn) {
        if (!CheckClassUtils.classExist("com.unionpay.UPPayAssistEx") && !CheckClassUtils.classExist("com.unionpay.UPPayWapActivity")) {
            int code = 5010;
            String msg = "当前版本不支持云闪付支付";
            mView.onFailure(code, msg);
            BuglessAction.reportCatchException(new Exception(), "云闪付支付失败：" + msg, getBuglessPayErrorActionType());
            return;
        }

        EventDispatcher.getInstance().addActivityResultListener(new ActivityResultListener() {
            @Override
            public void onResult(OnActivityResultEvent onActivityResultEvent) {
                Intent data = onActivityResultEvent.getIntent();
                if (data == null) {
                    return;
                }

                // 查看代码发现，云闪付 Activity 跳转使用的 requestCode 是 10
                if (onActivityResultEvent.getRequestCode() != 10) {
                    return;
                }

                // 移除 onActivityResult 监听，避免出现重复回调
                EventDispatcher.getInstance().removeActivityResultListener(this);

                String msg;
                int code;
                // 支付控件返回字符串：success、fail、cancel 分别代表支付成功，支付失败，支付取消
                String payResult = data.getStringExtra("pay_result");
                String resultData = data.getStringExtra("result_data");
                boolean paySuccess = false;
                if ("success".equalsIgnoreCase(payResult)) {
                    // 结果result_data为成功时，去商户后台查询一下再展示成功
                    msg = "支付成功";
                    paySuccess = true;
                    code = 0;
                } else if ("fail".equalsIgnoreCase(payResult)) {
                    msg = "支付失败：resultData = " + resultData;
                    code = 1;
                } else if ("cancel".equalsIgnoreCase(payResult)) {
                    msg = "取消支付";
                    code = 2;
                } else {
                    msg = "未知支付结果：" + "payResult = " + payResult + ", resultData = " + resultData;
                    code = -1;
                }

                reportPay(paySuccess);

                if (!paySuccess) {
                    BuglessAction.reportCatchException(new Exception(), "云闪付回调支付失败，msg = " + msg, getBuglessPayErrorActionType());
                }

                if (mView == null) {
                    return;
                }

                if (paySuccess) {
                    mView.paySuccess();
                    return;
                }

                mView.onFailure(code, msg);
            }
        });

        try {
            // 调起云闪付支付，需要注意的是：00 代表正式订单，01 代表测试订单
            UPPayAssistEx.startPay(context, null, null, tn, "00");
        } catch (Exception e) {
            String msg = "跳转云闪付支付界面失败";
            LogUtil.e(TAG, msg, e);
            BuglessAction.reportCatchException(e, msg, getBuglessPayErrorActionType());
        }
    }

    @Override
    public void toDouYinWebPay(WebView webView, String payUrl, String referer) {
        if (TextUtils.isEmpty(payUrl)) {
            callbackDouYinPayFailure(20011, "调起抖音支付失败，请联系客服【20011】");
            return;
        }

        if (TextUtils.isEmpty(referer)) {
            callbackDouYinPayFailure(20012, "调起抖音支付失败，请联系客服【20012】");
            return;
        }

        webView.post(() -> {
            HashMap<String, String> extraHeaders = new HashMap<>();
            extraHeaders.put("referer", referer);
            webView.loadUrl(payUrl, extraHeaders);

            HashMap<String, String> payTrackParam = new HashMap<>();
            payTrackParam.put("payUrl", payUrl);
            payTrackParam.put("referer", referer);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PAY_H5_DOU_YIN, payTrackParam);
        });
    }

    @Override
    public void toDouYinPaySchemePage(String schemeUrl) {
        if (!isMatchDouYinPaySchemePrefix(schemeUrl)) {
            callbackDouYinPayFailure(20015, "跳转抖音支付失败，请重试或联系客服【20015】");
            return;
        }

        LogUtil.i("准备跳转到抖音支付页：" + schemeUrl);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        PackageManager packageManager = context.getPackageManager();

        List<String> validSchemeUrl = new ArrayList<>();

        String pathAndParameter = null;
        for (String schemePrefix : douYinSchemePrefix) {
            if (!schemeUrl.startsWith(schemePrefix)) {
                continue;
            }
            pathAndParameter = schemeUrl.replace(schemePrefix, "");
            break;
        }

        // 没有匹配到已经定义的抖音支付 scheme 协议，直接拿当前的 url 做判断
        if (pathAndParameter == null || "".equals(pathAndParameter)) {
            intent.setData(Uri.parse(schemeUrl));
            List<ResolveInfo> resolveInfoList = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
            if (resolveInfoList != null && !resolveInfoList.isEmpty()) {
                validSchemeUrl.add(schemeUrl);
            }
        } else {
            for (String schemePrefix : douYinSchemePrefix) {
                String newSchemeUrl = schemePrefix + pathAndParameter;
                intent.setData(Uri.parse(newSchemeUrl));
                List<ResolveInfo> resolveInfoList = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
                if (resolveInfoList == null || resolveInfoList.isEmpty()) {
                    continue;
                }
                // 把当前网页跳转的 url 放在首位（能跳转的前提条件下）
                if (schemeUrl.startsWith(schemePrefix)) {
                    validSchemeUrl.add(0, newSchemeUrl);
                } else {
                    validSchemeUrl.add(newSchemeUrl);
                }
            }
        }

        if (validSchemeUrl.isEmpty()) {
            // callbackDouYinPayFailure(6021, "调起抖音支付失败，未安装抖音");
            if (mView != null) {
                mView.showToast("未安装抖音，请安装后重新下单【20013】");
            }
            return;
        }

        try {
            intent.setData(Uri.parse(validSchemeUrl.get(0)));
            context.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            String msg = "跳转抖音支付失败，请重试或联系客服【20014】";
            LogUtil.e(TAG, msg, e);
            callbackDouYinPayFailure(20014, msg, e);
        }
    }

    @Override
    public boolean isMatchDouYinPaySchemePrefix(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        for (String schemePrefix : douYinSchemePrefix) {
            if (url.startsWith(schemePrefix)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void checkWxPay() {
        checkPayStatus(wxOrderId, PayWay.WECHAT.getHttpPayWay());
    }

    @Override
    public void checkDouYinWebPay() {
        checkPayStatus(douYinOrderId, PayWay.DOU_YIN_H5.getHttpPayWay());
    }

    /**
     * 检查alipay的支付状态
     */
    private void checkAlipay() {
        checkPayStatus(alipayOrderId, currentPayWay);
        // 订单ip重置
        alipayOrderId = null;
    }

    @Override
    public void reportPay(boolean success) {
        /* no-op */
    }

    @Override
    public void setWxOrderId(String wxOrderId) {
        this.wxOrderId = wxOrderId;
    }

    @Override
    public void setAliOrderId(String aliOrderId) {
        this.alipayOrderId = aliOrderId;
    }

    @Override
    public void setDouYinOrderId(String douYinOrderId) {
        this.douYinOrderId = douYinOrderId;
    }

    public abstract String payChannel();

    public abstract int getBuglessPayErrorActionType();

    @Override
    public void setCurrentPayMethod(String currentPayMethod) {
        this.currentPayMethod = currentPayMethod;
    }

    /**
     * 检查订单的状态
     *
     * @param orderId 订单id
     * @param pway    支付方式
     */
    private void checkPayStatus(final String orderId, final String pway) {
        Logger.info("查询支付状态：" + orderId + "  " + pway);
        mOrderRequestManager.checkPay(orderId, pway, new SqHttpCallback<Void>() {

            @Override
            public void onSuccess(Void unused) {
                if (!retryPayCallbackFlag) {
                    reportPay(true);
                    if (mView != null) {
                        mView.paySuccess();
                    }
                } else {
                    LogUtil.d(pway + "支付回调重试成功");
                    LogUtil.d("补充上报支付成功");
                    reportPay(true);
                }
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (!retryPayCallbackFlag) {
                    LogUtil.d(pway + "查询未支付成功，且未重试过，一分钟后重试");
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            LogUtil.d(pway + "支付查询发起重试");
                            retryPayCallbackFlag = true;
                            checkPayStatus(orderId, pway);
                        }
                    }, PAY_RETRY_TIME);
                    reportPay(false);
                    BuglessAction.reportCatchException(new Exception(), "检查到 " + pway + " 订单支付失败，code = " + SDKError.PAY_FAIL.code + "，msg = " + SDKError.PAY_FAIL.message, getBuglessPayErrorActionType());
                    if (mView != null) {
                        mView.onFailure(SDKError.PAY_FAIL.code, SDKError.PAY_FAIL.message);
                    }
                } else {
                    LogUtil.d("重试" + pway + "付结果查询还是未支付，无需处理");
                }
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                BuglessAction.reportCatchException(error, "检查 " + pway + " 订单状态失败，code = " + code + "，msg = " + msg, getBuglessPayErrorActionType());
                if (mView != null) {
                    mView.onFailure(code, msg);
                }
            }
        });
    }

    /**
     * 回调抖音支付失败
     */
    private void callbackDouYinPayFailure(int code, String msg) {
        callbackDouYinPayFailure(code, msg, new Exception(msg));
    }

    private void callbackDouYinPayFailure(int code, String msg, Exception e) {
        if (mView != null) {
            mView.onFailure(code, msg);
        }
        BuglessAction.reportCatchException(e, "抖音支付失败：" + msg, getBuglessPayErrorActionType());
    }
}
