package com.sy37sdk.order.nat.coupon;

import android.content.Context;
import android.view.View;
import com.sqwan.common.BuglessAction;
import com.sy37sdk.order.nat.BaseNativePayPresenter;

public class CouponPresenter extends BaseNativePayPresenter<ICouponView> implements ICouponPresenter {


    public CouponPresenter(Context context, ICouponView view) {
        super(context, view);
    }

    @Override
    public int getBuglessPayErrorActionType() {
        return BuglessAction.PAY_NATIVE_PAGE_ERROR;
    }


    @Override
    public void initData() {
        super.initData();

    }

    @Override
    public View getView() {
        return (View) mView;
    }

    @Override
    public void select() {

    }
}
