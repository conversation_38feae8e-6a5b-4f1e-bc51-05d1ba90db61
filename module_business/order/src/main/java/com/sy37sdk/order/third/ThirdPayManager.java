package com.sy37sdk.order.third;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.webkit.WebView;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.util.CheckClassUtils;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.third.IPayWay.PayWayCallback;
import com.sy37sdk.order.third.IPayWay.UIPayWayCallback;
import com.sy37sdk.order.third.ali.AliPayWay;
import com.sy37sdk.order.third.douyin.DouYinPayWay;
import com.sy37sdk.order.third.union.UnionPayWay;
import com.sy37sdk.order.third.wechat.WeChatPayWay;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/6
 */
public class ThirdPayManager {

    private static final String TAG = "【Pay Third】";
    /**
     * 订单号
     */
    public static final String EXTRA_ORDER_ID = "third_order_id";
    /**
     * 来源
     */
    public static final String EXTRA_REFERER = "referer";
    /**
     * 订单信息
     */
    public static final String EXTRA_TRADE_INFO = "trade_info";
    /**
     * 跳转的url
     */
    public static final String EXTRA_PAY_URL = "pay_url";

    private final Map<PayWay, IPayWay> mPayWayMap = new HashMap<>();

    public ThirdPayManager(WebView webView) {
        put(new WeChatPayWay(webView));
        put(new AliPayWay());
        put(new DouYinPayWay(webView));
        if (isSupportUnion()) {
            put(new UnionPayWay());
        }
    }

    private void put(IPayWay payWay) {
        mPayWayMap.put(payWay.getName(), payWay);
    }

    public void init(@NonNull Context context) {
        SQLog.v(TAG + "init " + context);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.init(context);
        }
    }

    public boolean isSupport(@NonNull PayWay payWay) {
        return mPayWayMap.containsKey(payWay);
    }

    public boolean interceptorWithUrl(@NonNull Activity activity, @NonNull PayOrderModel order,
        @Nullable String url, @Nullable Bundle extra, @Nullable PayWayCallback callback) {
        SQLog.v(TAG + "interceptorWithUrl " + url);
        for (IPayWay payWay : mPayWayMap.values()) {
            if (payWay instanceof IWebPayWay) {
                boolean intercept = ((IWebPayWay) payWay).interceptorWithUrl(activity, order, url, extra, callback);
                if (intercept) {
                    return true;
                }
            }
        }
        return false;
    }

    public void pay(@NonNull Activity activity, @NonNull PayWay type, @NonNull PayOrderModel order,
        @NonNull Bundle extra,
        @Nullable PayWayCallback callback) {
        SQLog.d(TAG + "触发支付, 类型=" + type);
        IPayWay payWay = mPayWayMap.get(type);
        if (payWay == null) {
            SQLog.e(TAG + "不支持类型" + type);
            UIPayWayCallback.wrap(callback).onFailed(type, order, -1, "不支持该支付类型(" + type.type + ")");
            return;
        }
        payWay.pay(activity, order, extra, callback);
    }

    /**
     * 手动触发查单
     */
    public void checkPay(@NonNull PayOrderModel order) {
        SQLog.d(TAG + "检查支付状态, moid=" + order.getMoid());
        for (IPayWay payWay : mPayWayMap.values()) {
            if (payWay instanceof CheckablePayWay) {
                ((CheckablePayWay) payWay).check();
            }
        }
    }

    public void onStart(@NonNull Activity activity) {
        SQLog.v(TAG + "onStart " + activity);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.onStart(activity);
        }
    }

    public void onResume(@NonNull Activity activity) {
        SQLog.v(TAG + "onResume " + activity);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.onResume(activity);
        }
    }

    public void onPause(@NonNull Activity activity) {
        SQLog.v(TAG + "onPause " + activity);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.onPause(activity);
        }
    }

    public void onStop(@NonNull Activity activity) {
        SQLog.v(TAG + "onStop " + activity);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.onStop(activity);
        }
    }

    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data) {
        SQLog.v(TAG + "onActivityResult " + activity + ", requestCode=" + requestCode);
        for (IPayWay payWay : mPayWayMap.values()) {
            payWay.onActivityResult(activity, requestCode, resultCode, data);
        }
    }

    /**
     * 检查宿主是否支持云闪付
     */
    private static boolean isSupportUnion() {
        return CheckClassUtils.classExist("com.unionpay.UPPayAssistEx")
            && CheckClassUtils.classExist("com.unionpay.UPPayWapActivity");
    }
}
