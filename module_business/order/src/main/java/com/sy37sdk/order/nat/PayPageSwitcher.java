package com.sy37sdk.order.nat;

import android.os.Bundle;
import android.support.v4.view.ViewPager;

import com.sqwan.common.mvp.IPageSwitcher;

public class PayPageSwitcher implements IPageSwitcher {

    //商品信息购买页
    public static final int PAGE_TRADE = 0;
    //代金券
    public static final int PAGE_COUPON = 1;
    //人工充值
    public static final int PAGE_LOBAR = 2;

    private ViewPager mViewPager;

    private int mCurPage = PAGE_TRADE;


    public PayPageSwitcher(ViewPager viewPager) {
        this.mViewPager = viewPager;
    }


    @Override
    public void onSwitch(int page) {
        onSwitch(page,null);
    }

    public void onSwitch(int page, Bundle bundle) {
        if (pageScrollListener != null) {
            pageScrollListener.scroll(mCurPage, page, bundle);
        }
        mCurPage = page;
        mViewPager.setCurrentItem(page);
    }

    private IPageScrollListener pageScrollListener;

    public void setPageScrollListener(IPageScrollListener pageScrollListener) {
        this.pageScrollListener = pageScrollListener;
    }

    public interface IPageScrollListener {
        void scroll(int fromIndex, int toIndex, Bundle bundle);
    }
}
