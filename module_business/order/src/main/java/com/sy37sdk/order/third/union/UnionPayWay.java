package com.sy37sdk.order.third.union;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.JsonMap;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;
import com.sy37sdk.order.third.IPayWay;
import com.sy37sdk.order.third.ThirdPayManager;
import com.unionpay.UPPayAssistEx;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
public class UnionPayWay implements IPayWay {

    public static final int ERROR_PAY = 400;
    public static final int ERROR_PARAM = 401;
    public static final int ERROR_ORDER_ID = 402;
    public static final int ERROR_GO_PAY_PAGE = 403;
    public static final int ERROR_INVALID_RESULT = 404;

    @Nullable
    private UnionChatOrder mCurrentOrder;

    @Override
    public void init(@NonNull Context context) {

    }

    @Override
    public void onStart(@NonNull Activity activity) {

    }

    @Override
    public void onResume(@NonNull Activity activity) {
    }

    @Override
    public void onPause(@NonNull Activity activity) {

    }

    @Override
    public void onStop(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data) {
        SQLog.v(TAG + "[" + getSimpleName() + "]onActivityResult, requestCode=" + requestCode + ", data=" + data);
        UnionChatOrder currentOrder = mCurrentOrder;
        if (currentOrder == null) {
            return;
        }
        if (data == null) {
            return;
        }
        // 查看代码发现，云闪付 Activity 跳转使用的 requestCode 是 10
        if (requestCode != 10) {
            return;
        }

        PayOrderModel order = currentOrder.order;
        PayWayCallback callback = currentOrder.callback;
        SQLog.d(TAG + "[" + getSimpleName() + "]onActivityResult, 支付回调, " + data);
        SQLog.d(TAG + "[" + getSimpleName() + "]当前订单moid=" + order.getMoid());
        // 支付控件返回字符串：success、fail、cancel 分别代表支付成功，支付失败，支付取消
        String payResult = data.getStringExtra("pay_result");
        String resultData = data.getStringExtra("result_data");
        SQLog.d(TAG + "[" + getSimpleName() + "]支付结果, result=" + payResult + ", data=" + resultData);
        if ("success".equalsIgnoreCase(payResult)) {
            // 结果result_data为成功时，去商户后台查询一下再展示成功
            SQLog.i(TAG + "[" + getSimpleName() + "]支付成功");
            callback.onSuccess(getName(), order);
        } else if ("fail".equalsIgnoreCase(payResult)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]支付失败: " + payResult);
            callback.onFailed(getName(), order, ERROR_PAY, "支付失败");
        } else if ("cancel".equalsIgnoreCase(payResult)) {
            SQLog.w(TAG + "[" + getSimpleName() + "]用户取消支付");
            callback.onCancel(getName(), order);
        } else {
            SQLog.e(TAG + "[" + getSimpleName() + "]支付失败: " + payResult);
            JsonMap map = new JsonMap();
            map.put("order_id", order.getMoid());
            map.put("third_order_id", currentOrder.tn);
            map.put("pay_result", payResult);
            map.put("result_data", resultData);
            BuglessAction.reportCatchException(new IllegalStateException("云闪付支付结果异常"),
                map.toString(), BuglessAction.PAY_ERROR);
            callback.onFailed(getName(), order, ERROR_INVALID_RESULT, "未知支付结果");
        }
    }

    /**
     * @see ThirdPayManager#EXTRA_ORDER_ID 云闪付订单号
     */
    @Override
    public void pay(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable Bundle extra,
        @Nullable PayWayCallback callback) {
        SQLog.d(TAG + "[" + getSimpleName() + "]支付:\n" + order);
        SQLog.d(TAG + "[" + getSimpleName() + "]extra: " + extra);
        InternalCallback uiCallback = wrapCallback(callback);
        if (extra == null) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无额外参数, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_PARAM, "无参数");
            return;
        }

        String tn = extra.getString(ThirdPayManager.EXTRA_ORDER_ID);
        if (TextUtils.isEmpty(tn)) {
            SQLog.e(TAG + "[" + getSimpleName() + "]无tn, 无法支付");
            uiCallback.onFailed(getName(), order, ERROR_ORDER_ID, "订单号异常");
            return;
        }

        assert tn != null;
        SQLog.d(TAG + "[" + getSimpleName() + "]tn=" + tn);
        UnionChatOrder oldOrder = mCurrentOrder;
        if (oldOrder != null) {
            SQLog.w(TAG + "[" + getSimpleName() + "]覆盖订单: " + oldOrder.order.getMoid());
        }
        mCurrentOrder = new UnionChatOrder(tn, order, uiCallback);
        try {
            SQLog.d(TAG + "[" + getSimpleName() + "]调起云闪付, tn=" + tn);
            // 调起云闪付支付，需要注意的是：00 代表正式订单，01 代表测试订单
            UPPayAssistEx.startPay(activity, null, null, tn, "00");
        } catch (Exception e) {
            String msg = "跳转云闪付支付界面失败";
            SQLog.e(TAG + "[" + getSimpleName() + "]" + msg, e);
            BuglessAction.reportCatchException(e, msg, BuglessAction.PAY_WEB_PAGE_ERROR);
            uiCallback.onFailed(getName(), order, ERROR_GO_PAY_PAGE, "无法跳转云闪付");
        }
    }

    @NonNull
    @Override
    public PayWay getName() {
        return PayWay.UNION;
    }

    private static class UnionChatOrder {

        /**
         * 云闪付的订单号
         */
        @NonNull
        final String tn;
        @NonNull
        final PayOrderModel order;
        @NonNull
        InternalCallback callback;

        private UnionChatOrder(@NonNull String tn, @NonNull PayOrderModel order, @NonNull InternalCallback callback) {
            this.tn = tn;
            this.order = order;
            this.callback = callback;
        }
    }

    @NonNull
    private InternalCallback wrapCallback(PayWayCallback callback) {
        if (callback instanceof InternalCallback) {
            return (InternalCallback) callback;
        } else {
            return new InternalCallback(callback);
        }
    }

    /**
     * 回调后重置当前订单
     */
    private class InternalCallback extends UIPayWayCallback {

        public InternalCallback(@Nullable PayWayCallback callback) {
            super(callback);
        }

        @Override
        public void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("success");
            super.onSuccess(payWay, order);
        }

        @Override
        public void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            resetOrder("cancel");
            super.onCancel(payWay, order);
        }

        @Override
        public void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message) {
            resetOrder("failed");
            super.onFailed(payWay, order, code, message);
        }

        private void resetOrder(String scene) {
            UnionChatOrder order = mCurrentOrder;
            if (order != null) {
                SQLog.d(TAG + "[" + getSimpleName() + "]" + order.order.getMoid() + "订单回调(" + scene + ")");
            }
            mCurrentOrder = null;
        }
    }

    private String getSimpleName() {
        return "Union";
    }
}
