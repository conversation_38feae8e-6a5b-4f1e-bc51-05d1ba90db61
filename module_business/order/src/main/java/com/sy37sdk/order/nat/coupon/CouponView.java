package com.sy37sdk.order.nat.coupon;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.order.nat.bean.Coupon;
import com.sy37sdk.order.nat.BasePayView;
import com.sy37sdk.order.nat.INativePayDialog;
import com.sy37sdk.order.nat.PayBundleKey;
import com.sy37sdk.order.nat.PayPageSwitcher;

import java.util.List;

/**
 * 代金券页面
 */
public class CouponView extends BasePayView implements ICouponView {

    private ICouponPresenter couponPresenter;

    private Context mContext;

    private ListView lvCoupon;

    private View notUseCoupon;

    private View rlNotUseCouponContainer;

    private ImageView ivNotUseCoupon;

    private CouponAdapter couponAdapter;

    private List<Coupon> coupons;

    private int selectPos = 0;

    private Coupon selectCoupon;

    private boolean useCoupon = true;

    public CouponView(Context context, INativePayDialog nativePayDialog) {
        super(context);
        this.mContext = context;
        this.nativePayDialog = nativePayDialog;
    }


    @Override
    public String getTitle() {
        return SqResUtils.getStringByName(mContext, "sysq_pay_trade_coupon");
    }

    @Override
    public void initView() {
        setLeftResDrawable("sysq_ic_pay_back");
        rlNotUseCouponContainer = getViewByName(rootView, "rl_not_use_coupon_container");
        notUseCoupon = getViewByName(rootView, "not_use_coupon");
        ivNotUseCoupon = getViewByName(rootView, "iv_not_use_coupon");
        lvCoupon = getViewByName(rootView, "lv_coupon");
        lvCoupon.setFocusable(false);
        lvCoupon.setVerticalScrollBarEnabled(false);
        couponAdapter = new CouponAdapter(mContext);
        lvCoupon.setAdapter(couponAdapter);
    }


    @Override
    public void initEvent() {
        rlNotUseCouponContainer.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ivNotUseCoupon.setImageResource(SqResUtils.getDrawableId(mContext, "sysq_ic_pay_selected"));
                selectCoupon(-1);
                back();
            }
        });
        couponAdapter.setSelectListener(new CouponAdapter.SelectListener() {
            @Override
            public void onSelect(int pos) {
                selectCoupon(pos);
                back();
            }
        });
    }

    private void selectCoupon(int pos) {
        for (int i = 0; i < coupons.size(); i++) {
            Coupon coupon = coupons.get(i);
            coupon.setSelect(i == pos);
        }
        selectPos = pos;
        if (pos == -1) {
            selectCoupon = null;
            useCoupon = false;
        } else {
            selectCoupon = coupons.get(selectPos);
            useCoupon = true;
        }
        couponAdapter.setDatas(coupons);
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        LogUtil.i("onSwitched CouponView " + fromIndex + "   " + toIndex);
        ivNotUseCoupon.setImageResource(useCoupon ? SqResUtils.getDrawableId(mContext, "sysq_ic_pay_select") : SqResUtils.getDrawableId(mContext, "sysq_ic_pay_selected"));
        if (bundle != null) {
            coupons = bundle.getParcelableArrayList(PayBundleKey.KEY_COUPON);
            selectCoupon(selectPos);
        }
    }


    @Override
    protected void leftViewClicked() {
        back();
    }

    @Override
    public String getLayoutResName() {
        return "sysq_pay_dialog_coupon";
    }

    @Override
    public void setPresenter(ICouponPresenter presenter) {
        this.couponPresenter = presenter;
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void onBackPressed() {
        back();
    }


    @Override
    public void back() {
        Bundle bundle = new Bundle();
        bundle.putBoolean(PayBundleKey.KEY_USE_COUPON, useCoupon);
        if (useCoupon) {
            bundle.putParcelable(PayBundleKey.KEY_SELECT_COUPON, selectCoupon);
        }
        nativePayDialog.onSwitch(PayPageSwitcher.PAGE_TRADE, bundle);
    }
}
