package com.sy37sdk.order.nat;

import android.support.v4.view.PagerAdapter;
import android.support.v4.view.ViewPager;
import android.view.View;
import com.sqwan.common.util.LogUtil;
import java.util.List;


/**
 * 账号切换Adapter
 */
public class PayPageAdapter extends PagerAdapter {

    private static final String TAG = "AccountPageAdapter";
    private List<BaseNativePayPresenter> nativePayPresenters;

    public PayPageAdapter(List<BaseNativePayPresenter> nativePayPresenters) {
        this.nativePayPresenters = nativePayPresenters;
    }

    @Override
    public int getCount() {
        return nativePayPresenters.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object obj) {
        return view == obj;
    }

    @Override
    public int getItemPosition(Object object) {
        return super.getItemPosition(object);
    }

    @Override
    public void destroyItem(View collection, int position, Object o) {
        View view = nativePayPresenters.get(position).getView();
        LogUtil.i(TAG, "destroyItem: position=" + position + "  view:" + view);
        ((ViewPager) collection).removeView(view);
    }

    @Override
    public Object instantiateItem(View view, int position) {
        View v = nativePayPresenters.get(position).getView();
        LogUtil.i(TAG, "instantiateItem: v=" + v + "  pos=" + position);
        ((ViewPager) view).addView(v);
        return v;
    }

}
