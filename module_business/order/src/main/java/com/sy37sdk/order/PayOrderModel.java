package com.sy37sdk.order;

import android.support.annotation.NonNull;
import com.sqwan.order.base.PayInfoModel;

/**
 * <AUTHOR>
 * @since 2024/9/5
 */
public class PayOrderModel {

    private final PayInfoModel mPayInfoModel;
    /**
     * 下单接口返回的订单号
     */
    private String mMoid;

    /**
     * 支付session, 上报用
     */
    private String mPaySession;

    public PayOrderModel(@NonNull PayInfoModel payInfoModel) {
        mPayInfoModel = payInfoModel;
    }

    public String getPaySession() {
        return mPaySession;
    }

    public void setPaySession(String paySession) {
        mPaySession = paySession;
    }

    @NonNull
    public PayInfoModel getPayInfoModel() {
        return mPayInfoModel;
    }

    public String getMoid() {
        return mMoid;
    }

    public void setMoid(String moid) {
        mMoid = moid;
    }

    @NonNull
    @Override
    public String toString() {
        return mPayInfoModel + "\n  moid=" + getMoid();
    }
}
