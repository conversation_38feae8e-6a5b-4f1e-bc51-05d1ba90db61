package com.sy37sdk.order.third.ali;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import com.alipay.sdk.app.PayTask;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sy37sdk.order.third.ali.AliPayWay.AliPayResult;
import java.util.HashMap;
import java.util.Map;

/**
 * 没有37订单号, 只有支付宝订单号, 所以新建一个类
 *
 * <AUTHOR>
 * @since 2024/5/21
 */
public class AliPay {

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    /**
     * 调起支付宝
     * <a
     * href="https://opendocs.alipay.com/open/204/105302?pathHash=eab39489#%E8%BF%94%E5%9B%9E%E7%BB%93%E6%9E%9C%E7%A4%BA%E4%BE%8B%EF%BC%88iOS%7CAndroid%EF%BC%89">...</a>
     *
     * @param orderId 37订单号, 上报用
     * @param tradeInfo 透传给阿里支付
     */
    public void pay(Activity activity, String orderId, String tradeInfo, AliPayCallback callback) {
        SQLog.i("调起阿里支付: " + orderId + ", " + tradeInfo);
        reportInvoke(orderId);
        if (TextUtils.isEmpty(tradeInfo)) {
            SQLog.e("阿里支付参数异常, 支付失败");
            reportFail(orderId, -1, "支付参数异常");
            if (callback != null) {
                callback.onFailure(-1, "支付参数异常");
            }
            return;
        }
        Runnable runnable = () -> {
            PayTask payTask = new PayTask(activity);
            Map<String, String> result = payTask.payV2(tradeInfo, true);
            SQLog.d("阿里支付回调结果: " + result);
            // 切到主线程处理回调
            mHandler.post(() -> handlePayResult(orderId, result, callback));
        };
        AliPayWay.WORKER.execute(runnable);
    }

    private void handlePayResult(String orderId, Map<String, String> result, AliPayCallback callback) {
        AliPayResult payResult = new AliPayResult(result);
        if (!payResult.isValid()) {
            SQLog.e("阿里支付结果解析异常, 支付失败: " + result);
            reportFail(orderId, -1, "支付结果解析异常");
            if (callback != null) {
                callback.onFailure(-1, "支付结果解析异常");
            }
            return;
        }
        if (payResult.isSuccess()) {
            SQLog.i("阿里支付成功");
            reportSuccess(orderId);
            // 支付成功
            if (callback != null) {
                callback.onSuccess();
            }
        } else if (payResult.isCancel()) {
            SQLog.w("用户取消阿里支付");
            reportCancel(orderId);
            // 支付取消
            if (callback != null) {
                callback.onCancel();
            }
        } else {
            SQLog.e("阿里支付失败: " + result);
            // 支付失败
            int code = payResult.getErrorCode();
            String reason = payResult.getErrorMsg();
            reportFail(orderId, code, reason);
            if (callback != null) {
                callback.onFailure(code, reason);
            }
        }
    }

    public interface AliPayCallback {

        void onSuccess();

        void onCancel();

        void onFailure(int code, String message);
    }

    private void reportInvoke(String orderId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("order_id", orderId);
        SqTrackActionManager2.getInstance().trackAction(
            "get_android_sdk_to_alipay", "请求从安卓SDK调起支付宝", params);
    }

    private void reportSuccess(String orderId) {
        reportResult(orderId, "成功", null, null);
    }

    private void reportCancel(String orderId) {
        reportResult(orderId, "取消", null, null);
    }

    private void reportFail(String orderId, int failCode, String reason) {
        reportResult(orderId, "失败", String.valueOf(failCode), reason);
    }

    private void reportResult(String orderId, String result, String failCode, String reason) {
        HashMap<String, String> params = new HashMap<>();
        params.put(SqTrackKey.order_id, orderId);
        params.put("result", result);
        if (failCode != null) {
            params.put(SqTrackKey.fail_code, failCode);
        }
        if (reason != null) {
            params.put(SqTrackKey.reason_fail, reason);
        }
        SqTrackActionManager2.getInstance().trackAction(
            "android_sdk_to_alipay_result", "支付结果", params);
    }
}