package com.sy37sdk.order.nat.trade;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.BuglessAction;
import com.sy37sdk.order.nat.bean.Order;
import com.sqwan.common.util.SDKError;
import com.sy37sdk.order.nat.NatRequestManager;
import com.sy37sdk.order.nat.bean.Coupon;
import java.util.List;
import org.json.JSONException;

/**
 * <AUTHOR>
 * @date 2020/2/27
 */
public class OrderData {

    private NatRequestManager requestManager;

    private static OrderData instance;
    private Context mContext;

    private OrderData(Context context) {
        this.mContext = context;
        requestManager = new NatRequestManager(mContext);
    }

    public static OrderData getInstance(Context context) {
        if (instance == null) {
            synchronized (OrderData.class) {
                if (instance == null) {
                    instance = new OrderData(context);
                }
            }
        }
        return instance;
    }

    public void getAvailableWays(String moid, final PayCallback<String> callback) {
        requestManager.getAvailablePways(moid, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                callback.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                callback.onSuccess(data);
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                callback.onFailure(code, msg);
            }
        });
    }

    public void getWalletBalance(String moid, final PayCallback<Wallet> callback) {
        requestManager.getWalletBalance(moid, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                callback.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                try {
                    Wallet wallet = Wallet.fromJson(data);
                    callback.onSuccess(wallet);
                } catch (JSONException e) {
                    e.printStackTrace();
                    BuglessAction.reportCatchException(e, data, BuglessAction.ORDER_GET_WALLET);
                    callback.onFailure(SDKError.NET_DATA_PARSE_ERROR.code, SDKError.NET_DATA_PARSE_ERROR.message);
                }
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                callback.onFailure(code, msg);
            }
        });
    }

    public void getCoupons(String moid, float money, String dsid, final PayCallback<List<Coupon>> callback) {
        requestManager.getCoupon(moid, money, dsid, 1, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                callback.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                try {
                    List<Coupon> coupons = Coupon.fromJson(data);
                    callback.onSuccess(coupons);
                } catch (JSONException e) {
                    e.printStackTrace();
                    BuglessAction.reportCatchException(e, data, BuglessAction.ORDER_GET_COUPON);
                    callback.onFailure(SDKError.NET_DATA_PARSE_ERROR.code, SDKError.NET_DATA_PARSE_ERROR.message);
                }
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                callback.onFailure(code, msg);
            }
        });
    }

    public void walletPay(Order order, final PayCallback<Boolean> callback) {
        if (order == null) {
            callback.onFailure(-1, "订单异常");
            return;
        }
        requestManager.wtPay(order, new SqHttpCallback<Void>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                callback.onFailure(state, msg);
            }

            @Override
            public void onSuccess(Void unused) {
                callback.onSuccess(true);
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                callback.onFailure(code, msg);
            }
        });
    }

    public void otherPay(float money, String pway, String moid, boolean isHuabei, String couponCode, final PayCallback<PayOrder> callback) {
        requestManager.pay(money, pway, moid, isHuabei, couponCode, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                callback.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                try {
                    PayOrder payOrder = PayOrder.fromJson(data);
                    callback.onSuccess(payOrder);
                } catch (JSONException e) {
                    e.printStackTrace();
                    BuglessAction.reportCatchException(e, data, BuglessAction.S_ORDER);
                    callback.onFailure(SDKError.NET_DATA_PARSE_ERROR.code, SDKError.NET_DATA_PARSE_ERROR.message);
                }
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                callback.onFailure(code, msg);
            }
        });
    }

    public interface PayCallback<T> {
        void onSuccess(T t);

        void onFailure(int code, String msg);
    }

}
