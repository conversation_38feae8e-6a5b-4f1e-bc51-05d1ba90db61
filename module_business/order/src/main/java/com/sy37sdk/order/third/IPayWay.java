package com.sy37sdk.order.third;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sqwan.order.base.PayWay;
import com.sy37sdk.order.PayOrderModel;

/**
 * <AUTHOR>
 * @since 2024/8/7
 */
public interface IPayWay {

    String TAG = "【Pay Way】";

    void init(@NonNull Context context);

    void onStart(@NonNull Activity activity);

    void onResume(@NonNull Activity activity);

    void onPause(@NonNull Activity activity);

    void onStop(@NonNull Activity activity);

    void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, Intent data);

    void pay(@NonNull Activity activity, @NonNull PayOrderModel order, @Nullable Bundle extra,
        @Nullable PayWayCallback callback);

    @NonNull
    PayWay getName();

    interface PayWayCallback {

        void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order);

        void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order);

        void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message);
    }

    class UIPayWayCallback implements PayWayCallback {

        private static final Handler sHandler = new Handler(Looper.getMainLooper());
        final PayWayCallback callback;

        @NonNull
        public static PayWayCallback wrap(@Nullable PayWayCallback callback) {
            if (callback instanceof UIPayWayCallback) {
                return callback;
            } else {
                return new UIPayWayCallback(callback);
            }
        }

        public UIPayWayCallback(@Nullable PayWayCallback callback) {
            this.callback = callback;
        }

        @Override
        public void onSuccess(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onSuccess(payWay, order);
                } else {
                    sHandler.post(() -> callback.onSuccess(payWay, order));
                }
            }
        }

        @Override
        public void onCancel(@NonNull PayWay payWay, @NonNull PayOrderModel order) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onCancel(payWay, order);
                } else {
                    sHandler.post(() -> callback.onCancel(payWay, order));
                }
            }
        }

        @Override
        public void onFailed(@NonNull PayWay payWay, @NonNull PayOrderModel order, int code, String message) {
            if (callback != null) {
                if (isMainThread()) {
                    callback.onFailed(payWay, order, code, message);
                } else {
                    sHandler.post(() -> callback.onFailed(payWay, order, code, message));
                }
            }
        }

        private static boolean isMainThread() {
            return Looper.getMainLooper() == Looper.myLooper();
        }
    }
}
