package com.sqwan.common.mod.order;

import android.app.Activity;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sqwan.common.mod.IModBase;
import com.sqwan.order.base.IPay.PayCallback;
import com.sqwan.order.base.PayContext;
import com.sqwan.order.base.PayInfoModel;

/**
 * <AUTHOR>
 * @date 2020/2/28
 */
public interface IOrderMod extends IModBase {

    void pay(@NonNull Activity activity, @NonNull PayContext payContext, @NonNull PayInfoModel payInfo,
        @Nullable Bundle extra, @Nullable PayCallback callback);

}
