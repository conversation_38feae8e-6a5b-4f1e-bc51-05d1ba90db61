apply plugin: 'com.android.library'
apply plugin: 'com.sq.sqinject'

def isPluginMode = get_isPluginMode()

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {

        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"


        //2、android标签内defaultConfig标签处理
        defaultConfig {
            //...
            //增加如下配置,其中com.sq.sqinjectdemo换成对应包名，如果是lib工程，对应成lib工程R文件的包名
            javaCompileOptions {
                annotationProcessorOptions {
                    includeCompileClasspath = true
                    arguments = [packageName: 'com.sy37sdk.order.eventbus', className: 'EventBusIndex', applicationId: 'com.sy37sdk.order']
                }
            }
        }
    }

    buildTypes {

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    // 支持 Java JDK 8
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':37sdkcommon')
    api project(':module_base:base_order')
    //3、增加依赖
    annotationProcessor 'com.37sy.android:sqinject-compile:1.0.0'
    implementation 'com.37sy.android:sqinject:1.0.0'
    annotationProcessor 'com.37sy.android:sqeventbus-compile:1.0.0'
    api 'com.37sy.android:sqeventbus-annotation:1.0.0'
    api 'com.37sy.android:sqeventbus:1.0.0'
    if (!isPluginMode) {
        // 阿里支付
        implementation project(':module_third:ali_pay:ali_pay_sdk')
        // 云闪付
        implementation project(':module_third:union_pay:union_pay_sdk')
    } else {
        // 插件下阿里支付在插件宿主中, 所以不打进插件apk
        compileOnly project(':module_third:ali_pay:ali_pay_sdk')
        // 插件下云闪付在插件宿主中, 所以不打进插件apk
        compileOnly project(':module_third:union_pay:union_pay_sdk')
    }
}
