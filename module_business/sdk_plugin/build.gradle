apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"

        //2、android标签内defaultConfig标签处理
        defaultConfig {
            //...
            //增加如下配置,其中com.sq.sqinjectdemo换成对应包名，如果是lib工程，对应成lib工程R文件的包名
            javaCompileOptions {
                annotationProcessorOptions {
                    includeCompileClasspath = true
                    arguments = [packageName: 'com.sy37sdk.plugin.eventbus', className: 'EventBusIndex']
                }
            }
        }
    }

    buildTypes {

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':37sdkcommon')
    annotationProcessor 'com.37sy.android:sqeventbus-compile:1.0.0'
    api 'com.37sy.android:sqeventbus:1.0.0'
    api 'com.37sy.android:sqeventbus-annotation:1.0.0'
}
