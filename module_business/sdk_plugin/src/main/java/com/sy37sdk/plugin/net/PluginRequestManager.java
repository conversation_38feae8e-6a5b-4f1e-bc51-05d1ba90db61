package com.sy37sdk.plugin.net;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV1;
import com.sqwan.common.request.CommonParamsV2;
import com.sqwan.common.util.VersionUtil;
import org.json.JSONObject;

public class PluginRequestManager {

    /**
     * 获取插件配置
     *
     * @param pluginVersion 插件版本
     */
    public void getPluginConfig(int pluginVersion, SqHttpCallback<JSONObject> callback) {
        SqRequest.of(PluginUrl.PLUGIN_CONFIG_URL)
            .signV3()
            .addParam("plugin_version", pluginVersion)
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    /**
     * @param pluginVersion 插件版本
     * @param state 下载成功失败
     * @param getType 下发类型：1：全量下发，2：部分下发
     * @param errorMsg 失败原因
     */
    public void pluginDownloadReport(int pluginVersion, int state, int getType, int confId, String errorMsg,
        SqHttpCallback<Void> callback) {
        SqRequest.of(PluginUrl.PLUGIN_REPORT_URL)
            .signV3()
            .addParam("plugin_version", pluginVersion)
            .addParam("ret", state)
            .addParam("type", getType)
            .addParam("msg", errorMsg)
            .addParam("conf_id", confId)
            // 宿主版本号
            .addParam("host_sdk_version", VersionUtil.getOriginalVersion())
            .addParamsTransformer(new CommonParamsV1())
            .post(callback, Void.class);
    }
}
