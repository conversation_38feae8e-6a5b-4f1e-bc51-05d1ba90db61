package com.sy37sdk.plugin.data;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import com.sq.sdk.tool.download.DownloadListener;
import com.sq.sdk.tool.download.DownloadTask;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.plugin.net.PluginRequestManager;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.util.HashMap;

public class PluginDownloadManager {

    private static final int FILE_MD5_CALCULATION_FAILURE = 1024;
    private static final int FILE_MD5_DIFFERENT = 1025;

    private String mPluginPath;

    private Context mContext;

    private SpPluginConfig mPluginConfig;

    private PluginRequestManager mRequestManager;

    public PluginDownloadManager(Context context) {
        mContext = context;
        mPluginConfig = new SpPluginConfig(context);
        mRequestManager = new PluginRequestManager();
    }

    /**
     * 插件处理入口
     */
    public void handlePlugin() {
        if (mPluginConfig.getPluginVersion() != 0) {
            mPluginPath = generatePluginPath();
            if (mPluginConfig.getPluginVersion() > mPluginConfig.getPluginCurrentVersion()) {
                //当下发插件版本大于当前插件版本号则上报
                HashMap<String, String> trackMap = new HashMap<>();
                trackMap.put(SqTrackKey.plug_id, mPluginConfig.getPluginConfId() + "");
                trackMap.put(SqTrackKey.plug_type, mPluginConfig.getPluginType() + "");
                trackMap.put(SqTrackKey.plug_version, mPluginConfig.getPluginVersion() + "");
                trackMap.put(SqTrackKey.plug_current_version, mPluginConfig.getPluginCurrentVersion() + "");
                trackMap.put(SqTrackKey.plug_link, mPluginConfig.getPluginUrl());
                trackMap.put(SqTrackKey.plug_hash, mPluginConfig.getPluginHash() + "");
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update, trackMap);
            }

            //下载插件
            downloadPlugin();
        } else {
            if (mPluginConfig.getPluginLatestVersion() != 0 && mPluginConfig.getPluginLatestVersion() != -1) {
                //上报插件回滚下发
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update_rollback);
                SharedPreferences sp = mContext.getSharedPreferences("sq_plugin_config", Context.MODE_PRIVATE);
                sp.edit().putString("hotter_rollback_effect", mPluginConfig.getPluginLatestVersion() + "").apply();
            }
            //设置下次启动为0版本
            mPluginConfig.setPluginLatestVersion(0);
            LogUtil.i("没有插件配置，或插件已全部回滚，走默认插件");
        }
    }

    /**
     * 获取插件存储路径
     *
     * @return
     */
    private String generatePluginPath() {
        File pluginDirFile = mContext.getDir("plugin", Context.MODE_PRIVATE);
        if (!pluginDirFile.exists()) {
            pluginDirFile.mkdirs();
        }
        String pluginPath = pluginDirFile.getAbsolutePath();
        LogUtil.i("插件地址: " + pluginPath);
        return pluginPath;
    }

    /**
     * 插件是否存在
     *
     * @return
     */
    private boolean isPluginExist(String pluginPath, String pluginApkName) {
        File pluginFile = new File(pluginPath, pluginApkName);
        if (pluginFile.exists()) {
            return true;
        }
        return false;
    }

    /**
     * 下载插件
     */
    private void downloadPlugin() {
        new DownloadTask(mPluginConfig.getPluginUrl(), "plugin_" + mPluginConfig.getPluginVersion() + ".apk", mPluginPath, new DownloadListener() {
            @Override
            public void onUpdate(long l, long l1) {

            }

            @Override
            public void onSuccess(File file) {
                LogUtil.i("文件下载成功: " + file.getAbsolutePath());
                //如果上次的版本就是这个了，说明下载成功过，这里只是个验证环节而已，无需再次上报直接走逻辑
                if (mPluginConfig.getPluginLatestVersion() == mPluginConfig.getPluginVersion()) {
                    return;
                }

                String fileMd5;
                try {
                    fileMd5 = getFileMd5(file);
                } catch (Exception e) {
                    e.printStackTrace();
                    reportHotterUpdateFail(FILE_MD5_CALCULATION_FAILURE, e.getMessage());
                    return;
                }
                String pluginHash = mPluginConfig.getPluginHash();
                if (!TextUtils.isEmpty(pluginHash) && !fileMd5.equalsIgnoreCase(mPluginConfig.getPluginHash())) {
                    reportHotterUpdateFail(FILE_MD5_DIFFERENT, "热更插件本地 apk md5 值和后台下发的不一致");
                    return;
                }

                mPluginConfig.setPluginLatestVersion(mPluginConfig.getPluginVersion());
                reportHotterUpdateSuccess();
            }

            @Override
            public void onFailure(Throwable throwable, int i, String s) {
                LogUtil.e("插件下载失败: " + s);
                reportHotterUpdateFail(i, s);
            }
        }).execute();
    }

    private void reportHotterUpdateSuccess() {
        //上报一次下载插件成功
        uploadDownloadState(true, "");
        HashMap<String, String> trackMap = new HashMap<>();
        trackMap.put(SqTrackKey.plug_id, mPluginConfig.getPluginConfId() + "");
        trackMap.put(SqTrackKey.plug_type, mPluginConfig.getPluginType() + "");
        trackMap.put(SqTrackKey.plug_version, mPluginConfig.getPluginVersion() + "");
        trackMap.put(SqTrackKey.plug_link, mPluginConfig.getPluginUrl());
        trackMap.put(SqTrackKey.plug_hash, mPluginConfig.getPluginHash());
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update_success, trackMap);
    }

    private void reportHotterUpdateFail(int failCode, String failReason) {
        uploadDownloadState(false, failReason);
        HashMap<String, String> trackMap = new HashMap<>();
        trackMap.put(SqTrackKey.plug_id, mPluginConfig.getPluginConfId() + "");
        trackMap.put(SqTrackKey.plug_type, mPluginConfig.getPluginType() + "");
        trackMap.put(SqTrackKey.plug_version, mPluginConfig.getPluginVersion() + "");
        trackMap.put(SqTrackKey.plug_link, mPluginConfig.getPluginUrl());
        trackMap.put(SqTrackKey.plug_hash, mPluginConfig.getPluginHash());
        trackMap.put(SqTrackKey.fail_code, failCode + "");
        trackMap.put(SqTrackKey.reason_fail, failReason);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.hotter_update_fail, trackMap);
    }

    /**
     * 获取文件的 md5
     */
    public static String getFileMd5(File file) throws Exception {
        FileInputStream inputStream = new FileInputStream(file);
        DigestInputStream digestInputStream = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            digestInputStream = new DigestInputStream(inputStream, messageDigest);
            byte[] buffer = new byte[1024 * 256];
            while (true) {
                if (digestInputStream.read(buffer) <= 0) {
                    break;
                }
            }
            messageDigest = digestInputStream.getMessageDigest();
            byte[] md5 = messageDigest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5) {
                sb.append(String.format("%02X", b));
            }
            return sb.toString().toLowerCase();
        } finally {
            closeStream(inputStream);
            closeStream(digestInputStream);
        }
    }

    public static void closeStream(Closeable closeable) {
        if (closeable == null) {
            return;
        }
        try {
            closeable.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报插件下载情况到后台
     *
     * @param isSuccess 是否成功
     * @param errorMsg  错误信息
     */
    private void uploadDownloadState(boolean isSuccess, String errorMsg) {
        if (isSuccess) {
            mRequestManager.pluginDownloadReport(mPluginConfig.getPluginVersion(), 1, mPluginConfig.getPluginType(), mPluginConfig.getPluginConfId(), "", null);
        } else {
            mRequestManager.pluginDownloadReport(mPluginConfig.getPluginVersion(), 0, mPluginConfig.getPluginType(), mPluginConfig.getPluginConfId(), errorMsg, null);
        }
    }

}
