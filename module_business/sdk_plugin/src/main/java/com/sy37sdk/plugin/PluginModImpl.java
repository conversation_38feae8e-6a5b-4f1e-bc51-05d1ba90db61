package com.sy37sdk.plugin;

import android.content.Context;
import com.sq.data.BuildConfig;
import com.sq.eventbus.annotation.Subscribe;
import com.sq.eventbus.core.EventBus;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tools.Logger;
import com.sqwan.common.eventbus.PreInitEvent;
import com.sqwan.common.eventbus.SActiveEvent;
import com.sqwan.common.mod.plugin.IPluginMod;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.plugin.data.PluginDownloadManager;
import com.sy37sdk.plugin.data.SpPluginConfig;
import com.sy37sdk.plugin.eventbus.EventBusIndex;
import com.sy37sdk.plugin.net.PluginRequestManager;
import org.json.JSONObject;

public class PluginModImpl implements IPluginMod {

    private final Context mContext;
    private PluginRequestManager mRequestManager;

    private SpPluginConfig mPluginConfig;

    public PluginModImpl(Context context) {
        mContext = context;
        EventBus.getDefault().addIndex(new EventBusIndex());
        EventBus.getDefault().register(this);
        mRequestManager = new PluginRequestManager();
        mPluginConfig = new SpPluginConfig(context);
        LogUtil.i("PluginModImpl init");
    }

    @Override
    public void refresh(Context context) {

    }

    @Override
    public int getPluginVersion() {
        return mPluginConfig.getPluginCurrentVersion();
    }


    @Subscribe()
    public void onSActiveEvent(SActiveEvent event) {
        if (!BuildConfig.isPluginMode) {
            return;
        }
        LogUtil.e("插件模块激活请求成功");

    }

    @Subscribe()
    public void onPreInitEvent(PreInitEvent event) {
        if (!BuildConfig.isPluginMode) {
            return;
        }

        Logger.info("当前插件版本号：" + mPluginConfig.getPluginCurrentVersion());
        //激活成功后请求配置
        mRequestManager.getPluginConfig(mPluginConfig.getPluginCurrentVersion(),
            new SimpleSqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject dataJson) {
                    parsePluginConfig(dataJson);
                }
            });
    }

    private void parsePluginConfig(JSONObject dataObj) {
        try {
            int configPluginVersion = dataObj.optInt("plugin_version");
            mPluginConfig.setPluginVersion(configPluginVersion);
            mPluginConfig.setPluginUrl(dataObj.optString("plugin_url"));
            mPluginConfig.setPluginHash(dataObj.optString("plugin_hash"));
            mPluginConfig.setPluginConfId(dataObj.optInt("conf_id"));
            mPluginConfig.setPluginType(dataObj.optInt("type"));
            int currentPluginVersion = mPluginConfig.getPluginCurrentVersion();
            if (currentPluginVersion != configPluginVersion) {
                //走文件下载逻辑
                LogUtil.i("当前插件版本: " + currentPluginVersion + ", 配置插件版本: " + configPluginVersion);
                new PluginDownloadManager(mContext).handlePlugin();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
