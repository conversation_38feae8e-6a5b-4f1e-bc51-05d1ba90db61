package com.sy37sdk.plugin.data;

import android.content.Context;

import com.sqwan.common.util.SpUtils;

public class SpPluginConfig {


    private SpUtils mSpUtils;

    /**
     * 插件配置文件名称
     */
    private final String SQ__PLUGIN_CONFIG = "sq_plugin_config";

    /**
     * 配置下发的插件版本
     */
    private final String KEY_PLUGIN_VERSION = "plugin_version";

    /**
     * 插件下发url
     */
    private final String KEY_PLUGIN_URL = "plugin_url";

    /**
     * 插件 apk md5 值
     */
    private final String KEY_PLUGIN_HASH = "plugin_hash";

    /**
     * 插件配置id
     */
    private final String KEY_CONF_ID = "plugin_conf_id";

    /**
     * 插件下发类型：全量下发/部分下发
     */
    private final String KEY_TAYE = "plugin_type";

    /**
     * 插件当前版本
     */
    private final String KEY_CURRENT_VERSION = "plugin_current_version";

    private final String KEY_LATEST_VERSION = "plugin_latest_version";

    public SpPluginConfig(Context context) {
        mSpUtils = new SpUtils(context, SQ__PLUGIN_CONFIG);
    }


    public void setPluginVersion(int pluginVersion) {
        mSpUtils.put(KEY_PLUGIN_VERSION, pluginVersion);
    }

    public int getPluginVersion() {
        return mSpUtils.getInt(KEY_PLUGIN_VERSION);
    }

    public void setPluginUrl(String pluginUrl) {
        mSpUtils.put(KEY_PLUGIN_URL, pluginUrl);
    }

    public String getPluginUrl() {
        return mSpUtils.getString(KEY_PLUGIN_URL);
    }

    public void setPluginHash(String pluginHash) {
        mSpUtils.put(KEY_PLUGIN_HASH, pluginHash);
    }

    public String getPluginHash() {
        return mSpUtils.getString(KEY_PLUGIN_HASH);
    }

    public void setPluginConfId(int confId) {
        mSpUtils.put(KEY_CONF_ID, confId);
    }

    public int getPluginConfId() {
        return mSpUtils.getInt(KEY_CONF_ID);
    }

    public void setPluginType(int type){
        mSpUtils.put(KEY_TAYE, type);
    }

    public int getPluginType() {
        return mSpUtils.getInt(KEY_TAYE);
    }

    public void setPluginCurrentVersion(int version) {
        mSpUtils.put(KEY_CURRENT_VERSION, version);
    }

    public int getPluginCurrentVersion() {
        return mSpUtils.getInt(KEY_CURRENT_VERSION);
    }

    public void setPluginLatestVersion(int latestVersion) {
        mSpUtils.put(KEY_LATEST_VERSION, latestVersion);
    }

    public int getPluginLatestVersion() {
        return mSpUtils.getInt(KEY_LATEST_VERSION);
    }


}
