apply plugin: 'com.android.library'

def isPluginMode = get_isPluginMode()

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {

        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"

        //2、android标签内defaultConfig标签处理
        defaultConfig {
            //...
            //增加如下配置,其中com.sq.sqinjectdemo换成对应包名，如果是lib工程，对应成lib工程R文件的包名
            javaCompileOptions {
                annotationProcessorOptions {
                    arguments = [packageName: 'com.sy37sdk.account.eventbus', className: 'EventBusIndex']
                }
            }
        }

        // 支持 Java JDK 8
        compileOptions {
            targetCompatibility JavaVersion.VERSION_1_8
            sourceCompatibility JavaVersion.VERSION_1_8
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

}

dependencies {
    api project(':37sdkcommon')
    if (!isPluginMode) {
        // 穿山甲SDK 依赖
        implementation project(':module_third:bytedance_advertise:bytedance_advertise_sdk')
    } else {
        compileOnly project(':module_third:bytedance_advertise:bytedance_advertise_sdk')
    }
}
