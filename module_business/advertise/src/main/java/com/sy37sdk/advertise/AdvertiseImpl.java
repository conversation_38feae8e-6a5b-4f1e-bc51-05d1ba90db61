package com.sy37sdk.advertise;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdConfig;
import com.bytedance.sdk.openadsdk.TTAdConstant;
import com.bytedance.sdk.openadsdk.TTAdLoadType;
import com.bytedance.sdk.openadsdk.TTAdManager;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdNative.RewardVideoAdListener;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTCustomController;
import com.bytedance.sdk.openadsdk.TTRewardVideoAd;
import com.bytedance.sdk.openadsdk.TTRewardVideoAd.RewardAdInteractionListener;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.advertise.IAdvertiseMod;
import com.sqwan.common.mod.config.IConfigMod;
import com.sqwan.common.util.AssetsUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.config.ConfigManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/8/26
 * @desc: 穿山甲广告实现类
 */
public class AdvertiseImpl implements IAdvertiseMod {

    private static final String KEY_ADVERTISE_APP_ID = "chuanshanjia_app_id";
    private static final String LOG_TAG = "chuanshanjia-AD";
    private boolean isInitSuccess = false;
    private String appId = "";

    @Override
    public void init(Context context) {
        appId = AssetsUtils.readProperties(context, AssetsUtils.SQ_MULTI_CONFIG)
            .getProperty(KEY_ADVERTISE_APP_ID);
        LogUtil.i(LOG_TAG, "init 穿山甲 appID " + appId);
        if (!TextUtils.isEmpty(appId)) {
            TTAdConfig config = new TTAdConfig.Builder().appId(appId)
                .customController(new TTCustomController() {
                    @Override
                    public boolean isCanUseLocation() {
                        return false;
                    }

                    @Override
                    public boolean alist() {
                        return false;
                    }

                    @Override
                    public boolean isCanUsePhoneState() {
                        return false;
                    }

                    @Override
                    public boolean isCanUseWriteExternal() {
                        return false;
                    }

                    @Override
                    public boolean isCanUseAndroidId() {
                        return false;
                    }

                    @Override
                    public boolean isCanUsePermissionRecordAudio() {
                        return false;
                    }
                })
                .directDownloadNetworkType(TTAdConstant.NETWORK_STATE_WIFI)
                .supportMultiProcess(false)
                .build();
            TTAdSdk.init(context, config);
            TTAdSdk.start(new TTAdSdk.Callback() {
                @Override
                public void success() {
                    isInitSuccess = true;
                    LogUtil.i(LOG_TAG, " 穿山甲 start success");
                }

                @Override
                public void fail(int i, String s) {
                    LogUtil.i(LOG_TAG, " 穿山甲 start fail, 原因：" + s + "code " + i);
                }
            });
        } else {
            LogUtil.i(LOG_TAG, "没有配置穿山甲APP_ID，不初始化");
        }

    }

    @Override
    public void showAdvertiseReward(Context context, String params, SQResultListener resultListener) {
        if (!isInitSuccess) {
            resultListener.onFailture(201, "穿山甲 初始化失败");
            LogUtil.i(LOG_TAG, "初始化失败，不打开广告页面");
            return;
        }
        if (context instanceof Activity) {
            TTAdManager manager = TTAdSdk.getAdManager();
            TTAdNative ttAdNative = manager.createAdNative(context);
            String adId = "";
            String extra = "";
            try {
                JSONObject jsonObject = new JSONObject(params);
                adId = jsonObject.optString("advertise_id");
                extra = getExtra(context, jsonObject, appId, adId);
                LogUtil.i(LOG_TAG, "穿山甲 extra -> " + extra);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (TextUtils.isEmpty(adId)) {
                resultListener.onFailture(201, "advertise_id is empty");
                return;
            }
            IConfigMod configMod = ModHelper.getConfig();
            AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(adId) // 广告代码位Id
                .setMediaExtra(extra)
                .setUserID(configMod.getCommonConfig().getUserId())
                .setAdLoadType(TTAdLoadType.LOAD) // 本次广告用途：TTAdLoadType.LOAD实时；TTAdLoadType.PRELOAD预请求
                .build();
            ttAdNative.loadRewardVideoAd(adSlot, new RewardVideoAdListener() {
                @Override
                public void onError(int i, String s) {
                    LogUtil.i(LOG_TAG, "穿山甲 onError " + s + " code : " + i);
                    ToastUtil.showToast("系统异常，请稍后再试（" + i + "）");
                }

                @Override
                public void onRewardVideoAdLoad(TTRewardVideoAd ttRewardVideoAd) {
                    ttRewardVideoAd.setRewardAdInteractionListener(new RewardAdInteractionListener() {
                        @Override
                        public void onAdShow() {
                            LogUtil.i(LOG_TAG, "穿山甲 onAdShow");
                        }

                        @Override
                        public void onAdVideoBarClick() {
                            LogUtil.i(LOG_TAG, "穿山甲 onAdVideoBarClick");
                        }

                        @Override
                        public void onAdClose() {
                            LogUtil.i(LOG_TAG, "穿山甲 onAdClose");
                        }

                        @Override
                        public void onVideoComplete() {
                            LogUtil.i(LOG_TAG, "穿山甲 onVideoComplete");
                        }

                        @Override
                        public void onVideoError() {
                            LogUtil.i(LOG_TAG, "穿山甲 onVideoError");
                            ToastUtil.showToast("广告展示出错，请稍后重试");
                        }

                        @Override
                        public void onRewardVerify(boolean rewardVerify, int rewardAmount, String rewardName,
                            int errorCode, String errorMsg) {
                            //废弃API，不需要实现
                        }

                        @Override
                        public void onRewardArrived(boolean isRewardValid, int rewardType, Bundle extraInfo) {
                            LogUtil.i(LOG_TAG, "穿山甲 奖励发放 onRewardVerify result : " + isRewardValid);
                            if (resultListener != null) {
                                if (isRewardValid) {
                                    resultListener.onSuccess(extraInfo);
                                } else {
                                    resultListener.onFailture(201, "RewardVerify is fail");
                                }
                            }
                        }

                        @Override
                        public void onSkippedVideo() {
                            LogUtil.i(LOG_TAG, "穿山甲 onSkippedVideo");
                        }
                    });
                    //视频加载完成
                    ttRewardVideoAd.showRewardVideoAd((Activity) context);
                }

                @Override
                public void onRewardVideoCached() {
                    LogUtil.i(LOG_TAG, "穿山甲 onRewardVideoCached ");
                }

                @Override
                public void onRewardVideoCached(TTRewardVideoAd ttRewardVideoAd) {
                    LogUtil.i(LOG_TAG, "穿山甲 onRewardVideoCached ad");
                }
            });
        } else {
            LogUtil.e(LOG_TAG, "传入 context 不是Activity，不展示广告");
            resultListener.onFailture(201, "传入 context 不是Activity，不展示广告");
        }
    }

    /*
     * 穿山甲透传给服务端的参数
     * */
    private String getExtra(Context context, JSONObject cpParams, String appId, String codeId) {
        IConfigMod configMod = ModHelper.getConfig();
        String pid = ConfigManager.getInstance(context).getSQAppConfig().getPartner();
        String gid = ConfigManager.getInstance(context).getSQAppConfig().getGameid();
        String uid = configMod.getCommonConfig().getUserId();
        String dsid = cpParams.optString("dsid");
        String dsname = cpParams.optString("dsname");
        String drid = cpParams.optString("drid");
        String drname = cpParams.optString("drname");
        String time = String.valueOf(System.currentTimeMillis() / 1000);

        Map<String, String> map = new HashMap<>();
        map.put("pid", pid);
        map.put("gid", gid);
        map.put("uid", uid);
        map.put("dsid", dsid);
        map.put("dsname", dsname);
        map.put("drid", drid);
        map.put("drname", drname);
        map.put("time", time);
        map.put("appid", appId);
        map.put("codeid", codeId);
        StringBuilder signStr = buildString(ConfigManager.getInstance(context).getAppKey(), map);
        String sign = sign(signStr);
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("pid", pid);
            jsonObject.put("gid", gid);
            jsonObject.put("uid", uid);
            jsonObject.put("dsid", dsid);
            jsonObject.put("dsname", dsname);
            jsonObject.put("drid", drid);
            jsonObject.put("drname", drname);
            jsonObject.put("time", time);
            jsonObject.put("appid", appId);
            jsonObject.put("codeid", codeId);
            jsonObject.put("sign", sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    private static StringBuilder buildString(String appKey, Map<String, String> temp) {
        // 3. 将参数以其参数名的字典序升序进行排序
        List<String> keys = new ArrayList<>(temp.keySet());
        Collections.sort(keys);
        // 4. 遍历排序后的参数数组中的每一个key/value对, 生成一个key=value格式的字符串
        StringBuilder signStr = new StringBuilder();
        for (String key : keys) {
            signStr.append(key).append('=').append(temp.get(key));
        }
        // 5. 在末尾连接上 key , 得到签名原始字符串
        signStr.append(appKey);
        return signStr;
    }


    private static String sign(StringBuilder signStr) {
        // 6. 对签名原始字符串计算md5值, 得到最终签名(小写)
        return MD5Util.Md5(signStr.toString()).toLowerCase(Locale.US);
    }

    public static String sign(Map<String, String> temp) {
        String appKey = ConfigManager.getInstance(SQContextWrapper.getApplicationContext()).getAppKey();
        return sign(appKey, temp);
    }

    public static String sign(String appKey, Map<String, String> temp) {
        if (temp == null || appKey == null) {
            return "";
        }
        StringBuilder signStr = buildString(appKey, temp);
        return sign(signStr);
    }
}
