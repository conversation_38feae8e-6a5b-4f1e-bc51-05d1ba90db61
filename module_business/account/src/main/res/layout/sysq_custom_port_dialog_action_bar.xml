<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="23dp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="23dp"
        android:textColor="#000000"
        android:textSize="19dp"
        android:visibility="gone"
        android:text="一键登录" />

    <RelativeLayout
      android:id="@+id/view_back"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:padding="15dp">

        <ImageView
          android:id="@+id/btn_back"
          android:layout_width="16dp"
          android:layout_height="16dp"
          android:layout_centerInParent="true"
          android:scaleType="fitCenter"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/view_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:padding="15dp">

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_centerInParent="true"
            android:scaleType="fitCenter"/>
    </RelativeLayout>

</RelativeLayout>