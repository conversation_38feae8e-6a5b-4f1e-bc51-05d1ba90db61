<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.sy37sdk.account">

  <uses-permission android:name="android.permission.CAMERA" />
  <application>
        <activity
            android:name="com.sy37sdk.account.face.ui.FaceVerifyConfirmActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <!-- 提供给阿里闪验使用 -->
        <activity
          android:name="com.sqwan.common.web.UserProtocolWebActivity"
          android:configChanges="orientation|keyboardHidden|screenSize|uiMode|fontScale"
          android:theme="@style/protocol_activity_dialog">
            <intent-filter>
                <action android:name="${applicationId}.protocolWeb" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
          android:name="com.sy37sdk.account.scanCode.ScanCodeCameraActivity"
          android:configChanges="orientation|keyboardHidden|screenSize"
          android:screenOrientation="portrait"
          android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />

      <activity
        android:name="com.sy37sdk.account.scanCode.ScanCodeConfirmActivity"
        android:configChanges="orientation|keyboardHidden|screenSize"
        android:screenOrientation="portrait"
        android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
    </application>
</manifest>
