package com.sqwan.common.web;

import android.annotation.SuppressLint;
import android.content.pm.ActivityInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import com.mobile.auth.gatewayauth.Constant;
import com.plugin.standard.RealBaseActivity;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.webview.SQCommonJsInterface;
import com.sqwan.common.webview.SQWeb;
import com.sqwan.common.webview.SQWebView;

public class UserProtocolWebActivity extends RealBaseActivity {

    private SQWebView mWebView;

    private WebViewToolBar webToolbar;

    //阈值
    private static final int threshold = 50;
    //处理手势
    private float posX, posY, curPosX, curPosY;
    //下滑
    private static final int SCROLL_DOWN = 0;
    //上滑
    private static final int SCROLL_UP = 1;
    //当前的滑动方向
    private int curScrollDirection = -1;
    //正在滑动的方向
    private int scrollDirection = -1;

    //是否展示webview的工具栏
    private boolean showToolBar;

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        String protocol_name = getIntent().getStringExtra(Constant.PROTOCOL_WEB_VIEW_NAME);
        //联通用户协议h5未加背景色，需要主动加上
        if (!TextUtils.isEmpty(protocol_name) && protocol_name.contains("联通")) {
            setTheme(SqResUtils.getStyleId(SQContextWrapper.getActivity(), "protocol_activity"));
        } else {
            setTheme(SqResUtils.getStyleId(SQContextWrapper.getActivity(), "protocol_activity_dialog"));
        }
        setContentView(SqResUtils.getLayoutId(SQContextWrapper.getActivity(), "sysq_user_protocol"));
        initWindow();
        mWebView = findViewById(SqResUtils.getId(SQContextWrapper.getActivity(), "webView"));
        webToolbar = findViewById(SqResUtils.getId(SQContextWrapper.getActivity(), "web_tool_bar"));
        initWebView();
        initWebToolBar();
        String mUrl = getIntent().getStringExtra("url");
        setRequestedOrientation(getIntent().getIntExtra("orientation", ActivityInfo.SCREEN_ORIENTATION_PORTRAIT));
        if (!TextUtils.isEmpty(mUrl)) {
            mWebView.loadUrl(mUrl);
            mWebView.addJavascriptInterface(new CustomJsObj(mWebView), SQCommonJsInterface.INTERFACE_NAME);
            showToolBar = !mUrl.contains("useragreement");
            webToolbar.setVisibility(showToolBar ? View.VISIBLE : View.GONE);
        }

    }

    private void initWebToolBar() {
        webToolbar.setWebToolBarClickListener(new WebViewToolBar.WebToolBarClickListener() {
            @Override
            public void onClickBack() {
                if (mWebView.canGoBack()) {
                    mWebView.goBack();
                }
            }

            @Override
            public void onClickForward() {
                if (mWebView.canGoForward()) {
                    mWebView.goForward();
                }
            }

            @Override
            public void onClickRefresh() {
                mWebView.reload();
            }

            @Override
            public void onClickClose() {
                mHandler.post(() -> finish());
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    private void handleGesture() {
        mWebView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        posX = event.getX();
                        posY = event.getY();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        curPosX = event.getX();
                        curPosY = event.getY();
                        if ((curPosY - posY > 0) && (Math.abs(curPosY - posY) > threshold)) {
                            //向下滑动显示web工具栏
                            scrollDirection = SCROLL_DOWN;
                            if (curScrollDirection != scrollDirection && showToolBar) {
                                webToolbar.showAnimation();
                                curScrollDirection = scrollDirection;
                            }
                        } else if ((curPosY - posY < 0) && (Math.abs(curPosY - posY) > threshold)) {
                            //向上滑动隐藏web工具栏
                            scrollDirection = SCROLL_UP;
                            if (curScrollDirection != scrollDirection && showToolBar) {
                                webToolbar.hideAnimation();
                                curScrollDirection = scrollDirection;
                            }
                        }
                        break;
                }
                return false;
            }

        });
    }

    private void initWindow() {
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            getWindow().setAttributes(layoutParams);
        }

        Window window = getWindow();
        window.setGravity(Gravity.CENTER);
        window.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
        lp.height = ViewGroup.LayoutParams.MATCH_PARENT;
        // 这里还可以设置lp.x，lp.y在x轴，y轴上的坐标，只是这个位置是基于Gravity的
        window.setAttributes(lp);
        //隐藏导航栏
        StatusBarUtil.hideSystemUI(getWindow());
    }

    private void initWebView() {
        SQWeb.getInstance().bind(mWebView).disableLocalH5();
        SQWeb.enableCache(mWebView);
        mWebView.setBackgroundColor(0);
        WebSettings wvSettings = mWebView.getSettings();
        // 是否阻止网络请求
        wvSettings.setBlockNetworkLoads(false);
        wvSettings.setDatabaseEnabled(true);
    }

    public class CustomJsObj extends SQCommonJsInterface {

        public CustomJsObj(@NonNull SQWebView webView) {
            super(webView);
        }

        @JavascriptInterface
        public void enClose(final String tag, final String data) {
            log("enClose", "tag = " + tag + ", data = " + data);
            mHandler.post(() -> finish());
        }
    }
}
