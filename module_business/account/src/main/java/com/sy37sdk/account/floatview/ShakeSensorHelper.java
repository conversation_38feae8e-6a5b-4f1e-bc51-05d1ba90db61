package com.sy37sdk.account.floatview;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Vibrator;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.msdk.config.ConfigManager;

import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-01-20 10:16
 */
public class ShakeSensorHelper {
    private String tag = this.getClass().getSimpleName();
    private static ShakeSensorHelper ourInstance;
    private static Context mContext;
    private SensorManager sensorManager;
    private ShakeSensorListener shakeListener;
    private boolean isShake;
    private Task task;
    private List<SensorChangedCallback> sensorChangedCallbacks = new ArrayList<>();
    private boolean hasRegister;
    // 速度阈值，当摇晃速度达到这值后产生作用
    private static final int SPEED_SHRESHOLD = 5000;
    // 两次检测的时间间隔
    private static final int UPTATE_INTERVAL_TIME = 70;
    // 加速度阈值
    private static final float SHAKE_THRESHOLD = 12.0f;
    //晃动时间间隔
    private static final int SHAKE_INTERVAL_TIME = 500;
    //连续晃动时间
    private static final int SHAKE_CONTINUE_TIME = 1000;
    //晃动起始时间
    private long mShakeStartTime = 0L;
    //上一次晃动时间
    private long mLastShakeTime = 0L;
    // 手机上一个位置时重力感应坐标
    private float lastX;
    private float lastY;
    private float lastZ;
    // 上次检测时间
    private long lastUpdateTime;
    public static ShakeSensorHelper getInstance(Context context) {
        if(ourInstance == null) {
            synchronized (ConfigManager.class) {
                if(ourInstance == null) {
                    ourInstance = new ShakeSensorHelper(context);
                }
            }
        }
        return ourInstance;
    }

    public boolean isShake() {
        return isShake;
    }

    public void setShake(boolean shake) {
        isShake = shake;
    }

    public void addSensorChangedCallback(SensorChangedCallback sensorChangedCallback) {
        this.sensorChangedCallbacks.add(sensorChangedCallback);
    }
    public void removeSensorChangedCallback(SensorChangedCallback sensorChangedCallback) {
        this.sensorChangedCallbacks.remove(sensorChangedCallback);
    }

    private ShakeSensorHelper(Context context) {
        if (mContext==null) {
            mContext = context.getApplicationContext();
            sensorManager = (SensorManager)mContext.getSystemService(Context.SENSOR_SERVICE);
            task = Task.create();
            shakeListener = new ShakeSensorListener();
        }

    }
    public interface SensorChangedCallback{
        void onSensorChanged(Context context,SensorEvent event);
    }
    class ShakeSensorListener implements SensorEventListener {

        @Override
        public void onSensorChanged(final SensorEvent event) {
            //避免一直摇
            if (isShake) {
                return;
            }
            // 现在检测时间
            long currentUpdateTime = System.currentTimeMillis();
            // 两次检测的时间间隔
            long timeInterval = currentUpdateTime - lastUpdateTime;

            // 判断是否达到了检测时间间隔
            if (timeInterval < UPTATE_INTERVAL_TIME)
                return;
            // 现在的时间变成last时间
            lastUpdateTime = currentUpdateTime;

            // 获得x,y,z坐标
            float x = event.values[0];
            float y = event.values[1];
            float z = event.values[2];

            // 获得x,y,z的变化值
            float deltaX = x - lastX;
            float deltaY = y - lastY;
            float deltaZ = z - lastZ;

            // 将现在的坐标变成last坐标
            lastX = x;
            lastY = y;
            lastZ = z;

            //加速度
            double acceleration = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ) - SensorManager.GRAVITY_EARTH;
            LogUtil.i(tag, "acceleration = " + acceleration);
            if (acceleration >= SHAKE_THRESHOLD) {
                if (mShakeStartTime == 0) {
                    mShakeStartTime = currentUpdateTime;
                }
                mLastShakeTime = currentUpdateTime;
            } else {
                //500ms没有晃动，重置开始时间
                if (mLastShakeTime != 0  && currentUpdateTime - mLastShakeTime > SHAKE_INTERVAL_TIME) {
                    resetShakeStatics();
                }
            }

            long shakeContinueTime = currentUpdateTime - mShakeStartTime;
            LogUtil.d(tag, "shakeContinueTime = " + shakeContinueTime);
            if ((mShakeStartTime > 0 && shakeContinueTime > SHAKE_CONTINUE_TIME)) {
                LogUtil.i(tag,"shake");
                isShake = true;
                //开始震动，重置统计时间，执行回调
                resetShakeStatics();
                //仿网络延迟操作，这里可以去请求服务器...
                task.oneShot(500, new Task.TaskFunc() {
                    @Override
                    public Task.Result exec() {
                        for (SensorChangedCallback sensorChangedCallback : sensorChangedCallbacks) {
                            if (sensorChangedCallback!=null) {
                                sensorChangedCallback.onSensorChanged(mContext,event);
                            }
                        }

                        return null;
                    }
                });
            }

        }

        @Override
        public void onAccuracyChanged(Sensor sensor, int accuracy) {
            LogUtil.d(tag,"onAccuracyChanged accuracy " + accuracy);
        }
    }

    private void resetShakeStatics() {
        mShakeStartTime = 0L;
        mLastShakeTime = 0L;
    }

    public void onResume() {
        LogUtil.d(tag,"onResume");
        if (sensorManager!=null) {
            if (!hasRegister) {
                sensorManager.registerListener(shakeListener, sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER),
                        SensorManager.SENSOR_DELAY_FASTEST);
                hasRegister = true;
                isShake=false;
                LogUtil.d(tag,"hasRegister true");
            }

        }

    }

    public void onPause() {
        LogUtil.d(tag,"onPause");
        if (sensorManager!=null) {
            if (hasRegister) {
                sensorManager.unregisterListener(shakeListener);
                hasRegister = false;
                LogUtil.d(tag,"hasRegister false");
            }
        }
        if (task!=null) {
            task.stop();
        }
        resetShakeStatics();
    }
    public void release(){
        onPause();
    }

}
