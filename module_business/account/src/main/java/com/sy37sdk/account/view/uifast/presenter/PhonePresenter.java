package com.sy37sdk.account.view.uifast.presenter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.sdk.libs.SqR;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AutoAccountBean;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.policy.view.PolicyDialog;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.view.uifast.view.IPhoneView;

import java.util.Map;

public class PhonePresenter extends BaseAccountPagerPresenter<IPhoneView> implements IPhonePresenter {

    //手机号格式错误
    public static final int CODE_PHONE_VALID = -77710;

    /**
     * 注册条款是否勾选已读
     */
    private boolean clauseStatus = false;

    public PhonePresenter(Context context, IPhoneView view) {
        super(context, view);
    }

    @Override
    public View getView() {
        return (View) mView;
    }

    @Override
    public void initData() {
        super.initData();
        mView.regEntrance(EntranceManager.getInstance().isQuickRegEntrance());
        mView.accountLoginEntrance(EntranceManager.getInstance().isAccountLoginEntrance());
    }

    @Override
    public void obtainVerifyCode() {
        String phoneNumber = mView.getPhone();
        if (TextUtils.isEmpty(phoneNumber)) {
            ToastUtil.showToast(context, "请输入手机号");
        } else if (!AppUtils.isMobileNO(phoneNumber)) {
            ToastUtil.showToast(context, "请填写正确的手机号");
        } else if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                clauseStatus = true;
                mView.checkedClause();
                obtainVerifyCode();
            });
        } else {
            if (mView != null) {
                mView.showLoading();
            }
            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.getPhoneCode, SqTrackBtn.SqTrackBtnExt.getPhoneCode);
            AccountLogic.getInstance(context).sendPhoneCode(phoneNumber, new AccountLogic.VerifyCodeListener() {
                @Override
                public void onSuccess() {
                    if (mView != null) {
                        mView.hideLoading();
                        mView.startVerifyCodeView();
                    }
                    ToastUtil.showToast(context, "请求已发送，请注意查收短信");
                }

                @Override
                public void onFailure(int code, String msg) {
                    ToastUtil.showToast(context, msg);
                    if (mView != null) {
                        mView.hideLoading();
                    }
                    if (code != CODE_PHONE_VALID) {
                        //获取验证码失败，跳转页面
                        if (mView != null) {
                            mView.startVerifyCodeView();
                        }
                    }
                }
            });
        }
    }

    /**
     * 快速游戏
     * 1.快速生成账号
     * 2.静默注册账号
     */
    @Override
    public void quickStart() {
        if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                mView.checkedClause();
                clauseStatus = true;
                quickStart();
            });
            return;
        }
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.quickStart, SqTrackBtn.SqTrackBtnExt.quickStart);
        String autoName = AccountCache.getAutoName(context);
        String autoPassword = AccountCache.getAutoPassword(context);
        if (AccountCache.getUsername(context).equals(autoName) || TextUtils.isEmpty(autoPassword)) {
            authReq();
        } else {
            register(autoName, autoPassword);
        }

    }

    private void register(String name, String pwd) {
        if ("".equals(name) || "".equals(pwd)) {
            ToastUtil.showToast(context, SqResUtils.getStringByName(context, SqR.string.sy37_reg_input_empty_tips));
            return;
        }

        if (!name.equals(AccountCache.getAutoName(context)) && name.substring(0, 4).equals("37zd")) {
            ToastUtil.showToast(context, SqResUtils.getStringByName(context, SqR.string.sy37_reg_not_auto_account_tips));
            return;
        }
        if (mView != null) {
            mView.showLoading();
        }
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_quick_start);
        AccountLogic.getInstance(context).accountRegister(name, pwd, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                LoginTractionManager.track(LoginTractionManager.login_way_quick_start, data);
                if (mView != null) {
                    mView.hideLoading();
                    mView.accountRegSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_quick_start, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, msg);
            }
        });
    }


    @Override
    public void clauseClick(boolean isCheck) {
        this.clauseStatus = isCheck;
        if (isCheck) {
            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.agreement, SqTrackBtn.SqTrackBtnExt.agreement);
        }
    }

    @Override
    public void toClausePage() {
        UAgreeManager.getInstance().showUserProtocol(context);
    }

    @Override
    public void toPolicy() {
        UAgreeManager.getInstance().showPolicy(context);
    }

    @Override
    public void forgetPassword() {
        super.forgotPassword();
    }

    private void authReq() {
        AccountLogic.getInstance(context).autoAccount(new AccountLogic.AutoAccountListener() {

            @Override
            public void onSuccess(AutoAccountBean autoAccountBean) {
                LogUtil.i("msg: " + autoAccountBean.getMsg() + ", ssuccess:" + autoAccountBean.getSsuccess());
                String uname = autoAccountBean.getUname();
                String pwd = autoAccountBean.getPwd();
                AccountCache.setAutoName(context, uname);
                AccountCache.setAutoPassword(context, pwd);
                AccountCache.setAutoIssave(context, autoAccountBean.getIssave());
                AccountCache.setAutoState(context, autoAccountBean.getAutostate());
                register(uname, pwd);
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.e("请求自动注册账号失败code=" + code + ",meg=" + msg);
            }
        });
    }
}
