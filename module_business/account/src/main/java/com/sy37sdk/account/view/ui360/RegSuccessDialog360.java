package com.sy37sdk.account.view.ui360;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.QrCodeInfo;
import com.sy37sdk.account.view.IRegSuccessDialog;
import com.sy37sdk.account.view.LoginSkinHelper;

/**
 * <AUTHOR>
 * @date 2020/2/20
 */
public class RegSuccessDialog360 extends BaseDialog implements IRegSuccessDialog {

    private TextView tvAccount;
    private TextView tvPassword;
    private TextView tvEnterGame;
    private TextView tvTitle;
    private View close;
    private TextView tvTip;

    private View rootView;

    private View contentView;

    private EnterGameListener enterGameListener;
    private String uname, pwd;

    private boolean isGoInGame = false;

    public RegSuccessDialog360(Context context, String uname, String pwd) {
        super(context);
        this.uname = uname;
        this.pwd = pwd;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getContext().setTheme(getIdByName("Mdialog", "style"));
        rootView = LayoutInflater.from(getContext()).inflate(getIdByName("sysq_reg_success", "layout"), null);
        setContentView(rootView);
        initView();
    }

    private void initView() {
        tvTitle = findViewById(getIdByName("tv_title", "id"));
        tvAccount = findViewById(getIdByName("tv_account", "id"));
        tvPassword = findViewById(getIdByName("tv_password", "id"));
        tvEnterGame = findViewById(getIdByName("tv_enter_game", "id"));
        tvEnterGame.setBackgroundResource(LoginSkinHelper.getLoginBtnBackgroundResId(getContext()));
        tvEnterGame.setTextColor(LoginSkinHelper.getLoginBtnTextColor(getContext()));
        tvTip = findViewById(getIdByName("tv_no_qr_success", "id"));
        close = findViewById(getIdByName("view_close", "id"));
        contentView = findViewById(getIdByName("content", "id"));
        contentView.setBackgroundResource(LoginSkinHelper.getHistoryLoginBackgroundResId(getContext()));
        initAction();
        initData();
        setCanceledOnTouchOutside(false);
    }

    private void initAction() {
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (!isGoInGame && enterGameListener != null) {
                    enterGameListener.cancel();
                }
            }
        });
        tvEnterGame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isGoInGame = true;
                dismiss();
                if (enterGameListener != null) {
                    enterGameListener.enterGame();
                }
            }
        });
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

    }

    public void initData() {
        tvTitle.setText("注册成功");
        tvAccount.setText("账号：" + uname);
        tvPassword.setText("密码：" + pwd);
        if (AccountCache.getAutoIssave(mContext)) {
            saveAccountView();
        }
    }

    private void saveAccountView() {
        new Handler(Looper.getMainLooper()).postDelayed(
                new Runnable() {
                    @Override
                    public void run() {
                        boolean save = AppUtils.saveView(getContext(), rootView, "37_" + uname + ".png");
                        if (!save) {
                            tvTip.setText(SqResUtils.getStringByName(mContext, "sysq_save_account"));
                        } else {
                            tvTip.setText(SqResUtils.getStringByName(mContext, "sysq_save_account_fail"));
                        }
                    }
                }, 500);
    }

    @Override
    public void setQrCodeMessage(QrCodeInfo qrCodeInfo) {

    }

    @Override
    public void setEnterGameListener(EnterGameListener listener) {
        enterGameListener = listener;
    }

}
