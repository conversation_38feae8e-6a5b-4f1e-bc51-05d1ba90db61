package com.sy37sdk.account.floatview.request.websocket.factory;

import android.text.TextUtils;
import com.sq.websocket_engine.ARecInfMsg;
import com.sq.websocket_engine.ARecInfMsgBaseFactory;

public abstract class FloatWindowRedDotMsgBaseFactory extends ARecInfMsgBaseFactory {

    @Override
    protected ARecInfMsg convert(String ev) {
        if (TextUtils.equals(ev, getFloatViewRedMsgEv())) {
            return new FloatWindowRedDotRecInfMsg();
        }
        return null;
    }

    public String getFloatViewRedMsgEv(){
        return "fl.rd.nt";
    }
}