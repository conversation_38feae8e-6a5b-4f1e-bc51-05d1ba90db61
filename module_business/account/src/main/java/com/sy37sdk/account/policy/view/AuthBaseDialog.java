package com.sy37sdk.account.policy.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.URLSpan;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-15 19:52
 */
abstract public class AuthBaseDialog extends Dialog {
    protected String userprotol = ActiveBeforeManager.getInstance().userProtocolInfo.userprotol;
    protected String privacyprotol = ActiveBeforeManager.getInstance().userProtocolInfo.privacyprotol;
    public static class ClickCallbackAdapter implements ClickCallback {

        @Override
        public void onClickCancel() {

        }

        @Override
        public void onClickOk() {

        }
    }
    public interface ClickCallback{
        void onClickCancel();
        void onClickOk();
    }
    private ClickCallback clickCallback;

    public ClickCallback getClickCallback() {
        return clickCallback;
    }

    public void setClickCallback(ClickCallback clickCallback) {
        this.clickCallback = clickCallback;
    }
    public static class URLSpanNoUnderline extends URLSpan {
        private final Context mContext;
        private String mColorStr;
        public URLSpanNoUnderline(String url, Context context) {
            super(url);
            mContext = context;
        }

        public void setTextColor(String colorStr) {
            this.mColorStr = colorStr;
        }

        @Override
        public void onClick(View widget) {
            String url  = getURL();
            LogUtil.i("url " + url);
            if (!TextUtils.isEmpty(url)) {
                SQWebViewDialog dialog = new SQWebViewDialog(mContext);
                dialog.setCancelable(true);
                dialog.setUrl(url);
                dialog.show();
            }
        }

        @Override
        public void updateDrawState(TextPaint ds) {
            super.updateDrawState(ds);
            ds.setUnderlineText(false);
            if (TextUtils.isEmpty(mColorStr)) {
                ds.setColor(Color.parseColor("#f59a23"));
            } else {
                ds.setColor(Color.parseColor(mColorStr));
            }

        }
    }
    protected TextView tvOk,tvCancel,tvTitle;
    protected Context mContext;
    public AuthBaseDialog(Context context) {
        super(context, SqResUtils.getStyleId(context,"CustomDialog"));
        this.mContext = context;
    }

    protected abstract String getContainerLayout();
    protected abstract void doEngine();
    protected abstract String getTitle();
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Configuration configuration = getContext().getResources().getConfiguration();
        boolean isLandScape = configuration.orientation == Configuration.ORIENTATION_LANDSCAPE;
        setContentView(SqResUtils.getLayoutId(mContext,"sy37_dialog_permission_preview"));
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        doBase();
        doEngine();
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams params = getWindow().getAttributes();
        if (isLandScape) {
            params.width = display.getWidth()/4*3;
            params.gravity = Gravity.BOTTOM|Gravity.CENTER_HORIZONTAL;
        }else{
            params.gravity = Gravity.BOTTOM|Gravity.LEFT;
            params.width = display.getWidth();
        }
        getWindow().setAttributes(params);
    }
    protected int findId(String str){
        return SqResUtils.getId(mContext,str);
    }
    protected int findLayout(String str){
        return SqResUtils.getLayoutId(mContext,str);
    }
    protected void doBase(){
        ViewGroup container = findViewById(findId("rl_container"));
        container.addView(View.inflate(mContext,findLayout(getContainerLayout()),null),new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT));
        tvOk = findViewById(findId("tv_ok"));
        tvTitle = findViewById(findId("tv_title"));
        tvTitle.setText(getTitle());
        tvCancel = findViewById(findId("tv_cancel"));
        tvOk.setSelected(true);
        tvCancel.setSelected(true);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clickCallback!=null) {
                    clickCallback.onClickCancel();
                }
                onClickCancel();
            }
        });
        tvOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (clickCallback!=null) {
                    clickCallback.onClickOk();
                }
                onClickOk();
            }
        });

    }
    protected void onClickOk(){


    }
    protected void onClickCancel(){

    }
    protected void exit(){
        if (mContext instanceof Activity) {
            ((Activity)mContext).finish();
            System.exit(0);
        }

    }

    @Override
    public void show() {
        if (mContext instanceof Activity) {
            if (((Activity)mContext).isFinishing()) {
                return;
            }
        }
        super.show();
    }

    protected SpannableString setLink(TextView textView, String txt){
        SpannableString ss = new SpannableString(txt);
        String tag1= "《用户协议》";
        String tag2= "《隐私政策》";
        ss.setSpan(new URLSpanNoUnderline(userprotol, mContext), txt.indexOf(tag1), txt.indexOf(tag1)+tag1.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ss.setSpan(new URLSpanNoUnderline(privacyprotol, mContext), txt.indexOf(tag2), txt.indexOf(tag2)+tag2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        textView.setText(ss);
        textView.setMovementMethod(LinkMovementMethod.getInstance());
        textView.setHighlightColor(Color.TRANSPARENT);
        return ss;
    }
}
