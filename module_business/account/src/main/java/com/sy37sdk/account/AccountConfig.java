package com.sy37sdk.account;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020-05-12
 */
public class AccountConfig {

    /**
     * 公众号引导弹窗文案
     */
    public static String doubtGuideContent = "";
    public static String doubtGuideCopyContent = "";

    public static String tips_notice = "欢迎来到37手游，祝您游戏愉快!";
    public static String notice_url = "";
    public static String tips_downinfo = "";
    public static String downinfo_url = "";
    public static String gameName = "";
    public static String packageName = "com.sy37.gamebox";
    public static String exitImgPath = "";
    public static String exitUrl = "";
    public static String exitDpgn = "";



    public static void praseConfig(String data) {
        try {
            JSONObject dataJson = new JSONObject(data);
            if(dataJson.has("c")) {
                JSONObject configJson = dataJson.getJSONObject("c");
                if(configJson.has("content")) {
                    JSONObject contentJson = configJson.getJSONObject("content");
                    doubtGuideContent = contentJson.optString("totastKf", "");
                    doubtGuideCopyContent = contentJson.optString("totastGzh", "");
                }

                if(configJson.has("gg")) {
                    JSONObject ggJson = configJson.getJSONObject("gg");
                    tips_notice = ggJson.optString("n", "");
                    notice_url = ggJson.optString("u", "");
                }

                if(configJson.has("dl")) {
                    JSONObject dlJson = configJson.getJSONObject("dl");
                    tips_downinfo = dlJson.optString("n","");
                    downinfo_url = dlJson.optString("u","");
                    packageName = dlJson.optString("dpgn","");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

}
