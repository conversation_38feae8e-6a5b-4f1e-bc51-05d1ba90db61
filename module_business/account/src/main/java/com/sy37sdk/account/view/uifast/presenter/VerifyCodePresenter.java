package com.sy37sdk.account.view.uifast.presenter;

import android.content.Context;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;
import com.sy37sdk.account.view.uifast.view.IVerifyCodeView;

import java.util.Map;

public class VerifyCodePresenter extends BaseAccountPagerPresenter<IVerifyCodeView> implements IVerifyCodePresenter {

    private static final long TIMER_THRESHOLD = 60 * 1000L;
    private VerifyCodeTimer timer;

    public VerifyCodePresenter(Context context, IVerifyCodeView view) {
        super(context, view);
    }

    @Override
    public View getView() {
        return (View) mView;
    }

    @Override
    public void sendCode(String phone) {
        mView.showLoading();
        AccountLogic.getInstance(context).sendPhoneCode(phone, new AccountLogic.VerifyCodeListener() {
            @Override
            public void onSuccess() {
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, "请求已发送，请注意查收短信");
                initTimer();
            }

            @Override
            public void onFailure(int code, String msg) {
                ToastUtil.showToast(context, msg);
                if (mView != null) {
                    mView.hideLoading();
                }
            }
        });
    }

    @Override
    public void loginPwd(String phone, String pwd) {
        LogUtil.i("调用手机+密码登录");
        if (TextUtils.isEmpty(pwd)) {
            ToastUtil.showToast(context, "请输入密码");
            return;
        }
        mView.showLoading();
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_phone_pwd);
        AccountLogic.getInstance(context).phoneLoginPwd(phone, pwd, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> account) {
                LogUtil.i("login success");
                trackLoginSuccess(account, false);
                if (mView != null) {
                    mView.hideLoading();
                    mView.loginSuccess(account);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.i("login onFailure");
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_phone_pwd, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, msg);
            }
        });
    }

    @Override
    public void loginVerifyCode(String phone, String code) {
        LogUtil.i("调用手机+验证码登录");
        mView.showLoading();
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.verifyPhoneCode, SqTrackBtn.SqTrackBtnExt.verifyPhoneCode);
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_phone_code);
        AccountLogic.getInstance(context).phoneLoginCheckCode(phone, code, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> account) {
                LogUtil.i("login success");
                trackLoginSuccess(account, true);
                if (mView != null) {
                    mView.hideLoading();
                    mView.loginSuccess(account);
                }
                AccountCache.setTicketState(false);
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.i("login onFailure");
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_phone_code, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                    if (AccountCache.getTicketState()){
                        mView.startView(AccountPage.ACCOUNT_HISTORY_PAGE, null);
                        AccountCache.setTicketState(false);
                    }
                }
                ToastUtil.showToast(context, msg);
            }
        });
    }

    @Override
    public void initTimer() {
        long storeLastRequestTime = AccountCache.getVerifyCodeLastTime(context);
        long currentTime = System.currentTimeMillis();
        long spaceTime = currentTime - storeLastRequestTime;
        LogUtil.i("storeLastRequestTime=" + storeLastRequestTime + " currentTime=" + currentTime + " spaceTime=" + spaceTime + "spaceTime < TIMER_THRESHOLD?" + (spaceTime < TIMER_THRESHOLD));
        LogUtil.i("TIMER_THRESHOLD - spaceTime=" + (TIMER_THRESHOLD - spaceTime));
        if (timer != null) {
            timer.cancel();
        }
        timer = new VerifyCodeTimer((TIMER_THRESHOLD - spaceTime), 1000);
        timer.start();
    }

    @Override
    public void toAppeal() {
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.loginHelp, SqTrackBtn.SqTrackBtnExt.loginHelp);
        AppUtils.toSQWebUrl(context, UrlConstant.APPEAL_PAGE, "账号申诉");
    }


    class VerifyCodeTimer extends CountDownTimer {

        VerifyCodeTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            if (mView != null) {
                mView.verifyCodeStatus(true);
                mView.verifyCodeText("重新发送");
            }
        }

        @Override
        public void onTick(long millisUntilFinished) {
            if (mView != null) {
                mView.verifyCodeStatus(false);
                mView.verifyCodeText(millisUntilFinished / 1000 + "");
            }
        }
    }

    private void trackLoginSuccess(Map<String, String> account, boolean isCode) {
        LoginTractionManager.track(isCode ? LoginTractionManager.login_way_phone_code : LoginTractionManager.login_way_phone_pwd, account);
    }

}
