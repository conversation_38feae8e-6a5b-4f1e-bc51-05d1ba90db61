package com.sy37sdk.account.scanCode;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import com.plugin.standard.RealBaseActivity;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import java.util.HashMap;
import java.util.Map;
import notchtools.geek.com.notchtools.NotchTools;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/12/19
 * @desc: 扫码登录确认
 */
public class ScanCodeConfirmActivity extends RealBaseActivity {

    public static final String KEY_QRCODE = "key_qrcode";
    private String mQrCode;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(SqResUtils.getLayoutId(getContext(), "sy37_scan_code_confirm_activity"));
        //隐藏导航栏
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                StatusBarUtil.hideSystemUI(getWindow());
                int notchHeight = NotchTools.getFullScreenTools().getStatusHeight(getWindow());
                View v = findViewById(SqResUtils.getId(getContext(), "title_bar"));
                FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) v.getLayoutParams();
                layoutParams.topMargin = notchHeight;
                v.setLayoutParams(layoutParams);
            }
        });
        mQrCode = getIntent().getStringExtra(KEY_QRCODE);
        TextView tvLogin = findViewById(SqResUtils.getId(getContext(), "tv_login"));
        TextView tvCancel = findViewById(SqResUtils.getId(getContext(), "tv_cancel"));
        ImageView ivBack = findViewById(SqResUtils.getId(getContext(), "iv_back"));
        ivBack.setOnClickListener(v -> cancelLogin());
        tvLogin.setOnClickListener(v -> confirmLogin());
        tvCancel.setOnClickListener(v -> cancelLogin());
    }


    private void confirmLogin() {
        LogUtil.i(buildPrefixLog("确认授权"));
        Map<String, String> map = new HashMap<>();
        map.put("confirm_result", "1");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_CONFIRM_SUCC, map);
        ScanCodeRequest.confirmAuth(AccountCache.getToken(getContext()), mQrCode, new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject jsonObject) {
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_LOGIN_SUCC, map);
                finish();
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                super.onResponseStateError(httpStatus, state, msg, data);
                trackScanLoginFail(msg);
                LogUtil.d(buildPrefixLog("确认授权失败 onResponseStateError msg: " + msg + " state " + state));
                ToastUtil.showToast(msg);
                finish();
                startScanCodeActivity();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                super.onFailure(code, errorMsg, error);
                trackScanLoginFail(errorMsg);
                LogUtil.w(buildPrefixLog("确认授权失败 onFailure msg: " + errorMsg + " state " + code));
                ToastUtil.showToast(getContext(), errorMsg);
            }
        });
    }

    private void cancelLogin() {
        LogUtil.i(buildPrefixLog("取消授权"));
        Map<String, String> map1 = new HashMap<>();
        map1.put("confirm_result", "2");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_CONFIRM_SUCC, map1);
        trackScanLoginFail("取消登录"); //取消也需要额外埋点
        ScanCodeRequest.cancelAuth(AccountCache.getToken(getContext()), mQrCode, new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject jsonObject) {
                finish();
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                super.onResponseStateError(httpStatus, state, msg, data);
                LogUtil.d(buildPrefixLog("取消授权失败 onResponseStateError msg: " + msg + " state " + state));
                finish();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                super.onFailure(code, errorMsg, error);
                LogUtil.w(buildPrefixLog("取消授权 onFailure msg: " + errorMsg + " state " + code));
                finish();
            }
        });
    }

    private void startScanCodeActivity() {
        Intent intent = new Intent();
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setClass(getContext(), ScanCodeCameraActivity.class);
        intent.putExtra("screenOrientation", "portrait");
        getContext().startActivity(intent);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            cancelLogin();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private String buildPrefixLog(String msg) {
        return "【ScanCode】" + msg;
    }

    /**
     * @param reason 失败原因
     */
    private void trackScanLoginFail(String reason) {
        Map<String, String> map = new HashMap<>();
        map.put("reason_fail", reason);
        map.put("scene", "授权登录失败");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_LOGIN_FAIL, map);
    }

}
