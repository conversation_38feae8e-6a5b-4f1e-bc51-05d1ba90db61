package com.sy37sdk.account.config;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;

import com.sqwan.common.util.LogUtil;
import com.taptap.sdk.BuildConfig;
import com.taptap.sdk.core.TapTapRegion;
import com.taptap.sdk.core.TapTapSdk;
import com.taptap.sdk.core.TapTapSdkOptions;
import com.taptap.sdk.review.TapTapReview;


import org.json.JSONException;
import org.json.JSONObject;

public class OpenReview {
    public static boolean TapTapInit = false;
    public static boolean toHaoYouKuaiBaoMarketDetails(Context context, String gameID){
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("hykb://openTopic?type=gamedetail&gameId=" + gameID));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean toTapTapMarketDetails(){
        try {
            TapTapReview.openReview();
        } catch (Exception e) {
            LogUtil.e("toTapTapMarketDetails异常：" + e);
            e.printStackTrace();
        }
        return false;
    }

    public static void initTapTap(Context context, String clientId, String clientToken){
        if(TapTapInit){
            return;
        }
        /* 必选配置 */
        // 开发者中心对应 Client ID
        // 开发者中心对应 Client Token
        // 是否开启 log，建议 Debug 开启，Release 关闭，默认关闭 log
        boolean enableLog = BuildConfig.DEBUG;

        TapTapSdkOptions tapSdkOptions = new TapTapSdkOptions(
                clientId, // 游戏 Client ID
                clientToken, // 游戏 Client Token
                TapTapRegion.CN // 游戏可玩区域: [TapTapRegion.CN]=国内 [TapTapRegion.GLOBAL]=海外
        );
        tapSdkOptions.setEnableLog(enableLog);
        // 初始化 TapSDK
        TapTapSdk.init(context, tapSdkOptions);
        TapTapInit = true;
    }

    public static void getTapTapInfo(Context context) {
        String commentInfo = ConfigManager.getInstance().getCommentInfo(context);
        String market = "";
        String clientId = "";
        String clientToken = "";
        try {
            JSONObject data = new JSONObject(commentInfo);
            String jump_url = data.optString("url");
            JSONObject obj = new JSONObject(jump_url);
            market = obj.optString("market");
            clientId = obj.optString("clientId");
            clientToken = obj.optString("clientToken");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if(!market.isEmpty() && market.equals("TapTap")){
            initTapTap(context,clientId,clientToken);
        }
    }



}
