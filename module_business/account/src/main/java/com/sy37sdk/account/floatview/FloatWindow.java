package com.sy37sdk.account.floatview;

import android.view.View;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-07 15:30
 */
public class FloatWindow {
    public static class ClickListenerAdapter implements ClickListener{

        @Override
        public void onClick(View view, int x, int y) {

        }

        @Override
        public void onShowComplete() {

        }
    }
    public interface ClickListener{
        void onClick(View view, int x, int y);
        void onShowComplete();
    }
    public interface DragBottom2DeleteCallback {
        void onDragBottom2Delete(boolean toDelete,boolean isShowBottomView,boolean highlight);
    }
    public interface OnDragCallBack {
        void onStayEdge();
        void onStartDrag();
    }
}
