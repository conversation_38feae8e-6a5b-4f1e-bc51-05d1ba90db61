package com.sy37sdk.account.floatview;

import android.content.Context;
import android.support.v4.widget.ListViewAutoScrollHelper;
import android.view.OrientationEventListener;
import android.view.Surface;
import android.view.WindowManager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-03
 */
public class ScreenOrientationHelper {

    public static final int ORIENTATION_TYPE_0 = 0;
    public static final int ORIENTATION_TYPE_90 = 90;
    public static final int ORIENTATION_TYPE_180 = 180;
    public static final int ORIENTATION_TYPE_270 = 270;

    private static OrientationEventListener mOrientationEventListener;


    public static int currentType = ORIENTATION_TYPE_0;

    public static List<ScreenOrientationChangeListener> listeners = new ArrayList<>();
    public static void initOrAdd(final Context context, ScreenOrientationChangeListener listener) {
        listeners.add(listener);
        if (mOrientationEventListener==null) {
            mOrientationEventListener = new OrientationEventListener(context) {
                @Override
                public void onOrientationChanged(int orientation) {
                    if (orientation > 340 || orientation < 20) {
                        //0
                        if(currentType == 0) {
                            return;
                        }
                        if(getScreenRotation(context) == Surface.ROTATION_0) {
                            for (ScreenOrientationChangeListener screenOrientationChangeListener : listeners) {
                                screenOrientationChangeListener.onChange(ORIENTATION_TYPE_0);
                            }
                            currentType = ORIENTATION_TYPE_0;
                        }
                    } else if (orientation > 70 && orientation < 110) {
                        //90
                        if(currentType == 90) {
                            return;
                        }
                        int angle = getScreenRotation(context);
                        if(angle == Surface.ROTATION_270) {
                            for (ScreenOrientationChangeListener screenOrientationChangeListener : listeners) {
                                screenOrientationChangeListener.onChange(ORIENTATION_TYPE_90);
                            }
                            currentType = ORIENTATION_TYPE_90;
                        }
                    } else if (orientation > 160 && orientation < 200) {
                        //180
                        if(currentType == 180) {
                            return;
                        }
                        int angle = getScreenRotation(context);
                        if(angle == Surface.ROTATION_180) {
                            for (ScreenOrientationChangeListener screenOrientationChangeListener : listeners) {
                                screenOrientationChangeListener.onChange(ORIENTATION_TYPE_180);
                            }
                            currentType = ORIENTATION_TYPE_180;
                        }
                    } else if (orientation > 250 && orientation < 290) {
                        //270
                        if(currentType == 270) {
                            return;
                        }
                        int angle = getScreenRotation(context);
                        if(angle == Surface.ROTATION_90) {
                            for (ScreenOrientationChangeListener screenOrientationChangeListener : listeners) {
                                screenOrientationChangeListener.onChange(ORIENTATION_TYPE_270);
                            }
                            currentType = ORIENTATION_TYPE_270;
                        }
                    }
                }
            };
            register();
        }

    }

    public static int getOrientationType(Context context) {
        if(currentType == ORIENTATION_TYPE_0) {
            int angle = getScreenRotation(context);
            switch(angle) {
                case Surface.ROTATION_0:
                    currentType = ORIENTATION_TYPE_0;
                    break;
                case Surface.ROTATION_90:
                    currentType = ORIENTATION_TYPE_270;
                    break;
                case Surface.ROTATION_180:
                    currentType = ORIENTATION_TYPE_180;
                    break;
                case Surface.ROTATION_270:
                    currentType = ORIENTATION_TYPE_90;
                    break;
                default:
                    break;
            }
        }
        return currentType;
    }

    private static int getScreenRotation(Context context) {
        WindowManager windowManager = ((WindowManager)context.getSystemService(Context.WINDOW_SERVICE));
        if(windowManager != null) {
            return windowManager.getDefaultDisplay().getRotation();
        }
        return 0;
    }

    public static void register() {
        if(mOrientationEventListener != null) {
            mOrientationEventListener.enable();
        }
    }

    public static void unRegister() {
        if(mOrientationEventListener != null) {
            mOrientationEventListener.disable();
        }
    }


    interface ScreenOrientationChangeListener {

        /**
         *
         * @param orientation
         */
        void onChange(int orientation);
    }

}
