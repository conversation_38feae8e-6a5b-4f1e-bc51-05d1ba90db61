package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.TouchDelegate;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackPage;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.ui.WechatRegSuccessDialog;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.constant.AccountViewBundleKey;
import com.sy37sdk.account.view.uifast.presenter.IWechatV2Presenter;
import com.sy37sdk.account.view.uifast.presenter.WechatLoginV2Presenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;
import java.util.Map;

public class WechatLoginV2View extends BaseSwitchView implements IWechatLoginV2View {
    private EditText etPhone;
    private IWechatV2Presenter presenter;
//
    private TextView tvAccountLogin, tvVerifyCode;
    private TextView tvClause, tvPolicy;
    private CheckBox cbClause;
    private View chatLoginView;

    public WechatLoginV2View(Context context, ILoginDialog loginDialog) {
        super(context);
        this.loginDialog = loginDialog;
        presenter = new WechatLoginV2Presenter(context, this);
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.wechat_login,SqTrackPage.SqTrackViewName.wechat_login);
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_wechat_v2";
    }

    @Override
    public void initView() {
        etPhone = getViewByName("et_phone");
        tvVerifyCode = getViewByName("tv_get_verify_code");
        tvAccountLogin = getViewByName("tv_account_login");
        tvClause = getViewByName("tv_clause");
        tvPolicy = getViewByName("tv_policy");
        cbClause = getViewByName("cb_clause");
        setTouchDelegate(cbClause);
        chatLoginView = getViewByName("ll_wechat_login");
    }

    private void setTouchDelegate(final View view) {
        final View parentView = (View) view.getParent();
        parentView.post(() -> {
            final Rect rect = new Rect();
            view.getHitRect(rect);
            rect.top -= 300;
            rect.bottom += 300;
            rect.left -= 300;
            parentView.setTouchDelegate(new TouchDelegate(rect, view));
        });
    }

    @Override
    public void initEvent() {
        tvAccountLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.accountLogin, SqTrackBtn.SqTrackBtnExt.ACCOUNT_LOGIN);
                loginDialog.onSwitch(AccountPage.ACCOUNT_LOGIN_PAGE, null);
            }
        });
        tvVerifyCode.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.obtainVerifyCode();
            }
        });
        tvClause.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toClausePage();
            }
        });
        tvPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toPolicy();
            }
        });
        cbClause.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                presenter.clauseClick(isChecked);
            }
        });
        chatLoginView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.wechatLogin();
            }
        });
    }

    @Override
    public String getTitle() {
        return "手机号登录";
    }

    @Override
    public void enableLoginBtn(boolean enable) {
//        chatLoginView.setEnabled(enable);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        loginDialog.loginSuccess(data);
    }

    @Override
    public void showRegDialog(Map<String, String> data, WechatRegSuccessDialog.IWechatRegListener regListener) {
        String username = AccountCache.getUsername(getContext());
        String password = AccountCache.getPassword(getContext());
        WechatRegSuccessDialog regSuccessDialog = new WechatRegSuccessDialog(getContext(), username, password);
        regSuccessDialog.setWechatRegListener(regListener);
        regSuccessDialog.show();
    }

    @Override
    public String getPhone() {
        return etPhone.getText().toString();
    }

    @Override
    public void startVerifyCodeView() {
        Bundle bundle = new Bundle();
        bundle.putString(AccountViewBundleKey.mobile, etPhone.getText().toString());
        loginDialog.onSwitch(AccountPage.ACCOUNT_VERIFY_CODE_PAGE, bundle);
    }

    @Override
    public void checkedClause() {
        cbClause.setChecked(true);
    }
}
