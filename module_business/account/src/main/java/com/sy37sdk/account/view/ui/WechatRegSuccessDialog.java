package com.sy37sdk.account.view.ui;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;

public class WechatRegSuccessDialog extends BaseDialog {

    private TextView tvAccount;
    private TextView tvPassword;
    private TextView tvEnterGame;
    private View close;

    private View rootView;

    private String uname, pwd;

    public WechatRegSuccessDialog(Context context, String uname, String pwd) {
        super(context);
        this.uname = uname;
        this.pwd = pwd;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getContext().setTheme(getIdByName("Mdialog", "style"));
        rootView = LayoutInflater.from(getContext()).inflate(getIdByName("sysq_wechat_reg_success", "layout"), null);
        setContentView(rootView);
        initView();
    }

    private void initView() {
        tvAccount = findViewById(getIdByName("tv_account", "id"));
        tvPassword = findViewById(getIdByName("tv_password", "id"));
        tvEnterGame = findViewById(getIdByName("tv_enter_game", "id"));
        close = findViewById(getIdByName("view_close", "id"));
        initAction();
        initData();
        setCanceledOnTouchOutside(false);
    }

    private void initAction() {
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (wechatRegListener != null) {
                    wechatRegListener.onSuccess();
                }
            }
        });
        tvEnterGame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

    }


    public void initData() {
        tvAccount.setText(uname);
        tvPassword.setText(pwd);

    }

    public interface IWechatRegListener {
        void onSuccess();
    }

    private IWechatRegListener wechatRegListener;

    public void setWechatRegListener(IWechatRegListener regListener) {
        this.wechatRegListener = regListener;
    }
}
