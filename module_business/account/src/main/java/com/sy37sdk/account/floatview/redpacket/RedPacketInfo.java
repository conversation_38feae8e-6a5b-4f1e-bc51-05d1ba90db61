package com.sy37sdk.account.floatview.redpacket;

/**
 * 描述:
 * 作者：znb
 * 时间：2020/11/13 14:17
 */
public class RedPacketInfo {
    public String imgLocalPath;
    public String imgUrl;
    public String jumpLink;
    public WebViewConfig webViewConfig;
    public static class WebViewConfig{
        public String pop_url;
        public int width;
        public int height;

        @Override
        public String toString() {
            return "WebViewConfig{" +
                    "pop_url='" + pop_url + '\'' +
                    ", width='" + width + '\'' +
                    ", height='" + height + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "RedPacketInfo{" +
                "imgLocalPath='" + imgLocalPath + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", jumpLink='" + jumpLink + '\'' +
                ", webViewConfig=" + webViewConfig +
                '}';
    }
}