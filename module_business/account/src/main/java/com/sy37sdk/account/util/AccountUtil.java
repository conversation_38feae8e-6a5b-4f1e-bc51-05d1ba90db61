package com.sy37sdk.account.util;

import android.content.Context;
import android.text.TextUtils;

import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountTools;
import com.sy37sdk.account.UserInfo;

import java.util.ArrayList;
import java.util.List;

public class AccountUtil {
    private static boolean isToFastLogin = false;
    private static boolean isCanFastBack = false;
    private static boolean isToWeiChatLogin = false;
    private static boolean notSupportFast = false;
    /**
     * 获取所有登录过的账户
     *
     * @param context
     * @return
     */
    public static List<UserInfo> getAllUserInfo(Context context) {
        return AccountTools.getAccountFromFile(context);
    }

    /**
     * 获取账户登录过的账户（排除调手机登录的）
     */
    public static List<UserInfo> getUserInfoFilterByAccount(Context context) {
        List<UserInfo> accounts = AccountTools.getAccountFromFile(context);
        List<UserInfo> accountUsers = new ArrayList<>();
        if (accounts != null && accounts.size() > 0) {
            for (UserInfo userInfo : accounts) {
                if (TextUtils.isEmpty(userInfo.getMobile())) {
                    accountUsers.add(userInfo);
                }
            }
        }
        return accountUsers;
    }

    /**
     * 获取上次登录的用户 / 历史的第一个账户
     *
     * @param context
     * @return
     */
    public static UserInfo getLastUserInfo(Context context) {
        //从sp缓存中读取
        UserInfo userInfo = AccountCache.getUserInfo(context);
        //sp中没有，则从sd卡文件中读取
        if (userInfo == null || TextUtils.isEmpty(userInfo.getUname())) {
            List<UserInfo> accountList = getAllUserInfo(context);
            if (accountList != null && accountList.size() > 0) {
                return accountList.get(accountList.size() - 1);
            }
        }
        return userInfo;
    }

    /**
     * 获取上次通过账户密码登录的用户(过滤掉手机号登录的)
     *
     * @param context
     * @return
     */
    public static UserInfo getLastAccountUserInfo(Context context) {
        //从sp缓存中读取
        UserInfo spUserInfo = AccountCache.getUserInfo(context);
        //sp缓存中获取的是账户登录的
        if (spUserInfo != null && !TextUtils.isEmpty(spUserInfo.getUname()) && TextUtils.isEmpty(spUserInfo.getMobile())) {
            return spUserInfo;
        }
        //sp缓存中没有则从缓存文件当中读取
        List<UserInfo> accountList = getUserInfoFilterByAccount(context);
        if (accountList != null && accountList.size() > 0) {
            for (int i = accountList.size() - 1; i >= 0; i--) {
                UserInfo cacheUserInfo = accountList.get(i);
                if (cacheUserInfo != null && !TextUtils.isEmpty(cacheUserInfo.getUname()) && TextUtils.isEmpty(cacheUserInfo.getMobile())) {
                    return cacheUserInfo;
                }
            }
        }
        return null;
    }

    /**
     * 根据uname查找文件缓存中的用户
     */
    public static UserInfo findUserByUname(Context context, String uname) {
        List<UserInfo> userInfos = AccountUtil.getAllUserInfo(context);
        if (!TextUtils.isEmpty(uname) && userInfos != null && userInfos.size() > 0) {
            for (UserInfo userInfo : userInfos) {
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getUname()) && (uname.toLowerCase().equals(userInfo.getUname().toLowerCase()))) {
                    return userInfo;
                }
            }
        }
        return null;
    }

    /**
     * 根据手机号查找文件缓存中的用户
     */
    public static UserInfo findUserByPhone(Context context, String phone) {
        List<UserInfo> userInfos = AccountUtil.getAllUserInfo(context);
        if (!TextUtils.isEmpty(phone) && userInfos != null && userInfos.size() > 0) {
            for (UserInfo userInfo : userInfos) {
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getLoginType()) && userInfo.getLoginType().equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE)) {
                    if (userInfo.getMobile().equals(phone)) {
                        return userInfo;
                    }
                }
            }
        }
        return null;
    }

    public static boolean checkToFastLogin(){
        return isToFastLogin;
    }

    public static void setToFastLogin(boolean state){
        isToFastLogin = state;
    }

    public static boolean checkCanFastBack(){
        return isCanFastBack;
    }

    public static void setCanFastBack(boolean state){
        isCanFastBack = state;
    }

    public static boolean checkNotSupportFast(){
        return notSupportFast;
    }

    public static void setNotSupportFast(boolean state){
        notSupportFast = state;
    }

    public static boolean checkToWeiChatLogin(){
        return isToWeiChatLogin;
    }

    public static void setToWeiChatLogin(boolean state){
        isToWeiChatLogin = state;
    }
}
