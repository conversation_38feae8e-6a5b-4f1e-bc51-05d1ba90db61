package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.hardware.SensorEvent;
import android.text.TextUtils;
import android.view.View;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tools.Logger;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.util.ViewUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.floatview.CommonFloatMenuLayout.UpdateRedDotCallback;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.redpacket.RedPacketInfo;
import com.sy37sdk.account.floatview.ui.MenuUIConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.json.JSONObject;

public class SqFloatViewManager implements FloatWindow.DragBottom2DeleteCallback, ShakeSensorHelper.SensorChangedCallback {
    private Task taskShowFloatView = Task.create();
    private Task taskSensorChanged = Task.create();
    private String TAG = "SqFloatViewManager";
    private static SqFloatViewManager sInstance;

    private boolean isShowFloatView;

    public SqBaseFloatView floatView;

    public CommonFloatConfig config;

    private CommonFloatMenuLayout layout;

    private SqFloatViewManager() {

    }

    public static SqFloatViewManager getInstance() {
        if (sInstance == null) {
            sInstance = new SqFloatViewManager();
        }
        return sInstance;
    }


    /**
     * 显示悬浮窗
     */
    public void showFloatView(final Activity activity) {
        Logger.info("activity的类名为: %s", activity.getClass().getName());
        ScreenOrientationHelper.initOrAdd(activity, new ScreenOrientationHelper.ScreenOrientationChangeListener() {
            @Override
            public void onChange(final int orientation) {
                if (floatView != null) {
                    taskShowFloatView.oneShot(500, new Task.TaskFunc() {
                        @Override
                        public Task.Result exec() {
                            LogUtil.i(TAG, "onChange orientation " + orientation);
                            FloatViewUtils.resetFloatViewPos(activity, floatView);
                            if (floatView != null) {
                                floatView.resetView(true);
                            }
                            return null;
                        }
                    });
                }

            }
        });

        if (isShowFloatView) return;
        if (config == null) return; // or show default floating view, check with product
        if (!config.isShow) return;

        isShowFloatView = true;

        HashMap<String, String> ballShowMap = new HashMap<>();
        ballShowMap.put(SqTrackKey.ball_id, config.id);
        ballShowMap.put(SqTrackKey.ball_name, config.name);
        ballShowMap.put(SqTrackKey.ball_url, config.iconUrl);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_show, ballShowMap);

        SqBaseFloatView.Builder builder = new SqBaseFloatView.Builder(activity);
        Logger.info("显示普通悬浮窗");
        if (layout == null) {
            layout = new CommonFloatMenuLayout(activity);
        }
        layout.setConfigs(config.menuConfigs);
        layout.setUserCenter(config.user_center);
        layout.setUIConfig(new MenuUIConfig(config.background_url, config.nickname_color, config.user_center_color,
            config.title_color));
        layout.setOnMenuItemClickListener(new CommonFloatMenuLayout.OnMenuItemClickListener() {
            @Override
            public void onMenuItemClick(MenuConfig menuConfig) {
                floatView.handleMenuConfigClick(menuConfig);
            }
        });
        layout.setUpdateRedCallback(new UpdateRedDotCallback() {
            @Override
            public void onUpdateRedDotRemind() {
                requestFloatRemind(config.menuConfigs);
            }
        });
        floatView = builder
                .setMenuLayout(layout)
                .setFloatIconUrl(config.iconUrl)
                //客户端写死不显示红点
                .setRedPacketInfo(null)
                .setOnDragBottom2DeleteCallback(this)
                .build();

        floatView.setClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (ConfigManager.getInstance(activity).isSqSDK()) {
                    FloatViewDataManager.getInstance().requestPtUserInfo(new SimpleSqHttpCallback<JSONObject>() {
                        @Override
                        public void onSuccess(JSONObject dataJson) {
                            layout.setPersonInfo();
                        }
                    });
                }


                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_access, new HashMap<String, String>() {
                    {
                        put(SqTrackKey.ball_id, config.id);
                        put(SqTrackKey.ball_name, config.name);
                        put(SqTrackKey.ball_url, config.iconUrl);
                        put(SqTrackKey.ball_red_dot, floatViewNeedRedDot() + "");
                    }
                });
            }
        });

        floatView.setMenuListener(new SqBaseFloatView.MenuListener() {
            @Override
            public void onMenuShow() {
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_entry_show, new HashMap<String, String>() {
                    {
                        put(SqTrackKey.ball_id, config.id);
                    }
                });
                //二级菜单曝光埋点
                List<MenuConfig> menuConfigs = config.menuConfigs;
                if (menuConfigs != null && menuConfigs.size() > 0) {
                    for (MenuConfig menuConfig : menuConfigs) {
                        HashMap<String, String> ballBtnShowMap = new HashMap<>();
                        ballBtnShowMap.put(SqTrackKey.ball_id, menuConfig.id);
                        ballBtnShowMap.put(SqTrackKey.ball_name, menuConfig.title);
                        ballBtnShowMap.put(SqTrackKey.ball_url, menuConfig.openUrl);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_btn_show, ballBtnShowMap);

                    }
                }
            }
        });

        floatView.setDismissListener(new SqBaseFloatView.DismissListener() {
            @Override
            public void onDismiss() {
                //悬浮球消失
            }
        });

        floatView.show(false);
        onPauseShakeSensorHelper();
        if (config.needRedDot()) {
            requestFloatWindowRedPoint(config.menuConfigs, activity);
        }
    }

    public void showFloatMenu() {
        if (floatView != null) {
            floatView.expandMenu();
        }
    }

    /**
     * 隐藏悬浮窗
     */
    public void dismissFloatView() {
        if (isShowFloatView) {
            if (floatView != null) {
                floatView.dismiss();
            }
            isShowFloatView = false;
        }
        taskSensorChanged.stop();
        taskShowFloatView.stop();
        onPauseShakeSensorHelper();
    }

    public void requestFloatWindowConfig(final Context context) {
        dismissFloatView();
        AccountRequestManager requestManager = new AccountRequestManager(context);
        requestManager.floatWindow(new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject dataJson) {
                // 解析返回数据
                config = new CommonFloatConfig(dataJson);
                if (config.isShow) {
                    showFloatView((Activity) context);
                }
            }
        });
    }

    public void bindRedDot() {
        //建立红点长连接
        FloatViewManager.getInstance().bindFloatSocket(new FloatViewManager.OnRecInfListener() {
            @Override
            public void onMsgInf(MenuConfig menuConfig) {
                showFloatItemRedDot();
                if (floatView != null && floatView.getContext() instanceof Activity) {
                    ((Activity)floatView.getContext()).runOnUiThread(() -> handleFloatViewMsgRemind(menuConfig));
                }
            }
        });
    }

    /**
     * 处理悬浮球消息提醒
     */
    private void handleFloatViewMsgRemind(MenuConfig menuConfig) {
        if (floatView != null) {
            MenuConfig showingConfig = floatView.getShowingConfig();
            if (showingConfig != null) {
                if (menuConfig.priority > showingConfig.priority) { // 消息提示大于展示的提示，展示消息的提示
                    showFloatViewRemind(menuConfig);
                }
            } else { // 当前没有展示提醒，直接提示
                showFloatViewRemind(menuConfig);
            }
        }
    }


    /**
     * 获取悬浮球红点
     *
     * @param context
     */
    public void requestFloatWindowRedPoint(List<MenuConfig> menuConfigs, final Context context) {

        //礼包红点传："礼包" ； 活动红点："活动"  ;  代金卷红点："代金卷"；需要多种类型红点数，使用","拼接(英文逗号)，eg：礼包&活动红点数"活动,礼包,代金卷"
        StringBuilder redTitleSb = new StringBuilder();
        //活动相关，必传
        String pageUuid = "";
        if (menuConfigs != null && menuConfigs.size() > 0) {
            for (MenuConfig menuConfig : menuConfigs) {
                if (menuConfig.needRedDot()) {
                    redTitleSb.append(menuConfig.title).append(",");
                    if (TextUtils.isEmpty(pageUuid)) {
                        pageUuid = UrlUtils.readValueFromUrlStrByParamName(menuConfig.openUrl, "page_uuid");
                    }
                }

            }
        }
        if (redTitleSb.length() <= 0) {
            Logger.info("不需要显示红点");
            return;
        }
        AccountRequestManager requestManager = new AccountRequestManager(context);
        requestManager.floatRedPoint(pageUuid, redTitleSb.substring(0, redTitleSb.length() - 1), new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject dataJson) {
                try {
                    String res_data = dataJson.optString("res_data");
                    ArrayList<RedDot> redDots = RedDot.getRedDots(res_data);
                    FloatViewDataManager.getInstance().saveRedDot(redDots);
                    showFloatItemRedDot();
                    requestFloatRemind(menuConfigs);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 显示悬浮球二级菜单的红点
     */
    public void showFloatItemRedDot() {
        if (layout != null) {
            layout.refreshRedDot(FloatViewDataManager.getInstance().getRedDotCache());
        }
    }

    /**
     * 获取悬浮球提醒方式
     */
    private void requestFloatRemind(List<MenuConfig> menuConfigs) {
        StringBuilder redTitleSb = new StringBuilder();
        String pageUuid = "";
        if (menuConfigs != null && menuConfigs.size() > 0) {
            for (MenuConfig menuConfig : menuConfigs) {
                if (menuConfig.needRedDot()) {
                    redTitleSb.append(menuConfig.title).append(",");
                    if (TextUtils.isEmpty(pageUuid)) {
                        pageUuid = UrlUtils.readValueFromUrlStrByParamName(menuConfig.openUrl, "page_uuid");
                    }
                }
            }
        }
        if (redTitleSb.length() <= 0) {
            Logger.info("不需要显示红点");
            return;
        }
        if (floatView != null) {
            if (floatViewNeedRedDot()) { //有红点则代表需要提醒
                AccountRequestManager requestManager = new AccountRequestManager(floatView.getContext());
                requestManager.getFloatWarning(pageUuid, redTitleSb.substring(0, redTitleSb.length() - 1), new SimpleSqHttpCallback<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        MenuConfig menuConfig = new MenuConfig(jsonObject);
                        showFloatViewRemind(menuConfig);
                    }
                });
            }
        }
    }


    /**
     * 显示悬浮球icon红点
     */
    private void showFloatViewRemind(MenuConfig menuConfig) {
        MenuConfig showingMenuConfig = floatView.getShowingConfig();
        if (showingMenuConfig != null) {
            if (TextUtils.equals(showingMenuConfig.warningMsg, menuConfig.warningMsg) && TextUtils.equals(
                menuConfig.warningType, showingMenuConfig.warningType)
                && menuConfig.priority == showingMenuConfig.priority) {
                return; // 相同的消息，不做处理
            }
        }
        floatView.setShowingConfig(menuConfig);
        if (menuConfig != null) {
            if (!TextUtils.isEmpty(menuConfig.warningType)) {
                if (!menuConfig.warningType.contains(MenuConfig.WARNING_TYPE_RED_POINT)) {
                    floatView.showRedDot(false); //气泡类型不要红点
                    floatView.showRemindAnim(menuConfig);
                } else {
                    floatView.removeBubble(); //红点类型不要气泡
                    floatView.refreshRedDot();
                    floatView.showRedDot(true);
                }
                floatView.checkShake(menuConfig);
            } else { //移除所有当前提醒
                floatView.showRedDot(false);
                floatView.removeBubble();
            }
        }
    }

    /**
     * 悬浮球是否有红点
     *
     * @return
     */
    private boolean floatViewNeedRedDot() {
        boolean needRedDot = false;
        List<RedDot> redDots = FloatViewDataManager.getInstance().getRedDotCache();
        for (RedDot redDot : redDots) {
            if (redDot.getNum() > 0) {
                needRedDot = true;
                break;
            }
        }
        return needRedDot;
    }

    /**
     * 悬浮球红点已读
     */
    public void redDotCalled(String pageUuid, String redDotTitle, Context context) {
        AccountRequestManager requestManager = new AccountRequestManager(context);
        requestManager.floatRedCalled(pageUuid, redDotTitle, null);
    }

    /**
     * 悬浮窗是否符合显示条件
     *
     * @return true -> 符合显示条件, false -> 不符合显示条件
     */
    public boolean isShowFloat() {
        return config != null && config.isShow;
    }


    /**
     * 显示红包gif悬浮球
     *
     * @param activity
     * @param redPacketInfo
     */
    public void showRedPacketFloat(Activity activity, RedPacketInfo redPacketInfo, boolean hasShowRedPacketPop) {
        LogUtil.d(TAG, "showRedPacketFloat:" + redPacketInfo);
        dismissFloatView();
        if (!isShowFloatView) {
            isShowFloatView = true;
            SqBaseFloatView.Builder builder = new SqBaseFloatView.Builder(activity);
            floatView = builder.setRedPacketInfo(redPacketInfo)
                    .setOnDragBottom2DeleteCallback(this)
                    .build();

            floatView.show(!hasShowRedPacketPop);
            onPauseShakeSensorHelper();
        }
    }

    public void showRedPacktFloatView() {
        if (floatView != null) {
            ViewUtils.show(floatView);
        }
    }

    public void onResume() {
        ScreenOrientationHelper.register();
        onResumeShakeSensorHelper();
    }

    public void onPause() {
        ScreenOrientationHelper.unRegister();
        onPauseShakeSensorHelper();
    }

    private void onPauseShakeSensorHelper() {
        if (floatView != null) {
            Context context = floatView.getContext();
            ShakeSensorHelper.getInstance(context).onPause();
        }
    }

    private void onResumeShakeSensorHelper() {
        if (floatView != null) {
            int windowVisibility = floatView.getWindowVisibility();
            if (windowVisibility == View.GONE) {
                Context context = floatView.getContext();
                ShakeSensorHelper.getInstance(context).onResume();
                ShakeSensorHelper.getInstance(context).addSensorChangedCallback(this);
            }
        }
    }

    @Override
    public void onDragBottom2Delete(boolean toDelete, boolean isShowBottomView, boolean higilight) {
        LogUtil.d("onDragBottom2Delete toDelete=" + toDelete + " isShowBottomView" + isShowBottomView + " higilight" + higilight);
        if (toDelete) {
            if (floatView != null && floatView.mBottomDeleteView != null) {
                floatView.setWindowVisibility(View.GONE);
                floatView.dismissMenu();
                ViewUtils.gone(floatView.mBottomDeleteView);
                onResumeShakeSensorHelper();
                Context context = floatView.getContext();
//                FloatViewUtils.resetFloatViewPos(context);
//                FloatViewUtils.setShankeTips(context,true);
                if (FloatViewUtils.isShakeTips(context)) {
                    new Shake2FloatTipsDialog(context).show();
                }
                HashMap<String, String> ballMap = new HashMap<>();
                ballMap.put("ball_id", config.id);
                ballMap.put("ball_name", config.name);
                ballMap.put("ball_url", config.iconUrl);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.FLOAT_BALL_HIDE, ballMap);
            }
        } else {
            if (floatView != null) {
                if (floatView.mBottomDeleteView != null) {
                    if (isShowBottomView) {
                        floatView.mBottomDeleteView.showBottomDeleteView();
                    } else {
                        floatView.mBottomDeleteView.hideBottomDeleteView();
                    }
                    floatView.mBottomDeleteView.setStatus(higilight);

                }
            }

        }
    }

    @Override
    public void onSensorChanged(final Context context, SensorEvent event) {
        LogUtil.d("onSensorChanged");
        if (floatView != null && floatView.mBottomDeleteView != null) {
            floatView.setWindowVisibility(View.VISIBLE);
            floatView.resetView(false);

            HashMap<String, String> ballShowMap = new HashMap<>();
            ballShowMap.put(SqTrackKey.ball_id, config.id);
            ballShowMap.put(SqTrackKey.ball_name, config.name);
            ballShowMap.put(SqTrackKey.ball_url, config.iconUrl);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_show, ballShowMap);
        }
        onPauseShakeSensorHelper();
    }

}
