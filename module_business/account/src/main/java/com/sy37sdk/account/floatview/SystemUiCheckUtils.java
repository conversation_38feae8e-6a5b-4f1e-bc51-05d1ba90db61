package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.sqwan.common.util.LogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/4/10 14:20
 */
class SystemUiCheckUtils {
    private List<onSystemUiChangedCallback> listener = new ArrayList<>();
    interface onSystemUiChangedCallback{
        void onSystemUiChanged(FloatViewUtils.FloatViewConfig config);
    }
    public void addListener(onSystemUiChangedCallback onSystemUiChangedCallback){
        listener.add(onSystemUiChangedCallback);

    }
    public void removeListener(onSystemUiChangedCallback onSystemUiChangedCallback){
        listener.remove(onSystemUiChangedCallback);

    }    String TAG = this.getClass().getSimpleName();
    private static final SystemUiCheckUtils ourInstance = new SystemUiCheckUtils();

    static SystemUiCheckUtils getInstance() {
        return ourInstance;
    }

    private SystemUiCheckUtils() {
    }
    boolean hasChanged = false;
    private boolean isUiHideNavigation(View decorView){
        return (decorView.getSystemUiVisibility()& View.SYSTEM_UI_FLAG_HIDE_NAVIGATION)==View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
    }
    public void init(final Context mContext){
        if (mContext instanceof Activity) {
            Activity activity = ((Activity)mContext);

            final View decorView  = activity.getWindow().getDecorView();
            final boolean isUiHideNavigation = isUiHideNavigation(decorView);
            LogUtil.i(TAG,"isUiHideNavigation:"+isUiHideNavigation);
            if (!isUiHideNavigation) {
                decorView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                    @Override
                    public void onSystemUiVisibilityChange(int visibility) {
                        LogUtil.i(TAG,"onSystemUiVisibilityChange visibility:"+visibility);
                        if (!isUiHideNavigation) {
                            if (hasChanged) {
                                return;
                            }
                            boolean _isUiHideNavigation = isUiHideNavigation(decorView);
                            if (_isUiHideNavigation) {
                                FloatViewUtils.FloatViewConfig config = new FloatViewUtils.FloatViewConfig(mContext);
                                onSystemUiChanged(config);
                                hasChanged=true;
                            }
                        }
                    }
                });
            }
        }
    }

    private void onSystemUiChanged(FloatViewUtils.FloatViewConfig config) {
        for (onSystemUiChangedCallback _onSystemUiChangedCallback : listener) {
            LogUtil.i(TAG,"onSystemUiChanged _onSystemUiChangedCallback:"+_onSystemUiChangedCallback);
            _onSystemUiChangedCallback.onSystemUiChanged(config);
        }
    }
}
