package com.sy37sdk.account.presenter.fast;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.mvp.IView;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.view.base.view.IAccountPresenter;
import com.sy37sdk.account.view.base.view.IPageSwitchView;
import com.sy37sdk.account.view.uifast.switcher.IPageSwitcher;

import java.net.URLEncoder;
import java.util.HashMap;
import java.net.URLEncoder;
import java.util.Map;


public abstract class BaseAccountPagerPresenter<K extends IView> extends IAccountPresenter<K> {

    private IPageSwitcher mAccountPageSwitcher;

    protected ILoginListener mLoginListener;

    public BaseAccountPagerPresenter(Context context, K view) {
        super(context, view);
    }

    public void setAccountPageSwitcher(IPageSwitcher accountPageSwitcher) {
        mAccountPageSwitcher = accountPageSwitcher;
    }

    public void setLoginListener(ILoginListener loginListener) {
        mLoginListener = loginListener;
    }

    /**
     * 切换账号弹窗
     *
     * @param page
     */
    public void onSwitch(int page) {
        if (mAccountPageSwitcher != null) {
            mAccountPageSwitcher.onSwitch(page);
        }
    }

    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        ((IPageSwitchView) mView).onSwitched(fromIndex, toIndex, bundle);
    }

    public abstract View getView();

    public void onBackPressed() {
        ((IPageSwitchView) mView).onBackPressed();
    }

    /**
     * 快速注册
     */


    /**
     * 手机号+验证码登录
     */
    public void loginMobileWithCode(String mobile, String coe) {

    }

    /**
     * 手机号+密码登录
     */
    public void loginMobileWithPwd(String mobile, String pwd) {

    }

    /**
     * 账号+密码登录
     */
    public void loginAccount(String accountName, String pwd, final AccountLogic.AccountListener accountListener) {
        AccountLogic.getInstance(context).accountLogin(accountName, pwd, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                if(accountListener!=null){
                    accountListener.onSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                ToastUtil.showToast(context, msg);
                if(accountListener!=null){
                    accountListener.onFailure(code,msg);
                }
            }
        });
    }

    public void forgotPassword() {
        String language = AppUtils.getLocaleLanguage().toLowerCase();
        String gameName = AppUtils.getAppName(context);
        String userName = AccountCache.getUsername(context);
        StringBuilder params = new StringBuilder();
        if (UrlConstant.FORGET_PWD.contains("?")) {
            params.append("&");
        } else {
            params.append("?");
        }
        if (!"".equals(language)) {
            params.append("locale=" + language);
        }
        if (!"".equals(gameName)) {
            params.append("&gn=" + URLEncoder.encode(gameName));
        }
        if (!"".equals(userName)) {
            params.append("&uname=" + userName);
        }
        LogUtil.i(params.toString());
        AppUtils.toSQWebUrl(context, UrlConstant.FORGET_PWD + params.toString(), "忘记密码");
    }

    /**
     * 快速登录
     */

    /**
     * 获取验证码
     */

}
