package com.sy37sdk.account.controller;

import android.app.Dialog;
import android.content.Context;

import com.sqwan.common.mod.account.ILoginListener;
import com.sy37sdk.account.QrCodeInfo;
import com.sy37sdk.account.alifast.AccountLoginManager;
import com.sy37sdk.account.view.IRegSuccessDialog;
import com.sy37sdk.account.view.ui360.RegSuccessDialog360;

/**
 * 闪验登录
 */
public class FastVerifyController extends AbstractLoginController {
    public FastVerifyController(Context context) {
        super(context);
    }

    @Override
    public void showLoginDialog(ILoginListener listener) {
        LOG.d("展示登录弹窗");
        AccountLoginManager.getInstance(mContext).login(listener);
    }


    @Override
    public void showRegSuccessDialog(String uname, String pwd, QrCodeInfo qrCodeInfo, IRegSuccessDialog.EnterGameListener enterGameListener) {
        IRegSuccessDialog regSuccessDialog = new RegSuccessDialog360(mContext, uname, pwd);
        regSuccessDialog.setEnterGameListener(enterGameListener);
        ((Dialog) regSuccessDialog).show();
    }


}
