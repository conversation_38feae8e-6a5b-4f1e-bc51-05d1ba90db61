package com.sy37sdk.account.policy.view;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.sqwan.common.util.PermissionHelper;

import static android.Manifest.permission.READ_PHONE_STATE;
import static android.Manifest.permission.WRITE_EXTERNAL_STORAGE;

public class PermissionDialog extends AuthBaseDialog {
    private String simdes = "设备信息：读取设备唯一标识用于保护账号安全";
    private String sdcarddes = "存储权限：实现账号、图片的缓存和使用，图片保存与分享";
    private String permissionDes = simdes + "\n" + sdcarddes;
    private TextView tvProtol;
    private OnBtnClickListener mListener;

    public PermissionDialog(Context context) {
        super(context);
    }

    @Override
    protected String getContainerLayout() {
        return "sy37_layout_auth_permission";
    }

    @Override
    protected void doEngine() {
        tvProtol = (TextView) findViewById(findId("tvProtol"));
        tvCancel.setText("退出游戏");
        tvOk.setText("确认");
        tvCancel.setSelected(false);
    }

    @Override
    protected String getTitle() {
        return "权限申请";
    }


    @Override
    protected void onClickOk() {
        if (mListener != null) {
            mListener.onClick();
        }
        dismiss();
    }

    @Override
    protected void onClickCancel() {
        dismiss();
        exit();
    }

    @Override
    public void show() {
        super.show();
        setDescText();
    }

    public void setDescText() {
        String _desc = "";
        if (mContext instanceof Activity) {
            if (!PermissionHelper.getInstance().checkPermission(READ_PHONE_STATE) &&
                    !PermissionHelper.getInstance().checkPermission(WRITE_EXTERNAL_STORAGE)) {
                _desc = permissionDes;
            } else {
                if (!PermissionHelper.getInstance().checkPermission(READ_PHONE_STATE)) {
                    _desc = simdes;
                } else if (!PermissionHelper.getInstance().checkPermission(WRITE_EXTERNAL_STORAGE)) {
                    _desc = sdcarddes;
                }
            }
        }
        tvProtol.setText(_desc);
    }


    public void setBtnClickListener(OnBtnClickListener listener) {
        mListener = listener;
    }

    public interface OnBtnClickListener {
        void onClick();
    }
}
