package com.sy37sdk.account.auth;

import com.sqwan.TestConst;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.config.IConfigMod;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/12/12
 */
public class AuthBean {

    /**
     * 上报时长间隔 默认值
     */
    public static int DEFAULT_INTERVAL = 1;

    /**
     * 弹窗开关（0不弹窗 1非强制弹窗 2强制弹窗）
     */
    private int code;

    /**
     * 自营使用认证地址
     */
    private String url;

    /**
     * CP使用认证地址
     */
    private String durl;

    /**
     * 是否需要防沉迷 （1是， 0否）
     */
    private int needAddiction;

    /**
     * 是否需要一小时实名（1是 ，0否）
     */
    private int needAccumulateDuration;

    /**
     * 是否已实名（1是 ，0否）
     */
    private int isAuth;

    /**
     * 实名及防沉迷时长接口上报间隔，分钟，大于等于1的整数
     */
    private int interval = DEFAULT_INTERVAL;

    /**
     * 是否继续调用起支付界面 1是，0否
     * 默认值 1
     */
    private int allowRecharge = 1;

    /**
     * 游客模式倒计时，秒
     */
    private int remainingTime = 0;

    private int needReportOnline = 0;

    //未成年登录时段限制弹窗
    private String timerangeLimtUrl;

    //当前时间戳（秒）
    private long timestamp;



    //当前是否可玩日期（1是 ，0否）
    private boolean isPlayableDate;

    //可玩的时间段（20:00-21:00）
    private String playableTimerange;

    //用户年龄（默认值：-1）
    private int age;

    //版署实名状态（未认证:0，认证成功:1，认证中:-1，认证失败:-2）
    private int banshuAuthState;

    public int getNeedReportOnline() {
        return needReportOnline;
    }

    public void setNeedReportOnline(int needReportOnline) {
        this.needReportOnline = needReportOnline;
    }

    public int getRemainingTime() {
        if (TestConst.isRemainingTime) {
            return TestConst.testRemainingTime;
        } else {
            return remainingTime;
        }
    }

    /**
     * 18禁
     * 后端是否触发年龄限制（1是 ，0否）
     */
    public boolean isAgeLimited;

    /**
     * 18禁
     * 年龄限制提醒消息
     */
    public String ageLimitedMsg = "";




    public void setRemainingTime(int remainingTime) {
        this.remainingTime = remainingTime;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDurl() {
        return durl;
    }

    public void setDurl(String durl) {
        this.durl = durl;
    }

    public boolean getNeedAddiction() {
        return needAddiction == 1;
    }

    public void setNeedAddiction(int needAddiction) {
        this.needAddiction = needAddiction;
    }

    public boolean getNeedAccumulateDuration() {
        return needAccumulateDuration == 1;
    }

    public void setNeedAccumulateDuration(int needAccumulateDuration) {
        this.needAccumulateDuration = needAccumulateDuration;
    }

    public boolean getIsAuth() {
        return isAuth == 1;
    }

    public void setIsAuth(int isAuth) {
        this.isAuth = isAuth;
    }

    public boolean needAuth() {
        return code == 1 || code == 2;
    }

    public boolean isFocus() {
        return code == 2;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public boolean getAllowRecharge() {
        return allowRecharge == 1;
    }

    public void setAllowRecharge(int allowRecharge) {
        this.allowRecharge = allowRecharge;
    }

    public String getTimerangeLimtUrl() {
        return timerangeLimtUrl;
    }

    public void setTimerangeLimtUrl(String timerangeLimtUrl) {
        this.timerangeLimtUrl = timerangeLimtUrl;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
        IConfigMod iConfigMod = ModHelper.getConfig();
        if (iConfigMod!=null) {
            iConfigMod.getCommonConfig().setTimestamp(timestamp);
        }
    }

    public boolean isPlayableDate() {
        return isPlayableDate;
    }

    public void setPlayableDate(boolean playableDate) {
        isPlayableDate = playableDate;
    }

    public String getPlayableTimerange() {
        return playableTimerange;
    }

    public void setPlayableTimerange(String playableTimerange) {
        this.playableTimerange = playableTimerange;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getBanshuAuthState() {
        return banshuAuthState;
    }

    public void setBanshuAuthState(int banshuAuthState) {
        this.banshuAuthState = banshuAuthState;
    }

    public static AuthBean parseFromJson(JSONObject authJson) {
        int dRemainingTime = 2 * 60;
        AuthBean authBean = new AuthBean();
        try {
            authBean.setCode(authJson.optInt("code"));
            authBean.setUrl(authJson.optString("url"));
            authBean.setDurl(authJson.optString("durl"));
            authBean.setNeedAddiction(authJson.optInt("needAddiction"));
            authBean.setNeedAccumulateDuration(authJson.optInt("needAccumulateDuration"));
            authBean.setIsAuth(authJson.optInt("isAuth"));
            authBean.setInterval(authJson.optInt("interval"));
            authBean.setAllowRecharge(authJson.optInt("allowRecharge"));
            int remainingTime = authJson.optInt("remainingTime");
            if (remainingTime > 0) {
                remainingTime += dRemainingTime;
            }
            authBean.setRemainingTime(remainingTime);
            authBean.setNeedReportOnline(authJson.optInt("needReportOnline"));
            authBean.isAgeLimited = authJson.optInt("isAgeLimited") == 1;
            authBean.ageLimitedMsg = authJson.optString("ageLimitedMsg");
            authBean.setTimerangeLimtUrl(authJson.optString("timerangeLimtUrl"));
            authBean.setTimestamp(authJson.optLong("timestamp"));
            authBean.setPlayableDate(authJson.optInt("isPlayableDate") == 1);
            authBean.setPlayableTimerange(authJson.optString("playableTimerange"));
            authBean.setAge(authJson.optInt("age", -1));
            authBean.setBanshuAuthState(authJson.optInt("banshuAuthState", -1));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return authBean;
    }

    public boolean isNeddReportOnline() {
        return getNeedReportOnline() == 1;
    }

}
