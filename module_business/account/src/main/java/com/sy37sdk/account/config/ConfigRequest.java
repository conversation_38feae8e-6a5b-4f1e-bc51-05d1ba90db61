package com.sy37sdk.account.config;

import android.content.Context;

import com.sq.webview.net.IRequest;
import com.sqwan.common.webview.WebRequestProxy;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.UrlConstant;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ConfigRequest {
    public static void getConfigInfo(Context context, IRequest.RequestCallback<JSONObject> callback) {
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        // 历史原因，因为接口返回没有state，用了webview封装代理，后续可以优化
        IRequest mRequestProxy = new WebRequestProxy();
        Map<String, String> params = new HashMap<>();
        params.put("gid", config.getGameid());
        params.put("pid", config.getPartner());
        mRequestProxy.getRequest(UrlConstant.URL_GET_CONFIG_INFO, params, callback);
    }
}
