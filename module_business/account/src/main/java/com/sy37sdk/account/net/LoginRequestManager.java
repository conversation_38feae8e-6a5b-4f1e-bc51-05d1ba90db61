package com.sy37sdk.account.net;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tool.sqtools.detector.DevicesFingerprint;
import com.sqwan.base.L;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.account.UrlConstant;

public class LoginRequestManager {

    public static void phoneLoginPwd(final String phone, final String pwd, final SqHttpCallback<String> callback) {
        LogUtil.i("手机+密码登录");
        SqRequest.of(UrlConstant.URL_LOGIN_PHONE_PWD)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("pwd", pwd)
            .addParam("mobile", phone)
            .addParam("display_name", AppUtils.getAppName(SQContextWrapper.getActivity()))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, String.class);
    }


    public static void sendPhoneCode(String mobile, SqHttpCallback<Void> callback) {
        LogUtil.i("获取手机验证码");
        SqRequest.of(UrlConstant.URL_LOGIN_SEND_CODE)
            .signV3()
            .addParam("mobile", mobile)
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, Void.class);
    }

    public static void phoneLoginCheckCode(final String phone, final String code,
        final SqHttpCallback<String> callback) {
        LogUtil.i("确认验证码");
        SqRequest.of(UrlConstant.URL_LOGIN_CHECK_CODE)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("mobile", phone)
            .addParam("scode", code)
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, String.class);
    }

    public static void phoneLoginTicket(final String ticket, final SqHttpCallback<String> callback) {
        LogUtil.i("Ticket登录");
        SqRequest.of(UrlConstant.URL_LOGIN_TICKET)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("ticket", ticket)
            .addParam("display_name", AppUtils.getAppName(SQContextWrapper.getActivity()))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, String.class);
    }


    public static void wechatLogin(String authCode, SqHttpCallback<String> callback) {
        LogUtil.i("微信登录");
        SqRequest.of(UrlConstant.WECHAT_LOGIN)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("auth_code", authCode)
            .addParam("display_name", AppUtils.getAppName(SQContextWrapper.getActivity()))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, String.class);
    }
}
