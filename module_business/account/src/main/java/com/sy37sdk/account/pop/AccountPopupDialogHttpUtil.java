package com.sy37sdk.account.pop;

import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SpUtils;
import com.sy37sdk.account.UrlConstant;

public class AccountPopupDialogHttpUtil {

    private static final String SQ_PREFS = "sq_prefs";

    public static final String URL_M_POP_LOGIN = "pop_ups_login_api"; // m层请求登录弹窗

    /**
     * 请求登录弹窗
     */
    public static void requestLoginPopup(final String token, final String actionType,
        final SqHttpCallback<String> callback) {
        String url = SpUtils.get(SQContextWrapper.getActivity(), SQ_PREFS).getString(URL_M_POP_LOGIN);
        if (TextUtils.isEmpty(url)) {
            url = UrlConstant.URL_M_LOGIN_POPUP;
        }
        SqRequest.of(url)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .addParam("token", token)
            .addParam("action_type", actionType)
            .post(callback, String.class);
    }
}
