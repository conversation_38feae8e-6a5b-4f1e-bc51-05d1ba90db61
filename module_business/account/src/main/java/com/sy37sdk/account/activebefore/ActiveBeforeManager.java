package com.sy37sdk.account.activebefore;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.uagree.UAgreeManager;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONObject;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-19 12:00
 */
public class ActiveBeforeManager extends BaseEnginHandler {

    private static final ActiveBeforeManager ourInstance = new ActiveBeforeManager();

    @Nullable
    private Handler loopHandler;

    public static ActiveBeforeManager getInstance() {
        return ourInstance;
    }

    private ActiveBeforeManager() {
    }
    private int messageWhat = 0;
    private Handler handler;
    private int messageCount = 0;
    private List<ActiveBeforeBaseInfo> baseInfos = new ArrayList<>();
    private ActiveBeforeRequestManager activeBeforeRequestManager;
    private long sTime;
    public PermissionInfo permissionInfo=new PermissionInfo();
    public UserProtocolInfo userProtocolInfo=new UserProtocolInfo();
    @Override
    public void init(Context _context) {
        super.init(_context);
        if (context!=null) {
            activeBeforeRequestManager = new ActiveBeforeRequestManager();
            handler = new Handler(context.getMainLooper()){
                @Override
                public void handleMessage(Message msg) {
                    messageCount+=1;
                    if (messageCount==baseInfos.size()) {
                        invoke();
                    }
                }
            };
        }
        messageCount = 0;
        handler.removeMessages(messageWhat);
        baseInfos.clear();
        baseInfos.add(permissionInfo);
        baseInfos.add(userProtocolInfo);
    }
    public interface UpdateDataCallback{
        void invoke();
    }
    private UpdateDataCallback updateDataCallback;
    private void invoke(){
        if (updateDataCallback!=null) {
            long dTime = System.currentTimeMillis() - sTime;
            LogUtil.i(TAG,"dTime:"+dTime);
            for (ActiveBeforeBaseInfo baseInfo : baseInfos) {
                LogUtil.i(TAG,"baseInfo:"+baseInfo);
            }
            updateDataCallback.invoke();
        }
    }
    public void updateRsp(){
        if (handler!=null) {
            handler.sendEmptyMessage(messageWhat);
        }
    }

    private abstract class Callback extends SqHttpCallback<JSONObject> {

        abstract void onData(String jsonData);

        @Override
        public void onSuccess(JSONObject jsonObject) {
            onData(jsonObject.toString());
            updateRsp();
        }

        @Override
        public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
            updateRsp();
        }

        @Override
        public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
            @Nullable String data) {
            updateRsp();
        }
    }

    private void updateUserprotocolInfo(){
        if (activeBeforeRequestManager!=null) {
            activeBeforeRequestManager.reqUserProtocol(new Callback() {

                @Override
                void onData(String jsonData) {
                    Context context = checkValid();
                    if (context != null) {
                        UAgreeManager.getInstance().initConfig(jsonData);
                    }

                    userProtocolInfo.parse(jsonData);
                }
            });
        }
    }
    private void updatePermissionInfo(){
        if (activeBeforeRequestManager!=null) {
            activeBeforeRequestManager.reqGetpermission(new Callback() {

                @Override
                void onData(String jsonData) {
                    permissionInfo.parse(jsonData);
                }
            });
        }

    }
    public void updateData(final UpdateDataCallback updateDataCallback){
        sTime = System.currentTimeMillis();
        this.updateDataCallback = updateDataCallback;
        updatePermissionInfo();
        updateUserprotocolInfo();
    }

    public void requestGameUrlList() {
        if (activeBeforeRequestManager == null) {
            return;
        }

        // 需求文档地址：https://doc.weixin.qq.com/doc/w3_AQIASwZ-AKMPdLCCEaURiex39hkHB?scode=AIwAKQfDAAw5ft0010AQIASwZ-AKM
        activeBeforeRequestManager.getGameUrlList(new SimpleSqHttpCallback<JSONObject>() {

            @Override
            public void onSuccess(JSONObject jsonObject) {
                int maxReqNum = jsonObject.optInt("max_req_num");
                if (maxReqNum == 0) {
                    return;
                }
                int reqFrequency = jsonObject.optInt("req_frequency");
                JSONArray urlList = jsonObject.optJSONArray("url_list");
                if (urlList == null || urlList.length() == 0) {
                    return;
                }
                for (int i = 0; i < urlList.length(); i++) {
                    String url = urlList.optString(i);
                    if (TextUtils.isEmpty(url)) {
                        continue;
                    }
                    loopRequestDomain(url, reqFrequency, maxReqNum);
                }
            }
        });
    }

    /**
     * 发起轮询请求域名
     *
     * @param url                    请求域名
     * @param requestFrequency       请求频率
     * @param requestCount           请求次数
     */
    public void loopRequestDomain(String url, long requestFrequency, long requestCount) {
        if (activeBeforeRequestManager == null) {
            return;
        }
        if (requestCount == 0) {
            return;
        }
        if (loopHandler == null) {
            loopHandler = new Handler();
        }
        activeBeforeRequestManager.requestCustomDomain(url);
        loopHandler.postDelayed(() -> {
            if (requestCount == 1) {
                return;
            }
            loopRequestDomain(url, requestFrequency, requestCount - 1);
        }, requestFrequency * 1000);
    }
}