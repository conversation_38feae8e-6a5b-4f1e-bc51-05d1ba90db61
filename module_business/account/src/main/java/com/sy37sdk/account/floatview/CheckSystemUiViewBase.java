package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.graphics.PixelFormat;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.sqwan.common.util.LogUtil;

/**
 * 描述:
 * 作者：znb
 * 时间：2021/4/10 13:49
 */
public class CheckSystemUiViewBase extends RelativeLayout implements SystemUiCheckUtils.onSystemUiChangedCallback, ScreenOrientationHelper.ScreenOrientationChangeListener {
    public interface CloseCallback{
        void invoke();
    }
    public CloseCallback closeCallback;
    protected int mTouchSlop;
    protected WindowManager mWindowManager;
    protected WindowManager.LayoutParams floatLayoutParams;
    protected final String TAG = this.getClass().getSimpleName();
    public Context mContext;
    /**
     * 屏幕亮度
     */
    private float screenBrightness;
    /**
     * 暗屏恢复亮屏的逻辑
     * @return
     */
    public boolean isScreenOnType() {
        return false;
    }

    public CheckSystemUiViewBase(Context context) {
        this(context,null);
    }

    public CheckSystemUiViewBase(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public CheckSystemUiViewBase(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }
    private void initScreenOnType(){
        if (isScreenOnType()) {
            if (floatLayoutParams!=null) {
//                floatLayoutParams.flags|=FLAG_KEEP_SCREEN_ON;
                if (getWindow()!=null) {
                    screenBrightness = getWindow().getAttributes().screenBrightness;
                    LogUtil.d(TAG, "initScreenOnType screenBrightness:"+screenBrightness);
                }
            }
        }

    }
    private void uninitScreenOnType(){


    }
    public void init(){
        if (mWindowManager==null) {
            mWindowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
            initLayoutParams();
            mTouchSlop = ViewConfiguration.get(mContext).getScaledTouchSlop()/4;
            if (mTouchSlop<=4) {
                mTouchSlop=4;
            }
            ScreenOrientationHelper.initOrAdd(mContext,this);
        }
        SystemUiCheckUtils.getInstance().addListener(this);
    }
    protected FloatViewUtils.FloatViewConfig config;
    public void initLayoutParams(){
        config = new FloatViewUtils.FloatViewConfig(mContext);
        floatLayoutParams = new WindowManager.LayoutParams(WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.FIRST_SUB_WINDOW,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.RGBA_8888);
        this.setPadding(config.marginLeft,config.marginTop,0,0);
        floatLayoutParams.gravity = Gravity.TOP | Gravity.LEFT;
        initScreenOnType();

    }

    @Override
    public void onSystemUiChanged(FloatViewUtils.FloatViewConfig config) {

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        uninitScreenOnType();
        release();

    }
    public void release(){
        SystemUiCheckUtils.getInstance().removeListener(this);
        removeView();
    }
    public void update(){
        if (this.isAttachedToWindow()) {
            if (mWindowManager!=null && floatLayoutParams!=null) {
                mWindowManager.updateViewLayout(this,floatLayoutParams);
            }
        }
    }
    public void addView(){
        if (!this.isAttachedToWindow()) {
            if (mWindowManager!=null && floatLayoutParams!=null) {
                mWindowManager.addView(this,floatLayoutParams);
            }
        }

    }
    public void removeView(){
        if (this.isAttachedToWindow()) {
            if (mWindowManager!=null && floatLayoutParams!=null) {
                mWindowManager.removeViewImmediate(this);
            }
        }
    }
    public void updateY(int y){
        if (floatLayoutParams!=null) {
            floatLayoutParams.y = y;
        }
    }
    public void updateX(int x){
        if (floatLayoutParams!=null) {
            floatLayoutParams.x = x;
        }
    }

    @Override
    public void onChange(int orientation) {

    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
            if (isScreenOnType()) {
                if (ev.getAction()== MotionEvent.ACTION_UP|ev.getAction()== MotionEvent.ACTION_DOWN) {
                if (screenBrightness!=0f) {
                    if (getWindow()!=null) {
                        WindowManager.LayoutParams params = getWindow().getAttributes();
                        float _screenBrightness = params.screenBrightness;
                        if (_screenBrightness!=screenBrightness) {
                            params.screenBrightness = screenBrightness;
                            getWindow().setAttributes(params);
                            LogUtil.d(TAG, "dispatchTouchEvent screenBrightness:"+screenBrightness);
                            return true;
                        }
                    }
                }
            }
        }
        return super.dispatchTouchEvent(ev);
    }
    private Window getWindow(){
        if (mContext instanceof Activity) {
            Activity activity = (Activity)mContext;
            return activity.getWindow();
        }
        return null;
    }
}
