package com.sy37sdk.account.view.base.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;
import com.sq.tool.sqtools.utils.ScreenUtils;
import com.sq.tools.utils.ResourceUtils;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.DisplayUtil;


public class ScannerView extends View {
    private final Paint maskPaint;
    private final int maskColor;
    private Rect frame;
    private final Paint textPaint;
    private final Rect mTextRect = new Rect();
    private Bitmap mScanline;
    private int mSlideTop;
    private final int mScreenW ;

    public ScannerView(final Context context, final AttributeSet attrs) {
        super(context, attrs);
        maskColor = Color.parseColor("#********");
        maskPaint = new Paint();
        maskPaint.setStyle(Style.FILL);
        textPaint = new Paint();
        textPaint.setColor(Color.parseColor("#FFFFFF"));
        textPaint.setStyle(Style.FILL_AND_STROKE);
        textPaint.setAntiAlias(true);
        textPaint.setTextAlign(Paint.Align.CENTER);
        textPaint.setTextSize(DensityUtil.dip2px(getContext(), 13));
        mScreenW = DisplayUtil.getScreenWidth(context);
    }

    public void setFraming(final Rect frame) {
        this.frame = frame;
        this.mSlideTop = frame.top;
        Bitmap bm = BitmapFactory.decodeResource(this.getResources(), ResourceUtils.getDrawableIdByName(getContext(), "sy37_scan_line"));
        this.mScanline = Bitmap.createScaledBitmap(bm, frame.width(), DensityUtil.dip2px(getContext(), 7), false);
        bm.recycle();
        invalidate();
    }

    @Override
    public void onDraw(final Canvas canvas) {
        if (frame == null) {
            return;
        }

        final int width = getWidth();
        final int height = getHeight();

        //绘制灰色遮罩区域
        maskPaint.setColor(maskColor);
        canvas.drawRect(0, 0, width, frame.top, maskPaint);
        canvas.drawRect(0, frame.top, frame.left, frame.bottom + 1, maskPaint);
        canvas.drawRect(frame.right + 1, frame.top, width, frame.bottom + 1,
            maskPaint);
        canvas.drawRect(0, frame.bottom + 1, width, height, maskPaint);

        textPaint.getTextBounds("将二维码放入框内，即可自动扫描", 0, "将二维码放入框内，即可自动扫描".length(),
            mTextRect);
        canvas.drawText("将二维码放入框内，即可自动扫描", (float) mScreenW / 2,
            frame.bottom + mTextRect.height() + DensityUtil.dip2px(getContext(), 26), textPaint);
        mSlideTop += 3;
        if (this.mSlideTop >= frame.bottom - 20) {
            this.mSlideTop = frame.top;
        }
        canvas.drawBitmap(this.mScanline, (float) frame.left, (float) this.mSlideTop, (Paint) null);
        this.postInvalidateDelayed(14L, frame.left, frame.top, frame.right, frame.bottom);
    }
}
