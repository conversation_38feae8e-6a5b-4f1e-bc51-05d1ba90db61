package com.sy37sdk.account.uagree;

import android.content.Context;
import android.graphics.Color;
import android.text.style.ForegroundColorSpan;
import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.policy.view.PolicyDialog;
import com.sy37sdk.account.policy.view.PolicyDialog.ConfirmCallback;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020-03-17
 */
public class UAgreeManager extends BaseEnginHandler {

    /**
     * 关闭
     */
    private static final int TYPE_CLOSE = 0;

    /**
     * 仅注册
     */
    private static final int TYPE_REG_ONLY = 1;

    /**
     * 仅登录
     */
    private static final int TYPE_LOGIN_ONLY = 2;

    /**
     * 注册和登录
     */
    private static final int TYPE_LOGIN_REG = 3;

    //外部调用传入的操作类型（login/regiser）
    public static final int OPERA_LOGIN = 1;
    public static final int OPERA_REGISTER = 2;

    private static volatile UAgreeManager instance;

    /**
     * 默认值关闭
     */
    private int type = TYPE_CLOSE;
    private int version = -1;

    private UAgreeManager() {
    }

    @Override
    public void init(Context _context) {
        super.init(_context);
        context = checkValid();
    }

    public static UAgreeManager getInstance() {
        if(instance == null) {
            synchronized (UAgreeManager.class) {
                if(instance == null) {
                    instance = new UAgreeManager();
                }
            }
        }
        return instance;
    }


    public void initConfig(String data) {
        LogUtil.i("int uagree config:" + data);
        try {
            JSONObject jsonObject = new JSONObject(data);
            String urlProtocol = jsonObject.optString("url_protocol", "");
            String urlpolicy = jsonObject.optString("url_policy", "");
            String urlInterim = jsonObject.optString("url_interim", "");
            UAgreeCacheHelper.setUrlProtocol(context, urlProtocol);
            UAgreeCacheHelper.setUrlPolicy(context, urlpolicy);
            UAgreeCacheHelper.setUrlInterim(context, urlInterim);
            if(jsonObject.has("uagree_config")) {
                JSONObject uagreeJson = jsonObject.getJSONObject("uagree_config");
                type = uagreeJson.optInt("type", TYPE_CLOSE);
                version = uagreeJson.optInt("version", 0);
            }
        } catch (JSONException e) {
            LogUtil.i("解析用户隐私协议配置失败");
            e.printStackTrace();
        }
    }

    /**
     * 登录需要判断当前版本是否已经同意
     * 如果是登录的话，必须传uname来判断当前用户是否需要查看用户协议
     */
    public boolean needShow(int opera, String uname) {
        int historyVersion = UAgreeCacheHelper.getVersion(context, "permission");
        return version > historyVersion || version == -1;
    }


    /**
     * 记录登录用户看到用户协议的新版本
     */
    public void refreshVersion(String uname) {
        UAgreeCacheHelper.setVersion(context, uname, version);
    }

    /**
     * 没有传递type， 只是用作浏览，游戏入口调用
     */
    public void showUAgree() {
        SQWebViewDialog dialog = new SQWebViewDialog(context);
        String openUrl = generateUAgreeUrl() + "&isAgree=true";
        dialog.setUrl(openUrl);
        dialog.show();
    }


    public void showUserProtocol(Context context) {
        SQWebViewDialog dialog = new SQWebViewDialog(context);
        String urlProtocol = UAgreeCacheHelper.getUrlProtocol(context) + "&isAgree=true";
        dialog.setUrl(urlProtocol);
        dialog.show();
    }

    public void showPolicy(Context context) {
        SQWebViewDialog dialog = new SQWebViewDialog(context);
        String urlPolicy = UAgreeCacheHelper.getUrlPolicy(context)  + "&isAgree=true";
        dialog.setUrl(urlPolicy);
        dialog.show();
    }

    public void showLoginPolicyAlert(ConfirmCallback confirmCallback) {
        PolicyDialog policyDialog = new PolicyDialog(context, confirmCallback);
        policyDialog.show();
    }


    public void showUAgreeToast() {
        new ToastUtil.Builder(context)
                .setBackgroundColor(Color.parseColor("#BB000000"))
                .addText("请勾选同意")
                .addText("用户协议", new ForegroundColorSpan(context.getResources().getColor(
                        SqResUtils.getColorId(context, "sy37_reg_protocol_txt_color"))))
                .addText("与")
                .addText("隐私政策", new ForegroundColorSpan(context.getResources().getColor(
                        SqResUtils.getColorId(context, "sy37_reg_protocol_txt_color"))))
                .addText("再进入游戏")
                .show();
    }

    private String generateUAgreeUrl() {
        String baseUrl = UAgreeCacheHelper.getUrlInterim(context);
        return addBaseParams(context, baseUrl);
    }

    public static String addBaseParams(Context context, String url) {
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        String baseParams = "gid=" + config.getGameid()
                + "&pid=" + config.getPartner()
                + "&dev=" + DevLogic.getInstance(context).getValue()
                + "&token=" + AccountCache.getToken(context)
                + "&sversion=" + VersionUtil.sdkVersion
                + "&refer=" + config.getRefer();
// scut 和 scut3 这两个参数少群说他拼接，客户端不用
//                + "&scut=" + ConfigManager.getInstance(context).getLoginCode();
//        if(MultiSdkManager.getInstance().isScut3()) {
//            baseParams = baseParams + "&scut3=" + MultiSdkManager.getInstance().getScut3();
//        }

        if(url.contains("?")) {
            return url + "&" + baseParams;
        } else {
            return url + "?" + baseParams;
        }
    }

    public boolean needShow(){
        return needShow(UAgreeManager.OPERA_LOGIN,"permission");
    }
    public void update(){
        UAgreeCacheHelper.setVersion(context, "permission", version);
    }
    public boolean isFirstCheck(){
        return UAgreeCacheHelper.getVersion(context, "permission")==0;
    }
}
