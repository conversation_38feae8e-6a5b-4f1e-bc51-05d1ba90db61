package com.sy37sdk.account.captcha;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;

import com.sqwan.common.web.WebViewToolBar;
import com.sqwan.common.webview.SQWebViewDialog;

/**
 *    author : 王琪
 *    time   : 2021/06/21
 *    desc   : 手机号验证登录号弹窗
 */
public class VerifyPhoneDialog extends SQWebViewDialog {

    private static final String SUCCESS_TAG = "1";

    protected VerifyListener mListener;

    public VerifyPhoneDialog(Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setOnCancelListener(new OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if (mListener != null) {
                    mListener.result(false, "取消验证");
                }
            }
        });
        setWebToolBarClickListener(new WebViewToolBar.WebToolBarClickListener() {
            @Override
            public void onClickBack() {

            }

            @Override
            public void onClickForward() {

            }

            @Override
            public void onClickRefresh() {

            }

            @Override
            public void onClickClose() {
                if (mListener != null) {
                    mListener.result(false, "取消验证");
                }
            }
        });
    }

    @Override
    protected void jsClose(String tag, String data) {
        super.jsClose(tag, data);
        boolean isSuccess = !TextUtils.isEmpty(tag) && tag.equals(SUCCESS_TAG);
        mListener.result(isSuccess,data);
    }

    @Override
    protected boolean isShowWebBar() {
        return true;
    }

    public void setVerifyListener(VerifyListener listener) {
        this.mListener = listener;
    }

    public interface VerifyListener {

        /**
         * @param success 是否验证成功
         * @param msg
         */
        void result(boolean success, String msg);
    }
}
