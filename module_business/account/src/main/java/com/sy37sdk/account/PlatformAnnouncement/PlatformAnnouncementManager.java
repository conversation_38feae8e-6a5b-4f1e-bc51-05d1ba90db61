package com.sy37sdk.account.PlatformAnnouncement;

import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.UrlConstant;
import org.json.JSONObject;

public class PlatformAnnouncementManager {


    private PlatformAnnouncementManager() {
    }

    private static final com.sy37sdk.account.PlatformAnnouncement.PlatformAnnouncementManager platformFaultManager = new com.sy37sdk.account.PlatformAnnouncement.PlatformAnnouncementManager();

    public static com.sy37sdk.account.PlatformAnnouncement.PlatformAnnouncementManager getInstance() {
        return platformFaultManager;
    }


    public void getPlatformAnnouncementRequest(final SQResultListener sqResultListener) {
        SqRequest.of(UrlConstant.PLATFORM_FAULT_URL)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .get(new SqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    Bundle bundle = new Bundle();
                    bundle.putString("title", jsonObject.optString("title"));
                    bundle.putBoolean("is_show", jsonObject.optBoolean("is_show"));
                    bundle.putString("content", jsonObject.optString("content"));
                    sqResultListener.onSuccess(bundle);
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    sqResultListener.onFailture(code, errorMsg);
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    sqResultListener.onFailture(state, msg);
                }
            });
    }


}
