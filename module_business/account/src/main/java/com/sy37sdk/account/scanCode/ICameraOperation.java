package com.sy37sdk.account.scanCode;

import android.util.Size;
import android.view.TextureView;

/**
 * @author: gsp
 * @date: 2024/12/17
 * @desc: 相机管理接口
 */
public interface ICameraOperation {

    /**
     * 打开相机并开启预览
     * @param surfaceView 用于预览
     * @param frameCallback 预览帧回调
     */
    void openCamera(TextureView surfaceView, FrameCallback frameCallback, OpenFailCallback failCallback);

    /**
     * 关闭相机
     */
    void closeCamera();

    interface FrameCallback {
        /**
         * 预览帧数据回调
         * @param imageData  预览帧数据
         * @return 返回值表示是否处理不再需要帧数据
         */
        boolean onFrameAvailable(byte[] imageData, int imageWidth, int imageHeight, Size previewSize);
    }

    interface OpenFailCallback {
        /**
         * 打开相机失败回调
         * @param msg 失败信息
         */
        void onFail(String msg);
    }
}
