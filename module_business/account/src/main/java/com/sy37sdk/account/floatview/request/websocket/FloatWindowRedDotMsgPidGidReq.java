package com.sy37sdk.account.floatview.request.websocket;

import com.sqwan.common.mod.CommonConfigs;

public class FloatWindowRedDotMsgPidGidReq extends FloatWindowRedDotMsgBaseReq {

    @Override
    public String getRealAppId() {
        String pid = CommonConfigs.getInstance().getSqAppConfig().getPartner();
        String gid = CommonConfigs.getInstance().getSqAppConfig().getGameid();
        return String.format("%s_%s", pid, gid);
    }
}