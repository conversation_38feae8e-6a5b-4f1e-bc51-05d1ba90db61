package com.sy37sdk.account.db;

/**
 * 假登录
 */
public interface LoginTriggerTable {
    //表名称
    String TABLE_NAME = "login_trigger";

    /**
     * 表字段
     */
    //id
    String ID = "ID";
    //用户id
    String UID = "uid";
    //用户名称
    String UNAME = "uname";
    //时间
    String TRIGGER_TIME = "trigger_time";
    //登录类型 common:普通账户  phone:手机
    String LOGIN_TYPE = "login_type";

    String TOKEN = "token";

    String SQL = "create table " + TABLE_NAME + " ("
            + ID + " integer primary key autoincrement, "
            + UID + " text, "
            + UNAME + " text, "
            + TOKEN + " text, "
            + TRIGGER_TIME + " integer, "
            + LOGIN_TYPE + " text)";
}
