package com.sy37sdk.account.scanCode;

import android.graphics.Rect;
import android.util.Size;
import android.view.TextureView;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.PlanarYUVLuminanceSource;
import com.google.zxing.ReaderException;
import com.google.zxing.Result;
import com.google.zxing.common.HybridBinarizer;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.scanCode.ICameraOperation.FrameCallback;
import java.util.Collection;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;

/**
 * @author: gsp
 * @date: 2024/12/18
 * @desc: zxing解码
 */
public class ZxingDecoder implements FrameCallback {

    private final TextureView mTextureView;
    private final ResultCallback mResultCallback;
    private boolean mPause = false;
    public ZxingDecoder(TextureView textureView, ResultCallback resultCallback) {
        this.mTextureView = textureView;
        this.mResultCallback = resultCallback;
    }


    @Override
    public boolean onFrameAvailable(byte[] imageData, int imageWidth, int imageHeight, Size previewSize) {
        Rect rect = getFramingRectInPreview(mTextureView.getWidth(), mTextureView.getHeight(),
            previewSize);
        MultiFormatReader multiFormatReader = getMultiFormatReader();
        Result result;
        if (rect.left + rect.width() > imageWidth || rect.top + rect.height() > imageHeight){
            LogUtil.w("【ScanCode】帧数据异常 left :" + rect.left + " ，width :"+ rect.width() +  " top :" + rect.top + " ，height :"+ rect.height());
            LogUtil.w("【ScanCode】帧数据异常 imageWidth :" + imageWidth + " ，imageHeight :" + imageHeight) ;
            rect.right = imageWidth - rect.left;
            rect.bottom = imageHeight - rect.top;
        }
        PlanarYUVLuminanceSource planarYUVLuminanceSource = new PlanarYUVLuminanceSource(imageData,
            imageWidth,
            imageHeight, rect.left, rect.top, rect.width(), rect.height(), false);
        BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(planarYUVLuminanceSource));
        try {
            result = multiFormatReader.decodeWithState(bitmap);
            if (mResultCallback != null && !mPause) {
                LogUtil.d("【ScanCode】解码成功 " + result);
                mResultCallback.onResult(result);
                return false;
            }
        } catch (ReaderException re) {
            //no-op
        } finally {
            multiFormatReader.reset();
        }
        return true;
    }

    /**
     * 获取富文本reader
     */
    private MultiFormatReader getMultiFormatReader() {
        MultiFormatReader mMultiFormatReader = new MultiFormatReader();
        Collection<BarcodeFormat> decodeFormats = EnumSet.noneOf(BarcodeFormat.class);
        //指定扫码数据类型
        decodeFormats.addAll(DecodeFormatManager.ONE_D_FORMATS);
        decodeFormats.addAll(DecodeFormatManager.QR_CODE_FORMATS);
        decodeFormats.addAll(DecodeFormatManager.DATA_MATRIX_FORMATS);

        final Map<DecodeHintType, Object> hints = new EnumMap<>(DecodeHintType.class);
        hints.put(DecodeHintType.POSSIBLE_FORMATS, decodeFormats);
        hints.put(DecodeHintType.CHARACTER_SET, "UTF8");
        mMultiFormatReader.setHints(hints);
        return mMultiFormatReader;
    }

    /**
     * 预览输出区域
     */
    private Rect getFramingRectInPreview(int viewWidth, int viewHeight, Size previewSize) {
        int width = viewWidth * 3 / 4;
        int height = viewHeight * 3 / 4;
        int leftOffset = 0;
        int topOffset = 0;
        Rect framingRect = new Rect(leftOffset, topOffset, leftOffset + width, topOffset + height);

        Rect rect = new Rect(framingRect);
        rect.left = rect.left * previewSize.getWidth() / viewWidth;
        rect.right = rect.right * previewSize.getWidth() / viewWidth;
        rect.top = rect.top * previewSize.getHeight() / viewHeight;
        rect.bottom = rect.bottom * previewSize.getHeight() / viewHeight;
        return rect;
    }

    /**
    * 继续解码
    * */
    public void resume() {
        mPause = false;
    }

    /**
     * 暂停解码
     * */
    public void pause() {
        mPause = true;
    }

    public interface ResultCallback {
        void onResult(Result result);
    }
}
