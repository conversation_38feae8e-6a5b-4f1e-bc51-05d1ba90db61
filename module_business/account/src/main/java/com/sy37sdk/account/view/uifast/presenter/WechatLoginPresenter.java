package com.sy37sdk.account.view.uifast.presenter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.sq.tools.Logger;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.view.ui.WechatRegSuccessDialog;
import com.sy37sdk.account.view.uifast.view.IWechatLoginView;

import java.util.Map;

public class WechatLoginPresenter extends BaseAccountPagerPresenter<IWechatLoginView> implements IWechatPresenter {
    /**
     * 注册条款是否勾选已读
     */
    private boolean clauseStatus = false;

    public WechatLoginPresenter(Context context, IWechatLoginView view) {
        super(context, view);
    }

    @Override
    public View getView() {
        return (View) mView;
    }

    @Override
    public void clauseClick(boolean isCheck) {
        this.clauseStatus = isCheck;
        Logger.info("clauseClick " + isCheck);
        if (isCheck) {
            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.agreement, SqTrackBtn.SqTrackBtnExt.agreement);
        }
    }

    @Override
    public void toClausePage() {
        UAgreeManager.getInstance().showUserProtocol(context);
    }

    @Override
    public void toPolicy() {
        UAgreeManager.getInstance().showPolicy(context);
    }

    @Override
    public void wechatLogin() {
        if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                clauseStatus = true;
                mView.checkedClause();
                wechatLogin();
            });
            return;
        }
        if (mView != null) {
            mView.showLoading();
        }
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.wechat, SqTrackBtn.SqTrackBtnExt.wechat);
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_WECHAT_NUM, LoginTractionManager.login_way_wechat);

        AccountLogic.getInstance(context).wechatLogin(new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(final Map<String, String> account) {
                LoginTractionManager.track(LoginTractionManager.login_way_wechat, account);
                if (mView != null) {
                    mView.hideLoading();
                }
                String pwd = account.get("pwd");
                //微信登录后如果有返回密码，则弹出注册成功弹窗
                if (!TextUtils.isEmpty(pwd) ) {
                    //通过微信注册的，弹注册成功的弹窗
                    if (mView != null) {
                        mView.showRegDialog(account, new WechatRegSuccessDialog.IWechatRegListener() {
                            @Override
                            public void onSuccess() {
                                if (mView != null) {
                                    mView.loginSuccess(account);
                                }
                            }
                        });
                    }
                } else {
                    if (mView != null) {
                        mView.loginSuccess(account);
                    }
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, "登录失败，请稍后再试");
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_WECHAT_NUM, LoginTractionManager.login_way_wechat, code + "", msg);
            }
        });

    }

    @Override
    public void wechatRegister() {

    }
}
