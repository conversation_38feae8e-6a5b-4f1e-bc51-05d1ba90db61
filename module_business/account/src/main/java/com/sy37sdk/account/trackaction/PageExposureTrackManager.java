package com.sy37sdk.account.trackaction;

import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;

import java.util.HashMap;

/**
 * 页面曝光上报管理类
 */
public class PageExposureTrackManager {

    //页面曝光的间隔时长，超过这个值才上报
    private static final int TRACK_TIME_INTERVAL = 60 * 1000;

    /**
     * 新埋点上报页面曝光
     */
    public static void track(String viewId, String viewName) {
        LogUtil.i("页面曝光" + viewId + " " + viewName);
        HashMap<String, String> extra = new HashMap<>();
        extra.put(SqTrackKey.view_id, viewId);
        extra.put(SqTrackKey.view_name, viewName);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_view_show, extra);
    }
}
