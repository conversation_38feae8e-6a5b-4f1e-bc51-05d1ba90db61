package com.sy37sdk.account.floatview;

import android.support.annotation.NonNull;

import com.sq.tools.network.respond.JsonResponse;
import com.sq.tools.network.respond.ResponseTools;

import org.json.JSONObject;

/**
 * 菜单配置的数据类，根据API:  http://yapi.39on.com/project/75/interface/api/11072 返回数据解析而来.
 */
public class MenuConfig extends ResponseTools {
    public static final String WARNING_TYPE_RED_POINT = "1"; //红点
    public static final String WARNING_TYPE_TEXT = "2"; //文字提醒
    public static final String WARNING_TYPE_SHAKE = "3"; //抖动

    @JsonResponse("id")
    public String id;

    @JsonResponse("uuid")
    public String uuid;

    @JsonResponse("title")
    public String title;

    @JsonResponse("desc")
    public String desc;

    @JsonResponse("icon_url")
    public String iconUrl;

    @JsonResponse("open_type")
    public String openType;

    @JsonResponse("url")
    public String openUrl;

    @JsonResponse("need_red_dot")
    public int needRedDot;
    @JsonResponse("warning_type")
    public String warningType = "";

    @JsonResponse("warning_msg")
    public String warningMsg = "";

    @JsonResponse("priority")
    public int priority;

    @JsonResponse("sdk_method_value")
    public String sdk_method_value;

    public MenuConfig(@NonNull String data) {
        initSelfByString(data);
    }

    public MenuConfig(@NonNull JSONObject data) {
        initSelfByJson(data);
    }

    public MenuConfig() {

    }

    public boolean needRedDot(){
        return needRedDot==1;
    }

    @Override
    public String toString() {
        return "MenuConfig{" +
                "id='" + id + '\'' +
                ", uuid='" + uuid + '\'' +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", openType='" + openType + '\'' +
                ", openUrl='" + openUrl + '\'' +
                ", needRedDot=" + needRedDot +
                '}';
    }
}
