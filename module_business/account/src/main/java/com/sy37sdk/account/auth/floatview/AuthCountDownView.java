package com.sy37sdk.account.auth.floatview;

import android.animation.ValueAnimator;
import android.content.Context;
import android.support.annotation.NonNull;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.account.floatview.DragViewLayout;
import com.sy37sdk.account.floatview.FloatViewUtils;
import com.sy37sdk.account.floatview.FloatWindow;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-01 12:05
 */
public class AuthCountDownView extends DragViewLayout {
    interface AuthClickCallback{
        void onClick();
    }

    View authShow,authHide,showArrow;
    TextView tvAuth,tvAuthCd;
    public AuthClickCallback authClickCallback;
    public AuthCountDownView(@NonNull Context context) {
        super(context);
        View.inflate(context, SqResUtils.getLayoutId(context, "sysq_view_auth_countdown"),this);
        authHide = findViewById(SqResUtils.getId(context,"view_hide"));
        authShow = findViewById(SqResUtils.getId(context,"view_show"));
        tvAuth = findViewById(SqResUtils.getId(context,"tvAuth"));
        tvAuthCd = findViewById(SqResUtils.getId(context,"tvAuthCd"));
        showArrow = findViewById(SqResUtils.getId(context,"view_show_arrow"));
        setFilterDragClickListener(tvAuth,new FloatWindow.ClickListenerAdapter(){
            @Override
            public void onClick(View v,int x,int y) {
                if (authClickCallback!=null) {
                    authClickCallback.onClick();
                }
            }
        });
        setFilterDragClickListener(authHide,new FloatWindow.ClickListenerAdapter() {
            @Override
            public void onClick(View v,int x,int y) {
                show();
            }
        });
        setFilterDragClickListener(showArrow,new FloatWindow.ClickListenerAdapter() {
            @Override
            public void onClick(View v,int x,int y) {
                hide();
            }
        });
    }
    private void hideAll(){
        ViewUtils.gone(authShow);
        ViewUtils.gone(authHide);

    }
    public void show(){
        LogUtil.i(TAG,"show");
        hideAll();
        ViewUtils.show(authShow);
        update();
    }
    public void hide(){
        LogUtil.i(TAG,"hide");
        hideAll();
        ViewUtils.show(authHide);
        update();
    }
    public void changeTime(String time){
        LogUtil.i(TAG,"changeTime");
        String result = String.format(SqResUtils.getStringByName(getContext(),"sysq_name_auth_cd_time"),time);
        if (!tvAuthCd.getText().toString().equals(result)) {
            LogUtil.i(TAG,"changeTime result:"+result);
            tvAuthCd.setText(result);
            update();
        }
    }
    public void initView() {
        updateY(0);
        updateX(0);
        addView();
    }
    @Override
    public void release(){
        LogUtil.i(TAG,"release");
        authClickCallback = null;
        super.release();
    }

    @Override
    protected void handleStartAnimResultPos() {
        valueAnimator = ValueAnimator.ofInt( floatLayoutParams.y, 0);
    }

    @Override
    protected void handleOnAnimationUpdate(ValueAnimator animation) {
        floatLayoutParams.y = (int)animation.getAnimatedValue();
    }
    public boolean isCheckStayEdge() {
        return false;
    }

    public boolean isCheckRecordPos() {
        return false;
    }

    @Override
    public void onSystemUiChanged(FloatViewUtils.FloatViewConfig config) {
        updateX(0);
        updateY(0);
        update();
    }
    @Override
    public void onChange(int orientation) {
        initLayoutParams();
        update();
    }

    @Override
    public boolean isCheckShowCompelete() {
        return false;
    }
}
