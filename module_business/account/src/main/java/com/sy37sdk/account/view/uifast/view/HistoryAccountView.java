package com.sy37sdk.account.view.uifast.view;

import static com.sy37sdk.account.view.uifast.switcher.AccountPage.ACCOUNT_PHONE_PAGE;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.CheckClassUtils;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.alifast.AccountLoginManager;
import com.sy37sdk.account.alifast.FastBooleanResultListener;
import com.sy37sdk.account.alifast.FastLoginManager;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.util.AccountLoginType;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.LoginSkinHelper;
import com.sy37sdk.account.view.PopListHelper;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.presenter.HistoryAccountPresenter;
import com.sy37sdk.account.view.uifast.presenter.IHistoryAccountPresenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;

import java.util.Map;

public class HistoryAccountView extends BaseSwitchView implements IHistoryAccountView {

    private IHistoryAccountPresenter presenter;

    private TextView tvAccount;
    private TextView tvForgetPwd;
    private TextView tvLogin;
    private TextView tvClause, tvPolicy;
    private CheckBox cbClause;
    private TextView tvOtherLogin;

    private ImageView ivAccountType;

    private PopListHelper popListHelper;

    private ImageView selectAccountView;

    private View accountRow;

    private View contentLayout;

    private UserInfo mUserInfo;

    private ILoginListener loginListener;

    private Context mContext;

    public HistoryAccountView(Context context, ILoginDialog loginDialog, ILoginListener loginListener) {
        super(context);
        this.mContext = context;
        this.loginListener = loginListener;
        this.loginDialog = loginDialog;
        presenter = new HistoryAccountPresenter(context, this);
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_history_account";
    }

    @Override
    public void initView() {
        contentLayout = getViewByName("content_layout");
        contentLayout.setBackgroundResource(LoginSkinHelper.getHistoryLoginBackgroundResId(getContext()));
        tvAccount = getViewByName("tv_account");
        tvLogin = getViewByName("tv_login");
        tvLogin.setBackgroundResource(LoginSkinHelper.getLoginBtnBackgroundResId(getContext()));
        tvLogin.setTextColor(LoginSkinHelper.getLoginBtnTextColor(getContext()));
        tvOtherLogin = getViewByName("tv_other_login");
        tvOtherLogin.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        ivAccountType = getViewByName("iv_account_type");
        selectAccountView = getViewByName("iv_select_account");
        accountRow = getViewByName("ll_account_row");
        tvForgetPwd = getViewByName("tv_forget_pwd");
        tvForgetPwd.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        cbClause = getViewByName("cb_clause");
        tvClause = getViewByName("tv_clause");
        tvPolicy = getViewByName("tv_policy");
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        post(new Runnable() {
            @Override
            public void run() {
                initAccountList();
            }
        });
        presenter.initData();
        hideSoftInput();
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.history_account, SqTrackPage.SqTrackViewName.HISTORY_ACCOUNT);
    }

    @Override
    public void initEvent() {
        tvOtherLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (EntranceManager.getInstance().isWxLoginEntrance()) {
                    if (EntranceManager.getInstance().getWxLoginUIVersion() == EntranceManager.WX_LOGIN_UI_VERSION_V1) {
                        loginDialog.onSwitch(AccountPage.ACCOUNT_LOGIN_WECHAT, null);
                    } else {
                        loginDialog.onSwitch(AccountPage.ACCOUNT_LOGIN_WECHAT_V2, null);
                    }
                }else {
                    if(isLoginAuthActivityExist()){
                        FastLoginManager.getInstance(mContext).getFastEnv(new FastBooleanResultListener() {
                            @Override
                            public void callback(boolean isFastEvn) {
                                if(!isFastEvn){
                                    loginDialog.onSwitch(ACCOUNT_PHONE_PAGE, null);
                                }else {
                                    AccountUtil.setToFastLogin(true);
                                    AccountUtil.setCanFastBack(true);
                                    loginDialog.dismissAccountDialog();
                                    AccountLoginManager.getInstance(mContext).login(loginListener);
                                }
                            }
                        });
                    }else{
                        loginDialog.onSwitch(ACCOUNT_PHONE_PAGE, null);
                    }

                }

            }
        });

        accountRow.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (popListHelper == null || popListHelper.getAccountList() == null || popListHelper.getAccountList().size() < 1) {
                    return;
                }
                if (!popListHelper.isShowing()) {
                    selectAccountView.setImageResource(getIdByName("sysq_ic_account_list_up", "drawable"));
                    popListHelper.showAccountList(accountRow);
                } else {
                    selectAccountView.setImageResource(getIdByName("sysq_ic_account_list_down", "drawable"));
                    popListHelper.hideAccountList();
                }
            }
        });
        tvLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isQuickClick()) {
                    return;
                }
                presenter.login(mUserInfo);
            }
        });
        tvForgetPwd.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.forgetPassword();
            }
        });
        tvClause.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toClausePage();
            }
        });
        tvPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toPolicy();
            }
        });
        cbClause.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                presenter.clauseClick(isChecked);
            }
        });
    }

    @Override
    public String getTitle() {
        return "登录";
    }

    @Override
    public void setAccount(UserInfo userInfo) {
        mUserInfo = userInfo;
        if (userInfo == null) {
            loginDialog.onSwitch(ACCOUNT_PHONE_PAGE, null);
            return;
        }
        selectUserInfo(userInfo);
    }

    private void initAccountList() {
        popListHelper = new PopListHelper();
        popListHelper.initAccountList(getContext(), AccountUtil.getAllUserInfo(getContext()), accountRow.getWidth(), 42, new PopListHelper.ItemClickListener() {
            @Override
            public void onSelect(UserInfo userInfo) {
                mUserInfo = userInfo;
                selectUserInfo(userInfo);
            }

            @Override
            public void onDelete(UserInfo userInfo) {
                presenter.deleteUser(userInfo);
                String alias = userInfo.getAlias();
                String uname = userInfo.getUname();
                String currentUname = mUserInfo == null ? "" : mUserInfo.getUname();
                if ((!TextUtils.isEmpty(alias) && alias.contentEquals(currentUname)) || (!TextUtils.isEmpty(uname) && uname.contentEquals(currentUname))) {
                    tvAccount.setText("");
                }
            }
        }, new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (popListHelper != null) {
                    selectAccountView.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_ic_account_list_down"));
                }
            }
        });
    }


    @Override
    public void onSwitch(int index) {
        loginDialog.onSwitch(index, null);
    }

    @Override
    public void enableLoginBtn(boolean enable) {
        tvLogin.setEnabled(enable);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        loginDialog.loginSuccess(data);
    }

    @Override
    public String getPhone() {
        String mobile = "";
        if (mUserInfo != null) {
            if (!TextUtils.isEmpty(mUserInfo.getMobile())) {
                mobile = mUserInfo.getMobile();
            }
        }
        return mobile;
    }

    @Override
    public void startView(int page, Bundle bundle) {
        // 如果是不允许使用账号密码登录，就不进行跳转
        if (page == AccountPage.ACCOUNT_LOGIN_PAGE && !EntranceManager.getInstance().isAccountLoginEntrance()) {
            return;
        }
        loginDialog.onSwitch(page, bundle);
    }

    @Override
    public void checkedClause() {
        cbClause.setChecked(true);
    }


    private void selectUserInfo(UserInfo userInfo) {
        String loginType = userInfo.getLoginType();
        if (AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE.equals(loginType)) {
            ivAccountType.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_item_account_phone"));
            tvAccount.setText(userInfo.getMobile());
        } else if (AccountLoginType.LoginType.ACCOUNT_TYPE_WECHAT.equals(loginType)) {
            ivAccountType.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_item_account_wechat"));
            tvAccount.setText(userInfo.getUname());
        } else {
            ivAccountType.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_ic_account"));
            tvAccount.setText(!TextUtils.isEmpty(userInfo.getAlias()) ? userInfo.getAlias() : userInfo.getUname());
        }
    }

    private boolean isLoginAuthActivityExist() {
        return CheckClassUtils.classExist("com.mobile.auth.gatewayauth.LoginAuthActivity");
    }
}