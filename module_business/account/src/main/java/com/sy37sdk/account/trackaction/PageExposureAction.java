package com.sy37sdk.account.trackaction;

import java.util.HashMap;

public class PageExposureAction {
    //闪验页面
    public static final String PAGE_VIEW_FAST = "view01";
    //手机号输入
    public static final String PAGE_VIEW_PHONE = "view02";
    //手机号+验证码
    public static final String PAGE_VIEW_PHONE_CODE = "view03";
    //手机号+密码
    public static final String PAGE_VIEW_PHONE_PWD = "view04";
    //账号密码登录
    public static final String PAGE_VIEW_ACCOUNT_LOGIN = "view05";
    //账号密码注册
    public static final String PAGE_VIEW_ACCOUNT_REG = "view06";

    public static HashMap<String, Long> pageExposureTime = new HashMap<>();

    public static HashMap<String, String> pageExposure = new HashMap<>();

    static {
        pageExposure.put(PAGE_VIEW_FAST, "闪验页面曝光");
        pageExposure.put(PAGE_VIEW_PHONE, "手机号获取验证码页面曝光");
        pageExposure.put(PAGE_VIEW_PHONE_CODE, "手机号填写验证码页面曝光");
        pageExposure.put(PAGE_VIEW_PHONE_PWD, "手机号填写密码码页面曝光");
        pageExposure.put(PAGE_VIEW_ACCOUNT_LOGIN, "账号密码登录页曝光");
        pageExposure.put(PAGE_VIEW_ACCOUNT_REG, "账号密码注册页曝光");
    }
}
