package com.sy37sdk.account.face.ui;

import static com.sqwan.common.util.PermissionHelper.SQ_REQUEST_PERMISSION_CODE;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.aliyun.aliyunface.api.ZIMCallback;
import com.aliyun.aliyunface.api.ZIMFacade;
import com.aliyun.aliyunface.api.ZIMFacadeBuilder;
import com.aliyun.aliyunface.api.ZIMResponse;
import com.plugin.standard.RealBaseActivity;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tools.Logger;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.base.L;
import com.sqwan.common.data.SpannableEntity;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.SpannableHelper;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;
import com.sy37sdk.account.face.FaceActivityHelper;
import com.sy37sdk.account.face.OnFaceVerifyListener;
import java.util.HashMap;
import org.json.JSONObject;

/**
 * 人脸认证二次确认提醒
 */
public class FaceVerifyConfirmActivity extends RealBaseActivity {

    public static final int REQUEST_CODE = 10000;
    public static final int RESULT_CODE = 10001;

    //当人脸认证sdk返回结果为Z1024 - SDK认证流程正在进行中，请等待本地认证流程完成后再发起新调用时，重试发起认证的次数
    private static final int RETRY_COUNT = 1;

    private String[] permission = {Manifest.permission.CAMERA};
    private String[] permissionDesc = {"相机权限：协助用户找回账号密码和运用人脸识别验证用户身份"};

    //页面传值的key
    public static final String BUNDLE_VERIFY_STATUS = "verify_status";
    public static final String BUNDLE_VERIFY_NEED_SHOW = "verify_need_show";
    //认证成功
    public static final int VERIFY_STATUS_SUCCESS = 1;
    //认证失败
    public static final int VERIFY_STATUS_FAIL = 0;

    //认证完成后是否需要关闭
    public static final boolean VERIFY_NEED_FINISH = false;


    //姓名
    public static final String BUNDLE_USER_NAME = "user_name";
    //身份证
    public static final String BUNDLE_USER_CARD = "user_card";
    //提示信息
    public static final String BUNDLE_VERIFY_TIP = "verify_tip";

    private View backView, helpView, privacyView;

    private TextView tvConfirmTip, tvUserName, tvUserCard, tvVerify, tvClause;

    private ImageView ivClause;

    private String userName, userCard, verifyTip = "";

    private VerifyValidLoadingDialog verifyValidLoadingDialog;

    //是否勾选了用户协议
    private boolean isClause;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        FaceActivityHelper.requestFull((Activity) getContext());
        setContentView(SqResUtils.getLayoutId(getContext(), "sysq_face_confirm_activity"));
        FaceActivityHelper.initStatusView((Activity) getContext());
        initData();
        initView();
        initEvent();
    }


    private void initView() {
        backView = findViewById(SqResUtils.getId(getContext(), "back"));
        helpView = findViewById(SqResUtils.getId(getContext(), "help"));
        tvConfirmTip = findViewById(SqResUtils.getId(getContext(), "tv_confirm_tip"));
        tvUserName = findViewById(SqResUtils.getId(getContext(), "tv_user_name"));
        tvUserCard = findViewById(SqResUtils.getId(getContext(), "tv_user_card"));
        privacyView = findViewById(SqResUtils.getId(getContext(), "ll_privacy"));
        ivClause = findViewById(SqResUtils.getId(getContext(), "iv_clause"));
        tvVerify = findViewById(SqResUtils.getId(getContext(), "tv_start_verify"));
        tvClause = findViewById(SqResUtils.getId(getContext(), "tv_clause"));

        tvConfirmTip.setText(verifyTip);
        tvUserName.setText(userName);
        tvUserCard.setText(userCard);
        setSpannable();
    }


    private void initData() {
        Intent intent = getIntent();
        if (intent != null && intent.getExtras() != null) {
            Bundle bundle = intent.getExtras();
            userName = bundle.getString(BUNDLE_USER_NAME);
            userCard = bundle.getString(BUNDLE_USER_CARD);
            verifyTip = bundle.getString(BUNDLE_VERIFY_TIP);
        }
        if (TextUtils.isEmpty(userName)) {
            userName = "";
        }
        if (TextUtils.isEmpty(userCard)) {
            userCard = "";
        }
        if (TextUtils.isEmpty(verifyTip)) {
            verifyTip = "";
        }
    }

    private void initEvent() {
        backView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.putExtra(BUNDLE_VERIFY_NEED_SHOW, true);
                ((Activity) getContext()).setResult(RESULT_CODE, intent);
                finish();
            }
        });
        helpView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtils.toSQWebUrl(L.getActivity(), UrlConstant.FACE_VERIFY_HELP_PAGE, "帮助");
            }
        });
        privacyView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toggleClause();
            }
        });
        tvVerify.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isClause) {
                    handlePermission();
                } else {
                    ToastUtil.showToast("请勾选同意用户协议和隐私条款");
                }
            }
        });

    }


    private void setSpannable() {
        final String userprotol = ActiveBeforeManager.getInstance().userProtocolInfo.userprotol;
        final String privacyprotol = ActiveBeforeManager.getInstance().userProtocolInfo.privacyprotol;
        SpannableEntity spannableEntityClause = new SpannableEntity();
        spannableEntityClause.setLink(userprotol);
        spannableEntityClause.setTag("《用户协议》");
        spannableEntityClause.setOnClickListener(new SpannableEntity.OnClickListener() {
            @Override
            public void onClick() {
                SQWebViewDialog dialog = new SQWebViewDialog(getContext());
                dialog.setCancelable(true);
                dialog.setUrl(userprotol);
                dialog.show();
            }
        });

        SpannableEntity spannableEntityPrivacy = new SpannableEntity();
        spannableEntityPrivacy.setLink(privacyprotol);
        spannableEntityPrivacy.setTag("《隐私条款》");
        spannableEntityPrivacy.setOnClickListener(new SpannableEntity.OnClickListener() {
            @Override
            public void onClick() {
                SQWebViewDialog dialog = new SQWebViewDialog(getContext());
                dialog.setCancelable(true);
                dialog.setUrl(privacyprotol);
                dialog.show();
            }
        });
        String spannableStr = SqResUtils.getStringByName(getContext(), "sysq_txt_face_verify_privacy");
        SpannableHelper.handleSpannableHighLineClick(tvClause, spannableStr, spannableEntityClause, spannableEntityPrivacy);
    }

    private void toggleClause() {
        if (isClause) {
            ivClause.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_ic_check_face_verify_normal"));
            isClause = false;
        } else {
            ivClause.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_ic_check_face_verify_selected"));
            isClause = true;
        }
    }

    private void startVerify() {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition);
        //1.初始化
        ZIMFacade.install(getContext());
        requestCertifyId();
    }

    //跟后端拿CertifyId
    private void requestCertifyId() {
        String metaInfo = ZIMFacade.getMetaInfos(getContext());
        AccountRequestManager requestManager = new AccountRequestManager(getContext());
        requestManager.requestCertifyId(metaInfo, new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                Logger.error("request certify id failed, response bean: %s", msg);
                ToastUtil.showToast(msg);
                HashMap<String, String> failMap = new HashMap<>();
                failMap.put(SqTrackKey.fail_code, "-1");
                failMap.put(SqTrackKey.reason_fail, "请求certifyId失败 " + data);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                try {
                    String certifyId = dataJson.optString("certify_id");
                    startCertifyFace(certifyId);
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast("请求认证失败");
                    HashMap<String, String> failMap = new HashMap<>();
                    failMap.put(SqTrackKey.fail_code, "-1");
                    failMap.put(SqTrackKey.reason_fail, "请求certifyId 后台返回数据解析错误 " + e.getMessage());
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                Logger.info("请求人脸验证id onFailure：code=" + code + " msg=" + errorMsg);
                showNetErrorDialog();
                HashMap<String, String> failMap = new HashMap<>();
                failMap.put(SqTrackKey.fail_code, code + "");
                failMap.put(SqTrackKey.reason_fail, "请求certifyId网络错误 " + errorMsg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
            }
        });
    }

    private int retryCount = 0;

    /**
     * 发起认证
     *
     * @param certifyId
     */
    private void startCertifyFace(final String certifyId) {
        //3、调用认证sdk
        final ZIMFacade zimFacade = ZIMFacadeBuilder.create(getContext());
        HashMap<String, String> extParams = new HashMap<>();
//        // 如需指定活体检测UI界面方向（横屏+竖屏），请指定这一项。
        extParams.put(ZIMFacade.ZIM_EXT_PARAMS_KEY_SCREEN_ORIENTATION, ZIMFacade.ZIM_EXT_PARAMS_VAL_SCREEN_PORT);
//        // 如需支持活体视频返回，请指定这一项，并在response.videoFilePath或在服务端查询接口获取视频本地路径。
//        extParams.put(ZIMFacade.ZIM_EXT_PARAMS_KEY_USE_VIDEO, ZIMFacade.ZIM_EXT_PARAMS_VAL_USE_VIDEO_TRUE);
//
//        // 如需设置OCR的"下一步"按钮颜色（默认可不设置），请设置此项，如红色 #FF0000。
//        extParams.put(ZIMFacade.ZIM_EXT_PARAMS_KEY_OCR_BOTTOM_BUTTON_COLOR, "#FF0000");
//        // 如需自定义活体检测页面的进度条颜色（默认可不设置），请设置此项，如红色 #FF0000。
//        extParams.put(ZIMFacade.ZIM_EXT_PARAMS_KEY_FACE_PROGRESS_COLOR, "#FF0000");
        zimFacade.verify(certifyId, false, extParams, new ZIMCallback() {
            @Override
            public boolean response(final ZIMResponse response) {
                Logger.info("人脸验证回调：msg = " + response.msg + " code = " + response.code + " reason = " + response.reason + " deviceToken = " + response.deviceToken + " videoFilePath = " + response.videoFilePath);
                if (response.reason.contains("Z1024") && retryCount < RETRY_COUNT) {
                    HashMap<String, String> failMap = new HashMap<>();
                    failMap.put(SqTrackKey.fail_code, "1001-Z1024");
                    failMap.put(SqTrackKey.reason_fail, "SDK认证流程正在进行中，请等待本地认证流程完成后再发起新调用。");
                    failMap.put(SqTrackKey.face_verify_id, certifyId);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
                    //当人脸认证sdk返回结果为Z1024 - SDK认证流程正在进行中，请等待本地认证流程完成后再发起新调用时，且小于最大重试次数,重试
                    retryCount++;
                    startVerify();
                } else {
                    validateCertifyFace(certifyId, response);
                }
                return true;
            }
        });
    }

    /**
     * 校验认证是否成功
     */
    private void validateCertifyFace(final String certifyId, final ZIMResponse response) {
        if (response.code == 2006 || response.code == 1000) {
            //如果认证成功或失败，才调用loading
            showVerifyValidLoadingDialog();
        }
        AccountRequestManager requestManager = new AccountRequestManager(getContext());
        requestManager.checkValidCertify(certifyId, new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                dismissVerifyValidLoadingDialog();
                Logger.error("request validateCertifyFace failed, response bean: %s", msg);
                showVerifyFail();
                HashMap<String, String> failMap = new HashMap<>();
                failMap.put(SqTrackKey.fail_code, "-1");
                failMap.put(SqTrackKey.reason_fail, "验证失败");
                failMap.put(SqTrackKey.face_verify_id, certifyId);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                dismissVerifyValidLoadingDialog();
                try {
                    int passed = dataJson.optInt("passed");
                    String passedMsg = dataJson.optString("passed_msg");
                    if (passed == 1) {
                        //认证成功
                        HashMap<String, String> verifySuccMap = new HashMap<>();
                        verifySuccMap.put(SqTrackKey.face_verify_id, certifyId);
                        SqTrackActionManager2.getInstance()
                            .trackAction(SqTrackAction2.face_recognition_succ, verifySuccMap);
                        showVerifySuccess();
                    } else {
                        HashMap<String, String> failMap = new HashMap<>();
                        failMap.put(SqTrackKey.fail_code, response.code + "-" + response.reason);
                        failMap.put(SqTrackKey.reason_fail, "验证不通过 " + passedMsg + " - " + response.msg);
                        failMap.put(SqTrackKey.face_verify_id, certifyId);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
                        if (1003 == response.code) {
                            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_FACE_CANCEL);
                            //用户主动退出认证,直接回到切换账号页面
                            ModHelper.get(IAccountMod.class).webEnLogin(false);
                            Intent intent = new Intent();
                            intent.putExtra(BUNDLE_VERIFY_NEED_SHOW, false);
                            ((Activity) getContext()).setResult(RESULT_CODE, intent);
                            finish();
                        } else if (2006 == response.code) {
                            //刷脸失败，跳转至失败页面
                            showVerifyFail();
                        } else {
                            //其他错误情况，弹toast
                            if (TextUtils.isEmpty(response.msg)) {
                                ToastUtil.showToast(response.code + "-" + response.reason + " 认证失败");
                            } else {
                                ToastUtil.showToast(response.code + "-" + " " + response.msg);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtil.showToast("数据解析错误，认证失败");
                    HashMap<String, String> failMap = new HashMap<>();
                    failMap.put(SqTrackKey.fail_code, "-1");
                    failMap.put(SqTrackKey.reason_fail, "验证发生数据解析错误 " + e.getMessage());
                    failMap.put(SqTrackKey.face_verify_id, certifyId);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                dismissVerifyValidLoadingDialog();
                showNetErrorDialog();
                HashMap<String, String> failMap = new HashMap<>();
                failMap.put(SqTrackKey.fail_code, code + "");
                failMap.put(SqTrackKey.reason_fail, "验证发生网络错误 " + errorMsg);
                failMap.put(SqTrackKey.face_verify_id, certifyId);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.face_recognition_fail, failMap);
            }
        });


    }

    private void showNetErrorDialog() {
        NetErrorDialog netErrorDialog = new NetErrorDialog(getContext());
        netErrorDialog.setOnClickNetListener(new NetErrorDialog.OnClickNetListener() {
            @Override
            public void clickLeft() {
                AppUtils.toSQWebUrl(L.getActivity(), UrlConstant.FACE_VERIFY_CUSTOMER_PAGE, "客服");
            }

            @Override
            public void clickRight() {

            }
        });
        netErrorDialog.show();
    }

    private void showVerifyValidLoadingDialog() {
        if (verifyValidLoadingDialog == null) {
            verifyValidLoadingDialog = new VerifyValidLoadingDialog(getContext());
            verifyValidLoadingDialog.setCancelable(false);
        }
        verifyValidLoadingDialog.show();
    }

    private void dismissVerifyValidLoadingDialog() {
        if (verifyValidLoadingDialog != null) {
            verifyValidLoadingDialog.dismiss();
        }
    }

    private void showVerifySuccess() {

        //认证成功
        FaceVerifySuccessDialog successDialog = new FaceVerifySuccessDialog(getContext());
        successDialog.setOnFaceVerifyListener(new OnFaceVerifyListener() {
            @Override
            public void onVerifySuccess() {
                Intent intent = new Intent();
                intent.putExtra(BUNDLE_VERIFY_NEED_SHOW, false);
                intent.putExtra(BUNDLE_VERIFY_STATUS,true);
                ((Activity) getContext()).setResult(RESULT_CODE, intent);
                finish();
            }

            @Override
            public void onVerifyFail(String msg) {

            }
        });
        successDialog.show();
    }

    private void showVerifyFail() {
        //认证失败
        FaceVerifyFailDialog faceVerifyFailDialog = new FaceVerifyFailDialog(getContext());
        faceVerifyFailDialog.setOnFaceVerifyListener(new OnFaceVerifyListener() {
            @Override
            public void onVerifySuccess() {

            }

            @Override
            public void onVerifyFail(String msg) {
                Intent intent = new Intent();
                intent.putExtra(BUNDLE_VERIFY_NEED_SHOW, false);
                ((Activity) getContext()).setResult(RESULT_CODE, intent);
                finish();
            }
        });
        faceVerifyFailDialog.show();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    private void handlePermission() {
        if (!PermissionHelper.getInstance().checkPermissions(permission)) {
            PermissionHelper.getInstance().requestPermissions(getContext(), permission, permissionDesc, SQ_REQUEST_PERMISSION_CODE, new PermissionHelper.PermissionCallback() {
                @Override
                public void onRequestPermissionsResult(String[] permissions, int[] grantResults) {
                    boolean denied = false;
                    for (int grantResult : grantResults) {
                        if (grantResult != PackageManager.PERMISSION_GRANTED) {
                            denied = true;
                            break;
                        }
                    }
                    if (denied) {
                        showPermissionTipDialog();
                    } else {
                        startVerify();
                    }
                }
            });
        } else {
            startVerify();
        }
    }

    private CameraPermissionDialog permissionDialog;

    private void showPermissionTipDialog() {
        if (permissionDialog == null) {
            permissionDialog = new CameraPermissionDialog(getContext());
        }
        permissionDialog.show();
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Intent intent = new Intent();
            intent.putExtra(BUNDLE_VERIFY_NEED_SHOW, true);
            ((Activity) getContext()).setResult(RESULT_CODE, intent);
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }
}
