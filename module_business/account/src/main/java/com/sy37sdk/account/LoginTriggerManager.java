package com.sy37sdk.account;

import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tools.Logger;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.TimeTools;
import com.sqwan.common.util.task.Task;
import com.sy37sdk.account.db.LoginTrigger;
import com.sy37sdk.account.db.LoginTriggerDBManager;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONObject;

public class LoginTriggerManager {

    private Task queryTask = Task.create();

    private boolean isLoop = false;


    private static LoginTriggerManager loginTriggerManager;

    private LoginTriggerManager() {

    }

    public static LoginTriggerManager getInstance() {
        if (loginTriggerManager == null) {
            synchronized (LoginTriggerManager.class) {
                if (loginTriggerManager == null) {
                    loginTriggerManager = new LoginTriggerManager();
                }
            }
        }
        return loginTriggerManager;
    }

    //启动查询假登录信息
    public void startQueryLoginTrigger() {
        if (isLoop) {
            return;
        }
        Logger.info("启动查询假登录信息");
        queryTask.repeat(0, 5 * 60 * 1000, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                Logger.info("queryLoginTrigger 当前时间：" + TimeTools.stampToDate(System.currentTimeMillis() + ""));
                uploadLoginTriggerData();
                return null;
            }
        });
        isLoop = true;
    }

    public void stopQueryLoginTrigger() {
        Logger.info("停止假登录信息查询");
        if (isLoop) {
            queryTask.stop();
            isLoop = false;
        }
    }

    /**
     * 上报假登录的信息
     */
    public void uploadLoginTriggerData() {
        LogUtil.i("查询假登录信息计时到了 uploadLoginTriggerData");
        List<LoginTrigger> loginTriggers = LoginTriggerDBManager.getInstance().query();
        if (loginTriggers == null || loginTriggers.isEmpty()) {
            LogUtil.i("查询假登录信息计时到了 no loginTriggerData");
            stopQueryLoginTrigger();
            return;
        }
        LogUtil.i("查询假登录信息计时到了 开始上报");
        reportLogin(loginTriggers);
    }

    private void reportLogin(final List<LoginTrigger> loginTriggers) {
        if (loginTriggers == null || loginTriggers.isEmpty()) {
            return;
        }
        String userJson = "";
        try {
            JSONArray jsonArray = new JSONArray();
            for (LoginTrigger loginTrigger : loginTriggers) {
                if (loginTrigger != null) {
                    JSONObject obj = new JSONObject();
                    obj.put("uid", loginTrigger.getUid());
                    obj.put("uname", loginTrigger.getUname());
                    obj.put("token", loginTrigger.getToken());
                    obj.put("trigger_time", loginTrigger.getTriggerTime());
                    obj.put("login_type", loginTrigger.getLoginType());
                    jsonArray.put(obj);
                }
            }
            userJson = jsonArray.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        LogUtil.i("开始上报假登录：" + userJson);
        if (TextUtils.isEmpty(userJson)) {
            return;
        }
        SqRequest.of(UrlConstant.URL_LOGIN_REPORT)
            .signV3()
            .addParam("users", userJson)
            .addParamsTransformer(new CommonParamsV3())
            .post(new SimpleSqHttpCallback<Void>() {
                @Override
                public void onSuccess(Void unused) {
                    LoginTriggerDBManager.getInstance().delete(loginTriggers);
                }
            }, Void.class);
    }

}
