package com.sy37sdk.account.view.uifast;

import android.os.Bundle;

import com.sqwan.common.mvp.ILoadView;

import java.util.Map;

public interface ILoginDialog extends ILoadView {
    //关闭弹窗
    void closeAccountDialog();

    // 仅仅是关闭弹窗，无回调操作
    void dismissAccountDialog();

    //页面切换
    void onSwitch(int index, Bundle bundle);

    void loginSuccess(Map<String, String> data);

    void accountRegSuccess(Map<String, String> data);

    void goBack();

    boolean canGoBack();

    void printStackViews();

    //是否从闪验页面吊起
    boolean isFromAliFastLogin();

}
