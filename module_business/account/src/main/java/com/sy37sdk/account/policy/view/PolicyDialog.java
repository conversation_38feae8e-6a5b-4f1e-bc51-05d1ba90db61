package com.sy37sdk.account.policy.view;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.sqwan.common.mvp.BaseDialog;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;
import com.sy37sdk.account.policy.view.AuthBaseDialog.URLSpanNoUnderline;

/**
 * @author: gsp
 * @date: 2024/11/4
 * @desc: 协议弹窗，用户未勾选情况下点登录或者获取验证码弹出协议弹窗
 */
public class PolicyDialog extends BaseDialog {

    private final Context mContext;
    private final ConfirmCallback mConfirmCallback;

    public PolicyDialog(@NonNull Context context, ConfirmCallback confirmCallback) {
        super(context);
        mContext = context;
        mConfirmCallback = confirmCallback;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sysq_policy_dialog", "layout"));
        TextView tvText = findViewById(getIdByName("tv_tip", "id"));
        setLink(tvText,
            "为了更好地保障你的合法权益，请你先阅读并同意《用户协议》《隐私政策》，未注册的手机号将自动完成帐号注册");
        ImageView ivClose = findViewById(getIdByName("iv_close", "id"));
        ivClose.setOnClickListener(v -> dismiss());
        TextView tvConfirm = findViewById(getIdByName("tv_sure", "id"));
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mConfirmCallback != null) {
                    mConfirmCallback.onConfirm();
                }
                dismiss();
            }
        });
    }


    private void setLink(TextView textView, String txt) {
        SpannableString ss = new SpannableString(txt);
        String tag1 = "《用户协议》";
        String tag2 = "《隐私政策》";
        URLSpanNoUnderline line1 = new URLSpanNoUnderline(ActiveBeforeManager.getInstance().userProtocolInfo.userprotol,
            mContext);
        line1.setTextColor("#FE673A");
        URLSpanNoUnderline line2 = new URLSpanNoUnderline(
            ActiveBeforeManager.getInstance().userProtocolInfo.privacyprotol, mContext);
        line2.setTextColor("#FE673A");
        ss.setSpan(line1, txt.indexOf(tag1), txt.indexOf(tag1) + tag1.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ss.setSpan(line2, txt.indexOf(tag2), txt.indexOf(tag2) + tag2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        textView.setText(ss);
        textView.setMovementMethod(LinkMovementMethod.getInstance());
        textView.setHighlightColor(Color.TRANSPARENT);
    }

    public interface ConfirmCallback {
        void onConfirm();
    }


}
