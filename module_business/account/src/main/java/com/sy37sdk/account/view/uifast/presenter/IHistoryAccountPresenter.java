package com.sy37sdk.account.view.uifast.presenter;

import com.sqwan.common.mvp.IPresenter;
import com.sy37sdk.account.UserInfo;

public interface IHistoryAccountPresenter extends IPresenter {

    /**
     * 删除文件中的账户列表
     */
    void deleteUser(UserInfo userInfo);

    /**
     * 登录
     * @param userInfo
     */
    void login(UserInfo userInfo);

    /**
     * 找回账号/密码
     */
    void forgetPassword();

    void clauseClick(boolean isCheck);

    /**
     * 跳转注册协议页面
     */
    void toClausePage();


    /**
     * 跳转隐私协议页面
     */
    void toPolicy();
}
