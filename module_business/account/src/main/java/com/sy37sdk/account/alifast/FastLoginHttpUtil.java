package com.sy37sdk.account.alifast;

import android.content.Context;
import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tool.sqtools.detector.DevicesFingerprint;
import com.sqwan.base.L;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.util.AccountLoginType;
import org.json.JSONObject;

public class FastLoginHttpUtil {

    /**
     * 请求闪验配置
     */
    public static void requestFastConfig(SqHttpCallback<JSONObject> callback) {
        Context context = SQContextWrapper.getApplicationContext();
        SqRequest.of(UrlConstant.URL_REQUEST_FAST_CONFIG)
            .signV3()
            .addParam("package_name", context.getPackageName())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback);
    }

    /**
     * 验证闪验token
     */
    public static void verifyFastToken(final String token, final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_VERIFY_FAST_TOKEN)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("shan_yan_token", token)
            .addParam("display_name", AppUtils.getAppName(SQContextWrapper.getActivity()))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback, JSONObject.class);
    }

    /**
     * 快速登录
     */
    public static void fastLogin(final UserInfo userInfo, final SqHttpCallback<JSONObject> callBack) {
        String token = userInfo.getToken();
        String refreshToken = userInfo.getRefreshToken();
        String uname = userInfo.getUname();
        String loginType = TextUtils.isEmpty(userInfo.getLoginType()) ? "" : userInfo.getLoginType();
        String requestLoginType = "common";
        switch (loginType) {
            case AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE:
                requestLoginType = "phone";
                break;
            case AccountLoginType.LoginType.ACCOUNT_TYPE_WECHAT:
                requestLoginType = "wx";
                break;
        }
        SqRequest request = SqRequest.of(UrlConstant.URL_LOGIN_FAST)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParam("token", token)
            .addParam("refresh_token", refreshToken)
            .addParam("login_type", requestLoginType)
            .addParam("display_name", AppUtils.getAppName(SQContextWrapper.getActivity()))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV3());
        if (loginType.equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE)) {
            request.addParam("replacement", userInfo.getMobile());
        } else {
            request.addParam("uname", uname);
        }
        request.post(callBack);
    }
}
