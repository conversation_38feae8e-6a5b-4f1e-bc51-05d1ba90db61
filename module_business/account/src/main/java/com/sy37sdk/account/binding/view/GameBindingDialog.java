package com.sy37sdk.account.binding.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;

/**
 * @author: zls
 * @date: 2025/8/4
 */
public class GameBindingDialog extends BaseDialog {

    private final Context mContext;
    private final String mTips;
    private final MMOBindingDialogCallback mCallback;

    public GameBindingDialog(Context context, String content, MMOBindingDialogCallback callback) {
        super(context);
        mContext = context;
        mTips = content;
        mCallback = callback;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sysq_binding_dialog", "layout"));
        TextView tvText = findViewById(getIdByName("tv_tip", "id"));
        tvText.setText(mTips);
        TextView tvCancel = findViewById(getIdByName("tv_cancel", "id"));
        TextView tvConfirm = findViewById(getIdByName("tv_sure", "id"));
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mCallback != null) {
                    mCallback.onCancel();
                }
            }
        });
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCallback != null) {
                    mCallback.onConfirm();
                }
                dismiss();
            }
        });
    }

    public interface MMOBindingDialogCallback {
        void onConfirm();
        void onCancel();
    }
}
