package com.sy37sdk.account.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.sq.sdk.tool.database.BaseDatabaseHelper;


public class DataBaseHelper extends BaseDatabaseHelper {

    private static final String DB_NAME = "sq_sdk";

    private static final int DB_VERSION = 1;

    public DataBaseHelper(Context context) {
        super(context, DB_NAME, DB_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        try{
            db.beginTransaction();
            db.execSQL(LoginTriggerTable.SQL);
            db.setTransactionSuccessful();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            db.endTransaction();
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {

    }
}
