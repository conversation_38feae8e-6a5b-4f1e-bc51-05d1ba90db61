package com.sy37sdk.account.view.uifast.presenter;

import com.sqwan.common.mvp.IPresenter;

public interface IWechatV2Presenter extends IPresenter {

    void clauseClick(boolean isCheck);

    /**
     * 跳转注册协议页面
     */
    void toClausePage();

    void obtainVerifyCode();

    /**
     * 跳转隐私协议页面
     */
    void toPolicy();

    //微信登录
    void wechatLogin();

    //注册成功
    void wechatRegister();
}
