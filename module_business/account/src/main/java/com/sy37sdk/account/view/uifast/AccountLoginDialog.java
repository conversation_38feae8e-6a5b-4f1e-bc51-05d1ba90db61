package com.sy37sdk.account.view.uifast;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.support.v4.view.ViewPager;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sq.tools.Logger;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.NavigationUtils;
import com.sqwan.common.util.SDKError;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.widget.BaseViewPager;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AccountTools;
import com.sy37sdk.account.QrCodeInfo;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.age.AppropriateAge;
import com.sy37sdk.account.age.AppropriateAgeCacheHelper;
import com.sy37sdk.account.age.AppropriateAgeManager;
import com.sy37sdk.account.controller.UIVersionManager;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.util.AccountLoginType.LoginType;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.IRegSuccessDialog;
import com.sy37sdk.account.view.uifast.adapter.AccountPageAdapter;
import com.sy37sdk.account.view.uifast.presenter.AccountLoginPresenter;
import com.sy37sdk.account.view.uifast.presenter.HistoryAccountPresenter;
import com.sy37sdk.account.view.uifast.presenter.MultiAccountSelectPresenter;
import com.sy37sdk.account.view.uifast.presenter.PhonePresenter;
import com.sy37sdk.account.view.uifast.presenter.VerifyCodePresenter;
import com.sy37sdk.account.view.uifast.presenter.WechatLoginPresenter;
import com.sy37sdk.account.view.uifast.presenter.WechatLoginV2Presenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;
import com.sy37sdk.account.view.uifast.switcher.PageSwitcher;
import com.sy37sdk.account.view.uifast.view.AccountLoginView;
import com.sy37sdk.account.view.uifast.view.HistoryAccountView;
import com.sy37sdk.account.view.uifast.view.MultiSelectView;
import com.sy37sdk.account.view.uifast.view.PhoneView;
import com.sy37sdk.account.view.uifast.view.VerifyCodeView;
import com.sy37sdk.account.view.uifast.view.WechatLoginV2View;
import com.sy37sdk.account.view.uifast.view.WechatLoginView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Stack;


/**
 * 账号登录/注册弹窗
 */
public class AccountLoginDialog extends BaseDialog implements ILoginDialog {

    private Context mContext;

    private ILoginListener loginListener;

    private ImageView ivAppropriateAge;

    private RelativeLayout loginParentLayout;

    private BaseViewPager viewPager;

    private PageSwitcher mAccountPageSwitcher;

    private AccountPageAdapter mAccountPageAdapter;

    private List<BaseAccountPagerPresenter> mAccountPagePresenterList;

    private WechatLoginPresenter wechatLoginPresenter;

    private WechatLoginV2Presenter wechatLoginV2Presenter;
    private HistoryAccountPresenter historyAccountPresenter;
    private PhonePresenter phonePresenter;
    private VerifyCodePresenter verifyCodePresenter;
    private AccountLoginPresenter accountLoginPresenter;
    private MultiAccountSelectPresenter mMultiAccountSelectPresenter;
    private Stack<Integer> viewStacks;

    private int mFromIndex, mToIndex;
    private Bundle mBundle;

    //导航栏高度
    private int navigationBarHeight;

    //状态栏高度
    int statusBarHeight;

    //当前屏幕方向
    int orientation;

    //是否从阿里闪验登录吊起
    private boolean isFromAliFastLogin = false;

    private boolean canBack = false;

    //dialog是否销毁
    private boolean dismiss;

    public AccountLoginDialog(Context context, ILoginListener listener) {
        super(context);
        this.mContext = context;
        this.loginListener = listener;
    }

    public void setFromAliFastLogin(boolean isFromAliFastLogin) {
        this.isFromAliFastLogin = isFromAliFastLogin;
    }

    public void setCanBack(boolean isCanBack) {
        this.canBack = isCanBack;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_dialog_login"));
        initView();
    }


    /**
     * 初始化登录相关各个界面以及设置回调
     */
    private void initPageViewList() {
        mAccountPagePresenterList = new ArrayList<>();

        //手机号页面
        phonePresenter = new PhonePresenter(mContext, new PhoneView(mContext, this));
        phonePresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        phonePresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_PHONE_PAGE, phonePresenter);


        //账号登录、注册页
        accountLoginPresenter = new AccountLoginPresenter(mContext, new AccountLoginView(mContext, this));
        accountLoginPresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        accountLoginPresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_LOGIN_PAGE, accountLoginPresenter);


        //历史账号页面
        historyAccountPresenter = new HistoryAccountPresenter(mContext, new HistoryAccountView(mContext, this,loginListener));
        historyAccountPresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        historyAccountPresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_HISTORY_PAGE, historyAccountPresenter);

        //验证码页面
        verifyCodePresenter = new VerifyCodePresenter(mContext, new VerifyCodeView(mContext, this));
        verifyCodePresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        verifyCodePresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_VERIFY_CODE_PAGE, verifyCodePresenter);

        //微信登录页面
        wechatLoginPresenter = new WechatLoginPresenter(mContext, new WechatLoginView(mContext, this, loginListener));
        wechatLoginPresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        wechatLoginPresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_LOGIN_WECHAT, wechatLoginPresenter);


        //微信登录V2页面
        wechatLoginV2Presenter = new WechatLoginV2Presenter(mContext, new WechatLoginV2View(mContext, this));
        wechatLoginV2Presenter.setAccountPageSwitcher(mAccountPageSwitcher);
        wechatLoginV2Presenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_LOGIN_WECHAT_V2, wechatLoginV2Presenter);

        //多账号选择确认页
        mMultiAccountSelectPresenter = new MultiAccountSelectPresenter(mContext, new MultiSelectView(mContext, this));
        mMultiAccountSelectPresenter.setAccountPageSwitcher(mAccountPageSwitcher);
        mMultiAccountSelectPresenter.setLoginListener(loginListener);
        mAccountPagePresenterList.add(AccountPage.ACCOUNT_MULTI_SELECT, mMultiAccountSelectPresenter);
    }

    private void initView() {
        viewStacks = new Stack<>();
        viewPager = findViewById(getIdByName("account_dialog_content", "id"));
        viewPager.setPagingEnabled(false);
        mAccountPageSwitcher = new PageSwitcher(viewPager);
        initPageViewList();
        viewPager.setOffscreenPageLimit(mAccountPagePresenterList.size());
        mAccountPageAdapter = new AccountPageAdapter(mAccountPagePresenterList);
        mAccountPageSwitcher.setPageScrollListener(new PageSwitcher.IPageScrollListener() {
            @Override
            public void scroll(int fromIndex, int toIndex, Bundle bundle) {
                mFromIndex = fromIndex;
                mToIndex = toIndex;
                mBundle = bundle;
                LogUtil.i("scroll 页面切换 from " + mFromIndex + " to " + mToIndex);
            }
        });
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {
                LogUtil.i("push");
                if (viewStacks.isEmpty() || viewStacks.peek() != mToIndex) {
                    //如果从历史账号列表切换的，判断是否都删除了本地账号，若都删除了则直接pop出栈
                    if (!viewStacks.isEmpty() && viewStacks.peek() == AccountPage.ACCOUNT_HISTORY_PAGE) {
                        List<UserInfo> allUserInfo = AccountUtil.getAllUserInfo(getContext());
                        if ((allUserInfo == null || allUserInfo.isEmpty())) {
                            LogUtil.i("账号列表空，pop历史账号列表页");
                            viewStacks.pop();
                        }
                    }
                    viewStacks.push(mToIndex);
                    LogUtil.i("push mToIndex" + mToIndex);
                }
                BaseAccountPagerPresenter accountPagerPresenter = mAccountPagePresenterList.get(mToIndex);
                accountPagerPresenter.onSwitched(mFromIndex, mToIndex, mBundle);
            }

            @Override
            public void onPageSelected(int i) {
                LogUtil.i("onPageSelected " + i);
            }

            @Override
            public void onPageScrollStateChanged(int i) {
                LogUtil.i("onPageScrollStateChanged");
            }
        });
        viewPager.setAdapter(mAccountPageAdapter);
        List<UserInfo> accountList = AccountTools.getAccountFromFile(mContext);
        if (EntranceManager.getInstance().isWxLoginEntrance() && !isFromAliFastLogin()) {
            //如果是微信小游戏转端的包
            //则首次调用登录或者没有历史账号列表时默认显示微信登录界面
            //上次登录是微信登录，默认走微信登录
            UserInfo userInfo = AccountCache.getUserInfo(mContext);
            boolean isLastWxLoginFail = false;
            if (userInfo != null) {
                isLastWxLoginFail = TextUtils.equals(userInfo.getLoginType(), LoginType.ACCOUNT_TYPE_WECHAT) && UserInfo.wechatLoginFail;
            }
            if (DeviceUtils.isFirstInvokeLogin(mContext) || accountList == null || accountList.isEmpty() || isLastWxLoginFail || AccountUtil.checkToWeiChatLogin()) {
                if (isLastWxLoginFail && !AccountUtil.checkToWeiChatLogin()) {
                    //需要返回到历史账号
                    Logger.info("需要返回到历史账号");
                    viewStacks.push(AccountPage.ACCOUNT_HISTORY_PAGE);
                }
                AccountUtil.setToWeiChatLogin(false);
                //如果是闪验失败跳转过来的，可能是已经是从微信登录页跳过来了，那就跳转到手机号登录页
                if (AccountUtil.checkNotSupportFast()){
                    Logger.info("从闪验失败跳转来，去手机号登录页");
                    //重置特殊逻辑下状态
                    AccountUtil.setNotSupportFast(false);
                    mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE);
                }else if (EntranceManager.getInstance().getWxLoginUIVersion() == EntranceManager.WX_LOGIN_UI_VERSION_V2) {
                    Logger.info("展示微信登录V2");
                    mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_LOGIN_WECHAT_V2);
                } else {
                    Logger.info("展示微信登录V1");
                    mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_LOGIN_WECHAT);
                }
            } else {
                //其他场景显示历史账号列表
                Logger.info("其他场景展示历史账号");
                mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_HISTORY_PAGE);
            }
        } else {
            //正常包，
            // 无历史账号则显示手机登录页面
            if (accountList == null || accountList.isEmpty()) {
                Logger.info("无历史账号则显示手机登录页面");
                mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE);
                canBack = false;
            }else if (isFromAliFastLogin()){
                //有历史账号，但是从闪验跳转其他登录，跳转到手机号登录
                Logger.info("有历史账号，但是从闪验跳转其他登录，跳转到手机号登录");
                mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE);
            }else if (AccountUtil.checkNotSupportFast()){
                //有历史账号，但是从其他登录方式跳到闪验，但闪验不支持的版本跳转其他登录，跳转到手机号登录
                Logger.info("有历史账号，但是从其他登录方式跳到闪验，但闪验不支持的版本跳转其他登录，跳转到手机号登录");
                //重置特殊逻辑下状态
                AccountUtil.setNotSupportFast(false);
                mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE);
            }else {
                //有历史账号则显示历史账号页
                Logger.info("有历史账号则显示历史账号页");
                mAccountPageSwitcher.onSwitch(AccountPage.ACCOUNT_HISTORY_PAGE);
            }
        }
        UserInfo.wechatLoginFail = false;
        navigationBarHeight = NavigationUtils.getNavigationBarHeight(mContext);
        statusBarHeight = StatusBarUtil.getStatusBarHeight(mContext);
        LogUtil.i("状态栏高度=" + statusBarHeight + " 导航栏高度=" + navigationBarHeight);
        Configuration configuration = mContext.getResources().getConfiguration();
        orientation = configuration.orientation;
        setAttachViewLocate();
        loginParentLayout = findViewById(getIdByName("login_parent", "id"));
        loginParentLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                StatusBarUtil.hideSystemKeyBoard(mContext, loginParentLayout);
            }
        });
        initAppropriateAgeView();
    }


    /**
     * 设置客服按钮、版本的位置
     */
    private void setAttachViewLocate() {
        final View attachView = findViewById(getIdByName("attach_view", "id"));
        RelativeLayout.LayoutParams attachViewLp = (RelativeLayout.LayoutParams) attachView.getLayoutParams();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attachViewLp.setMargins(0, 0, navigationBarHeight + DisplayUtil.dip2px(mContext, 10f), DisplayUtil.dip2px(mContext, 6f));
        } else {
            attachViewLp.setMargins(0, 0, DisplayUtil.dip2px(mContext, 12f), navigationBarHeight + DisplayUtil.dip2px(mContext, 10f));
        }
    }

    /**
     * 设置适龄提醒图标
     */
    private void initAppropriateAgeView() {
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(mContext);
        if (appropriateAge == null) {
            Logger.info("登录页，适龄图标无缓存，发起请求");
            //没有缓存则重新发起请求，走请求回调
            AppropriateAgeManager.getInstance().refreshConfig(new SQResultListener() {
                @Override
                public void onSuccess(Bundle bundle) {
                    showAgeAppropriate();
                }

                @Override
                public void onFailture(int code, String msg) {

                }
            });
            return;
        }
        Logger.info("登录页，适龄图标有缓存，直接显示");
        showAgeAppropriate();

    }


    @Override
    public void dismiss() {
        super.dismiss();
        dismiss = true;
    }

    private void showAgeAppropriate() {
        if (dismiss) {
            return;
        }
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(mContext);
        if (appropriateAge == null || !appropriateAge.isStatus()) {
            return;
        }
        List<String> timing = appropriateAge.getTiming();
        if (timing == null || !timing.contains(AppropriateAge.TIMING_LOGIN)) {
            return;
        }
        ivAppropriateAge = new ImageView(mContext);
        int screenWidth = DisplayUtil.getScreenWidth(mContext);
        int screenHeight = DisplayUtil.getScreenHeight(mContext);
        //icon宽去屏幕宽高中最小值得十分之一
        int icWidth = Math.min(screenWidth, screenHeight) / 10;
        //icon尺寸比例为48*62
        int icHeight = 62 * icWidth / 48;
        int horizontalMargin = DisplayUtil.dip2px(mContext, 20f);
        int verticalMargin = DisplayUtil.dip2px(mContext, 20f);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(icWidth, icHeight);
        int location = appropriateAge.getLocation();
        switch (location) {
            //右上
            case AppropriateAge.LOCATE_RIGHT_TOP:
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    layoutParams.setMargins(0, verticalMargin, navigationBarHeight + DisplayUtil.dip2px(mContext, 10), 0);
                } else {
                    layoutParams.setMargins(0, verticalMargin, horizontalMargin, 0);
                }
                break;
            //左下
            case AppropriateAge.LOCATE_LEFT_BOTTOM:
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    layoutParams.setMargins(statusBarHeight + DisplayUtil.dip2px(mContext, 10), 0, 0, statusBarHeight + DisplayUtil.dip2px(mContext, 10));
                } else {
                    layoutParams.setMargins(horizontalMargin, 0, 0, navigationBarHeight + DisplayUtil.dip2px(mContext, 10));
                }
                break;
            //右下
            case AppropriateAge.LOCATE_RIGHT_BOTTOM:
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    layoutParams.setMargins(0, 0, navigationBarHeight + DisplayUtil.dip2px(mContext, 18), verticalMargin + DisplayUtil.dip2px(mContext, 55f));
                } else {
                    layoutParams.setMargins(0, 0, horizontalMargin, navigationBarHeight + DisplayUtil.dip2px(mContext, 80));
                }
                break;
            //左上
            default:
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    layoutParams.setMargins(horizontalMargin, verticalMargin, 0, 0);
                } else {
                    layoutParams.setMargins(horizontalMargin, statusBarHeight + DisplayUtil.dip2px(mContext, 10), 0, 0);
                }
                break;
        }
        ivAppropriateAge.setScaleType(ImageView.ScaleType.CENTER_CROP);
        ivAppropriateAge.setLayoutParams(layoutParams);
        loginParentLayout.addView(ivAppropriateAge);
        AsyncImageLoader loader = new AsyncImageLoader(getContext());
        loader.loadDrawable(appropriateAge.getIcon(), ivAppropriateAge, new AsyncImageLoader.ImageCallback() {
            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                imageView.setImageBitmap(imageDrawable);
                LogUtil.i("适龄图片加载成功");
            }
        });
        ivAppropriateAge.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                AppropriateAgeManager.getInstance().showAppropriateAgeDialog(mContext);
            }
        });
    }
    @Override
    public void closeAccountDialog() {
        dismiss();
        if (accountDialogCloseListener != null) {
            accountDialogCloseListener.onClose();
        }
        if (loginListener != null) {
            loginListener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, SDKError.ACCOUNT_LOGIN_CANCEL.message);
        }
    }

    @Override
    public void dismissAccountDialog() {
        dismiss();
        if (accountDialogCloseListener != null) {
            accountDialogCloseListener.onClose();
        }
    }


    @Override
    public void onSwitch(int index, Bundle bundle) {
        mAccountPageSwitcher.onSwitch(index, bundle);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        if (accountDialogCloseListener != null) {
            accountDialogCloseListener.onClose();
        }
        if (loginListener != null) {
            loginListener.onSuccess(data);
        }
        dismiss();
    }

    @Override
    public void accountRegSuccess(final Map<String, String> data) {
        if (accountDialogCloseListener != null) {
            accountDialogCloseListener.onClose();
        }
        String uname = AccountCache.getUsername(getContext());
        String pwd = AccountCache.getPassword(getContext());
        if (uname.equals(AccountCache.getAutoName(getContext())) && AccountCache.getAutoState(getContext())) {
            QrCodeInfo qrCodeInfo = AccountLogic.getInstance(getContext()).getQrCodeInfo();
            UIVersionManager.getInstance(getContext()).getLoginController()
                    .showRegSuccessDialog(uname, pwd, qrCodeInfo, new IRegSuccessDialog.EnterGameListener() {
                        @Override
                        public void enterGame() {
                            if (loginListener != null) {
                                loginListener.onSuccess(data);
                            }
                        }

                        @Override
                        public void cancel() {
                            if (loginListener != null) {
                                loginListener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, SDKError.ACCOUNT_LOGIN_CANCEL.message);
                            }
                        }
                    });
            AccountCache.setAutoName(mContext, "");
            AccountCache.setAutoPassword(mContext, "");
        } else {
            if (loginListener != null) {
                loginListener.onSuccess(data);
            }
        }
        dismiss();
    }

    @Override
    public void goBack() {
        LogUtil.i("goBack");
        if (canGoBack()) {
            if (viewStacks.size() > 1) {
                Integer pop = viewStacks.pop();
                LogUtil.i(pop + "出栈");
                Integer peek = viewStacks.peek();
                LogUtil.i("跳转至  " + peek);
                onSwitch(peek, null);
            } else {
                if (accountDialogCloseListener != null) {
                    accountDialogCloseListener.onDismiss();
                }
                dismiss();
            }
        }
    }

    @Override
    public boolean canGoBack() {
        printStackViews();
        return viewStacks.size() > 1 || isFromAliFastLogin || canBack;
    }


    @Override
    public void printStackViews() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < viewStacks.size(); i++) {
            sb.append(viewStacks.get(i)).append(" -> ");
        }
        LogUtil.i("view堆栈：" + sb.toString());
    }

    @Override
    public boolean isFromAliFastLogin() {
        return isFromAliFastLogin;
    }

    @Override
    public void onBackPressed() {
        if (canGoBack()) {
            BaseAccountPagerPresenter accountPagerPresenter = mAccountPagePresenterList.get(mToIndex);
            accountPagerPresenter.onBackPressed();
        } else {
            if (accountDialogCloseListener != null) {
                accountDialogCloseListener.onClose();
            }
            if (loginListener != null) {
                loginListener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, SDKError.ACCOUNT_LOGIN_CANCEL.message);
            }
            super.onBackPressed();
        }
    }

    private OnAccountDialogCloseListener accountDialogCloseListener;

    public interface OnAccountDialogCloseListener {
        void onClose();

        void onDismiss();
    }

    public void setOnAccountDialogCloseListener(OnAccountDialogCloseListener accountDialogCloseListener) {
        this.accountDialogCloseListener = accountDialogCloseListener;
    }
}
