package com.sy37sdk.account.auth;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/12/12
 */
public class PopConfig {

    /**
     * 是否停止上报（1是 0否）
     */
    private int needStop;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 弹窗类型（0不弹窗 1非强制弹窗 2强制弹窗）
     */
    private int code;

    private int remainingTime;

    //当前时间戳
    private long timestamp;

    public int getRemainingTime() {
        return remainingTime;
    }

    public void setRemainingTime(int remainingTime) {
        this.remainingTime = remainingTime;
    }

    public boolean getNeedStop() {
        return needStop == 1;
    }

    public void setNeedStop(int needStop) {
        this.needStop = needStop;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isShow() {
        return code == 1 || code == 2;
    }

    public boolean isFocus() {
        return code == 2;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public static PopConfig parseFromJson(JSONObject authJson) {
        PopConfig config = new PopConfig();
        try {
            config.setCode(authJson.optInt("code"));
            config.setUrl(authJson.optString("url"));
            config.setNeedStop(authJson.optInt("needStop"));
            config.setRemainingTime(authJson.optInt("remainingTime"));
            config.setRemainingTime(authJson.optInt("remainingTime"));
            config.setTimestamp(authJson.optLong("timestamp"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return config;
    }
}
