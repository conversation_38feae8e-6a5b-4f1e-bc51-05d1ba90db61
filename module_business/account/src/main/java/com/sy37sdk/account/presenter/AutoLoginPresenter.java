package com.sy37sdk.account.presenter;

import android.content.Context;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.RiskInterceptor;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.user.UserInfoManager;
import com.sqwan.common.util.JsonMap;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AccountLogic.AccountListener;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.util.AccountLoginType.LoginType;
import com.sy37sdk.account.view.IAutoLoginDialog;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/2/20
 */
public class AutoLoginPresenter extends BaseAutoLoginPresenter {

    private String name, pwd;
    private boolean isMobile;

    public AutoLoginPresenter(Context context, IAutoLoginDialog view) {
        super(context, view);
    }

    @Override
    public void autoLoginFinish(Map<String, String> data) {
        super.autoLoginFinish(data);
        mView.autoLoginSuccess(data);
    }

    public void setLoginInfo(String name, String pwd, boolean isMobile) {
        this.name = name;
        this.pwd = pwd;
        this.isMobile = isMobile;
    }

    @Override
    public void autoLogin() {
        if (!TextUtils.isEmpty(name)) {
            doLogin();
        } else {
            SQLog.e(TAG + "用户名为空, 无法自动登录");
        }
    }

    private void doLogin() {
        UserInfo userInfo = AccountCache.getUserInfo(context);
        if (userInfo != null) {
            checkAndReport(userInfo);
            if(userInfo.isPhoneLoginType() && !TextUtils.isEmpty(userInfo.getTicket())){
                // 先判断是否ticket登录
                AccountLogic.getInstance(context).phoneLoginTicket(userInfo.getTicket(), userInfo.getMobile(),
                    new AccountListener() {
                        @Override
                        public void onSuccess(Map<String, String> account) {
                            loginSuccess(account);
                        }

                        @Override
                        public void onFailure(int code, String msg) {
                            SQLog.w(TAG + "ticket登录失败");
                            ToastUtil.showToast(context, msg);
                            loginAccount();
                        }
                    });
            }else if (!TextUtils.isEmpty(userInfo.getToken()) && !TextUtils.isEmpty(userInfo.getRefreshToken())) {
                SQLog.d(TAG + "自动登录");
                AccountLogic.getInstance(context).fastLogin(userInfo, new AccountLogic.AccountListener() {
                    @Override
                    public void onSuccess(Map<String, String> account) {
                        loginSuccess(account);
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        if (code == RiskInterceptor.RISK_WEB_STATE_CODE) {
                            // 如果快速登录失败了，并且是因为统一弹窗导致的
                            // 这次就不再触发账号密码登录，避免再次触发统一弹窗
                            // 这里就直接回调登录失败
                            String loginType;
                            String loginWay = LoginTractionManager.login_way_auto;
                            if (!isMobile) {
                                loginType = LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM;
                                LoginTractionManager.trackInvoke(loginType, loginWay);
                                LoginTractionManager.trackFail(loginType, loginWay, code + "", msg);
                                loginFail(code, msg);
                            } else {
                                loginType = LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM;
                                if (TextUtils.isEmpty(pwd)) {
                                    LoginTractionManager.trackInvoke(loginType, loginWay);
                                    LoginTractionManager.trackFail(loginType, loginWay, "-1", "手机号自动登录没有密码");
                                    //如果是手机号登录的，但没有密码，则吊起登录弹窗
                                    loginFail(-1, "");
                                } else {
                                    LoginTractionManager.trackInvoke(loginType, loginWay);
                                    LoginTractionManager.trackFail(loginType, loginWay, code + "", msg);
                                    loginFail(code, msg);
                                }
                            }
                            return;
                        }
                        //快速登录失败尝试账号密码登录
                        loginAccount();
                    }
                });
            } else {
                SQLog.w(TAG + "无效的用户信息, 无法自动登录");
                loginAccount();
            }
        } else {
            SQLog.w(TAG + "无用户信息, 无法自动登录");
            loginAccount();
        }
    }


    private void loginAccount() {
        UserInfo userInfo = AccountCache.getUserInfo(context);
        if (userInfo != null && TextUtils.equals(userInfo.getLoginType(), LoginType.ACCOUNT_TYPE_WECHAT)) {
            UserInfo.wechatLoginFail = true;
            loginFail(-1, "");
        } else if (!isMobile) {
            AccountLogic.getInstance(context).accountLogin(name, pwd, false, new AccountLogic.AccountListener() {
                @Override
                public void onSuccess(final Map<String, String> data) {
                    loginSuccess(data);
                }

                @Override
                public void onFailure(int code, String msg) {
                    LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_auto);
                    LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_auto, code + "", msg);
                    loginFail(code, msg);
                }
            });
        } else {
            if (TextUtils.isEmpty(pwd)) {
                LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_auto);
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_auto, "-1", "手机号自动登录没有密码");
                //如果是手机号登录的，但没有密码，则吊起登录弹窗
                loginFail(-1, "");
            } else {
                AccountLogic.getInstance(context).phoneLoginPwd(name, pwd, new AccountLogic.AccountListener() {
                    @Override
                    public void onSuccess(final Map<String, String> data) {
                        loginSuccess(data);
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_auto);
                        LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_auto, code + "", msg);
                        loginFail(code, msg);
                    }
                });
            }
        }

    }

    /**
     * 埋点检查
     */
    private void checkAndReport(@NonNull UserInfo oldUser) {
        // TODO: 2024/10/9 后续版本移除
        boolean report;
        com.sqwan.common.user.UserInfo newUser = UserInfoManager.getInstance().getLastLoginUser();
        JsonMap map = new JsonMap();
        map.put("desc", "last user");
        map.put("old", oldUser.toString());
        if (newUser == null || !Objects.equals(oldUser.getToken(), newUser.getToken())
            || !Objects.equals(oldUser.getUid(), newUser.getUid())
            || !Objects.equals(oldUser.getUname(), newUser.getUname())
            || !Objects.equals(oldUser.getLoginType(), String.valueOf(newUser.type.code))) {
            report = true;
        } else {
            String newPwd = com.sqwan.common.user.UserInfo.getPwd(newUser);
            String oldPwd = oldUser.getUpwd();
            oldPwd = oldPwd == null ? "" : oldPwd;

            String newRT = newUser.refreshToken;
            newRT = newRT == null ? "" : newRT;

            String oldRt = oldUser.getRefreshToken();
            oldRt = oldRt == null ? "" : oldRt;

            report = !Objects.equals(newPwd, oldPwd) || !Objects.equals(newRT, oldRt);
        }

        if (!report) {
            return;
        }
        if (newUser != null) {
            map.put("new_user", newUser.toString());
        }
        BuglessAction.reportCatchException(new IllegalArgumentException(), "上次登录用户参数重构前后不一致",
            map.toString(), BuglessAction.COMMON_ERROR);
    }
}
