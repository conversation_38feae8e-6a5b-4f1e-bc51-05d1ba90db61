package com.sy37sdk.account.controller;


import android.content.Context;

import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.bean.UIVersion;

/**
 * <AUTHOR>
 * @date 2020/2/21
 *
 */
public class UIVersionManager {


    private static UIVersionManager instance;
    private Context mContext;
    private UIVersion uiVersion = new UIVersion();
    private AbstractLoginController mLoginController;

    private UIVersionManager(Context context){
        this.mContext = context;
    }

    public static UIVersionManager getInstance(Context context) {
        if(instance == null) {
            synchronized (UIVersionManager.class) {
                if(instance == null) {
                    instance = new UIVersionManager(context);
                }
            }
        }
        return instance;
    }

    public void initVersion(String data) {
        uiVersion = UIVersion.fromJson(data);
        LogUtil.e("init ui version " + uiVersion.toString());
        mLoginController = new FastVerifyController(mContext);
    }

    public AbstractLoginController getLoginController() {
        if (mLoginController == null) {
            // 当外部没有初始化, 使用版本2的控制器
            mLoginController = new FastVerifyController(mContext);
        }
        return mLoginController;
    }



    public boolean oneKeyRegOpen() {
        return uiVersion.getFregister() != UIVersion.FREGISTER_CLOSE;
    }

}
