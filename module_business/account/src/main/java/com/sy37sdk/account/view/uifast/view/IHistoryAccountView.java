package com.sy37sdk.account.view.uifast.view;

import android.os.Bundle;

import com.sqwan.common.mvp.ILoadView;
import com.sy37sdk.account.UserInfo;

import java.util.Map;

public interface IHistoryAccountView extends ILoadView {

    void setAccount(UserInfo userInfo);


    void onSwitch(int index);

    /**
     * 修改登录按钮的可点击状态
     */
    void enableLoginBtn(boolean enable);

    void loginSuccess(Map<String, String> data);

    String getPhone();

    void startView(int page,Bundle bundle);

    void checkedClause();
}
