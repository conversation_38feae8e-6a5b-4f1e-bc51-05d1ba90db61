package com.sy37sdk.account.uagree;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-03-17
 */
public class UAgreeCacheHelper {

    private static final String SP_KEY_HISTORY_VERSION = "sp_key_history_version";

    /**
     * 用户协议
     */
    private static final String SP_KEY_URL_PROTOCOL = "sp_key_url_protocol";

    /**
     * 隐私协议
     */
    private static final String SP_KEY_URL_POLICY = "sp_key_url_policy";

    /**
     * 中转页
     */
    private static final String SP_KEY_URL_INTERIM = "sp_key_url_interim";


    public static int getVersion(Context context, String uname) {
        Map<String, Integer> versions = getVersionFromSP(context);
        if (versions.containsKey(uname.toLowerCase())) {
            return versions.get(uname.toLowerCase());
        }
        return 0;
    }


    public static void setVersion(Context context, String uname, int newVersion) {
        Map<String, Integer> versions = getVersionFromSP(context);
        versions.put(uname.toLowerCase(), newVersion);
        saveVersionToSP(context, versions);
    }

    private static Map<String, Integer> getVersionFromSP(Context context) {
        Map<String, Integer> versions = new HashMap<>();
        //兼容老版本获取为int类型的
        try {
            LogUtil.i("尝试读取用户协议版本string类型");
            String versionJson = SpUtils.get(context).getString(SP_KEY_HISTORY_VERSION);
            LogUtil.i("尝试读取用户协议版本string类型成功，"+versionJson);
            if (!TextUtils.isEmpty(versionJson)) {
                try {
                    JSONObject jsonObject = new JSONObject(versionJson);
                    Iterator<String> iterator = jsonObject.keys();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        int value = jsonObject.getInt(key);
                        versions.put(key, value);
                    }
                } catch (JSONException e) {
                    LogUtil.e("读取用户协议历史版本出错！");
                    e.printStackTrace();
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            LogUtil.i("读取用户协议版本string类型出错，尝试读取int类型");
            try {
                LogUtil.i("尝试读取用户协议版本int类型");
                int version = SpUtils.get(context).getInt(SP_KEY_HISTORY_VERSION);
                LogUtil.i("读取用户协议版本int类型成功 " + version);
                versions.put("permission", version);
            } catch (Exception e1) {
                e1.printStackTrace();
                LogUtil.i("读取用户协议版本int类型也失败 " );
            }
        }
        return versions;
    }

    private static void saveVersionToSP(Context context, Map<String, Integer> versions) {
        JSONObject jsonObject = new JSONObject(versions);
        LogUtil.i("version json --> " + jsonObject.toString());
        SpUtils.get(context).put(SP_KEY_HISTORY_VERSION, jsonObject.toString());
    }

    /**
     * 用户协议链接
     */
    public static void setUrlProtocol(Context context, String url) {
        SpUtils.get(context).put(SP_KEY_URL_PROTOCOL, url);
    }


    public static String getUrlProtocol(Context context) {
        return SpUtils.get(context).getString(SP_KEY_URL_PROTOCOL, "");
    }

    /**
     * 隐私协议链接
     */
    public static void setUrlPolicy(Context context, String url) {
        SpUtils.get(context).put(SP_KEY_URL_POLICY, url);
    }


    public static String getUrlPolicy(Context context) {
        return SpUtils.get(context).getString(SP_KEY_URL_POLICY, "");
    }

    /**
     * 中转页面链接
     */
    public static void setUrlInterim(Context context, String url) {
        SpUtils.get(context).put(SP_KEY_URL_INTERIM, url);
    }


    public static String getUrlInterim(Context context) {
        return SpUtils.get(context).getString(SP_KEY_URL_INTERIM, "");
    }


}
