package com.sy37sdk.account.alifast;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.mobile.auth.gatewayauth.AuthUIControlClickListener;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.ResultCode;
import com.mobile.auth.gatewayauth.TokenResultListener;
import com.mobile.auth.gatewayauth.model.TokenRet;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.msdk.api.SQResultListener;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.alifast.config.BaseUIConfig;
import com.sy37sdk.account.alifast.config.Constant;
import com.sy37sdk.account.uagree.UAgreeManager;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import org.json.JSONObject;


public class FastLoginManager {

    private static final String TAG = "【Login Fast】";

    private static FastLoginManager instance;

    private Context mContext;

    private Activity activity;

    private PhoneNumberAuthHelper mPhoneNumberAuthHelper;

    private TokenResultListener mTokenResultListener;

    private BaseUIConfig mUIConfig;

    private String accessKey = "";

    private FastLoginListener fastLoginListener;

    private LoadingDialog loadingDialog;

    //是否请求过accessKey了
    private boolean requestedAccessKey;

    private FastLoginManager(Context context) {
        this.mContext = context;
    }

    public static FastLoginManager getInstance(Context context) {
        if (instance == null) {
            synchronized (FastLoginManager.class) {
                if (instance == null) {
                    instance = new FastLoginManager(context);
                }
            }
        }
        return instance;
    }

    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    /**
     * 执行闪验操作
     */
    public void doFastVerifyLogin(final FastLoginListener listener) {
        this.fastLoginListener = listener;
        showLoading();
        if (requestedAccessKey()) {
            oneKeyLogin();
            return;
        }
        SQLog.d(TAG + "请求闪验配置");
        FastLoginHttpUtil.requestFastConfig(new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.w(TAG + "请求闪验配置失败, " + state + ", " + msg);
                disableFastLogin(FastLoginConstants.Code.FAILURE_REQUEST_CONFIG,FastLoginConstants.MESSAGE.FAILURE_REQUEST_CONFIG);
            }

            @Override
            public void onSuccess(JSONObject dataObj) {
                SQLog.i(TAG + "请求闪验配置成功");
                try {
                    accessKey = decodeAccessKey(dataObj.optString("access_key"));
                    if (!TextUtils.isEmpty(accessKey)) {
                        requestedAccessKey = true;
                        oneKeyLogin();
                    } else {
                        SQLog.w(TAG + "获取闪验key失败, " + dataObj);
                        disableFastLogin(FastLoginConstants.Code.FAILURE_NOT_CONFIG, FastLoginConstants.MESSAGE.FAILURE_NOT_CONFIG);
                    }
                } catch (Exception e) {
                    SQLog.e(TAG + "解析闪验配置出错, "+ dataObj);
                    disableFastLogin(FastLoginConstants.Code.FAILURE_PARSE_CONFIG, FastLoginConstants.MESSAGE.FAILURE_PARSE_CONFIG);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.e(TAG + "请求闪验配置异常, " + code + ", " + errorMsg);
                disableFastLogin(FastLoginConstants.Code.FAILURE_CONFIG_ERROR, FastLoginConstants.MESSAGE.FAILURE_CONFIG_ERROR);
            }
        });
    }

    public void initFastAccessKey(final SQResultListener listener) {
        showLoading();
        if (requestedAccessKey()) {
            hideLoading();
            listener.onSuccess(null);
            return;
        }
        SQLog.d(TAG + "请求闪验配置");
        FastLoginHttpUtil.requestFastConfig(new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.w(TAG + "请求闪验配置失败, " + state + ", " + msg);
                listener.onFailture(state, "请求闪验配置失败, " + state + ", " + msg);
                hideLoading();
            }

            @Override
            public void onSuccess(JSONObject dataObj) {
                SQLog.i(TAG + "请求闪验配置成功");
                try {
                    accessKey = decodeAccessKey(dataObj.optString("access_key"));
                    if (!TextUtils.isEmpty(accessKey)) {
                        requestedAccessKey = true;
                        listener.onSuccess(null);
                    } else {
                        SQLog.w(TAG + "获取闪验key失败, " + dataObj);
                        listener.onFailture(0,"获取闪验key失败, " + dataObj);
                    }
                } catch (Exception e) {
                    SQLog.e(TAG + "解析闪验配置出错, "+ dataObj);
                    listener.onFailture(0,"解析闪验配置出错, "+ dataObj);
                    hideLoading();
                }
                hideLoading();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.e(TAG + "请求闪验配置异常, " + code + ", " + errorMsg);
                listener.onFailture(code,"请求闪验配置异常, " + code + ", " + errorMsg);
                hideLoading();
            }
        });
    }

    /**
     * 闪验不可用
     */
    private void disableFastLogin(String code, String msg) {
        SQLog.w(TAG + "禁用闪验登录, code=" + code + ", msg=" + msg);
        Bundle bundle = new Bundle();
        bundle.putString(FastLoginConstants.BundleKey.CODE, code);
        bundle.putString(FastLoginConstants.BundleKey.MESSAGE, msg);
        disableFastLogin(bundle);
    }

    /**
     * 闪验不可用
     */
    private void disableFastLogin(Bundle bundle) {
        if (fastLoginListener != null) {
            SQLog.w(TAG + "回调闪验失败");
            fastLoginListener.onFastLoginFail(bundle);
        }
        hideLoading();
    }

    /**
     * 拿到服务端传的闪验accessKey后解密
     *
     * @param encodeAccessKey
     * @return
     */
    private String decodeAccessKey(String encodeAccessKey) {
        String decodeKey = "";
        String appKey = ConfigManager.getInstance(mContext).getAppKey();
        if (!TextUtils.isEmpty(appKey)) {
            int length = appKey.length();
            //不足16位则补齐0
            if (length < 16) {
                StringBuilder sb = new StringBuilder(appKey);
                for (int i = 0; i < 16 - length; i++) {
                    sb.append("0");
                }
                decodeKey = sb.toString();
            } else {
                //满16位则截取前16位
                decodeKey = appKey.substring(0, 16);
            }
        }
        return AESUtil.decryptString(encodeAccessKey, decodeKey);
    }

    /**
     * 一键登录
     */
    public void oneKeyLogin() {
        try {
            fastInit();
            fastLogin();
        } catch (Exception e) {
            e.printStackTrace();
            disableFastLogin(FastLoginConstants.Code.FAILURE_NOT_SUPPORT, FastLoginConstants.MESSAGE.FAILURE_NOT_SUPPORT);
        }
    }


    public boolean requestedAccessKey() {
        return requestedAccessKey;
    }

    /**
     * 初始化
     */
    private void fastInit() {
        SQLog.d(TAG + "初始化闪验");
        mTokenResultListener = new TokenResultListener() {
            @Override
            public void onTokenSuccess(String s) {
                hideLoading();
                TokenRet tokenRet = null;
                try {
                    tokenRet = TokenRet.fromJson(s);
                    if (ResultCode.CODE_START_AUTHPAGE_SUCCESS.equals(tokenRet.getCode())) {
                        SQLog.i(TAG + "唤起授权页成功: " + s);
                    } else if (ResultCode.CODE_SUCCESS.equals(tokenRet.getCode())) {
                        SQLog.i(TAG + "获取token成功: " + s);
                        mPhoneNumberAuthHelper.hideLoginLoading();
                        verifyFastToken(tokenRet.getToken());
                    }
                } catch (Exception e) {
                    SQLog.e(TAG + "解析token异常", e);
                    disableFastLogin(FastLoginConstants.Code.FAILURE_PARSE_ALI, FastLoginConstants.MESSAGE.FAILURE_PARSE_ALI);
                }
            }

            @Override
            public void onTokenFailed(String s) {
                SQLog.w(TAG + "获取token失败: " + s);
                mPhoneNumberAuthHelper.hideLoginLoading();
                hideLoading();
                TokenRet tokenRet = null;
                try {
                    tokenRet = TokenRet.fromJson(s);
                    if (ResultCode.CODE_ERROR_USER_CANCEL.equals(tokenRet.getCode())) {
                        disableFastLogin(FastLoginConstants.Code.CANCEL, FastLoginConstants.MESSAGE.CANCEL);
                        if (fastLoginListener != null) {
                            fastLoginListener.onFastRelease();
                        }
                        quitLoginPage();
                    } else if ("600023".equals(tokenRet.getCode())) {
                        disableFastLogin(tokenRet.getCode(), tokenRet.getMsg());
                        if (fastLoginListener != null) {
                            fastLoginListener.onFastRelease();
                        }
                        quitLoginPage();
                    }else {
                        disableFastLogin(tokenRet.getCode(), tokenRet.getMsg());
                    }
                } catch (Exception e) {
                    SQLog.e(TAG + "解析token异常", e);
                    disableFastLogin(FastLoginConstants.Code.FAILURE_PARSE_ALI_FAILURE, FastLoginConstants.MESSAGE.FAILURE_PARSE_ALI_FAILURE);
                }
            }
        };
        mPhoneNumberAuthHelper = PhoneNumberAuthHelper.getInstance(mContext, mTokenResultListener);
        Context context = SQContextWrapper.getApplicationContext();
        if (context != null) {
            // debug包开启日志
            boolean isDebuggable = (0 != (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE));
            mPhoneNumberAuthHelper.getReporter().setLoggerEnable(isDebuggable);
        }
        mPhoneNumberAuthHelper.setAuthSDKInfo(accessKey);
        mPhoneNumberAuthHelper.setUIClickListener(new AuthUIControlClickListener() {
            @Override
            public void onClick(String code, Context context, String data) {
                SQLog.d(TAG + "授权页点击 code=" + code + ", data=" + data);
                handleClickBtn(code, context, data);
            }
        });
        mUIConfig = BaseUIConfig.init(Constant.DIALOG, (Activity) mContext, mPhoneNumberAuthHelper);
        mPhoneNumberAuthHelper = PhoneNumberAuthHelper.getInstance(mContext, mTokenResultListener);
        mUIConfig.configAuthPage();
        mUIConfig.setOnClickFastLoginListener(new BaseUIConfig.OnClickFastLoginListener() {
            @Override
            public void clickOtherLogin() {
                SQLog.d(TAG + "闪验点击其他方式登录");
                disableFastLogin(FastLoginConstants.Code.FAILURE_CLICK_OTHER_WAY, FastLoginConstants.MESSAGE.FAILURE_CLICK_OTHER_WAY);
            }

            @Override
            public void clickClose() {
                SQLog.d(TAG + "闪验点击关闭");
                disableFastLogin(FastLoginConstants.Code.CANCEL, FastLoginConstants.MESSAGE.CANCEL);
                if (fastLoginListener != null) {
                    fastLoginListener.onFastRelease();
                }
                quitLoginPage();
            }

            @Override
            public void clickForgetPwd() {
                SQLog.d(TAG + "闪验点击忘记账号/密码");

                String language = AppUtils.getLocaleLanguage().toLowerCase();
                String gameName = AppUtils.getAppName(context);
                String userName = AccountCache.getUsername(context);
                StringBuilder params = new StringBuilder();
                if (UrlConstant.FORGET_PWD.contains("?")) {
                    params.append("&");
                } else {
                    params.append("?");
                }
                if (!"".equals(language)) {
                    params.append("locale=" + language);
                }
                if (!"".equals(gameName)) {
                    params.append("&gn=" + URLEncoder.encode(gameName));
                }
                if (!"".equals(userName)) {
                    params.append("&uname=" + userName);
                }
                String url = UrlConstant.FORGET_PWD + params;
                Map<String, String> map = new HashMap<>();
                map.put("push_link", url);
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.forgetPwd, SqTrackBtn.SqTrackBtnExt.forgetPwd, map);
                LogUtil.i(params.toString());
                AppUtils.toSQWebUrl(mContext, url, "忘记密码");
            }

            @Override
            public void clickBack() {
                SQLog.d(TAG + "闪验点击返回");
                disableFastLogin(FastLoginConstants.Code.FAILURE_CLICK_BACK, FastLoginConstants.MESSAGE.FAILURE_CLICK_BACK);
            }


        });
    }

    private void fastLogin() {
        SQLog.d(TAG + "拉起闪验授权页");
        //拉起授权页
        mPhoneNumberAuthHelper.getLoginToken(mContext, 5000);
    }


    public void getFastEnv(FastBooleanResultListener fastBooleanResultListener){
        SQLog.i(TAG + "检查闪验环境");
        initFastAccessKey(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                boolean isFastEnv;
                // 检查闪验环境
                if(mPhoneNumberAuthHelper == null){
                    SQLog.e(TAG + "mPhoneNumberAuthHelper为空，初始化闪验");
                    fastInit();
                }
                isFastEnv = mPhoneNumberAuthHelper.checkEnvAvailable();
                SQLog.i(TAG + "闪验环境检测结果：" + isFastEnv);
                fastBooleanResultListener.callback(isFastEnv);
            }

            @Override
            public void onFailture(int code, String msg) {
                SQLog.e(TAG + "闪验配置获取失败，闪验环境返回false");
                fastBooleanResultListener.callback(false);
            }
        });
    }

    /**
     * 处理授权页点击按钮事件
     *
     * @param code
     * @param context
     * @param data
     */
    private void handleClickBtn(String code, Context context, String data) {
        switch (code) {
            case "700002":
                //点击一键登录
                try {
                    JSONObject object = new JSONObject(data);
                    boolean isChecked = object.optBoolean("isChecked");
                    if (isChecked) {
                        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.aliLogin, SqTrackBtn.SqTrackBtnExt.aliLogin);
                    } else {
                        UAgreeManager.getInstance().showUAgreeToast();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case "700003":
                //点击同意用户协议
                try {
                    JSONObject object = new JSONObject(data);
                    boolean isChecked = object.optBoolean("isChecked");
                    if (isChecked) {
                        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.agreement, SqTrackBtn.SqTrackBtnExt.agreement);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            default:
                break;
        }
    }

    /**
     * 拿到闪验的token后进行校验
     *
     * @param token
     */
    public void verifyFastToken(final String token) {
        showLoading();
        AccountLogic.getInstance(mContext).fastVerifyLogin(token, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> account) {
                hideLoading();
                LoginTractionManager.track(LoginTractionManager.login_way_ali_fast, account);
                if (fastLoginListener != null) {
                    fastLoginListener.onFastLoginSuccess(account);
                }
                if (fastLoginListener != null) {
                    fastLoginListener.onFastRelease();
                }
                quitLoginPage();
            }

            @Override
            public void onFailure(int code, String msg) {
                hideLoading();
                LogUtil.i("校验闪验token失败 ");
                ToastUtil.showToast(mContext, msg);
                disableFastLogin(FastLoginConstants.Code.FAILURE_VERIFY_FAIL, FastLoginConstants.MESSAGE.FAILURE_VERIFY_FAIL);
            }
        }, new VerifyDialogListener() {
            @Override
            public void onShow() {
                //前端的验证页面被打开则先关闭阿里云闪验界面
                quitLoginPage();
            }

            @Override
            public void onClose(boolean needShow) {
                //前端的验证页面被关闭则判断是否需要重新打开阿里云闪验界面
                if (fastLoginListener != null) {
                    fastLoginListener.onVerifyAccount(needShow);
                }
            }
        });
    }


    public interface FastLoginListener {
        //验证失败
        void onFastLoginFail(Bundle bundle);

        //验证成功
        void onFastLoginSuccess(Map<String, String> data);

        //释放
        void onFastRelease();

        //验证账号后是否需要重新打开闪验弹窗
        void onVerifyAccount(boolean needOpen);
    }


    private void showLoading() {
        if (loadingDialog == null) {
            loadingDialog = new LoadingDialog(mContext);
            loadingDialog.setCancelable(false);
        }
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private void hideLoading() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }

    public void quitLoginPage() {
        SQLog.w(TAG + "退出登录页");
        if (mPhoneNumberAuthHelper != null) {
            mPhoneNumberAuthHelper.setAuthListener(null);
            if (fastLoginListener != null) {
                fastLoginListener.onFastRelease();
            }
            mPhoneNumberAuthHelper.quitLoginPage();
        }
    }

    public interface VerifyDialogListener {
        //验证手机号是否是自身的前端页面打开
        void onShow();

        //验证手机号是否是自身的前端页面关闭，needshow 关闭后是否需要重新打开阿里云闪验界面
        void onClose(boolean needShow);
    }

}
