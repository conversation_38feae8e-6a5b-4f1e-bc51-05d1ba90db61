package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.LoginSkinHelper;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.constant.AccountViewBundleKey;
import com.sy37sdk.account.view.uifast.presenter.IPhonePresenter;
import com.sy37sdk.account.view.uifast.presenter.PhonePresenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;

import java.util.Map;

/**
 * 输入手机号页面
 */
public class PhoneView extends BaseSwitchView implements IPhoneView {

    private IPhonePresenter presenter;

    private TextView tvGetVerifyCode;
    private TextView tvForgetPwd;
    private TextView tvClause, tvPolicy;
    private EditText etPhone;
    private TextView tvQuickStart;
    private TextView tvAccountLogin;
    private CheckBox cbClause;
    private View content;


    public PhoneView(Context context, ILoginDialog loginDialog) {
        super(context);
        presenter = new PhonePresenter(context, this);
        this.loginDialog = loginDialog;
    }


    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_phone";
    }

    @Override
    public void initView() {
        content = getViewByName("content_layout");
        content.setBackgroundResource(LoginSkinHelper.getPhoneLoginBackgroundResId(getContext()));
        tvGetVerifyCode = getViewByName("tv_get_verify_code");
        tvGetVerifyCode.setBackgroundResource(LoginSkinHelper.getLoginBtnBackgroundResId(getContext()));
        tvGetVerifyCode.setTextColor(LoginSkinHelper.getLoginBtnTextColor(getContext()));
        etPhone = getViewByName("et_phone");
        tvQuickStart = getViewByName("tv_quick_start");
        tvQuickStart.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        tvClause = getViewByName("tv_clause");
        tvPolicy = getViewByName("tv_policy");
        tvForgetPwd = getViewByName("tv_forget_pwd");
        tvForgetPwd.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        tvAccountLogin = getViewByName("tv_account_login");
        tvAccountLogin.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        cbClause = getViewByName("cb_clause");
        presenter.initData();
    }

    @Override
    public void initEvent() {
        tvGetVerifyCode.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.obtainVerifyCode();
            }
        });
        tvQuickStart.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.quickStart();
            }
        });
        tvAccountLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.accountLogin, SqTrackBtn.SqTrackBtnExt.ACCOUNT_LOGIN);
                loginDialog.onSwitch(AccountPage.ACCOUNT_LOGIN_PAGE, null);
            }
        });
        tvClause.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toClausePage();
            }
        });
        tvPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toPolicy();
            }
        });
        cbClause.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                presenter.clauseClick(isChecked);
            }
        });
        tvForgetPwd.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.forgetPassword();
            }
        });
    }

    @Override
    public String getTitle() {
        return "手机号登录";
    }

    @Override
    public String getPhone() {
        return etPhone.getText().toString();
    }

    @Override
    public void startVerifyCodeView() {
        Bundle bundle = new Bundle();
        bundle.putString(AccountViewBundleKey.mobile, etPhone.getText().toString());
        loginDialog.onSwitch(AccountPage.ACCOUNT_VERIFY_CODE_PAGE, bundle);
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.phone_input, SqTrackPage.SqTrackViewName.phone_input);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void accountRegSuccess(Map<String, String> data) {
        loginDialog.accountRegSuccess(data);
    }

    @Override
    public void regEntrance(boolean enable) {
        tvQuickStart.setVisibility(enable ? VISIBLE : GONE);
    }

    @Override
    public void accountLoginEntrance(boolean enable) {
        tvAccountLogin.setVisibility(enable ? VISIBLE : GONE);
    }

    @Override
    public void checkedClause() {
        cbClause.setChecked(true);
    }
}
