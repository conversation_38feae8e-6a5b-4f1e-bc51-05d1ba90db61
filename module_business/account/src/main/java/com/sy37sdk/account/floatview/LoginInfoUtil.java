package com.sy37sdk.account.floatview;



import android.content.Context;
import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Set;

/**
 *
 * @ClassName: LoginInfoUtil
 * @Description: TODO(sdk登录成功之后返回的信息)
 * <AUTHOR>
 * @date 2016-7-27 下午12:06:06
 *
 */

public class LoginInfoUtil {

	@Deprecated
	public static void setOauthNickName(String oauthinfo, Context context) throws JSONException {
		JSONObject oauthinfoJson = new JSONObject(oauthinfo);
		//Util.setOauthNickName(context, oauthinfoJson.getString("pnick"));
	}

}
