package com.sy37sdk.account.entrance;

import android.content.Context;
import android.util.Log;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tool.sqtools.detector.DevicesFingerprint;
import com.sqwan.base.L;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SpUtils;
import com.sqwan.msdk.config.MultiConfigManager;
import com.sy37sdk.account.UrlConstant;
import org.json.JSONObject;

public class EntranceManager {

    public static final int WX_LOGIN_UI_VERSION_V1 = 1; //微信+其他方式
    public static final int WX_LOGIN_UI_VERSION_V2 = 2; //微信+手机验证码

    private static final String KEY_ACCOUNT_LOGIN_STATE = "account_login_state";
    private static final String KEY_UNAME_REG_STATE = "uname_reg_state";
    private static final String KEY_QUICK_REG_STATE = "quick_reg_state";
    private static final String KEY_WX_LOGIN_STATE = "wx_login_state";
    private static final String KEY_QRCODE_SCAN_STATE = "qrcode_scan_state";
    private static final String KEY_WX_LOGIN_UI_VERSION = "wx_login_ui_version";

    private static EntranceManager instance;

    /**
     * 账号登录入口开关
     */
    private boolean accountLoginEntrance = true;

    /**
     * 账号注册开关
     */
    private boolean uNameRegEntrance = false;

    /**
     * 手机登录页的快速注册开关
     */
    private boolean quickRegEntrance = false;

    /**
     * 微信登录开关
     */
    private boolean wxLoginEntrance = MultiConfigManager.getInstance().isWechat();

    private int wxLoginUIVersion = WX_LOGIN_UI_VERSION_V1;

    private int qrcodeScanState = 0;

    private EntranceManager() {

    }

    public static EntranceManager getInstance() {
        if (instance == null) {
            synchronized (EntranceManager.class) {
                if (instance == null) {
                    instance = new EntranceManager();
                }
            }
        }
        return instance;
    }

    public void requestEntranceConfig(final Context context) {
        LogUtil.i("请求sdk入口配置");

        // 先读取缓存中的配置
        accountLoginEntrance = SpUtils.get(context).getBoolean(KEY_ACCOUNT_LOGIN_STATE, accountLoginEntrance);
        uNameRegEntrance = SpUtils.get(context).getBoolean(KEY_UNAME_REG_STATE, uNameRegEntrance);
        quickRegEntrance = SpUtils.get(context).getBoolean(KEY_QUICK_REG_STATE, quickRegEntrance);
        wxLoginEntrance = SpUtils.get(context).getBoolean(KEY_WX_LOGIN_STATE, wxLoginEntrance);

        SqRequest.of(UrlConstant.URL_REG_ENTRANCE)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(SQContextWrapper.getActivity()))
            .addParamsTransformer(new CommonParamsV3())
            .post(new SimpleSqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    // 更新对应的配置
                    if (jsonObject.has(KEY_ACCOUNT_LOGIN_STATE)) {
                        accountLoginEntrance = (jsonObject.optInt(KEY_ACCOUNT_LOGIN_STATE) == 1);
                        SpUtils.get(context).put(KEY_ACCOUNT_LOGIN_STATE, accountLoginEntrance);
                    } else {
                        // 如果后台没有返回账号登录开关的状态，那么就默认为开启状态
                        accountLoginEntrance = true;
                        SpUtils.get(context).remove(KEY_ACCOUNT_LOGIN_STATE);
                    }

                    if (jsonObject.has(KEY_UNAME_REG_STATE)) {
                        uNameRegEntrance = (jsonObject.optInt(KEY_UNAME_REG_STATE) == 1);
                        SpUtils.get(context).put(KEY_UNAME_REG_STATE, uNameRegEntrance);
                    } else {
                        // 如果后台没有返回账号注册开关的状态，那就默认关闭
                        uNameRegEntrance = false;
                        SpUtils.get(context).remove(KEY_UNAME_REG_STATE);
                    }

                    if (jsonObject.has(KEY_QUICK_REG_STATE)) {
                        quickRegEntrance = (jsonObject.optInt(KEY_QUICK_REG_STATE) == 1);
                        SpUtils.get(context).put(KEY_QUICK_REG_STATE, quickRegEntrance);
                    } else {
                        // 如果后台没有返回账号快速注册开关的状态，那就默认关闭
                        quickRegEntrance = false;
                        SpUtils.get(context).remove(KEY_QUICK_REG_STATE);
                    }

                    if (jsonObject.has(KEY_WX_LOGIN_STATE)) {
                        wxLoginEntrance = (jsonObject.optInt(KEY_WX_LOGIN_STATE) == 1);
                        SpUtils.get(context).put(KEY_WX_LOGIN_STATE, wxLoginEntrance);
                    }

                    if (jsonObject.has(KEY_WX_LOGIN_UI_VERSION)) {
                        wxLoginUIVersion = jsonObject.optInt(KEY_WX_LOGIN_UI_VERSION, WX_LOGIN_UI_VERSION_V1);
                    }

                    if (jsonObject.has(KEY_QRCODE_SCAN_STATE)) {
                        qrcodeScanState = jsonObject.optInt(KEY_QRCODE_SCAN_STATE, qrcodeScanState);
                    }
                }
            });
    }

    public boolean isUNameRegEntrance() {
        return uNameRegEntrance;
    }

    public boolean isQuickRegEntrance() {
        return quickRegEntrance;
    }

    public boolean isAccountLoginEntrance() {
        return accountLoginEntrance;
    }

    public boolean isWxLoginEntrance() {
        return wxLoginEntrance;
    }

    public boolean isSupportScanLogin() {
        return qrcodeScanState == 1;
    }

    public int getWxLoginUIVersion() {
        return wxLoginUIVersion;
    }
}
