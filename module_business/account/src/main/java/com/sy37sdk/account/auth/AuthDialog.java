package com.sy37sdk.account.auth;

import android.content.Context;
import android.os.Bundle;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;

import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sy37sdk.account.AccountCache;

import java.util.HashMap;

/**
 *    author : Aisheng
 *    time   : 2020/05/15
 *    desc   : 实名认证弹窗
 */
public class AuthDialog extends SQWebViewDialog {

    public void setFocusAuthCdFinish(boolean focusAuthCdFinish) {
        isFocusAuthCdFinish = focusAuthCdFinish;
    }

    public boolean isFocusAuthCdFinish() {
        return isFocusAuthCdFinish;
    }

    private boolean isFocusAuthCdFinish = false;
    private boolean isFocus = false;
    private CloseListener mCloseListener;

    public AuthDialog(Context context) {
        super(context);
    }

    public AuthDialog(Context context, int theme) {
        super(context, theme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        if (window == null) {
            return;
        }
        window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
    }

    @Override
    protected void timeOut() {
        super.timeOut();
        if (!isFocus) {
            dismiss();
        }
    }

    public void setFocus(boolean focus) {
        setCancelable(!focus);
        isFocus = focus;
    }

    public void setCloseListener(CloseListener listener) {
        this.mCloseListener = listener;
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (!isFocus) {
            mCloseListener.onClose("", "");
        }
    }

    @Override
    protected void jsClose(final String tag, final String data) {
        super.jsClose(tag, data);
        if (mCloseListener != null) {
            mCloseListener.onClose(tag, data);
        }
    }

    @Override
    public void show() {
        setFocusAuthCdFinish(false);
        loadUrl();
        super.show();
    }

    @Override
    protected void jsEnLogin() {
        super.jsEnLogin();
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount,SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_AUTH);
        HashMap<String,String> logoutMap=new HashMap<>();
        logoutMap.put(SqTrackKey.logout_type, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_AUTH);
        String loginType = AccountCache.getLoginType(getContext());
        logoutMap.put(SqTrackKey.login_type, loginType);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC,logoutMap);
    }

    @Override
    protected void jsCertificate(String code, String msg) {
        super.jsCertificate(code, msg);
        HashMap<String,String> map=new HashMap<>();
        map.put(SqTrackKey.fail_code,code);
        map.put(SqTrackKey.reason_fail,msg);
        map.put(SqTrackKey.certification_url, mUrl);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.certification_fail,map);
    }

    public interface CloseListener {

        /**
         * 关闭回调
         */
        void onClose(String tag, String data);
    }
}