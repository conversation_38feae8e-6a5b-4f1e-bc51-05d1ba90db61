package com.sy37sdk.account.floatview.redpacket;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.animation.AccelerateInterpolator;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.webview.SQWebViewDialog;

import org.json.JSONObject;

/**
 *    author : znb
 *    time   : 2020/12/07
 *    desc   : 红包弹窗
 */
public class RedPacketDialog extends SQWebViewDialog {
    public interface RedPacketDialogCallback{
        void onClose();
        void onOpenUrl();
    }
    public interface AnimCallback{
        void onEnd();
    }
    private RedPacketDialogCallback redPacketDialogCallback;
    private long mDuration = 800;
    private AnimatorSet animatorSet;
    private View floatView;
    private RedPacketInfo redPacketInfo;
    public RedPacketDialog(Context context) {
//        this(context, SqResUtils.getStyleId(context, SqR.style.Dialog));
        super(context);
    }

    public RedPacketDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    public void initData(View floatView,RedPacketInfo redPacketInfo, RedPacketDialogCallback redPacketDialogCallback){
        this.redPacketInfo = redPacketInfo;
        this.floatView = floatView;
        this.redPacketDialogCallback = redPacketDialogCallback;
        String url = UrlUtils.parse(redPacketInfo.webViewConfig.pop_url).baseUrl;
        log("initData url " + url);
        setUrl(url);
    }

    @Override
    protected void jsOpenUrl(String url) {
        post(new Runnable() {

            @Override
            public void run() {
                if (redPacketDialogCallback == null) {
                    return;
                }
                redPacketDialogCallback.onOpenUrl();
            }
        });
    }

    @Override
    protected void jsClose(String tag, String data) {
        post(new Runnable() {
            @Override
            public void run() {
                handleAnim(new AnimCallback() {
                    @Override
                    public void onEnd() {
                        dismiss();
                        if (redPacketDialogCallback!=null) {
                            post(new Runnable() {
                                @Override
                                public void run() {
                                    redPacketDialogCallback.onClose();
                                }
                            });

                        }
                    }
                });
            }
        });

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCanceledOnTouchOutside(false);
    }


    public void handleAnim(final AnimCallback animCallback){
        if (mWebView!=null && floatView!=null) {
            animatorSet = new AnimatorSet();
            animatorSet.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (animCallback!=null) {
                        animCallback.onEnd();
                    }
                }
            });
            int[] locationFloatview = new int[2];
            floatView.getLocationOnScreen(locationFloatview);
            int xFloatview = locationFloatview[0];
            int yFloatview = locationFloatview[1];
            int[] locationWebview = new int[2];
            mWebView.getLocationOnScreen(locationWebview);
            int xWebview = locationWebview[0];
            int yWebview = locationWebview[1];
            int floatviewWidth = floatView.getWidth();
            int floatviewHeight = floatView.getHeight();
            int webviewWidth = mWebView.getWidth();
            int webviewHeight = mWebView.getHeight();
            final int dx = (xFloatview+floatviewWidth/2) - (xWebview+webviewWidth/2);
            final int dy = (yFloatview+floatviewHeight/2) - (yWebview+webviewHeight/2);
            LogUtil.i(String.format("xFloatview:%d,yFloatview:%d,xWebview:%d,yWebview:%s,dx:%d,dy:%d",xFloatview,yFloatview,xWebview,yWebview,dx,dy));
            Animator scaleY = ObjectAnimator.ofFloat(mWebView, "scaleY", 1f, 0.1f);
            Animator scaleX = ObjectAnimator.ofFloat(mWebView, "scaleX", 1f, 0.1f);
            Animator translationX = ObjectAnimator.ofFloat(mWebView, "translationX", 0, dx);
            Animator translationY = ObjectAnimator.ofFloat(mWebView, "translationY", 0, dy);
            animatorSet.playTogether(translationX,translationY,scaleX,scaleY);
            animatorSet.setDuration(mDuration);
            animatorSet.setInterpolator(new AccelerateInterpolator());
            animatorSet.start();
        }else{
            if (animCallback!=null) {
                animCallback.onEnd();
            }
        }


    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (animatorSet!=null) {
            animatorSet.removeAllListeners();
            animatorSet.end();
        }
    }

    @Override
    public void onBackPressed() {
        jsClose("","");
    }

    @Override
    public String getPopData() {
        if (this.redPacketInfo!=null && this.redPacketInfo.webViewConfig!=null) {
            if (this.redPacketInfo.webViewConfig.pop_url!=null) {
                String data = new JSONObject(UrlUtils.parse(this.redPacketInfo.webViewConfig.pop_url).params).toString();
                LogUtil.d("getPopData " + data);
                return data;
            }
        }
        return "";

    }

    @Override
    protected void initView(View view) {
        super.initView(view);

        mWebView.getSettings().setUseWideViewPort(false);
        // 通过百分比来设置文字的大小，默认值是100。
        mWebView.getSettings().setTextZoom(100);

    }
}
