package com.sy37sdk.account.face.ui;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;

/**
 * 退出人脸认证提示框
 */
public class FaceVerifyExitDialog extends BaseDialog {

    private Context mContext;

    private TextView tvCancel, tvSure;

    public FaceVerifyExitDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_dialog_exit_face_verify"));
        tvCancel = findViewById(SqResUtils.getId(mContext, "tv_cancel"));
        tvSure = findViewById(SqResUtils.getId(mContext, "tv_sure"));
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                ModHelper.get(IAccountMod.class).webEnLogin(false);
                if (onClickExitListener != null) {
                    onClickExitListener.clickExit();
                }
            }
        });
    }

    public OnClickExitListener onClickExitListener;

    public void setOnClickExitListener(OnClickExitListener onClickExitListener) {
        this.onClickExitListener = onClickExitListener;
    }

    public interface OnClickExitListener {
        void clickExit();
    }
}
