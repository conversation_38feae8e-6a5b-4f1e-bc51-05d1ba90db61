package com.sy37sdk.account.floatview;

import com.sq.websocket_engine.ARecInfMsg;
import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.WebSocketEngine;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.request.FloatRequestManager;
import com.sy37sdk.account.floatview.request.bean.FetchFloatWindowRedDotRspBean;
import com.sy37sdk.account.floatview.request.websocket.factory.FloatWindowRedDotMsgBaseFactory;
import com.sy37sdk.account.floatview.request.websocket.factory.FloatWindowRedDotMsgPidGidFactory;
import com.sy37sdk.account.floatview.request.websocket.factory.FloatWindowRedDotMsgPlatformFactory;
import com.sy37sdk.account.floatview.request.websocket.factory.FloatWindowRedDotRecInfMsg;


public class FloatViewManager implements WebSocketEngine.WebSocketEngineCallback {
    private static final String TAG = "FloatViewManager";
    private static final FloatViewManager sInstance = new FloatViewManager();

    public static FloatViewManager getInstance() {
        return sInstance;
    }

    private FloatRequestManager floatRequestManager;

    private FloatViewManager() {
        floatRequestManager = new FloatRequestManager();
    }

    private FloatWindowRedDotMsgBaseFactory mFloatWindowRedDotMsgPlatformFactory;
    private FloatWindowRedDotMsgBaseFactory mFloatWindowRedDotMsgPidGidFactory;

    public void requestFloatWindowRedDotMsg() {
        LogUtil.i(TAG, "requestFloatWindowRedDotMsg");
        floatRequestManager.requestFloatWindowRedDotMsgPlatform(new ReqWrapperHandler.FinishListener<Boolean>() {
            @Override
            public void on(Boolean result) {
                LogUtil.i(TAG, "requestFloatWindowRedDotMsgPlatform：" + result);
            }
        });

        floatRequestManager.requestFloatWindowRedDotMsgPidGid(new ReqWrapperHandler.FinishListener<Boolean>() {
            @Override
            public void on(Boolean result) {
                LogUtil.i(TAG, "requestFloatWindowRedDotMsgPidGid：" + result);
            }
        });
    }

    public void bindFloatSocket(OnRecInfListener onRecInfListener) {
        LogUtil.i(TAG, "bindFloatSocket");
        this.mOnRecInfListener = onRecInfListener;
        release();
        if (mFloatWindowRedDotMsgPlatformFactory == null) {
            mFloatWindowRedDotMsgPlatformFactory = new FloatWindowRedDotMsgPlatformFactory();
            WebSocketEngine.getInstance().addARecInfMsgBaseFactory(mFloatWindowRedDotMsgPlatformFactory);
        }

        if (mFloatWindowRedDotMsgPidGidFactory == null) {
            mFloatWindowRedDotMsgPidGidFactory = new FloatWindowRedDotMsgPidGidFactory();
            WebSocketEngine.getInstance().addARecInfMsgBaseFactory(mFloatWindowRedDotMsgPidGidFactory);
        }

        WebSocketEngine.getInstance().registerWebSocketEngineCallback(this);
    }

    @Override
    public void onAuth() {
        LogUtil.i(TAG, "onAuth");
        requestFloatWindowRedDotMsg();
    }

    @Override
    public void onReceiveInf(ARecInfMsg inf) {
        handleMsgInf(inf);
    }

    private void handleMsgInf(ARecInfMsg inf) {
        if (inf instanceof FloatWindowRedDotRecInfMsg) {
            FetchFloatWindowRedDotRspBean floatWindowRedDotRspBean = ((FloatWindowRedDotRecInfMsg) inf).getInf();
            if (floatWindowRedDotRspBean != null) {
                RedDot redDot = new RedDot();
                redDot.setNum(floatWindowRedDotRspBean.getNumber());
                String title = floatWindowRedDotRspBean.getTitle();
                redDot.setTitle(title);
                FloatViewDataManager.getInstance().updateRedDotNum(redDot);
                LogUtil.i(TAG, "FloatViewRedRecInfMsg：" + floatWindowRedDotRspBean);

                MenuConfig menuConfig = new MenuConfig();
                menuConfig.title = floatWindowRedDotRspBean.getTitle();
                menuConfig.warningType = floatWindowRedDotRspBean.getWarningType();
                menuConfig.priority = floatWindowRedDotRspBean.getPriority();
                menuConfig.warningMsg = floatWindowRedDotRspBean.getWarningMsg();
                if (mOnRecInfListener != null) {
                    mOnRecInfListener.onMsgInf(menuConfig);
                }
            }
        }
    }

    public void release() {
        WebSocketEngine.getInstance().unregisterWebSocketEngineCallback(this);
        if (mFloatWindowRedDotMsgPlatformFactory != null) {
            WebSocketEngine.getInstance().removeARecInfMsgBaseFactory(mFloatWindowRedDotMsgPlatformFactory);
            mFloatWindowRedDotMsgPlatformFactory = null;
        }

        if (mFloatWindowRedDotMsgPidGidFactory != null) {
            WebSocketEngine.getInstance().removeARecInfMsgBaseFactory(mFloatWindowRedDotMsgPidGidFactory);
            mFloatWindowRedDotMsgPidGidFactory = null;
        }
    }


    private OnRecInfListener mOnRecInfListener;

    public interface OnRecInfListener {
        void onMsgInf(MenuConfig menuConfig);
    }
}
