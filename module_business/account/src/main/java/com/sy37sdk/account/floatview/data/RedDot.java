package com.sy37sdk.account.floatview.data;


import android.text.TextUtils;

import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

public class RedDot {

    //1:数字红点  ； 2:普通红点
    private int redDotTpe;
    //对应入参red_dot_title
    private String title;
    //红点数
    private int num;

    public int getRedDotTpe() {
        return redDotTpe;
    }

    public void setRedDotTpe(int redDotTpe) {
        this.redDotTpe = redDotTpe;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public boolean needShowNum() {
        return redDotTpe == 1;
    }

    @Override
    public String toString() {
        return "RedDot{" +
                "redDotTpe=" + redDotTpe +
                ", title='" + title + '\'' +
                ", num=" + num +
                '}';
    }


    public static ArrayList<RedDot> getRedDots(String json) {
        ArrayList<RedDot> redDots = new ArrayList<>();
        try {
            JSONArray jsonArray = new JSONArray(json);
            if (jsonArray != null && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    String redJsonStr = jsonArray.optString(i);
                    RedDot redDot = parse(redJsonStr);
                    redDots.add(redDot);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return redDots;
    }

    /**
     * 从服务端获取到红点后解析
     *
     * @param json
     * @return
     */
    public static RedDot parse(String json) {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            JSONObject object = new JSONObject(json);
            int redDotType = object.optInt("red_dot_type");
            String title = object.optString("title");
            int num = object.optInt("num");
            RedDot redDot = new RedDot();
            redDot.setNum(num);
            redDot.setRedDotTpe(redDotType);
            redDot.setTitle(title);
            return redDot;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将对象转成json字符串
     *
     * @param redDot
     * @return
     */
    public static String objectToJson(RedDot redDot) {
        if (redDot == null) {
            return "";
        }
        try {
            JSONObject object = new JSONObject();
            object.put("red_dot_type", redDot.getRedDotTpe());
            object.put("title", redDot.getTitle());
            object.put("num", redDot.getNum());
            return object.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
