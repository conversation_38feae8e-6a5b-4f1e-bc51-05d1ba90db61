package com.sy37sdk.account;

import static com.sy37sdk.account.util.AccountLoginType.LoginWay.LOGIN_ACCOUNT;
import static com.sy37sdk.account.util.AccountLoginType.LoginWay.LOGIN_ONE_KEY;
import static com.sy37sdk.account.util.AccountLoginType.LoginWay.LOGIN_PHONE_CODE;
import static com.sy37sdk.account.util.AccountLoginType.LoginWay.LOGIN_PHONE_PWD;
import static com.sy37sdk.account.util.AccountLoginType.LoginWay.LOGIN_WECHAT;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import com.sdk.sq.net.RequestErrorCode;
import com.social.sdk.SocialApi;
import com.social.sdk.common.SocialConstant;
import com.social.sdk.common.listener.OnAuthListener;
import com.social.sdk.platform.PlatformType;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tools.Logger;
import com.sqnetwork.voly.TimeoutError;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IBindWxListener;
import com.sqwan.common.mod.config.IConfigMod;
import com.sqwan.common.user.UserInfoManager;
import com.sqwan.common.util.AESUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.NetWorkUtils;
import com.sqwan.common.util.SDKError;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ZipString;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.alifast.FastLoginHttpUtil;
import com.sy37sdk.account.alifast.FastLoginManager;
import com.sy37sdk.account.captcha.CaptchaDialog;
import com.sy37sdk.account.captcha.VerifyPhoneDialog;
import com.sy37sdk.account.db.LoginTrigger;
import com.sy37sdk.account.db.LoginTriggerDBManager;
import com.sy37sdk.account.net.LoginRequestManager;
import com.sy37sdk.account.trackaction.UserNameEmptyTrackAction;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.util.AccountLoginType;
import com.sy37sdk.account.util.AccountRegType;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.uifast.presenter.PhonePresenter;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
public class AccountLogic {

    private static final String TAG = "【Login Logic】";

    private int login_timeout_count = 0;

    private static int MAX_TIME_OUT = 2;

    //登录接口当服务端返回state为500时进行假登录
    private static int SERVER_ERROR_CODE = 500;

    //登录接口当服务端返回state为302时data.link打开链接
    private static int SERVER_ERROR_CODE_LINK = 302;

    //ticket过期，需要重新回到手机发验证
    public static int SERVER_ERROR_CODE_TICKET_TIMEOUT = 401;

    private Context context;
    private AccountRequestManager requestManager;
    private AccountListener loginListener;

    private String loginData;

    /**
     * 账号注册之后的二维码信息
     */
    private QrCodeInfo qrCodeInfo;

    private AccountLogic(Context context) {
        this.context = context;
        requestManager = new AccountRequestManager(context);
    }

    private static volatile AccountLogic instance;

    public static AccountLogic getInstance(Context context) {
        if (instance == null) {
            synchronized (AccountLogic.class) {
                if (instance == null) {
                    instance = new AccountLogic(context);
                }
            }
        }
        return instance;
    }


    /**
     * 账号+密码登录
     */
    public void accountLogin(final String loginName, final String pw, final boolean isPhoneType,
        final AccountListener loginListener) {
        this.loginListener = loginListener;
        requestManager.loginRequest(loginName, encryptPwd(pw), isPhoneType, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (state == SERVER_ERROR_CODE) {
                    //后端接口返回500错误码，调用假登录
                    handleTriggerLoginAccount(loginName);
                } else {
                    handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                        @Override
                        public void result(boolean success, String msg) {
                            if (success) {
                                accountLogin(loginName, pw, isPhoneType, loginListener);
                            } else {
                                loginListener.onFailure(state, msg);
                                BuglessAction.reportCatchException(new Exception("登录失败"), msg,
                                    data, BuglessAction.S_LOGIN);
                            }
                        }
                    });
                }
            }

            @Override
            public void onSuccess(String data) {
                handleLoginSuccess(data, pw, LOGIN_ACCOUNT, false, "");
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (error instanceof TimeoutError) {
                    login_timeout_count++;
                    if (login_timeout_count >= MAX_TIME_OUT) {
                        //登录超时两次则调用假登录
                        handleTriggerLoginAccount(loginName);
                    } else {
                        loginListener.onFailure(SDKError.NET_TIME_OUT_ERROR.code, SDKError.NET_TIME_OUT_ERROR.message);
                    }
                } else {
                    if (500 <= code && code <= 599) {
                        //http错误码为5开头的则调用假登录
                        handleTriggerLoginAccount(loginName);
                    } else {
                        loginListener.onFailure(code, errorMsg);
                    }
                }
            }
        });
    }

    /**
     * 账号登录
     */
    public void accountLogin(String loginName, final String pw, final AccountListener loginListener) {
        accountLogin(loginName, pw, false, loginListener);
    }


    /**
    * 检查可用的账号列表
    * */
    public void checkAccountList(String loginName, final String pw, MultiAccountHandler multiAccountHandler, AccountListener accountListener) {
        requestManager.checkAccountList(loginName, encryptPwd(pw), new SqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject jsonObject) {
                try{
                    int total = jsonObject.optInt("total");
                    JSONArray accountList = jsonObject.optJSONArray("account_list");
                    if (accountList != null && total > 0) {
                        if (total == 1) {
                            //只有一个角色，直接登录
                            JSONObject account = accountList.optJSONObject(0);
                            String loginType = account.optString("login_type");
                            //根据账号登录类型走对应的登录接口
                            if ("account".equals(loginType)) {
                                accountLogin(loginName, pw, accountListener);
                            } else if ("phone_pwd".equals(loginType)) {
                                phoneLoginPwd(loginName, pw, accountListener);
                            } else {
                                accountListener.onFailure(-1, "check_account_list 接口返回错误");
                            }
                        } else {
                            multiAccountHandler.onMultiAccount(accountList, loginName, pw);
                            JSONObject account1 = accountList.optJSONObject(0);
                            JSONObject account2 = accountList.optJSONObject(1);
                            String okUid = "";
                            if (account1.optBoolean("ok")) {
                                okUid = account1.optString("uid");
                            }
                            if (account2.optBoolean("ok")) {
                                okUid = okUid + (TextUtils.isEmpty(okUid) ? "" : ",")+ account2.optString("uid");
                            }
                            LoginTractionManager.trackCheckAccountListSucc(new String[]{
                                account1.optString("uid"),
                                account2.optString("uid"),
                            }, okUid);
                        }
                    } else {
                        accountListener.onFailure(-1, "check_account_list 接口返回错误，没有角色数据");
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    accountListener.onFailure(-1, "check_account_list 接口返回错误，json解析失败");
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                accountListener.onFailure(code, errorMsg);
                LoginTractionManager.trackCheckAccountListFail(errorMsg);
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                accountListener.onFailure(state, msg);
                LoginTractionManager.trackCheckAccountListFail(msg);
            }


        });
    }

    /**
     * 账号注册
     */
    public void accountRegister(final String regName, final String regPW, final AccountListener regListener) {
        this.loginListener = regListener;
        requestManager.registerRequest(regName, encryptPwd(regPW), new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                    @Override
                    public void result(boolean success, String message) {
                        if (success) {
                            accountRegister(regName, regPW, regListener);
                        } else {
                            regListener.onFailure(state, message);
                            BuglessAction.reportCatchException(new Exception("登录失败"), msg,
                                data, BuglessAction.S_LOGIN);
                        }
                    }
                });
            }

            @Override
            public void onSuccess(String data) {
                // 需要置空，不然注册完，下次登录账号为上一个账号，密码是这次注册的密码，导致无法登录
                AccountCache.setAccountAlias(context, "");
                if (regName.equals(AccountCache.getAutoName(context)) && AccountCache.getAutoState(context)) {
                    handleRegQrCode(data);
                }
                handleLoginSuccess(data, regPW, LOGIN_ACCOUNT, true);
                LogUtil.d("账号注册成功");
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                regListener.onFailure(code, errorMsg);
            }
        });
    }

    /**
     * 处理注册失败后跳转验证的逻辑
     */
    private void handleCaptcha(String data, String msg, final CaptchaDialog.VerifyListener listener) {
        try {
            JSONObject dataJson = new JSONObject(data);
            final String captchaUrl = dataJson.optString("captcha_url", "");
            LogUtil.i("解析到 captchaUrl: " + captchaUrl);
            if (!TextUtils.isEmpty(captchaUrl)) {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        CaptchaDialog dialog = new CaptchaDialog(context);
                        dialog.setUrl(AppUtils.constructWebUrlParam(context, captchaUrl));
                        dialog.setVerifyListener(listener);
                        dialog.show();
                    }
                });
            } else {
                if (listener != null) {
                    listener.result(false, msg);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (listener != null) {
                listener.result(false, msg);
            }
        }
    }

    public void handlePhoneVerify(final String link, final VerifyPhoneDialog.VerifyListener listener) {
        LogUtil.i("打开 verifyLink: " + link);
        if (!TextUtils.isEmpty(link)) {
            new Handler().post(new Runnable() {
                @Override
                public void run() {
                    VerifyPhoneDialog verifyPhoneDialog = new VerifyPhoneDialog(context);
                    verifyPhoneDialog.setUrl(link);
                    verifyPhoneDialog.setVerifyListener(listener);
                    verifyPhoneDialog.show();
                }
            });
        }

    }

    /**
     * 发送验证码
     */
    public void sendPhoneCode(final String phoneNumber, final VerifyCodeListener listener) {
        LoginRequestManager.sendPhoneCode(phoneNumber, new SqHttpCallback<Void>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (state == PhonePresenter.CODE_PHONE_VALID) {
                    listener.onFailure(PhonePresenter.CODE_PHONE_VALID, msg);
                } else {
                    handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                        @Override
                        public void result(boolean success, String message) {
                            if (success) {
                                sendPhoneCode(phoneNumber, listener);
                            } else {
                                listener.onFailure(state, message);
                            }
                        }
                    });
                }
            }

            @Override
            public void onSuccess(Void unused) {
                AccountCache.setVerifyCodeLastTime(context, System.currentTimeMillis());
                listener.onSuccess();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                listener.onFailure(code, errorMsg);
            }
        });
    }

    public void autoAccount(final AutoAccountListener listener) {
        requestManager.autoAccountRequest(new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                listener.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                AutoAccountBean autoAccountBean = null;
                try {
                    autoAccountBean = AutoAccountBean.fromJson(data);
                    String pwd = decryptPwd(autoAccountBean.getPwd());
                    autoAccountBean.setPwd(pwd);
                } catch (JSONException e) {
                    e.printStackTrace();
                    BuglessAction.reportCatchException(e, data, BuglessAction.S_AUTO_REG);
                }
                if (autoAccountBean != null && autoAccountBean.isAutoAccount()) {
                    listener.onSuccess(autoAccountBean);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                listener.onFailure(code, errorMsg);
            }
        });
    }

    public void bindWxOpenId(String code, final IBindWxListener listener) {
        requestManager.wxAuthRequest(code, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull  String msg, @Nullable String data) {
                listener.onFailure(-1, "绑定微信openId出错");
            }

            @Override
            public void onSuccess(String data) {
                listener.onSuccess(data);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                listener.onFailure(-1, "绑定微信openId出错" + errorMsg);
            }
        });
    }

    private void handleRegQrCode(String data) {
        qrCodeInfo = null;
        try {
            JSONObject dataJson = new JSONObject(data);
            if (dataJson.has("qrcode")) {
                JSONObject qrcodeJson = dataJson.getJSONObject("qrcode");
                String url = qrcodeJson.getString("img");
                String content = qrcodeJson.getString("content");
                LogUtil.d("qrCodeImgUrl: " + url + ", qrCodeImgContent: " + content);
                qrCodeInfo = new QrCodeInfo(url, content);
            }
        } catch (JSONException e) {
            LogUtil.d("获取qrCode异常");
            e.printStackTrace();
        }
    }

    public QrCodeInfo getQrCodeInfo() {
        return qrCodeInfo;
    }

    private void handleLoginSuccess(String content, String pwd, String loginWay, boolean isReg) {
        handleLoginSuccess(content, pwd, loginWay, isReg, "");
    }


    public void handleLoginSuccess(String content, String pwd, String loginWay, boolean isReg, String mobile) {
        handleLoginSuccess(content, pwd, AccountLoginType.parseLoginType(loginWay), loginWay, isReg, mobile);
    }

    /**
     * 处理登录返回数据
     * isAuto 是否自动登录
     * loginType 账号类型 {@link AccountLoginType.LoginType}
     * loginWay 登录方式 {@link AccountLoginType.LoginWay}
     *
     * @param isReg 是否是注册， 如果是注册的话需要解析密码
     */
    private void handleLoginSuccess(String content, String pwd, String loginType, String loginWay, boolean isReg,
        String mobile) {
        loginData = content;
        UserInfo user;
        try {
            user = createUserInfo(content, pwd, loginType, loginWay, isReg, mobile);

            IConfigMod configMod = ModHelper.getConfig();
            if (configMod != null) {
                configMod.getCommonConfig().setUserId(user.getUid());
            }

            UserNameEmptyTrackAction.report(UserNameEmptyTrackAction.ActionType.login, user.getUname(), content);
            SQLog.i(TAG + "登录成功用户: " + user);
            com.sqwan.common.user.UserInfo newUser = user.convert();
            if (newUser != null) {
                UserInfoManager.getInstance().setLoginUser(newUser);
            }
            AccountCache.setUserInfo(context, user);
            AccountTools.setAccountToFile(context, user);
            UAgreeManager.getInstance().refreshVersion(user.getUname());
        } catch (Exception e) {
            SQLog.e(TAG + "处理用户信息异常, 登录失败", e);
            BuglessAction.reportCatchException(e, content, BuglessAction.S_LOGIN);
            loginListener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, SDKError.ACCOUNT_LOGIN_ERROR.message);
            return;
        }
        //登录成功回调
        HashMap<String, String> accountData = new HashMap<>();
        accountData.put("userid", user.getUid());
        accountData.put("username", user.getUname());
        accountData.put("token", user.getToken());
        accountData.put("pwd", user.getUpwd());
        accountData.put("pid", ConfigManager.getInstance(context).getSQAppConfig().getPartner());
        accountData.put("gid", ConfigManager.getInstance(context).getSQAppConfig().getGameid());

        //下面的字段用作埋点
        boolean register = user.getActionType().equals("register");
        //账号类型(手机or账号)
        String regWay = isReg ? AccountRegType.RegWay.REG_FAST : AccountRegType.parseRegWay(loginWay);
        String accountType = register ? AccountRegType.parseRegType(regWay) : loginType;
        accountData.put(LoginTractionManager.TRACK_ACCOUNT_TYPE, LoginTractionManager.parseAccountType(accountType));
        //登录类型（注册or登录）
        String actionType = user.getActionType().equals("register") ? LoginTractionManager.TRACK_LOGIN_TYPE_REGISTER
            : LoginTractionManager.TRACK_LOGIN_TYPE_LOGIN;
        accountData.put(LoginTractionManager.TRACK_LOGIN_TYPE, actionType);
        DeviceUtils.setLoginInvoked(context);
        loginListener.onSuccess(accountData);
        AccountCache.setLogined(context, true);
    }


    /**
     * 处理ticket登录失败数据
     * loginType 账号类型 {@link AccountLoginType.LoginType}
     * loginWay 登录方式 {@link AccountLoginType.LoginWay}
     */
    private void handleTicket(String loginType, String loginWay, String mobile, String ticket) {
        UserInfo user;
        try {
            user = new UserInfo();
            user.setUid("");
            user.setUpwd("");
            user.setUname(mobile);
            user.setToken("");
            user.setRefreshToken("");
            user.setAlias("");
            user.setActionType("");
            user.setLoginType(loginType);
            user.setLoginWay(loginWay);
            user.setMobile(mobile);
            user.setTicket(ticket);

            IConfigMod configMod = ModHelper.getConfig();
            if (configMod != null) {
                configMod.getCommonConfig().setUserId(user.getUid());
            }
            SQLog.i(TAG + "handleTicket-->user:"+user);
            SQLog.i(TAG + "handleTicket-->Ticket:" + ticket);

            com.sqwan.common.user.UserInfo newUser = user.convert();
            if (newUser != null) {
                UserInfoManager.getInstance().setLoginUser(newUser);
            }
            AccountCache.setUserInfo(context, user);
            AccountTools.setAccountToFile(context, user);
        } catch (Exception e) {
            SQLog.e(TAG + "处理用户信息异常, 登录失败", e);
            BuglessAction.reportCatchException(e, "", BuglessAction.S_LOGIN);
            loginListener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, SDKError.ACCOUNT_LOGIN_ERROR.message);
        }
    }

    /**
     * 根据接口json构建用户实例
     */
    private UserInfo createUserInfo(String content, String pwd, String loginType, String loginWay, boolean isReg,
        String mobile) throws Exception {
        JSONObject data = new JSONObject(content);
        if (isReg && data.has("upwd")) {
            pwd = decryptPwd(data.optString("upwd"));
        }
        if (data.has("pwd")) {
            pwd = decryptPwd(data.optString("pwd"));
        }

        String uid = data.optString("uid");
        String uname = data.optString("uname");
        String token = data.optString("token");
        String refreshToken = data.optString("refresh_token");
        String alias = data.optString("login_account");
        String actionType = data.optString("action_type");
        UserInfo user = new UserInfo();
        user.setUid(uid);
        user.setUpwd(!TextUtils.isEmpty(pwd) ? ZipString.json2ZipString(pwd) : "");
        user.setUname(uname);
        user.setToken(token);
        user.setRefreshToken(refreshToken);
        user.setAlias(alias);
        user.setActionType(actionType);
        user.setLoginType(loginType);
        user.setLoginWay(loginWay);
        user.setMobile(mobile);
        user.setTicket(""); //登录成功后清空ticket
        return user;
    }

    private void handleTriggerLoginAccount(String uname) {
        UserInfo userInfo;
        UserInfo cacheInfo = AccountCache.getUserInfo(context);
        if (cacheInfo != null && !TextUtils.isEmpty(uname) && ((!TextUtils.isEmpty(cacheInfo.getUname()) && uname.equals(cacheInfo.getUname())) || (!TextUtils.isEmpty(cacheInfo.getAlias()) && uname.equals(cacheInfo.getAlias())))) {
            userInfo = cacheInfo;
        } else {
            userInfo = AccountUtil.findUserByUname(context, uname);
        }
        handleTriggerLoginSuccess(userInfo);
    }

    private void handleTriggerLoginPhone(String phone) {
        UserInfo userInfo;
        UserInfo cacheInfo = AccountCache.getUserInfo(context);
        if (cacheInfo != null && !TextUtils.isEmpty(phone) && !TextUtils.isEmpty(cacheInfo.getActionType()) && cacheInfo.getActionType().equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE) && phone.equals(cacheInfo.getMobile())) {
            userInfo = cacheInfo;
        } else {
            userInfo = AccountUtil.findUserByPhone(context, phone);
        }
        handleTriggerLoginSuccess(userInfo);
    }

    /**
     * 当服务器不可用时，直接返回登录成功
     */
    private void handleTriggerLoginSuccess(UserInfo userInfo) {

        //如果有这个文件，则不限制是否挂了代理等
        boolean fileExits = EnvironmentUtils.isFileExits(context, "sq-trigger");
        if (!fileExits) {
            if (!NetWorkUtils.isNetworkAvailable(context) || NetWorkUtils.isWifiProxy()) {
                //网络无连接或挂了代理则不允许假登录
                loginListener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, SDKError.ACCOUNT_LOGIN_ERROR.message);
                return;
            }
        }

        if (userInfo == null) {
            loginListener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, SDKError.ACCOUNT_LOGIN_ERROR.message);
            return;
        }
        //重置超时的登录次数
        login_timeout_count = 0;
        String userInfoJsonStr = UserInfo.encodeToJson(userInfo).toString();
        handleLoginSuccess(userInfoJsonStr, userInfo.getUpwd(), userInfo.getLoginType(), userInfo.getLoginWay(), false, userInfo.getMobile());
        try {
            LoginTrigger loginTrigger = new LoginTrigger();
            String loginType = "";
            if (userInfo.getLoginType().equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE)) {
                loginType = "phone";
            } else {
                loginType = "common";
            }
            loginTrigger.setLoginType(loginType);
            loginTrigger.setTriggerTime(System.currentTimeMillis());
            loginTrigger.setUid(userInfo.getUid());
            loginTrigger.setUname(userInfo.getUname());
            loginTrigger.setToken(userInfo.getToken());
            LoginTriggerDBManager.getInstance().insertLoginTrigger(loginTrigger);
            LoginTriggerManager.getInstance().startQueryLoginTrigger();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public String getLoginData() {
        return loginData;
    }


    /**
     * 手机+验证码访问后台获取ticket
     */
    public void phoneLoginCheckCode(final String phone, final String code, final AccountListener listener) {
        this.loginListener = listener;
        LoginRequestManager.phoneLoginCheckCode(phone, code, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull  String msg, @Nullable String data) {
                loginListener.onFailure(state, msg);
            }

            @Override
            public void onSuccess(String data) {
                try {
                    JSONObject dataObj = new JSONObject(data);
                    String ticket = dataObj.optString("ticket");
                    AccountCache.setTicketState(true);
                    handleTicket(AccountLoginType.parseLoginType(LOGIN_PHONE_CODE), LOGIN_PHONE_CODE, phone, ticket);
                    phoneLoginTicket(ticket, phone, listener);
                } catch (JSONException e) {
                    e.printStackTrace();
                    loginListener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, SDKError.ACCOUNT_LOGIN_ERROR.message);
                }

            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (code == RequestErrorCode.ERROR_TIMEOUT) {
                    login_timeout_count++;
                    if (login_timeout_count >= MAX_TIME_OUT) {
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(SDKError.NET_TIME_OUT_ERROR.code, SDKError.NET_TIME_OUT_ERROR.message);
                    }
                } else {
                    if (500 <= code && code <= 599) {
                        //http错误码为5开头的则调用假登录
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(code, errorMsg);
                    }
                }
            }
        });
    }


    /**
     * Ticket登录
     */
    public void phoneLoginTicket(final String ticket,final String phone, final AccountListener listener) {
        this.loginListener = listener;
        LoginRequestManager.phoneLoginTicket(ticket, new SqHttpCallback<String>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull  String msg, @Nullable String data) {
                if (state == SERVER_ERROR_CODE) {
                    handleTriggerLoginPhone(phone);
                } else if(state == SERVER_ERROR_CODE_TICKET_TIMEOUT){
                    loginListener.onFailure(state, "请重新获取验证码，"+msg);
                    BuglessAction.reportCatchException(new Exception("登录失败"),
                        msg, data, BuglessAction.S_LOGIN);
                } else {
                    try {
                        JSONObject dataObj = new JSONObject(data);
                        String link = dataObj.optString("link");
                        if (!TextUtils.isEmpty(link) && state == SERVER_ERROR_CODE_LINK) {
                            //验证手机号登录
                            handlePhoneVerify(link, new VerifyPhoneDialog.VerifyListener() {
                                @Override
                                public void result(boolean success, String data) {
                                    if (success) {
                                        handleLoginSuccess(data, "", LOGIN_PHONE_CODE, false, phone);
                                    } else {
                                        loginListener.onFailure(state, data);
                                        BuglessAction.reportCatchException(new Exception("登录失败"),
                                            msg, data, BuglessAction.S_LOGIN);
                                    }
                                }
                            });
                        } else {
                            //验证风控
                            String captchaUrl = dataObj.optString("captcha_url", "");
                            if (!TextUtils.isEmpty(captchaUrl)) {
                                handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                                    @Override
                                    public void result(boolean success, String message) {
                                        if (success) {
                                            phoneLoginTicket(ticket, phone, listener);
                                        } else {
                                            loginListener.onFailure(state, message);
                                            BuglessAction.reportCatchException(new Exception("登录失败"),
                                                msg, data, BuglessAction.S_LOGIN);
                                        }
                                    }
                                });
                            } else {
                                loginListener.onFailure(state, msg);
                                BuglessAction.reportCatchException(new Exception("登录失败"), msg,
                                    data, BuglessAction.S_LOGIN);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        loginListener.onFailure(state, msg);
                        BuglessAction.reportCatchException(new Exception("登录失败"), msg,
                            data, BuglessAction.S_LOGIN);
                    }
                }
            }

            @Override
            public void onSuccess(String data) {
                handleLoginSuccess(data, "", LOGIN_PHONE_CODE, false, phone);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (code == RequestErrorCode.ERROR_TIMEOUT) {
                    login_timeout_count++;
                    if (login_timeout_count >= MAX_TIME_OUT) {
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(SDKError.NET_TIME_OUT_ERROR.code, SDKError.NET_TIME_OUT_ERROR.message);
                    }
                } else {
                    if (500 <= code && code <= 599) {
                        //http错误码为5开头的则调用假登录
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(code, errorMsg);
                    }
                }
            }
        });
    }


    /**
     * 手机 + 密码登录
     */
    public void phoneLoginPwd(final String phone, final String pwd, final AccountListener listener) {
        this.loginListener = listener;
        LoginRequestManager.phoneLoginPwd(phone, encryptPwd(pwd), new SqHttpCallback<String>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull  String msg, @Nullable String data) {
                if (state == SERVER_ERROR_CODE) {
                    handleTriggerLoginPhone(phone);
                } else {
                    handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                        @Override
                        public void result(boolean success, String message) {
                            if (success) {
                                phoneLoginPwd(phone, pwd, listener);
                            } else {
                                loginListener.onFailure(state, message);
                                BuglessAction.reportCatchException(
                                    new Exception("登录失败"), msg, data, BuglessAction.S_LOGIN);
                            }
                        }
                    });
                }
            }

            @Override
            public void onSuccess(String data) {
                handleLoginSuccess(data, pwd, LOGIN_PHONE_PWD, false, phone);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (code == RequestErrorCode.ERROR_TIMEOUT) {
                    login_timeout_count++;
                    if (login_timeout_count >= MAX_TIME_OUT) {
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(SDKError.NET_TIME_OUT_ERROR.code, SDKError.NET_TIME_OUT_ERROR.message);
                    }
                } else {
                    if (500 <= code && code <= 599) {
                        //http错误码为5开头的则调用假登录
                        handleTriggerLoginPhone(phone);
                    } else {
                        loginListener.onFailure(code, errorMsg);
                    }
                }
            }
        });
    }


    /**
     * 阿里云闪验登录
     */
    public void fastVerifyLogin(final String token, final AccountListener listener, final FastLoginManager.VerifyDialogListener verifyDialogListener) {
        this.loginListener = listener;
        SQLog.d(TAG + "校验闪验token");
        FastLoginHttpUtil.verifyFastToken(token, new SqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject dataObj) {
                try {
                    String mobile = dataObj.optString("mobile");
                    SQLog.i(TAG + "校验闪验token成功: " + mobile);
                    handleLoginSuccess(dataObj.toString(), "", LOGIN_ONE_KEY, false, mobile);
                } catch (Exception e) {
                    SQLog.e(TAG + "校验闪验token未解析到手机号: " + dataObj, e);
                    loginListener.onFailure(SDKError.NET_DATA_PARSE_ERROR.code, SDKError.NET_DATA_PARSE_ERROR.message);
                }
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.e(TAG + "校验闪验token失败, code=" + state + ", msg=" + msg);
                if (data == null || data.isEmpty()) {
                    loginListener.onFailure(state, msg);
                    BuglessAction.reportCatchException(new Exception("登录失败"), msg, data, BuglessAction.S_LOGIN);
                    return;
                }
                try {
                    JSONObject dataObj = new JSONObject(data);
                    String link = dataObj.optString("link");
                    if (!TextUtils.isEmpty(link)) {
                        //验证手机号登录
                        if (verifyDialogListener != null) {
                            verifyDialogListener.onShow();
                        }
                        handlePhoneVerify(link, (success, resultData) -> {
                            if (success) {
                                String mobile = "";
                                try {
                                    JSONObject object = new JSONObject(resultData);
                                    mobile = object.optString("mobile");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                handleLoginSuccess(resultData, "", LOGIN_ONE_KEY, false, mobile);
                            }
                            if (verifyDialogListener != null) {
                                verifyDialogListener.onClose(!success);
                            }
                        });
                    } else {
                        loginListener.onFailure(state, msg);
                        BuglessAction.reportCatchException(new Exception("登录失败"), msg, data, BuglessAction.S_LOGIN);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    loginListener.onFailure(state, msg);
                    BuglessAction.reportCatchException(new Exception("登录失败"), msg, data, BuglessAction.S_LOGIN);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.e(TAG + "校验闪验token异常, code=" + code + ", msg=" + errorMsg);
                loginListener.onFailure(SDKError.NET_REQUEST_FAIL.code, errorMsg);
            }
        });
    }


    /**
     * 快速登录
     */
    public void fastLogin(final UserInfo userInfo, final AccountListener listener) {
        this.loginListener = listener;
        SQLog.d(TAG + "快速登录");
        FastLoginHttpUtil.fastLogin(userInfo, new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.e(TAG + "快速登录失败 " + msg + "(" + state + ")");
                loginListener.onFailure(state, msg);
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                SQLog.i(TAG + "快速登录成功");
                handleFastLoginSuccess(dataJson);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.e(TAG + "快速登录异常 " + errorMsg + "(" + code + ")");
                loginListener.onFailure(code, errorMsg);
            }
        });
    }

    /**
     * 处理快速登录成功(快速登录仅返回token/uid/uname)
     */
    private void handleFastLoginSuccess(JSONObject dataObj) {
        UserInfo userInfo = null;
        String token = "";
        String actionType = "";
        try {
            String uname = dataObj.optString("uname");
            token = dataObj.optString("token");
            actionType = dataObj.optString("action_type");
            String spUname = AccountCache.getUsername(context);
            if (uname.equals(spUname)) {
                //跟sp中缓存的uname一样
                userInfo = AccountCache.getUserInfo(context);
            } else if (AccountUtil.findUserByUname(context, uname) != null) {
                //跟sp中缓存的id不一样，则查找文件缓存当中的
                userInfo = AccountUtil.findUserByUname(context, uname);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (userInfo != null) {
            userInfo.setToken(token);
            userInfo.setActionType(actionType);
            String userInfoJsonStr = UserInfo.encodeToJson(userInfo).toString();
            handleLoginSuccess(userInfoJsonStr, userInfo.getUpwd(), userInfo.getLoginType(), AccountLoginType.LoginWay.LOGIN_FAST_TOKEN, false, userInfo.getMobile());
        } else {
            SQLog.e(TAG + "用户信息无效, 登录失败");
            loginListener.onFailure(SDKError.NET_REQUEST_FAIL.code, "快速登录失败");
        }

    }

    /**
     * 微信登录
     */

    public void wechatLogin(final AccountListener listener) {
        this.loginListener = listener;
        socialLoginWechat(new OnAuthListener() {
            @Override
            public void onSuccess(PlatformType platformType, Bundle bundle) {
                Logger.info("onSuccess bundle:");
                String authCode = bundle.getString(SocialConstant.CODE);
                LoginTractionManager.trackWechatSuccess(authCode);
                //先调用微信授权，获取authCode
                requestWechatLogin(authCode);
            }

            @Override
            public void onCancel(PlatformType platformType) {
                ToastUtil.showToast(context, "取消微信登录");
                Logger.info("wechat login onCancel");
                if (loginListener != null) {
                    loginListener.onFailure(203, "取消微信登录");
                }
                LoginTractionManager.trackWechatFail("取消微信登录");
            }

            @Override
            public void onFailure(PlatformType platformType, String s) {
                ToastUtil.showToast(context, s);
                Logger.info("wechat login onFailure,msg:" + s);
                loginListener.onFailure(203, s);
                LoginTractionManager.trackWechatFail(s);
            }
        });


    }

    private void requestWechatLogin(final String authCode) {
        LoginRequestManager.wechatLogin(authCode, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                handleCaptcha(data, msg, new CaptchaDialog.VerifyListener() {
                    @Override
                    public void result(boolean success, String message) {
                        if (success) {
                            wechatLogin(loginListener);
                        } else {
                            if (loginListener != null) {
                                loginListener.onFailure(state, message);
                            }
                            BuglessAction.reportCatchException(new Exception("登录失败"), msg, data, BuglessAction.S_LOGIN);
                        }
                    }
                });
            }

            @Override
            public void onSuccess(String data) {
                handleLoginSuccess(data, "", LOGIN_WECHAT, false);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                LogUtil.i("快速登录失败 " + error);
                if (loginListener != null) {
                    loginListener.onFailure(code, errorMsg);
                }
            }
        });
    }

    private void socialLoginWechat(OnAuthListener authListener) {
        if (context instanceof Activity) {
            SocialApi.getInstance().authorize((Activity) context, PlatformType.WECHAT, authListener);
        } else {
            ToastUtil.showToast(context, "微信登录错误，请联系客服【10001】");
            Logger.info("context is not Activity");
            LoginTractionManager.trackWechatFail("context异常");
            if (loginListener != null) {
                loginListener.onFailure(203, "context异常");
            }
        }
    }


    public interface AccountListener {

        /**
         * 登录成功回调
         */
        void onSuccess(Map<String, String> account);

        /**
         * 失败回调
         */
        void onFailure(int code, String msg);
    }

    public interface VerifyCodeListener {

        void onSuccess();

        void onFailure(int code, String msg);
    }


    public interface AutoAccountListener {

        void onSuccess(AutoAccountBean autoAccountBean);

        void onFailure(int code, String msg);

    }


    /**
    *  多账号处理；账号类型，手机类型
     * */
    public interface MultiAccountHandler {
        void onMultiAccount(JSONArray accountList, String loginName, String pwd);
    }



    /**
     * 对密码进行加密
     *
     * @param pwd 密码
     * @return 加密后的密码
     */
    private String encryptPwd(String pwd) {
        String encodePwd = "";
        try {
            byte[] encryptBytes = AESUtil.encrypt(pwd, getEncodeKey());
            encodePwd = Base64.encodeToString(encryptBytes, Base64.NO_WRAP);
            LogUtil.i("加密后：" + encodePwd);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encodePwd;
    }

    /**
     * 对密码进行解密
     *
     * @param encryptPwd
     * @return
     */
    private String decryptPwd(String encryptPwd) {
        String decryptPwd = "";
        try {
            decryptPwd = AESUtil.decryptString(encryptPwd, getEncodeKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decryptPwd;
    }

    private String getEncodeKey() {
        String encodeKey = "";
        String appKey = ConfigManager.getInstance(context).getAppKey();
        if (!TextUtils.isEmpty(appKey)) {
            int length = appKey.length();
            //不足16位则补齐0
            if (length < 16) {
                StringBuilder sb = new StringBuilder(appKey);
                for (int i = 0; i < 16 - length; i++) {
                    sb.append("0");
                }
                encodeKey = sb.toString();
            } else {
                //满16位则截取前16位
                encodeKey = appKey.substring(0, 16);
            }
        }
        return encodeKey;
    }
}