package com.sy37sdk.account.floatview.request.bean;

import android.text.TextUtils;

import org.json.JSONObject;

public class FloatUserInfo {
    //昵称
    private String nickName;
    //头像
    private String avatar;
    //等级
    private String level;
    //uid
    private String uid;

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        return "FloatUserInfo{" +
                "nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", level='" + level + '\'' +
                ", uid='" + uid + '\'' +
                '}';
    }

    /**
     * 将适龄实体转换成json字符串
     *
     * @param floatUserInfo
     * @return
     */
    public static String objectToJson(FloatUserInfo floatUserInfo) {
        if (floatUserInfo == null) {
            return "";
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("nickname", floatUserInfo.getNickName());
            jsonObject.putOpt("avatar", floatUserInfo.getAvatar());
            jsonObject.putOpt("level", floatUserInfo.getLevel());
            jsonObject.putOpt("uid", floatUserInfo.getUid());
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

    }

    /**
     * json串解析成适龄实体
     *
     * @param json
     * @return
     */
    public static FloatUserInfo jsonToObject(String json) {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            JSONObject object = new JSONObject(json);
            String nickname = object.optString("nickname");
            String avatar = object.optString("avatar");
            String level = object.optString("level");
            String uid = object.optString("uid");
            FloatUserInfo floatUserInfo = new FloatUserInfo();
            floatUserInfo.setNickName(nickname);
            floatUserInfo.setAvatar(avatar);
            floatUserInfo.setLevel(level);
            floatUserInfo.setUid(uid);
            return floatUserInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从服务端获取到适龄配置后解析
     *
     * @param json
     * @return
     */
    public static FloatUserInfo parse(String json) {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            JSONObject object = new JSONObject(json);
            String nickname = object.optString("nickname");
            String avatar = object.optString("avatar");
            String level = object.optString("level");
            String uid = object.optString("uid");
            FloatUserInfo floatUserInfo = new FloatUserInfo();
            floatUserInfo.setNickName(nickname);
            floatUserInfo.setAvatar(avatar);
            floatUserInfo.setLevel(level);
            floatUserInfo.setUid(uid);
            return floatUserInfo;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
