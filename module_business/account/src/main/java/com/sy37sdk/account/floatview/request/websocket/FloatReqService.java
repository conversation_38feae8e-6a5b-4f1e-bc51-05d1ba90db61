package com.sy37sdk.account.floatview.request.websocket;

import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.parse.ResponseDataParse;

public class FloatReqService extends ReqWrapperHandler {

    public FailedHolder<ResponseDataParse> requestFloatWindowRedDotPlatformMsg(FloatWindowRedDotMsgPlatformReq msgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(msgReq, successListener);
    }

    public FailedHolder<ResponseDataParse> requestFloatWindowRedDotPidGidMsg(FloatWindowRedDotMsgPidGidReq msgReq, SuccessListener<ResponseDataParse> successListener){
        return reqImpl(msgReq, successListener);
    }
}
