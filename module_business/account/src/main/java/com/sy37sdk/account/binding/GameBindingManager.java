package com.sy37sdk.account.binding;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.google.sqgson.Gson;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.ActivityLifecycleAdapter;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.binding.view.GameBindingDialog;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: zls
 * @date: 2025/8/7
 */
public class GameBindingManager {

    private static final String TAG = "MMOBindingUtils";

    public static final String BINDING_PARAMS = "bindingParams";

    private static volatile GameBindingManager sInstance;

    private MMOBindingCallback mCallback;

    //app传递完整参数
    private String mBindingParams = "";

    //跳转app包名
    private String mCallingPackageName = "";

    //弹窗展示内容
    private String mDialogContent = "";

    private Map<String, String> mApiMap;
    private volatile boolean mIsLogin = false;

    private Context mContext;

    private WeakReference<Activity> mWeakActivity;

    private GameBindingDialog mGameBindingDialog;

    private ActivityLifecycleAdapter mAppStartCallback = new ActivityLifecycleAdapter() {
        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            super.onActivityCreated(activity, savedInstanceState);
            try {
                LogUtil.d("MMOBindingUtils onActivityCreated ");
                String bindingParams = "";
                if (savedInstanceState != null) {
                    bindingParams = savedInstanceState.getString(GameBindingManager.BINDING_PARAMS);
                }
                if (TextUtils.isEmpty(bindingParams)) {
                    Intent intent = activity.getIntent();
                    if (intent != null) {
                        bindingParams = intent.getStringExtra(GameBindingManager.BINDING_PARAMS);
                    }
                }
                handleBinding(activity, bindingParams);
            } catch (Exception e) {
                LogUtil.d(TAG, "onActivityCreated error = " + e.getMessage());
            }
        }
    };

    private GameBindingManager() {
        // 私有构造函数
    }

    public static GameBindingManager getInstance() {
        if (sInstance == null) {
            synchronized (GameBindingManager.class) {
                if (sInstance == null) {
                    sInstance = new GameBindingManager();
                }
            }
        }
        return sInstance;
    }

    public void init(Object application) {
        if (application != null) {
            if (application instanceof Application) {
                mContext = ((Application) application).getApplicationContext();
                LogUtil.e(TAG, "registerActivityLifecycleCallbacks mAppStartCallback");
                ((Application) application).registerActivityLifecycleCallbacks(mAppStartCallback);
            } else {
                LogUtil.e(TAG, "registerActivityLifecycleCallbacks mAppStartCallback error");
            }
        }
    }

    public void handlerNewIntentEvent(Activity activity, Intent intent) {
        if (activity != null && intent != null) {
            handleBinding(activity, intent.getStringExtra(GameBindingManager.BINDING_PARAMS));
        } else {
            LogUtil.e(TAG, "activity or intent is null");
        }
    }

    public SQResultListener getBindingLoginResultListener(GameBindingLoginType type, final SQResultListener listener) {
        if (type == GameBindingLoginType.TYPE_CHANGE_ACCOUNT || type == GameBindingLoginType.TYPE_LOGIN) {
            //切换账号场景，游戏会退到选服页，会重新拉起登录，此时为非登录场景
            mIsLogin = false;
        }
        return new SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                mIsLogin = true;
                LogUtil.d(TAG, "getBindingLoginResultListener onSuccess");
                if (listener != null) {
                    listener.onSuccess(bundle);
                }
                executeCallback();
            }

            @Override
            public void onFailture(int code, String msg) {
                LogUtil.d(TAG, "getBindingLoginResultListener onFailture code = " + code + " message = " + msg);
                if (listener != null) {
                    listener.onFailture(code, msg);
                }
            }
        };
    }

    /**
     * 处理绑定逻辑
     */
    public void handleBinding(Activity activity, String bindingParams) {
        try {
            if (TextUtils.isEmpty(bindingParams)) {
                //过滤非APP跳转过来的场景
                LogUtil.d(TAG, "bindingParams is null");
                return;
            }
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.SDK_GAME_BINDING_INVOKE);
            mWeakActivity = new WeakReference<>(activity);
            mBindingParams = bindingParams;
            LogUtil.d(TAG, "handleBinding bindingParams = " + bindingParams);
            parseBindingParams(mBindingParams);
            LogUtil.d(TAG, "获取到的调用包名: " + mCallingPackageName);
            //校验成功，存储callback，登录成功之后再回调
            if (mIsLogin) {
                showBindingDialog();
            } else {
                mCallback = new MMOBindingCallback() {
                    @Override
                    public void executeCallback() {
                        LogUtil.d(TAG, "handlerBinding bindingParams = " + mBindingParams + " callingPackageName = " + mCallingPackageName);
                        showBindingDialog();
                    }
                };
            }
        } catch (Exception e) {
            LogUtil.d(TAG, "handlerBinding bindingParams error = " + e.getMessage());
        }
    }

    private void parseBindingParams(String bindingParams) {
        try {
            JSONObject bindingJson = new JSONObject(bindingParams);
            JSONObject extParams = bindingJson.optJSONObject("extParams");
            if (extParams != null) {
                mDialogContent = extParams.optString("dialogContent");
                mCallingPackageName = extParams.optString("callingPackageName");
            }
            JSONObject apiParams = bindingJson.optJSONObject("apiParams");
            if (apiParams != null) {
                //转map
                Gson gson = new Gson();
                mApiMap = gson.fromJson(apiParams.toString(), Map.class);
            }
        } catch (Exception e) {
            LogUtil.d(TAG, "parseBindingParams error = " + e.getMessage());
        }
    }

    /**
     * 登录成功之后执行绑定回调，跳转回绑定页面
     */
    public void executeCallback() {
        if (mCallback != null) {
            mCallback.executeCallback();
        }
    }

    public void setIsLogin(boolean isLogin) {
        mIsLogin = isLogin;
    }

    /**
     * 清空数据
     */
    public void clearData() {
        mBindingParams = "";
        mCallingPackageName = "";
        mDialogContent = "";
        mCallback = null;
        mGameBindingDialog = null;
        mApiMap = null;
    }

    /**
     * 展示绑定弹窗
     */
    private void showBindingDialog() {
        if (mWeakActivity == null || mWeakActivity.get() == null) {
            trackBindingFail(GameBindingErrorType.TYPE_ACTIVITY_NULL_ERROR, "activity is null");
            return;
        }
        Activity activity = mWeakActivity.get();
        if (activity.isFinishing() || activity.isDestroyed()) {
            trackBindingFail(GameBindingErrorType.TYPE_ACTIVITY_LIFECYCLE_ERROR, "activity lifecycle error");
            return;
        }
        if (mGameBindingDialog != null) {
            //避免上一次dialog未销毁
            try {
                mGameBindingDialog.dismiss();
            } catch (Exception e) {
                LogUtil.e(TAG, "dismiss dialog error = " + e.getMessage());
            }
        }
        GameBindingDialog dialog = new GameBindingDialog(activity, mDialogContent, new GameBindingDialog.MMOBindingDialogCallback() {

            @Override
            public void onConfirm() {
                requestUserRelate(new SqHttpCallback<JSONObject>() {
                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        LogUtil.d(TAG, "requestUserRelate onSuccess data = " + jsonObject);
                        jumpToDlyzApp(mContext, mCallingPackageName);
                        clearData();
                    }

                    @Override
                    public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                        LogUtil.d(TAG, "requestUserRelate onFailure code = " + code + " errorMsg = " + errorMsg);
                        clearData();
                        String failMsg = "code = " + code + ", msg = " + errorMsg;
                        trackBindingFail(GameBindingErrorType.TYPE_NETWORK_ERROR, failMsg);
                    }

                    @Override
                    public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                        LogUtil.d(TAG, "requestUserRelate onResponseStateError state = " + state + " msg = " + msg);
                        clearData();
                        String failMsg = "code = " + httpStatus + ", msg = " + msg;
                        trackBindingFail(GameBindingErrorType.TYPE_NETWORK_ERROR, failMsg);
                    }
                });
            }

            @Override
            public void onCancel() {
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.SDK_GAME_BINDING_CANCEL);
                clearData();
            }
        });
        mGameBindingDialog = dialog;
        dialog.show();
    }

    private void jumpToDlyzApp(Context context, String packageName) {
        try {
            LogUtil.d(TAG, "jumpToDlyzApp 开始执行跳转逻辑");
            if (context == null || TextUtils.isEmpty(packageName)) {
                String failMsg = "context = null 或 packageName = null，无法跳转";
                LogUtil.e(TAG, failMsg);
                trackBindingFail(GameBindingErrorType.TYPE_JUMP_ERROR, failMsg);
                return;
            }
            LogUtil.d(TAG, "context 不为空，开始获取启动Intent");
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                LogUtil.d(TAG, "成功获取到启动Intent，准备跳转到: " + packageName);
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(launchIntent);
                LogUtil.d(TAG, "跳转命令已执行");
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.SDK_GAME_BINDING_SUCCESS);
            } else {
                String failMsg = "未找到包名对应的启动Intent: " + packageName + "，可能应用未安装";
                LogUtil.d(TAG, failMsg);
                trackBindingFail(GameBindingErrorType.TYPE_JUMP_ERROR, failMsg);
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "jumpToDlyzApp 跳转失败: " + e.getMessage());
            e.printStackTrace();
            trackBindingFail(GameBindingErrorType.TYPE_JUMP_ERROR, e.getMessage());
        }
    }

    private void requestUserRelate(final SqHttpCallback<JSONObject> callback) {
        HashMap<String, String> formMap = new HashMap<>();
        try {
            String oaid = "";
            String oaidJsonString = DeviceUtils.getOaid(mContext);
            if (!TextUtils.isEmpty(oaidJsonString)) {
                JSONObject oaidObj = new JSONObject(oaidJsonString);
                oaid = oaidObj.optString("oaid");
            }
            String token = AccountCache.getToken(mContext);
            if (mApiMap != null) {
                formMap.putAll(mApiMap);
            }
            formMap.put("oaid", oaid);
            formMap.put("token", token);
            LogUtil.d(TAG, "oaid = " + oaid + ", token = " + token);
            SqRequest.of(UrlConstant.URL_USER_RELATE)
                    .formParams(formMap)
                    .addParamsTransformer(new CommonParamsV3())
                    .signV3()
                    .post(callback);
        } catch (Exception e) {
            clearData();
            LogUtil.e(TAG, "requestUserRelate error = " + e.getMessage());
            trackBindingFail(GameBindingErrorType.TYPE_NETWORK_ERROR, e.getMessage());
        }
    }

    private void trackBindingFail(int code, String msg) {
        HashMap<String, String> failMap = new HashMap<>();
        failMap.put(SqTrackKey.fail_code, String.valueOf(code));
        failMap.put(SqTrackKey.reason_fail, msg);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.SDK_GAME_BINDING_FAIL, failMap);
    }

    /**
     * 判断是否游戏绑定跳转过来的，callback不为空的时候，登陆成功之后会跳转回app
     * @return
     */
    public boolean isGameBinding() {
        return mCallback != null;
    }

    interface MMOBindingCallback {
        void executeCallback();
    }

    /**
     * 登录类型
     */
    public enum GameBindingLoginType {
        TYPE_LOGIN,
        TYPE_CHANGE_ACCOUNT,
        TYPE_SWITCH
    }

    public static class GameBindingErrorType {
        public static final int TYPE_ACTIVITY_NULL_ERROR = 1001;
        public static final int TYPE_ACTIVITY_LIFECYCLE_ERROR = 1002;

        public static final int TYPE_NETWORK_ERROR = 1003;

        public static final int TYPE_JUMP_ERROR = 1004;
    }
}
