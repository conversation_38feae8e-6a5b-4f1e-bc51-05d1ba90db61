package com.sy37sdk.account.view.base.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sqwan.base.L;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.UrlConstant;
import java.util.HashMap;
import java.util.Map;

public class AccountLoginAttachView extends RelativeLayout {

    private Context mContext;

    private View rootView;

    private TextView tvVersion;

    private ImageView ivCustomer;

    public AccountLoginAttachView(Context context) {
        this(context, null);
    }

    public AccountLoginAttachView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AccountLoginAttachView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        LogUtil.i("AccountLoginAttachView mcontext:" + mContext);
        int sysq_account_login_dialog_attach = SqResUtils.getLayoutId(mContext, "sysq_account_login_dialog_attach");
        rootView = View.inflate(context, sysq_account_login_dialog_attach, this);
        initView();
        initEvent();
    }


    private void initView() {
        tvVersion = rootView.findViewById(SqResUtils.getId(mContext, "tv_sversion"));
        ivCustomer = rootView.findViewById(SqResUtils.getId(mContext, "iv_customer"));
        LogUtil.i("客服页面scut:" + ConfigManager.getInstance(mContext).getLoginCode() );
        //判断是否简版，简版不显示客服入口
        if(isSimplifiedSDK()){
            ivCustomer.setVisibility(GONE);
        }
        tvVersion.setText("v" + VersionUtil.getVersionStr(mContext));
        if (!TextUtils.isEmpty(UrlConstant.KEFU_ICON_URL)) {
            AsyncImageLoader loader = new AsyncImageLoader(getContext());
            loader.loadDrawable(UrlConstant.KEFU_ICON_URL, ivCustomer, (imageDrawable, imageView, imageUrl) -> {
                if (imageDrawable != null) {
                    imageView.setImageBitmap(imageDrawable);
                } else {
                    imageView.setBackgroundResource(SqResUtils.getDrawableId(mContext, "sysq_icon_customer"));
                }

            });
        } else {
            ivCustomer.setBackgroundResource(SqResUtils.getDrawableId(mContext, "sysq_icon_customer"));
        }

    }

    private void initEvent() {
        if(!isSimplifiedSDK()){
            ivCustomer.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    showKefuPage();
                }
            });
        }
    }

    private void showKefuPage() {
        if (TextUtils.isEmpty(UrlConstant.KEFU_GUIDE_URL)) {
            ToastUtil.showToast("功能升级中");
        } else {
            Context context = L.getActivity();
            SQAppConfig sqAppConfig = ConfigManager.getInstance(context).getSQAppConfig();
            Map<String, String> params = new HashMap<>();
            params.put("pid", sqAppConfig.getPartner());
            params.put("gid", sqAppConfig.getGameid());
            String url = UrlUtils.appendUrlParams(UrlConstant.KEFU_GUIDE_URL, params);
            AppUtils.toSQWebUrlNoAppend(context, url, "", false);
        }
    }

    //判断是否简版
    private boolean isSimplifiedSDK(){
        return ConfigManager.getInstance(mContext).isSimplifiedSDK();
    }
}
