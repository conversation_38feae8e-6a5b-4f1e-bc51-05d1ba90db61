package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;

import com.sq.tools.utils.ScreenUtils;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.NavigationUtils;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.SqResUtils;

import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import notchtools.geek.com.notchtools.NotchTools;

import static com.sy37sdk.account.floatview.FloatViewUtils.FloatViewSpkey.key_floatview_position;
import static com.sy37sdk.account.floatview.ScreenOrientationHelper.ORIENTATION_TYPE_0;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-01-21 18:35
 */
public class FloatViewUtils {
    public static boolean isUiFlagHideNavigation(Context context) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            Window window = activity.getWindow();
            View decorView = window.getDecorView();
            return (decorView.getSystemUiVisibility() & View.SYSTEM_UI_FLAG_HIDE_NAVIGATION) == View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
        }
        return false;

    }
    private static String setKeyWithViewTag(View view,String key){
        return view.getClass().getSimpleName() + key;
    }
    public static boolean isShakeTips(Context context){
        return SpUtils.get(context).getBoolean(FloatViewUtils.FloatViewSpkey.key_shake_tips,true);
    }

    public static void setShankeTips(Context context,boolean flag){
        SpUtils.get(context).put(FloatViewUtils.FloatViewSpkey.key_shake_tips,flag);
    }

    public static void setFloatViewPos(Context context,View view,int x,int y,int orientationType){
        FloatViewPosConfig config = new FloatViewPosConfig(x,y,orientationType);
        String configString = config.toJsonString();
        SpUtils.get(context).put(setKeyWithViewTag(view,key_floatview_position),configString);
    }

    public static void resetFloatViewPos(Context context,View view){
        SpUtils.get(context).put(setKeyWithViewTag(view,key_floatview_position),"");
    }

    public static FloatViewPosConfig getFloatViewPositionConfig(Context context,View view){
        String configString =  SpUtils.get(context).getString(setKeyWithViewTag(view,key_floatview_position) ,"");
        if (TextUtils.isEmpty(configString)) {
            return new FloatViewPosConfig();
        }
        try {
            Class clazz = FloatViewPosConfig.class;
            Object obj = clazz.getConstructor().newInstance();
            JSONObject jsonObject = new JSONObject(configString);
            Iterator<String> iterator = jsonObject.keys();
            while (iterator.hasNext()){
                String key = iterator.next();
                clazz.getField(key).set(obj,jsonObject.optInt(key));

            }
            return (FloatViewPosConfig) obj;
        } catch (Exception e) {
            e.printStackTrace();
            return new FloatViewPosConfig();
        }
    }

    public static int getBottomDeleteHeight(Context context){
        if (DisplayUtil.isLandscape(context)) {
            return SqResUtils.getDimensionPixelSize(context, "sysq_float_bottomdelete_height_landscape");
        }else{
            return SqResUtils.getDimensionPixelSize(context, "sysq_float_bottomdelete_height_portrait");
        }
    }

    public static int getBottomDeleteWidth(Context context) {
        if (DisplayUtil.isLandscape(context)) {
            return (int) (ScreenUtils.getScreenWidth(context) * 0.55f);
        } else {
            return (int) (ScreenUtils.getScreenWidth(context) * 0.88f);
        }
    }

    public static int getViewXOnScreen(View view){
        return getViewLocationOnScreen(view)[0];

    }
    public static int getViewYOnScreen(View view){
        return getViewLocationOnScreen(view)[1];
    }
    public static int[] getViewLocationOnScreen(View view){
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        return location;
    }
    public interface FloatViewSpkey {
        String key_shake_tips = "key_shake_tips";
        String key_floatview_position = "key_floatview_position";


    }

    public static class FloatViewPosConfig {
        public int x=-1;
        public int y=-1;
        public int orientationType = ORIENTATION_TYPE_0;

        public FloatViewPosConfig(int x, int y, int orientationType) {
            this.x = x;
            this.y = y;
            this.orientationType = orientationType;
        }

        public FloatViewPosConfig() {
        }

        public String toJsonString(){
            try {
                Map params = new HashMap<String,Object>();
                for (Field declaredField : this.getClass().getDeclaredFields()) {
                        Object value = declaredField.get(this);
                        params.put(declaredField.getName(),value);
                }
                return new JSONObject(params).toString();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
                return "";
            }

        }
    }
    public static class FloatViewConfig {
        public String TAG = this.getClass().getSimpleName();
        public int regionWidth;
        public int regionHeight;
        public int marginLeft;
        public int marginTop;
        public int screenType;
        public boolean isNotchScreen;
        public int _notchHeight;
        public boolean ignoreNotchScreen;
        public int navigationBarHeight;
        public boolean isUiFlagHideNavigation;
        public View contentView,decorView;

        @Override
        public String toString() {
            return "FloatViewConfig{" +
                    ", regionWidth=" + regionWidth +
                    ", regionHeight=" + regionHeight +
                    ", marginLeft=" + marginLeft +
                    ", marginTop=" + marginTop +
                    ", screenType=" + screenType +
                    ", isNotchScreen=" + isNotchScreen +
                    ", _notchHeight=" + _notchHeight +
                    ", ignoreNotchScreen=" + ignoreNotchScreen +
                    ", navigationBarHeight=" + navigationBarHeight +
                    ", isUiFlagHideNavigation=" + isUiFlagHideNavigation +
                    '}';
        }

        public FloatViewConfig(Context context){
            screenType = ScreenOrientationHelper.getOrientationType(context);
            buildConfig(context);
            LogUtil.i(TAG,toString());
        }

        private void buildConfig(Context context){
            if (context instanceof Activity) {
                Activity activity = (Activity) context;
                Window window = activity.getWindow();
                decorView  = window.getDecorView();
                contentView = decorView.findViewById(android.R.id.content);
                int decorViewx = getViewXOnScreen(decorView);
                int decorViewy = getViewYOnScreen(decorView);
                int contentViewx = getViewXOnScreen(contentView);
                int contentViewy = getViewYOnScreen(contentView);
                LogUtil.i(TAG,String.format("decorViewx:%d,decorViewy:%d,contentViewx:%d,contentViewy:%d",decorViewx,decorViewy,contentViewx,contentViewy));
                LogUtil.i(TAG,String.format("decorViewWidth:%d,decorViewHeight:%d,contentViewWidth:%d,contentViewHeight:%d",decorView.getWidth(),decorView.getHeight(),contentView.getWidth(),contentView.getHeight()));
                navigationBarHeight = NavigationUtils.getNavigationBarHeight(context);
                isNotchScreen = NotchTools.getFullScreenTools().isNotchScreen(window);
                _notchHeight = NotchTools.getFullScreenTools().getNotchHeight(window);
                ignoreNotchScreen = NotchTools.getFullScreenTools().ignoreNotchScreen(window,decorView.getWidth(),decorView.getHeight());
                regionWidth = decorView.getWidth();
                regionHeight = decorView.getHeight();
                marginLeft =  contentViewx-decorViewx;
                marginTop = contentViewy - decorViewy;
                isUiFlagHideNavigation = isUiFlagHideNavigation(context);
                if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_270) {
                    if (isNotchScreen) {
                        if (!ignoreNotchScreen) {
                            marginLeft += _notchHeight;
                        }
                    }
                    if (NavigationUtils.hasNavigationBar(context)) {

                        if (!isUiFlagHideNavigation) {
                            regionWidth -=navigationBarHeight;
                        }

                    }
                } else if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_90) {
                    if (isNotchScreen) {
                        if (!ignoreNotchScreen) {
                            regionWidth -= _notchHeight;

                        }

                    }
                    if (NavigationUtils.hasNavigationBar(context)) {
                        if (!isUiFlagHideNavigation) {
                            marginLeft +=navigationBarHeight;
                        }

                    }

                }else if (screenType == ScreenOrientationHelper.ORIENTATION_TYPE_0) {
                    if (NavigationUtils.hasNavigationBar(context)) {
                        if (!isUiFlagHideNavigation) {
                            regionHeight -=navigationBarHeight;
                        }

                    }
                }

            }
        }


    }
    //(x,y)是否在view的区域内
    public static boolean isTouchPointInView(View view, int x, int y) {
        if (view == null) {
            return false;
        }
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int left = location[0];
        int top = location[1];
        int right = left + view.getMeasuredWidth();
        int bottom = top + view.getMeasuredHeight();
        //view.isClickable() &&
        if (y >= top && y <= bottom && x >= left
                && x <= right) {
            return true;
        }
        return false;
    }

}
