package com.sy37sdk.account.view;

import android.content.Context;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ListView;
import android.widget.PopupWindow;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.config.IConfigMod;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.UserInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/23
 */
public class PopListHelper {

    private PopupWindow mPopupWindow;
    private List<UserInfo> accountList;
    private AccountAdapter accountAdapter;

    private static final float MAX_VISIBLE_ITEM = 3.5f;

    /**
     * @param itemWidth  px
     * @param itemHeight dp
     */
    public void initAccountList(final Context context, List<UserInfo> accounts,
                                int itemWidth, int itemHeight, final ItemClickListener clickListener) {
        this.initAccountList(context, accounts, itemWidth, itemHeight, clickListener, null);
    }

    public void initAccountList(final Context context, final List<UserInfo> accounts,
                                int itemWidth, final int itemHeight, final ItemClickListener clickListener, final PopupWindow.OnDismissListener dismissListener) {
        this.accountList = accounts;
        ListView listView = new ListView(context);
        listView.setCacheColorHint(0xffffffff);
        listView.setDivider(null);
        listView.setFocusable(false);
        listView.setVerticalScrollBarEnabled(false);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                int index = accountList.size() - position - 1;
                if (mPopupWindow != null) {
                    mPopupWindow.dismiss();
                }
                UserInfo userInfo = accountList.get(index);
                clickListener.onSelect(userInfo);
            }
        });
        float popViewHeight = 0f;
        if (accountList != null && !accountList.isEmpty()) {
            if (accountList.size() < MAX_VISIBLE_ITEM) {
                popViewHeight = itemHeight * accountList.size();
            } else {
                popViewHeight = (MAX_VISIBLE_ITEM * itemHeight);
            }
        }
        mPopupWindow = new PopupWindow(listView, itemWidth, DisplayUtil.dip2px(context, popViewHeight), true);
        mPopupWindow.setFocusable(true);
        mPopupWindow.setOutsideTouchable(true);
        mPopupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        int bgResourceId = SqResUtils.getIdByName("sysq_bg_account_pop", "drawable", context);
        mPopupWindow.setBackgroundDrawable(context.getResources().getDrawable(bgResourceId));
        accountAdapter = new AccountAdapter(context, accountList, new AccountAdapter.DeleteListener() {
            @Override
            public void onDelete(int index) {
                UserInfo deleteUser = accountList.get(index);
                if (deleteUser.getUname().equals(AccountCache.getUsername(context))) {
                    IConfigMod configMod = ModHelper.getConfig();
                    if (configMod!=null) {
                        configMod.getCommonConfig().setUserId("");
                    }
                    AccountCache.setUserInfo(context,null);
                }
                accountList.remove(index);
                accountAdapter.notifyDataSetChanged();
                if (clickListener != null) {
                    clickListener.onDelete(deleteUser);
                }
                if(accountList==null||accountList.size()<1){
                    mPopupWindow.dismiss();
                }
            }
        });

        listView.setAdapter(accountAdapter);
        accountAdapter.notifyDataSetChanged();
        mPopupWindow.setOnDismissListener(dismissListener);
    }


    public void showAccountList(View anchor) {
        //需要判断accountList是否为空对象，读取账号文件的时候可能返回null
        if (anchor != null && mPopupWindow != null && accountList != null && accountList.size() > 0) {
            mPopupWindow.showAsDropDown(anchor, 0, DisplayUtil.dip2px(anchor.getContext(), 6));
        } else {
            LogUtil.i("account list size is 0");
        }
    }

    public List<UserInfo> getAccountList(){
        return accountList;
    }

    public void hideAccountList() {
        if (mPopupWindow != null && mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return mPopupWindow != null && mPopupWindow.isShowing();
    }

    public interface ItemClickListener {

        void onSelect(UserInfo userInfo);

        void onDelete(UserInfo userInfo);
    }

}
