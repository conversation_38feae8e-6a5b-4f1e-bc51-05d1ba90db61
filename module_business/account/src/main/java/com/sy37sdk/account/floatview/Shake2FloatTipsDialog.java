package com.sy37sdk.account.floatview;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.social.sdk.common.util.DensityUtil;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.SqResUtils;

public class Shake2FloatTipsDialog extends Dialog {
    public Shake2FloatTipsDialog(Context context) {
        super(context, SqResUtils.getStyleId(context, "CustomDialog"));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(getContext(), "sy37_dialog_shake2floattips"));

        Window window = getWindow();

        if (window != null) {
            WindowManager.LayoutParams params = getWindow().getAttributes();
            params.width = DisplayUtil.dip2px(getContext(),280);
            window.setAttributes(params);
        }

        View decorView = null;
        if (window != null) {
            decorView = window.getDecorView();
        }

        if (decorView != null) {
            // 避免弹窗的时候底部导航栏显示出来
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_FULLSCREEN |
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY |
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        }

        final LinearLayout ll = findViewById(SqResUtils.getId(getContext(), "ll_float_shake_status"));
        final int textSize = DensityUtil.dip2px(getContext(),15);
        TextView tvTips = findViewById(SqResUtils.getId(getContext(), "tv_tips"));
        CharSequence tips = SpanUtil.concat(SpanUtil.getFontString("摇1秒",textSize, Color.parseColor("#FFA100"),true),
                SpanUtil.getFontString(" 显示悬浮球",textSize, Color.BLACK,true));
        tvTips.setText(tips);
        ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ll.setSelected(!ll.isSelected());
            }
        });
        findViewById(SqResUtils.getId(getContext(), "btnOk")).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FloatViewUtils.setShankeTips(getContext(),!ll.isSelected());
                dismiss();
            }
        });

    }
}