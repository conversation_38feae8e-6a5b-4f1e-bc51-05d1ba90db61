package com.sy37sdk.account.view.uifast.presenter;

import android.content.Context;
import android.view.View;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AccountLogic.AccountListener;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.view.uifast.view.IMultiSelectView;

/**
 * @author: gsp
 * @date: 2025/3/24
 * @desc: 多账号选择
 */
public class MultiAccountSelectPresenter extends BaseAccountPagerPresenter<IMultiSelectView> implements
    IMultiAccountSelectPresenter {

    public MultiAccountSelectPresenter(Context context, IMultiSelectView view) {
        super(context, view);
    }

    @Override
    public View getView() {
        return (View) mView;
    }


    @Override
    public void login(String loginType, String uname, String pwd, AccountListener listener) {
        if ("account".equals(loginType)) {
            AccountLogic.getInstance(getView().getContext()).accountLogin(uname, pwd, listener);
        } else if ("phone_pwd".equals(loginType)) {
            AccountLogic.getInstance(getView().getContext()).phoneLoginPwd(uname, pwd, listener);
        } else {
            listener.onFailure(-1, "check_account_list 接口返回错误");
        }
    }
}
