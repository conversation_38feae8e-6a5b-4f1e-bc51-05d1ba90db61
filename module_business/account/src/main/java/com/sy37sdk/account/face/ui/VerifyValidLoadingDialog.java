package com.sy37sdk.account.face.ui;

import android.content.Context;
import android.os.Bundle;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;

public class VerifyValidLoadingDialog extends BaseDialog {
    private Context mContext;

    public VerifyValidLoadingDialog(Context context) {
        super(context);
        this.mContext=context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext,"sysq_face_valid_dialog"));
    }
}
