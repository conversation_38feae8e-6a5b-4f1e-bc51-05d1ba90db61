package com.sy37sdk.account.floatview;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;

/**
 * 悬浮窗item
 * @deprecated 根据需求文档与讨论: https://vulenhv772.feishu.cn/docs/doccnanNokzpS9Ci9jWmxSn8Q8d#
 * 目前SDK暂时不再需要支持悬浮窗红点，会放在 3.7.8 后续的版本进行实现，此处实现已不再被调用
 * 保留此项实现以供后续版本实现时参考使用
 */
@Deprecated
public class SyFloatItemView extends FrameLayout {

    private TextView mTextView;
    private ImageView mRedDotView;

    private String mName;
    private int mIconId;

    public SyFloatItemView(@NonNull Context context) {
        this(context, null);
    }

    public SyFloatItemView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SyFloatItemView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        int syName = SqResUtils.getIdByName("sy_name", "attr", context);
        int syIcon = SqResUtils.getIdByName("sy_icon", "attr", context);
        if (attrs != null) {
            for (int i = 0; i < attrs.getAttributeCount(); i++) {
                int attrId = attrs.getAttributeNameResource(i);
                if (attrId == syName) {
                    mName = context.getString(attrs.getAttributeResourceValue(i, -1));
                } else if (attrId == syIcon) {
                    mIconId = attrs.getAttributeResourceValue(i, -1);
                }
            }
        }
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        int layoutId = SqResUtils.getIdByName("sy37_float_item", "layout",getContext());
        LayoutInflater.from(getContext()).inflate(layoutId, this, true);
        mTextView = findViewById(SqResUtils.getIdByName("sy_text", "id", getContext()));
        mRedDotView = findViewById(SqResUtils.getIdByName("sy_red_dot", "id", getContext()));
        mTextView.setText(mName);
        mTextView.setCompoundDrawablesWithIntrinsicBounds(null, getContext().getResources().getDrawable(mIconId), null, null);
    }

    // 客户端写死不显示红点
    public void setRedDotVisible(boolean redDotVisible) {
//        if (redDotVisible) {
//            mRedDotView.setVisibility(VISIBLE);
//        } else {
//            mRedDotView.setVisibility(GONE);
//        }
    }

    public void setVisible(boolean visible) {
        if (visible) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    public boolean isVisible() {
        if (getVisibility() == VISIBLE) {
            return true;
        }
        return false;
    }

    public boolean isRedDotVisible() {
        if (mRedDotView.getVisibility() == VISIBLE) {
            return true;
        }
        return false;
    }
}
