package com.sy37sdk.account.floatview.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.floatview.MenuConfig;
import com.sy37sdk.account.floatview.data.RedDot;

import java.util.ArrayList;
import java.util.List;

public class FloatItemAdapter extends RecyclerView.Adapter<FloatItemAdapter.ViewHolder> {

    private Context context;

    private List<MenuConfig> dataList = new ArrayList<>();

    private List<RedDot> redDots = new ArrayList<>();

    private int titleTextColor = 0;

    public FloatItemAdapter(Context context) {
        this.context = context;
    }

    public void setDataList(List<MenuConfig> list) {
        dataList.clear();
        dataList.addAll(list);
        notifyDataSetChanged();
    }

    public void setRedDots(List<RedDot> redDots) {
        this.redDots = redDots;
        notifyDataSetChanged();
    }

    public void setTitleColor(String color) {
        if (TextUtils.isEmpty(color)) {
            return;
        }
        try {
            titleTextColor = Color.parseColor(color);
        } catch (Exception e) {
            e.printStackTrace();
            BuglessAction.reportCatchException(e, "解析颜色错误 setTitleColor : " + color,
                BuglessAction.COMMON_ERROR);
        }
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        View view = LayoutInflater.from(context).inflate(SqResUtils.getLayoutId(context, "sy37_float_menu_item"), viewGroup, false);
        return new FloatItemAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, int position) {
        final MenuConfig menuConfig = dataList.get(position);
        AsyncImageLoader loader = new AsyncImageLoader(context);
        loader.loadDrawable(menuConfig.iconUrl, holder.ivMenu, new AsyncImageLoader.ImageCallback() {

            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                holder.ivMenu.setImageBitmap(imageDrawable);
            }
        });
        holder.tvMenu.setText(menuConfig.title);
        int findPos = -1;
        for (int i = 0; i < redDots.size(); i++) {
            if (redDots.get(i).getTitle().equals(menuConfig.title)) {
                findPos = i;
                break;
            }
        }
        if (findPos != -1) {
            RedDot redDot = redDots.get(findPos);
            int num = redDot.getNum();
            if (num > 0) {
                //数字红点
                if (redDot.needShowNum()) {
                    holder.tvNum.setText(num < 10 ? redDot.getNum() + "" : "9+");
                    holder.tvNum.setVisibility(View.VISIBLE);
                    holder.ivRedDot.setVisibility(View.GONE);
                } else {
                    holder.ivRedDot.setVisibility(View.VISIBLE);
                    holder.tvNum.setVisibility(View.GONE);
                }
            } else {
                holder.ivRedDot.setVisibility(View.GONE);
                holder.tvNum.setVisibility(View.GONE);
            }
        } else {
            holder.ivRedDot.setVisibility(View.GONE);
            holder.tvNum.setText("");
            holder.tvNum.setVisibility(View.GONE);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (itemClickListener != null) {
                    itemClickListener.ItemOnClick(menuConfig);
                }
            }
        });
        if (titleTextColor != 0) {
            holder.tvMenu.setTextColor(titleTextColor);
        }
    }

    @Override
    public int getItemCount() {
        return dataList == null ? 0 : dataList.size();
    }


    class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvNum;
        ImageView ivMenu;
        TextView tvMenu;

        ImageView ivRedDot;

        ViewHolder(View itemView) {
            super(itemView);
            ivRedDot = itemView.findViewById(SqResUtils.getId(context, "iv_red_dot"));
            tvNum = itemView.findViewById(SqResUtils.getId(context, "tv_red_dot"));
            ivMenu = itemView.findViewById(SqResUtils.getId(context, "menu_item_icon"));
            tvMenu = itemView.findViewById(SqResUtils.getId(context, "menu_item_text"));
        }
    }

    private FloatItemAdapter.ItemClickListener itemClickListener;

    public interface ItemClickListener {
        void ItemOnClick(MenuConfig menuConfig);
    }

    public void setItemClickListener(FloatItemAdapter.ItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }
}
