package com.sy37sdk.account.view.base.view;

import android.app.Activity;
import android.content.Context;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sq.diagnostic.assistant.DiagnosticAssistant;
import com.sq.tools.Logger;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.msdk.config.MultiConfigManager;

/**
 * 带返回、关闭的标题view
 */
public class BackTitleView extends RelativeLayout {
    private View rootView;

    //返回、关闭
    private View backView, closeView;

    //手游logo图标，仅在37正版时展示
    private ImageView ivLogo;

    //标题，37简版、官斗、捷诚等其他版本展示
    private TextView tvTitle;

    private Context mContext;

    /** 点击计数器 */
    private int clickCount = 0;
    /** 上一次点击时间 */
    private long lastClickTime = 0;

    private OnClickTitleViewListener mOnClickTitleViewListener;

    public BackTitleView(Context context) {
        this(context, null);
    }

    public BackTitleView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BackTitleView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }


    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        rootView = View.inflate(mContext, SqResUtils.getIdByName("sysq_base_title_layout", "layout", mContext), this);
        initView();
        initEvent();
    }

    private void initView() {
        backView = rootView.findViewById(SqResUtils.getIdByName("view_back", "id", mContext));
        closeView = rootView.findViewById(SqResUtils.getIdByName("view_close", "id", mContext));
        ivLogo = rootView.findViewById(SqResUtils.getIdByName("iv_logo", "id", mContext));
        boolean sqUnion = MultiConfigManager.getInstance().isSqUnion();
        ivLogo.setImageResource(SqResUtils.getIdByName(sqUnion ? "sysq_ic_logo_union" : "sysq_ic_logo", "drawable", mContext));
        tvTitle = rootView.findViewById(SqResUtils.getIdByName("tv_title", "id", mContext));
    }

    private void initEvent() {
        backView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickTitleViewListener != null) {
                    mOnClickTitleViewListener.clickBack();
                }
            }
        });

        closeView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnClickTitleViewListener != null) {
                    mOnClickTitleViewListener.clickClose();
                }
            }
        });

        ivLogo.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                long currentTime = SystemClock.uptimeMillis();
                // 如果距离上次点击间隔小于 500 毫秒，则不做处理
                if (currentTime - lastClickTime < 500) {
                    return;
                }
                // 否则更新点击时间和计数器
                lastClickTime = currentTime;
                clickCount++;
                if (clickCount < 5) {
                    return;
                }
                // 如果点击次数为5，则弹出诊断助手并重置计数器
                if (clickCount == 5) {
                    clickCount = 0;
                }

                Context context = getContext();
                if (!(context instanceof Activity)) {
                    return;
                }
                Activity activity = ((Activity) context);
                if (activity.isFinishing() || activity.isDestroyed()) {
                    return;
                }
                DiagnosticAssistant.show(activity);
            }
        });
    }

    public void showTitle() {
        tvTitle.setVisibility(VISIBLE);
        ivLogo.setVisibility(GONE);
    }

    public void showLogo() {
        tvTitle.setVisibility(GONE);
        ivLogo.setVisibility(VISIBLE);
    }

    public void showBackView(boolean show) {
        LogUtil.i("showBackView " + show);
        backView.setVisibility(show ? VISIBLE : GONE);
    }

    public void setTitle(String title) {
        tvTitle.setText(title);
    }

    public interface OnClickTitleViewListener {

        void clickBack();

        void clickClose();
    }

    public void setOnClickTitleViewListener(OnClickTitleViewListener onClickTitleViewListener) {
        this.mOnClickTitleViewListener = onClickTitleViewListener;
    }

    public ImageView getCloseImageView() {
        return rootView.findViewById(SqResUtils.getIdByName("btn_close", "id", mContext));
    }

    public ImageView getBackImageView() {
        return rootView.findViewById(SqResUtils.getIdByName("iv_back_icon", "id", mContext));
    }
}
