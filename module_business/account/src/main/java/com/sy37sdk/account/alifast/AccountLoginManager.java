package com.sy37sdk.account.alifast;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import com.mobile.auth.gatewayauth.LoginAuthActivity;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.user.UserInfoManager;
import com.sqwan.common.util.CheckClassUtils;
import com.sqwan.common.util.SDKError;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.LoginSkinHelper;
import com.sy37sdk.account.view.base.manager.AccountLoginAttachManager;
import com.sy37sdk.account.view.uifast.AccountLoginDialog;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

public class AccountLoginManager {

    private static final String TAG = "【Login Manager】";
    private static AccountLoginManager instance;

    private Context mContext;

    private ILoginListener loginListener;

    private Context loginAuthActivity;

    private boolean loginAuthActivityShow;

    private AccountLoginManager(Context context) {
        this.mContext = context;
    }

    public static AccountLoginManager getInstance(Context context) {
        if (instance == null) {
            synchronized (AccountLoginManager.class) {
                if (instance == null) {
                    instance = new AccountLoginManager(context);
                }
            }
        }
        return instance;
    }

    public void login(final ILoginListener listener) {
        LoginSkinHelper.init();
        this.loginListener = listener;
        UserInfo lastUserInfo = AccountUtil.getLastUserInfo(mContext);
        //重置特殊逻辑下状态
        AccountUtil.setNotSupportFast(false);
        if(!AccountUtil.checkToFastLogin()){
            // 非闪验页面
            AccountUtil.setCanFastBack(false);
        }
        if (lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname()) && !AccountUtil.checkToFastLogin()) {
            //有历史账号则吊起历史账号页面
            SQLog.i(TAG + "存在历史账号: " + lastUserInfo);
            startAccountLoginDialog(listener);
        } else {
            if (EntranceManager.getInstance().isWxLoginEntrance() && !AccountUtil.checkToFastLogin()) {
                //如果开启了微信登录则忽略闪验，在其他登录方式再跳闪验
                SQLog.w(TAG + "开启了微信登录, 不走闪验");
                startAccountLoginDialog(listener);
                return;
            }
            if(AccountUtil.checkToFastLogin()){
                AccountUtil.setToFastLogin(false);
            }
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.invoke_ali_fast_login);
            //没有历史账号则吊起闪验
            try {
                if (!isLoginAuthActivityExist()) {
                    SQLog.w(TAG + "不满足闪验版本, 跳过闪验登录");
                    HashMap<String, String> extraMap = new HashMap<>();
                    extraMap.put(SqTrackKey.fail_code, FastLoginConstants.Code.FAILURE_NOT_SUPPORT);
                    extraMap.put(SqTrackKey.reason_fail, "不满足闪验版本");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ali_fast_login_fail, extraMap);
                    startAccountLoginDialog(listener);
                    return;
                }
                registerLoginAuthActivityListener();
                LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_ali_fast);
                SQLog.d(TAG + "触发闪验登录");
                FastLoginManager.getInstance(mContext).doFastVerifyLogin(new FastLoginManager.FastLoginListener() {

                    @Override
                    public void onFastLoginSuccess(Map<String, String> data) {
                        SQLog.d(TAG + "闪验登录成功, " + data);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ali_fast_login_succ);
                        if (loginListener != null) {
                            loginListener.onSuccess(data);
                        }
                    }

                    @Override
                    public void onFastLoginFail(Bundle bundle) {
                        if (bundle != null && !TextUtils.isEmpty(bundle.getString(FastLoginConstants.BundleKey.CODE))) {
                            String code = bundle.getString(FastLoginConstants.BundleKey.CODE);
                            String msg = bundle.getString(FastLoginConstants.BundleKey.MESSAGE);
                            String ext = "";
                            try {
                                JSONObject obj = new JSONObject();
                                obj.put("code", code);
                                obj.put("msg", msg);
                                ext = obj.toString();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            HashMap<String, String> failMap = new HashMap<>();
                            failMap.put(SqTrackKey.fail_code, code);
                            failMap.put(SqTrackKey.reason_fail, msg);
                            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ali_fast_login_fail, failMap);
                            LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_ali_fast, code, msg);
                            if (code.equals(FastLoginConstants.Code.CANCEL)) {
                                SQLog.w(TAG + "闪验取消登录");
                                //取消登录
                                loginListener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, SDKError.ACCOUNT_LOGIN_CANCEL.message);
                            } else if (code.equals(FastLoginConstants.Code.FAILURE_CLICK_OTHER_WAY)) {
                                SQLog.w(TAG + "闪验切换登录方式");
                                //点击切换到其他登录方式
                                startAccountLoginDialog(true, listener);
                            } else if (code.equals(FastLoginConstants.Code.FAILURE_CLICK_BACK)) {
                                SQLog.w(TAG + "闪验返回登录");
                                //点击切换返回
                                if (EntranceManager.getInstance().isWxLoginEntrance() && lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())){
                                    AccountUtil.setToWeiChatLogin(true);
                                    startWeiChatLoginDialogWithBack(listener);
                                }else {
                                    startAccountLoginDialog(listener);
                                }
                            } else if (code.equals(FastLoginConstants.Code.FAILURE_NOT_SUPPORT)) {
                                SQLog.w(TAG + "版本不支持闪验");
                                AccountUtil.setNotSupportFast(true);
                                //如果阿里的授权页没有创建，则吊起37的登录弹窗
                                if (EntranceManager.getInstance().isWxLoginEntrance() && lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())){
                                    AccountUtil.setToWeiChatLogin(true);
                                    startWeiChatLoginDialogWithBack(listener);
                                }else {
                                    startPhoneLoginDialogWithBack(listener);
                                }
                            }else if (code.equals("600012")) {
                                SQLog.w(TAG + "闪验应用无效");
                                AccountUtil.setNotSupportFast(true);
                                //如果阿里的授权页没有创建，则吊起37的登录弹窗
                                if (EntranceManager.getInstance().isWxLoginEntrance() && lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())){
                                    AccountUtil.setToWeiChatLogin(true);
                                    startWeiChatLoginDialogWithBack(listener);
                                }else {
                                    startPhoneLoginDialogWithBack(listener);
                                }
                            }else if (code.equals("600011")) {
                                SQLog.w(TAG + "闪验应用无效");
                                if (loginAuthActivityShow) {
                                    SQLog.w(TAG + "阿里授权页已展示，需要关闭");
                                    ToastUtil.showToast("游戏版本过低，请重新下载最新包体或者选择其他方式登录");
                                }else {
                                    AccountUtil.setNotSupportFast(true);
                                    //如果阿里的授权页没有创建，则吊起37的登录弹窗
                                    if (EntranceManager.getInstance().isWxLoginEntrance() && lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())){
                                        AccountUtil.setToWeiChatLogin(true);
                                        startWeiChatLoginDialogWithBack(listener);
                                    }else {
                                        startPhoneLoginDialogWithBack(listener);
                                    }
                                }
                            }else {
                                SQLog.w(TAG + "闪验登录失败, code=" + code + ", msg=" + msg);
                                //其他失败情况
                                if (!loginAuthActivityShow) {
                                    SQLog.w(TAG + "阿里授权页未展示, 走37登录");
                                    AccountUtil.setNotSupportFast(true);
                                    //如果阿里的授权页没有创建，则吊起37的登录弹窗
                                    if (EntranceManager.getInstance().isWxLoginEntrance() && lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())){
                                        AccountUtil.setToWeiChatLogin(true);
                                        startWeiChatLoginDialogWithBack(listener);
                                    }else {
                                        startPhoneLoginDialogWithBack(listener);
                                    }
                                } else {
                                    SQLog.w(TAG + "阿里授权页已展示, 忽略");
                                }
                            }
                        } else {
                            SQLog.w(TAG + "闪验登录失败, " + bundle);
                            startAccountLoginDialog(listener);
                        }
                    }

                    @Override
                    public void onFastRelease() {
                        if (attachManager != null) {
                            attachManager.detachView();
                        }
                    }

                    @Override
                    public void onVerifyAccount(boolean needOpen) {
                        if (needOpen) {
                            login(listener);
                        }
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                SQLog.e(TAG + "调起闪验异常", e);
                HashMap<String, String> extraMap = new HashMap<>();
                extraMap.put(SqTrackKey.fail_code, FastLoginConstants.Code.FAILURE_VERIFY_FAIL_UNKNOWN);
                extraMap.put(SqTrackKey.reason_fail, FastLoginConstants.MESSAGE.FAILURE_VERIFY_FAIL_UNKNOWN + " " + e.getMessage());
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ali_fast_login_fail, extraMap);
                unRegisterLoginAuthActivityListener();
                startAccountLoginDialog(listener);
            }
        }
    }

    private void startAccountLoginDialog(ILoginListener listener) {
        startAccountLoginDialog(false, listener);
    }

    private void startAccountLoginDialog(boolean isFromAliFastLogin, final ILoginListener listener) {
        AccountLoginDialog accountLoginDialog = new AccountLoginDialog(mContext, listener);
        if (isFromAliFastLogin) {
            FastLoginManager.getInstance(mContext).quitLoginPage();
            accountLoginDialog.setOnAccountDialogCloseListener(new AccountLoginDialog.OnAccountDialogCloseListener() {
                @Override
                public void onClose() {

                }

                @Override
                public void onDismiss() {
                    AccountUtil.setToFastLogin(true);
                    login(listener);
                }
            });
        }
        accountLoginDialog.setFromAliFastLogin(isFromAliFastLogin);
        accountLoginDialog.show();

    }

    private void startWeiChatLoginDialogWithBack(final ILoginListener listener) {
        AccountLoginDialog accountLoginDialog = new AccountLoginDialog(mContext, listener);
        accountLoginDialog.setOnAccountDialogCloseListener(new AccountLoginDialog.OnAccountDialogCloseListener() {
            @Override
            public void onClose() {

            }

            @Override
            public void onDismiss() {
                login(listener);
            }
        });
        accountLoginDialog.setCanBack(true);
        accountLoginDialog.show();

    }

    private void startPhoneLoginDialogWithBack(final ILoginListener listener) {
        AccountLoginDialog accountLoginDialog = new AccountLoginDialog(mContext, listener);
        accountLoginDialog.setOnAccountDialogCloseListener(new AccountLoginDialog.OnAccountDialogCloseListener() {
            @Override
            public void onClose() {

            }

            @Override
            public void onDismiss() {
                login(listener);
            }
        });
        accountLoginDialog.setCanBack(true);
        accountLoginDialog.show();
    }


    private void registerLoginAuthActivityListener() {
        SQContextWrapper.getActivity().getApplication().registerActivityLifecycleCallbacks(activityListener);
    }

    private void unRegisterLoginAuthActivityListener() {
        SQContextWrapper.getActivity().getApplication().unregisterActivityLifecycleCallbacks(activityListener);
    }

    private AccountLoginAttachManager attachManager;

    private final Application.ActivityLifecycleCallbacks activityListener = new Application.ActivityLifecycleCallbacks() {
        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            if (isLoginAuthActivityExist()) {
                if (activity instanceof LoginAuthActivity) {
                    SQLog.v(TAG + "阿里闪验登录授权页面onActivityCreated: " + activity);
                    loginAuthActivity = activity;
                    FastLoginManager.getInstance(mContext).setActivity((Activity) loginAuthActivity);
                }
            }

        }

        @Override
        public void onActivityStarted(Activity activity) {

        }

        @Override
        public void onActivityResumed(Activity activity) {
            if (isLoginAuthActivityExist()) {
                if (activity instanceof LoginAuthActivity) {
                    SQLog.d(TAG + "阿里闪验登录授权页面onActivityResumed: " + activity);
                    loginAuthActivityShow = true;
                    try {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                SQLog.d(TAG + "往闪验授权页面插入自定义UI");
                                if (attachManager == null) {
                                    attachManager = new AccountLoginAttachManager(loginAuthActivity);
                                }
                                if (!attachManager.isShown()) {
                                    attachManager.attachView();
                                }
                                PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.ali_fast, SqTrackPage.SqTrackViewName.ali_fast);
                            }
                        }, 500);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

        }

        @Override
        public void onActivityPaused(Activity activity) {

        }

        @Override
        public void onActivityStopped(Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(Activity activity) {
            if (isLoginAuthActivityExist()) {
                if (activity instanceof LoginAuthActivity) {
                    loginAuthActivityShow = false;
                    unRegisterLoginAuthActivityListener();
                    attachManager = null;
                }
            }

        }
    };

    private boolean isLoginAuthActivityExist() {
        return CheckClassUtils.classExist("com.mobile.auth.gatewayauth.LoginAuthActivity");
    }
}
