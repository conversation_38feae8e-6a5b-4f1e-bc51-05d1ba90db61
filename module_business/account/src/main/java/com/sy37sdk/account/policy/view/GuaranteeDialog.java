package com.sy37sdk.account.policy.view;


import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;

/**
 * 弱网情况下的提示弹窗
 */
public class GuaranteeDialog extends BaseDialog {
    private TextView tvTip;
    private TextView tvSure;
    private String tip;

    public GuaranteeDialog(Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sysq_guarantee_dialog", "layout"));
        initView();
    }

    private void initView() {
        tvTip = findViewById(getIdByName("tv_tip", "id"));
        tvSure = findViewById(getIdByName("tv_sure", "id"));
        tvTip.setText(TextUtils.isEmpty(tip) ? "" : tip);
        tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                ((Activity) mContext).finish();
                System.exit(0);
            }
        });
    }

    public void setTip(String tip) {
        this.tip = tip;
    }
}
