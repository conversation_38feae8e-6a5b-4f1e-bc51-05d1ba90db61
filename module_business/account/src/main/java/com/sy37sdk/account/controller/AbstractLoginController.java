package com.sy37sdk.account.controller;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import com.sq.tool.logger.Printer;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.track.SqTrackAction;
import com.sqwan.common.track.SqTrackActionManager;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.QrCodeInfo;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.presenter.AutoLoginPresenter;
import com.sy37sdk.account.view.IAutoLoginDialog;
import com.sy37sdk.account.view.IRegSuccessDialog;
import com.sy37sdk.account.view.ui.AutoLoginDialog;

/**
 * <AUTHOR>
 * @date 2020-05-17
 */
public abstract class AbstractLoginController {

    protected static final Printer LOG = SQLog.m("【Login Ctrl】");
    protected Context mContext;


    public AbstractLoginController(Context context) {
        this.mContext = context;
    }


    //todo  抽象
    public void login(final ILoginListener loginListener) {
        UserInfo userInfo = AccountCache.getUserInfo(mContext);
        LOG.i("缓存的user: " + userInfo);
        if (userInfo != null && !TextUtils.isEmpty(userInfo.getUname())) {
            LOG.d("有效的缓存账号, 触发自动登录");
            //sp中有缓存的账号则吊起自动登录
            String alias = userInfo.getAlias();
            String userName = userInfo.getUname();
            final String mobile = userInfo.getMobile();
            final String name = TextUtils.isEmpty(alias) ? userName : alias;
            final String uname = TextUtils.isEmpty(mobile) ? name : mobile;
            showAutoLoginDialog(uname, loginListener);
        } else {
            showLoginDialog(loginListener);
        }
    }

    public abstract void showLoginDialog(ILoginListener listener);

    public abstract void showRegSuccessDialog(String uname, String pwd, QrCodeInfo qrCodeInfo, IRegSuccessDialog.EnterGameListener enterGameListener);

    protected void showAutoLoginDialog(String uname, final ILoginListener listener) {
        LOG.d("展示自动登录弹窗: " + uname);
        SqTrackActionManager.getInstance().trackAction(SqTrackAction.SHOW_AUTO_LOGIN_VIEW);
        AutoLoginDialog dialog = new AutoLoginDialog(mContext, uname, listener);
        AutoLoginPresenter presenter = new AutoLoginPresenter(mContext, dialog);

        String alias = AccountCache.getAccountAlias(mContext);
        String userName = AccountCache.getUsername(mContext);
        final String name = TextUtils.isEmpty(alias) ? userName : alias;
        final String pwd = AccountCache.getPassword(mContext);
        UserInfo userInfo = AccountCache.getUserInfo(mContext);
        if (alias != null && !alias.equals(userName)) {
            // TODO: 2024/10/9 后续版本移除
            BuglessAction.reportCatchException(new IllegalArgumentException(), "alias和uname不一致",
                userInfo != null ? userInfo.toString() : "alias=" + alias + ", uname=" + userName,
                BuglessAction.COMMON_ERROR);
        }
        boolean phoneLoginType = userInfo != null && userInfo.isPhoneLoginType();
        String mobile = "";
        if (userInfo != null && userInfo.isPhoneLoginType()) {
            mobile = userInfo.getMobile();
        }
        presenter.setLoginInfo(phoneLoginType ? mobile : name, pwd, phoneLoginType);
        dialog.setPresenter(presenter);
        dialog.setChangeAccountListener(new IAutoLoginDialog.IChangeAccountListener() {
            @Override
            public void changeAccount() {
                LOG.d("点击自动登录中的切换账号");
                showLoginDialog(listener);
            }
        });
        dialog.show();
    }

    public void onActivityResult(int requestCode, int resultCode, Intent intent) {

    }

}
