package com.sy37sdk.account.view.base.view;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sy37sdk.account.view.LoginSkinHelper;

public abstract class BaseSwitchView extends BasePageSwitchView {

    protected BackTitleView backTitleView;

    protected View rootView;

    public BaseSwitchView(Context context) {
        super(context);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        initRootView();
        initView();
        initEvent();
    }


    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        backTitleView.showBackView(canGoBack());
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        onBack();
    }

    protected void initRootView() {
        if (rootView != null) {
            return;
        }
        rootView = LayoutInflater.from(getContext()).inflate(getIdByName(getLayoutResName(), "layout"), null);
        addView(rootView);
        initBackTitleView();
        View content_layout = getViewByName("content_layout");
        if (content_layout != null) {
            content_layout.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    LogUtil.i("点击了内容区域");
                }
            });
        }
    }

    protected void onBack() {
        if (loginDialog != null) {
            loginDialog.goBack();
        }
    }


    protected <T extends View> T getViewByName(String name) {
        return rootView.findViewById(getIdByName(name, "id"));
    }

    private void initBackTitleView() {
        backTitleView = rootView.findViewById(SqResUtils.getIdByName("common_title_view", "id", getContext()));
        if (backTitleView != null) {
            backTitleView.setOnClickTitleViewListener(new BackTitleView.OnClickTitleViewListener() {
                @Override
                public void clickBack() {
                    onBack();
                }

                @Override
                public void clickClose() {
                    closeLoginDialog();
                    if (loginDialog != null) {
                        loginDialog.closeAccountDialog();
                    }
                }
            });
            //是否是非37
            boolean scut3 = MultiSdkManager.getInstance().isScut3();
            if (scut3) {
                //不是37版本则显示标题
                backTitleView.showTitle();
            } else {
                //是37
                if (ConfigManager.getInstance(getContext()).isSplashSDK() || ConfigManager.getInstance(getContext()).isLessFunction()) {
                    //37简版显示标题
                    backTitleView.showTitle();
                } else {
                    //37正版显示logo
                    backTitleView.showLogo();
                }
            }
            backTitleView.getBackImageView().setImageResource(LoginSkinHelper.getBackIconResId(getContext()));
            backTitleView.getCloseImageView().setImageResource(LoginSkinHelper.getCloseIconResId(getContext()));
        }
        backTitleView.setTitle(getTitle());
    }

    protected void closeLoginDialog(){
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.closeLoginPage, SqTrackBtn.SqTrackBtnExt.closeLoginPage);
    }

    protected void setTitleView(String title){
        if(backTitleView!=null){
            backTitleView.setTitle(title);
        }
    }

    public void showSoftInput(final View focusView) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                requestFocus(focusView);
            }
        }, 300);
    }

    public void hideSoftInput() {
        StatusBarUtil.hideSystemKeyBoard(getContext(), this);
    }

    private void requestFocus(View view) {
        if (view == null) {
            return;
        }
        view.setFocusable(true);
        view.setFocusableInTouchMode(true);
        view.requestFocus();
        InputMethodManager inputManager = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        inputManager.showSoftInput(view, 0);
    }


    public boolean canGoBack() {
        return loginDialog.canGoBack();
    }

    public abstract String getLayoutResName();

    public abstract void initView();

    public abstract void initEvent();

    public abstract String getTitle();
}
