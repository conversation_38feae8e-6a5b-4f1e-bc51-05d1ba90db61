package com.sy37sdk.account.db;

import android.content.ContentValues;
import android.database.Cursor;

import java.util.ArrayList;
import java.util.List;

public class LoginTriggerDBManager extends DBManager {

    private static LoginTriggerDBManager dbManager;

    private LoginTriggerDBManager() {

    }

    public static LoginTriggerDBManager getInstance() {
        if (dbManager == null) {
            synchronized (LoginTriggerDBManager.class) {
                if (dbManager == null) {
                    dbManager = new LoginTriggerDBManager();
                }
            }
        }
        return dbManager;
    }


    /***
     * 插入一条假登录的数据
     * @param loginTrigger
     */
    public void insertLoginTrigger(LoginTrigger loginTrigger) {
        ContentValues cv = new ContentValues();
        cv.put(LoginTriggerTable.UID, loginTrigger.getUid());
        cv.put(LoginTriggerTable.UNAME, loginTrigger.getUname());
        cv.put(LoginTriggerTable.TOKEN, loginTrigger.getToken());
        cv.put(LoginTriggerTable.TRIGGER_TIME, loginTrigger.getTriggerTime());
        cv.put(LoginTriggerTable.LOGIN_TYPE, loginTrigger.getLoginType());
        getDb().insert(LoginTriggerTable.TABLE_NAME, null, cv);
    }

    /**
     * 查询所有的假登录的记录
     */
    public List<LoginTrigger> query() {
        List<LoginTrigger> loginTriggers = new ArrayList<>();
        Cursor c = getDb().rawQuery("select * from " + LoginTriggerTable.TABLE_NAME, null);
        if (c != null) {
            while (c.moveToNext()) {
                LoginTrigger loginTrigger = new LoginTrigger();
                loginTrigger.setId(c.getLong(c.getColumnIndex(LoginTriggerTable.ID)));
                loginTrigger.setUid(c.getString(c.getColumnIndex(LoginTriggerTable.UID)));
                loginTrigger.setUname(c.getString(c.getColumnIndex(LoginTriggerTable.UNAME)));
                loginTrigger.setToken(c.getString(c.getColumnIndex(LoginTriggerTable.TOKEN)));
                loginTrigger.setTriggerTime(c.getLong(c.getColumnIndex(LoginTriggerTable.TRIGGER_TIME)));
                loginTrigger.setLoginType(c.getString(c.getColumnIndex(LoginTriggerTable.LOGIN_TYPE)));
                loginTriggers.add(loginTrigger);
            }
            c.close();
        }
        return loginTriggers;
    }

    /**
     * 删除假登录的记录
     */
    public void delete(String id) {
        getDb().delete(LoginTriggerTable.TABLE_NAME, LoginTriggerTable.ID + " = ?", new String[]{id + ""});
    }

    /**
     * 删除假登录的记录
     */
    public void delete(List<LoginTrigger> loginTriggers) {
        for (LoginTrigger loginTrigger : loginTriggers) {
            getDb().delete(LoginTriggerTable.TABLE_NAME, LoginTriggerTable.ID + " = ?", new String[]{loginTrigger.getId() + ""});
        }
    }
}
