package com.sy37sdk.account.age;

import android.text.TextUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AppropriateAge {
    //登录界面
    public static final String TIMING_LOGIN = "1";
    //选服界面
    public static final String TIMING_ROLE = "2";

    public static final int LOCATE_LEFT_TOP = 1;
    public static final int LOCATE_RIGHT_TOP = 2;
    public static final int LOCATE_LEFT_BOTTOM = 3;
    public static final int LOCATE_RIGHT_BOTTOM = 4;
    //显示位置 1:左上，2:右上，3左下：，4右下
    private int location;
    //适龄提醒url
    private String desc;
    //图标
    private String icon;
    //是否启用，0:不启用，1:启用
    private boolean status;

    private List<String> timing;

    public List<String> getTiming() {
        return timing;
    }

    public void setTiming(List<String> timing) {
        this.timing = timing;
    }

    public int getLocation() {
        return location;
    }

    public void setLocation(int location) {
        this.location = location;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    /**
     * 将适龄实体转换成json字符串
     *
     * @param appropriateAge
     * @return
     */
    public static String objectToJson(AppropriateAge appropriateAge) {
        if (appropriateAge == null) {
            return "";
        }
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("location", appropriateAge.getLocation());
            jsonObject.putOpt("desc", appropriateAge.getDesc());
            jsonObject.putOpt("icon", appropriateAge.getIcon());
            jsonObject.putOpt("status", appropriateAge.isStatus());
            JSONArray timingArray = new JSONArray();
            for (int i = 0; i < appropriateAge.getTiming().size(); i++) {
                timingArray.put(appropriateAge.getTiming().get(i));
            }
            jsonObject.putOpt("timing", timingArray);
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

    }

    /**
     * json串解析成适龄实体
     *
     * @param json
     * @return
     */
    public static AppropriateAge jsonToObject(String json) {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            JSONObject object = new JSONObject(json);
            int location = object.optInt("location");
            String desc = object.optString("desc");
            String icon_url = object.optString("icon");
            boolean status = object.optBoolean("status");
            JSONArray timingArray = object.optJSONArray("timing");
            List<String> timings = new ArrayList<>();
            for (int i = 0; i < timingArray.length(); i++) {
                timings.add(timingArray.optString(i));
            }
            AppropriateAge appropriateAge = new AppropriateAge();
            appropriateAge.setDesc(desc);
            appropriateAge.setIcon(icon_url);
            appropriateAge.setStatus(status);
            appropriateAge.setLocation(location);
            appropriateAge.setTiming(timings);
            return appropriateAge;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从服务端获取到适龄配置后解析
     *
     * @param json
     * @return
     */
    public static AppropriateAge parse(String json) {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            JSONObject object = new JSONObject(json);
            String locationStr = object.optString("location");
            String desc = object.optString("desc");
            String icon_url = object.optString("icon");
            String statusStr = object.optString("status");
            AppropriateAge appropriateAge = new AppropriateAge();
            appropriateAge.setDesc(desc);
            appropriateAge.setIcon(icon_url);
            int status = 0;
            try {
                status = Integer.parseInt(statusStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            appropriateAge.setStatus(status == 1);
            int location = LOCATE_LEFT_TOP;
            try {
                location = Integer.parseInt(locationStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
            appropriateAge.setLocation(location);
            JSONArray timingArray = object.optJSONArray("timing");
            List<String> timings = new ArrayList<>();
            if (timingArray != null && timingArray.length() > 0) {
                for (int i = 0; i < timingArray.length(); i++) {
                    timings.add(timingArray.optString(i));
                }
            }
            appropriateAge.setTiming(timings);
            return appropriateAge;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
