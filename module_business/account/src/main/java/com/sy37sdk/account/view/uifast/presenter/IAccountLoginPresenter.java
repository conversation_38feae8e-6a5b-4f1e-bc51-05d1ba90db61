package com.sy37sdk.account.view.uifast.presenter;

import com.sqwan.common.mvp.IPresenter;
import com.sy37sdk.account.UserInfo;

public interface IAccountLoginPresenter extends IPresenter {


    /**
     * 删除文件中的账户列表
     */
    void deleteUser(UserInfo userInfo);

    /**
     * 账号登录
     * @param name
     * @param password
     */
    void login(String name, String password);

    /**
     * 账号注册
     * @param name register name
     * @param pwd password
     */
    void accountRegister(String name, String pwd);

    /**
     * 自动填充注册的账号密码
     * @param refresh 是否直接从服务器生成获取
     */
    void autoRegister(boolean refresh);

    /**
     * 自动填充登录的账号密码
     */
    void autoAccount();

    void clauseClick(boolean isCheck);

    /**
     * 跳转注册协议页面
     */
    void toClausePage();


    /**
     * 跳转隐私协议页面
     */
    void toPolicy();

    /**
     * 忘记密码
     */
    void forgotPassword();

}
