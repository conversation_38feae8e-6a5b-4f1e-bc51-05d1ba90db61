package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.DensityUtil;
import com.sy37sdk.account.AccountLogic.AccountListener;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.presenter.MultiAccountSelectPresenter;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2025/3/24
 */
public class MultiSelectView extends BaseSwitchView implements IMultiSelectView {
    private final Map<String,String> mUidMap = new HashMap<>();
    private LinearLayout mListLayout;
    private LinearLayout mAccountError;
    private TextView mTvReInput;
    private final MultiAccountSelectPresenter mPresenter;

    public MultiSelectView(Context context, ILoginDialog dialog) {
        super(context);
        this.loginDialog = dialog;
        mPresenter = new MultiAccountSelectPresenter(context, this);
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_multi_select";
    }

    @Override
    public void initView() {
        mListLayout = getViewByName("ll_list");
        mAccountError = getViewByName("ll_account_error");
        mTvReInput = getViewByName("tv_input");
    }

    @Override
    public void initEvent() {
        mTvReInput.setOnClickListener(v -> {
            trackReInput();
            loginDialog.goBack();
        });
    }

    @Override
    public String getTitle() {
        return "多账号选择";
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        resetUI();
        setAccountList(bundle);
        trackEnterPage();
    }


    public void setAccountList(Bundle bundle) {
        String jsonStr = bundle.getString("account_list");
        String loginUname = bundle.getString("login_uname");
        String loginPwd = bundle.getString("login_pwd");
        mUidMap.clear();
        try {
            JSONArray jsonArray = new JSONArray(jsonStr);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.optJSONObject(i);
                bindData(jsonObject, loginUname, loginPwd, i == 0);
                mUidMap.put("uid" + (i+1), jsonObject.optString("uid"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void resetUI() {
        mListLayout.removeAllViews();
        mAccountError.setVisibility(View.GONE);
    }

    private void bindData(JSONObject jsonObject, String loginUname, String loginPwd, boolean isNew) {
        String roleName = jsonObject.optString("role_name");
        String loginTime = jsonObject.optString("login_time");
        String serverName = jsonObject.optString("server_name");
        String level = jsonObject.optString("level");
        String publicGameName = jsonObject.optString("public_game_name");
        RelativeLayout itemCard = (RelativeLayout) LayoutInflater.from(getContext())
            .inflate(getIdByName("sy37_multi_account_item", "layout"), null);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = DensityUtil.dip2px(getContext(), 8f);
        TextView tvGameName = itemCard.findViewById(getIdByName("tv_game_name", "id"));
        TextView tvRoleLevel = itemCard.findViewById(getIdByName("tv_role_level", "id"));
        TextView tvRoleServer = itemCard.findViewById(getIdByName("tv_role_server", "id"));
        TextView tvLoginTime = itemCard.findViewById(getIdByName("tv_login_time", "id"));
        TextView tvConfirm = itemCard.findViewById(getIdByName("tv_confirm", "id"));
        TextView tvNew = itemCard.findViewById(getIdByName("tv_new", "id"));
        tvNew.setVisibility(isNew ? View.VISIBLE : View.GONE);
        tvGameName.setText(publicGameName);
        tvLoginTime.setText(loginTime);
        tvRoleServer.setText(roleName + "-" + serverName);
        if (TextUtils.isEmpty(level)) {
            tvRoleLevel.setVisibility(View.GONE);
        } else {
            tvRoleLevel.setText(level + "级");
        }
        mListLayout.addView(itemCard, layoutParams);
        tvConfirm.setOnClickListener(v -> {
            String loginType = jsonObject.optString("login_type");
            mPresenter.login(loginType, loginUname, loginPwd, new AccountListener() {
                @Override
                public void onSuccess(Map<String, String> data) {
                    hideLoading();
                    trackLoginSucc(loginType, data);
                    loginDialog.loginSuccess(data);

                }

                @Override
                public void onFailure(int code, String msg) {
                    hideLoading();
                    tvConfirm.setEnabled(false);
                    mAccountError.setVisibility(View.VISIBLE);
                    trackLoginFail(loginType, code, msg);
                }
            });
        });
    }

    private void trackEnterPage() {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.MULTI_ACCOUNT_SELECT_PAGE, mUidMap);
    }

    private void trackLoginSucc(String loginType, Map<String, String> data) {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.MULTI_ACCOUNT_LOGIN_SUCC, null);
        if ("account".equals(loginType)) {
            LoginTractionManager.track(LoginTractionManager.login_way_account, data);
        } else {
            LoginTractionManager.track(LoginTractionManager.login_way_phone_pwd, data);
        }
    }

    private void trackLoginFail(String loginType, int code, String msg) {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.MULTI_ACCOUNT_LOGIN_FAIL, null);
        if ("account".equals(loginType)) {
            LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_account, code + "", msg);
        } else {
            LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_phone_pwd, code + "", msg);
        }
    }

    private void trackReInput() {
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.MULTI_ACCOUNT_RE_INPUT, mUidMap);
    }
}
