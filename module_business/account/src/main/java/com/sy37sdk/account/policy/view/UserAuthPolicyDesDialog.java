package com.sy37sdk.account.policy.view;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.account.policy.AuthHandler;
import com.sy37sdk.account.uagree.UAgreeManager;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-15 20:28
 */
public class UserAuthPolicyDesDialog extends AuthBaseDialog {
    private LinearLayout llPolicy,llPolicyUpdate;
    public UserAuthPolicyDesDialog(Context context) {
        super(context);
    }

    @Override
    protected String getContainerLayout() {
        return "sy37_layout_auth_preview";
    }

    @Override
    protected void doEngine() {
        llPolicy = findViewById(findId("llPolicy"));
        llPolicyUpdate = findViewById(findId("llPolicyUpdate"));
        TextView tvProtol = findViewById(findId("tvProtol"));
        TextView tvProtolUpdate = findViewById(findId("tvProtolUpdate"));
        setLink(tvProtol,tvProtol.getText().toString());
        setLink(tvProtolUpdate,tvProtolUpdate.getText().toString());
        setCanceledOnTouchOutside(false);
        tvCancel.setText("不同意");
        tvCancel.setSelected(false);
        tvOk.setText("同意");
    }

    @Override
    protected String getTitle() {
        if (UAgreeManager.getInstance().isFirstCheck()) {
            return "个人信息保护指引";
        }else{
            return "个人信息保护指引更新";
        }
    }



    @Override
    protected void onClickOk() {
        super.onClickOk();
    }
    private void updatePolice(){
        if (UAgreeManager.getInstance().isFirstCheck()) {
            ViewUtils.show(llPolicy);
        }else{
            ViewUtils.show(llPolicyUpdate);
        }
    }

    @Override
    public void show() {
        super.show();
        updatePolice();
    }
}
