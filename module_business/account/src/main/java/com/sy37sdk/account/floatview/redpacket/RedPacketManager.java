package com.sy37sdk.account.floatview.redpacket;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import com.sq.sdk.tool.download.DownloadListener;
import com.sq.sdk.tool.download.DownloadTask;
import com.sq.sdk.tool.util.MD5Util;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.TestConst;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.floatview.SqFloatViewManager;
import java.io.File;
import java.util.Map;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020-11-17
 */
public class RedPacketManager {
    //start -------------------------------------- just test --------------------------------------------
    private void testRedPacketDialog(final Activity activity){
        final RedPacketInfo redPacketInfo = new RedPacketInfo();
        String act_url = "http://37.com.cn/huodong/20201207_gd_red_envelopes";
        RedPacketInfo.WebViewConfig webViewConfig = new RedPacketInfo.WebViewConfig();
        redPacketInfo.jumpLink = getCouponUrl(activity,act_url);
//        String pop_url = "http://www.baidu.com";
        String pop_url = "http://37.com.cn/huodong/20201207_gd_red_envelopes_popup";
        webViewConfig.pop_url = pop_url;
        webViewConfig.height = 589;
        webViewConfig.width = 691;
        redPacketInfo.webViewConfig = webViewConfig;
        final RedPacketDialog redPacketDialog = showRedPacketDialog(activity,redPacketInfo);
//        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                redPacketDialog.handleAnim(new RedPacketDialog.AnimCallback() {
//                    @Override
//                    public void onEnd() {
//                        redPacketDialog.dismiss();
//                    }
//                });
//            }
//        },1000);


    }
    private void testGifUrl(final Activity activity) {
        final RedPacketInfo redPacketInfo = new RedPacketInfo();
        redPacketInfo.imgUrl = "https://s3.ax1x.com/2020/11/17/DVA1aT.gif";
        downloadFloatViewGifImg(activity, redPacketInfo, new DownloadGifCallback() {
            @Override
            public void getLocalGifPath(String localPath, boolean isCache) {
                LogUtil.d(TAG, "getLocalGifPath localPath : " + localPath + " isCache : " + isCache);
                if (!TextUtils.isEmpty(localPath)) {
                    redPacketInfo.imgLocalPath = localPath;
                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            SqFloatViewManager.getInstance().showRedPacketFloat(activity, redPacketInfo,false);
                            showRedPacketDialog(activity,redPacketInfo);

                        }
                    });

                }
            }
        });
    }
    //end -------------------------------------- just test --------------------------------------------

    public interface DownloadGifCallback{
        void getLocalGifPath(String localPath,boolean isCache);
    }
    public final static String SP_KEY_HASSHOWNREDPACKETPOP = "SP_KEY_HASSHOWNREDPACKETPOP";

    private String TAG = "RedPacketManager";
    private static RedPacketManager sInstance;
    private static Map<String, String> roleInfos;
    private final static String popImgUrlKey = "imgUrl";
    public static RedPacketManager getInstance() {
        if (sInstance == null) {
            sInstance = new RedPacketManager();
        }
        return sInstance;
    }

    public void submitRoleInfos(Context context, Map<String, String> roleInfos) {
        RedPacketManager.roleInfos = roleInfos;
        String serverTime = roleInfos.get("serverTime");

        //test
        if (TestConst.isTestRedPacketDialog) {
            testRedPacketDialog((Activity) context);
        } else {
            getRedPacketFloatConfig((Activity) context, serverTime);
        }
    }

    /**
     * 红包点击跳转链接
     * @param context
     * @param act_url
     * @return
     */
    public String getCouponUrl(Context context,String act_url) {
        String couponUrl = AppUtils.constructWebUrlParam(context, act_url);
        String dsid = roleInfos.get("serverId");
        String drid = roleInfos.get("roleId");
        String roleName = roleInfos.get("roleName");
        return couponUrl + "&dsid=" + dsid + "&drid=" + drid + "&roleName=" + roleName;
    }
    /**
     *  红包开关为true并且下载到图片才显示
     * @param serverTime 角色所在的区服的开服时间
     */
    public void getRedPacketFloatConfig(final Activity activity, String serverTime){
        AccountRequestManager requestManager = new AccountRequestManager(activity);
        requestManager.getRedPacketSwitch(serverTime, new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject data) {
                if (TestConst.isTestDownloadGifUrl) {
                    testGifUrl(activity);
                    return;
                }
                try {
                    boolean toShow = data.optBoolean("show_red_float_window");
                    if (!toShow) {
                        if(SqFloatViewManager.getInstance().isShowFloat()) {
                            SqFloatViewManager.getInstance().showFloatView(activity);
                        }
                        return;
                    }
                    final RedPacketInfo redPacketInfo = new RedPacketInfo();
                    String icon_url = data.optString("icon_url");
                    final String act_url = data.optString("act_url");
                    int pop_width = data.optInt("pop_width");
                    int pop_height = data.optInt("pop_height");
                    final String pop_url = data.optString("pop_url");
                    final RedPacketInfo.WebViewConfig webViewConfig = new RedPacketInfo.WebViewConfig();
                    webViewConfig.width = pop_width;
                    webViewConfig.height = pop_height;
                    webViewConfig.pop_url = pop_url;
                    redPacketInfo.webViewConfig = webViewConfig;
                    redPacketInfo.imgUrl = icon_url ;
                    LogUtil.i(TAG,"redPacketInfo:"+redPacketInfo);
                    downloadFloatViewGifImg(activity, redPacketInfo, new DownloadGifCallback() {
                        @Override
                        public void getLocalGifPath(String localPath,boolean isCache) {
                            LogUtil.d(TAG,"getLocalGifPath localPath : " + localPath + " isCache : " + isCache);
                            if (!TextUtils.isEmpty(localPath)) {
                                redPacketInfo.imgLocalPath = localPath;
                                redPacketInfo.jumpLink = getCouponUrl(activity,act_url);
                                LogUtil.i(TAG,"redPacketInfo:"+redPacketInfo);
                                activity.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        boolean checkValidatePopImg = false;
                                        if (!TestConst.isTestEmptyRedPacketPop) {
                                            if (!TextUtils.isEmpty(webViewConfig.pop_url)) {
                                                checkValidatePopImg = checkValidatePopImg(webViewConfig.pop_url);
                                            }
                                        }
                                        if (TestConst.isForceShowRedPacketPop) {
                                            if (!checkValidatePopImg) {
                                                SqFloatViewManager.getInstance().showRedPacketFloat(activity,redPacketInfo,true);
                                            }else{
                                                SqFloatViewManager.getInstance().showRedPacketFloat(activity,redPacketInfo,false);
                                                showRedPacketDialog(activity,redPacketInfo);
                                            }
                                        }else{
                                            boolean hasShowRedPacketPop = hasShownRedpacketPop(activity);
                                            if (hasShowRedPacketPop || !checkValidatePopImg) {
                                                SqFloatViewManager.getInstance().showRedPacketFloat(activity,redPacketInfo,true);
                                            }else{
                                                SqFloatViewManager.getInstance().showRedPacketFloat(activity,redPacketInfo,false);
                                                showRedPacketDialog(activity,redPacketInfo);
                                                setShownRedpacketPop(activity,true);
                                            }
                                        }

                                    }
                                });

                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }
    private boolean hasShownRedpacketPop(Context context){
        return SpUtils.get(context).getBoolean(RedPacketManager.SP_KEY_HASSHOWNREDPACKETPOP,false);
    }
    private void setShownRedpacketPop(Context context,boolean result){
        SpUtils.get(context).put(RedPacketManager.SP_KEY_HASSHOWNREDPACKETPOP,result);
    }
    private RedPacketDialog showRedPacketDialog(final Activity activity, final RedPacketInfo redPacketInfo){
        final RedPacketDialog redPacketDialog = new RedPacketDialog(activity);
        redPacketDialog.initData(SqFloatViewManager.getInstance().floatView,redPacketInfo, new RedPacketDialog.RedPacketDialogCallback() {
            @Override
            public void onClose() {
                redPacketDialog.dismiss();
                SqFloatViewManager.getInstance().showRedPacktFloatView();

            }

            @Override
            public void onOpenUrl() {
                redPacketDialog.dismiss();
                showTargetUrl(activity,redPacketInfo.jumpLink);
                SqFloatViewManager.getInstance().showRedPacktFloatView();
            }
        });
        redPacketDialog.show();
        return redPacketDialog;
    }

    /**
     * 下载悬浮球gif图片
     * @param redPacketInfo
     */
    public void downloadFloatViewGifImg(final Context context, RedPacketInfo redPacketInfo, final DownloadGifCallback downloadGifCallback){
        final String localFloatGifNameKey = "localFloatGifNameKey";
        String localFloatGifPathDir = context.getExternalCacheDir().getAbsolutePath();
        String imgUrl = redPacketInfo.imgUrl;
        if (!TextUtils.isEmpty(imgUrl)) {
            String localFloatGifName = MD5Util.encode(imgUrl);
//            String localFloatGifName = "test.gif";
            String _localFloatGifName = SpUtils.get(context).getString(localFloatGifNameKey,"");
            if (_localFloatGifName.equals(localFloatGifName)) {
                File localFloatGifFile = new File(localFloatGifPathDir,localFloatGifName);
                if (localFloatGifFile.exists() && localFloatGifFile.length()>0) {
                    if (downloadGifCallback!=null) {
                        downloadGifCallback.getLocalGifPath(localFloatGifFile.getAbsolutePath(),true);
                        return;
                    }
                }
            }
            //下载gif
            new DownloadTask(imgUrl, localFloatGifName, localFloatGifPathDir, new DownloadListener() {
                @Override
                public void onUpdate(long l, long l1) {

                }

                @Override
                public void onSuccess(File file) {
                    String localFloatGifPath = file.getAbsolutePath();
                    LogUtil.i(TAG,"onSuccess localFloatGifPath : " + localFloatGifPath);
                    String localFloatGifName = file.getName();
                    SpUtils.get(context).put(localFloatGifNameKey,localFloatGifName);
                    if (downloadGifCallback!=null) {
                        downloadGifCallback.getLocalGifPath(localFloatGifPath,false);
                    }
                }

                @Override
                public void onFailure(Throwable throwable, int i, String s) {
                    LogUtil.e(TAG,"onFailure : " + s);
                    downloadGifCallback.getLocalGifPath(null,false);

                }
            }).execute();

        }else{
            downloadGifCallback.getLocalGifPath(null,false);
        }

    }

    /**
     * 跳转webview
     * @param context
     * @param redPacketInfo
     */
    public void showTargetUrl(Context context,RedPacketInfo redPacketInfo){
        showTargetUrl(context,redPacketInfo.jumpLink);
    }
    public void showTargetUrl(Context context,String targetUrl){
        LogUtil.i("showTargetUrl");
        SQWebViewDialog dialog = new SQWebViewDialog(context);
        dialog.setUrl(targetUrl);
        dialog.setAllowJumpURL(false);
        dialog.setCancelable(true);
        dialog.show();
    }
    //判断弹窗图片是否有空
    private boolean checkValidatePopImg(String popUrl){
        try {
            return !TextUtils.isEmpty(UrlUtils.parse(popUrl).params.get(popImgUrlKey));
        }catch (Exception e){
            return false;
        }

    }
}
