package com.sy37sdk.account.face;


import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.CheckClassUtils;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.face.data.FaceVerifyData;
import com.sy37sdk.account.face.ui.FaceVerifyConfirmActivity;
import com.sy37sdk.account.face.ui.FaceVerifyExitDialog;
import com.sy37sdk.account.face.ui.FaceVerifyWarningDialog;
import org.json.JSONObject;


/**
 * 人脸认证管理类
 */
public class FaceVerifyManager {

    private static final String TAG = "【FaceVerify】";
    private static FaceVerifyManager sInstance;

    protected Context mContext;

    private FaceVerifyData faceVerifyData;

    private FaceVerifyWarningDialog faceVerifyWarningDialog;

    private SQResultListener faceVerifyResultListener;

    private FaceVerifyManager(Context context) {
        this.mContext = context;
    }


    public void setFaceVerifyData(FaceVerifyData faceVerifyData) {
        this.faceVerifyData = faceVerifyData;
    }

    public FaceVerifyData getFaceVerifyData() {
        return this.faceVerifyData;
    }

    public static FaceVerifyManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (FaceVerifyManager.class) {
                if (sInstance == null) {
                    sInstance = new FaceVerifyManager(context);
                }
            }
        }
        return sInstance;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        SQLog.v(TAG + "onActivityResult: " + requestCode);
        if (requestCode == FaceVerifyConfirmActivity.REQUEST_CODE) {
            if (resultCode == FaceVerifyConfirmActivity.RESULT_CODE) {
                if (data != null && data.getExtras() != null) {
                    Bundle bundle = data.getExtras();
                    boolean needShow = bundle.getBoolean(FaceVerifyConfirmActivity.BUNDLE_VERIFY_NEED_SHOW);
                    if (needShow) {
                        showFaceVerifyWarningDialog(faceVerifyResultListener);
                    }
                    boolean isSuccess = bundle.getBoolean(FaceVerifyConfirmActivity.BUNDLE_VERIFY_STATUS, false);
                    if (isSuccess && faceVerifyResultListener != null) {
                        faceVerifyResultListener.onSuccess(new Bundle());
                    }
                }
            }
        }
    }

    /**
     * 处理人脸认证
     */
    public void handleFaceVerify(SQResultListener faceVerifyResultListener) {
        this.faceVerifyResultListener = faceVerifyResultListener;
        if (!isAliFaceActivityExist()) {
            SQLog.w(TAG + "不满足人脸认证版本");
            if (faceVerifyResultListener != null) {
                faceVerifyResultListener.onSuccess(new Bundle());
            }
            return;
        }
        checkNeedFaceVerify(faceVerifyResultListener);
    }

    public void showFaceVerifyWarningDialog(SQResultListener faceVerifyResultListener) {
        if (faceVerifyWarningDialog == null) {
            faceVerifyWarningDialog = new FaceVerifyWarningDialog(mContext, faceVerifyResultListener);
            faceVerifyWarningDialog.setCancelable(false);
        }
        faceVerifyWarningDialog.show();
    }

    public void dismissFaceVerifyWarningDialog() {
        if (faceVerifyWarningDialog != null) {
            faceVerifyWarningDialog.dismiss();
        }
    }

    public void showExitFaceVerifyDialog(Context context, FaceVerifyExitDialog.OnClickExitListener onClickExitListener) {
        FaceVerifyExitDialog faceVerifyExitDialog = new FaceVerifyExitDialog(context);
        faceVerifyExitDialog.setCancelable(false);
        faceVerifyExitDialog.setOnClickExitListener(onClickExitListener);
        faceVerifyExitDialog.show();
    }

    /**
     * 检查是否需要人脸认证
     */
    public void checkNeedFaceVerify(final SQResultListener faceVerifyResultListener) {
        AccountRequestManager requestManager = new AccountRequestManager(mContext);
        requestManager.needFaceVerify(new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (faceVerifyResultListener != null) {
                    faceVerifyResultListener.onSuccess(new Bundle());
                }
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                try {
                    String data = dataJson.toString();
                    UrlConstant.refreshFaceUrls(data);
                    FaceVerifyData faceVerifyData = FaceVerifyData.jsonToObject(data);
                    setFaceVerifyData(faceVerifyData);
                    if (faceVerifyData.isNeedVerify()) {
                        SQLog.w(TAG + "需要人脸认证");
                        SqTrackActionManager2.getInstance().trackBtn(
                            SqTrackBtn.SqTrackBtnId.faceVerify, SqTrackBtn.SqTrackBtnExt.faceVerify);
                        showFaceVerifyWarningDialog(faceVerifyResultListener);
                    } else {
                        SQLog.d(TAG + "不需要人脸认证");
                        if (faceVerifyResultListener != null) {
                            faceVerifyResultListener.onSuccess(new Bundle());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    if (faceVerifyResultListener != null) {
                        faceVerifyResultListener.onSuccess(new Bundle());
                    }
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (faceVerifyResultListener != null) {
                    faceVerifyResultListener.onSuccess(new Bundle());
                }
            }
        });
    }

    private boolean isAliFaceActivityExist() {
        return CheckClassUtils.classExist("com.aliyun.aliyunface.ui.ToygerActivity");
    }

}
