package com.sy37sdk.account.view;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.util.AccountLoginType;

import java.util.List;

public class AccountAdapter extends BaseAdapter {

    private Context context;
    private List<UserInfo> accountList;
    private DeleteListener deleteListener;

    public AccountAdapter(Context context, List<UserInfo> accountList, DeleteListener listener) {
        this.context = context;
        this.accountList = accountList;
        this.deleteListener = listener;
    }

    @Override
    public int getCount() {
        return accountList != null ? accountList.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return accountList.size() - position - 1;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        final int realposition = accountList.size() - position - 1;
        if (convertView == null) {
            convertView = LayoutInflater.from(context)
                    .inflate(SqResUtils.getIdByName("sysq_account_item", "layout", context), null);
            viewHolder = new ViewHolder();
            viewHolder.tvUserName = convertView.findViewById(SqResUtils.getIdByName("fg_name", "id", context));
            viewHolder.deleteBtn = convertView.findViewById(SqResUtils.getIdByName("fg_delete", "id", context));
            viewHolder.ivAccountType = convertView.findViewById(SqResUtils.getIdByName("iv_account_type", "id", context));
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        UserInfo userInfo = accountList.get(realposition);
        String loginType = userInfo.getLoginType();
        switch (loginType){
            case AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE:
                viewHolder.tvUserName.setText(userInfo.getMobile());
                viewHolder.ivAccountType.setImageResource(SqResUtils.getDrawableId(context, "sysq_item_account_phone"));
                break;
            case AccountLoginType.LoginType.ACCOUNT_TYPE_WECHAT:
                viewHolder.tvUserName.setText(userInfo.getUname());
                viewHolder.ivAccountType.setImageResource(SqResUtils.getDrawableId(context, "sysq_item_account_wechat"));
                break;
            default:
                viewHolder.tvUserName.setText(TextUtils.isEmpty(userInfo.getAlias()) ? userInfo.getUname() : userInfo.getAlias());
                viewHolder.ivAccountType.setImageResource(SqResUtils.getDrawableId(context, "sysq_ic_account"));
                break;
        }
        viewHolder.deleteBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (deleteListener != null) {
                    deleteListener.onDelete(realposition);
                }
            }
        });
        return convertView;
    }

    private class ViewHolder {

        private TextView tvUserName;
        private View deleteBtn;
        private ImageView ivAccountType;

    }

    public interface DeleteListener {
        void onDelete(int index);
    }


}
