package com.sy37sdk.account.view;

import com.sqwan.common.mvp.ILoadView;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/9/9
 */
public interface IRegisterView extends ILoadView {

    /**
     * 修改获取验证码按钮的状态
     * @param enable is enable
     */
    void verifyCodeBtnStatus(boolean enable);

    /**
     * 修改用户协议checkBox选中状态
     */
    void changeUAgreeCbStatus(boolean check);

    /**
     * 修改获取验证码按钮文案
     * @param txt text
     */
    void verifyCodeBtnText(String txt);

    /**
     *
     */
    void showAccountRegister();

    void showPhoneRegister();

    /**
     * 设置自动成成的账号和密码
     * @param name  register name
     * @param pwd register password
     */
    void setAutoAccount(String name, String pwd);

    void phoneRegSuccess(Map<String, String> data);

    void accountRegSuccess(Map<String, String> data);

    /**
     * 当前页面的注册状态（账号注册/手机注册）
     * @return
     */
    int currentRegType();
}
