package com.sy37sdk.account.view.uifast.presenter;

import static com.sy37sdk.account.view.uifast.view.AccountLoginView.LOGIN_TYPE;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.sdk.libs.SqR;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AccountLogic.AccountListener;
import com.sy37sdk.account.AccountLogic.MultiAccountHandler;
import com.sy37sdk.account.AccountTools;
import com.sy37sdk.account.AutoAccountBean;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.uifast.view.IAccountLoginView;
import java.util.Map;
import org.json.JSONArray;

public class AccountLoginPresenter extends BaseAccountPagerPresenter<IAccountLoginView> implements IAccountLoginPresenter {
    public static String autoTitle = "注册成功";
    public static String autoMsg = "您的账号已注册成功！";
    public static String autoSuccess = "已截图保存至手机相册";

    /**
     * 注册账号最短长度
     */
    private static final int REG_NAME_MIN_LENGTH = 4;

    /**
     * 注册账号最大长度
     */
    private static final int REG_NAME_MAX_LENGTH = 20;

    /**
     * 密码的最小长度
     */
    private static final int REG_PWD_MIN_LENGTH = 6;

    /**
     * 注册条款是否勾选已读
     */
    private boolean clauseStatus = false;

    public AccountLoginPresenter(Context context, IAccountLoginView view) {
        super(context, view);
    }


    @Override
    public View getView() {
        return (View) mView;
    }


    @Override
    public void initData() {
        super.initData();
        mView.toggleUI(LOGIN_TYPE);
        mView.regEntrance(EntranceManager.getInstance().isUNameRegEntrance());
    }

    @Override
    public void deleteUser(UserInfo userInfo) {
        AccountTools.delAccountFromFile(context, userInfo.getUname());
    }

    @Override
    public void login(final String name, final String password) {
        if (TextUtils.isEmpty(name)) {
            ToastUtil.showToast(context, "请输入账号");
            return;
        }

        if (TextUtils.isEmpty(password)) {
            ToastUtil.showToast(context, "请输入密码");
            return;
        }
        if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                clauseStatus = true;
                mView.checkedClause();
                login(name, password);
            });
            return;
        }
        //防止多次点击
        if (mView != null) {
            mView.enableLoginBtn(false);
            mView.showLoading();
        }
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.login, SqTrackBtn.SqTrackBtnExt.login);
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_account);
        AccountLogic.getInstance(context).checkAccountList(name, password,
            (accountList, loginName, pwd) -> {
                //打开账号选择页
                if (mView != null) {
                    mView.enableLoginBtn(true);
                    mView.hideLoading();
                    mView.selectMultiAccount(accountList, loginName, pwd);
                }
            }, new AccountListener() {
                @Override
                public void onSuccess(Map<String, String> data) {
                    LoginTractionManager.track(LoginTractionManager.login_way_account, data);
                    LogUtil.i("login success");
                    if (mView != null) {
                        mView.hideLoading();
                        mView.enableLoginBtn(true);
                        mView.loginSuccess(data);
                    }
                }

                @Override
                public void onFailure(int code, String msg) {
                    LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_account, code + "", msg);
                    if (mView != null) {
                        mView.hideLoading();
                        mView.enableLoginBtn(true);
                    }
                    ToastUtil.showToast(context, msg);
                }
            });
    }

    @Override
    public void accountRegister(String name, String pwd) {
        if (TextUtils.isEmpty(name)) {
            ToastUtil.showToast(context, "请输入账号");
            return;
        }

        if (TextUtils.isEmpty(pwd)) {
            ToastUtil.showToast(context, "请输入密码");
            return;
        }

        if (name.length() < REG_NAME_MIN_LENGTH || name.length() > REG_NAME_MAX_LENGTH) {
            ToastUtil.showToast(context, SqResUtils.getStringByName(context, SqR.string.sy37_reg_account_illegal_tips));
            return;
        }
        if (pwd.length() < REG_PWD_MIN_LENGTH) {
            ToastUtil.showToast(context, SqResUtils.getStringByName(context, SqR.string.sy37_reg_password_illegal_tips));
            return;
        }
        //********新增用户服务协议
        if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                clauseStatus = true;
                mView.checkedClause();
                accountRegister(name, pwd);
            });
            return;
        }
        if (!name.equals(AccountCache.getAutoName(context)) && name.startsWith("37zd")) {
            ToastUtil.showToast(context, SqResUtils.getStringByName(context, SqR.string.sy37_reg_not_auto_account_tips));
            return;
        }
        if (mView != null) {
            mView.showLoading();
        }
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.register, SqTrackBtn.SqTrackBtnExt.register);
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_account);
        AccountLogic.getInstance(context).accountRegister(name, pwd, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                LoginTractionManager.track(LoginTractionManager.register_way_account, data);
                if (mView != null) {
                    mView.hideLoading();
                    mView.accountRegSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_account, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, msg);
                if (code == -1) {
                    autoRegister(true);
                }
            }
        });
    }

    @Override
    public void autoRegister(boolean refresh) {
        if (refresh) {
            authReq();
        } else {
            // 自动生成的账号已经被注册或者没有，就需要重新生成
            if (AccountCache.getUsername(context).equals(AccountCache.getAutoName(context)) || "".equals(AccountCache.getAutoName(context))) {
                authReq();
            } else {
                if (mView != null) {
                    mView.setAutoAccount(AccountCache.getAutoName(context), AccountCache.getAutoPassword(context));
                }
            }
        }
    }

    @Override
    public void autoAccount() {
        UserInfo lastUserInfo = AccountUtil.getLastAccountUserInfo(context);
        String accountName = "";
        String accountPwd = "";
        if (lastUserInfo != null) {
            accountName = !TextUtils.isEmpty(lastUserInfo.getAlias()) ? lastUserInfo.getAlias() : lastUserInfo.getUname();
            accountPwd = lastUserInfo.getUpwd();
            if (TextUtils.isEmpty(accountPwd) && !TextUtils.isEmpty(accountName)) {
                //如果密码为空，则从文件缓存中找
                UserInfo userByUname = AccountUtil.findUserByUname(context, accountName);
                if (userByUname != null) {
                    accountPwd = userByUname.getUpwd();
                }
            }
        }
        if (mView != null) {
            mView.setAutoAccount(accountName, accountPwd);
        }
    }

    @Override
    public void clauseClick(boolean isCheck) {
        this.clauseStatus = isCheck;
    }

    @Override
    public void toClausePage() {
        UAgreeManager.getInstance().showUserProtocol(context);
    }

    @Override
    public void toPolicy() {
        UAgreeManager.getInstance().showPolicy(context);
    }


    private void authReq() {
        AccountLogic.getInstance(context).autoAccount(new AccountLogic.AutoAccountListener() {

            @Override
            public void onSuccess(AutoAccountBean autoAccountBean) {
                LogUtil.i("msg: " + autoAccountBean.getMsg() + ", ssuccess:" + autoAccountBean.getSsuccess());
                String uname = autoAccountBean.getUname();
                String pwd = autoAccountBean.getPwd();
                AccountCache.setAutoName(context, uname);
                AccountCache.setAutoPassword(context, pwd);
                AccountCache.setAutoIssave(context, autoAccountBean.getIssave());
                AccountCache.setAutoState(context, autoAccountBean.getAutostate());
                autoTitle = autoAccountBean.getTitle();
                autoMsg = autoAccountBean.getMsg();
                autoSuccess = autoAccountBean.getSsuccess();
                if (mView != null) {
                    mView.setAutoAccount(uname, pwd);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.e("请求自动注册账号失败code=" + code + ",meg=" + msg);
            }
        });
    }
}
