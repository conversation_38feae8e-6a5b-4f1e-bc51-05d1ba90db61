package com.sy37sdk.account.trackaction;

import android.content.Context;

import com.sq.touch.CollectCallback;
import com.sq.touch.IGetEngineInterface;
import com.sq.touch.IGetUserInterface;
import com.sq.touch.TTAction;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sy37sdk.account.AccountCache;


import java.util.HashMap;
import java.util.Map;


public class TrackNoticeLog {

    public static void noticeLog(final Context context) {
        if (AccountCache.getTouch(context)) {
            TTAction.getInstance().setGetUserInterface(new IGetUserInterface() {
                @Override
                public String getUid() {
                    return AccountCache.getUserid(context);
                }
            });

            TTAction.getInstance().setGetEngineInterface(new IGetEngineInterface() {
                @Override
                public String getEngine() {
                    // 用于后续后台传入自定义引擎的view预留接口，目前暂时没有，所以传null
                    return null;
                }
            });

            TTAction.getInstance().initCollect(context, new CollectCallback() {

                @Override
                public void onSuccess(Map map) {
                    SqTrackActionManager2.getInstance()
                        .trackAction(SqTrackAction2.TOUCH, map);
                }

                @Override
                public void onFail() {

                }
            });
        }
    }

}
