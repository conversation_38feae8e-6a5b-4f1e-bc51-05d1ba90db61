package com.sy37sdk.account.age;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.parameters.performfeatureconfig.PerformFeatureKey;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.dialog.BaseNormalDialog;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.uagree.UAgreeManager;
import org.json.JSONObject;

public class AppropriateAgeManager {

    private AccountRequestManager requestManager;

    private static volatile AppropriateAgeManager instance;

    private AppropriateAgeManager() {
        requestManager = new AccountRequestManager(SQContextWrapper.getApplicationContext());
    }

    public static AppropriateAgeManager getInstance() {
        if (instance == null) {
            synchronized (UAgreeManager.class) {
                if (instance == null) {
                    instance = new AppropriateAgeManager();
                }
            }
        }
        return instance;
    }

    public void refreshConfig(final SQResultListener listener) {
        requestManager.appropriateAgeProtocol(new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                LogUtil.e("refresh appropriate age protocol config fail state: " + state + "message: " + msg);
                if (listener != null) {
                    callbackAppropriateIcon(listener);
                }
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                initConfig(dataJson);
                if (listener != null) {
                    callbackAppropriateIcon(listener);
                }
            }

            @Override
            public void onFailure(int code, String msg, @NonNull VolleyError error) {
                LogUtil.e("refresh appropriate age protocol config error code: " + code + "message: " + msg);
                if (listener != null) {
                    callbackAppropriateIcon(listener);
                }
            }
        });
    }

    private void callbackAppropriateIcon(SQResultListener listener) {
        String appropriateIconUrl = "";
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(SQContextWrapper.getApplicationContext());
        if (appropriateAge != null && appropriateAge.isStatus() && appropriateAge.getTiming() != null && appropriateAge.getTiming().contains(AppropriateAge.TIMING_ROLE)) {
            appropriateIconUrl = AppropriateAgeManager.getInstance().getAppropriateIconUrl();
        }
        Bundle bundle = new Bundle();
        bundle.putString(PerformFeatureKey.KEY_AGE_APPROPRIATE_ICON, appropriateIconUrl);
        LogUtil.i("通过后端接口回调适龄提醒url：" + appropriateIconUrl);
        listener.onSuccess(bundle);
    }

    public void refreshConfig() {
        refreshConfig(null);
    }

    private void initConfig(JSONObject data) {
        LogUtil.i("int appropriate age config:" + data);
        try {
            //适龄提醒的配置
            String configJson = data.optString("age_appropriate_config");
            AppropriateAge appropriateAge = AppropriateAge.parse(configJson);
            AppropriateAgeCacheHelper.saveAppropriateAge(SQContextWrapper.getApplicationContext(), appropriateAge);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showAppropriateAgeDialog(Context context) {
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(SQContextWrapper.getApplicationContext());
        if (appropriateAge != null && !TextUtils.isEmpty(appropriateAge.getDesc())) {
            BaseNormalDialog dialog=new BaseNormalDialog(context);
            dialog.setUrl(appropriateAge.getDesc());
            dialog.show();
        }

        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.SHOW_AGE_APPROPRIATE);
    }

    public String getAppropriateIconUrl() {
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(SQContextWrapper.getApplicationContext());
        if (appropriateAge != null && !TextUtils.isEmpty(appropriateAge.getIcon())) {
            return appropriateAge.getIcon();
        }
        return "";
    }
}
