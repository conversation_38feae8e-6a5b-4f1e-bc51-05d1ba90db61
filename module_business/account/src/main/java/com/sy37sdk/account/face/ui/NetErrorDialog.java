package com.sy37sdk.account.face.ui;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.SqResUtils;

public class NetErrorDialog extends BaseDialog {

    private Context mContext;

    private TextView tvCustomer, tvRetry;

    private OnClickNetListener mOnClickNetListener;

    public NetErrorDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_dialog_net_error"));
        tvCustomer = findViewById(SqResUtils.getId(mContext, "tv_customer"));
        tvRetry = findViewById(SqResUtils.getId(mContext, "tv_retry"));
        tvCustomer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mOnClickNetListener != null) {
                    mOnClickNetListener.clickLeft();
                }
            }
        });

        tvRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mOnClickNetListener != null) {
                    mOnClickNetListener.clickRight();
                }
            }
        });
    }


    public void setOnClickNetListener(OnClickNetListener onClickNetListener) {
        this.mOnClickNetListener = onClickNetListener;
    }

    public interface OnClickNetListener {
        void clickLeft();

        void clickRight();
    }

}
