package com.sy37sdk.account.view;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;

import com.sqwan.common.util.SqResUtils;
import com.sqwan.msdk.config.MultiConfigManager;

public class LoginSkinHelper {
    private static String skinType = "";

    public static void init() {
        String skinTypeMulti = MultiConfigManager.getInstance().getSkinType();
        if (!TextUtils.isEmpty(skinTypeMulti)) {
            skinType = skinTypeMulti;
        }
    }

    //手机号输入页面的背景资源
    public static int getPhoneLoginBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_bg_login_phone");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_bg_login");
        }
        return drawableId;
    }

    //历史账号页面的背景资源
    public static int getHistoryLoginBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_bg_login_history");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_bg_login");
        }
        return drawableId;
    }

    //账号登录页面的背景资源
    public static int getAccountLoginBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_bg_login_account");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_bg_login");
        }
        return drawableId;
    }

    //手机号+验证码登录页面
    public static int getPhoneCodeLoginBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_bg_login_phone_code");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_bg_login");
        }
        return drawableId;
    }

    //手机号+密码登录页面
    public static int getPhonePwdLoginBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_bg_login_phone_pwd");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_bg_login");
        }
        return drawableId;
    }

    //登录按钮背景
    public static int getLoginBtnBackgroundResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy_" + skinType + "_dialog_login_btn_bg");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sy_sq_dialog_login_btn_bg");
        }
        return drawableId;
    }


    //登录按钮字体颜色
    public static int getLoginBtnTextColor(Context context) {
        int colorId = SqResUtils.getColorId(context, "sy_" + skinType + "_login_btn_text_color");
        if (colorId == 0) {
            colorId = SqResUtils.getColorId(context, "sy_sq_login_btn_text_color");
        }
        return context.getResources().getColor(colorId);
    }

    //次要字体颜色
    public static int getPrimaryTextColor(Context context) {
        int colorId = SqResUtils.getColorId(context, "sy_" + skinType + "_dialog_text_primary");
        if (colorId == 0) {
            colorId = SqResUtils.getColorId(context, "sy_sq_dialog_text_primary");
        }
        return context.getResources().getColor(colorId);
    }


    //闪验页面次要字体颜色
    public static int getFastLoginPrimaryTextColor(Context context) {
        try {
            if (TextUtils.isEmpty(skinType)) {
                return Color.parseColor("#333333");
            }
            int colorId = SqResUtils.getColorId(context, "sy_" + skinType + "_dialog_text_primary");
            return context.getResources().getColor(colorId);
        } catch (Exception e) {
            return Color.parseColor("#333333");
        }
    }

    //闪验登录按钮背景资源
    public static String getFastLoginBtnBackgroundResString(Context context) {
        try {
            if (TextUtils.isEmpty(skinType)) {
                return "sysq_dialog_login_btn_bg";
            }
            return "sy_" + skinType + "_dialog_login_btn_bg";
        } catch (Exception e) {
            return "sysq_dialog_login_btn_bg";
        }
    }

    //闪验登录按钮字体颜色
    public static int getFastLoginBtnTextColor(Context context) {
        try {
            if (TextUtils.isEmpty(skinType)) {
                return Color.parseColor("#ffffff");
            }
            int colorId = SqResUtils.getColorId(context, "sy_" + skinType + "_login_btn_text_color");
            return context.getResources().getColor(colorId);
        } catch (Exception e) {
            return Color.parseColor("#ffffff");
        }
    }

    //闪验登录页面背景资源
    public static String getPhoneLoginBackgroundResString(Context context) {
        try {
            if (TextUtils.isEmpty(skinType)) {
                return "sysq_dialog_login_bg";
            }
            return "sy_" + skinType + "_bg_login_fast";
        } catch (Exception e) {
            return "sysq_dialog_login_bg";
        }
    }


    public static int getTipInputColor(Context context) {
        int colorId = SqResUtils.getColorId(context, "sy" + skinType + "_dialog_login_text_primary");
        if (colorId == 0) {
            colorId = SqResUtils.getColorId(context, "sysq_dialog_login_text_primary");
        }
        return context.getResources().getColor(colorId);
    }

    public static int getLoginTextAccentColor(Context context) {
        int colorId = SqResUtils.getColorId(context, "sy" + skinType + "_dialog_login_text_accent");
        if (colorId == 0) {
            colorId = SqResUtils.getColorId(context, "sysq_dialog_login_text_accent");
        }
        return context.getResources().getColor(colorId);
    }

    public static int getLoginTextHintColor(Context context) {
        int colorId = SqResUtils.getColorId(context, "sy" + skinType + "_dialog_login_text_hint");
        if (colorId == 0) {
            colorId = SqResUtils.getColorId(context, "sysq_dialog_login_text_hint");
        }
        return context.getResources().getColor(colorId);
    }


    public static int getCloseIconResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy" + skinType + "_ic_close");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sysq_ic_close");
        }
        return drawableId;
    }

    public static int getBackIconResId(Context context) {
        int drawableId = SqResUtils.getDrawableId(context, "sy" + skinType + "_ic_back");
        if (drawableId == 0) {
            drawableId = SqResUtils.getDrawableId(context, "sysq_ic_back");
        }
        return drawableId;
    }

}
