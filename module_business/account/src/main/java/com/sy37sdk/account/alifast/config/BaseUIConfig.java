package com.sy37sdk.account.alifast.config;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.util.TypedValue;
import android.view.Surface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.view.LoginSkinHelper;


public abstract class BaseUIConfig {
    public Activity mActivity;
    public Context mContext;
    public PhoneNumberAuthHelper mAuthHelper;
    public int mScreenWidthDp;
    public int mScreenHeightDp;

    public static BaseUIConfig init(int type, Activity activity, PhoneNumberAuthHelper authHelper) {
        switch (type) {
            case Constant.DIALOG:
                return new DialogConfig(activity, authHelper);
            default:
                return null;
        }
    }

    public BaseUIConfig(Activity activity, PhoneNumberAuthHelper authHelper) {
        mActivity = activity;
        mContext = activity.getApplicationContext();
        mAuthHelper = authHelper;
    }

    protected View initSwitchView() {
        TextView switchTV = new TextView(mActivity);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, DisplayUtil.dip2px(mActivity, 50));
        switchTV.setText(SqResUtils.getStringByName(mContext, "sysq_switch_msg"));
        switchTV.setTextColor(LoginSkinHelper.getFastLoginPrimaryTextColor(mContext));
        switchTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        switchTV.setLayoutParams(mLayoutParams);
        switchTV.setOnClickListener(v -> {
            if (listener != null) {
                listener.clickOtherLogin();
            }
        });
        return switchTV;
    }

    protected View initForgetPwdView() {
        TextView forgetPwdTV = new TextView(mActivity);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, DisplayUtil.dip2px(mActivity, 50));
//        forgetPwdTV.setText(SqResUtils.getStringByName(mContext, "sysq_forget_pwd"));
        //因为覆盖旧版本闪验出现找不到资源id的问题，改用直接使用字符串
        forgetPwdTV.setText("找回账号/密码");
        forgetPwdTV.setTextColor(LoginSkinHelper.getFastLoginPrimaryTextColor(mContext));
        forgetPwdTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        forgetPwdTV.setLayoutParams(mLayoutParams);
        forgetPwdTV.setOnClickListener(v -> {
            if (listener != null) {
                listener.clickForgetPwd();
            }
        });
        return forgetPwdTV;
    }

    protected View initDivide() {
        TextView forgetPwdTV = new TextView(mActivity);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, DisplayUtil.dip2px(mActivity, 50));
        forgetPwdTV.setText("   |   ");
        forgetPwdTV.setTextColor(LoginSkinHelper.getFastLoginPrimaryTextColor(mContext));
        forgetPwdTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        forgetPwdTV.setLayoutParams(mLayoutParams);
        return forgetPwdTV;
    }

    protected View initViewGroup(int marginTop) {
        LinearLayout linearLayout = new LinearLayout(mActivity);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
            LayoutParams.WRAP_CONTENT,
            DisplayUtil.dip2px(mActivity, 50)
        );
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        layoutParams.setMargins(0, DisplayUtil.dip2px(mContext, marginTop), 0, 0);
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);
        linearLayout.setLayoutParams(layoutParams);
        linearLayout.addView(initForgetPwdView());
        linearLayout.addView(initDivide());
        linearLayout.addView(initSwitchView());
        return linearLayout;
    }

    protected void updateScreenSize(int authPageScreenOrientation) {
        int screenHeightDp = DisplayUtil.px2dip(mContext, DisplayUtil.getScreenHeight(mContext));
        int screenWidthDp = DisplayUtil.px2dip(mContext, DisplayUtil.getScreenWidth(mContext));
        int rotation = mActivity.getWindowManager().getDefaultDisplay().getRotation();
        if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_BEHIND) {
            authPageScreenOrientation = mActivity.getRequestedOrientation();
        }
        if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_LANDSCAPE) {
            rotation = Surface.ROTATION_90;
        } else if (authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
                || authPageScreenOrientation == ActivityInfo.SCREEN_ORIENTATION_USER_PORTRAIT) {
            rotation = Surface.ROTATION_180;
        }
        switch (rotation) {
            case Surface.ROTATION_0:
            case Surface.ROTATION_180:
                mScreenWidthDp = screenWidthDp;
                mScreenHeightDp = screenHeightDp;
                break;
            case Surface.ROTATION_90:
            case Surface.ROTATION_270:
                mScreenWidthDp = screenHeightDp;
                mScreenHeightDp = screenWidthDp;
                break;
            default:
                break;
        }
    }

    public abstract void configAuthPage();

    /**
     * 在横屏APP弹竖屏一键登录页面或者竖屏APP弹横屏授权页时处理特殊逻辑
     * Android8.0只能启动SCREEN_ORIENTATION_BEHIND模式的Activity
     */
    public void onResume() {

    }

    public interface OnClickFastLoginListener {
        void clickOtherLogin();

        void clickClose();

        void clickForgetPwd();

        void clickBack();
    }

    protected OnClickFastLoginListener listener;

    public void setOnClickFastLoginListener(OnClickFastLoginListener listener) {
        this.listener = listener;
    }
}
