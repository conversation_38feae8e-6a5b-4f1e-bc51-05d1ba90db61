package com.sy37sdk.account.floatview;


import android.text.TextUtils;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tools.Logger;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.UrlUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;
import com.sy37sdk.account.uagree.UAgreeManager;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONObject;

public class FloatViewDataManager {

    private AccountRequestManager requestManager;

    private static volatile FloatViewDataManager instance;

    //上次请求个人信息接口时间（后端接口限制3s之后才能再次请求）
    private long lastRequestUserTime;

    private long MAX_REQUEST_USER_INFO_INTERVAL = 3 * 1000;

    private FloatViewDataManager() {
        requestManager = new AccountRequestManager(SQContextWrapper.getApplicationContext());
    }

    public static FloatViewDataManager getInstance() {
        if (instance == null) {
            synchronized (UAgreeManager.class) {
                if (instance == null) {
                    instance = new FloatViewDataManager();
                }
            }
        }
        return instance;
    }

    /**
     * 请求个人信息
     */
    public void requestPtUserInfo(final SqHttpCallback<JSONObject> requestCallback) {
        long requestTime = System.currentTimeMillis();
        if ((requestTime - lastRequestUserTime) > MAX_REQUEST_USER_INFO_INTERVAL) {
            final String uid = AccountCache.getUserid(SQContextWrapper.getApplicationContext());
            requestManager.getPtUserInfo(new SimpleSqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject dataJson) {
                    savePtUserInfo(uid, dataJson.toString());
                    if (requestCallback != null) {
                        requestCallback.onSuccess(dataJson);
                    }
                }
            });
            lastRequestUserTime = requestTime;
        }
    }

    public void requestRedDot(List<MenuConfig> menuConfigs, SimpleSqHttpCallback<JSONObject> callback) {
        StringBuilder redTitleSb = new StringBuilder();
        //活动相关，必传
        String pageUuid = "";
        if (menuConfigs != null && menuConfigs.size() > 0) {
            for (MenuConfig menuConfig : menuConfigs) {
                if (menuConfig.needRedDot()) {
                    redTitleSb.append(menuConfig.title).append(",");
                    if (TextUtils.isEmpty(pageUuid)) {
                        pageUuid = UrlUtils.readValueFromUrlStrByParamName(menuConfig.openUrl, "page_uuid");
                    }
                }

            }
        }
        if (redTitleSb.length() <= 0) {
            Logger.info("不需要显示红点");
            return;
        }
        requestManager.floatRedPoint(pageUuid, redTitleSb.substring(0, redTitleSb.length() - 1), callback);
    }

    /**
     * 保存用户信息，以uid做键
     *
     * @param uid
     * @param data
     */
    private void savePtUserInfo(String uid, String data) {
        try {
            FloatUserInfo floatUserInfo = FloatUserInfo.parse(data);
            floatUserInfo.setUid(uid);
            FloatViewDataCacheHelper.saveFloatUserInfo(floatUserInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void modifyPtUserInfo(String nickName, String avatar) {
        try {
            FloatUserInfo floatUserInfo = FloatViewDataCacheHelper.getFloatUserInfo();
            if (!TextUtils.isEmpty(nickName)) {
                floatUserInfo.setNickName(nickName);
            }
            if (!TextUtils.isEmpty(avatar)) {
                floatUserInfo.setAvatar(avatar);
            }
            FloatViewDataCacheHelper.saveFloatUserInfo(floatUserInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public FloatUserInfo getPtUserInfo() {
        return FloatViewDataCacheHelper.getFloatUserInfo();
    }


    /**
     * 获取头像列表
     */
    public void requestAvatarList() {
        requestManager.getAvatarList(new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject dataJson) {
                saveAvatars(dataJson);
            }
        });
    }

    private void saveAvatars(JSONObject dataJson) {
        try {
            String items = dataJson.optString("items");
            FloatViewDataCacheHelper.saveAvatars(items);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ArrayList<String> getAvatars() {
        return FloatViewDataCacheHelper.getAvatars();
    }

    /**
     * 修改个人信息
     */
    public void modifyPersonInfo(final String nick, final String avatar, final SqHttpCallback<Void> callback) {
        requestManager.modifyPersonInfo(nick, avatar, callback);
    }

    public List<RedDot> getRedDotCache() {
        return FloatViewDataCacheHelper.getRedDotCache();
    }

    /**
     * 存储悬浮球红点
     */
    public void saveRedDot(List<RedDot> redDotList) {
        if (redDotList == null || redDotList.size() < 1) {
            return;
        }
        List<RedDot> resultRedDots = new ArrayList<>();
        List<RedDot> redDotCaches = FloatViewDataCacheHelper.getRedDotCache();
        for (int i = 0; i < redDotList.size(); i++) {
            RedDot redDotNew = redDotList.get(i);
            int findPos = -1;
            for (int j = 0; j < redDotCaches.size(); j++) {
                RedDot redDotCache = redDotCaches.get(j);
                if (redDotNew.getTitle().equals(redDotCache.getTitle())) {
                    findPos = j;
                    break;
                }
            }
            if (findPos != -1) {
                //更新已经存在的红点的类型、数量
                RedDot redDotTemp = redDotList.get(findPos);
                redDotTemp.setRedDotTpe(redDotTemp.getRedDotTpe());
                redDotTemp.setNum(redDotTemp.getNum());
                resultRedDots.add(redDotTemp);
            } else {
                //新加入
                resultRedDots.add(redDotNew);
            }
        }
        FloatViewDataCacheHelper.saveRedDotCache(resultRedDots);
    }


    /**
     * 根据key获取对应的红点
     *
     * @param title
     * @return
     */
    public RedDot getRedDotByKey(String title) {
        if (TextUtils.isEmpty(title)) {
            return null;
        }
        List<RedDot> redDotCache = getRedDotCache();
        if (redDotCache == null || redDotCache.size() < 1) {
            return null;
        }
        for (RedDot redDot : redDotCache) {
            if (title.equals(redDot.getTitle())) {
                return redDot;
            }
        }
        return null;
    }

    /**
     * 根据key将对应的红点数量置0
     *
     * @param title
     */
    public void clearRedDotByKey(String title) {
        if (TextUtils.isEmpty(title)) {
            return;
        }
        List<RedDot> redDotCache = getRedDotCache();
        if (redDotCache == null || redDotCache.size() < 1) {
            return;
        }
        for (int i = 0; i < redDotCache.size(); i++) {
            RedDot redDot = redDotCache.get(i);
            if (title.equals(redDot.getTitle())) {
                redDot.setNum(0);
                redDotCache.set(i, redDot);
                saveRedDot(redDotCache);
                return;
            }
        }
    }

    /**
     * 更新红点数量
     */
    public void updateRedDotNum(RedDot redDot) {
        List<RedDot> redDotCache = getRedDotCache();
        if (redDotCache == null || redDotCache.size() < 1) {
            return;
        }
        int findPos = -1;
        for (int i = 0; i < redDotCache.size(); i++) {
            if (redDotCache.get(i).getTitle().equals(redDot.getTitle())) {
                findPos = i;
                break;
            }
        }
        if (findPos != -1) {
            RedDot redDotTemp = redDotCache.get(findPos);
            redDotTemp.setNum(redDot.getNum() + redDotTemp.getNum());
            redDotCache.set(findPos, redDotTemp);
            saveRedDot(redDotCache);
        }

    }

    /**
     * 清楚所有的红点缓存
     */
    public void clearRedDot() {
        FloatViewDataCacheHelper.clearRedDotCache();
    }

    /**
     * 是否还有未读的红点
     */
    public boolean hasRedDot() {
        List<RedDot> redDotCache = getRedDotCache();
        if (redDotCache == null || redDotCache.size() < 1) {
            return false;
        }
        for (RedDot redDot : redDotCache) {
            if (redDot.getNum() > 0) {
                return true;
            }
        }
        return false;
    }


}
