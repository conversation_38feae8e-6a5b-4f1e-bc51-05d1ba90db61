package com.sy37sdk.account.config;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Bundle;

import com.sq.webview.net.IRequest.RequestCallback;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.msdk.SQwanCore;
import com.sqwan.msdk.api.SQResultListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public class ConfigManager {
    private static volatile ConfigManager sInstance;
    private SQResultListener commentListener = null;
    private SQResultListener policyListener = null;
    private Context mContext;
    private String JumpUrl;
    private String SQ_PREFS="sq_prefs";

    public static ConfigManager getInstance() {
        if (sInstance == null) {
            synchronized (ConfigManager.class) {
                if (sInstance == null) {
                    sInstance = new ConfigManager();
                }
            }
        }
        return sInstance;
    }

    public void initConfigInfo(Context context) {
        setContext(context);
        ConfigRequest.getConfigInfo(context, new RequestCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject data) {
                    try {
                        LogUtil.i("initCommentInfo请求成功，jsonObject：" + data);
                        if(!data.isNull("good_review")){
                            setCommentInfo(context, data.getJSONObject("good_review").toString());
                        }
                        if(!data.isNull("double_list")){
                            setPolicyInfo(context,data.getJSONObject("double_list").toString());
                        }
                        initInfo(context);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        LogUtil.i("获取哆啦A梦后台sdk配置信息异常，e：" + e);
                    }

                }

                @Override
                public void onError(int code, String msg) {
                    LogUtil.i("initCommentInfo请求失败，errorMsg：" + msg);
                }
            }

        );
    }

    public void jumpComment(){
        SQResultListener listener = ConfigManager.getInstance().getCommentListener();
        String market = "";
        String gameId = "";
        JSONObject obj = null;
        String jumpConfig =  ConfigManager.getInstance().getJumpUrl();
        if (jumpConfig.isEmpty()){
            listener.onFailture(3,"参数异常，没有跳转参数");
            return;
        }
        try {
            obj = new JSONObject(jumpConfig);
            market = obj.optString("market");
        } catch (Exception e) {
            e.printStackTrace();
        }
        switch (market){
            case "HaoYouKuaiBao":
                gameId = obj.optString("gameId");
                if(OpenReview.toHaoYouKuaiBaoMarketDetails(mContext, gameId)){
                    listener.onSuccess(null);
                }else {
                    listener.onFailture(3, "跳转好游快爆应用商店失败");
                    ToastUtil.showToast(mContext,"请检查是否有安装好游快爆应用商店");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.COMMENT_ERROR);
                }
                break;
            case "TapTap":
                if(OpenReview.toTapTapMarketDetails()){
                    listener.onSuccess(null);
                }else {
                    listener.onFailture(3, "跳转TapTap应用商店失败");
                    ToastUtil.showToast(mContext,"请检查是否有安装TapTap应用商店");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.COMMENT_ERROR);
                }
                break;
            default:
                listener.onFailture(3, "请检查跳转应用商店参数");
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.COMMENT_ERROR);
        }
    }

    public void initInfo(Context context) {
        OpenReview.getTapTapInfo(context);
    }


    public void handlePolicySuccess(Bundle bundle,SqTrackAction2 sqTrackAction,SQResultListener listener){
        listener.onSuccess(bundle);
        SqTrackActionManager2.getInstance().trackAction(sqTrackAction);
        LogUtil.i("handlePolicySuccess: " + sqTrackAction.getName());
    }

    public void handlePolicyFailure(int code,String msg,SqTrackAction2 sqTrackAction,SQResultListener listener){
        listener.onFailture(code,msg);
        HashMap<String, String> extraMap = new HashMap<>();
        extraMap.put(SqTrackKey.reason_fail, msg);
        extraMap.put(SqTrackKey.fail_code, String.valueOf(code));
        SqTrackActionManager2.getInstance().trackAction(sqTrackAction,extraMap);
        LogUtil.i("handlePolicyFailure,code:" + code + " ,msg:"+msg);
    }


    public void setCommentInfo(Context context, String commentInfo) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
            Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString("commentInfo", commentInfo);
        editor.commit();
    }

    public String getCommentInfo(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
            Context.MODE_PRIVATE);
        return uiState.getString("commentInfo", "");
    }

    public void setPolicyInfo(Context context, String policyInfo) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString("policyInfo", policyInfo);
        editor.commit();
    }

    public String getPolicyInfo(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,
                Context.MODE_PRIVATE);
        return uiState.getString("policyInfo", "");
    }

    public void setCommentListener(SQResultListener listener){
        this.commentListener = listener;
    }

    public SQResultListener getCommentListener(){
        return this.commentListener;
    }

    public void setPolicyListener(SQResultListener listener){
        this.policyListener = listener;
    }

    public SQResultListener getPolicyListener(){
        return this.policyListener;
    }

    public void setContext(Context context){
        this.mContext = context;
    }

    public Context getContext(){
        return this.mContext;
    }

    public void setJumpUrl(String url){
        this.JumpUrl = url;
    }

    public String getJumpUrl(){
        return this.JumpUrl;
    }
}
