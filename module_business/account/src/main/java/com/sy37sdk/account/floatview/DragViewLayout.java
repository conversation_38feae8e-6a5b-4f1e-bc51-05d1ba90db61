package com.sy37sdk.account.floatview;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ViewUtils;
import java.util.ArrayList;
import java.util.List;
import notchtools.geek.com.notchtools.NotchTools;


/**
 * <AUTHOR>
 * 可拖拽贴边的view
 */
public class DragViewLayout extends CheckSystemUiViewBase implements SystemUiCheckUtils.onSystemUiChangedCallback {
    private boolean checkStayEdge = true;
    private boolean checkRecordPos = true;
    private boolean checkShowCompelete = true;
    private boolean authStartAnim = true;
    public boolean isAuthStartAnim() {
        return authStartAnim;
    }
    public boolean isCheckShowCompelete() {
        return checkShowCompelete;
    }

    public boolean isCheckStayEdge() {
        return checkStayEdge;
    }

    public boolean isCheckRecordPos() {
        return checkRecordPos;
    }

    public FloatViewUtils.FloatViewConfig floatViewConfig;
    public int mWidth,mHeight;

    @Nullable private Activity activity;

    /**
     * 贴边是否在左边
     */
    public boolean isLeft = true;

    /**
     * 是否在拖拽过程中
     */
    protected boolean isDrag = false;

    /**
     * 是否动画进行中
     */
    private boolean isAnimating = false;

    private FloatWindow.OnDragCallBack mOnDragCallBack;

    public FloatWindow.DragBottom2DeleteCallback DragBottom2DeleteCallback;

    public FloatWindow.ClickListener clickListener;

    private float xInRaw,yInRaw,xInRawLast,yInRawLast;

    protected float deletedY = 0f;

    private boolean reachDeleteArea = false;

    public boolean isPostView(){
        return true;
    }

    public DragViewLayout(Context context) {
        this(context, null);
    }

    public DragViewLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DragViewLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        SystemUiCheckUtils.getInstance().addListener(this);
        initView();
        if(context instanceof Activity) this.activity = (Activity) context;
        if (isPostView()) {
            post(new Runnable() {
                @Override
                public void run() {
                    mWidth = getWidth();
                    mHeight = getHeight();
                }
            });
        }



    }
    private boolean checkDragBottom2Delete(boolean moveFlag,boolean isDrag){
        if (DragBottom2DeleteCallback !=null) {
            if (isDrag) {
                if (moveFlag) {
                    if (reachDeleteArea()) {
                        DragBottom2DeleteCallback.onDragBottom2Delete(false,true,true);
                    }else{
                        DragBottom2DeleteCallback.onDragBottom2Delete(false,true,false);
                    }
                }else{
                    if (reachDeleteArea()) {
                        canAnimate();
                        DragBottom2DeleteCallback.onDragBottom2Delete(true,false,true);
                        return true;

                    }else{
                        DragBottom2DeleteCallback.onDragBottom2Delete(false,false,true);
                    }
                }

            }

        }
        return false;

    }
    public boolean canDispatchTouchEvent = true;

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (!canDispatchTouchEvent) {
            return super.dispatchTouchEvent(event);
        }
        int action = event.getAction();
        yInRaw = event.getRawY();
        xInRaw = event.getRawX();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                removeCallbacks(mStayEdgeRunnable);
                isDrag = false;
                break;
            case MotionEvent.ACTION_MOVE:
                float dy = (yInRaw - yInRawLast);
                float dx = (xInRaw - xInRawLast);
                if (Math.abs(dx) > mTouchSlop || Math.abs(dy) > mTouchSlop) {
                    if (!isDrag && mOnDragCallBack != null) {
                        mOnDragCallBack.onStartDrag();
                    }
                    isDrag = true;
                }
                floatLayoutParams.x += dx;
                floatLayoutParams.y += dy;
                if (isDrag) {
                    updateFloatPosition();
                }
                if (checkDragBottom2Delete(true,isDrag)) {
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
                if (checkDragBottom2Delete(false,isDrag)) {
                    return true;
                }
                if (!isDrag) {
                    onClick((int) xInRaw,(int) yInRaw);
                }
                if (isDrag || !isStayEdge()) {
                    if (isAuthStartAnim()) {
                        startAnim();
                    }
                    isDrag = false;
                }

                break;
            default:
                break;
        }
        yInRawLast = yInRaw;
        xInRawLast = xInRaw;
        return super.dispatchTouchEvent(event);
    }

    public void handleClick(int x,int y){
        LogUtil.i(TAG,"handleClick");
        if (clickListener!=null) {
            clickListener.onClick(null,x,y);
        }
    }
    private void onClick(int x,int y){
        LogUtil.i(TAG,"onClick");
        if (isCheckShowCompelete()) {
            if (isShowComplete()) {
                handleClick(x,y);
            }else{
                showComplete();
            }
        }else{
            handleClick(x,y);
        }

    }
    private boolean reachDeleteArea(){
        boolean lastreachDeleteArea = reachDeleteArea;
        reachDeleteArea = (( floatLayoutParams.y+mHeight) > deletedY)&&reachDeleteAreaX();
        if (lastreachDeleteArea!=reachDeleteArea && reachDeleteArea) {
        }
        return reachDeleteArea;
    }
    protected int getRegionnWidth(){
        return floatViewConfig.regionWidth;

    }
    protected int getRegionHeight(){
        return floatViewConfig.regionHeight;

    }
    private boolean reachDeleteAreaX(){
        int minX = getRegionnWidth()/3;
        int maxX =  getRegionnWidth()-minX;
        boolean result = ( floatLayoutParams.x>=minX &&  floatLayoutParams.x<=maxX);
        LogUtil.d(TAG,"reachDeleteAreaX:"+result);
        return result;
    }
    public void updateFloatPosition() {
        int x =  floatLayoutParams.x;
        int y =  floatLayoutParams.y;
        if(y < 0) {
            y = 0;
        }
        if(y > getRegionHeight() - mHeight) {
            y = getRegionHeight() - mHeight;
        }
        if(x < 0) {
            x = 0;
        }
        if(x > getRegionnWidth() - mWidth) {
            x = getRegionnWidth() - mWidth;
        }
        floatLayoutParams.x = x;
        floatLayoutParams.y = y;
        updateViewLayout();
    }

    private boolean isLeft(){
        return ( floatLayoutParams.x+mWidth/2)  < getRegionnWidth() / 2;
    }

    protected boolean isNotchAffected() {
        return activity != null && NotchTools.getFullScreenTools().isNotchScreen(activity.getWindow())
                && this.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    protected void handleOnAnimationUpdate(ValueAnimator animation){
        if (isNotchAffected()) {
            floatLayoutParams.y = (int) animation.getAnimatedValue();
        } else {
            floatLayoutParams.x = (int) animation.getAnimatedValue();
        }
    }

    protected void handleStartAnimResultPos(){
        isLeft = isLeft();
        if (isNotchAffected()) {
            valueAnimator = ValueAnimator.ofInt(floatLayoutParams.y, 0);
            return;
        }
        if (isLeft) {
            valueAnimator = ValueAnimator.ofInt( floatLayoutParams.x, 0);
        } else {
            valueAnimator = ValueAnimator.ofInt( floatLayoutParams.x, getRegionnWidth() - mWidth);
        }
    }


    protected ValueAnimator valueAnimator;
    //执行贴边动画
    private void startAnim(){
        LogUtil.i("startAnim");
        isAnimating = true;
        handleStartAnimResultPos();
        valueAnimator.setDuration(10);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                handleOnAnimationUpdate(animation);
                updateViewLayout();
            }
        });
        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                isAnimating = false;
                LogUtil.i("onAnimationEnd");
                stayEdge();
                if (mOnDragCallBack != null) {
                    mOnDragCallBack.onStayEdge();
                }
                if (!reachDeleteArea) {
                    if (checkRecordPos) {
                        FloatViewUtils.setFloatViewPos(mContext, DragViewLayout.this,floatLayoutParams.x, floatLayoutParams.y,ScreenOrientationHelper.currentType);
                    }
                }
            }
        });

        valueAnimator.start();
    }

    public void show(boolean addWindowManagerByGone) {
        //回复上次悬浮球坐标
        checkFloatViewPosition();
        if (addWindowManagerByGone) {
            ViewUtils.hidden(this);
        }
        addView();
    }
    public boolean isAnim() {
        return isAnimating;
    }




    public void setOnDragCallBack(FloatWindow.OnDragCallBack callBack) {
        mOnDragCallBack = callBack;
    }
    private boolean isShowComplete(){
        int left = 0;
        int right = floatViewConfig.regionWidth - mWidth;
        int top = 0;
        int x =  floatLayoutParams.x;
        int y = floatLayoutParams.y;
        if (!isNotchAffected() && (x==left || x==right)){
            return true;
        }
        if(isNotchAffected() && y == top) {
            return true;
        }
        return false;
    }
    private boolean isStayEdge() {
        int left = 0;
        int right = floatViewConfig.regionWidth - mWidth;
        int x =  floatLayoutParams.x;
        if (x > left + mTouchSlop && x < right - mTouchSlop) {
            LogUtil.i("isStayEdge " + false);
            return false;
        }
        if(x < left - mTouchSlop || x > right + mTouchSlop) {
            LogUtil.i("isStayEdge " + false);
            return false;
        }
        LogUtil.i("isStayEdge " + true);
        return true;
    }

    /**
     * 部分情况，悬浮球可能隐藏一半，此方法将悬浮球显示完整
     */
    public void showComplete() {
        LogUtil.i(TAG,"showComplete");
        if(isNotchAffected()) {
            floatLayoutParams.y = 0;
        } else if (isLeft) {
            floatLayoutParams.x = 0;
        } else {
            floatLayoutParams.x = floatViewConfig.regionWidth - mWidth;
        }

        updateViewLayout();
    }

    private void verifyLeft(){
        if ( floatLayoutParams.x < floatViewConfig.regionWidth / 2) {
            isLeft = true;
        } else {
            isLeft = false;
        }
    }
    protected Runnable mStayEdgeRunnable = new Runnable() {
        @Override
        public void run() {
            if( isAnimating || isDrag) return;

            if (isNotchAffected()) {
                // 刘海屏适配，存在刘海屏，则悬浮球靠上方停靠
                floatLayoutParams.y = - mHeight / 2;
            } else if (isLeft) {
                floatLayoutParams.x =  -mWidth / 2;
            } else {
                floatLayoutParams.x = floatViewConfig.regionWidth - mWidth / 2;
            }
            updateViewLayout();
        }
    };

    public void stayEdge() {
        if (isCheckStayEdge()) {
            LogUtil.i("stayEdge");
            postDelayed(mStayEdgeRunnable, 3000);
        }
    }
    private void resetData(){
        isLeft = true;
        isDrag = false;
        isAnimating = false;
        floatViewConfig=null;
    }
    private void initView(){
        resetData();
        floatViewConfig = new FloatViewUtils.FloatViewConfig(mContext);
        LogUtil.i(TAG,floatViewConfig.toString());
    }

    /**
     * 恢复上次悬浮球坐标
     */
    private void checkFloatViewPosition(){
        FloatViewUtils.FloatViewPosConfig config = FloatViewUtils.getFloatViewPositionConfig(getContext(),this);
        if (config.x!=-1&&config.y!=-1&&config.orientationType==ScreenOrientationHelper.currentType) {
            floatLayoutParams.x = config.x;
            floatLayoutParams.y = config.y;
        }else{
            floatLayoutParams.x = 0;
            floatLayoutParams.y = (int)(getRegionHeight() * 0.4);
        }
        verifyLeft();
    }
    protected void resetView(boolean resetFloatPosition) {
        initView();
        if (resetFloatPosition) {
            floatLayoutParams.gravity = Gravity.LEFT | Gravity.TOP;
            floatLayoutParams.x = 0;
            floatLayoutParams.y = (int) (getRegionHeight() * 0.4);
        }else {
            checkFloatViewPosition();
        }

        updateFloatPosition();
        stayEdge();
    }
    protected void updateViewLayout(){
        update();

    }

    @Override
    public void onSystemUiChanged(FloatViewUtils.FloatViewConfig config) {
        floatViewConfig = config;
    }

    private List<View> views = new ArrayList<>();
    protected void setFilterDragClickListener(View view, final FloatWindow.ClickListener onClickListener){
        if (clickListener==null) {
            clickListener = new FloatWindow.ClickListener() {
                @Override
                public void onClick(View view,int x, int y) {
                    boolean[] currentVisibility = new boolean[views.size()];
                    for (int i = 0; i < views.size(); i++) {
                        currentVisibility[i] = views.get(i).getVisibility()==View.VISIBLE;
                    }
                    for (int i = 0; i < views.size(); i++) {
                        View _view = views.get(i);
                        if (FloatViewUtils.isTouchPointInView(_view,x,y)) {
                            FloatWindow.ClickListener _onClickListener = (FloatWindow.ClickListener) _view.getTag();
                            if (currentVisibility[i]) {
                                _onClickListener.onClick(_view,x,y);
                            }
                        }
                    }
                }

                @Override
                public void onShowComplete() {

                }
            };
        }
        if (!views.contains(view)) {
            view.setTag(onClickListener);
            views.add(view);
        }
    }

}
