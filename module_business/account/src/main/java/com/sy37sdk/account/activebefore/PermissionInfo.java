package com.sy37sdk.account.activebefore;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-19 12:32
 */
public class PermissionInfo extends ActiveBeforeBaseInfo{

    public static final String DESC_NO_REQUEST_PERMISSION = "0"; //不申请权限
    public static final String DESC_MIDDLE_PERMISSION = "1"; //折中申请
    public static final String DESC_REQUEST_PERMISSION = "2"; //直接申请

    private String is_necessary = "0";
    private String sceneDesc = DESC_NO_REQUEST_PERMISSION;

    @Deprecated
    public boolean isNecessary(){
        return "1".equals(is_necessary);
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    @Override
    public void parse(String jsonData){
        try {
            JSONObject jsonObject = new JSONObject(jsonData);
            is_necessary = jsonObject.optString("is_necessary");
            sceneDesc = jsonObject.optString("scene_desc");
        } catch (JSONException e) {
            e.printStackTrace();

        }
    }

    @Override
    public String toString() {
        return "PermissionInfo{" +
                "is_necessary='" + is_necessary + '\'' +
                '}';
    }
}
