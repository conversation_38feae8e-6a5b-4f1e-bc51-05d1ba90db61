package com.sy37sdk.account.policy.view;

import android.content.Context;

import com.sq.data.SqR;

public class UserHistorySearchDialog extends AuthBaseDialog {

    public UserHistorySearchDialog(Context context) {
        super(context);
    }

    @Override
    protected String getContainerLayout() {
        return SqR.layout.sy37_layout_history_search_dialog;
    }

    @Override
    protected void doEngine() {
        setCanceledOnTouchOutside(false);
        tvCancel.setText("不同意");
        tvCancel.setSelected(false);
        tvOk.setText("同意");
    }

    @Override
    protected String getTitle() {
        return "查询历史账号";
    }
}
