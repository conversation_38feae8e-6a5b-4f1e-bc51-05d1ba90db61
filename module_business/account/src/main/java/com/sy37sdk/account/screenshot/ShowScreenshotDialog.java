package com.sy37sdk.account.screenshot;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;

public class ShowScreenshotDialog extends Dialog {

    private ImageView mIvScreenshot;
    private Bitmap mBitmap;

    public ShowScreenshotDialog(Context context, Bitmap bitmap) {
        this(context, SqResUtils.getIdByName("Dialog", "style", context));
        mBitmap = bitmap;
    }

    public ShowScreenshotDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    protected ShowScreenshotDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Context context = getContext();
        View view = LayoutInflater.from(context).inflate(SqResUtils.getIdByName("sy37_show_screenshot_dialog", "layout", context), null);
        setContentView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        initView(view, context);
    }

    private void initView(View view, Context context){
        mIvScreenshot = (ImageView) view.findViewById(SqResUtils.getIdByName("iv_screenshot", "id", context));
        if (mBitmap != null){
            mIvScreenshot.setImageBitmap(mBitmap);
        }
        view.findViewById(SqResUtils.getIdByName("iv_close", "id", context)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    @Override
    public void dismiss() {
        LogUtil.d("ScreenshotTest", "dismiss");
        super.dismiss();
    }
}