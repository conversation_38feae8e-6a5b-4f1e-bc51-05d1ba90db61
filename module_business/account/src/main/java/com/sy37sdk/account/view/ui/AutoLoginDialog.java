package com.sy37sdk.account.view.ui;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackAction;
import com.sqwan.common.track.SqTrackActionManager;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.SDKError;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.presenter.IAutoLoginPresenter;
import com.sy37sdk.account.view.IAutoLoginDialog;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/20
 */
public class AutoLoginDialog extends BaseDialog implements IAutoLoginDialog, DialogInterface.OnCancelListener {

    private View auto_login_layout;
    private TextView tvAccount;
    private TextView tvChangeAccount;
    private IChangeAccountListener changeAccountListener;
    private IAutoLoginPresenter presenter;
    private ILoginListener loginListener;
    private String uname;

    public AutoLoginDialog(Context context, String uname, ILoginListener listener) {
        super(context);
        loginListener = listener;
        this.uname = uname;
        setOnCancelListener(this);
        getWindow().setDimAmount(0);
    }

    public void setPresenter(IAutoLoginPresenter presenter) {
        this.presenter = presenter;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getIdByName("sy37_account_change_simple", "layout"));
        initView();
    }

    @Override
    protected void onStart() {
        super.onStart();
        presenter.setAutoLoginShow(true);
        presenter.autoLogin();
    }

    private void initView() {
        auto_login_layout = findViewById(getIdByName("auto_login_layout", "id"));
        tvChangeAccount = findViewById(getIdByName("tv_changeAccount", "id"));
        tvAccount = findViewById(getIdByName("text_account", "id"));

        //重新设置自动登录弹窗的宽度
        Configuration configuration = mContext.getResources().getConfiguration();
        int orientation = configuration.orientation;
        int screenWidth = DisplayUtil.getScreenWidth(mContext);
        int autoLayoutWidth;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            autoLayoutWidth = screenWidth / 2;
        } else {
            autoLayoutWidth = screenWidth - 2 * DisplayUtil.dip2px(mContext, 28);
        }
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) auto_login_layout.getLayoutParams();
        layoutParams.width = autoLayoutWidth;
        auto_login_layout.setLayoutParams(layoutParams);
        tvChangeAccount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount,SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_AUTO_LOGIN);
                SqTrackActionManager.getInstance().trackAction(SqTrackAction.CLICK_CHANGE_ACCOUNT_IN_AUTO_LOGIN_VIEW);
                switchAccount();
            }
        });
        tvAccount.setText(uname);
        showAnimation();
    }


    @Override
    public void autoLoginSuccess(Map<String, String> data) {
        dismiss();
        if (loginListener != null) {
            SqTrackActionManager.getInstance().trackAction(SqTrackAction.AUTO_LOGIN_SUCCESS, true);
            loginListener.onSuccess(data);
        }
    }

    private void showAnimation() {
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(auto_login_layout, "translationY", -200, 0);
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(auto_login_layout, "alpha", 0, 1);
        AnimatorSet animatorSet=new AnimatorSet();
        animatorSet.playTogether(translateAnimator,alphaAnimator);
        animatorSet.setDuration(500);
        animatorSet.start();
    }

    @Override
    public void autoLoginFail(int code, String msg) {
        ToastUtil.showToast(getContext(), msg);
        dismiss();
        if (changeAccountListener != null) {
            changeAccountListener.changeAccount();
        }
    }

    private void switchAccount() {
        presenter.setAutoLoginShow(false);
        dismiss();
        if (changeAccountListener != null) {
            changeAccountListener.changeAccount();
        }
    }

    @Override
    public void setChangeAccountListener(IChangeAccountListener listener) {
        this.changeAccountListener = listener;
    }

    @Override
    public void closeDialog() {
        dismiss();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        presenter.detachView();
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        if (loginListener != null) {
            loginListener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, SDKError.ACCOUNT_LOGIN_CANCEL.message);
        }
    }
}
