package com.sy37sdk.account.scanCode;

import static com.sqwan.common.util.PermissionHelper.SQ_REQUEST_PERMISSION_CODE;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.KeyEvent;
import android.view.TextureView;
import android.view.View;
import android.widget.ImageView;
import com.google.zxing.Result;
import com.plugin.standard.RealBaseActivity;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.face.ui.CameraPermissionDialog;
import com.sy37sdk.account.scanCode.ZxingDecoder.ResultCallback;
import com.sy37sdk.account.view.base.view.ScannerView;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/12/16
 * @desc: 扫码登录
 */
public class ScanCodeCameraActivity extends RealBaseActivity {

    protected ScannerView scannerView;
    private TextureView surfaceView;
    private ICameraOperation mICameraOperation;
    private ImageView mIvScanRect;
    private ZxingDecoder mDecoder;
    private View mLoadingView;
    private final String[] permission = {Manifest.permission.CAMERA};
    private final String[] permissionDesc = {"相机权限：用于账号授权登录验证用户身份"};
    private CameraPermissionDialog permissionDialog;
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(SqResUtils.getLayoutId(getContext(), "sy37_scan_code_activity"));
        mLoadingView = findViewById(SqResUtils.getId(getContext(), "rl_loading_img"));
        surfaceView = findViewById(SqResUtils.getId(getContext(), "scan_activity_preview"));
        scannerView = findViewById(SqResUtils.getId(getContext(), "scan_activity_mask"));
        mIvScanRect = findViewById(SqResUtils.getId(getContext(), "iv_scan_rect"));
        ImageView ivBack = findViewById(SqResUtils.getId(getContext(), "iv_back"));
        ivBack.setOnClickListener(v -> finish());
        mICameraOperation = new Camera2(getContext());
        mDecoder = new ZxingDecoder(surfaceView, new ResultCallback() {
            @Override
            public void onResult(Result result) {
                LogUtil.i(buildPrefixLog("result " + result));
                mDecoder.pause();
                scanCodeResult(result);
            }
        });
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_SCAN_PAGE);
    }

    @Override
    public void onResume() {
        super.onResume();
        surfaceView.post(new Runnable() {
            @Override
            public void run() {
                openCameraWithPermission();
            }
        });
    }


    private void openCamera() {
        LogUtil.i(buildPrefixLog("打开相机"));
        Rect rect = new Rect(mIvScanRect.getLeft(), mIvScanRect.getTop(), mIvScanRect.getRight(),
            mIvScanRect.getBottom());
        scannerView.setFraming(rect);
        mICameraOperation.openCamera(surfaceView, mDecoder, msg -> {
            ToastUtil.showToast(msg);
            finish();
        });
    }

    private void openCameraWithPermission() {
        if (!PermissionHelper.getInstance().checkPermissions(permission)) {
            boolean hasRequest = SpUtils.get(getContext()).getBoolean("sq_" + permission[0], false);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_SCAN_PERMISSION);
            PermissionHelper.getInstance().requestPermissions(getContext(), permission, permissionDesc, SQ_REQUEST_PERMISSION_CODE,
                (permissions, grantResults) -> {
                    boolean denied = false;
                    for (int grantResult : grantResults) {
                        if (grantResult != PackageManager.PERMISSION_GRANTED) {
                            denied = true;
                            break;
                        }
                    }
                    if (denied) {
                        LogUtil.w(buildPrefixLog("拒绝相机权限"));
                        if (!hasRequest) {
                            //第一次拒绝权限的情况
                            trackScanPermission("2");
                        }
                        showPermissionTipDialog();
                    } else {
                        trackScanPermission("1");
                    }
                });
        } else {
            //这里需要post是因为申请权限弹窗回来后导致textureView的高度不对（会比正常值大），导致预览变形
            surfaceView.post(this::openCamera);
        }
    }

    private void showPermissionTipDialog() {
        if (permissionDialog == null) {
            permissionDialog = new CameraPermissionDialog(getContext());
            permissionDialog.setCancelListener(() -> {
                LogUtil.v(buildPrefixLog("取消前往相机权限设置"));
                trackScanPermission("4");
                finish();
            });
            permissionDialog.setConfirmListener(() -> {
                trackScanPermission("3");
            });
        }
        permissionDialog.show();
    }

    private void scanCodeResult(Result result) {
        LogUtil.d(buildPrefixLog("授权登录请求 二维码内容：" + result.getText()));
        mLoadingView.setVisibility(View.VISIBLE);
        UserInfo userInfo = AccountCache.getUserInfo(getContext());
        if (userInfo == null) {
            LogUtil.d(buildPrefixLog("授权登录请求 用户信息为空，不请求"));
            return;
        }
        ScanCodeRequest.notifyScan(userInfo, result.getText(),
            new SimpleSqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_SCAN_SUCC);
                    mLoadingView.setVisibility(View.GONE);
                    LogUtil.d(buildPrefixLog("授权登录请求 onSuccess ：" + jsonObject.toString()));
                    startConfirmActivity(result.getText());
                    finish();
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    super.onResponseStateError(httpStatus, state, msg, data);
                    LogUtil.w(buildPrefixLog("授权登录请求失败 onResponseStateError onResponseStateError msg: " + msg + " state " + state));
                    trackScanFail(msg);
                    mLoadingView.setVisibility(View.GONE);
                    ToastUtil.showToast(msg);
                    mDecoder.resume();
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    super.onFailure(code, errorMsg, error);
                    LogUtil.w(buildPrefixLog("授权登录请求失败 onFailure msg: " + errorMsg + " state " + code));
                    trackScanFail(errorMsg);
                    mLoadingView.setVisibility(View.GONE);
                    ToastUtil.showToast(errorMsg);
                    mDecoder.resume();
                }
            });
    }

    private void startConfirmActivity(String qrCode) {
        Intent intent = new Intent();
        intent.setClass(getContext()
            , ScanCodeConfirmActivity.class);
        intent.putExtra(ScanCodeConfirmActivity.KEY_QRCODE, qrCode);
        intent.putExtra("screenOrientation", "portrait");
        getContext().startActivity(intent);
    }

    @Override
    public void onPause() {
        super.onPause();
        LogUtil.i(buildPrefixLog("关闭相机"));
        mICameraOperation.closeCamera();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionHelper.getInstance().onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    private String buildPrefixLog(String msg) {
        return "【ScanCode】" + msg;
    }

    /**
     * @param scene 是否授权，1好，2不允许，3设置，4取消
     */
    private void trackScanPermission(String scene) {
        Map<String, String> map = new HashMap<>();
        map.put("confirm_result", scene);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_SCAN_AUTH, map);
    }

    /**
     * @param reason 失败原因
     */
    private void trackScanFail(String reason) {
        Map<String, String> map = new HashMap<>();
        map.put("reason_fail", reason);
        map.put("scene", "扫码失败");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.QRCODE_LOGIN_FAIL, map);
    }

}
