package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.ActivityLifeCycleUtils.AppVisibilityCallbackAdapter;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-01-19 14:24
 */
public class BottomDeleteView extends CheckSystemUiViewBase implements SystemUiCheckUtils.onSystemUiChangedCallback {
    FloatViewUtils.FloatViewConfig floatViewConfig;
    private TextView tvStatus;
    private ImageView ivStatus;
    private View llContainer;
    private MoniterHeightListener moniterHeightListener;

    public interface MoniterHeightListener{
        void onChange(int paramsY);
    }
    public BottomDeleteView(@NonNull Context context) {
        this(context, null);
    }

    public BottomDeleteView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        ViewUtils.hidden(this);
        View.inflate(mContext, SqResUtils.getLayoutId(mContext, "sy37_view_bottom_delete"),this);
        ivStatus = findViewById(SqResUtils.getId(mContext,"iv_status"));
        tvStatus = findViewById(SqResUtils.getId(mContext,"tv_status"));
        llContainer = findViewById(SqResUtils.getId(mContext, "ll_container"));
        ActivityLifeCycleUtils.getInstance().registerActivityListener(appVisibilityCallback);
        SystemUiCheckUtils.getInstance().init(mContext);
    }
    private ActivityLifeCycleUtils.AppVisibilityCallback appVisibilityCallback= new AppVisibilityCallbackAdapter(){
        @Override
        public void onActivityPaused(Activity activity) {
            super.onActivityPaused(activity);
            hideBottomDeleteView();
        }

        @Override
        public void onActivityResumed(Activity activity) {
            super.onActivityResumed(activity);
            hideBottomDeleteView();
        }
    };
    public void setStatus(boolean highlight){
        boolean isLandscape = DisplayUtil.isLandscape(getContext());
        int deleteBgResId;
        if (highlight) {
            deleteBgResId = isLandscape ? SqResUtils.getDrawableId(getContext(), "sy37_bg_bottomdelete_landscape_highlight")
                    : SqResUtils.getDrawableId(getContext(), "sy37_bg_bottomdelete_portrait_highlight");
        }else{
            deleteBgResId = isLandscape ? SqResUtils.getDrawableId(getContext(), "sy37_bg_bottomdelete_landscape_normal")
                    : SqResUtils.getDrawableId(getContext(), "sy37_bg_bottomdelete_portrait_normal");
        }
        llContainer.setBackgroundResource(deleteBgResId);
    }

    public void attach(final MoniterHeightListener moniterHeightListener) {
        initView();
        final int height = FloatViewUtils.getBottomDeleteHeight(getContext());
        final int width = FloatViewUtils.getBottomDeleteWidth(getContext());
        ViewGroup.LayoutParams containerLayoutParams = llContainer.getLayoutParams();
        if (containerLayoutParams != null) {
            containerLayoutParams.width = width;
        }
        BottomDeleteView.this.moniterHeightListener = moniterHeightListener;
        floatLayoutParams.height = height;
        floatLayoutParams.width = floatViewConfig.regionWidth;
        int y = floatViewConfig.regionHeight -  height;
        updateY(y);
        updateX(0);
        addView();
        post(new Runnable() {
            @Override
            public void run() {
                if (moniterHeightListener!=null) {
                    moniterHeightListener.onChange(floatLayoutParams.y);
                }
            }
        });


    }
    public void showBottomDeleteView() {
        if (this.isAttachedToWindow()) {
            ViewUtils.show(this);
            update();
        }
    }

    public void hideBottomDeleteView() {
        if (this.isAttachedToWindow()) {
            ViewUtils.hidden(this);
            update();
        }
    }

    @Override
    public void release() {
        super.release();
        ActivityLifeCycleUtils.getInstance().unRegisterActivityListener(appVisibilityCallback);
    }

    public void removeBottomDeleteView() {
        release();
    }
    public void updateView(){
        initView();
        final int height = FloatViewUtils.getBottomDeleteHeight(getContext());
        floatLayoutParams.width = floatViewConfig.regionWidth;
        floatLayoutParams.height = height;
        int y = floatViewConfig.regionHeight -  height;
        updateY(y);
        updateX(0);
        update();
        if (moniterHeightListener!=null) {
            moniterHeightListener.onChange(y);
        }
    }
    private void initView(){
        FloatViewUtils.FloatViewConfig config = new FloatViewUtils.FloatViewConfig(mContext);
        this.floatViewConfig = config;
        LogUtil.i(TAG,config.toString());
    }

    @Override
    public void onSystemUiChanged(FloatViewUtils.FloatViewConfig config) {
        updateY(config.regionHeight);
        update();
    }

}
