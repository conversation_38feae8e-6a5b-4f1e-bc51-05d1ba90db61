package com.sy37sdk.account;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/24
 */
public class AutoAccountBean {

    /**
     * 账号
     */
    private String uname;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 说明标题
     */
    private String title;

    /**
     * 说明
     */
    private String  msg;

    private String ssuccess;

    private String serror;

    /**
     * 是否打开自动分配账号密码功能，1=打开，0=不打开
     */
    private String autostate;

    /**
     * 是否保存相册，1=保存，0=不保存
     */
    private String issave;


    public String getAutostate() {
        return autostate;
    }

    public String getUname() {
        return uname;
    }

    public String getPwd() {
        return pwd;
    }

    public String getTitle() {
        return title;
    }

    public String getMsg() {
        return msg;
    }

    public String getSsuccess() {
        return ssuccess;
    }

    public String getSerror() {
        return serror;
    }

    public String getIssave() {
        return issave;
    }

    public boolean isAutoAccount() {
        return "1".equals(autostate);
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public static AutoAccountBean fromJson(String jsonContent) throws JSONException {
        AutoAccountBean bean = new AutoAccountBean();
        JSONObject jsonObject = new JSONObject(jsonContent);
        bean.issave = jsonObject.optString("issave", "0");
        bean.autostate = jsonObject.optString("autostate", "0");
        bean.uname = jsonObject.optString("uname");
        bean.pwd = jsonObject.optString("pwd");
        bean.title = jsonObject.optString("title");
        bean.msg = jsonObject.optString("msg");
        bean.ssuccess = jsonObject.optString("ssuccess");
        return bean;
    }



}
