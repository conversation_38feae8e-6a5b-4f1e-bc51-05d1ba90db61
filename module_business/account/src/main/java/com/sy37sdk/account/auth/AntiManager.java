package com.sy37sdk.account.auth;

import android.app.Activity;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.TestConst;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.TimeTools;
import com.sqwan.common.util.task.Task;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.sy37sdk.account.policy.PolicyManager;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/12/11
 * <p>
 * 防沉迷
 */
public class AntiManager {

    private static AntiManager sInstance;
    private Context mContext;
    private AccountRequestManager requestManager;
    private int currentTimeTick = 0;
    private boolean isReporting = false;
    private Task reportTask = Task.create();


    public static AntiManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (AntiManager.class) {
                if (sInstance == null) {
                    sInstance = new AntiManager(context);
                }
            }
        }
        return sInstance;
    }

    private AntiManager(Context context) {
        mContext = context;
        requestManager = new AccountRequestManager(context);
    }

    public void report() {
        LogUtil.i("上报心跳");
        startReport();
    }


    private void startReport() {
        if (isReporting) {
            return;
        }
        SQLog.i("启动防沉迷上报");
        final int intervalMs = AuthConfigCache.getInterval() * 60 * 1000;
        reportTask.repeat(0, intervalMs, new Task.TaskFunc() {
            @Override
            public Task.Result exec() {
                SQLog.v("reportAnti currentTimeTick=" + currentTimeTick
                    + ", interval=" + AuthConfigCache.getInterval()
                    + ", 当前时间：" + TimeTools.stampToDate(System.currentTimeMillis() + ""));
                reportAnti();
                return null;
            }
        });
        isReporting = true;
    }

    public void stopReport() {
        SQLog.w("停止防沉迷上报");
        if (isReporting) {
            reportTask.stop();
            isReporting = false;
        }
    }


    private void showAntiDialog(String url, boolean focus) {
        LogUtil.i("显示防沉迷弹窗，focus: " + focus + ", url:" + url);
        AuthDialog dialog = new AuthDialog(mContext);
        dialog.setFocus(focus);
        dialog.setUrl(AppUtils.constructWebUrlParam(mContext, url));
        dialog.setCloseListener(new AuthDialog.CloseListener() {
            @Override
            public void onClose(String tag, String data) {
                if (TextUtils.isEmpty(tag)) {
                    return;
                }
                if (tag.equals("0") || tag.equals("exitGame")) {
                    stopReport();
                    LogUtil.i("退出游戏");
                    ((Activity) mContext).finish();
                    System.exit(0);
                }
            }
        });
        dialog.show();
    }

    /**
     * 防沉迷上报在线时长
     */
    private void reportAnti() {
        requestManager.reportUserDuration(new SqHttpCallback<JSONObject>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                SQLog.e("轮询上报接口失败 msg:" + msg);
                PolicyManager.getInstance().handleReportGuarantee(mContext);
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                if (!isReporting) {
                    return;
                }

                SQLog.v("轮询上报接口: " + dataJson);
                try {
                    if (testForceStopReportUserDuration()) {
                        return;
                    }
                    PopConfig popConfig = PopConfig.parseFromJson(dataJson);
                    long timestamp = popConfig.getTimestamp();
                    if (timestamp == 0) {
                        SQLog.w("处理reportUser服务端请求服务超时");
                        PolicyManager.getInstance().handleReportGuarantee(mContext);
                        return;
                    }
                    if (!TextUtils.isEmpty(popConfig.getUrl()) && popConfig.isShow()) {
                        SQLog.w("心跳接口判断要弹窗");
                        PolicyManager.getInstance().showPolicyDialog(mContext, popConfig.getUrl(), popConfig.isFocus());
                    } else {
                        SQLog.v("心跳接口判断是否在时间段内");
                        PolicyManager.getInstance().handleTimeLimit(
                            mContext, popConfig.getTimestamp() * 1000, popConfig.isFocus());
                    }
                } catch (JSONException e) {
                    SQLog.w("心跳接口解析异常", e);
                    PolicyManager.getInstance().handleReportGuarantee(mContext);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                SQLog.e("轮询上报接口异常 errorMsg:" + errorMsg);
                PolicyManager.getInstance().handleReportGuarantee(mContext);
            }
        });
    }

    public void reset() {
        stopReport();
        currentTimeTick = 0;
    }


    private boolean testForceStopReportUserDuration() throws JSONException {
        if (TestConst.isForceStopReportUserDuration) {
            JSONObject dataJson = new JSONObject(TestConst.forceUserStopData);
            PopConfig popConfig = PopConfig.parseFromJson(dataJson);
            if (popConfig.getNeedStop()) {
                stopReport();
                if (popConfig.getCode() != 0) {
                    AuthCountDownManager.getInstance().stopReportAuth();
                }
            }
            if (!TextUtils.isEmpty(popConfig.getUrl()) && popConfig.isShow()) {
                showAntiDialog(popConfig.getUrl(), popConfig.isFocus());
            }
            return true;
        }
        return false;
    }
}
