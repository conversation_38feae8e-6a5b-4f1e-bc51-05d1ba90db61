package com.sy37sdk.account.face;

import android.app.Activity;
import android.content.pm.ActivityInfo;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.sq.tools.Logger;
import com.sqwan.common.util.CutoutUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;

import notchtools.geek.com.notchtools.NotchTools;

public class FaceActivityHelper {

    /**
     * 设置全屏、隐藏导航栏等
     * @param activity
     */
    public static final void requestFull(Activity activity) {
        try{
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);//竖屏
            activity.requestWindowFeature(Window.FEATURE_NO_TITLE);
            final Window window = activity.getWindow();
            window.setSoftInputMode(
                    WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                            | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                WindowManager.LayoutParams layoutParams = window.getAttributes();
                layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                window.setAttributes(layoutParams);
            }

            //隐藏导航栏
            window.getDecorView().setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    StatusBarUtil.hideSystemUI(window);
                }
            });
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 填充状态栏
     * @param activity
     */
    public static final void initStatusView(Activity activity) {
        try{
            View statusView = activity.findViewById(SqResUtils.getId(activity, "status_view"));
            if (statusView == null) {
                return;
            }
            int statusHeight = NotchTools.getFullScreenTools().getStatusHeight(activity.getWindow());
            int notchHeight = NotchTools.getFullScreenTools().getNotchHeight(activity.getWindow());
            boolean hasNotchScreen = true;
            hasNotchScreen = CutoutUtil.hasNotchScreen(activity);
            Logger.info("statusHeight=" + statusHeight + " notchHeight=" + notchHeight + " 是否刘海屏：" + hasNotchScreen);
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) statusView.getLayoutParams();
            //如果是刘海屏则偏移状态栏高度+60
            if (hasNotchScreen) {
                params.height = statusHeight;
            } else {
                //否则不偏移
                params.height = 0;
            }
            statusView.setLayoutParams(params);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
