package com.sy37sdk.account.floatview.request.bean;

public class FetchFloatWindowRedDotRspBean {

    /** 红点数量 */
    private int number;

    /** 悬浮球标题 */
    private String title;

    private String warning_type;

    private String warning_msg;

    private String warning_category;

    private int priority;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    @Override
    public String toString() {
        return "FetchFloatViewRedRspBean{" +
                "number=" + number +
                ", title='" + title + '\'' +
                '}';
    }

    public String getWarningCategory() {
        return warning_category;
    }

    public String getWarningMsg() {
        return warning_msg;
    }


    public String getWarningType() {
        return warning_type;
    }

    public int getPriority() {
        return priority;
    }

}
