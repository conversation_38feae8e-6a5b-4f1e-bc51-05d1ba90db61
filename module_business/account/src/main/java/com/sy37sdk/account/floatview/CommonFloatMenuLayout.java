package com.sy37sdk.account.floatview;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.parameters.bean.MiniProgramBean;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tools.Logger;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.net.sq.CommonUrlConstant;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.AsyncImageLoader.ImageCallback;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;
import com.sy37sdk.account.floatview.ui.FloatItemAdapter;
import com.sy37sdk.account.floatview.ui.MenuUIConfig;
import com.sy37sdk.account.screenshot.ScreenshotManager;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.json.JSONObject;

/**
 * 普通悬浮窗菜单
 */
public class CommonFloatMenuLayout extends LinearLayout {

    private final Context mContext;
    private String userCenter;
    private List<MenuConfig> configs;
    private List<RedDot> redDots = new ArrayList<>();
    private RecyclerView functionParent;

    private FloatItemAdapter floatItemAdapter;

    private boolean needUpdate = false;
    private View view;
    //昵称
    private TextView tvNick;
    //用户中心
    private TextView tvUserCenter;
    //信息修改
    private ImageView ivEdit;
    //头像
    private ImageView ivHead;
    //等级
    private ImageView ivLevel;
    //个人中心
    private View personLayout;
    //个人信息面板
    private View personPanel;
    //面板UI配置
    private MenuUIConfig mUIConfig;

    public CommonFloatMenuLayout(Context context) {
        this(context, null, 0);
    }

    public CommonFloatMenuLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CommonFloatMenuLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }


    public void setConfigs(List<MenuConfig> configs) {
        this.configs = configs;
        floatItemAdapter.setDataList(this.configs);
    }

    public void setUserCenter(String userCenter) {
        this.userCenter = userCenter;
        if (personLayout != null) {
            personLayout.setVisibility(TextUtils.isEmpty(userCenter) ? GONE : VISIBLE);
        }
    }

    public void setUIConfig(MenuUIConfig config) {
        mUIConfig = config;
        loadBackgroundImg();
        setTextColor(tvNick, config.mNicknameColor);
        setTextColor(tvUserCenter, config.mUserCenterColor);
        floatItemAdapter.setTitleColor(config.mTitleColor);
    }

    private void setTextColor(TextView tv, String textColor) {
        if (TextUtils.isEmpty(textColor)) {
            return;
        }
        try {
            tv.setTextColor(Color.parseColor(textColor));
        }catch (Exception e) {
            e.printStackTrace();
            BuglessAction.reportCatchException(e, "解析颜色错误 textColor : " + textColor + " textView : " + tv.getText() , BuglessAction.COMMON_ERROR);
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        super.onWindowFocusChanged(hasWindowFocus);
        if (!hasWindowFocus) {
            return;
        }
        if (!needUpdate) {
            return;
        }
        if (configs == null) {
            return;
        }
        FloatViewDataManager.getInstance().requestRedDot(configs,
            new SimpleSqHttpCallback<JSONObject>() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    try {
                        String res_data = jsonObject.optString("res_data");
                        ArrayList<RedDot> redDots = RedDot.getRedDots(res_data);
                        FloatViewDataManager.getInstance().saveRedDot(redDots);
                        //刷新红点
                        refreshRedDot(FloatViewDataManager.getInstance().getRedDotCache());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg,
                    @NonNull VolleyError error) {
                    super.onFailure(code, errorMsg, error);
                    refreshRedDot(FloatViewDataManager.getInstance().getRedDotCache());
                }
            });
        if (onUpdateRedDotCallback != null) {
            onUpdateRedDotCallback.onUpdateRedDotRemind();
        }
        needUpdate = false;
    }

    private void initView() {
        view = LayoutInflater.from(mContext).inflate(SqResUtils.getLayoutId(mContext, "sysq_common_float_menu"), this, true);
        initPersonPanel();
        functionParent = findViewById(SqResUtils.getId(mContext, "rv_menu_layout"));
        functionParent.setLayoutManager(new GridLayoutManager(mContext, 4));
        floatItemAdapter = new FloatItemAdapter(mContext);
        functionParent.setAdapter(floatItemAdapter);
        floatItemAdapter.setItemClickListener(new FloatItemAdapter.ItemClickListener() {
            @Override
            public void ItemOnClick(final MenuConfig config) {
                final String redDotTitle = config.title;
                final RedDot redDot = FloatViewDataManager.getInstance().getRedDotByKey(redDotTitle);
                if (config.needRedDot()) {
                    String pageUuid = UrlUtils.readValueFromUrlStrByParamName(config.openUrl, "page_uuid");
                    if (redDot != null && redDot.getNum() > 0) {
                        //通知服务端红点已读
                        SqFloatViewManager.getInstance().redDotCalled(pageUuid, redDotTitle, mContext);
                        //将对应的红点数量置0
                        //FloatViewDataManager.getInstance().clearRedDotByKey(redDotTitle);
                        //refreshRedDot(FloatViewDataManager.getInstance().getRedDotCache());
                    }
                }
                if (onMenuItemClickListener != null) {
                    onMenuItemClickListener.onMenuItemClick(config);
                }
                if ("1".equals(config.openType)) {
                    showHalfWebView(config);
                } else if ("2".equals(config.openType)) {
                    jumpBrowser(AppUtils.constructWebUrlParam(mContext, config.openUrl));
                } else if ("3".equals(config.openType)) {
                    callMethod(config);
                } else if ("4".equals(config.openType)) {
                    showWebView(config);
                }
                needUpdate = true;

                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.ball_entry_access, new HashMap<String, String>() {
                    {
                        put(SqTrackKey.ball_id, config.id);
                        put(SqTrackKey.ball_name, config.title);
                        put(SqTrackKey.ball_url, config.openUrl);
                        put(SqTrackKey.entry_red_dot, redDot != null ? redDot.getNum() + "" : "0");
                    }
                });
            }
        });
    }

    private void loadBackgroundImg() {
        ImageView ivBackground =  findViewById(SqResUtils.getId(mContext, "iv_background"));
        if (mUIConfig == null || TextUtils.isEmpty(mUIConfig.mBackgroundUrl)) {
            ivBackground.setVisibility(View.GONE);
            return;
        }
        try {
            ivBackground.setVisibility(View.VISIBLE);
            AsyncImageLoader asyncImageLoader = new AsyncImageLoader(mContext);
            asyncImageLoader.loadDrawable(mUIConfig.mBackgroundUrl, ivBackground, new ImageCallback() {
                @Override
                public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                    imageView.setImageBitmap(imageDrawable);
                }
            });
        } catch (Exception e) {
            LogUtil.e("loadBackImg", e.getMessage());
        }
    }

    public void refreshRedDot(List<RedDot> redDotList) {
        this.redDots = redDotList;
        if (mContext != null && mContext instanceof Activity) {
            ((Activity) mContext).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    floatItemAdapter.setRedDots(redDots);
                }
            });
        }
    }

    /**
     * 初始化个人面板
     */
    private void initPersonPanel() {
        if (view == null) {
            return;
        }
        ivHead = findViewById(SqResUtils.getId(mContext, "iv_head"));
        tvNick = findViewById(SqResUtils.getId(mContext, "tv_nick"));
        ivLevel = findViewById(SqResUtils.getId(mContext, "iv_level"));
        ivEdit = findViewById(SqResUtils.getId(mContext, "iv_edit"));
        personPanel = findViewById(SqResUtils.getId(mContext, "person_panel"));
        personLayout = findViewById(SqResUtils.getId(mContext, "person_layout"));
        tvUserCenter = findViewById(SqResUtils.getId(mContext, "tv_user_center"));

        ivEdit.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.modifyPersonInfo, SqTrackBtn.SqTrackBtnExt.modifyPersonInfo);
                PersonModifyDialog personModifyDialog = new PersonModifyDialog(mContext);
                personModifyDialog.setModifyPersonInfoListener(new PersonModifyDialog.ModifyPersonInfoListener() {
                    @Override
                    public void modify() {
                        setPersonInfo();
                    }
                });
                personModifyDialog.show();
            }
        });
        personLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(userCenter)) {
                    AppUtils.toSQWebUrl(mContext, userCenter, "帐户");
                }
            }
        });
        ivHead.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(userCenter)) {
                    AppUtils.toSQWebUrl(mContext, userCenter, "帐户");
                }
            }
        });
        tvNick.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(userCenter)) {
                    AppUtils.toSQWebUrl(mContext, userCenter, "帐户");
                }
            }
        });
        //如果是官斗、捷诚则不显示个人信息面板
        if (ConfigManager.getInstance(mContext).isSqSDK()) {
            personPanel.setVisibility(VISIBLE);
            setPersonInfo();
        } else {
            personPanel.setVisibility(GONE);
        }
    }

    public void setPersonInfo() {
        if (!ConfigManager.getInstance(mContext).isSqSDK()) {
            return;
        }
        FloatUserInfo floatUserInfo = FloatViewDataCacheHelper.getFloatUserInfo();
        if (floatUserInfo == null) {
            tvNick.setText(AccountCache.getUsername(mContext));
            return;
        }
        AsyncImageLoader loader = new AsyncImageLoader(mContext);
        loader.loadDrawable(floatUserInfo.getAvatar(), ivHead, new AsyncImageLoader.ImageCallback() {

            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                ivHead.setImageBitmap(imageDrawable);
            }
        });
        AsyncImageLoader loaderLevel = new AsyncImageLoader(mContext);
        loaderLevel.loadDrawable(floatUserInfo.getLevel(), ivLevel, new AsyncImageLoader.ImageCallback() {

            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                ivLevel.setImageBitmap(imageDrawable);
            }
        });
        tvNick.setText(floatUserInfo.getNickName());
    }


    /**
     * 打开竖的全屏webview
     *
     * @param config
     */
    private void showWebView(MenuConfig config) {
        Logger.info("show web dialog");
        AppUtils.toSQWebUrl(getContext(), config.openUrl, "");

    }

    /**
     * 打开半屏webview
     *
     * @param config
     */
    private void showHalfWebView(MenuConfig config) {
        Logger.info("show half web dialog");

        SQWebViewDialog dialog = new SQWebViewDialog(getContext());
        dialog.setWebViewBackgroundColor(Color.WHITE);
        dialog.setUrl(AppUtils.constructWebUrlParam(mContext, config.openUrl));
        dialog.setPortraitHeightWeight(60);
        dialog.show();
    }

    private void jumpBrowser(String url) {
        if (TextUtils.isEmpty(url)) {
            ToastUtil.showToast(getContext(), "主人, 此url无效");
            return;
        }
        try {
            Uri uri = Uri.parse(url);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            mContext.startActivity(intent);
        } catch (Exception e) {
            Logger.error("Jump Browser exception, url: %s", url, e);
        }
    }

    private void callMethod(MenuConfig config) {
        String name = config.openUrl;
        if (TextUtils.isEmpty(name)) return;
        if (name.equals(SdkMethod.SCREEN_SHOT.value)) {
            ScreenshotManager.getInstance(mContext).screenShot();
        } else if (name.equals(SdkMethod.CHANGE_ACCOUNT.value)) {
            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_FLOAT_VIEW);
            HashMap<String, String> logoutMap = new HashMap<>();
            logoutMap.put(SqTrackKey.logout_type, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_FLOAT_VIEW);
            String loginType = AccountCache.getLoginType(mContext);
            logoutMap.put(SqTrackKey.login_type, loginType);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC, logoutMap);
            switchAccount();
        }else if(name.equals(SdkMethod.SKIP_MINI_APPLET.value)){
            jumpToMiniProgram(config.sdk_method_value);
        }
    }

    private void switchAccount() {
        LiveshowEngine.getInstance().leaveLiveshowRoom(null, null);
        LiveRadioEngine.getInstance().leaveLiveRadioRoom(null, null);
        SqFloatViewManager.getInstance().dismissFloatView();
        ModHelper.get(IAccountMod.class).webEnLogin(true);
    }

    public void jumpToMiniProgram(String flag) {
        Logger.info("jumpToMiniProgram", "flag = " + flag);

        SqRequest.of(CommonUrlConstant.SKIP_APPLET_INFO_URL)
            .signV3()
            .addParam("token", ModHelper.get(IAccountMod.class).getToken())
            // 唯一标识
            .addParam("flag_id", flag)
            .addParam("dsid", SqTrackUtil.getServerid(mContext))
            .addParam("dsname", SqTrackUtil.getServerName(mContext))
            .addParam("drid", SqTrackUtil.getRoleid(mContext))
            .addParam("drname", SqTrackUtil.getRolename(mContext))
            .addParam("drlevel", SqTrackUtil.getRolelevel(mContext))
            .addParamsTransformer(new CommonParamsV3())
            .get(new SqHttpCallback<String>() {
                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                    ToastUtil.showToast(getContext(), "无法跳转到微信小程序：" + msg);
                }

                @Override
                public void onSuccess(String data) {
                    if (!SqTrackUtil.checkAppInstalled(getContext(), "com.tencent.mm")) {
                        ToastUtil.showToast(getContext(), "检测到手机没有安装微信，请安装微信后重试");
                        return;
                    }

                    MiniProgramBean miniProgramBean = MiniProgramBean.parseToObject(data);
                    switch (miniProgramBean.skipType) {
                        case MiniProgramBean.SKIP_TYPE_WECHAT_SDK:
                            IWXAPI api = WXAPIFactory.createWXAPI(getContext(), miniProgramBean.appId);
                            WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                            // 填小程序原始id
                            req.userName = miniProgramBean.miniProgramId;
                            // 拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
                            req.path = miniProgramBean.miniProgramPath;
                            // 可选打开 开发版，体验版和正式版
                            req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
                            api.sendReq(req);
                            break;
                        case MiniProgramBean.SKIP_TYPE_SCHEME_URL:
                            try {
                                Intent intent = new Intent(Intent.ACTION_VIEW);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                intent.setData(Uri.parse(miniProgramBean.schemeUrl));
                                getContext().startActivity(intent);
                                // 这里为什么不用这个 API，因为这个 API 会跳转到浏览器上面去，而不是直接跳转到微信上面去
                                //AppUtils.toSdkUrl(getContext(), miniProgramBean.schemeUrl);
                            } catch (Exception e) {
                                e.printStackTrace();
                                ToastUtil.showToast(getContext(), "微信小程序跳转失败：" + miniProgramBean.schemeUrl);
                            }
                            break;
                        default:
                            ToastUtil.showToast(getContext(), "不支持该类型跳转到微信小程序：" + miniProgramBean.skipType);
                            break;
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    ToastUtil.showToast(getContext(), errorMsg);
                }
            }, String.class);
    }

    /**
     * 悬浮球点击二级菜单，能够调用的SDK方法的枚举
     */
    public enum SdkMethod {

        SCREEN_SHOT("screen_shot"),
        CHANGE_ACCOUNT("change_account"),
        SKIP_MINI_APPLET("skip_mini_applet");

        public final String value;

        SdkMethod(String value) {
            this.value = value;
        }
    }

    private OnMenuItemClickListener onMenuItemClickListener;

    private UpdateRedDotCallback onUpdateRedDotCallback;
    public interface OnMenuItemClickListener {
        void onMenuItemClick(MenuConfig menuConfig);
    }

    public interface UpdateRedDotCallback {
        void onUpdateRedDotRemind();
    }

    public void setUpdateRedCallback(UpdateRedDotCallback updateRedCallback) {
        this.onUpdateRedDotCallback = updateRedCallback;
    }

    public void setOnMenuItemClickListener(OnMenuItemClickListener onMenuItemClickListener) {
        this.onMenuItemClickListener = onMenuItemClickListener;
    }

}
