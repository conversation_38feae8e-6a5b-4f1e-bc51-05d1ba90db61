package com.sy37sdk.account.device;

import android.content.Context;
import com.sq.tool.sqtools.detector.Detector;
import com.sq.tool.sqtools.detector.DevicesFingerprint;
import com.sq.tool.sqtools.detector.TokenHelper;
import com.sq.tool.sqtools.detector.callback.DeviceCollectCallback;
import com.sq.tool.sqtools.detector.inter.IPrivateAgreement;
import com.sq.tool.sqtools.detector.sp.SPConfigInfo;
import com.sq.tool.sqtools.net.DHttpClient;
import com.sq.tool.sqtools.net.DevicesHttpCallback;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.AccountCache;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;


public class DevicesInfo {

    public static Map<String, String> deviceInfoMap = new HashMap<>();
    private static boolean activeDone = false;
    public static final int DEVICE_LOGIN_SUCC = 1;
    public static final int DEVICE_LOGIN_FAIL = 2;
    public static int loginType = 1;

    public static void setDeviceInfo(String key, String value) {
        deviceInfoMap.put(key, value);
    }

    //从pInfo数据上报接口获取
    public static void setDeviceInfoFromJson(String data) {
        if (deviceInfoMap == null) {
            deviceInfoMap = new HashMap<>();
        }
        try {
            JSONObject jsonObject = new JSONObject(data);
            deviceInfoMap.put("boot_time", getJsonValue(jsonObject,"boot_time"));
            deviceInfoMap.put("screen_brightness", getJsonValue(jsonObject,"screen_brightness"));
            deviceInfoMap.put("cpu_count", getJsonValue(jsonObject,"cpu_count"));
            deviceInfoMap.put("is_first_launch", getJsonValue(jsonObject,"is_first_launch"));
            deviceInfoMap.put("input_method_list", getJsonValue(jsonObject,"input_method_list"));
            deviceInfoMap.put("ram_total", getJsonValue(jsonObject,"ram_total"));
            deviceInfoMap.put("display_metrics", getJsonValue(jsonObject,"display_metrics"));
            deviceInfoMap.put("sensor_list", getJsonValue(jsonObject,"sensor_list"));
            deviceInfoMap.put("enable_adb", getJsonValue(jsonObject,"enable_adb"));
            deviceInfoMap.put("country", getJsonValue(jsonObject,"country"));
            deviceInfoMap.put("is_wifi_proxy", getJsonValue(jsonObject,"is_wifi_proxy"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //从激活接口获取
    public static void setDeviceInfoFromMap(Map<String, String> map) {
        if (deviceInfoMap == null) {
            deviceInfoMap = new HashMap<>();
        }
        try {
            deviceInfoMap.put("phone_model", getMapValue(map,"mode"));
            deviceInfoMap.put("os", getMapValue(map,"os"));
            deviceInfoMap.put("os_desc", getMapValue(map,"os_desc"));
            deviceInfoMap.put("os_over", getMapValue(map,"over"));
            deviceInfoMap.put("brand", getMapValue(map,"brand"));
            deviceInfoMap.put("apk_name", getMapValue(map,"dpgn"));
            deviceInfoMap.put("network_type", getMapValue(map,"nwk"));
            deviceInfoMap.put("battery_level", getMapValue(map,"battery_level"));
            deviceInfoMap.put("battery_status", getMapValue(map,"battery_status"));
            deviceInfoMap.put("ssid", getMapValue(map,"ssid"));
            deviceInfoMap.put("bssid", getMapValue(map,"bssid"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setBaseDeviceInfo(Context context) {
        if (deviceInfoMap == null) {
            deviceInfoMap = new HashMap<>();
        }
        try {
            String android_id = SensitiveInfoManager.getInstance().getAndroidId(context);
            String oaid = DeviceUtils.getOaid(context);
            String imei = ImeiLogic.getInstance(context).getFromCache() == null ? "" : ImeiLogic.getInstance(context).getFromCache().getValue();
            String mac = MacLogic.getInstance(context).getFromCache() == null ? "" : MacLogic.getInstance(context).getFromCache().getValue();
            deviceInfoMap.put("d_android_id", android_id);
            deviceInfoMap.put("d_oaid", oaid);
            deviceInfoMap.put("d_mac", mac);
            deviceInfoMap.put("d_imei", imei);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void initDevices(final Context context) {
        LogUtil.i("调用initDevices");
        Detector.build().setPrivateAgreement(new IPrivateAgreement() {
            @Override
            public boolean getPrivateAgreement() {
                return true;
            }
        }).setCollectCallback(new DeviceCollectCallback() {
            @Override
            public void onSuccess(Map<String, String> map) {
                LogUtil.i("DEVICE_INFO回调onSuccess");
                map.put("token", DevicesFingerprint.getDevToken(context));
                if (getActive()) {
                    LogUtil.i("loginType-->"+loginType);
                    switch (loginType) {
                        case DEVICE_LOGIN_SUCC:
                            map.put("userid", AccountCache.getUserid(context));
                            SqTrackActionManager2.getInstance()
                                .trackAction(SqTrackAction2.DEVICE_INFO_LOGIN, map);
                            LogUtil.i("DEVICE_INFO_LOGIN");
                            break;
                        case DEVICE_LOGIN_FAIL:
                            SqTrackActionManager2.getInstance()
                                .trackAction(SqTrackAction2.DEVICE_INFO_LOGIN_FAIL,  map);
                            LogUtil.i("DEVICE_INFO_LOGIN_FAIL");
                            break;
                        default:
                            map.put("userid", AccountCache.getUserid(context));
                            SqTrackActionManager2.getInstance()
                                .trackAction(SqTrackAction2.DEVICE_INFO_LOGIN,map);
                            LogUtil.i("default:DEVICE_INFO_LOGIN");
                    }
                } else {
                    SqTrackActionManager2.getInstance()
                        .trackAction(SqTrackAction2.DEVICE_INFO_ACTIVE, map);
                    LogUtil.i("DEVICE_INFO_ACTIVE");
                    setActive(true);
                }
            }

            @Override
            public void onFail() {

            }
        }).setSPConfigInfo(new SPConfigInfo() {
            @Override
            public Map<String, String> getMap() {
                return deviceInfoMap;
            }
        }).setHttpClient(new DevicesHttpClient())
            .init(context);
    }


    /**
     * 仅收集设备信息上报
     */
    public static void doDeviceCollect() {
        Detector.getInstance().doDeviceCollect();
    }

    /**
     * 仅收集设备信息上报
     */
    public static void doDeviceCollect(int type) {
        loginType = type;
        Detector.getInstance().doDeviceCollect();
    }

    /**
     * 初始化与收集设备信息上报
     */
    public static void initAndDoDeviceCollect(Context context) {
        setBaseDeviceInfo(context);
        initDevices(context);
        Detector.getInstance().doDeviceCollect();
    }

    public static void setActive(boolean bool) {
        activeDone = bool;
    }

    public static boolean getActive() {
        return activeDone;
    }

    public static String getMapValue(Map<String, String> map ,String key){
        try {
            String value = map.get(key);
            if (value == null){
                return "";
            }
            return value;
        }catch (Exception e){
            return "";
        }
    }

    public static String getJsonValue(JSONObject jsonObject ,String key){
        try {
            String value = String.valueOf(jsonObject.opt(key));
            if (value.isEmpty()){
                return "";
            }
            return value;
        }catch (Exception e){
            return "";
        }
    }

}
