package com.sy37sdk.account.util;

import android.text.TextUtils;

public class AccountRegType {

    //注册的账号类型
    public static class RegType {

        //账号密码
        public static final String ACCOUNT_TYPE_ACCOUNT = "1";
        //手机
        public static final String ACCOUNT_TYPE_PHONE = "2";
        //微信
        public static final String ACCOUNT_TYPE_WECHAT ="3";
    }

    //注册方式
    public static class RegWay {
        //账号密码
        public static final String REG_ACCOUNT = "1";
        //手机号+验证码
        public static final String REG_PHONE_CODE = "2";
        //本机号一键注册
        public static final String REG_ONE_KEY = "3";
        //快速注册
        public static final String REG_FAST = "4";
        //微信
        public static final String REG_WECHAT = "5";
        //qq
        public static final String REG_QQ = "6";
        //其他
        public static final String REG_OTHER = "7";
        //未知
        public static final String REG_UNKNOWN = "8";

    }

    /**
     * 根据登录方式解析注册类型
     */
    public static String parseRegWay(String loginWay) {
        if (TextUtils.isEmpty(loginWay)) {
            return "";
        }
        switch (loginWay) {
            case AccountLoginType.LoginWay.LOGIN_ACCOUNT:
                return RegWay.REG_ACCOUNT;
            case AccountLoginType.LoginWay.LOGIN_PHONE_CODE:
                return RegWay.REG_PHONE_CODE;
            case AccountLoginType.LoginWay.LOGIN_ONE_KEY:
                return RegWay.REG_ONE_KEY;
            case AccountLoginType.LoginWay.LOGIN_WECHAT:
                return RegWay.REG_WECHAT;
            default:
                return RegWay.REG_FAST;
        }
    }

    /**
     * 根据登录方式解析注册类型
     *
     * @param loginWay
     * @return
     */
    public static String parseLoginType(String loginWay) {
        if (TextUtils.isEmpty(loginWay)) {
            return "";
        }
        switch (loginWay) {
            case AccountLoginType.LoginWay.LOGIN_PHONE_CODE:
            case AccountLoginType.LoginWay.LOGIN_ONE_KEY:
                return RegType.ACCOUNT_TYPE_PHONE;
            default:
                return RegType.ACCOUNT_TYPE_ACCOUNT;
        }
    }

    /**
     * 根据注册方式解析注册类型
     *
     * @param regWay
     * @return
     */
    public static String parseRegType(String regWay) {
        if (TextUtils.isEmpty(regWay)) {
            return "";
        }
        switch (regWay) {
            case RegWay.REG_PHONE_CODE:
            case RegWay.REG_ONE_KEY:
                return RegType.ACCOUNT_TYPE_PHONE;
            case RegWay.REG_WECHAT:
                return RegType.ACCOUNT_TYPE_WECHAT;
            default:
                return RegType.ACCOUNT_TYPE_ACCOUNT;
        }
    }


    /**
     * 是否是手机注册类型的
     *
     * @param regWay
     * @return
     */
    public static boolean isPhoneRegType(String regWay) {
        return parseRegType(regWay).equals(RegType.ACCOUNT_TYPE_PHONE);
    }


}
