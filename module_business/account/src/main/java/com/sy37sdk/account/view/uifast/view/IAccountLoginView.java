package com.sy37sdk.account.view.uifast.view;

import com.sqwan.common.mvp.ILoadView;

import com.sy37sdk.account.AccountLogic.AccountListener;
import java.util.Map;
import org.json.JSONArray;

public interface IAccountLoginView extends ILoadView {

    /**
     * 修改登录按钮的可点击状态
     */
    void enableLoginBtn(boolean enable);

    void loginSuccess(Map<String, String> data);

    /**
     * 显示账号和密码
     * @param name  register name
     * @param pwd register password
     */
    void setAutoAccount(String name, String pwd);

    void accountRegSuccess(Map<String, String> data);

    void toggleUI(int type);

    void regEntrance(boolean entrance);

    void checkedClause();

    void selectMultiAccount(JSONArray accountList, String uname, String pwd);
}
