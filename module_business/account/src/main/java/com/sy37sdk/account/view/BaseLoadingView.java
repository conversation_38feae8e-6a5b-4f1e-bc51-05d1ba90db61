package com.sy37sdk.account.view;

import android.content.Context;

import com.sqwan.common.mvp.ILoadView;
import com.sqwan.common.view.BaseView;
import com.sy37sdk.account.view.uifast.ILoginDialog;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public class BaseLoadingView extends BaseView implements ILoadView {

    protected ILoginDialog loginDialog;


    public BaseLoadingView(Context context) {
        super(context);
    }

    @Override
    public void showLoading() {
        if (loginDialog != null) {
            loginDialog.showLoading();
        }
    }

    @Override
    public void hideLoading() {
        if (loginDialog != null) {
            loginDialog.hideLoading();
        }
    }
}
