package com.sy37sdk.account.floatview;

import android.text.TextUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SpUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;

public class FloatViewDataCacheHelper {
    private static final String SP_KEY_FLOAT_USER_INFO = "sp_key_float_user_info_list";
    private static final String SP_KEY_FLOAT_AVATAR_LIST = "sp_key_float_avatar_list";
    private static final String SP_KEY_FLOAT_RED_DOT = "sp_key_float_red_dot";

    public static void saveFloatUserInfo(FloatUserInfo floatUserInfo) {
        ArrayList<FloatUserInfo> floatUserInfos = getFloatUserInfos();
        int findPos = -1;
        for (int i = 0; i < floatUserInfos.size(); i++) {
            FloatUserInfo floatUserInfoTemp = floatUserInfos.get(i);
            if (floatUserInfoTemp.getUid().equals(floatUserInfo.getUid())) {
                findPos = i;
                break;
            }
        }
        if (findPos != -1) {
            floatUserInfos.set(findPos, floatUserInfo);
        } else {
            floatUserInfos.add(floatUserInfo);
        }
        try {
            JSONArray jsonArray = new JSONArray();
            for (FloatUserInfo floatUserInfoTemp : floatUserInfos) {
                jsonArray.put(FloatUserInfo.objectToJson(floatUserInfoTemp));
            }
            SpUtils.get(SQContextWrapper.getApplicationContext()).put(SP_KEY_FLOAT_USER_INFO, jsonArray.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static ArrayList<FloatUserInfo> getFloatUserInfos() {
        ArrayList<FloatUserInfo> userInfos = new ArrayList<>();
        String userInfoStrS = SpUtils.get(SQContextWrapper.getApplicationContext()).getString(SP_KEY_FLOAT_USER_INFO);
        if (TextUtils.isEmpty(userInfoStrS)) {
            return userInfos;
        }
        try {
            JSONArray jsonArray = new JSONArray(userInfoStrS);
            if (jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    String userInfoStr = (String) jsonArray.get(i);
                    FloatUserInfo floatUserInfo = FloatUserInfo.jsonToObject(userInfoStr);
                    userInfos.add(floatUserInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userInfos;

    }

    public static FloatUserInfo getFloatUserInfo() {
        final String uid = AccountCache.getUserid(SQContextWrapper.getApplicationContext());
        return getFloatUserInfo(uid);
    }

    public static FloatUserInfo getFloatUserInfo(String uid) {
        FloatUserInfo floatUserInfo = new FloatUserInfo();
        ArrayList<FloatUserInfo> floatUserInfos = getFloatUserInfos();
        if (floatUserInfos.size() > 0) {
            for (FloatUserInfo floatUserInfoTemp : floatUserInfos) {
                if (floatUserInfoTemp.getUid().equals(uid)) {
                    floatUserInfo = floatUserInfoTemp;
                    break;
                }
            }
        }
        return floatUserInfo;
    }

    public static void saveAvatars(String avatars) {
        SpUtils.get(SQContextWrapper.getApplicationContext()).put(SP_KEY_FLOAT_AVATAR_LIST, avatars);
    }

    public static ArrayList<String> getAvatars() {
        String avatarsStr = SpUtils.get(SQContextWrapper.getApplicationContext()).getString(SP_KEY_FLOAT_AVATAR_LIST);
        ArrayList<String> avatars = new ArrayList();
        if (!TextUtils.isEmpty(avatarsStr)) {
            try {
                JSONArray jsonArray = new JSONArray(avatarsStr);
                for (int i = 0; i < jsonArray.length(); i++) {
                    String avatar = (String) jsonArray.get(i);
                    avatars.add(avatar);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return avatars;
    }

    public static List<RedDot> getRedDotCache() {
        String redDotStr = SpUtils.get(SQContextWrapper.getApplicationContext()).getString(SP_KEY_FLOAT_RED_DOT);
        ArrayList<RedDot> redDots = new ArrayList();
        if (!TextUtils.isEmpty(redDotStr)) {
            try {
                JSONArray jsonArray = new JSONArray(redDotStr);
                for (int i = 0; i < jsonArray.length(); i++) {
                    String redDotTempStr = jsonArray.optString(i);
                    RedDot redDot = RedDot.parse(redDotTempStr);
                    redDots.add(redDot);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return redDots;
    }

    public static void clearRedDotCache(){
        SpUtils.get(SQContextWrapper.getApplicationContext()).put(SP_KEY_FLOAT_RED_DOT, "");
    }

    public static void saveRedDotCache(List<RedDot> redDotList) {
        if (redDotList == null || redDotList.size() < 1) {
            return;
        }
        JSONArray redDotJsonArr = new JSONArray();
        for (RedDot redDot : redDotList) {
            redDotJsonArr.put(RedDot.objectToJson(redDot));
        }
        SpUtils.get(SQContextWrapper.getApplicationContext()).put(SP_KEY_FLOAT_RED_DOT, redDotJsonArr.toString());
    }
}
