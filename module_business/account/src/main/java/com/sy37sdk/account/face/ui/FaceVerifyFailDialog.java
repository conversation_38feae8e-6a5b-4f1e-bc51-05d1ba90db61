package com.sy37sdk.account.face.ui;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import com.sqwan.base.L;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.face.OnFaceVerifyListener;

/**
 * 人脸认证失败
 */
public class FaceVerifyFailDialog extends BaseDialog {

    private TextView tvRetry,tvQuit,tvCustomer;

    private Context mContext;

    public FaceVerifyFailDialog(Context context) {
        super(context);
        this.mContext=context;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(SqResUtils.getLayoutId(mContext,"sysq_face_fail_activity"));
        setCancelable(false);
        tvRetry=findViewById(SqResUtils.getId(mContext,"tv_retry"));
        tvQuit=findViewById(SqResUtils.getId(mContext,"tv_quit_verify"));
        tvCustomer=findViewById(SqResUtils.getId(mContext,"tv_customer"));
        tvRetry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        tvQuit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_FACE_FAIL);
                ModHelper.get(IAccountMod.class).webEnLogin(false);
                if(onFaceVerifyListener!=null){
                    onFaceVerifyListener.onVerifyFail("退出了人脸认证");
                }
            }
        });
        tvCustomer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtils.toSQWebUrl(L.getActivity(), UrlConstant.FACE_VERIFY_CUSTOMER_PAGE, "客服");
            }
        });
    }

    private OnFaceVerifyListener onFaceVerifyListener;

    public void setOnFaceVerifyListener(OnFaceVerifyListener onFaceVerifyListener){
        this.onFaceVerifyListener=onFaceVerifyListener;
    }
}
