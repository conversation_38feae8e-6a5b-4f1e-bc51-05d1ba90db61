package com.sy37sdk.account.scanCode;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV1;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.util.AccountLoginType;
import org.json.JSONObject;

/**
 * @author: gsp
 * @date: 2024/12/19
 * @desc: 扫码相关的请求
 */
public class ScanCodeRequest {

    public static void notifyScan(UserInfo userInfo, String qrCodeText, SqHttpCallback<JSONObject> callback) {
        String requestLoginType = "common";
        switch (userInfo.getLoginType()) {
            case AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE:
                requestLoginType = "phone";
                break;
            case AccountLoginType.LoginType.ACCOUNT_TYPE_WECHAT:
                requestLoginType = "wx";
                break;
        }
        SqRequest.of(UrlConstant.URL_QRCODE_SCAN)
            .addParam("os", "android")
            .addParam("code", qrCodeText)
            .addParam("token", userInfo.getToken())
            .addParam("login_type",  requestLoginType)
            .addParamsTransformer(new CommonParamsV1())
            .signV3()
            .post(callback);
    }

    public static void confirmAuth(String token, String qrCodeText,SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_QRCODE_CONFIRM_AUTH)
            .addParam("os", "android")
            .addParam("token", token)
            .addParam("code", qrCodeText)
            .addParamsTransformer(new CommonParamsV1())
            .signV3()
            .post(callback);
    }

    public static void cancelAuth(String token, String qrCodeText, SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_QRCODE_CANCEL_AUTH)
            .addParam("os", "android")
            .addParam("token", token)
            .addParam("code", qrCodeText)
            .addParamsTransformer(new CommonParamsV1())
            .signV3()
            .post(callback);
    }
}
