package com.sy37sdk.account.view.uifast.presenter;

import static com.sy37sdk.account.view.uifast.presenter.PhonePresenter.CODE_PHONE_VALID;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import com.sq.tool.network.RiskInterceptor;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountLogic;
import com.sy37sdk.account.AccountTools;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.util.AccountLoginType.LoginType;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.uifast.constant.AccountViewBundleKey;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;
import com.sy37sdk.account.view.uifast.view.IHistoryAccountView;
import java.util.Map;

public class HistoryAccountPresenter extends BaseAccountPagerPresenter<IHistoryAccountView> implements IHistoryAccountPresenter {

    /**
     * 注册条款是否勾选已读
     */
    private boolean clauseStatus = false;

    public HistoryAccountPresenter(Context context, IHistoryAccountView view) {
        super(context, view);
    }


    @Override
    public void initData() {
        super.initData();
        UserInfo lastUserInfo = AccountUtil.getLastUserInfo(context);
        if (lastUserInfo != null && !TextUtils.isEmpty(lastUserInfo.getUname())) {
            mView.setAccount(lastUserInfo);
        } else {
            mView.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE);
        }
    }

    @Override
    public View getView() {
        return (View) mView;
    }


    @Override
    public void deleteUser(UserInfo userInfo) {
        AccountTools.delAccountFromFile(context, userInfo.getUname());
    }

    @Override
    public void login(UserInfo userInfo) {
        fastLogin(userInfo);
    }

    /**
     * 快速登录
     */
    private void fastLogin(final UserInfo userInfo) {
        if (!clauseStatus) {
            UAgreeManager.getInstance().showLoginPolicyAlert(() -> {
                mView.checkedClause();
                clauseStatus = true;
                fastLogin(userInfo);
            });
            return;
        }
        if (userInfo != null) {
            if(userInfo.isPhoneLoginType() && !TextUtils.isEmpty(userInfo.getTicket())){
                //判断是否有ticket，有ticket优先用ticket登录
                AccountLogic.getInstance(context).phoneLoginTicket(userInfo.getTicket(), userInfo.getMobile(),
                    new AccountLogic.AccountListener() {
                        @Override
                        public void onSuccess(Map<String, String> account) {
                            LogUtil.i("历史账号页面快速login success");
                            trackLoginSuccess(account);
                            if (mView != null) {
                                mView.hideLoading();
                                mView.enableLoginBtn(true);
                                mView.loginSuccess(account);
                            }
                        }

                        @Override
                        public void onFailure(int code, String msg) {
                            LogUtil.i("历史账号页面ticket登录失败");
                            //ticket登录失败，如果是ticket过期则重新获取验证码
                            if(code == AccountLogic.SERVER_ERROR_CODE_TICKET_TIMEOUT){
                                loginNormal(userInfo);
                            }else {
                                ToastUtil.showToast(context, msg);}
                        }
                    });

            }
            else if (!TextUtils.isEmpty(userInfo.getToken()) && !TextUtils.isEmpty(userInfo.getRefreshToken())) {
                if (mView != null) {
                    mView.showLoading();
                }
                final String loginType = userInfo.getLoginType();
                LoginTractionManager.trackInvoke(loginType, LoginTractionManager.login_way_history);
                AccountLogic.getInstance(context).fastLogin(userInfo, new AccountLogic.AccountListener() {
                    @Override
                    public void onSuccess(Map<String, String> account) {
                        LogUtil.i("历史账号页面快速login success");
                        trackLoginSuccess(account);
                        if (mView != null) {
                            mView.hideLoading();
                            mView.enableLoginBtn(true);
                            mView.loginSuccess(account);
                        }
                    }

                    @Override
                    public void onFailure(int code, String msg) {
                        LoginTractionManager.trackFail(loginType, LoginTractionManager.login_way_history, code + "", msg);
                        if (code == RiskInterceptor.RISK_WEB_STATE_CODE) {
                            LogUtil.i("历史账号页面快速登录失败，但是是风控引起的，所以这里不触发帐密登录");
                            if (mView != null) {
                                mView.hideLoading();
                                mView.enableLoginBtn(true);
                            }
                            ToastUtil.showToast(context, msg);
                            return;
                        }
                        LogUtil.i("历史账号页面快速登录失败，切换至普通登录");
                        //快速登录失败则切换至普通登录
                        loginNormal(userInfo);
                    }
                });
            } else {
                loginNormal(userInfo);
            }
        }
    }

    /**
     * 普通登录
     */
    private void loginNormal(UserInfo userInfo) {
        if (mView != null) {
            mView.showLoading();
        }
        String mobile = userInfo.getMobile();
        boolean isLastWxLogin = TextUtils.equals(userInfo.getLoginType(), LoginType.ACCOUNT_TYPE_WECHAT);
        if (isLastWxLogin && EntranceManager.getInstance().isWxLoginEntrance()) {
            if (mView != null) {
                mView.hideLoading();
                if (EntranceManager.getInstance().getWxLoginUIVersion() == EntranceManager.WX_LOGIN_UI_VERSION_V2){
                    mView.startView(AccountPage.ACCOUNT_LOGIN_WECHAT_V2, null);
                } else {
                    mView.startView(AccountPage.ACCOUNT_LOGIN_WECHAT, null);
                }
            }
        } else if (TextUtils.isEmpty(mobile)) {
            if (TextUtils.isEmpty(userInfo.getUname()) || TextUtils.isEmpty(userInfo.getUpwd())) {
                if (mView != null) {
                    mView.hideLoading();
                    //如果账号或者密码为空则直接跳转至账号登录页
                    mView.startView(AccountPage.ACCOUNT_LOGIN_PAGE, null);
                }
            } else {
                loginByAccount(userInfo.getUname(), userInfo.getUpwd());
            }
        } else {
            if (TextUtils.isEmpty(userInfo.getUpwd())) {
                //手机号登录的且没有密码，去到发验证码页面
                obtainVerifyCode(mobile);
            } else {
                //手机号登录的有密码，则使用手机号+密码登录
                loginByMobile(userInfo.getMobile(), userInfo.getUpwd());
            }
        }
    }

    private void loginByAccount(String name, String password) {
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_history);
        AccountLogic.getInstance(context).accountLogin(name, password, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                trackLoginSuccess(data);
                LogUtil.i("login success");
                if (mView != null) {
                    mView.hideLoading();
                    mView.enableLoginBtn(true);
                    mView.loginSuccess(data);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM, LoginTractionManager.login_way_history, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                    mView.enableLoginBtn(true);
                }
                ToastUtil.showToast(context, msg);
                mView.startView(AccountPage.ACCOUNT_LOGIN_PAGE, null);
            }
        });
    }

    private void loginByMobile(final String mobile, String pwd) {
        LogUtil.i("调用手机+密码登录");
        LoginTractionManager.trackInvoke(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_history);
        AccountLogic.getInstance(context).phoneLoginPwd(mobile, pwd, new AccountLogic.AccountListener() {
            @Override
            public void onSuccess(Map<String, String> account) {
                LogUtil.i("login success");
                trackLoginSuccess(account);
                if (mView != null) {
                    mView.hideLoading();
                    mView.loginSuccess(account);
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                LogUtil.i("login onFailure");
                LoginTractionManager.trackFail(LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM, LoginTractionManager.login_way_history, code + "", msg);
                if (mView != null) {
                    mView.hideLoading();
                }
                ToastUtil.showToast(context, msg);
                obtainVerifyCode(mobile);
            }
        });
    }

    public void obtainVerifyCode(final String mobile) {
        AccountLogic.getInstance(context).sendPhoneCode(mobile, new AccountLogic.VerifyCodeListener() {
            @Override
            public void onSuccess() {
                if (mView != null) {
                    mView.hideLoading();
                    Bundle bundle = new Bundle();
                    bundle.putString(AccountViewBundleKey.mobile, mobile);
                    mView.startView(AccountPage.ACCOUNT_VERIFY_CODE_PAGE, bundle);
                    ToastUtil.showToast(context, "请求已发送，请注意查收短信");
                }
            }

            @Override
            public void onFailure(int code, String msg) {
                ToastUtil.showToast(context, msg);
                if (mView != null) {
                    mView.hideLoading();
                }
                if (code != CODE_PHONE_VALID) {
                    //获取验证码失败，跳转页面
                    if (mView != null) {
                        Bundle bundle = new Bundle();
                        bundle.putString(AccountViewBundleKey.mobile, mobile);
                        mView.startView(AccountPage.ACCOUNT_VERIFY_CODE_PAGE, bundle);
                    }
                }
            }
        });
    }

    private void trackLoginSuccess(Map<String, String> account) {
        LoginTractionManager.track(LoginTractionManager.login_way_history, account);
    }

    @Override
    public void forgetPassword() {
        super.forgotPassword();
    }

    @Override
    public void clauseClick(boolean isCheck) {
        this.clauseStatus = isCheck;
        if (isCheck) {
            SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.agreement, SqTrackBtn.SqTrackBtnExt.agreement);
        }
    }

    @Override
    public void toClausePage() {
        UAgreeManager.getInstance().showUserProtocol(context);
    }

    @Override
    public void toPolicy() {
        UAgreeManager.getInstance().showPolicy(context);
    }
}
