package com.sy37sdk.account.screenshot;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import com.sqwan.common.mod.account.IScreenshotListener;
import com.sqwan.common.util.AssetsUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;

import java.lang.ref.WeakReference;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2020-05-18
 */
public class ScreenshotManager {


    private static ScreenshotManager mInstance;
    private Context mContext;
    private IScreenshotListener mScreenshotListener;
    private MyHandler myHandler;


    private ScreenshotManager(Context context) {
        this.mContext = context;
        myHandler = new MyHandler(context, Looper.getMainLooper());
    }

    public static ScreenshotManager getInstance(Context context) {
        if(mInstance == null) {
            synchronized (ScreenshotManager.class) {
                mInstance = new ScreenshotManager(context);
            }
        }
        return mInstance;
    }

    public void screenShot() {
        if(mScreenshotListener != null) {
            ToastUtil.showToast(mContext, "点击截图");
            Thread thread = new Thread() {
                @Override
                public void run() {
                    Bitmap bitmap = mScreenshotListener.createScreenshot();
                    myHandler.obtainMessage(1, bitmap).sendToTarget();
                }
            };
            thread.start();
        } else {
            LogUtil.e("游戏未接入截图功能");
            ToastUtil.showToast(mContext, "游戏未接入截图功能！");
        }
    }

    public void setScreenshotListener(IScreenshotListener listener) {
        this.mScreenshotListener = listener;
    }

    /**
     * 解析multiconfig 文件中是否配置了开启截图功能
     */
    public boolean isOpenScreenShot() {
        Properties sdkInfo = AssetsUtils.readProperties(mContext, AssetsUtils.SQ_MULTI_CONFIG);
        if (sdkInfo != null) {
            String screenshot = sdkInfo.getProperty(AssetsUtils.PRO_SHOW_SCREENSHOT);
            return !TextUtils.isEmpty(screenshot) && "1".equals( screenshot);
        }
        return false;
    }

    /**
     * 避免内存泄露
     */
    private static class MyHandler extends Handler {

        private WeakReference<Context> contextWeakReference;

        MyHandler(Context context, Looper looper) {
            super(looper);
            contextWeakReference = new WeakReference<>(context);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            LogUtil.d("ScreenshotTest", "开始截屏显示");
            if(msg.obj == null){
                LogUtil.i("截图失败");
            }else {
                LogUtil.i("截图成功");
            }
            if(contextWeakReference.get() == null) {
                LogUtil.i("显示截图失败，context 为空");
                return;
            }
            ShowScreenshotDialog dialog = new ShowScreenshotDialog(contextWeakReference.get(), (Bitmap) msg.obj);
            dialog.show();
        }
    }


}
