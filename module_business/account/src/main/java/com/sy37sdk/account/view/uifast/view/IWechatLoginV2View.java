package com.sy37sdk.account.view.uifast.view;

import com.sqwan.common.mvp.ILoadView;
import com.sy37sdk.account.view.ui.WechatRegSuccessDialog;
import java.util.Map;

public interface IWechatLoginV2View extends ILoadView {
    void enableLoginBtn(boolean enable);

    void loginSuccess(Map<String, String> data);

    void showRegDialog(Map<String, String> data, WechatRegSuccessDialog.IWechatRegListener regListener);

    String getPhone();

    //页面切换
    void startVerifyCodeView();

    void checkedClause();
}
