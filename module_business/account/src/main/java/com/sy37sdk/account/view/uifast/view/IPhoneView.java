package com.sy37sdk.account.view.uifast.view;

import com.sqwan.common.mvp.ILoadView;

import java.util.Map;

public interface IPhoneView extends ILoadView {

    String getPhone();

    //页面切换
    void startVerifyCodeView();

    void accountRegSuccess(Map<String, String> data);

    /**
     * 刷新快速游戏入口开关
     */
    void regEntrance(boolean enable);

    /**
     * 刷新账号登录入口开关
     */
    void accountLoginEntrance(boolean enable);

    void checkedClause();
}
