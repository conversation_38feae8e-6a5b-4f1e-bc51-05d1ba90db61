package com.sy37sdk.account.activebefore;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV2;
import com.sqwan.common.request.CommonParamsV3;
import com.sy37sdk.account.UrlConstant;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-19 11:57
 */
public class ActiveBeforeRequestManager {

    public void reqGetpermission(SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.ACTIVE_BEFORE_PERMISSION)
            .signV3()
            .addParamsTransformer(new CommonParamsV2(true))
            .post(callback);
    }

    /**
     * 请求用户协议配置
     */
    public void reqUserProtocol(SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.USER_PROTOCOL_ACTIVE_BEFORE)
            .signV3()
            .addParamsTransformer(new CommonParamsV2(true))
            .post(callback);
    }

    /**
     * 获取游戏域名列表
     */
    public void getGameUrlList(SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.GET_GAME_URL_LIST)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .get(callback);
    }

    /**
     * 请求指定的域名
     */
    public void requestCustomDomain(String domain) {
        SqRequest.of(domain)
            .signV3()
            .addParamsTransformer(new CommonParamsV3())
            .get(null);
    }
}
