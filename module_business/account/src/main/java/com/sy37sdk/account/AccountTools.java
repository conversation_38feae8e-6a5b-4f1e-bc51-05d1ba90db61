package com.sy37sdk.account;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ZipString;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sy37sdk.account.entrance.EntranceManager;
import com.sy37sdk.account.trackaction.UserNameEmptyTrackAction;
import com.sy37sdk.account.util.AccountLoginType;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class AccountTools {


    /**
     * get account's path on sdcard
     *
     * @param context
     * @return
     */
    private static String getDir(Context context) {
        File file = new File(getSDPath(context) + "/" + MultiSdkManager.getInstance().getAccountDir());
        if (!file.exists()) {
            file.mkdir();
        }
        return getSDPath(context) + "/" + MultiSdkManager.getInstance().getAccountDir() + "/";
    }

    /**
     * @param context
     * @return
     */
    private static File getAccountFile(Context context) {
        //验证md5或加密
        try {
            File file = new File(getDir(context) + "/" + MultiSdkManager.getInstance().getAccountFile());
            if (!file.exists()) {
                file.createNewFile();
            }
            return file;
        } catch (Exception e) {

            System.err.println("无SDCard，获取AF失败");
            e.printStackTrace();
            return new File("");
        }

    }

    public static List<UserInfo> getAccountFromFile(Context context) {
        return getAccountFromFile(context, !EntranceManager.getInstance().isAccountLoginEntrance());
    }

    public static List<UserInfo> getAccountFromFile(Context context, boolean filterAccount) {
        File file = getAccountFile(context);
        String json = "";
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                // 显示行号
                json += tempString;
                line++;
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e1) {
                }
            }
        }
        if (json.equals("")) {
            return null;
        } else {
            List<UserInfo> accountList = new ArrayList<UserInfo>();
            try {
                String jsonDecode = ZipString.zipString2Json(json);
                JSONArray jsonArray = new JSONArray(jsonDecode);
                if (jsonArray.length() > 0) {
                    for (int index = 0; index < jsonArray.length(); index++) {
                        JSONObject userJson = jsonArray.getJSONObject(index);
                        UserInfo userInfo = UserInfo.decodeFromJson(userJson);
                        // 如果当前禁用了账号密码登录，那么在则过滤掉没有手机号的账号
                        // 因为在弹出的账号列表中，会优先显示手机号，如果没有手机号再显示用户名
                        if (filterAccount && TextUtils.isEmpty(userInfo.getMobile())) {
                            continue;
                        }
                        accountList.add(userInfo);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return accountList;
        }
    }

    public static void setAccountToFile(Context context, UserInfo uinfo) {
        UserNameEmptyTrackAction.report(UserNameEmptyTrackAction.ActionType.saveAccout, uinfo.getUname(), uinfo.toString());
        List<UserInfo> accountList = getAccountFromFile(context, false);
        File file = getAccountFile(context);

        if (accountList == null) {    //have no account
            accountList = new ArrayList<UserInfo>();
            LogUtil.i("用户缓存相关：系统的中目前的用户信息没有");
            LogUtil.i("用户缓存相关：系统的存储最新用户信息：" + uinfo.toString());

            accountList.add(uinfo);
        } else {
            int findPos = -1;
            for (int i = 0; i < accountList.size(); i++) {
                UserInfo tempUserInfo = accountList.get(i);
                if (tempUserInfo != null) {
                    //如果账号类型是手机，则优先根据手机号进行替换
                    if (!TextUtils.isEmpty(uinfo.getLoginType()) && uinfo.getLoginType().equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE)) {
                        if (!TextUtils.isEmpty(tempUserInfo.getMobile()) && !TextUtils.isEmpty(uinfo.getMobile()) && tempUserInfo.getMobile().equals(uinfo.getMobile())) {
                            findPos = i;
                        } else {
                            //如果手机号不一样，则再比较uname是否一样
                            //统一替换成小写比较， 因为后端不区分大小写
                            if (!TextUtils.isEmpty(tempUserInfo.getUname()) && !TextUtils.isEmpty(uinfo.getUname())) {
                                if (tempUserInfo.getUname().toLowerCase().equals(uinfo.getUname().toLowerCase())) {
                                    findPos = i;
                                }
                            }
                        }
                    } else {
                        //如果账号类型是其他，则根据uname进行替换
                        //统一替换成小写比较， 因为后端不区分大小写
                        if (!TextUtils.isEmpty(tempUserInfo.getUname()) && !TextUtils.isEmpty(uinfo.getUname())) {
                            if (tempUserInfo.getUname().toLowerCase().equals(uinfo.getUname().toLowerCase())) {
                                findPos = i;
                            }
                        }
                    }
                }
            }
            if (findPos != -1) {
                UserInfo oldUserInfo = accountList.get(findPos);
                LogUtil.i("用户缓存相关：系统的存储老的用户信息：" + oldUserInfo.toString());
                if (TextUtils.isEmpty(uinfo.getUpwd()) && !TextUtils.isEmpty(oldUserInfo.getUpwd())) {
                    //如果后登录的账号对应的密码为空但缓存中的密码不为空，则将密码设置为缓存的密码
                    uinfo.setUpwd(!TextUtils.isEmpty(oldUserInfo.getUpwd()) ? ZipString.json2ZipString(oldUserInfo.getUpwd()) : "");
                }
                accountList.remove(findPos);
            }
            LogUtil.i("用户缓存相关：系统的存储替换用户信息：" + uinfo.toString());
            accountList.add(uinfo);
        }

        try {
            Iterator<UserInfo> userInfos = accountList.iterator();
            while (userInfos.hasNext()) {
                UserInfo userInfo = userInfos.next();
                if (TextUtils.isEmpty(userInfo.getUname())) {
                    userInfos.remove();
                }
            }
            JSONArray jsonArray = new JSONArray();
            for (UserInfo userInfo : accountList) {
                jsonArray.put(UserInfo.encodeToJson(userInfo));
            }
            LogUtil.d("setAccountToFile jsonArray " + jsonArray);
            //打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
            FileWriter writer = new FileWriter(file.getAbsolutePath(), false);
            writer.write(ZipString.json2ZipString(jsonArray.toString()));    //压缩
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void delAccountFromFile(Context context, String account) {
        List<UserInfo> accountList = getAccountFromFile(context, false);
        File file = getAccountFile(context);

        if (accountList == null) {    //have no account
            return;
        }

        List<UserInfo> saveList = new ArrayList<UserInfo>();
        for (UserInfo ac : accountList) {
            if (null != ac.getUname() && null != account && !ac.getUname().equals(account)) {
                saveList.add(ac);
            }
        }

        try {

            JSONArray jsonArray = new JSONArray();
            for (UserInfo userInfo : saveList) {
                jsonArray.put(UserInfo.encodeToJson(userInfo));
            }
            //打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
            FileWriter writer = new FileWriter(file.getAbsolutePath(), false);
            writer.write(ZipString.json2ZipString(jsonArray.toString()));    //压缩
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取sd卡路径 返回路径带"／"
     *
     * @param context
     * @return
     */
    public static String getSDPath(Context context) {
        return EnvironmentUtils.getCommonDirPathEndWithSprit(context);

    }

    public static void cleanAccountInfoCache(Context context) {

        getAccountFile(context).delete();

    }

}
