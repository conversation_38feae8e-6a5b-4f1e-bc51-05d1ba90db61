package com.sy37sdk.account.auth;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.TestConst;
import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpanUtil;
import com.sqwan.common.util.task.Task;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.sy37sdk.account.face.FaceVerifyManager;
import com.sy37sdk.account.policy.PolicyManager;
import java.util.HashMap;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/12/9
 * <p>
 * 实名认证
 */
public class AuthManager {

    final String TAG = "AuthManager";
    private static AuthManager sInstance;
    private Context mContext;
    private AccountRequestManager requestManager;
    private int currentTimeTick = 0;
    private boolean isAuthDialogShow = false;
    private boolean isReporting = false;
    private AuthDialog authDialog = null;

    public static AuthManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (AuthManager.class) {
                if (sInstance == null) {
                    sInstance = new AuthManager(context);
                }
            }
        }
        return sInstance;
    }

    private AuthManager(Context context) {
        mContext = context;
        requestManager = new AccountRequestManager(context);
    }

    //登录之后隐藏强制实名窗口
    private void hideForceAuthDialog() {
        if (authDialog != null) {
            Task.post(new Runnable() {
                @Override
                public void run() {
                    authDialog.dismiss();
                }
            });
        }
    }

    public void requestAuthConfig(final SQResultListener authResultListener) {
        hideForceAuthDialog();
        AuthConfigCache.clearAuthConfig();
        AuthManager.getInstance(mContext).reset();
        requestManager.antiIndulge(AccountCache.getActionType(mContext), new SqHttpCallback<String>() {

            @Override
            public void onSuccess(String data) {
                AuthConfigCache.saveAuthConfig(data);
                long timeStamp = AuthConfigCache.getTimeStamp();
                if (timeStamp == 0) {
                    LogUtil.i("处理pcheck服务端请求服务超时");
                    PolicyManager.getInstance().handlePCheckGuarantee(mContext, authResultListener);
                    return;
                }
                if (AuthConfigCache.authBean != null && AuthConfigCache.authBean.isAgeLimited) {
                    //18禁弹窗
                    check18AgeLimit(AuthConfigCache.authBean);
                } else {
                    AuthManager.getInstance(mContext).checkAuth(authResultListener);
                }
            }

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                LogUtil.e("请求实名信息失败！！");
                PolicyManager.getInstance().handlePCheckGuarantee(mContext, authResultListener);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                LogUtil.e("请求实名信息失败！！");
                PolicyManager.getInstance().handlePCheckGuarantee(mContext, authResultListener);
            }
        });
    }

    public void checkAuth(final SQResultListener faceVerifyResultListener) {
        if (AuthConfigCache.getIsAuth()) {
            SQLog.d("已实名, 判断是否需要人脸识别");
            FaceVerifyManager.getInstance(mContext).handleFaceVerify(faceVerifyResultListener);
            PolicyManager.getInstance().handleTimeLimit(mContext, AuthConfigCache.getTimeStamp() * 1000, true);
            AntiManager.getInstance(mContext).report();
            SQLog.d("isBsAuth=" + AuthConfigCache.isBsAuth() + ", isAdult=" + AuthConfigCache.isAdult());
            if (AuthConfigCache.isBsAuth() && AuthConfigCache.isAdult()) {
                //版署实名过并且成年人则存储uid
                SQLog.i("版署实名过并且成年人, 存储uid");
                PolicyManager.saveAuthAdult(mContext);
            } else {
                SQLog.w("不满足缓存条件");
            }
        } else {
            if (AuthConfigCache.needAuth()) {
                showPersonalDialog(AuthConfigCache.getAuthUrl(), AuthConfigCache.isAuthFocus(), AuthConfigCache.needAccumulateDuration(), false, null, faceVerifyResultListener);
            }else{
                if (faceVerifyResultListener != null) {
                    faceVerifyResultListener.onSuccess(new Bundle());
                }
            }
        }

    }

    public void showAuthDialog(AuthCallback authCallback) {
        showPersonalDialog(AuthConfigCache.getAuthUrl(), AuthConfigCache.isAuthFocus(), AuthConfigCache.needAccumulateDuration(), false, authCallback);
    }

    private void startReport() {
        if (isReporting) {
            return;
        }
        LogUtil.i(TAG, "启动实名认证上报");
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_TICK);
        mContext.registerReceiver(broadcastReceiver, filter);
        isReporting = true;
    }

    public void stopReport() {
        LogUtil.i(TAG, "停止实名认证上报");
        if (isReporting) {
            mContext.unregisterReceiver(broadcastReceiver);
            isReporting = false;
        }
    }

    public void showPersonalDialog(String url, boolean focus, final boolean needAccumulateDuration, boolean handleByReportDevRsp, final AuthCallback authCallback, SQResultListener sqResultListener) {
        showAuthDialog(url, focus, handleByReportDevRsp, new AuthCallback() {
            @Override
            public void onSuccess() {
                LogUtil.i(TAG, "auth success");
                if (authCallback != null) {
                    authCallback.onSuccess();
                }
            }

            @Override
            public void onFailure() {
                if (authCallback != null) {
                    authCallback.onFailure();
                }
                if (needAccumulateDuration) {
                    startReport();
                }
            }

            @Override
            public void onShowDialog() {
                if (authCallback != null) {
                    authCallback.onShowDialog();
                }
            }
        }, sqResultListener);
    }

    public void showPersonalDialog(String url, boolean focus, final boolean needAccumulateDuration, boolean handleByReportDevRsp, final AuthCallback authCallback) {
        showAuthDialog(url, focus, handleByReportDevRsp, new AuthCallback() {
            @Override
            public void onSuccess() {
                LogUtil.i(TAG, "auth success");
                if (authCallback != null) {
                    authCallback.onSuccess();
                }
            }

            @Override
            public void onFailure() {
                if (authCallback != null) {
                    authCallback.onFailure();
                }
                if (needAccumulateDuration) {
                    startReport();
                }
            }

            @Override
            public void onShowDialog() {
                if (authCallback != null) {
                    authCallback.onShowDialog();
                }
            }
        });
    }

    public void showAuthDialog(final String url, final boolean focus, final boolean handleByReportDevRsp, final AuthCallback callback, final SQResultListener sqResultListener) {
        Task.post(new Runnable() {
            @Override
            public void run() {
                LogUtil.i("AuthManager showAuthDialog ,focus:" + focus + " isAuthDialogShow " + isAuthDialogShow);
                if (isAuthDialogShow && !(handleByReportDevRsp && focus)) {
                    return;
                }
                if (authDialog == null) {
                    authDialog = new AuthDialog(mContext);
                }
                authDialog.setFocus(focus);
                authDialog.setUrl(AppUtils.constructWebUrlParam(mContext, url));
                authDialog.setCloseListener(new AuthDialog.CloseListener() {
                    @Override
                    public void onClose(String tag, String data) {
                        isAuthDialogShow = false;
                        if (tag.equals("exitGame")) {
                            LogUtil.i("退出游戏");
                            ((Activity) mContext).finish();
                            System.exit(0);
                            return;
                        }
                        boolean isSuccess = tag.equals("0");
                        if (isSuccess) {
                            AuthCountDownManager.getInstance().setAuthResult(mContext, true, focus, authDialog);
                        }
                        if (callback != null) {
                            if (isSuccess) {
                                HashMap<String, String> map = new HashMap<>();
                                map.put(SqTrackKey.certification_url, url);
                                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.certification_succ, map);
                                AuthConfigCache.setIsAuth(true);
                                //实名成功之后就不上报了
                                stopReport();
                                callback.onSuccess();
                                requestAuthConfig(sqResultListener);
                            } else {
                                callback.onFailure();
                            }
                        }
                    }
                });
                authDialog.show();
                isAuthDialogShow = true;
                AuthCountDownManager.getInstance().setAuthResult(mContext, false, focus, authDialog);
                HashMap<String, String> map = new HashMap<>();
                map.put(SqTrackKey.certification_url, url);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.certification, map);
            }
        });

    }

    public void showAuthDialog(final String url, final boolean focus, final boolean handleByReportDevRsp, final AuthCallback callback) {
        Task.post(new Runnable() {
            @Override
            public void run() {
                LogUtil.i("AuthManager showAuthDialog ,focus:" + focus + " isAuthDialogShow " + isAuthDialogShow);
                if (isAuthDialogShow && !(handleByReportDevRsp && focus)) {
                    return;
                }
                if (authDialog == null) {
                    authDialog = new AuthDialog(mContext);
                }
                authDialog.setFocus(focus);
                authDialog.setUrl(AppUtils.constructWebUrlParam(mContext, url));
                authDialog.setCloseListener(new AuthDialog.CloseListener() {
                    @Override
                    public void onClose(String tag, String data) {
                        isAuthDialogShow = false;
                        if (tag.equals("exitGame")) {
                            LogUtil.i("退出游戏");
                            ((Activity) mContext).finish();
                            System.exit(0);
                            return;
                        }
                        boolean isSuccess = tag.equals("0");
                        if (isSuccess) {
                            AuthCountDownManager.getInstance().setAuthResult(mContext, true, focus, authDialog);
                        }
                        if (callback != null) {
                            if (isSuccess) {
                                HashMap<String, String> map = new HashMap<>();
                                map.put(SqTrackKey.certification_url, url);
                                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.certification_succ, map);
                                AuthConfigCache.setIsAuth(true);
                                //实名成功之后就不上报了
                                stopReport();
                                callback.onSuccess();
                                requestAuthConfig(null);
                            } else {
                                callback.onFailure();
                            }
                        }
                    }
                });
                authDialog.show();
                isAuthDialogShow = true;
                AuthCountDownManager.getInstance().setAuthResult(mContext, false, focus, authDialog);
                HashMap<String, String> map = new HashMap<>();
                map.put(SqTrackKey.certification_url, url);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.certification, map);
            }
        });

    }

    /**
     * 实名认证上报在线时长
     */
    private void reportAuth() {
        requestManager.reportDevDuration(new SqHttpCallback<JSONObject>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                LogUtil.e(TAG, "上报请求失败 msg:" + msg);
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                if (!isReporting) {
                    return;
                }
                LogUtil.i(TAG, "上报一小时实名认证：" + dataJson);
                try {
                    if (testForceStopReportDevDuration()) {
                        return;
                    }
                    PopConfig popConfig = PopConfig.parseFromJson(dataJson);
                    AuthConfigCache.autoCalAuthLimitTime();
                    if (popConfig.getNeedStop()) {
                        stopReport();
                    }
                    if (popConfig.getRemainingTime() == 0) {
                        AuthCountDownManager.getInstance().stopReportAuth();
                    }

                    if (!TextUtils.isEmpty(popConfig.getUrl()) && popConfig.isShow()) {
                        showPersonalDialog(popConfig.getUrl(), popConfig.isFocus(), false, true, null);
                    }
                } catch (JSONException ex) {
                    ex.printStackTrace();
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                LogUtil.e(TAG, "上报一小时实名认证 error:" + errorMsg);
            }
        });
    }

    public void reset() {
        stopReport();
        currentTimeTick = 0;
        isAuthDialogShow = false;

    }


    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (Intent.ACTION_TIME_TICK.equals(intent.getAction())) {
                if (!ModHelper.get(IAccountMod.class).hasSubmitRole()) {
                    LogUtil.e("not submitRole");
                    return;
                }
                if (!AuthCountDownManager.getInstance().canReport()) {
                    LogUtil.e("canReport false");
                    return;
                }
                if (currentTimeTick >= AuthConfigCache.getInterval() - 1) {
                    currentTimeTick = 0;
                    LogUtil.i("android time tick currentTimeTick");
                    reportAuth();
                } else {
                    currentTimeTick++;
                }
            }
        }
    };

    public static class AuthCallbackAdapter implements AuthCallback {

        @Override
        public void onSuccess() {

        }

        @Override
        public void onFailure() {

        }

        @Override
        public void onShowDialog() {

        }
    }

    public interface AuthCallback {

        void onSuccess();

        void onFailure();

        void onShowDialog();
    }


    private boolean testForceStopReportDevDuration() throws JSONException {
        if (TestConst.isForceStopReportDevDuration) {
            PopConfig popConfig = PopConfig.parseFromJson(new JSONObject(TestConst.forceDevStopData));
            if (popConfig.getNeedStop()) {
                stopReport();
            }
            if (!TextUtils.isEmpty(popConfig.getUrl()) && popConfig.isShow()) {
                showPersonalDialog(popConfig.getUrl(), popConfig.isFocus(), false, true, null);
            }
            return true;
        }
        return false;
    }

    public void check18AgeLimit(final AuthBean authBean) {
        if (authBean != null) {
            if (authBean.isAgeLimited) {
                Task.post(new Runnable() {
                    @Override
                    public void run() {
                        AntiManager.getInstance(mContext).stopReport();
                        AuthCountDownManager.getInstance().stopReportAuth();
                        int txtSize = DensityUtil.dip2px(mContext, 18);
                        new CommonAlertDialog.Builder(mContext)
                                .setTitle("温馨提示")
                                .setMessage(authBean.ageLimitedMsg)
                                .setPositiveButton(SpanUtil.getFontString("确认", txtSize, Color.parseColor("#FA9D05"), false), new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        ModHelper.get(IAccountMod.class).backToGameLogin();
                                    }
                                })
                                .setCancelable(false)
                                .showEx();
                    }
                });
            }
        }
    }
}
