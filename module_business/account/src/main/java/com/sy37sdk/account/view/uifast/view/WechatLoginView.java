package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.CheckClassUtils;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.alifast.AccountLoginManager;
import com.sy37sdk.account.alifast.FastBooleanResultListener;
import com.sy37sdk.account.alifast.FastLoginManager;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.ui.WechatRegSuccessDialog;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.presenter.IWechatPresenter;
import com.sy37sdk.account.view.uifast.presenter.WechatLoginPresenter;
import com.sy37sdk.account.view.uifast.switcher.AccountPage;

import java.util.Map;

public class WechatLoginView extends BaseSwitchView implements IWechatLoginView {

    private IWechatPresenter presenter;

    private TextView tv_more_login;
    private TextView tvClause, tvPolicy;
    private CheckBox cbClause;
    private View chatLoginView;
    private Context mContext;
    private ILoginListener loginListener;

    public WechatLoginView(Context context, ILoginDialog loginDialog, ILoginListener loginListener) {
        super(context);
        this.mContext = context;
        this.loginDialog = loginDialog;
        this.loginListener = loginListener;
        presenter = new WechatLoginPresenter(context, this);
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.wechat_login,SqTrackPage.SqTrackViewName.wechat_login);
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_wechat";
    }

    @Override
    public void initView() {
        tv_more_login = getViewByName("tv_other_login");
        tvClause = getViewByName("tv_clause");
        tvPolicy = getViewByName("tv_policy");
        cbClause = getViewByName("cb_clause");
        chatLoginView = getViewByName("rl_wechat");
    }

    @Override
    public void initEvent() {
        tv_more_login.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                // 使用FastLoginManager.getInstance前需要先判断是否有类，旧版本没有闪验代码
                if(isLoginAuthActivityExist()){
                    FastLoginManager.getInstance(mContext).getFastEnv(new FastBooleanResultListener() {
                        @Override
                        public void callback(boolean isFastEvn) {
                            if(!isFastEvn){
                                loginDialog.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE, null);
                            }else {
                                AccountUtil.setToFastLogin(true);
                                AccountUtil.setCanFastBack(true);
                                loginDialog.dismissAccountDialog();
                                AccountLoginManager.getInstance(mContext).login(loginListener);
                            }
                        }
                    });
                }else{
                    loginDialog.onSwitch(AccountPage.ACCOUNT_PHONE_PAGE, null);
                }
            }
        });
        tvClause.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toClausePage();
            }
        });
        tvPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toPolicy();
            }
        });
        cbClause.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                presenter.clauseClick(isChecked);
            }
        });
        chatLoginView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.wechatLogin();
            }
        });
    }

    @Override
    public String getTitle() {
        return "微信登录";
    }

    @Override
    public void enableLoginBtn(boolean enable) {
        chatLoginView.setEnabled(enable);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        loginDialog.loginSuccess(data);
    }

    @Override
    public void showRegDialog(Map<String, String> data, WechatRegSuccessDialog.IWechatRegListener regListener) {
        String username = AccountCache.getUsername(getContext());
        String password = AccountCache.getPassword(getContext());
        WechatRegSuccessDialog regSuccessDialog = new WechatRegSuccessDialog(getContext(), username, password);
        regSuccessDialog.setWechatRegListener(regListener);
        regSuccessDialog.show();
    }

    @Override
    public void checkedClause() {
        cbClause.setChecked(true);
    }

    private boolean isLoginAuthActivityExist() {
        return CheckClassUtils.classExist("com.mobile.auth.gatewayauth.LoginAuthActivity");
    }
}
