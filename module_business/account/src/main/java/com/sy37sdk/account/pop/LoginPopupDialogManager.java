package com.sy37sdk.account.pop;

import android.content.Context;
import com.sq.tool.logger.SQLog;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.dialog.pop.BasePopupDialogManager;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.account.AccountCache;

/**
 * 登录弹窗管理类
 */
public class LoginPopupDialogManager extends BasePopupDialogManager {

    private static LoginPopupDialogManager instance;


    private LoginPopupDialogManager() {

    }

    public static LoginPopupDialogManager getInstance() {
        if (instance == null) {
            synchronized (LoginPopupDialogManager.class) {
                if (instance == null) {
                    instance = new LoginPopupDialogManager();
                }
            }
        }
        return instance;
    }


    @Override
    public void requestPopup() {
        SQLog.d("请求登录后弹窗");
        Context context = SQContextWrapper.getActivity();
        String token = AccountCache.getToken(context);
        String actionType = AccountCache.getActionType(context);
        AccountPopupDialogHttpUtil.requestLoginPopup(token, actionType, new SimpleSqHttpCallback<String>() {

            @Override
            public void onSuccess(String data) {
                setPopupData(data);
                if (!needShowPopup()) {
                    SQLog.d("无登录后弹窗");
                } else {
                    SQLog.i("展示登录后弹窗");
                    showPopupDialog(null);
                }
            }
        });
    }

    @Override
    public String getDesc() {
        return "登录";
    }
}
