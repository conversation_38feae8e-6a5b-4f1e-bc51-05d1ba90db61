package com.sy37sdk.account.floatview.request;

import com.sq.websocket_engine.ReqWrapperHandler;
import com.sq.websocket_engine.parse.ResponseDataParse;
import com.sy37sdk.account.floatview.request.websocket.FloatReqService;
import com.sy37sdk.account.floatview.request.websocket.FloatWindowRedDotMsgPidGidReq;
import com.sy37sdk.account.floatview.request.websocket.FloatWindowRedDotMsgPlatformReq;

public class FloatRequestManager {

    private final FloatReqService floatReqService;

    public FloatRequestManager() {
        this.floatReqService = new FloatReqService();
    }

    public void requestFloatWindowRedDotMsgPlatform(final ReqWrapperHandler.FinishListener<Boolean> finishListener) {
        floatReqService.requestFloatWindowRedDotPlatformMsg(new FloatWindowRedDotMsgPlatformReq(), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
                @Override
                public void on(ResponseDataParse rsp) {
                    if (finishListener != null) {
                        finishListener.on(true);
                    }
                }
            })
            .onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
                @Override
                public void on(ResponseDataParse rsp) {
                    if (finishListener != null) {
                        finishListener.on(false);
                    }
                }
            });
    }

    public void requestFloatWindowRedDotMsgPidGid(final ReqWrapperHandler.FinishListener<Boolean> finishListener) {
        floatReqService.requestFloatWindowRedDotPidGidMsg(new FloatWindowRedDotMsgPidGidReq(), new ReqWrapperHandler.SuccessListener<ResponseDataParse>() {
                @Override
                public void on(ResponseDataParse rsp) {
                    if (finishListener != null) {
                        finishListener.on(true);
                    }
                }
            })
            .onFailed(new ReqWrapperHandler.FailedListener<ResponseDataParse>() {
                @Override
                public void on(ResponseDataParse rsp) {
                    if (finishListener != null) {
                        finishListener.on(false);
                    }
                }
            });
    }
}
