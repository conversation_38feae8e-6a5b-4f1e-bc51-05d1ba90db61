package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.LoginSkinHelper;
import com.sy37sdk.account.view.PopListHelper;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.presenter.AccountLoginPresenter;
import com.sy37sdk.account.view.uifast.presenter.IAccountLoginPresenter;

import com.sy37sdk.account.view.uifast.switcher.AccountPage;
import java.util.Map;
import org.json.JSONArray;

public class AccountLoginView extends BaseSwitchView implements IAccountLoginView {

    private EditText etAccount, etPwd;

    private View accountRow;

    private ImageView selectAccountView;

    private TextView tvLogin;

    private TextView tvReg;

    private TextView tvForgetPwd;

    private ImageView ivPwdToggle;

    private View functionView, privacyView;

    private CheckBox cbClause;

    private boolean isShowPwd = false;

    private PopListHelper popListHelper;

    private IAccountLoginPresenter presenter;

    private TextView tvClause, tvPolicy;

    private View contentLayout;

    private int mType;

    public static final int LOGIN_TYPE = 0;

    public static final int REGISTER_TYPE = 1;

    //登录的账号密码
    private String uname, upwd;

    //是否填充过登录的账号密码了
    private boolean isFillLoginAccount;

    //注册的账号密码
    private String uRegName, uRegPwd;

    //是否填充过注册的账号密码了
    private boolean isFillRegAccount;

    public AccountLoginView(Context context, ILoginDialog loginDialog) {
        super(context);
        this.loginDialog = loginDialog;
        presenter = new AccountLoginPresenter(context, this);
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_account";
    }


    @Override
    public void initView() {
        contentLayout = getViewByName("content_layout");
        contentLayout.setBackgroundResource(LoginSkinHelper.getAccountLoginBackgroundResId(getContext()));
        accountRow = getViewByName("ll_account_row");
        etAccount = getViewByName("et_account");
        etPwd = getViewByName("et_pwd");
        ivPwdToggle = getViewByName("iv_password_hide");
        selectAccountView = getViewByName("iv_select_account");
        tvLogin = getViewByName("tv_login");
        tvLogin.setBackgroundResource(LoginSkinHelper.getLoginBtnBackgroundResId(getContext()));
        tvLogin.setTextColor(LoginSkinHelper.getLoginBtnTextColor(getContext()));
        tvReg = getViewByName("tv_register");
        tvReg.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        tvForgetPwd = getViewByName("tv_forgot_pwd");
        tvForgetPwd.setTextColor(LoginSkinHelper.getPrimaryTextColor(getContext()));
        functionView = getViewByName("function_view");
        privacyView = getViewByName("ll_privacy");
        cbClause = getViewByName("cb_clause");
        tvClause = getViewByName("tv_clause");
        tvPolicy = getViewByName("tv_policy");
    }


    @Override
    public void initEvent() {
        ivPwdToggle.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                togglePassword();
            }
        });

        selectAccountView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (popListHelper == null || popListHelper.getAccountList() == null || popListHelper.getAccountList().size() < 1) {
                    return;
                }
                if (!popListHelper.isShowing()) {
                    selectAccountView.setImageResource(getIdByName("sysq_ic_account_list_up", "drawable"));
                    popListHelper.showAccountList(accountRow);
                } else {
                    selectAccountView.setImageResource(getIdByName("sysq_ic_account_list_down", "drawable"));
                    popListHelper.hideAccountList();
                }
            }
        });
        tvLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isQuickClick()) {
                    return;
                }
                if (mType == LOGIN_TYPE) {
                    presenter.login(etAccount.getText().toString(), etPwd.getText().toString());
                } else {
                    presenter.accountRegister(etAccount.getText().toString(), etPwd.getText().toString());
                }
            }
        });
        tvReg.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.accountRegister, SqTrackBtn.SqTrackBtnExt.accountRegister);
                toggleUI(REGISTER_TYPE);
            }
        });

        tvForgetPwd.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.forgotPassword();
            }
        });

        cbClause.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                presenter.clauseClick(isChecked);
            }
        });
        tvClause.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toClausePage();
            }
        });
        tvPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toPolicy();
            }
        });
    }

    @Override
    protected void onBack() {
        if (mType == REGISTER_TYPE) {
            toggleUI(LOGIN_TYPE);
        } else {
            super.onBack();
        }
    }

    @Override
    public String getTitle() {
        return "账号登录";
    }

    private void initAccountList() {
        popListHelper = new PopListHelper();
        popListHelper.initAccountList(getContext(), AccountUtil.getUserInfoFilterByAccount(getContext()), accountRow.getWidth(), 42, new PopListHelper.ItemClickListener() {
            @Override
            public void onSelect(UserInfo userInfo) {
                if (!TextUtils.isEmpty(userInfo.getAlias())) {
                    etAccount.setText(userInfo.getAlias());
                } else {
                    etAccount.setText(userInfo.getUname());
                }
                etPwd.setText(userInfo.getUpwd());
            }

            @Override
            public void onDelete(UserInfo userInfo) {
                presenter.deleteUser(userInfo);
                String alias = userInfo.getAlias();
                String uname = userInfo.getUname();
                String inputName = etAccount.getText().toString();
                if ((!TextUtils.isEmpty(alias) && alias.contentEquals(inputName)) || (!TextUtils.isEmpty(uname) && uname.contentEquals(inputName))) {
                    etAccount.setText("");
                    etPwd.setText("");
                }
            }
        }, new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (popListHelper != null) {
                    selectAccountView.setImageResource(SqResUtils.getDrawableId(getContext(), "sysq_ic_account_list_down"));
                }
            }
        });
    }

    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        if (fromIndex != AccountPage.ACCOUNT_MULTI_SELECT) {
            isFillLoginAccount = false;
            isFillRegAccount = false;
            post(new Runnable() {
                @Override
                public void run() {
                    initAccountList();
                }
            });
            presenter.initData();
        }
    }


    @Override
    public void enableLoginBtn(boolean enable) {
        tvLogin.setEnabled(enable);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        loginDialog.loginSuccess(data);
    }

    @Override
    public void setAutoAccount(String name, String pwd) {
        etAccount.setText(name);
        etPwd.setText(pwd);
    }

    @Override
    public void accountRegSuccess(Map<String, String> data) {
        loginDialog.accountRegSuccess(data);

    }

    private void togglePassword() {
        String pwd = etPwd.getText().toString();
        if (isShowPwd) {
            etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
            ivPwdToggle.setImageResource(getIdByName("sysq_ic_pwd_hide", "drawable"));
        } else {
            etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            ivPwdToggle.setImageResource(getIdByName("sysq_ic_pwd_show", "drawable"));
        }
        isShowPwd = !isShowPwd;
        etPwd.setText(pwd);
    }

    @Override
    public void toggleUI(int type) {
        mType = type;
        String name = etAccount.getText().toString();
        String pwd = etPwd.getText().toString();
        privacyView.setVisibility(VISIBLE);
        if (mType == LOGIN_TYPE) {
            uRegName = name;
            uRegPwd = pwd;
            selectAccountView.setVisibility(VISIBLE);
            functionView.setVisibility(VISIBLE);
            tvLogin.setText(SqResUtils.getStringByName(getContext(), "sysq_login"));
            etAccount.setPadding(DisplayUtil.dip2px(getContext(), 12), 0, 0, 0);
            if (isFillLoginAccount) {
                setAutoAccount(uname, upwd);
            } else {
                presenter.autoAccount();
                isFillLoginAccount = true;
            }
            setTitleView("账号登录");
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.account_login, SqTrackPage.SqTrackViewName.ACCOUNT_LOGIN);
        } else if (mType == REGISTER_TYPE) {
            uname = name;
            upwd = pwd;
            selectAccountView.setVisibility(GONE);
            functionView.setVisibility(GONE);
            tvLogin.setText(SqResUtils.getStringByName(getContext(), "sysq_register"));
            etAccount.setPadding(DisplayUtil.dip2px(getContext(), 12), 0, DisplayUtil.dip2px(getContext(), 12), 0);
            if (isFillRegAccount) {
                setAutoAccount(uRegName, uRegPwd);
            } else {
                isFillRegAccount = true;
                presenter.autoRegister(false);
            }
            setTitleView("账号注册");
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.account_register, SqTrackPage.SqTrackViewName.ACCOUNT_REGISTER);
        }
    }

    @Override
    public void regEntrance(boolean entrance) {
        tvReg.setVisibility(entrance ? VISIBLE : GONE);
    }

    @Override
    public void checkedClause() {
        cbClause.setChecked(true);
    }

    @Override
    public void selectMultiAccount(JSONArray accountList, String uname, String pwd) {
        Bundle bundle = new Bundle();
        bundle.putString("account_list", accountList.toString());
        bundle.putString("login_uname", uname);
        bundle.putString("login_pwd", pwd);
        loginDialog.onSwitch(AccountPage.ACCOUNT_MULTI_SELECT, bundle);
    }


    @Override
    protected void closeLoginDialog() {
        SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.closeRegisterPage, SqTrackBtn.SqTrackBtnExt.closeRegisterPage);
    }
}
