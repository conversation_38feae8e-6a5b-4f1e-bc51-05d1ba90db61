package com.sy37sdk.account;

import android.text.TextUtils;
import com.sqwan.base.L;
import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/25
 */
public class UrlConstant {

    private static String APP_HOST_Prefix_p = "http://message-api.";

    public static final String KEY_M_SDK_PERMISSION = "sdk_permission";
    public static final String KEY_GAME_URL_LIST = "game_url_list";
    public static final String KEY_M_USER_PROTOCOL = "m_user_protocol";
    public static final String POP_UPS_LOGIN_API = "pop_ups_login_api";
    public static final String KEY_S_LOGIN = "login";
    public static final String KEY_S_REG = "reg";
    public static final String KEY_S_MSCODE = "mscode";
    public static final String KEY_S_MREG = "mreg";
    public static final String KEY_S_MREG_RES = "mreg_res";
    public static final String KEY_S_UAGREE = "uagree";
    public static final String KEY_S_REST_PWD = "resetPwd";
    public static final String KEY_S_AUTO_ASSIGN = "auto_assign";
    public static final String KEY_S_REPORT_DEV_DURATION = "report_dev";
    public static final String KEY_S_REPORT_USER_DURATION = "report_user";
    public static final String KEY_S_REPORT_PCHECK = "pcheck";
    public static final String KEY_S_APPEAL_PAGE = "phone_retrieval";
    public static final String KEY_S_OTHER_LOGIN = "other_login";
    public static final String KEY_S_USER_PROTOCOL = "s_user_protocol";
    public static final String KEY_S_FLOAT_WINDOW_NEW = "s_float_window_new";
    public static final String KEY_S_SHOW_RED_FLOAT_WINDOW = "s_show_red_float_window";
    public static final String KEY_S_FETCH_INFO = "u_fetch_u_info";
    public static final String KEY_S_AGE_APPROPRIATE_COF = "right_age";
    public static final String KEY_S_REPORT_ONLINE = "report_online";
    public static final String KEY_S_CFG_SHAN_YAN = "cfg_shan_yan";
    public static final String KEY_S_SHAN_YAN_LOGIN = "mobile_shan_yan_login";
    public static final String KEY_S_LOGIN_SCODE = "mobile_login_scode";
    public static final String KEY_S_SEND_SCODE = "mobile_send_scode";
    public static final String KEY_S_CHECK_SCODE = "sdk_mobile_check_code";
    public static final String KEY_S_LOGIN_TICKET = "sdk_mobile_login_ticket";
    public static final String KEY_S_LOGIN_PWD = "mobile_login_pwd";
    public static final String KEY_S_QUICK_LOGIN = "quick_login";
    public static final String KEY_S_ENTRANCE = "entrance";
    public static final String KEY_S_REPORT_LOGIN_FAIL = "sdk_login_fail";
    public static final String KEY_S_GET_USER_INFO = "get_pt_user_info";
    public static final String KEY_S_GET_RED_POINT = "get_red_point";
    public static final String KEY_S_GET_FLOAT_WARNING = "get_float_warning";
    public static final String KEY_S_UPDATE_RED_POINT = "update_red_point";
    public static final String KEY_S_GET_AVATAR_LIST = "get_pt_avatar_list";
    public static final String KEY_S_UPDATE_USER_INFO = "update_pt_user_info";
    public static final String KEY_S_VALIDATE_INIT_FACE_VERIFY = "validate_init_face_verify";
    public static final String KEY_S_VALIDATE_CHECK_FACE_VERIFY = "validate_check_face_verify";
    public static final String KEY_S_VALIDATE_DESCRIBE_FACE_VERIFY = "validate_describe_face_verify";
    public static final String KEY_PLATFORM_BOARD = "announcement";
    public static final String KEY_WECHAT_LOGIN = "x_auth_wechat_oplatform";

    /**
     * 登录弹窗
     */
    @UrlUpdate(value = POP_UPS_LOGIN_API, xValue = "x_popups_login")
    public static String URL_M_LOGIN_POPUP = "http://m-api" + MultiSdkManager.SECURE_SUFFIX  + MultiSdkManager.APP_HOST + "/go/sdk/popups/login";

    /**
     * 用户协议 (激活前)
     */
    @UrlUpdate(value = KEY_M_USER_PROTOCOL, xValue = "x_user_protocol")
    public static String USER_PROTOCOL_ACTIVE_BEFORE = "http://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/cfg/user_protocol";

    /**
     * 激活前获取权限开关
     */
    @UrlUpdate(KEY_M_SDK_PERMISSION)
    public static String ACTIVE_BEFORE_PERMISSION = "http://m-api." + MultiSdkManager.APP_HOST + "/go/cfg/sdk_permission";

    /**
     * 获取游戏域名列表：http://yapi.39on.com/project/75/interface/api/32468
     */
    @UrlUpdate(KEY_GAME_URL_LIST)
    public static String GET_GAME_URL_LIST = "http://s-api." + MultiSdkManager.APP_HOST + "/api/sapi-service/v1/gamedomainnamefiling/game_url_list";

    /**
     * 登录接口
     */
    @UrlUpdate(value = KEY_S_LOGIN, xValue = "x_login")
    public static String LOGIN = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/login/";

    /**
     * 检查可用账户列表
     */
    @UrlUpdate(value = "x_sdk_check_account_list", xValue = "x_sdk_check_account_list")
    public static String CHECK_ACCOUNT_LIST = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/check_account_list";

    /**
     * 注册接口
     */
    @UrlUpdate(value = KEY_S_REG, xValue = "x_register")
    public static String REG = "http://s-api" + MultiSdkManager.SECURE_SUFFIX  + MultiSdkManager.APP_HOST + "/sdk/reg/";

    /**
     * 短信注册结果查询
     */
    @UrlUpdate(KEY_S_MREG_RES)
    public static String MREG_RES = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg_res/";

    /**
     * 自动获取注册账号密码
     */
    @UrlUpdate(value = KEY_S_AUTO_ASSIGN, xValue = "x_auto_assign")
    public static String AUTO_SET_ACCOUNT = "http://s-api" + MultiSdkManager.SECURE_SUFFIX  + MultiSdkManager.APP_HOST + "/sdk/autoassign";

    /**
     * 手机注册验证码接口
     */
    @UrlUpdate(KEY_S_MSCODE)
    public static String MSCODE = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/scode/";

    /**
     * 手机注册接口
     */
    @UrlUpdate(KEY_S_MREG)
    public static String MREG = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg/";

    /**
     * 用户协议接口
     */
    @UrlUpdate(KEY_S_UAGREE)
    public static String USER_AGREE = "http://" + MultiSdkManager.APP_HOST + "/sdk-wrap/iframe.html?sversion=3.1.0&isrc=http://" + MultiSdkManager.APP_HOST + "/uagree/37.html";

    /**
     * 实名认证，上报在线时长接口
     */
    @UrlUpdate(KEY_S_REPORT_DEV_DURATION)
    public static String URL_REPORT_DEV_DURATION = "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportDevDuration";

    /**
     * sdk 防沉迷上报用户在线时长接口
     */
    @UrlUpdate(value = KEY_S_REPORT_USER_DURATION, xValue = "x_report_user_duration")
    public static String URL_REPORT_USER_DURATION = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/reportUserDuration";

    /**
     * 实名/防沉迷接口
     */
    @UrlUpdate(value = KEY_S_REPORT_PCHECK, xValue = "x_pcheck")
    public static String CHECK_ANTI_AUTHENT = "https://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/antiindulge/pcheck/";

    /**
     * 忘记密码页面
     */
    @UrlUpdate(value = KEY_S_REST_PWD, xValue = "x_forget_password")
    public static String FORGET_PWD = "https://" + "user.37.com.cn" + "/sdkv1/service/retrieval/psw/index";

    /**
     * 账号申诉页面
     */
    @UrlUpdate(KEY_S_APPEAL_PAGE)
    public static String APPEAL_PAGE = "http://" + MultiSdkManager.APP_HOST + "/service-system/accountappeal/phoneRetrieval";

    /**
     * 第三方登录
     */
    @UrlUpdate(KEY_S_OTHER_LOGIN)
    public static String OTHER_LOGIN = "http://s-api." + MultiSdkManager.APP_HOST + "/oauth/login/";

    /**
     * 用户协议
     */
    @UrlUpdate(value = KEY_S_USER_PROTOCOL)
    public static String USER_PROTOCOL = "http://s-api." + MultiSdkManager.APP_HOST + "/go/cfg/user_protocol";

    /**
     * 悬浮球配置, 现已替换成 v2 版本， 见 {@link #FLOAT_WINDOW_V2}
     */
    @Deprecated
    public static String FLOAT_WINDOW = "http://s-api." + MultiSdkManager.APP_HOST + "/go/cfg/float_window";

    /**
     * v2 版本 悬浮球配置，详细见文档: http://yapi.39on.com/project/75/interface/api/11072
     */
    @UrlUpdate(value = KEY_S_FLOAT_WINDOW_NEW, xValue = "x_float_config")
    public static String FLOAT_WINDOW_V2 = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/cfg/v2/float_window";

    /**
     * 悬浮窗配置
     */
    @UrlUpdate(KEY_S_SHOW_RED_FLOAT_WINDOW)
    public static String REDPACKET_FLOATVIEW_SWITCH = "http://s-api." + MultiSdkManager.APP_HOST + "/go/cfg/show_red_float_window";

    /**
     * 微信授权，换取openID
     */
    @UrlUpdate(KEY_S_FETCH_INFO)
    public static String WX_AUTH = "http://us-api." + MultiSdkManager.APP_HOST + "/oauth/gw/fetchUinfo?";

    /**
     * 微信登录
     */
    @UrlUpdate(value = KEY_WECHAT_LOGIN,xValue = "x_s_auth_wx_oplatform")
    public static String WECHAT_LOGIN = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/third_auth/wechat/oplatform";

    /**
     * 适龄提醒
     */
    @UrlUpdate(value = KEY_S_AGE_APPROPRIATE_COF, xValue = "x_right_age")
    public static String APPROPRIATE_AGE_PROTOCOL = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/cfg/age_appropriate_conf";

    /**
     * 实名认证，上报在线时长接口
     */
    @UrlUpdate(value = KEY_S_REPORT_ONLINE, xValue = "x_report_online")
    public static String URL_REPORT_DEV_ONLINE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/reportOnline";

    /**
     * 请求闪验配置接口
     */
    @UrlUpdate(value = KEY_S_CFG_SHAN_YAN,xValue = "x_config_flash_verify_url")
    public static String URL_REQUEST_FAST_CONFIG = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/cfg/shan_yan";

    /**
     * 校验闪验token  /go/sdk/mobile/shan_yan_login
     *
     * @param data
     */
    @UrlUpdate(value = KEY_S_SHAN_YAN_LOGIN, xValue = "x_doflash_verify_url")
    public static String URL_VERIFY_FAST_TOKEN = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/shan_yan_login";

    //手机+验证码登陆  被ticket登录代替，不再使用
    @UrlUpdate(value = KEY_S_LOGIN_SCODE, xValue = "x_phone_logincode")
    public static String URL_LOGIN_PHONE_CODE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/login_scode";

    //获取手机验证码
    @UrlUpdate(value = KEY_S_SEND_SCODE, xValue = "x_phone_code")
    public static String URL_LOGIN_SEND_CODE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/send_scode";

    //验证手机生成Ticket
    @UrlUpdate(value = KEY_S_CHECK_SCODE, xValue = "x_sdk_mobile_check_code")
    public static String URL_LOGIN_CHECK_CODE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/check_code";

    //Ticket登录
    @UrlUpdate(value = KEY_S_LOGIN_TICKET, xValue = "x_sdk_mobile_login_ticket")
    public static String URL_LOGIN_TICKET = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/login_ticket";

    //手机+密码登录
    @UrlUpdate(value = KEY_S_LOGIN_PWD, xValue = "x_phone_login_pwd")
    public static String URL_LOGIN_PHONE_PWD = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/mobile/login_pwd";

    //快速登录
    @UrlUpdate(value = KEY_S_QUICK_LOGIN, xValue = "x_quick_login")
    public static String URL_LOGIN_FAST = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/quick_login";

    //注册入口
    @UrlUpdate(value = KEY_S_ENTRANCE, xValue = "x_entrance")
    public static String URL_REG_ENTRANCE = "http://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/cfg/entrance";

    //登陆异常上报
    @UrlUpdate(value = KEY_S_REPORT_LOGIN_FAIL, xValue = "x_sdk_login_fail")
    public static String URL_LOGIN_REPORT = "http://m-api."  + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/report/login_fail";


    /**
     * 上报易盾Token
     */
    public static String URL_M_REPORT_RISK_TOKEN = "http://m-api." + MultiSdkManager.APP_HOST + "/go/sdk/report/risk";


    /**
     * 获取平台用户信息
     */
    @UrlUpdate(value = KEY_S_GET_USER_INFO, xValue = "x_pt_user_info")
    public static String URL_GET_PT_USER_INFO = "https://us-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/userinfo/getPtUserInfo";

    /**
     * 获取悬浮球红点
     */
    @UrlUpdate(value = KEY_S_GET_RED_POINT, xValue = "x_red_point")
    public static String URL_GET_RED_POINT = "https://message-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/api/message-state-api/v1/red_dot/quantity";


    /**
     * 获取悬浮球提醒方式
     */
    @UrlUpdate(value = KEY_S_GET_FLOAT_WARNING, xValue = "x_get_float_warning")
    public static String URL_GET_FLOAT_WARNING = "https://message-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/api/message-state-api/v1/red_dot/sdk/get_warning";

    /**
     * 悬浮球红点已读
     */
    @UrlUpdate(value = KEY_S_UPDATE_RED_POINT, xValue = "x_red_point_watch")
    public static String URL_GET_RED_CALLED = "https://message-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/api/message-state-api/v1/red_dot/watched";

    /**
     * 获取头像列表
     */
    @UrlUpdate(value = KEY_S_GET_AVATAR_LIST, xValue = "x_pt_avatar_list")
    public static String URL_GET_AVATAR_LIST = "https://us-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/userinfo/getAvatarList";

    /**
     * 设置个人信息
     *
     * @param data
     */
    @UrlUpdate(value = KEY_S_UPDATE_USER_INFO, xValue = "x_update_pt_user_info")
    public static String URL_MODIFY_PERSON_INFO = "https://us-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/userinfo/setPtUserInfo";

    /**
     * 请求人脸验证的id
     */
    @UrlUpdate(value = KEY_S_VALIDATE_INIT_FACE_VERIFY, xValue = "x_face_init")
    public static String URL_ALI_CERTIFY_FACE_ID = "https://validate-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/face/init-verify";

    //是否需要人脸认证
    @UrlUpdate(value = KEY_S_VALIDATE_CHECK_FACE_VERIFY, xValue = "x_face_if_need")
    public static String URL_NEED_ALI_CERTIFY_FACE = "https://validate-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/face/check-verify-need";

    //验证是否人脸认证成功
    @UrlUpdate(value = KEY_S_VALIDATE_DESCRIBE_FACE_VERIFY, xValue = "x_face_verify_res")
    public static String URL_CHECK_VALIDATE_VERIFY = "https://validate-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/face/get-verify-res";

    @UrlUpdate(value = KEY_PLATFORM_BOARD, xValue = "x_announcement")
    public static String PLATFORM_FAULT_URL = "https://sos-service" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/api/sos-service/v1/platform_board/get";

    //人脸认证帮助页面
    public static String FACE_VERIFY_HELP_PAGE = "https://user." + (ConfigManager.getInstance(L.getActivity()).isSplashSDK() ? "39ej7e.com" : MultiSdkManager.APP_HOST) + "/sdkv1/user-system/face-help";

    //人脸认证客服页面
    public static String FACE_VERIFY_CUSTOMER_PAGE = "https://user." + (ConfigManager.getInstance(L.getActivity()).isSplashSDK() ? "39ej7e.com" : MultiSdkManager.APP_HOST) + "/sdkv1/service/home";

    @UrlUpdate(value = "x_sdk_kf_guide_url", xValue = "x_sdk_kf_guide_url")
    public static String KEFU_GUIDE_URL = "";
    @UrlUpdate(value = "x_sdk_kf_icon_url", xValue = "x_sdk_kf_icon_url")
    public static String KEFU_ICON_URL = "";

    //接口动态下发接口（统一网关）
    public static String UPDATE_URL = "https://sdk-apix-secure." + MultiSdkManager.APP_HOST + "/server-info-service/get-url";
    //接口动态下发接口（非统一网关）
//    public static String UPDATE_URL = "https://sdk-api." + MultiSdkManager.APP_HOST + "/server-info-service/get-url";

    //扫码授权登录-通知
    @UrlUpdate(value = "x_sdk_qrcode_login_scan", xValue = "x_sdk_qrcode_login_scan")
    public static String URL_QRCODE_SCAN =  "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/account/qrcode/scan";

    //扫码授权登录-确认授权
    @UrlUpdate(value = "x_sdk_qrcode_login_confirm", xValue = "x_sdk_qrcode_login_confirm")
    public static String URL_QRCODE_CONFIRM_AUTH =  "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/account/qrcode/confirm";

    //扫码授权登录-取消
    @UrlUpdate(value = "x_sdk_qrcode_login_cancel", xValue = "x_sdk_qrcode_login_cancel")
    public static String URL_QRCODE_CANCEL_AUTH=  "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/account/qrcode/cancel";

    //架构组下发的客户端清单配置接口
    public static String URL_GET_CONFIG_INFO=  "http://app-liefer.37.com.cn"  + "/app/client/config/get";

    public static void refreshFaceUrls(String data) {
        if (TextUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(data);
            String face_verify_customer = jsonObject.optString("face_verify_customer");
            if (!TextUtils.isEmpty(face_verify_customer)) {
                FACE_VERIFY_CUSTOMER_PAGE = face_verify_customer;
            }
            String face_verify_help = jsonObject.optString("face_verify_help");
            if (!TextUtils.isEmpty(face_verify_help)) {
                FACE_VERIFY_HELP_PAGE = face_verify_help;
            }
        } catch (JSONException e) {
            LogUtil.e("解析页面链接失败！");
            e.printStackTrace();
        }
    }


}
