package com.sy37sdk.account.floatview;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ToastUtil;
import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;
import com.sy37sdk.account.floatview.ui.AvatarAdapter;
import com.sy37sdk.account.floatview.ui.SpacesItemDecoration;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.view.base.view.ClearEditText;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PersonModifyDialog extends BaseDialog {

    private View modifyLayout;
    private ClearEditText etNick;
    private TextView tvCancel, tvSure;
    private RecyclerView rvAvatar;
    private AvatarAdapter avatarAdapter;
    private Context mContext;
    String currentAvatar = "";

    public PersonModifyDialog(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_dialog_person_modify"));
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.modifyUserInfo, SqTrackPage.SqTrackViewName.modifyUserInfo);
        initView();
        initEvent();
        initData();
    }

    private void initView() {
        etNick = findViewById(getIdByName("et_nick", "id"));
        //限制只能输入中英文、数字、长度8位
        etNick.setFilters(new InputFilter[]{typeFilter, new InputFilter.LengthFilter(8)});
        tvCancel = findViewById(getIdByName("tv_cancel", "id"));
        tvSure = findViewById(getIdByName("tv_sure", "id"));
        modifyLayout = findViewById(getIdByName("modify_layout", "id"));
        rvAvatar = findViewById(getIdByName("rv_avatar", "id"));
        int spaceDp = (310 - 20 * 2 - 38 * 5) / 4;
        int space = DisplayUtil.dip2px(mContext, spaceDp);
        rvAvatar.addItemDecoration(new SpacesItemDecoration(space));
        avatarAdapter = new AvatarAdapter(mContext);
        final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvAvatar.setLayoutManager(linearLayoutManager);
        rvAvatar.setAdapter(avatarAdapter);
    }

    private void initEvent() {
        etNick.setClearDrawableRes("sysq_ic_delete");
        modifyLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                StatusBarUtil.hideSystemKeyBoard(mContext, modifyLayout);
            }
        });
        tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                modifyPersonInfo();
            }
        });
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                StatusBarUtil.hideSystemKeyBoard(mContext, modifyLayout);
                dismiss();
            }
        });
        avatarAdapter.setItemClickListener(new AvatarAdapter.ItemClickListener() {
            @Override
            public void ItemOnClick(int position) {
                List<String> datas = avatarAdapter.getDatas();
                if (datas != null && position < datas.size()) {
                    currentAvatar = datas.get(position);
                }
            }
        });
    }

    private void initData() {
        ArrayList<String> avatars = FloatViewDataManager.getInstance().getAvatars();
        FloatUserInfo ptUserInfo = FloatViewDataManager.getInstance().getPtUserInfo();
        if (ptUserInfo != null && !TextUtils.isEmpty(ptUserInfo.getAvatar())) {
            currentAvatar = ptUserInfo.getAvatar();
        }
        avatarAdapter.setDataList(avatars, currentAvatar);

    }

    //限制只能输入中文，英文，数字
    InputFilter typeFilter = new InputFilter() {
        @Override
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
            Pattern p = Pattern.compile("[0-9a-zA-Z|\u4e00-\u9fa5]+");
            Matcher m = p.matcher(source.toString());
            if (!m.matches()) return "";
            return null;
        }
    };

    private void modifyPersonInfo() {
        final String nickName = etNick.getText().toString();
        FloatViewDataManager.getInstance().modifyPersonInfo(nickName, currentAvatar, new SqHttpCallback<Void>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                ToastUtil.showToast(mContext, msg);
            }

            @Override
            public void onSuccess(Void data) {
                FloatViewDataManager.getInstance().modifyPtUserInfo(nickName, currentAvatar);
                if (modifyPersonInfoListener != null) {
                    modifyPersonInfoListener.modify();
                }
                ToastUtil.showToast(mContext, "修改成功");
                StatusBarUtil.hideSystemKeyBoard(mContext, modifyLayout);
                dismiss();
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                ToastUtil.showToast(mContext, "修改失败");
            }
        });
    }

    public interface ModifyPersonInfoListener {
        void modify();
    }

    public ModifyPersonInfoListener modifyPersonInfoListener;

    public void setModifyPersonInfoListener(ModifyPersonInfoListener modifyPersonInfoListener) {
        this.modifyPersonInfoListener = modifyPersonInfoListener;
    }
}
