package com.sy37sdk.account.policy;

import android.content.Context;
import com.sq.tool.logger.SQLog;
import com.sq.tools.Logger;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.account.IAuthResultListener;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.task.Task;
import com.sqwan.common.webview.SQWebViewDialog;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.auth.AntiManager;
import com.sy37sdk.account.auth.AuthConfigCache;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.sy37sdk.account.face.FaceVerifyManager;
import com.sy37sdk.account.policy.view.GuaranteeDialog;
import java.util.ArrayList;
import org.json.JSONArray;

public class PolicyManager {

    private static final String SP_NAME = "adult_uids";
    private static final String SP_KEY = "adult_uids_key";
    private static final PolicyManager instance = new PolicyManager();

    public static PolicyManager getInstance() {
        return instance;
    }

    private PolicyManager() {
    }


    /**
     * 弹政策弹窗
     *
     * @param context
     * @param url
     * @param focus
     */
    public void showPolicyDialog(final Context context, String url, boolean focus) {
        LogUtil.i("显示弹窗，focus: " + focus + ", url:" + url);
        //如果code为2，则处理强制踢下线
        if (focus) {
            LogUtil.i("处理弹窗并踢下线");
            final IAuthResultListener authResultListener = ModHelper.get(IAccountMod.class).getAuthResultListener();
            if (authResultListener != null) {
                Task.post(new Runnable() {
                    @Override
                    public void run() {
                        ModHelper.get(IAccountMod.class).setSubmitRole(false);
                        AntiManager.getInstance(context).reset();
                        AuthCountDownManager.getInstance().stopReportAuth();
                        authResultListener.onAuthResult(false);
                        LogUtil.i("踢下线 onAuthResult false");
                    }
                });

            }
        }
        SQWebViewDialog dialog = new SQWebViewDialog(context);
        dialog.setUrl(AppUtils.constructWebUrlParam(context, url));
        // 政策类的弹窗不能被取消
        dialog.setCancelable(false);
        dialog.show();
    }

    /**
     * 处理年龄段限制弹窗
     */
    public void handleTimeLimit(final Context context, long timeStamp, boolean isFocus) {
        int age = AuthConfigCache.getAge();
        //未成年
        if (age != -1 && 18 - age > 0) {
            //当前可玩并且在可玩时间段
            if (!AuthConfigCache.isPlayDate() || !AuthConfigCache.isPlayTimeRange(timeStamp)) {
                SQLog.w("不在时间段内,弹时间段限制弹窗,并踢下线");
                showPolicyDialog(context, AuthConfigCache.getTimeRangeLimitUrl(), isFocus);
            }
        }
    }

    /**
     * 存储成年人的uid
     */
    public static void saveAuthAdult(Context context) {
        String uid = AccountCache.getUserid(context);
        ArrayList<String> authAdults = getAuthAdult(context);
        if (authAdults.contains(uid)) {
            return;
        }
        JSONArray jsonArray = new JSONArray();
        try {
            authAdults.add(uid);
            for (String adultUid : authAdults) {
                jsonArray.put(adultUid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.i("缓存实名的uid出错");
        }
        LogUtil.i("缓存实名的uid " + jsonArray.toString());
        SpUtils.get(context, SP_NAME).put(SP_KEY, jsonArray.toString());
    }

    /**
     * 获取成年人uid缓存信息
     *
     * @param context
     * @return
     */
    public static ArrayList<String> getAuthAdult(Context context) {
        ArrayList<String> uidList = new ArrayList<>();
        String uids = SpUtils.get(context, SP_NAME).getString(SP_KEY, "");
        try {
            JSONArray uidJsonArray = new JSONArray(uids);
            for (int i = 0; i < uidJsonArray.length(); i++) {
                uidList.add(uidJsonArray.optString(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return uidList;
    }

    /**
     * 处理pcheck弱网
     */
    public void handlePCheckGuarantee(Context context , SQResultListener authResultListener) {
        LogUtil.i("处理pcheck弱网");
        String userid = AccountCache.getUserid(context);
        ArrayList<String> authAdult = getAuthAdult(context);
        LogUtil.i("当前uid " + userid);
        LogUtil.i("缓存uid " + authAdult.toString());

        if (authAdult.contains(userid)) {
            LogUtil.i("该uid是实名成年用户，可继续游戏");
            AntiManager.getInstance(context).report();
            Logger.info("pcheck弱网，且是实名已成年，判断是否需要人脸识别");
            FaceVerifyManager.getInstance(context).handleFaceVerify(authResultListener);
            return;
        }

        LogUtil.i("该uid不是实名成年用户，弹窗");
        showGuaranteeDialog(context, "当前网络不稳定，请检查您的网络或稍后重试~");
    }

    /**
     * 处理心跳接口弱网
     */
    public void handleReportGuarantee(Context context) {
        LogUtil.i("心跳接口弱网判断是否在时间段内");
        handleTimeLimit(context, System.currentTimeMillis(), true);
    }


    public void showGuaranteeDialog(Context context, String tip) {
        GuaranteeDialog guaranteeDialog = new GuaranteeDialog(context);
        guaranteeDialog.setTip(tip);
        guaranteeDialog.setCancelable(false);
        guaranteeDialog.show();

    }
}
