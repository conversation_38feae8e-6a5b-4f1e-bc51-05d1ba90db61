package com.sy37sdk.account.view.uifast.adapter;

import android.support.v4.view.PagerAdapter;
import android.support.v4.view.ViewPager;
import android.view.View;

import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.presenter.fast.BaseAccountPagerPresenter;

import java.util.List;


/**
 * 账号切换Adapter
 */
public class AccountPageAdapter extends PagerAdapter {

    private static final String TAG = "AccountPageAdapter";
    private List<BaseAccountPagerPresenter> accountViewList;

    public AccountPageAdapter(List<BaseAccountPagerPresenter> accountViewList) {
        this.accountViewList = accountViewList;
    }

    @Override
    public int getCount() {
        return accountViewList.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object obj) {
        return view == obj;
    }

    @Override
    public int getItemPosition(Object object) {
        return super.getItemPosition(object);
    }

    @Override
    public void destroyItem(View collection, int position, Object o) {
        View view = accountViewList.get(position).getView();
        LogUtil.i(TAG, "destroyItem: position=" + position + "  view:" + view);
        ((ViewPager) collection).removeView(view);
    }

    @Override
    public Object instantiateItem(View view, int position) {
        View v = accountViewList.get(position).getView();
        LogUtil.i(TAG, "instantiateItem: v=" + v + "  pos=" + position);
        ((ViewPager) view).addView(v);
        return v;
    }

}
