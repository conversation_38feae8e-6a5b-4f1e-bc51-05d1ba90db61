package com.sy37sdk.account.policy;

import static android.Manifest.permission.READ_PHONE_STATE;
import static android.Manifest.permission.WRITE_EXTERNAL_STORAGE;
import static com.sqwan.common.util.PermissionHelper.SETTING_REQUEST_CODE;
import static com.sqwan.common.util.PermissionHelper.SQ_REQUEST_PERMISSION_CODE;
import static com.sqwan.common.util.PermissionHelper.mInitPermissions;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import android.support.v4.app.ActivityCompat;
import android.text.TextUtils;

import com.sqwan.base.BaseEnginHandler;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.BusinessUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.PermissionHelper;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.task.Task;
import com.sy37sdk.account.activebefore.ActiveBeforeManager;
import com.sy37sdk.account.activebefore.PermissionInfo;
import com.sy37sdk.account.policy.view.AuthBaseDialog;
import com.sy37sdk.account.policy.view.PermissionDialog;
import com.sy37sdk.account.policy.view.UserAuthPolicyDesDialog;
import com.sy37sdk.account.policy.view.UserAuthTipsDialog;
import com.sy37sdk.account.policy.view.UserHistorySearchDialog;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.uagree.UAgreeManager;


/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-15 19:18
 */
public class AuthHandler extends BaseEnginHandler {

    private static final String SP_AUTH_HANDLE = "sq_auth_handle";
    public static final String disAgreeUserHistorySearchPermission = "disAgreeUserHistorySearchPermission";
    public static AuthHandler getInstance() {
        return ourInstance;
    }

    private AuthHandler() {
    }
    private static final AuthHandler ourInstance = new AuthHandler();
    public interface AgreeCallback{
        void invoke(boolean checkDirect);
    }
    public interface PermissionCallback{
        void invoke();
    }
    private UserAuthPolicyDesDialog userAuthPolicyDesDialog;
    private UserAuthTipsDialog userAuthTipsDialog;
    private PermissionCallback permissionCallback;
    private PermissionDialog permissionDialog;
    //权限场景说明弹窗
    private UserHistorySearchDialog userHistorySearchDialog;


    public void showPermissionDialog(String[] permissions, final AuthBaseDialog.ClickCallback clickCallback){
        Activity activity = checkValid();
        if (activity==null) {
            return;
        }
        String permission = "";
        if (PermissionHelper.getInstance().checkPermission(permissions[0])) {
            permission = WRITE_EXTERNAL_STORAGE;
        }else{
            permission = READ_PHONE_STATE;
        }
        if (TextUtils.isEmpty(permission)) {
            return;
        }
        if (permissionDialog==null) {
            permissionDialog = new PermissionDialog(activity);
        }
        permissionDialog.setClickCallback(new AuthBaseDialog.ClickCallback() {
            @Override
            public void onClickCancel() {
                exit();
            }

            @Override
            public void onClickOk() {
                if (clickCallback!=null) {
                    clickCallback.onClickOk();
                }
                permissionDialog.dismiss();

            }
        });
        if (permissionDialog.isShowing()) {
            return;
        }

        permissionDialog.show();
    }


    public void showPreviewDialog(final AgreeCallback agreeCallback){
        LogUtil.i(TAG,"showPreviewDialog");
        final Activity activity = checkValid();
        if (activity==null) {
            LogUtil.e(TAG,"showPreviewDialog return");
            return;
        }
        if (!UAgreeManager.getInstance().needShow()) {
            LogUtil.i(TAG,"needShow");
            if (agreeCallback!=null) {
                agreeCallback.invoke(false);
            }
            return;
        }
        if (userAuthPolicyDesDialog==null) {
            userAuthPolicyDesDialog = new UserAuthPolicyDesDialog(activity);
            userAuthPolicyDesDialog.setClickCallback(new AuthBaseDialog.ClickCallback() {
                @Override
                public void onClickCancel() {
                    userAuthPolicyDesDialog.dismiss();
                    userAuthTipsDialog.show();
                }

                @Override
                public void onClickOk() {
                    userAuthPolicyDesDialog.dismiss();
                    UAgreeManager.getInstance().update();
                    requestPermissionScene();
                }

                private void requestPermissionScene() {
                    String permissionScene = ActiveBeforeManager.getInstance().permissionInfo.getSceneDesc();
                    if (permissionScene == null) {
                        handlePermissionSuccess();
                        return;
                    }
                    switch (permissionScene) {
                        case PermissionInfo.DESC_MIDDLE_PERMISSION:
                            showHistorySearchDialog(activity, agreeCallback);
                            break;
                        case PermissionInfo.DESC_REQUEST_PERMISSION:
                        case PermissionInfo.DESC_NO_REQUEST_PERMISSION:
                            LogUtil.i(TAG, "直接进游戏");
                            if (agreeCallback != null) {
                                agreeCallback.invoke(true);
                            }
                            break;
                    }
                }
            });
        }
        try {
            userAuthPolicyDesDialog.show();
        } catch (Exception e) {
            String msg = "个人信息保护指引弹窗失败";
            LogUtil.e(TAG, msg, e);
            BuglessAction.reportCatchException(e, msg, BuglessAction.USER_AUTH_POLICY_DES_DIALOG_ERROR);
        }
        if (userAuthTipsDialog==null) {
            userAuthTipsDialog = new UserAuthTipsDialog(activity);
            userAuthTipsDialog.setClickCallback(new AuthBaseDialog.ClickCallback() {
                @Override
                public void onClickCancel() {

                }

                @Override
                public void onClickOk() {
                    userAuthTipsDialog.dismiss();
                    userAuthPolicyDesDialog.show();
                }
            });
        }

    }

    private void showHistorySearchDialog(Activity activity, final AgreeCallback agreeCallback) {
        if (userHistorySearchDialog == null) {
            userHistorySearchDialog = new UserHistorySearchDialog(activity);
            userHistorySearchDialog.setClickCallback(new AuthBaseDialog.ClickCallback() {
                @Override
                public void onClickCancel() {
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PERMISSION_SCENE_FORBIDDEN);
                    handlePermissionSuccess();
                    if (userAuthPolicyDesDialog != null) {
                        userHistorySearchDialog.dismiss();
                    }
                    SpUtils.get(context).put(AuthHandler.disAgreeUserHistorySearchPermission, true);
                }

                @Override
                public void onClickOk() {
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.PERMISSION_SCENE_GRANTED);
                    if (agreeCallback != null) {
                        agreeCallback.invoke(true);
                    }
                    if (userAuthPolicyDesDialog != null) {
                        userHistorySearchDialog.dismiss();
                    }
                    SpUtils.get(context).put(AuthHandler.disAgreeUserHistorySearchPermission, false);
                }
            });
        }
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.permission_scene, SqTrackPage.SqTrackViewName.permission_scene);
        userHistorySearchDialog.show();
    }

    public void checkPermission(PermissionCallback permissionCallback){
        this.permissionCallback = permissionCallback;
        Task.post(new Runnable() {
            @Override
            public void run() {
                LogUtil.i(TAG,"showPreviewDialog");
                showPreviewDialog(new AuthHandler.AgreeCallback() {
                    @Override
                    public void invoke(boolean checkDirect) {
                        LogUtil.i(TAG,"showPreviewDialog invoke");
                        String permissionScene = ActiveBeforeManager.getInstance().permissionInfo.getSceneDesc();
                        if (permissionScene == null) {
                            handlePermissionSuccess();
                            return;
                        }
                        switch (permissionScene) {
                            case PermissionInfo.DESC_REQUEST_PERMISSION:
                            case PermissionInfo.DESC_MIDDLE_PERMISSION:
                                if (!BusinessUtil.getAuthPermission(context)) {
                                    checkPermission(checkDirect, false);
                                } else {
                                    handlePermissionSuccess();
                                }
                                break;
                            case PermissionInfo.DESC_NO_REQUEST_PERMISSION:
                            default:
                                handlePermissionSuccess();
                                break;
                        }
                    }
                });
            }
        });
    }


    private void requestPermissions(){
        PermissionHelper.getInstance().requestPermissions(mInitPermissions, PermissionHelper.DEFAULT_PERMISSIONS_DESC, SQ_REQUEST_PERMISSION_CODE, mPermissionCallback);
        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.REQUEST_PERMISSION);
    }


    public void checkPermission(boolean checkDirect,boolean fromActivityResult) {
        LogUtil.i(TAG,"checkPermission checkDirect " + checkDirect + " fromActivityResult " + fromActivityResult);
        if (PermissionHelper.getInstance().checkPermissions(mInitPermissions)){
                LogUtil.i(TAG,"权限申请完毕，初始化开始");
                handlePermissionSuccess();
        } else {
            LogUtil.i(TAG,"权限申请开始");
            if (checkDirect) {
                requestPermissions();
            }else{
                if (fromActivityResult && checkPermissionForbidden()) {
                    requestPermissions();
                }else{
                    if (checkPermissionForbidden()) {
                        requestPermissions();
                    }else{
                        AuthHandler.getInstance().showPermissionDialog(mInitPermissions, new AuthBaseDialog.ClickCallbackAdapter(){
                            @Override
                            public void onClickOk() {
                                requestPermissions();
                            }
                        });
                    }

                }

            }

        }
    }
    private void goSetting(Activity activity){
        LogUtil.i(TAG,"goSetting");
        Uri packageURI = Uri.parse("package:" + context.getPackageName());
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
        activity.startActivityForResult(intent, SETTING_REQUEST_CODE);
    }
    private PermissionHelper.PermissionCallback mPermissionCallback = new PermissionHelper.PermissionCallback() {
        @Override
        public void onRequestPermissionsResult(String[] permissions, int[] grantResults) {
            final Activity activity = checkValid();
            if (activity==null) {
                return;
            }
            String tag = "mPermissionCallback";
            LogUtil.i(tag,"sd卡，sim卡权限回调");
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    LogUtil.i(tag, permissions[i] + " 权限未申请");
                    boolean showRequestPermission = ActivityCompat.shouldShowRequestPermissionRationale(activity, permissions[i]);
                    LogUtil.i(tag, "权限是否被禁止: " + showRequestPermission);
                    if (!showRequestPermission) {
                        handlePermissionSuccess();
                        BusinessUtil.setAuthPermission(context, true);
                        return;
                    }
                } else {
                    LogUtil.i(tag,permissions[i] + " 权限审核通过");
                }
            }
            handlePermissionSuccess();
            BusinessUtil.setAuthPermission(context, true);
            SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.PERMISSION_CALLBACK);
        }
    };
    private boolean checkPermissionForbidden(){
        Activity activity = checkValid();
        boolean isForbidden = true;
        for (String mInitPermission : mInitPermissions) {
            if (activity!=null) {
                boolean _isForbidden = !ActivityCompat.shouldShowRequestPermissionRationale(activity, mInitPermission);
                LogUtil.i(TAG,"mInitPermission " + mInitPermission + " "+isForbidden);
                isForbidden = isForbidden && _isForbidden;
            }
        }
        LogUtil.i(TAG,"checkPermissionForbidden "+isForbidden);
        return isForbidden;
    }
    public boolean getIsPlayWithoutPermission(){
        return !ActiveBeforeManager.getInstance().permissionInfo.isNecessary();
    }
    private void handlePermissionSuccess(){
        if (permissionCallback!=null) {
            permissionCallback.invoke();
        }
    }

    /**
     * 设置用户点击了同意隐私协议
     */
    public void setAuthHandle() {
        SpUtils.get(context).put(SP_AUTH_HANDLE, true);
    }

    /**
     * 是否点击了同意隐私协议
     *
     * @return
     */
    public boolean isAuthHandle() {
        return SpUtils.get(context).getBoolean(SP_AUTH_HANDLE);
    }

}
