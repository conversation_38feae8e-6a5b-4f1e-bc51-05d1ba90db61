package com.sy37sdk.account.age;

import android.content.Context;

import com.sqwan.common.util.SpUtils;

public class AppropriateAgeCacheHelper {

    private static final String SP_KEY_APPROPRIATE_AGE = "sp_key_appropriate_age";

    /**
     * 将适龄配置存入sp
     *
     * @param context
     * @param appropriateAge
     */
    public static void saveAppropriateAge(Context context, AppropriateAge appropriateAge) {
        if (appropriateAge == null) {
            return;
        }
        SpUtils.get(context).put(SP_KEY_APPROPRIATE_AGE, AppropriateAge.objectToJson(appropriateAge));
    }

    /**
     * 从sp中读取适龄
     *
     * @param context
     * @return
     */
    public static AppropriateAge getAppropriateAge(Context context) {
        String appropriateAgeJson = SpUtils.get(context).getString(SP_KEY_APPROPRIATE_AGE);
        return AppropriateAge.jsonToObject(appropriateAgeJson);
    }

}
