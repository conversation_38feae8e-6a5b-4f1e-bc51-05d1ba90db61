package com.sy37sdk.account.floatview;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.Region;
import android.graphics.drawable.ColorDrawable;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sq.tools.utils.ScreenUtils;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.DensityUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sy.window.WindowX;
import com.sy.window.WindowX.OnWindowLifecycle;
import com.sy.window.draggable.BaseDraggable;
import com.sy.window.draggable.BaseDraggable.DraggingCallback;
import com.sy37sdk.account.floatview.FloatWindow.DragBottom2DeleteCallback;
import com.sy37sdk.account.floatview.redpacket.RedPacketInfo;
import com.sy37sdk.account.floatview.redpacket.RedPacketManager;

/**
 * <AUTHOR>
 * 37悬浮窗基础view
 */
public class SqBaseFloatView extends RelativeLayout {

    private static final float BUBBLE_SCALEX = 0.6f;
    private static final float BUBBLE_SCALEY = 0.6f;
    private PopupWindow mPopupWindow;

    private ImageView mRedImageView;

    private FloatWindow.OnDragCallBack mOnDragCallBack;

    public BottomDeleteView mBottomDeleteView;

    private final String mIconUrl;

    private View.OnClickListener onClickListener;
    private DismissListener dismissListener;
    private MenuListener menuListener;

    private final WindowX<?> mWindowX;

    private final Activity mActivity;

    /** 删除区域 y 坐标 */
    private float mDeletedStartY;

    private boolean mAnimatingFlag;
    private boolean mDraggingFlag;

    private final View mWindowDecorView;

    private Animator mAnimator;

    private final FloatViewDraggable mDraggable;

    private final DragBottom2DeleteCallback mDragBottom2DeleteCallback;

    private int mCurrentBubbleViewId = -1;

    private MenuConfig mWarnMenuConfig;
    private int mNormalViewId = -1;
    private final View mMenuLayout;

    private OnClickListener mNormalViewOnClickListener;

    /**
     * 计算悬浮球和不规则隐藏区域的相交区域
     */
    private Path mHidePath;
    private Path mFloatPath;
    private Region mHideRegion;
    private Region mScreenRegion;
    private Region mFloatRegion;
    private int mScreenWidth;
    private int mScreenHeight;

    public SqBaseFloatView(final Activity activity, String iconUrl, final View menuLayout,
                            final RedPacketInfo info, DragBottom2DeleteCallback callback) {
        super(activity);
        mIconUrl = iconUrl;
        mActivity = activity;
        mDragBottom2DeleteCallback = callback;
        mMenuLayout = menuLayout;
        setClickable(true);
        if (info != null) {
            addRedPackView(activity, info);
        } else {
            addNormalView(activity, menuLayout);
        }

        mWindowDecorView = mActivity.getWindow().getDecorView();

        mDraggable = new FloatViewDraggable();

        // 不允许移动到屏幕挖孔区域
        mDraggable.setAllowMoveToScreenNotch(false);
        mDraggable.setDraggingCallback(new DraggingCallback() {
            @Override
            public void onStartDragging(WindowX<?> window) {
                mDraggingFlag = true;
                if (mOnDragCallBack != null) {
                    mOnDragCallBack.onStartDrag();
                }
                View decorView = mWindowX.getDecorView();
                if (!isFullShowView(decorView)) {
                    showFullView(decorView);
                }
                if (mDragBottom2DeleteCallback == null) {
                    return;
                }
                mDragBottom2DeleteCallback.onDragBottom2Delete(false, true, false);
            }

            @Override
            public void onExecuteDragging(WindowX<?> window) {
                if (mDragBottom2DeleteCallback == null) {
                    return;
                }
                mDragBottom2DeleteCallback.onDragBottom2Delete(false, true, reachDeleteArea(window));
            }

            @Override
            public void onStopDragging(WindowX<?> window) {
                mDraggingFlag = false;
                updateBubbleView();
                saveWindowCoordinate();
                if (mDragBottom2DeleteCallback == null) {
                    return;
                }
                mDragBottom2DeleteCallback.onDragBottom2Delete(reachDeleteArea(window), false, false);
            }
        });
        mDraggable.setSpringBackAnimCallback(new FloatViewDraggable.SpringBackAnimCallback() {
            @Override
            public void onSpringBackAnimationStart(WindowX<?> window, Animator animator) {
                mAnimatingFlag = true;
                mAnimator = animator;
            }

            @Override
            public void onSpringBackAnimationEnd(WindowX<?> window, Animator animator) {
                mAnimatingFlag = false;
                mAnimator = null;
                saveWindowCoordinate();
                postStayEdgeRunnable();
                if (mOnDragCallBack != null) {
                    mOnDragCallBack.onStayEdge();
                }
            }
        });

        mWindowX = new WindowX<>(mActivity)
            // 不设置显示动画
            .setAnimStyle(0)
            .setContentView(this)
            // 设置成可拖拽的
            .setDraggable(mDraggable, false)
            .setOnWindowLifecycle(new OnWindowLifecycle() {
                @Override
                public void onWindowShow(WindowX<?> window) {
                    // 隐藏一半的悬浮球
                    postStayEdgeRunnable();
                }
            });

        mWindowX.show();

        int screenWidth = DisplayUtil.getScreenWidth(getContext());
        int screenHeight = DisplayUtil.getScreenHeight(getContext());

        //构建不规则的隐藏区域
        mHideRegion = new Region();
        mFloatRegion = new Region();
        mFloatPath = new Path();
        if (mScreenRegion == null) {
            mScreenRegion = new Region(0, 0, screenWidth, screenHeight); // 屏幕范围
        }

        mBottomDeleteView = new BottomDeleteView(activity);
        mBottomDeleteView.attach( paramsY -> {
            mDeletedStartY = paramsY;
            mScreenWidth = ScreenUtils.getScreenWidth(getContext());
            //计算屏幕完整高度，通过deleteView所处位置+实际view高度，避免华为手机有导航栏时候获取不到正确高度
            mScreenHeight = (int) (mDeletedStartY + mBottomDeleteView.getHeight());
            mScreenRegion = new Region(0, 0, mScreenWidth, mScreenHeight);
            mHidePath = getDeleteIrregularPath();
            mHideRegion.setPath(mHidePath, mScreenRegion);
        });

        recoverWindowCoordinate();
    }

    private void updateBubbleView() {
        ImageView normalView = findViewById(mNormalViewId);
        if (normalView == null) {
            LogUtil.i("normalView is null, updateBubbleView return");
            return;
        }
        removeView(findViewById(mCurrentBubbleViewId));
        TextView bubbleView = createBubbleView();
        if (bubbleView != null) {
            updateBubblePosition(bubbleView);
            bubbleView.setVisibility(VISIBLE);
            if (isNeedShake()) {
                post(this::setRotePivot);
            }
        }
    }

    private void updateBubblePosition(@Nullable TextView bubbleView) {
        if (bubbleView == null){
            return;
        }
        if (findViewById(mCurrentBubbleViewId) != null) { //已经有气泡了，不添加
            return;
        }
        ImageView normalView = findViewById(mNormalViewId);
        RelativeLayout.LayoutParams bubbleParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT); //气泡的参数
        RelativeLayout.LayoutParams normalViewParams = (RelativeLayout.LayoutParams) normalView.getLayoutParams(); //圆圈
        normalViewParams.removeRule(RelativeLayout.ALIGN_PARENT_LEFT);
        normalViewParams.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
        normalViewParams.removeRule(RelativeLayout.RIGHT_OF);
        normalViewParams.removeRule(RelativeLayout.BELOW);
        normalViewParams.setMargins(0, 0, 0, 0);
        if (isTopShow()) {
            LogUtil.d("showRemindAnim isTopShow");
            if (isLeftShow()) { //左上
                LogUtil.d("showRemindAnim isTopShow isLeftShow");
                bubbleView.setBackgroundResource(SqResUtils.getDrawableId(mActivity, "background_bubble_bottom_right"));
                normalViewParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                bubbleParams.addRule(RelativeLayout.RIGHT_OF, normalView.getId());
                bubbleParams.setMargins(-dip2px(15), -dip2px(15), 0, dip2px(15));
            } else {
                LogUtil.d("showRemindAnim isTopShow isRightShow");
                bubbleView.setBackgroundResource(SqResUtils.getDrawableId(mActivity, "background_bubble_bottom_left"));
                normalViewParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                normalViewParams.addRule(RelativeLayout.RIGHT_OF, bubbleView.getId());
                bubbleParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                bubbleParams.setMargins(0, -dip2px(15), -dip2px(15), 0);
            }
            bubbleParams.addRule(RelativeLayout.BELOW, normalView.getId());
        } else {
            if (isLeftShow()) {
                LogUtil.d("showRemindAnim isLeftShow");
                bubbleView.setBackgroundResource(SqResUtils.getDrawableId(mActivity, "background_bubble_right"));
                bubbleParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                bubbleParams.addRule(RelativeLayout.RIGHT_OF, normalView.getId());
                normalViewParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                bubbleParams.setMargins(-dip2px(15), dip2px(15), 0, -dip2px(15));
            } else {
                LogUtil.d("showRemindAnim isRightShow");
                bubbleView.setBackgroundResource(SqResUtils.getDrawableId(mActivity, "background_bubble_left"));
                bubbleParams.addRule(RelativeLayout.ALIGN_PARENT_TOP);
                bubbleParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                normalViewParams.addRule(RelativeLayout.RIGHT_OF, bubbleView.getId());
                bubbleParams.setMargins(0, dip2px(15), -dip2px(15), -dip2px(15));
            }
            normalViewParams.addRule(RelativeLayout.BELOW, bubbleView.getId());
        }
        addView(bubbleView, bubbleParams);
        bubbleView.post(() -> {
            setPivot(bubbleView);
            bubbleView.setScaleX(BUBBLE_SCALEX);
            bubbleView.setScaleY(BUBBLE_SCALEY);
        });
    }

    private void setPivot(View view) {
        if (isTopShow()) {
            if (isLeftShow()) {
                view.setPivotX(0);
                view.setPivotY(0);
            } else {
                view.setPivotX(view.getWidth());
                view.setPivotY(0);
            }
        } else {
            if (isLeftShow()) {
                view.setPivotX(0);
                view.setPivotY(view.getHeight());
            } else {
                view.setPivotX(view.getWidth());
                view.setPivotY(view.getHeight());
            }
        }
    }


    private boolean isNeedShake() {
        if (mWarnMenuConfig != null) {
            return mWarnMenuConfig.warningType.contains(MenuConfig.WARNING_TYPE_SHAKE);
        }
        return false;
    }

    private boolean isNeedTextWarn() {
        if (mWarnMenuConfig != null) {
            return mWarnMenuConfig.warningType.contains(MenuConfig.WARNING_TYPE_TEXT);
        }
        return false;
    }

    private TextView createBubbleView() {
        if (mWarnMenuConfig != null && !TextUtils.isEmpty(mWarnMenuConfig.warningMsg) && isNeedTextWarn()) {
            TextView bubbleView = new TextView(mActivity);
            mCurrentBubbleViewId = View.generateViewId();
            bubbleView.setId(mCurrentBubbleViewId);
            bubbleView.setText(mWarnMenuConfig.warningMsg);
            bubbleView.setLines(1);
            bubbleView.setPadding(20,0,20,0);
            bubbleView.setVisibility(View.INVISIBLE);
            bubbleView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dip2px(6));
            bubbleView.setGravity(Gravity.CENTER);
            bubbleView.setTextColor(Color.parseColor("#FFFFFF"));
            return bubbleView;
        }
        return null;
    }

    public MenuConfig getShowingConfig() {
        return mWarnMenuConfig;
    }

    public void setShowingConfig(MenuConfig menuConfig) {
        mWarnMenuConfig = menuConfig;
    }

    public void removeBubble() {
        removeView(findViewById(mCurrentBubbleViewId));
        post(() -> updatePopupWindow(false, mActivity, mMenuLayout));
    }

    public void showRemindAnim(MenuConfig menuConfig) {
        mWarnMenuConfig = menuConfig;
        if (mWindowX == null) {
            LogUtil.e("mWindowX is null, showRemindAnim return");
            return;
        }
        ImageView normalView = findViewById(mNormalViewId);
        if (normalView == null) {
            LogUtil.i("normalView is null, showRemindAnim return");
            return;
        }
        removeView(findViewById(mCurrentBubbleViewId));
        TextView bubbleView = createBubbleView();
        if (bubbleView != null) {
            postStayEdgeRunnable();
            mWindowX.getDecorView().setClipBounds(null);
            updateBubblePosition(bubbleView);
            post(() -> scaleAnim(bubbleView));
        }
    }

    public void checkShake(MenuConfig menuConfig) {
        mWarnMenuConfig = menuConfig;
        if (isNeedShake()) {
            post(() -> rotateAnim(SqBaseFloatView.this));
        }
    }

    private void scaleAnim(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0, BUBBLE_SCALEX).setDuration(600); //600
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0, BUBBLE_SCALEY).setDuration(600);
        setPivot(view);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX,scaleY);
        animatorSet.addListener(new AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                view.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.start();
    }

    private void setRotePivot() {
        ImageView normalView = findViewById(mNormalViewId);
        if (normalView == null) {
            return;
        }
        int[] location = new int[2];
        normalView.getLocationInWindow(location);
        SqBaseFloatView.this.setPivotX(location[0] + (normalView.getWidth() / 2f));
        SqBaseFloatView.this.setPivotY(location[1] + (normalView.getHeight() / 2f));
    }

    private void rotateAnim(View view) {
        setRotePivot();
        ObjectAnimator rotateAnimator = ObjectAnimator.ofFloat(view, "rotation", -8, 8);
        rotateAnimator.setDuration(500);
        rotateAnimator.setRepeatMode(ValueAnimator.REVERSE);
        rotateAnimator.setRepeatCount(6);
        rotateAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                view.setRotation(0);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                view.setRotation(0);
            }
        });
        rotateAnimator.start();
    }

    public void handleMenuConfigClick(MenuConfig menuConfig) {

    }

    private int dip2px(int px) {
        return DensityUtil.dip2px(mActivity, px);
    }

    protected boolean isNotchAffected() {
        Rect safeInsetRect = mDraggable.getSafeInsetRect();
        if (safeInsetRect == null) {
            return false;
        }
        return safeInsetRect.left > 0 || safeInsetRect.right > 0 || safeInsetRect.top > 0 || safeInsetRect.bottom > 0;
    }

    public void showRedDot(final boolean showRedDot) {
        if (mRedImageView == null) {
            return;
        }
        post(() -> mRedImageView.setVisibility(showRedDot ? VISIBLE : GONE));
    }

    public void setClickListener(OnClickListener clickListener) {
        this.onClickListener = clickListener;
    }

    public void setDismissListener(DismissListener dismissListener) {
        this.dismissListener = dismissListener;
    }

    public void setMenuListener(MenuListener menuListener) {
        this.menuListener = menuListener;
    }


    private void addNormalView(Activity activity, final View menuLayout) {
        final ImageView floatView = new ImageView(activity);
        mNormalViewId = View.generateViewId();
        floatView.setId(mNormalViewId);
        if (!TextUtils.isEmpty(mIconUrl)) {
            AsyncImageLoader loader = new AsyncImageLoader(activity);
            loader.loadDrawable(mIconUrl, floatView, (imageDrawable, imageView, imageUrl) -> {
                if (imageDrawable != null) {
                    floatView.setImageBitmap(imageDrawable);
                } else {
                    int floatImgId1 = SqResUtils.getDrawableId(activity, "sy37_wm_img_move");
                    floatView.setImageResource(floatImgId1);
                }
            });
        }
        mNormalViewOnClickListener = v -> {
            View windowDecorView = mWindowX.getDecorView();
            if (!isFullShowView(windowDecorView)) {
                showFullView(windowDecorView);
                postStayEdgeRunnable();
                return;
            }

            if (onClickListener != null) {
                onClickListener.onClick(SqBaseFloatView.this);
            }
            if (mAnimatingFlag || mDraggingFlag) {
                return;
            }

            if (menuLayout == null) {
                LogUtil.i("menuLayout == null");
                return;
            }

            if (mPopupWindow == null) {
                // 避免弹窗的时候底部导航栏显示出来
                menuLayout.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                        View.SYSTEM_UI_FLAG_FULLSCREEN |
                        View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY |
                        View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
                mPopupWindow = new PopupWindow(menuLayout, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
                mPopupWindow.setOnDismissListener(this::postStayEdgeRunnable);
                //设置透明背景，适配某些手机popWindow点击不消失的适配bug
                ColorDrawable colorDrawable = new ColorDrawable(Color.TRANSPARENT);
                mPopupWindow.setBackgroundDrawable(colorDrawable);
            }

            if (mPopupWindow.isShowing()) {
                return;
            }

            if (menuListener != null) {
                menuListener.onMenuShow();
            }

            updatePopupWindow(true,activity, menuLayout);
        };
        floatView.setOnClickListener(mNormalViewOnClickListener);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
            DensityUtil.dip2px(activity, 45), DensityUtil.dip2px(activity, 45));
        addView(floatView, params);
        mRedImageView = new ImageView(activity);
        mRedImageView.setImageResource(SqResUtils.getIdByName("sysq_bg_red_dot", "drawable", activity));
    }

    public void expandMenu() {
        if (mNormalViewOnClickListener != null) {
            if (isFullShowView(mWindowX.getDecorView())) {
                mNormalViewOnClickListener.onClick(this);
            } else {
                //模拟点两下，第一次是把悬浮球显示全，第二次是展开
                mNormalViewOnClickListener.onClick(this);
                mNormalViewOnClickListener.onClick(this);
            }
        }
    }

    private void addRedPackView(final Context context, final RedPacketInfo redPacketInfo) {
        GifMovieView gifMovieView = new GifMovieView(context);
        gifMovieView.setOnClickListener(v -> {
            View windowDecorView = mWindowX.getDecorView();
            if (!isFullShowView(windowDecorView)) {
                showFullView(windowDecorView);
                postStayEdgeRunnable();
                return;
            }
            postStayEdgeRunnable();
            RedPacketManager.getInstance().showTargetUrl(context, redPacketInfo);
        });
        gifMovieView.setMovieFilePath(redPacketInfo.imgLocalPath);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addView(gifMovieView, params);
    }

    private int getBubbleWidth() {
        View bubbleView = findViewById(mCurrentBubbleViewId);
        if (bubbleView != null) {
            if (isLeftShow()) {
                return (int) (bubbleView.getWidth()) + dip2px(15);
            } else {
                return (int) (bubbleView.getWidth()) - dip2px(15);
            }
        }
        return 0;
    }

    private void updatePopupWindow(boolean isAdd, Activity activity, View menuLayout) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        Window window = activity.getWindow();
        if (window == null) {
            return;
        }
        View activityDecorView = window.getDecorView();

        int gravity, x, y;
        if (isNotchAffected()) {
            int px = mWindowX.getWindowParams().x + mWindowX.getViewWidth() + 10;
            if (px > mWindowDecorView.getWidth() / 2) {
                int width = menuLayout.getWidth();
                if (width == 0) {
                    // View 还没有添加到 Window 上面，所以获取的宽度为 0，这里需要手动触发一下 View 测量
                    menuLayout.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                        MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
                    // 获取测量后的 View 的宽度
                    width = menuLayout.getMeasuredWidth();
                }
                px = mWindowX.getWindowParams().x  - width;
            }
            gravity = Gravity.TOP | Gravity.LEFT;
            if (isLeftShow()) {
                x = px - getBubbleWidth();
            } else {
                x = px + getBubbleWidth();
            }
            y = mWindowX.getWindowParams().y;
        } else if (isLeftShow()) {
            gravity = Gravity.TOP | Gravity.LEFT;
            x = mWindowX.getContentView().getWidth() - getBubbleWidth();
            y = mWindowX.getWindowParams().y;
        } else {
            gravity = Gravity.TOP | Gravity.RIGHT;
            x = mWindowX.getContentView().getWidth()  + getBubbleWidth();
            y = mWindowX.getWindowParams().y;
        }

        if (mPopupWindow != null) {
            if (isAdd) {
                mPopupWindow.showAtLocation(activityDecorView, gravity, x , y);
            } else {
                mPopupWindow.update(x, y, mPopupWindow.getWidth(), mPopupWindow.getHeight());
            }
        }
    }

    public void setWindowVisibility(int visibility) {
        mWindowX.setVisibility(visibility);
    }

    public int getWindowVisibility() {
        return mWindowX.getVisibility();
    }

    public void dismiss() {
        if (dismissListener != null) {
            dismissListener.onDismiss();
        }
        removeCallbacks(mStayEdgeRunnable);
        mWindowX.cancel();
        dismissMenu();
        if (mBottomDeleteView != null) {
            mBottomDeleteView.removeBottomDeleteView();
        }
    }

    public void show(boolean addWindowManagerByGone) {
        recoverWindowCoordinate();
        if (addWindowManagerByGone) {
            mWindowX.setVisibility(View.INVISIBLE);
        }
        mWindowX.show();
    }

    public void dismissMenu() {
        if (mPopupWindow == null) {
            return;
        }
        mPopupWindow.dismiss();
    }

    /**
     * 刷新红点位置
     */
    public void refreshRedDot() {
        if (mOnDragCallBack == null) {
            mOnDragCallBack = new FloatWindow.OnDragCallBack() {
                @Override
                public void onStayEdge() {
                    refreshRedDot();
                }

                @Override
                public void onStartDrag() {

                }
            };
        }
        post(() -> {
            if (mRedImageView == null) {
                return;
            }
            if (mRedImageView.getParent() == null) {
                addView(mRedImageView);
            }
            LayoutParams params;
            int size = DisplayUtil.dip2px(getContext(), 13);
            if (mRedImageView.getParent() == null) {
                params = new LayoutParams(size, size);
            } else {
                params = (LayoutParams) mRedImageView.getLayoutParams();
            }
            int padding = DisplayUtil.dip2px(getContext(), 4);
            if (isLeftShow()) {
                params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                params.setMargins(getRight() - mRedImageView.getDrawable().getIntrinsicWidth() - padding, padding, 0, 0);
                mRedImageView.setLayoutParams(params);
            } else {
                params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
                params.setMargins(padding, padding, 0, 0);
                mRedImageView.setLayoutParams(params);
            }
        });

    }

    /**
     * 悬浮球是否靠左显示
     */
    private boolean isLeftShow(){
        return (mWindowX.getWindowParams().x + mWindowX.getViewWidth() / 2f)  < DisplayUtil.getScreenWidth(getContext()) / 2f;
    }

    /**
     * 悬浮球是否靠顶显示
     */
    private boolean isTopShow() {
        WindowManager.LayoutParams windowParams = mWindowX.getWindowParams();
        if (isLeftShow()) {
            return windowParams.x > windowParams.y;
        } else {
            return DisplayUtil.getScreenWidth(getContext()) - windowParams.x - this.getWidth() > windowParams.y;
        }
    }

    /**
     * 重置悬浮球 View
     * @param resetWindowCoordinate     是否重置坐标
     */
    public void resetView(boolean resetWindowCoordinate) {
        dismissMenu();
        cancelAnim();
        removeCallbacks(mStayEdgeRunnable);
        if (resetWindowCoordinate) {
            resetWindowCoordinate();
        } else {
            recoverWindowCoordinate();
        }

        showFullView(mWindowX.getDecorView());
        mWindowX.update();
        postStayEdgeRunnable();
        updateBottomDeleteView();
    }

    /**
     * 取消悬浮球回弹动画
     */
    private void cancelAnim() {
        if (mAnimator == null) {
            return;
        }

        mAnimator.end();
        mAnimator.removeAllListeners();
    }

    /**
     * 更新底部删除 View 视图
     */
    private void updateBottomDeleteView() {
        mBottomDeleteView.updateView();
    }

    /**
     * 发送贴边显示任务
     */
    public void postStayEdgeRunnable() {
        removeCallbacks(mStayEdgeRunnable);
        postDelayed(mStayEdgeRunnable, 3000);
    }

    protected Runnable mStayEdgeRunnable = new Runnable() {
        @Override
        public void run() {
            if (mAnimatingFlag || mDraggingFlag) {
                return;
            }

            if (mPopupWindow != null && mPopupWindow.isShowing()) {
                return;
            }

            View windowDecorView = mWindowX.getDecorView();

            if (isLeftShow()) {
                if (isTopShow()) {
                    hideHalfView(windowDecorView, Gravity.TOP);
                } else {
                    hideHalfView(windowDecorView, Gravity.LEFT);
                }
            } else {
                if (isTopShow()) {
                    hideHalfView(windowDecorView, Gravity.TOP);
                } else {
                    hideHalfView(windowDecorView, Gravity.RIGHT);
                }
            }
        }
    };

    private void showFullView(View view) {
        Rect safeInsetRect = mDraggable.getSafeInsetRect();
        if (safeInsetRect != null && safeInsetRect.left > 0 && !isTopShow()) {
            mDraggable.updateLocation(
                mWindowX.getWindowParams().x + mWindowX.getViewWidth() / 2f,
                mWindowX.getWindowParams().y, false);
        }
        if (view == null) {
            return;
        }
        int viewWidth = view.getWidth();
        int viewHeight = view.getHeight();
        // 设置画板偏移
        view.setTranslationX(0);
        view.setTranslationY(0);
        // 设置裁剪区域
        view.setClipBounds(null);
    }

    /**
     * View 是否全屏显示
     */
    private boolean isFullShowView(View view) {
        Rect safeInsetRect = mDraggable.getSafeInsetRect();
        if (safeInsetRect != null && safeInsetRect.left > 0 && !isTopShow()) {
            if (mWindowX.getWindowParams().x < safeInsetRect.left) {
                return false;
            }
        }
        if (view == null) {
            return true;
        }
        int viewWidth = view.getWidth();
        int viewHeight = view.getHeight();
        Rect clipBounds = view.getClipBounds();
        if (view.getTranslationX() != 0 && view.getTranslationY() != 0) {
            return false;
        }
        if (clipBounds == null) {
            return true;
        }
        return clipBounds.left == 0 && clipBounds.top == 0 &&
            clipBounds.right == viewWidth && clipBounds.bottom == viewHeight;
    }

    /**
     * 隐藏 View 一半显示
     */
    private void hideHalfView(View view, int gravity) {
        if (view == null) {
            return;
        }

        int viewWidth = view.getWidth();
        int viewHeight = view.getHeight();

        // 创建一个矩形来定义裁剪区域
        Rect clipBounds = new Rect();
        switch (gravity) {
            case Gravity.LEFT:
                Rect safeInsetRect = mDraggable.getSafeInsetRect();
                if (safeInsetRect != null && safeInsetRect.left > 0) {
                    mDraggable.updateLocation(
                        mWindowX.getWindowParams().x - mWindowX.getViewWidth() / 2f,
                        mWindowX.getWindowParams().y, true);
                } else {
                    int offSet = getNormalView() != null ? getNormalView().getWidth() / 2 : viewWidth / 2; //用小球来做偏移
                    clipBounds.set(offSet, 0, viewWidth, viewHeight);
                    // 设置画板偏移
                    view.setTranslationX(-offSet);
                    view.setTranslationY(0);
                    // 设置裁剪区域
                    view.setClipBounds(clipBounds);
                }
                break;
            case Gravity.RIGHT:
                int offSet = getNormalView() != null ? getNormalView().getWidth() / 2 : viewWidth / 2; //用小球来做偏移
                clipBounds.set(0, 0, viewWidth - offSet, viewHeight);
                // 设置画板偏移
                view.setTranslationX(offSet);
                view.setTranslationY(0);
                // 设置裁剪区域
                view.setClipBounds(clipBounds);
                break;
            case Gravity.TOP:
                int offSetHeight = getNormalView() != null ? getNormalView().getHeight() / 2 : viewHeight / 2; //用小球来做偏移
                clipBounds.set(0, offSetHeight, viewWidth, viewHeight);
                // 设置画板偏移
                view.setTranslationX(0);
                view.setTranslationY(-offSetHeight);
                // 设置裁剪区域
                view.setClipBounds(clipBounds);
                break;
            default:
                break;
        }
    }

    /**
     * 屏幕高度使用deleteView的window.y + 自身高度来计算
     * 避免华为手机只要有导航栏存在，都会导致计算的高度有问题
     * 是否到达底部删除区域
     */
    private boolean reachDeleteArea(WindowX<?> window) {
        int radius = window.getViewWidth() / 2;
        int floatX =  window.getWindowParams().x + radius;
        int floatY = window.getWindowParams().y + radius;

        //hide path
        if (mHidePath == null) {
            mHidePath = getDeleteIrregularPath();
            mHideRegion.setPath(mHidePath, mScreenRegion);
        }

        //float path
        mFloatPath.reset();
        mFloatPath.addCircle(floatX, floatY, radius, Path.Direction.CW);
        mFloatRegion.setPath(mFloatPath, mScreenRegion);

        return mFloatRegion.op(mHideRegion, Region.Op.INTERSECT);
    }

    /**
     * 获取删除不规则区域的Path
     */
    private Path getDeleteIrregularPath() {
        final int screenWidth = mScreenWidth;
        final int screenHeight = mScreenHeight;
        int height = mBottomDeleteView.getHeight();
        int width = FloatViewUtils.getBottomDeleteWidth(getContext());
        boolean isLandscape = screenWidth > screenHeight;
        //横屏不规则空白区域宽高相等，竖屏则宽度为高度的一半
        int delta = isLandscape ? 0 : height / 2;
        float startX = (screenWidth - width) / 2f;
        float endX = screenWidth - startX;
        Path path = new Path();
        path.moveTo(startX, screenHeight);
        path.lineTo(endX, screenHeight);
        path.lineTo(endX - height + delta, screenHeight - height);
        path.lineTo(startX + height - delta, screenHeight - height);
        path.lineTo(startX, screenHeight);
        path.close();
        return path;
    }

    /**
     * 恢复上次悬浮球坐标
     */
    private void recoverWindowCoordinate() {
        FloatViewUtils.FloatViewPosConfig config = FloatViewUtils.getFloatViewPositionConfig(getContext(),this);
        if (config.x != -1 && config.y != -1) {
            mWindowX.setGravity(Gravity.LEFT | Gravity.TOP);
            mWindowX.setXOffset(config.x);
            mWindowX.setYOffset(config.y);
        } else {
            resetWindowCoordinate();
        }
    }

    private View getNormalView() {
        return findViewById(mNormalViewId);
    }

    /**
     * 保存悬浮球坐标
     */
    private void saveWindowCoordinate() {
        if (reachDeleteArea(mWindowX)) {
            return;
        }
        if (mWindowX.getVisibility() != View.VISIBLE) {
            return;
        }
        FloatViewUtils.setFloatViewPos(getContext(), this,
            mWindowX.getWindowParams().x, mWindowX.getWindowParams().y,
            ScreenOrientationHelper.currentType);
    }

    /**
     * 重置悬浮球坐标
     */
    private void resetWindowCoordinate() {
        Window window = mActivity.getWindow();
        if (window == null) {
            return;
        }
        mWindowX.setGravity(Gravity.LEFT | Gravity.TOP);
        Rect safeInsetRect = BaseDraggable.getSafeInsetRect(window);
        if (safeInsetRect != null) {
            mWindowX.setXOffset(safeInsetRect.left);
        } else {
            mWindowX.setXOffset(0);
        }
        mWindowX.setYOffset((int) (DisplayUtil.getScreenHeight(getContext()) * 0.4));
    }

    public static class Builder {

        private final Activity mActivity;
        private View mMenuLayout;
        private RedPacketInfo mRedPacketInfo;
        private String mIconUrl;

        private MenuConfig mMenuConfig;

        private FloatWindow.DragBottom2DeleteCallback mDragBottom2DeleteCallback;

        public Builder(Activity activity) {
            mActivity = activity;
        }

        public Builder setFloatIconUrl(String url) {
            mIconUrl = url;
            return this;
        }

        public Builder setMenuLayout(View view) {
            mMenuLayout = view;
            return this;
        }

        public Builder setRedPacketInfo(RedPacketInfo redPacketInfo) {
            mRedPacketInfo = redPacketInfo;
            return this;
        }

        public Builder setOnDragBottom2DeleteCallback(FloatWindow.DragBottom2DeleteCallback callback) {
            mDragBottom2DeleteCallback = callback;
            return this;
        }

        public SqBaseFloatView build() {
            return new SqBaseFloatView(mActivity, mIconUrl, mMenuLayout, mRedPacketInfo, mDragBottom2DeleteCallback);
        }
    }

    public int[] getRealLocation() {
        int[] location = new int[2];
        View normalView = findViewById(mNormalViewId);
        if (normalView != null) {
            normalView.getLocationOnScreen(location);
        } else {
            getLocationOnScreen(location);
        }
        return location;
    }

    public int getRealWidth() {
        View normalView = findViewById(mNormalViewId);
        if (normalView != null) {
           return normalView.getWidth();
        }
        return getWidth();
    }

    public int getRealHeight() {
        View normalView = findViewById(mNormalViewId);
        if (normalView != null) {
            return normalView.getHeight();
        }
        return getHeight();
    }

    public interface DismissListener {
        void onDismiss();
    }

    public interface MenuListener {
        void onMenuShow();
    }
}
