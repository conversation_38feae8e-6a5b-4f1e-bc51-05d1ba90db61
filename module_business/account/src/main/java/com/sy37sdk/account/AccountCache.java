package com.sy37sdk.account;

import android.content.Context;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.user.UserInfoManager;
import com.sqwan.common.util.JsonMap;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SpUtils;
import com.sqwan.common.util.ZipString;
import com.sy37sdk.account.entrance.EntranceManager;
import java.util.Objects;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public class AccountCache {

    private static final String TAG = "[AccountCache]";
    private static final String SQ_PREFS = "sq_prefs";

    private static final String USERID = "userid";
    private static final String USERNAME = "username";
    private static final String PASSWORD = "pd";
    private static final String TOKEN = "token";
    private static final String TICKET = "ticket";
    private static String USERALIAS = "useralias";
    private static final String LOGIN_WAY = "login_way";

    private static final String USER_INFO = "user_info";

    //自动注册
    private static final String AUTONAME = "auto_name";
    private static final String AUTOPASSWORD = "auto_pwd";
    private static final String AUTOSTATE = "auto_state";
    private static final String AUTOISSAVE = "auto_Issave";

    private static final String GET_VERIFY_CODE_LAST_TIME = "time_last_get_verify_code"; //最后点击请求短信验证码时间

    //区分是登录类型还是注册类型
    private static final String ACTIONTYPE = "action_type";

    //是否上报按压数据
    public static final String TOUCH_REPORT = "touchReport";

    //是否已经返回ticket标志位
    private static Boolean TICKET_SUCCESS = false;


    /**
     * 是否登录了
     * @param context
     * @param loginCode
     */
    public static final String LOGINED = "logined";
    public static void setVerifyCodeLastTime(Context context, long time) {
        SpUtils.get(context, SQ_PREFS).put(GET_VERIFY_CODE_LAST_TIME, time);
    }

    public static long getVerifyCodeLastTime(Context context) {
        return SpUtils.get(context, SQ_PREFS).getLong(GET_VERIFY_CODE_LAST_TIME, 0L);
    }

    public static void setUserid(Context context, String userid) {
        SpUtils.get(context, SQ_PREFS).put(USERID, userid);
    }

    public static String getUserid(Context context) {
        String uid = SpUtils.get(context, SQ_PREFS).getString(USERID, "");
        com.sqwan.common.user.UserInfo user = UserInfoManager.getInstance().getCurrentUser();
        if (user != null) {
            checkAndReport("uid", uid, user.getUid());
        }
        return uid;
    }

    public static void setUsername(Context context, String username) {
        SpUtils.get(context, SQ_PREFS).put(USERNAME, username);
    }

    public static String getUsername(Context context) {
        String uname = SpUtils.get(context, SQ_PREFS).getString(USERNAME, "");
        com.sqwan.common.user.UserInfo user = UserInfoManager.getInstance().getCurrentUser();
        if (user != null) {
            checkAndReport("uname", uname, user.getUname());
        }
        return uname;
    }

    public static void setPassword(Context context, String password) {
        SpUtils.get(context, SQ_PREFS).put(PASSWORD, ZipString.json2ZipString(password));
    }

    public static String getPassword(Context context) {
        String password = SpUtils.get(context, SQ_PREFS).getString(PASSWORD, "");
        // 明文密码
        password = ZipString.zipString2Json(password);
        com.sqwan.common.user.UserInfo user = UserInfoManager.getInstance().getCurrentUser();
        if (user != null) {
            checkAndReport("password", password, com.sqwan.common.user.UserInfo.getPwd(user));
        }
        return password;
    }

    public static void setToken(Context context, String token) {
        SpUtils.get(context, SQ_PREFS).put(TOKEN, token);
    }

    public static String getToken(Context context) {
        String token = SpUtils.get(context, SQ_PREFS).getString(TOKEN, "");
        com.sqwan.common.user.UserInfo user = UserInfoManager.getInstance().getCurrentUser();
        if (user != null) {
            checkAndReport("token", token, user.getToken());
        }
        return token;
    }

    public static void setAutoName(Context context, String token) {
        SpUtils.get(context, SQ_PREFS).put(AUTONAME, token);
    }

    public static String getAutoName(Context context) {
        return SpUtils.get(context, SQ_PREFS).getString(AUTONAME, "");
    }

    public static void setAutoPassword(Context context, String pwd) {
        SpUtils.get(context, SQ_PREFS).put(AUTOPASSWORD, pwd);
    }

    public static String getAutoPassword(Context context) {
        return SpUtils.get(context, SQ_PREFS).getString(AUTOPASSWORD, "");
    }

    public static void setAutoState(Context context, String state) {
        SpUtils.get(context, SQ_PREFS).put(AUTOSTATE, state);
    }

    public static boolean getAutoState(Context context) {
        String autoState = SpUtils.get(context, SQ_PREFS).getString(AUTOSTATE, "0");
        return autoState.equals("1");
    }

    public static void setAutoIssave(Context context, String issave) {
        SpUtils.get(context, SQ_PREFS).put(AUTOISSAVE, issave);
    }

    public static boolean getAutoIssave(Context context) {
        String isSave = SpUtils.get(context, SQ_PREFS).getString(AUTOISSAVE, "0");
        return isSave.equals("1");
    }

    public static void setAccountAlias(Context context, String alias) {
        SpUtils.get(context, SQ_PREFS).put(USERALIAS, alias);
    }

    public static String getAccountAlias(Context context) {
        return SpUtils.get(context, SQ_PREFS).getString(USERALIAS, "");
    }

    public static void setLoginType(Context context, String type) {
        SpUtils.get(context, SQ_PREFS).put(LOGIN_WAY, type);
    }

    public static String getLoginType(Context context) {
        String loginType = SpUtils.get(context, SQ_PREFS).getString(LOGIN_WAY, "sq");
        com.sqwan.common.user.UserInfo user = UserInfoManager.getInstance().getCurrentUser();
        if (user != null) {
            checkAndReport("loginType", loginType, String.valueOf(user.type.code));
        }
        return loginType;
    }

    public static void setLogined(Context context, boolean logined) {
        SpUtils.get(context, SQ_PREFS).put(LOGINED, logined);
    }

    public static void setActionType(Context context, String actionType) {
        SpUtils.get(context, SQ_PREFS).put(ACTIONTYPE, actionType);
    }

    public static String getActionType(Context context) {
        String actionType = SpUtils.get(context, SQ_PREFS).getString(ACTIONTYPE);
        return TextUtils.isEmpty(actionType)?"login":actionType;
    }

    public static void setTicketState(boolean b) {
        TICKET_SUCCESS = b;
    }

    public static boolean getTicketState() {
        return TICKET_SUCCESS;
    }


    public static void setUserInfo(Context context, UserInfo userInfo) {
        if (userInfo == null) {
            setUserid(context, "");
            setPassword(context, "");
            setUsername(context, "");
            setToken(context, "");
            setAccountAlias(context, "");
            setActionType(context, "");
            setLoginType(context, "");
        } else {
            setUserid(context, userInfo.getUid());
            setPassword(context, userInfo.getUpwd());
            setUsername(context, userInfo.getUname());
            setToken(context, userInfo.getToken());
            setAccountAlias(context, userInfo.getAlias());
            setActionType(context, userInfo.getActionType());
            setLoginType(context, userInfo.getLoginType());
        }
        JSONObject userInfoJsonObj = UserInfo.encodeToJson(userInfo);
        SpUtils.get(context, SQ_PREFS).put(USER_INFO, userInfoJsonObj.toString());
    }

    public static UserInfo getUserInfo(Context context) {
        //3.7.7版本开始存储userinfo的json信息
        String userInfoJsonStr = SpUtils.get(context, SQ_PREFS).getString(USER_INFO, "");
        UserInfo userInfo = null;
        if (TextUtils.isEmpty(userInfoJsonStr)) {
            //兼容3.7.7以前没有存储userinfo的json信息的时候，则直接获取uname、alias、upwd
            String alias = AccountCache.getAccountAlias(context);
            String userName = AccountCache.getUsername(context);
            String pwd = AccountCache.getPassword(context);
            userInfo = new UserInfo();
            userInfo.setUname(userName);
            userInfo.setAlias(alias);
            userInfo.setUpwd(ZipString.json2ZipString(pwd));
            return userInfo;
        } else {
            try {
                userInfo = UserInfo.decodeFromJson(new JSONObject(userInfoJsonStr));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 如果是不允许使用账号密码登录，并且最后一个登录的方式就是用账号密码登录，就返回空数据
        if (userInfo != null && TextUtils.isEmpty(userInfo.getMobile()) && !EntranceManager.getInstance().isAccountLoginEntrance()) {
            return null;
        }
        return userInfo;
    }

    public static void setTouch(Context context, boolean touchReport) {
        SpUtils.get(context, SQ_PREFS).put(TOUCH_REPORT, touchReport);
    }

    public static boolean getTouch(Context context) {
        return SpUtils.get(context, SQ_PREFS).getBoolean(TOUCH_REPORT, false);
    }

    /**
     * 埋点检查
     */
    private static void checkAndReport(String desc, String oldValue, String newValue) {
        // TODO: 2024/10/9 后续版本移除
        if (Objects.equals(oldValue, newValue)) {
            return;
        }
        SQLog.e(TAG + desc + " 值不同: old=" + oldValue + ", new=" + newValue);
        JsonMap map = new JsonMap();
        map.put("desc", desc);
        map.put("old", oldValue);
        map.put("new", newValue);
        Context context = SQContextWrapper.getApplicationContext();
        if (context != null) {
            UserInfo oldUser = getUserInfo(context);
            if (oldUser != null) {
                map.put("old_user", oldUser.toString());
            }
        }
        com.sqwan.common.user.UserInfo newUser = UserInfoManager.getInstance().getCurrentUser();
        if (newUser != null) {
            map.put("new_user", newUser.toString());
        }
        BuglessAction.reportCatchException(new IllegalArgumentException(), "用户参数重构前后不一致", map.toString(),
            BuglessAction.COMMON_ERROR);
    }
}
