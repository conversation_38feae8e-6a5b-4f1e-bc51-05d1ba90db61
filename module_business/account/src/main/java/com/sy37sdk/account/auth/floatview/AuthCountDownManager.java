package com.sy37sdk.account.auth.floatview;

import android.content.Context;
import com.sq.tool.network.SqHttpCallback.SimpleSqHttpCallback;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.account.IAuthResultListener;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.util.ActivityLifeCycleUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.task.Task;
import com.sy37sdk.account.AccountRequestManager;
import com.sy37sdk.account.auth.AuthConfigCache;
import com.sy37sdk.account.auth.AuthDialog;
import com.sy37sdk.account.auth.AuthManager;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-01 10:54
 */
public class AuthCountDownManager {

    public boolean isReportOnBackground = false;
    String TAG = this.getClass().getSimpleName();
    private static final AuthCountDownManager ourInstance = new AuthCountDownManager();

    public static AuthCountDownManager getInstance() {
        return ourInstance;
    }

    private Context mContext;

    private AuthCountDownView authCountDownView;
    private AccountRequestManager requestManager;
    private int repeatDuration = 1000;
    private Task task = Task.create();
    private Task reportTask = Task.create();
    private int limitedDuration;

    public void init(Context context){
        this.mContext = context;
        requestManager = new AccountRequestManager(mContext);
    }
    private AuthCountDownManager() {
    }
    interface CountDownCallback{
        void onResult(boolean isFinish,String time);
    }
    private String formatTime(int _time){
        int time = _time/1000;
        int hh = time / 60 / 60 % 60;
        int mm = time / 60 % 60;
        int ss = time % 60;
        String result  =  (hh==0?"":hh+"小时") + (mm==0?"":mm+"分钟") + ss + "秒";
        LogUtil.d(TAG,"formatTime "+ result);
        mm = time/60;
        ss = time%60;
        if ((mm<1)) {
            mm=1;
            if (ss==0) {
                mm=0;
            }
        }

        result = mm+"分钟";
        LogUtil.d(TAG,"formatTime "+ result);
        return result;
    }
    private void handleAuth(final Context context){
        AuthManager.getInstance(context).showAuthDialog(null);
    }
    private void handleAuthCdFinish(final Context context){
        AuthManager.getInstance(context).showAuthDialog(null);
    }
    public void startTask(final Context context, final boolean toStart, final int _limitedDuration){
        Task.post(new Runnable() {
            @Override
            public void run() {
                startTask(context, toStart, _limitedDuration, new CountDownCallback() {
                    @Override
                    public void onResult(boolean isFinish, String time) {
                        if (authCountDownView!=null) {
                            authCountDownView.changeTime(time);
                        }
                        if (isFinish) {
                            handleAuthCdFinish(context);
                        }else{

                        }
                    }
                });
            }
        });


    }
    private void startTask(final Context context, boolean toStart, final int _limitedDuration, final CountDownCallback countDownCallback){
        task.stop();
        if (authCountDownView == null) {
            authCountDownView = new AuthCountDownView(context);
            authCountDownView.initView();
            authCountDownView.authClickCallback = new AuthCountDownView.AuthClickCallback() {
                @Override
                public void onClick() {
                    handleAuth(context);
                }
            };
            authCountDownView.show();
        }
        limitedDuration = _limitedDuration;
        if (!toStart) {
            String result = formatTime(limitedDuration);
            countDownCallback.onResult(false,result);
        }else{
            task.repeat(repeatDuration, new Task.TaskFunc() {
                @Override
                public Task.Result exec() {
                    if (!canReport()) {
                        LogUtil.e(TAG,"cd canReport false");
                        return Task.Result.Next;
                    }
                    String result = formatTime(limitedDuration);
                    if (limitedDuration<=0) {
                        if (countDownCallback!=null) {
                            countDownCallback.onResult(true,result);
                        }
                        return Task.Result.Stop;
                    }
                    if (countDownCallback!=null) {
                        limitedDuration-=repeatDuration;
                        countDownCallback.onResult(false,result);
                    }
                    return Task.Result.Next;
                }
            });
        }


    }
    public void release(){
        AuthCountDownManager.getInstance().stopAuthCdTask();
        AuthCountDownManager.getInstance().stopReportAuth();
    }
    public void stopAuthCdTask(){
        Task.post(new Runnable() {
            @Override
            public void run() {
                limitedDuration=0;
                task.stop();
                if (authCountDownView!=null) {
                    authCountDownView.release();
                    authCountDownView=null;
                }
            }
        });

    }
    /**
     * 设置实名状态，回传给研发设置的回调接口
     * @param isSuccess
     */
    public void setAuthResult(final Context context, final boolean isSuccess, boolean focus, final AuthDialog authDialog) {
        LogUtil.d(TAG, "setAuthResult isSuccess " + isSuccess + " focus " + focus);
        boolean hasSubmitRole = ModHelper.get(IAccountMod.class).hasSubmitRole();
        if (!isSuccess && focus && hasSubmitRole) {
            final IAuthResultListener authResultListener = ModHelper.get(IAccountMod.class).getAuthResultListener();
            if (authResultListener != null) {
                Task.post(new Runnable() {
                    @Override
                    public void run() {
                        LogUtil.e(TAG, "onAuthResult false");
                        if (authDialog!=null) {
                            authDialog.setFocusAuthCdFinish(true);
                        }
                        ModHelper.get(IAccountMod.class).setSubmitRole(false);
                        AuthManager.getInstance(context).reset();
                        AuthCountDownManager.getInstance().stopAuthCdTask();
                        AuthCountDownManager.getInstance().stopReportAuth();
                        LiveshowEngine.getInstance().leaveLiveshowRoom(null,null);
                        LiveRadioEngine.getInstance().leaveLiveRadioRoom(null, null);

                        if (authResultListener!=null) {
                            LogUtil.e(TAG, "onAuthResult false callback");
                            authResultListener.onAuthResult(false);
                        }


                    }
                });

            }
        }
        if (isSuccess) {
            AuthCountDownManager.getInstance().stopAuthCdTask();
            if (authDialog.isFocusAuthCdFinish()) {
                final IAuthResultListener authResultListener = ModHelper.get(IAccountMod.class).getAuthResultListener();
                if (authResultListener != null) {
                    Task.post(new Runnable() {
                        @Override
                        public void run() {
                            authResultListener.onAuthResult(true);
                            LogUtil.e(TAG,"onAuthResult true");
                        }
                    });

                }
            }

        }



    }
    /**
     * 显示游客倒计时
     */
    public void showAuthLimiteTime(Context context){
        boolean toStart = ModHelper.get(IAccountMod.class).hasSubmitRole();
        LogUtil.i(TAG,"showAuthLimiteTime toStart " + toStart);
        if (!AuthConfigCache.getIsAuth()) {
            int authLimiteTime = AuthConfigCache.getAuthLimitTime();
            if (authLimiteTime>0) {
                AuthCountDownManager.getInstance().startTask(context,toStart,authLimiteTime);
            }else{
                AuthCountDownManager.getInstance().stopAuthCdTask();
            }
        }

    }
    public boolean canReport(){
        boolean canReport = isReportOnBackground || (!isReportOnBackground && ActivityLifeCycleUtils.getInstance().isForeground());
        LogUtil.i(TAG,"canReport:"+canReport);
        return canReport;
    }
    public void startReportAuth(){
        LogUtil.i(TAG,"startReportAuth");
        if (AuthConfigCache.isNeddReportOnline()) {
            int intervalMs = AuthConfigCache.getInterval()*60*1000;
            reportTask.repeat(0, intervalMs, new Task.TaskFunc() {
                @Override
                public Task.Result exec() {
                    if (canReport()) {
                        reportAuth();
                    }else{
                        LogUtil.e(TAG,"reportAuth canReport false");
                    }
                    return null;
                }
            });
        }

    }
    public void stopReportAuth(){
        LogUtil.i(TAG,"stopReportAuth");
        reportTask.stop();
    }

    private void reportAuth() {
        requestManager.reportOnlineDuration(new SimpleSqHttpCallback<JSONObject>() {
            @Override
            public void onSuccess(JSONObject dataJson) {
                boolean needStop = dataJson.optInt("need_stop") == 1;
                if (needStop) {
                    stopReportAuth();
                }
            }
        });
    }

    public void updateRemainingTime(int limitedDuration){
        if (task.isRunning()) {
            this.limitedDuration = limitedDuration;
        }
    }
}
