package com.sy37sdk.account.face.data;

import org.json.JSONObject;

public class FaceVerifyData {
    //脱敏身份证
    private String idCardName;
    //脱敏姓名
    private String name;
    //是否需要人脸认证
    private boolean needVerify;
    //提示信息
    private String verifyTip;

    public String getIdCardName() {
        return idCardName;
    }

    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isNeedVerify() {
        return needVerify;
    }

    public void setNeedVerify(boolean needVerify) {
        this.needVerify = needVerify;
    }

    public String getVerifyTip() {
        return verifyTip;
    }

    public void setVerifyTip(String verifyTip) {
        this.verifyTip = verifyTip;
    }

    @Override
    public String toString() {
        return "FaceVerifyData{" +
                "idCardName='" + idCardName + '\'' +
                ", name='" + name + '\'' +
                ", needVerify=" + needVerify +
                ", verifyTip='" + verifyTip + '\'' +
                '}';
    }

    public static FaceVerifyData jsonToObject(String json){
        FaceVerifyData faceVerifyData=new FaceVerifyData();
        try {
            JSONObject jsonObject = new JSONObject(json);
            int is_need_verify = jsonObject.optInt("is_need_verify");
            faceVerifyData.setNeedVerify(is_need_verify==1);
            faceVerifyData.setName(jsonObject.optString("name"));
            faceVerifyData.setIdCardName(jsonObject.optString("id_card_num"));
            faceVerifyData.setVerifyTip(jsonObject.optString("verify_tip"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return faceVerifyData;
    }
}
