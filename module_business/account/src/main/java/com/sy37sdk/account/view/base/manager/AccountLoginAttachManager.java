package com.sy37sdk.account.view.base.manager;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;

import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.NavigationUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sy37sdk.account.age.AppropriateAge;
import com.sy37sdk.account.age.AppropriateAgeCacheHelper;
import com.sy37sdk.account.age.AppropriateAgeManager;
import com.sy37sdk.account.view.base.view.AccountLoginAttachView;

import java.util.List;

public class AccountLoginAttachManager {

    private static final String TAG = "AccountLoginAttach";

    private Context mContext;

    private AccountLoginAttachView attachView;

    private ImageView ivAgeAppropriate;

    private WindowManager mWindowManager;

    private int orientation;

    private int navigationBarHeight;

    private int statusBarHeight;

    private int authPageWidth, authPageHeight;

    private int screenWidth, screenHeight;

    private int ageViewWidth, ageViewHeight;

    private int offsetPortraitX, offsetPortraitY;

    private int offsetLandscapeX, offsetLandscapeY;

    private int horizontalMargin, verticalMargin;

    private boolean isShown;

    private boolean isFinish;

    public AccountLoginAttachManager(Context context) {
        this.mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        attachView = new AccountLoginAttachView(mContext);
        init();
    }

    private void init() {
        Configuration configuration = mContext.getResources().getConfiguration();
        orientation = configuration.orientation;
        navigationBarHeight = NavigationUtils.getNavigationBarHeight(mContext);
        statusBarHeight = StatusBarUtil.getStatusBarHeight(mContext);

        authPageHeight = DisplayUtil.dip2px(mContext, 230);
        authPageWidth = DisplayUtil.dip2px(mContext, 320);

        screenWidth = DisplayUtil.getScreenWidth(mContext);
        screenHeight = DisplayUtil.getScreenHeight(mContext);
        //icon宽去屏幕宽高中最小值得十分之一
        ageViewWidth = Math.min(screenWidth, screenHeight) / 10;
        //icon尺寸比例为48*62
        ageViewHeight = 62 * ageViewWidth / 48;

        offsetPortraitX = (screenWidth - authPageWidth) / 2;
        offsetPortraitY = (screenHeight - authPageHeight) / 2;
        offsetLandscapeX = (screenWidth - authPageWidth) / 2;
        offsetLandscapeY = (screenHeight - authPageHeight) / 2;

        horizontalMargin = DisplayUtil.dip2px(mContext, 20f);
        verticalMargin = DisplayUtil.dip2px(mContext, 20f);
        LogUtil.i(TAG, "show: screenWidth=" + screenWidth + " screenHeight=" + screenHeight);
        LogUtil.i(TAG, "show: offsetPortraitX=" + offsetPortraitX + " offsetPortraitY=" + offsetPortraitY + " offsetLandscapeX=" + offsetLandscapeX + " offsetLandscapeY=" + offsetLandscapeY);
        LogUtil.i(TAG, "show: authPageWidth=" + authPageWidth + " authPageHeight=" + authPageHeight);
        LogUtil.i(TAG, "show: statusBarHeight=" + statusBarHeight + " navigationBarHeight=" + navigationBarHeight);
        LogUtil.i(TAG, "show: horizontalMargin=" + horizontalMargin + " verticalMargin=" + verticalMargin);
    }

    /**
     * 添加view
     */
    public void attachView() {
        try {
            if (isFinish) {
                return;
            }
            isShown = true;
            attachCustomerView();
            attachAgeAppropriateView();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 添加客服按钮、版本信息
     */
    private void attachCustomerView() {
        WindowManager.LayoutParams attachViewLp = new WindowManager.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.TYPE_APPLICATION_PANEL,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.RGBA_8888);
        attachViewLp.gravity = Gravity.END | Gravity.BOTTOM;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attachViewLp.x = -(offsetPortraitX - (navigationBarHeight + DisplayUtil.dip2px(mContext, 10f)));
            attachViewLp.y = -(offsetPortraitY - DisplayUtil.dip2px(mContext, 6f));
        } else {
            attachViewLp.x = -(offsetPortraitX - DisplayUtil.dip2px(mContext, 12f));
            attachViewLp.y = -(offsetPortraitY - (navigationBarHeight + DisplayUtil.dip2px(mContext, 10f)));
        }
        mWindowManager.addView(attachView, attachViewLp);
    }


    /**
     * 添加适龄提醒
     */
    private void attachAgeAppropriateView() {
        AppropriateAge appropriateAge = AppropriateAgeCacheHelper.getAppropriateAge(mContext);
        if (appropriateAge == null || !appropriateAge.isStatus()) {
            return;
        }
        List<String> timing = appropriateAge.getTiming();
        if (timing == null || !timing.contains(AppropriateAge.TIMING_LOGIN)) {
            return;
        }
        ivAgeAppropriate = new ImageView(mContext);
        WindowManager.LayoutParams ageViewLp = new WindowManager.LayoutParams(ageViewWidth,
                ageViewHeight, WindowManager.LayoutParams.TYPE_APPLICATION_PANEL,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.RGBA_8888);
        ageViewLp.gravity = Gravity.END | Gravity.BOTTOM;
        int location = appropriateAge.getLocation();
        switch (location) {
            //右上
            case AppropriateAge.LOCATE_RIGHT_TOP:
                ageViewLp.gravity = Gravity.END | Gravity.TOP;
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    ageViewLp.x = -(offsetLandscapeX - (navigationBarHeight + DisplayUtil.dip2px(mContext, 10)));
                    ageViewLp.y = -(offsetLandscapeY - verticalMargin);
                } else {
                    ageViewLp.x = -(offsetPortraitX - horizontalMargin);
                    ageViewLp.y = -(offsetPortraitY - verticalMargin);
                }
                break;
            //左下
            case AppropriateAge.LOCATE_LEFT_BOTTOM:
                ageViewLp.gravity = Gravity.START | Gravity.BOTTOM;
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    ageViewLp.x = -(offsetLandscapeX - (statusBarHeight + DisplayUtil.dip2px(mContext, 10)));
                    ageViewLp.y = -(offsetLandscapeY - (statusBarHeight + DisplayUtil.dip2px(mContext, 10)));
                } else {
                    ageViewLp.x = -(offsetPortraitX - horizontalMargin);
                    ageViewLp.y = -(offsetPortraitY - (navigationBarHeight + DisplayUtil.dip2px(mContext, 10)));
                }
                break;
            //右下
            case AppropriateAge.LOCATE_RIGHT_BOTTOM:
                ageViewLp.gravity = Gravity.END | Gravity.BOTTOM;
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    ageViewLp.x = -(offsetLandscapeX - (navigationBarHeight + DisplayUtil.dip2px(mContext, 18)));
                    ageViewLp.y = -(offsetLandscapeY - (verticalMargin + DisplayUtil.dip2px(mContext, 55f)));
                } else {
                    ageViewLp.x = -(offsetPortraitX - horizontalMargin);
                    ageViewLp.y = -(offsetPortraitY - (navigationBarHeight + DisplayUtil.dip2px(mContext, 80)));
                }
                break;
            //左上
            default:
                ageViewLp.gravity = Gravity.START | Gravity.TOP;
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    ageViewLp.x = -(offsetLandscapeX - horizontalMargin);
                    ageViewLp.y = -(offsetLandscapeY - (verticalMargin));
                } else {
                    ageViewLp.x = -(offsetPortraitX - horizontalMargin);
                    ageViewLp.y = -(offsetPortraitY);
                }
                break;
        }
        ivAgeAppropriate.setScaleType(ImageView.ScaleType.CENTER_CROP);

        LogUtil.i(TAG, "show: x= " + ageViewLp.x + "  y= " + ageViewLp.y);
        AsyncImageLoader loader = new AsyncImageLoader(mContext);
        loader.loadDrawable(appropriateAge.getIcon(), ivAgeAppropriate, new AsyncImageLoader.ImageCallback() {
            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                imageView.setImageBitmap(imageDrawable);
                LogUtil.i("适龄图片加载成功");
            }
        });
        ivAgeAppropriate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppropriateAgeManager.getInstance().showAppropriateAgeDialog(mContext);
            }
        });
        mWindowManager.addView(ivAgeAppropriate, ageViewLp);
    }

    /**
     * 隐藏
     */
    public void hide() {
        if (isFinish) {
            return;
        }
        isShown = false;
        if (ivAgeAppropriate != null) {
            ivAgeAppropriate.setVisibility(View.GONE);
        }
        if (attachView != null) {
            attachView.setVisibility(View.GONE);
        }
    }

    public void resume() {
        if (isFinish) {
            return;
        }
        isShown = true;
        if (ivAgeAppropriate != null) {
            ivAgeAppropriate.setVisibility(View.VISIBLE);
        }
        if (attachView != null) {
            attachView.setVisibility(View.VISIBLE);
        }
    }

    public boolean isShown() {
        return isShown;
    }

    /**
     * 移除view
     */
    public void detachView() {
        try{
            isShown = false;
            if (mWindowManager == null) {
                return;
            }
            if (ivAgeAppropriate != null) {
                mWindowManager.removeViewImmediate(ivAgeAppropriate);
            }
            if (attachView != null) {
                mWindowManager.removeViewImmediate(attachView);
            }
            mWindowManager = null;
            isFinish = true;
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public boolean isFinish() {
        return isFinish;
    }


}
