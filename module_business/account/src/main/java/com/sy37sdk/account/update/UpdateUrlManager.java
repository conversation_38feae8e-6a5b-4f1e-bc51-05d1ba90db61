package com.sy37sdk.account.update;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV1;
import com.sy37sdk.account.UrlConstant;
import java.util.ArrayList;
import java.util.HashMap;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class UpdateUrlManager {

    public static HashMap<String, String> apiMap = new HashMap<>();

    public ArrayList<String> apiUrls = new ArrayList<>();

    public void reqUrlUpdateManager(SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.UPDATE_URL)
            .signV3()
            .addParamsTransformer(new CommonParamsV1())
            .get(callback);
    }

    /**
     * 接口下发配置的url map
     */
    public HashMap<String, String> parse(JSONObject jsonObject) {
        JSONArray array = jsonObject.optJSONArray("api_infos");
        if (array == null) {
            return null;
        }
        for (int i = 0; i < array.length(); i++) {
            JSONObject apiObject = array.optJSONObject(i);
            if (apiObject == null) {
                continue;
            }
            try {
                String apiKey = apiObject.getString("api_key");
                String apiUrl = apiObject.getString("api_info");
                if ("x_secure_key".equals(apiKey)) {
                    continue;
                }
                apiMap.put(apiKey, apiUrl);
                apiUrls.add(apiUrl);
            } catch (JSONException e) {
                /* no-op */
            }
        }

        return apiMap;
    }

    public HashMap<String, String> getApiUrlMap() {
        return apiMap;
    }

    public ArrayList<String> getApiUrls() {
        return apiUrls;
    }
}
