package com.sy37sdk.account.floatview.request.websocket.factory;

import com.sqwan.common.mod.CommonConfigs;

public class FloatWindowRedDotMsgPidGidFactory extends FloatWindowRedDotMsgBaseFactory {

    public String getTargetAppid() {
        String pid = CommonConfigs.getInstance().getSqAppConfig().getPartner();
        String gid = CommonConfigs.getInstance().getSqAppConfig().getGameid();
        return String.format("%s_%s", pid, gid);
    }
}