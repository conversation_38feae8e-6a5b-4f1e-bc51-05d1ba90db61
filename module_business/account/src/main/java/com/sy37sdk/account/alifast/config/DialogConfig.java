package com.sy37sdk.account.alifast.config;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.mobile.auth.gatewayauth.AuthRegisterViewConfig;
import com.mobile.auth.gatewayauth.AuthRegisterXmlConfig;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.CustomInterface;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.ui.AbstractPnsViewDelegate;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.msdk.config.ConfigManager;
import com.sqwan.msdk.config.MultiConfigManager;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sy37sdk.account.uagree.UAgreeCacheHelper;
import com.sy37sdk.account.util.AccountUtil;
import com.sy37sdk.account.view.LoginSkinHelper;

public class DialogConfig extends BaseUIConfig {
    private static final String TAG = "DialogConfig";

    /**
     * 应用包名
     */

    public DialogConfig(Activity activity, PhoneNumberAuthHelper authHelper) {
        super(activity, authHelper);
    }

    @Override
    public void configAuthPage() {
        mAuthHelper.removeAuthRegisterXmlConfig();
        mAuthHelper.removeAuthRegisterViewConfig();
        int authPageOrientation = AppUtils.getOrientation();
        LogUtil.i("DialogConfig  " + authPageOrientation);
        if (authPageOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            authPageOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE;
        } else {
            authPageOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;
        }
        if (Build.VERSION.SDK_INT == 26) {
            authPageOrientation = ActivityInfo.SCREEN_ORIENTATION_BEHIND;
        }
        updateScreenSize(authPageOrientation);
        int unit = 5;
        mAuthHelper.addAuthRegistViewConfig("view_group", new AuthRegisterViewConfig.Builder()
            .setView(initViewGroup(unit + 95))
            .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY)
            .build());
        mAuthHelper.addAuthRegisterXmlConfig(new AuthRegisterXmlConfig.Builder()
                .setLayout(SqResUtils.getLayoutId(mContext, "sysq_custom_port_dialog_action_bar"), new AbstractPnsViewDelegate() {
                    @Override
                    public void onViewCreated(View view) {
                        LogUtil.i(TAG, "addAuthRegisterXmlConfig onViewCreated: ");
                        ImageView btnClose = (ImageView) findViewById(SqResUtils.getId(mContext, "btn_close"));
                        btnClose.setImageResource(LoginSkinHelper.getCloseIconResId(mContext));
                        findViewById(SqResUtils.getId(mContext, "view_close")).setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                mAuthHelper.quitLoginPage();
                                if (listener != null) {
                                    listener.clickClose();
                                }
                            }
                        });
                        ImageView btnBack = (ImageView) findViewById(SqResUtils.getId(mContext, "btn_back"));
                        btnBack.setImageResource(LoginSkinHelper.getBackIconResId(mContext));
                        if(!AccountUtil.checkCanFastBack()){
                            btnBack.setVisibility(View.INVISIBLE);
                        }
                        findViewById(SqResUtils.getId(mContext, "view_back")).setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if(!AccountUtil.checkCanFastBack()){
                                    return;
                                }
                                mAuthHelper.quitLoginPage();
                                if (listener != null) {
                                    listener.clickBack();
                                }
                            }
                        });
                        TextView tvTitle = (TextView) findViewById(SqResUtils.getId(mContext, "tv_title"));
                        ImageView ivLogo = (ImageView) findViewById(SqResUtils.getId(mContext, "iv_logo"));
                        //是否是非37
                        boolean scut3 = MultiSdkManager.getInstance().isScut3();
                        if (scut3) {
                            //不是37版本则显示标题
                            ivLogo.setVisibility(View.GONE);
                            tvTitle.setVisibility(View.VISIBLE);
                        } else {
                            //是37
                            if (ConfigManager.getInstance(getContext()).isSplashSDK() || ConfigManager.getInstance(getContext()).isLessFunction()) {
                                //37简版显示标题
                                ivLogo.setVisibility(View.GONE);
                                tvTitle.setVisibility(View.VISIBLE);
                            } else {
                                //37正版显示logo
                                ivLogo.setImageResource(SqResUtils.getDrawableId(mContext, MultiConfigManager.getInstance().isSqUnion() ? "sysq_ic_logo_union" : "sysq_ic_logo"));
                                ivLogo.setVisibility(View.VISIBLE);
                                tvTitle.setVisibility(View.GONE);
                            }
                        }
                    }
                })
                .build());
        mAuthHelper.setAuthUIConfig(new AuthUIConfig.Builder()
                .setAppPrivacyOne("《用户协议》", UAgreeCacheHelper.getUrlProtocol(mContext) + "&isAgree=true")
                .setAppPrivacyTwo("《隐私政策》", UAgreeCacheHelper.getUrlPolicy(mContext) + "&isAgree=true")
                .setAppPrivacyColor(Color.GRAY, SqResUtils.getColorByName(mContext, "sysq_dialog_login_text_accent"))
                .setPrivacyConectTexts(new String[]{"及"})
                .setPrivacyTextSizeDp(12)
                //设置运营商协议位置
                .setPrivacyOperatorIndex(2)
                .setPrivacyState(false)
                .setPrivacyOffsetY_B(10)
                .setNavHidden(true)
                .setDialogBottom(false)
                /**
                 * 通过action启动{@link com.sqwan.common.web.UserProtocolWebActivity}
                 */
                .setProtocolAction(AppUtils.getPackageName(mContext) + ".protocolWeb")
                .setLogoHidden(true)
                .setSloganHidden(true)
                .setAuthPageActIn("in_activity", "out_activity")
                .setAuthPageActOut("in_activity", "out_activity")
                .setNumFieldOffsetY(12)
                .setNumberSizeDp(17)
                .setNumberColor(LoginSkinHelper.getFastLoginPrimaryTextColor(mContext))
                .setLogBtnWidth(280)
                .setLogBtnHeight(38)
                .setLogBtnMarginLeftAndRight(20)
                .setLogBtnTextSizeDp(17)
                    .setNavReturnImgDrawable(mContext.getDrawable(LoginSkinHelper.getCloseIconResId(mContext)))
//                .setNavReturnImgDrawable()
                .setLogBtnText("本机号码一键登录")
                .setLogBtnTextColor(LoginSkinHelper.getFastLoginBtnTextColor(mContext))
                .setLogBtnBackgroundPath(LoginSkinHelper.getFastLoginBtnBackgroundResString(mContext))
                .setLogBtnOffsetY(unit + 45)
                .setPageBackgroundPath(LoginSkinHelper.getPhoneLoginBackgroundResString(mContext))
                .setVendorPrivacyPrefix("《")
                .setVendorPrivacySuffix("》")
                .setCheckboxHidden(false)
                .setCheckedImgDrawable(mContext.getResources().getDrawable(SqResUtils.getDrawableId(mContext, "sysq_dialog_login_privacy_check_box")))
                .setDialogWidth(320)
                .setDialogHeight(230)
                .setScreenOrientation(authPageOrientation)
                .setLogBtnToastHidden(true)
                .create());
    }

}
