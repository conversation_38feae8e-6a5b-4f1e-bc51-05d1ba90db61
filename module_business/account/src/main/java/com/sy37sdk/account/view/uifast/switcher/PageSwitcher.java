package com.sy37sdk.account.view.uifast.switcher;

import android.os.Bundle;
import android.support.v4.view.ViewPager;

/**
 * 账号页面切换管理器
 */
public class PageSwitcher implements IPageSwitcher {

    private int mCurPage = AccountPage.ACCOUNT_HISTORY_PAGE;
    private ViewPager mViewPager;

    public PageSwitcher(ViewPager viewPager) {
        mViewPager = viewPager;
    }

    @Override
    public void onSwitch(int page) {
        onSwitch(page, null);
    }

    public void onSwitch(int page, Bundle bundle) {
        if (pageScrollListener != null) {
            pageScrollListener.scroll(mCurPage, page, bundle);
        }
        mCurPage = page;
        mViewPager.setCurrentItem(page);
    }

    public interface IPageScrollListener {
        void scroll(int fromIndex, int toIndex, Bundle bundle);
    }

    private IPageScrollListener pageScrollListener;

    public void setPageScrollListener(IPageScrollListener pageScrollListener) {
        this.pageScrollListener = pageScrollListener;
    }
}
