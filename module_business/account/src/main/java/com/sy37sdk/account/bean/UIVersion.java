package com.sy37sdk.account.bean;

import android.text.TextUtils;

import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.config.MultiSdkManager;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/3/5
 */
public class UIVersion {


    /**
     * 一键注册关闭
     */
    public static final int FREGISTER_CLOSE = 0;

    /**
     * 一键注册旧版
     */
    public static final int FREGISTER_OLD = 1;

    /**
     * 一键注册新版
     */
    public static final int FREGISTER_NEW = 2;



    public static final int UI_VERSSION_1 = 1;
    public static final int UI_VERSSION_2 = 2;
    public static final int UI_VERSSION_3 = 3;

    /**
     * 默认使用新版本
     */
    private int ui = UI_VERSSION_2;


    /**
     * 默认使用新版版
     */
    private int fregister = FREGISTER_NEW;

    public int getUi() {
        // 如果是非 37的 SDk， 则默认使用版本2的UI
        if(!TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3())) {
            return UI_VERSSION_2;
        } else {
            return ui;
        }
    }

    public int getFregister() {
        // 如果是非 37的 SDk， 关闭一键注册
        if(!TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3())) {
            return FREGISTER_CLOSE;
        } else {
            return fregister;
        }
    }

    public static UIVersion fromJson(String data) {
        UIVersion version = new UIVersion();
        try {
            JSONObject jsonObject = new JSONObject(data);
            version.ui = jsonObject.optInt("ui", UI_VERSSION_2);
            version.fregister = jsonObject.optInt("fregister", FREGISTER_NEW);
        } catch (JSONException e) {
            LogUtil.e("ui版本控制数据解析异常！");
            e.printStackTrace();
        }
        return version;
    }

    @Override
    public String toString() {
        return "【UIVersion】-->  ui : " + ui  + ", fregister : " + fregister;
    }

}
