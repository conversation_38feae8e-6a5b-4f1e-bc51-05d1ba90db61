package com.sy37sdk.account.util;

import android.text.TextUtils;

public class AccountLoginType {

    //登录的账号类型
    public static class LoginType {

        //账号密码
        public static final String ACCOUNT_TYPE_ACCOUNT = "1";
        //手机
        public static final String ACCOUNT_TYPE_PHONE = "2";
        //微信
        public static final String ACCOUNT_TYPE_WECHAT ="3";
    }

    //登录方式
    public static class LoginWay {
        //账号密码
        public static final String LOGIN_ACCOUNT = "1";
        //手机号+验证码
        public static final String LOGIN_PHONE_CODE = "2";
        //本机号一键登录
        public static final String LOGIN_ONE_KEY = "3";
        //自动登录
        public static final String LOGIN_AUTO = "4";
        //手机号+密码
        public static final String LOGIN_PHONE_PWD = "5";
        //快速登录
        public static final String LOGIN_FAST_TOKEN = "6";
        //qq登录
        public static final String LOGIN_QQ = "7";
        //微信登录
        public static final String LOGIN_WECHAT = "8";
        //未知
        public static final String LOGIN_OTHER = "0";
    }


    public static String parseLoginType(String loginWay) {
        if (TextUtils.isEmpty(loginWay)) {
            return "";
        }
        switch (loginWay) {
            case LoginWay.LOGIN_PHONE_CODE:
            case LoginWay.LOGIN_PHONE_PWD:
            case LoginWay.LOGIN_ONE_KEY:
                return LoginType.ACCOUNT_TYPE_PHONE;
            case LoginWay.LOGIN_WECHAT:
                return LoginType.ACCOUNT_TYPE_WECHAT;
            default:
                return LoginType.ACCOUNT_TYPE_ACCOUNT;
        }
    }

    /**
     * 是否是手机登录类型的
     *
     * @param loginWay
     * @return
     */
    public static boolean isPhoneLoginType(String loginWay) {
        return parseLoginType(loginWay).equals(LoginType.ACCOUNT_TYPE_PHONE);
    }

}
