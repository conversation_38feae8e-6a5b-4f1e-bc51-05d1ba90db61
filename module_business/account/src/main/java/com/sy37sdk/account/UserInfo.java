package com.sy37sdk.account;

import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.user.LoginType;
import com.sqwan.common.user.UserInfo.AccountUserInfo;
import com.sqwan.common.user.UserInfo.PhoneUserInfo;
import com.sqwan.common.user.UserInfo.WechatUserInfo;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ZipString;
import com.sy37sdk.account.util.AccountLoginType;
import org.json.JSONObject;

public class UserInfo {

    public static boolean wechatLoginFail = false; //标记登录失败流程，用于微信登录失败拉起微信登录界面

    private String uname; //uname
    private String upwd;  //password

    private String alias; //别名********

    private String token;  //token

    private String refreshToken;  //下次快速登录所需的token

    private String uid;//用户uid

    private String mobile;//新版通过手机号登录的

    private String actionType; //登录的类型，注册 or 登录

    private String loginType;  //登录类型

    private String loginWay; //登录方式

    private String ticket; //验证码生成的ticket

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getLoginWay() {
        return loginWay;
    }

    public void setLoginWay(String loginWay) {
        this.loginWay = loginWay;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getUname() {
        return uname;
    }


    public void setUname(String uname) {
        this.uname = uname;
    }


    public String getUpwd() {
        return ZipString.zipString2Json(upwd);
    }


    public void setUpwd(String upwd) {
        this.upwd = upwd;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    @Override
    public String toString() {
        return "【UserInfo】" + "uname:" + uname + ", uid:" + uid + ", alias:" + alias + ", upwd:" + upwd  + ", token:" + token
                + ", refreshToken:" + refreshToken + ", actionType:" + actionType + ", loginType:" + loginType
                + ", loginWay:" + loginWay+ ", mobile:" + mobile;
    }

    //********新增
    public String getAlias() {
        return this.alias;
    }
    public void setAlias(String alias) {
        this.alias = alias;
    }

    public boolean isPhoneLoginType(){
        return !TextUtils.isEmpty(loginType) && loginType.equals(AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE);
    }

    public static UserInfo decodeFromJson(JSONObject jsonObject) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUname(jsonObject.optString("uname"));
        userInfo.setUpwd(jsonObject.optString("upwd"));
        userInfo.setAlias(jsonObject.optString("alias"));
        userInfo.setToken(jsonObject.optString("token"));
        userInfo.setRefreshToken(jsonObject.optString("refresh_token"));
        userInfo.setMobile(jsonObject.optString("mobile"));
        userInfo.setUid(jsonObject.optString("uid"));
        userInfo.setLoginType(jsonObject.optString("login_type"));
        userInfo.setLoginWay(jsonObject.optString("login_way"));
        userInfo.setActionType(jsonObject.optString("action_type"));
        userInfo.setTicket(jsonObject.optString("ticket"));
        return userInfo;
    }
    private static String getStrWithoutNull(String src){
        if (null==src) {
            return "";
        }else{
            return src;
        }
    }

    public static JSONObject encodeToJson(UserInfo userInfo) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("uname", getStrWithoutNull(userInfo.uname));
            jsonObject.put("upwd", getStrWithoutNull(userInfo.upwd));
            jsonObject.put("alias", getStrWithoutNull(userInfo.alias));
            jsonObject.put("token", getStrWithoutNull(userInfo.token));
            jsonObject.put("refresh_token", getStrWithoutNull(userInfo.refreshToken));
            jsonObject.put("mobile", getStrWithoutNull(userInfo.mobile));
            jsonObject.put("uid", getStrWithoutNull(userInfo.uid));
            jsonObject.put("action_type", getStrWithoutNull(userInfo.actionType));
            jsonObject.put("login_type", getStrWithoutNull(userInfo.loginType));
            jsonObject.put("login_way", getStrWithoutNull(userInfo.loginWay));
            jsonObject.put("ticket", getStrWithoutNull(userInfo.ticket));
        } catch (Exception ex) {
            LogUtil.e("UserInfo", "encode user info to json error!");
            ex.printStackTrace();
        }
        return jsonObject;
    }

    @Nullable
    public com.sqwan.common.user.UserInfo convert() {
        com.sqwan.common.user.UserInfo userInfo;
        String uname = getUname();
        // 明文密码
        String pwd = getUpwd();
        String token = getToken();
        String refreshToken = getRefreshToken();
        String uid = getUid();
        String loginTypeStr = getLoginType();
        LoginType loginType = LoginType.get(Integer.parseInt(loginTypeStr));
        if (loginType == LoginType.ACCOUNT) {
            userInfo = new AccountUserInfo(uid, token, uname, pwd);
        } else if (loginType == LoginType.PHONE) {
            String mobile = getMobile();
            PhoneUserInfo myUserInfo = new PhoneUserInfo(uid, token, mobile);
            myUserInfo.pwd = pwd;
            userInfo = myUserInfo;
        } else if (loginType == LoginType.WECHAT) {
            WechatUserInfo myUserInfo = new WechatUserInfo(uid, token);
            myUserInfo.pwd = pwd;
            userInfo = myUserInfo;
        } else {
            // 异常的类型, 忽略
            BuglessAction.reportCatchException(new IllegalStateException("用户缓存异常"),
                encodeToJson(this).toString(), BuglessAction.COMMON_ERROR);
            return null;
        }

        userInfo.uname = uname;
        userInfo.refreshToken = refreshToken;
        return userInfo;
    }
}
