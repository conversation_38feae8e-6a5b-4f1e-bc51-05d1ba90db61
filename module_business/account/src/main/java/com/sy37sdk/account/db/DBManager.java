package com.sy37sdk.account.db;

import android.database.sqlite.SQLiteDatabase;
import com.sqwan.common.util.SQContextWrapper;

public class DBManager {
    private DataBaseHelper helper;
    private SQLiteDatabase db;

    public SQLiteDatabase getDb(){
        if(helper == null){
            helper = new DataBaseHelper(SQContextWrapper.getApplicationContext());
        }
        if(db == null){
            db = helper.getWritableDatabase();
        }
        return db;
    }
}
