package com.sy37sdk.account.util;

import android.app.Activity;
import android.os.Bundle;
import com.social.sdk.SocialApi;
import com.social.sdk.common.listener.OnAuthListener;
import com.social.sdk.platform.PlatformType;
import com.sqwan.common.mod.account.IBindWxListener;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SDKError;
import com.sy37sdk.account.AccountLogic;

public class SocialAccountUtil {

    /**
     * 微信授权，红包活动用，授权之后请求后端接口获取openid
     */
    public static void socialBindAuthorize(final Activity activity, final IBindWxListener listener) {
        SocialApi.getInstance().authorize(activity, PlatformType.WECHAT, new OnAuthListener() {
            @Override
            public void onSuccess(PlatformType platformType, final Bundle bundle) {
                LogUtil.i("微信授权回调：[bundle]" + bundle.toString());
                String code = bundle.getString("code");
                AccountLogic.getInstance(activity).bindWxOpenId(code, listener);
            }

            @Override
            public void onCancel(PlatformType platformType) {
                if (listener != null) {
                    listener.onFailure(SDKError.ACCOUNT_LOGIN_CANCEL.code, "取消登录");
                }
            }

            @Override
            public void onFailure(PlatformType platformType, String s) {
                if (listener != null) {
                    listener.onFailure(SDKError.ACCOUNT_LOGIN_ERROR.code, s);
                }
            }
        });
    }
}
