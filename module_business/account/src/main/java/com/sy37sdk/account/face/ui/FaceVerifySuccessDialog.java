package com.sy37sdk.account.face.ui;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.KeyEvent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.plugin.standard.RealBaseActivity;
import com.sq.tools.Logger;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.util.CutoutUtil;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.face.FaceActivityHelper;
import com.sy37sdk.account.face.OnFaceVerifyListener;

import notchtools.geek.com.notchtools.NotchTools;

/**
 * 人脸认证成功
 */
public class FaceVerifySuccessDialog extends BaseDialog {
    private View closeView;
    private TextView tvTimeTip;
    private View statusView;
    private CountDownTimer countDownTimer;
    private Context mContext;

    public FaceVerifySuccessDialog(Context context) {
        super(context);
        this.mContext=context;
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(SqResUtils.getLayoutId(mContext,"sysq_face_success_activity"));
        setCancelable(false);
        closeView = findViewById(SqResUtils.getId(mContext, "close"));
        tvTimeTip = findViewById(SqResUtils.getId(mContext, "tv_time_tip"));
        statusView=findViewById(SqResUtils.getId(mContext,"status_view"));
        initStatusView();
        closeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if(onFaceVerifyListener!=null){
                    onFaceVerifyListener.onVerifySuccess();
                }
            }
        });

        countDownTimer = new CountDownTimer(3 * 1000, 1000) {
            @Override
            public void onTick(final long millisUntilFinished) {
                if (mContext instanceof Activity) {
                    if (!((Activity) mContext).isFinishing()) {
                        ((Activity) mContext).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                tvTimeTip.setText(String.format(SqResUtils.getStringByName(getContext(), "sysq_txt_face_verify_success_back"), millisUntilFinished / 1000 + 1));
                            }
                        });

                    }
                }
            }

            /**
             *倒计时结束后调用的
             */
            @Override
            public void onFinish() {
                dismiss();
                if(onFaceVerifyListener!=null){
                    onFaceVerifyListener.onVerifySuccess();
                }
            }

        };
        countDownTimer.start();
    }

    private void initStatusView(){
        int statusHeight = NotchTools.getFullScreenTools().getStatusHeight(getWindow());
        int notchHeight = NotchTools.getFullScreenTools().getNotchHeight(getWindow());
        boolean hasNotchScreen = true;
        hasNotchScreen = CutoutUtil.hasNotchScreen((Activity) mContext);
        Logger.info("statusHeight=" + statusHeight + " notchHeight=" + notchHeight + " 是否刘海屏：" + hasNotchScreen);
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) statusView.getLayoutParams();
        //如果是刘海屏则偏移状态栏高度
        if (hasNotchScreen) {
            params.height = statusHeight;
        } else {
            //否则不偏移
            params.height = 0;
        }
        statusView.setLayoutParams(params);
    }

    private OnFaceVerifyListener onFaceVerifyListener;

    public void setOnFaceVerifyListener(OnFaceVerifyListener onFaceVerifyListener){
        this.onFaceVerifyListener=onFaceVerifyListener;
    }


}
