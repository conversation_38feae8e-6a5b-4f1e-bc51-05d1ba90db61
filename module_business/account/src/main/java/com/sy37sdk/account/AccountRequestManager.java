package com.sy37sdk.account;

import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sdk.sq.net.SqVerifyError;
import com.sq.tool.network.SignInterceptor.SignVersion;
import com.sq.tool.network.SignV2Interceptor.SignExt;
import com.sq.tool.network.SignV3Interceptor;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tool.sqtools.detector.DevicesFingerprint;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.request.CommonParamsV2;
import com.sqwan.common.request.CommonParamsV3;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
public class AccountRequestManager {

    private final Context mContext;

    public AccountRequestManager(Context context) {
        mContext = context;
    }

    public void loginRequest(final String loginName, final String loginPwd, final boolean isPhoneLogin,
        final SqHttpCallback<String> callback) {
        SqRequest request = SqRequest.of(UrlConstant.LOGIN)
            .signV3()
            .addHeader("D-Token", DevicesFingerprint.getDevToken(mContext))
            .addParam("uname", loginName)
            .addParam("upwd", loginPwd)
            .addParam("signType", "all")
            .addParam("display_name", AppUtils.getAppName(mContext))
            .addParam(SqConstants.TRANS_INFO, SqRequest.getTransInfo())
            .addParamsTransformer(new CommonParamsV2());
        if (isPhoneLogin) {
            request.addParam("loginType", "phone");
        }
        request.post(callback, String.class);
    }

    public void checkAccountList(String loginName, String loginPwd, SqHttpCallback<JSONObject> callback) {
        SqRequest request = SqRequest.of(UrlConstant.CHECK_ACCOUNT_LIST)
            .signV3()
            .addParam("uname", loginName)
            .addParam("os", "android")
            .addParam("upwd", loginPwd)
            .addParamsTransformer(new CommonParamsV2());
        request.post(callback);
    }


    /**
     * 账号注册
     */
    public void registerRequest(final String regName, final String regPw, final SqHttpCallback<String> callback) {
        SqRequest.of(UrlConstant.REG)
            .signV2(new SignExt(regName).append(regPw))
            .addHeader("D-Token", DevicesFingerprint.getDevToken(mContext))
            .addParam("uname", regName)
            .addParam("upwd", regPw)
            .addParamsTransformer(new CommonParamsV2())
            .post(callback, String.class);
    }

    /**
     * 【实名认证】 定时上报用户在线状态，后端统计在线时长。
     */
    public void reportDevDuration(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_REPORT_DEV_DURATION)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("type", isLandScape(mContext) ? "1" : "2")
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    /**
     * 【防沉迷】 定时上报用户在线状态，后端统计在线时长。
     */
    public void reportUserDuration(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_REPORT_USER_DURATION)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("type", isLandScape(mContext) ? "1" : "2")
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    public void antiIndulge(String actionType, SqHttpCallback<String> callback) {
        String realActionType = "login";
        if (!TextUtils.isEmpty(actionType) && actionType.equals("register")) {
            realActionType = "reg";
        }
        SqRequest.of(UrlConstant.CHECK_ANTI_AUTHENT)
            .signV3()
            .addParam("uname", AccountCache.getUsername(mContext))
            .addParam("pdata", "")
            .addParam("type", isLandScape(mContext) ? "1" : "2")
            .addParam("from", "android")
            .addParam("actionType", realActionType)
            .addParam("token", AccountCache.getToken(mContext))
            .addParamsTransformer(new CommonParamsV2())
            .post(callback, String.class);
    }

    /**
     * 自动生成注册账号
     */
    public void autoAccountRequest(final SqHttpCallback<String> callback) {
        SQAppConfig appConfig = ConfigManager.getInstance(mContext).getSQAppConfig();
        String gid = appConfig.getGameid();
        String pid = appConfig.getPartner();
        String refer = appConfig.getRefer();
        String dev = DevLogic.getInstance(mContext).getValue();
        String time = String.valueOf(System.currentTimeMillis() / 1000);
        HashMap<String, String> params = new HashMap<>();
        params.put("pid", pid);
        params.put("gid", gid);
        params.put("refer", refer);
        params.put("dev", dev);
        params.put("time", time);
        params.put("scut", ConfigManager.getInstance(mContext).getLoginCode() + "");
        params.put("gwversion", VersionUtil.gwversion);
        // 不属于任何一种签名, 手动签名
        String sign = MD5Util.Md5(pid + gid + refer + dev + time).toLowerCase();
        params.put("sign", sign);
        SqRequest.of(UrlConstant.AUTO_SET_ACCOUNT)
            .formParams(params)
            .post(callback, String.class);
    }

    public void wxAuthRequest(String code, final SqHttpCallback<String> callback) {
        SqRequest.of(UrlConstant.WX_AUTH)
            .signV3()
            .addParam("authApp", "wechat")
            .addParam("oauthCode", code)
            .addParam("locale", "zh-cn")
            .addParam("scene", "sdk")
            .addParam("reqFrom", "android")
            .addParamsTransformer(new CommonParamsV2())
            .post(new SqHttpCallback<String>() {
                @Override
                public void onSuccess(String data) {
                    if (callback == null) {
                        return;
                    }
                    if (verifySign(data)) {
                        callback.onSuccess(data);
                    } else {
                        callback.onFailure(-1, "验证服务端返回数据sign值失败",
                            new SqVerifyError("验证服务端返回数据sign值失败"));
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    if (callback == null) {
                        return;
                    }
                    callback.onFailure(code, errorMsg, error);
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    if (callback == null) {
                        return;
                    }
                    callback.onResponseStateError(httpStatus, state, msg, data);
                }
            }, String.class);
    }


    /**
     * 请求适龄提醒配置
     */
    public void appropriateAgeProtocol(SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.APPROPRIATE_AGE_PROTOCOL)
            .signV3()
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    /**
     * 登录成功之后的悬浮球菜单配置
     */
    public void floatWindow(final SqHttpCallback<JSONObject> callback) {
        Context context = mContext;
        // mock url:  "http://yapi.39on.com/mock/75/go/cfg/v2/float_window";
        SqRequest.of(UrlConstant.FLOAT_WINDOW_V2)
            .signV3()
            .addParam("token", AccountCache.getToken(context))
            .addParam("scut", ConfigManager.getInstance(context).getLoginCode())
            .addParamsTransformer(new CommonParamsV3())
            .get(callback);
    }

    /**
     * 请求用户信息接口
     */
    public void getPtUserInfo(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_GET_PT_USER_INFO)
            .sign(SignVersion.V4, null)
            // 应用标识，悬浮球传float_window
            .addParam("appid", "float_window")
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("locale", "zh-cn")
            .addParamsTransformer(new CommonParamsV3())
            // 响应体的key和普通的不一样
            .get(new UserHttpCallback<>(callback));
    }


    /**
     * 获取悬浮球红点
     */
    public void floatRedPoint(String pageUuid, String redDotTitle, final SqHttpCallback<JSONObject> callback) {
        SqRequest request = SqRequest.of(UrlConstant.URL_GET_RED_POINT)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3());
        if (!TextUtils.isEmpty(pageUuid)) {
            request.addParam("page_uuid", pageUuid);
        }
        if (!TextUtils.isEmpty(redDotTitle)) {
            request.addParam("red_dot_title", redDotTitle);
        }
        request.get(callback);
    }

    /**
     * 获取悬浮球提醒
     */
    public void getFloatWarning(String pageUuid, String redDotTitle, final SqHttpCallback<JSONObject> callback) {
        SqRequest request = SqRequest.of(UrlConstant.URL_GET_FLOAT_WARNING)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3());
        if (!TextUtils.isEmpty(pageUuid)) {
            request.addParam("page_uuid", pageUuid);
        }
        if (!TextUtils.isEmpty(redDotTitle)) {
            request.addParam("red_dot_title", redDotTitle);
        }
        request.post(callback);
    }


    /**
     * 悬浮球红点已读
     */
    public void floatRedCalled(String pageUuid, String redDotTitle, final SqHttpCallback<Void> callback) {
        SqRequest request = SqRequest.of(UrlConstant.URL_GET_RED_CALLED)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3());
        if (!TextUtils.isEmpty(pageUuid)) {
            request.addParam("page_uuid", pageUuid);
        }
        if (!TextUtils.isEmpty(redDotTitle)) {
            request.addParam("red_dot_title", redDotTitle);
        }
        request.post(callback, Void.class);
    }

    /**
     * 请求人脸验证的id
     */
    public void requestCertifyId(String metaInfo, final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_ALI_CERTIFY_FACE_ID)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("meta_info", metaInfo)
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback);
    }

    /**
     * 是否需要人脸认证
     */
    public void needFaceVerify(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_NEED_ALI_CERTIFY_FACE)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            // 场景id，1为登陆后
            .addParam("scene_id", "1")
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback);
    }

    /**
     * 验证是否成功
     */
    public void checkValidCertify(String certifyId, final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_CHECK_VALIDATE_VERIFY)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("certify_id", certifyId)
            .addParam("scut", ConfigManager.getInstance(mContext).getLoginCode())
            .addParamsTransformer(new CommonParamsV3())
            .post(callback);
    }

    /**
     * 获取头像列表
     */
    public void getAvatarList(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_GET_AVATAR_LIST)
            .sign(SignVersion.V4, null)
            // 应用标识，悬浮球传float_window
            .addParam("appid", "float_window")
            .addParamsTransformer(new CommonParamsV3())
            // 响应体的key和普通的不一样
            .get(new UserHttpCallback<>(callback));
    }

    /**
     * 修改个人信息
     */
    public void modifyPersonInfo(String nick, String avatar, final SqHttpCallback<Void> callback) {
        SqRequest request = SqRequest.of(UrlConstant.URL_MODIFY_PERSON_INFO)
            .sign(SignVersion.V4, null)
            // 应用标识，悬浮球传float_window
            .addParam("appid", "float_window")
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("locale", "zh-cn")
            .addParamsTransformer(new CommonParamsV3());
        if (!TextUtils.isEmpty(nick)) {
            request.addParam("nickname", nick);
        }
        if (!TextUtils.isEmpty(avatar)) {
            request.addParam("avatar", avatar);
        }
        // 响应体的key和普通的不一样
        request.post(new UserHttpCallback<>(callback), Void.class);
    }

    /**
     * 获取是否展示红包悬浮窗的开关
     */
    public void getRedPacketSwitch(String serverTime, SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.REDPACKET_FLOATVIEW_SWITCH)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("start_service_ts", serverTime)
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    /**
     * 上报在线状态
     */
    public void reportOnlineDuration(final SqHttpCallback<JSONObject> callback) {
        SqRequest.of(UrlConstant.URL_REPORT_DEV_ONLINE)
            .signV3()
            .addParam("token", AccountCache.getToken(mContext))
            .addParam("from", "android")
            .addParamsTransformer(new CommonParamsV2())
            .post(callback);
    }

    private static boolean isLandScape(Context context) {
        return context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    /**
     * 和普通的响应结构不一样, 修改响应体的key
     */
    private static class UserHttpCallback<Data> extends SqHttpCallback<Data> {

        final SqHttpCallback<Data> mCallback;

        UserHttpCallback(SqHttpCallback<Data> callback) {
            mCallback = callback;
        }

        @Override
        protected int getOkState() {
            return 0;
        }

        @Override
        protected String getStateKey() {
            return "errcode";
        }

        @Override
        protected String getMsgKey() {
            return "errmsg";
        }

        @Override
        protected String getDataKey() {
            return "data";
        }

        @Override
        public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
            if (mCallback != null) {
                mCallback.onResponseStateError(httpStatus, state, msg, data);
            }
        }

        @Override
        public void onSuccess(Data data) {
            if (mCallback != null) {
                mCallback.onSuccess(data);
            }
        }

        @Override
        public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
            if (mCallback != null) {
                mCallback.onFailure(code, errorMsg, error);
            }
        }
    }

    /**
     * 校验服务端返回的数据，sign是否正确
     */
    private static boolean verifySign(String json) {
        try {
            JSONObject jsonObject = new JSONObject(json);
            String dataSign = jsonObject.getString("sign");
            Map<String, String> datas = new HashMap<>();
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                if (!"sign".equals(key)) {
                    datas.put(key, jsonObject.getString(key));
                }
            }
            String sign = SignV3Interceptor.sign(datas);
            LogUtil.i("本地计算出来的签名值为：" + sign);
            return sign.equals(dataSign);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }
}
