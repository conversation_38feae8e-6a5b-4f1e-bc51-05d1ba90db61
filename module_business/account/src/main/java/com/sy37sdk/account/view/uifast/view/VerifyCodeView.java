package com.sy37sdk.account.view.uifast.view;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.DisplayUtil;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.sdk.libs.SqR;
import com.sy37sdk.account.trackaction.PageExposureAction;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;
import com.sy37sdk.account.view.LoginSkinHelper;
import com.sy37sdk.account.view.base.view.BaseSwitchView;
import com.sy37sdk.account.view.uifast.ILoginDialog;
import com.sy37sdk.account.view.uifast.constant.AccountViewBundleKey;
import com.sy37sdk.account.view.uifast.presenter.IVerifyCodePresenter;
import com.sy37sdk.account.view.uifast.presenter.VerifyCodePresenter;

import java.util.Map;

/**
 * 验证码输入页面
 */
public class VerifyCodeView extends BaseSwitchView implements IVerifyCodeView {

    private TextView tvSendPhone;

    private TextView tvLoginWay;

    private TextView tvTipInput;

    private TextView tvLogin;

    private TextView loginHelp;

    private View inputVerifyView, inputPwdView;

    private TextView tvSendCode;

    private ImageView ivPwdStatus;

    private EditText etPwd, etCode;

    private View contentView;


    private String phoneNumber;

    private IVerifyCodePresenter presenter;

    private boolean isShowPwd = false;

    //当前界面登录类型 验证码登录/密码登录
    private int mType;

    //验证码登录
    private static final int LOGIN_WAY_VERIFY_CODE = 1;

    //密码登录
    private static final int LOGIN_WAY_PWD = 0;

    //验证码长度
    private static final int CODE_LENGTH = 6;

    public VerifyCodeView(Context context, ILoginDialog loginDialog) {
        super(context);
        this.loginDialog = loginDialog;
        presenter = new VerifyCodePresenter(context, this);
    }


    @Override
    public void onSwitched(int fromIndex, int toIndex, Bundle bundle) {
        super.onSwitched(fromIndex, toIndex, bundle);
        String mobile = bundle.getString(AccountViewBundleKey.mobile);
        this.phoneNumber = mobile;
        mType = LOGIN_WAY_PWD;
        toggleLoginWay();
        presenter.initTimer();
        if (etCode != null) {
            etCode.setText("");
        }
        if (etPwd != null) {
            etPwd.setText("");
        }
    }

    @Override
    public String getLayoutResName() {
        return "sysq_dialog_login_view_verify_code";
    }

    @Override
    public void initView() {
        tvSendPhone = getViewByName("tip_send");
        tvTipInput = getViewByName("tip_input_login_info");
        inputPwdView = getViewByName("ll_pwd");
        inputVerifyView = getViewByName("ll_verify_code");
        tvLoginWay = getViewByName("login_way");
        ivPwdStatus = getViewByName("iv_pwd_status");
        etPwd = getViewByName("et_pwd");
        tvSendCode = getViewByName("send_code");
        etCode = getViewByName("et_verify_code");
        tvLogin = getViewByName("tv_login");
        tvLogin.setBackgroundResource(LoginSkinHelper.getLoginBtnBackgroundResId(getContext()));
        tvLogin.setTextColor(LoginSkinHelper.getLoginBtnTextColor(getContext()));
        loginHelp = getViewByName("login_help");
        contentView = getViewByName("content_layout");
        tvTipInput.setTextColor(LoginSkinHelper.getTipInputColor(getContext()));
        tvLoginWay.setTextColor(LoginSkinHelper.getLoginTextAccentColor(getContext()));
        loginHelp.setTextColor(LoginSkinHelper.getLoginTextAccentColor(getContext()));
        tvSendPhone.setTextColor(LoginSkinHelper.getLoginTextHintColor(getContext()));
    }

    @Override
    public void initEvent() {

        loginHelp.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.toAppeal();
            }
        });
        ivPwdStatus.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                togglePassword();
            }
        });

        tvSendCode.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.sendCode(phoneNumber);
            }
        });

        tvLoginWay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                toggleLoginWay();
            }
        });

        etCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String code = s.toString();
                LogUtil.i("afterTextChanged  " + code);
                if (!TextUtils.isEmpty(code) && code.length() == CODE_LENGTH) {
                    presenter.loginVerifyCode(phoneNumber, code);
                }
            }
        });
        tvLogin.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.loginPwd(phoneNumber, etPwd.getText().toString());
            }
        });
    }

    @Override
    public String getTitle() {
        return "登录";
    }


    @Override
    public void verifyCodeStatus(boolean enable) {
        tvSendCode.setEnabled(enable);
        tvSendCode.setClickable(enable);
    }

    @Override
    public void verifyCodeText(String mills) {
        tvSendCode.setText(mills);
    }

    @Override
    public void loginSuccess(Map<String, String> data) {
        loginDialog.loginSuccess(data);
    }

    /**
     * 手机+密码登录  手机+验证码登录 切换
     */
    private void toggleLoginWay() {
        if (mType == LOGIN_WAY_VERIFY_CODE) {
            //切换到了手机+密码登录
            DisplayUtil.setViewSize(contentView, 340, 270);
            contentView.setBackgroundResource(LoginSkinHelper.getPhonePwdLoginBackgroundResId(getContext()));
            mType = LOGIN_WAY_PWD;
            tvTipInput.setText(SqResUtils.getStringByName(getContext(), "sysq_input_pwd"));
            String result = String.format(SqResUtils.getStringByName(getContext(), "sysq_pwd_phone"), phoneNumber);
            tvSendPhone.setText(result);
            inputVerifyView.setVisibility(View.GONE);
            inputPwdView.setVisibility(View.VISIBLE);
            tvLoginWay.setText(SqResUtils.getStringByName(getContext(), "sysq_login_verify"));
            showSoftInput(etPwd);
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.phone_pwd, SqTrackPage.SqTrackViewName.phone_pwd);
        } else {
            //切换到了手机+验证码登录
            DisplayUtil.setViewSize(contentView, 340, 222);
            contentView.setBackgroundResource(LoginSkinHelper.getPhoneCodeLoginBackgroundResId(getContext()));
            mType = LOGIN_WAY_VERIFY_CODE;
            tvTipInput.setText(SqResUtils.getStringByName(getContext(), "sysq_input_verify_code"));
            String result = String.format(SqResUtils.getStringByName(getContext(), "sysq_verify_code_phone"), phoneNumber);
            tvSendPhone.setText(result);
            inputPwdView.setVisibility(View.GONE);
            inputVerifyView.setVisibility(View.VISIBLE);
            tvLoginWay.setText(SqResUtils.getStringByName(getContext(), "sysq_login_pwd"));
            showSoftInput(etCode);
            PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.phone_code, SqTrackPage.SqTrackViewName.phone_code);
        }
    }

    private void togglePassword() {
        String pwd = etPwd.getText().toString();
        if (isShowPwd) {
            etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
            ivPwdStatus.setImageResource(getIdByName("sysq_ic_pwd_hide", "drawable"));
        } else {
            etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            ivPwdStatus.setImageResource(getIdByName("sysq_ic_pwd_show", "drawable"));
        }
        isShowPwd = !isShowPwd;
        etPwd.setText(pwd);
    }

    @Override
    public void startView(int page, Bundle bundle) {
        loginDialog.onSwitch(page, bundle);
    }
}
