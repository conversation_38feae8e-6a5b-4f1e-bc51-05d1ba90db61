package com.sy37sdk.account.floatview;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tools.network.respond.JsonResponse;
import com.sq.tools.network.respond.ResponseTools;

import java.util.Collections;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 洋葱头悬浮窗配置信息类，包含了相关数据的解析。
 * 详情见API文档: http://yapi.39on.com/project/75/interface/api/11072
 */
@SuppressWarnings("unused")
public class CommonFloatConfig extends ResponseTools {

    @JsonResponse({"float_window", "id"})
    public String id;

    @JsonResponse({"float_window", "sign"})
    public String sign;

    @JsonResponse({"float_window", "name"})
    public String name;

    @JsonResponse({"float_window", "icon_url"})
    public String iconUrl;

    @JsonResponse({"float_window", "is_show"})
    public boolean isShow;

    @JsonResponse({"float_window", "menu"})
    private JSONArray menuArray;

    @JsonResponse({"float_window", "user_center"})
    public String user_center;

    @JsonResponse({"float_window", "warning_type"})
    public String warning_type;

    @JsonResponse({"float_window", "background_url"})
    public String background_url;

    @JsonResponse({"float_window", "nickname_color"})
    public String nickname_color;

    @JsonResponse({"float_window", "user_center_color"})
    public String user_center_color;

    @JsonResponse({"float_window", "title_color"})
    public String title_color;


    @Nullable
    public List<MenuConfig> menuConfigs;

    public CommonFloatConfig(@NonNull String data) {
        initSelfByString(data);
        initMenuConfigs();
    }

    public CommonFloatConfig(JSONObject data) {
        initSelfByJson(data);
        initMenuConfigs();
    }

    private void initMenuConfigs() {
        if (menuArray == null || menuArray.length() <= 0) return;
        menuConfigs = new ArrayList<>();
        for (int i = 0; i < menuArray.length(); i++) {
            menuConfigs.add(new MenuConfig(menuArray.optJSONObject(i)));
        }
    }


    public List<MenuConfig> getMenuConfigs() {
        return menuConfigs;
    }


    @Override
    public String toString() {
        return "CommonFloatConfig{" +
                "id='" + id + '\'' +
                ", sign='" + sign + '\'' +
                ", name='" + name + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", isShow=" + isShow +
                ", menuArray=" + menuArray +
                ", user_center='" + user_center + '\'' +
                ", menuConfigs=" + menuConfigs +
                '}';
    }

    /**
     * 是否需要显示红点
     * @return
     */
    public boolean needRedDot() {
        if (menuConfigs == null || menuConfigs.size() < 1) {
            return false;
        }
        for (MenuConfig menuConfig : menuConfigs) {
            if (menuConfig.needRedDot()) {
                return true;
            }
        }
        return false;
    }
}
