package com.sy37sdk.account.policy.view;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.widget.TextView;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-15 20:28
 */
public class UserAuthTipsDialog extends AuthBaseDialog {
    public UserAuthTipsDialog(Context context) {
        super(context);
    }

    @Override
    protected String getContainerLayout() {
        return "sy37_layout_auth_tips";
    }

    @Override
    protected void doEngine() {
        TextView textView = findViewById(findId("tvProtol"));
        String txt = textView.getText().toString();
        String tag= "取消授权并退出游戏";
        SpannableString ss = setLink(textView,txt);
        ss.setSpan(new ForegroundColorSpan(Color.parseColor("#EC808D")), txt.indexOf(tag), txt.indexOf(tag)+tag.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        textView.setText(ss);
        tvCancel.setText("取消授权");
        tvCancel.setSelected(false);
        tvOk.setText("我再想想");
    }

    @Override
    protected String getTitle() {
        return "温馨提示";

    }

    @Override
    protected void onClickCancel() {
        exit();
    }
}
