package com.sy37sdk.account.activebefore.bean;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-19 12:32
 */
public class PermissionInfo {
    private String is_necessary = "0";
    public boolean isNecessary(){
        return "1".equals(is_necessary);
    }
    public void parse(String jsonData){
        try {
            JSONObject jsonObject = new JSONObject(jsonData);
            is_necessary = jsonObject.optString("is_necessary");
        } catch (JSONException e) {
            e.printStackTrace();

        }
    }
}
