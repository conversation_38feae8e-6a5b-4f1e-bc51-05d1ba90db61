package com.sy37sdk.account.presenter;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import com.sqwan.common.mvp.BasePresenter;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.account.view.IAutoLoginDialog;
import java.util.Map;

public abstract class BaseAutoLoginPresenter extends BasePresenter<IAutoLoginDialog> implements IAutoLoginPresenter {

    protected static final String TAG = "【Login AutoP】";
    protected boolean isAutoLoginShow = false;
    protected long autoLoginShowTime;

    /**
     * 自动登录等待时长
     */
    protected static final long AUTO_LOGIN_WAIT_THRESHOLD = 3 * 1000;

    public BaseAutoLoginPresenter(Context context, IAutoLoginDialog view) {
        super(context, view);
    }

    @Override
    public void setAutoLoginShow(boolean autoLoginShow) {
        isAutoLoginShow = autoLoginShow;
        if (autoLoginShow) {
            autoLoginShowTime = System.currentTimeMillis();
        }
    }

    protected void loginSuccess(final Map<String, String> data) {
        if (mView != null && isAutoLoginShow) {
            long specTime = System.currentTimeMillis() - autoLoginShowTime;
            if (specTime < AUTO_LOGIN_WAIT_THRESHOLD) {
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (isAutoLoginShow) {
                            if (mView != null) {
                                autoLoginFinish(data);
                            }
                        }
                    }
                }, AUTO_LOGIN_WAIT_THRESHOLD - specTime);
            } else {
                autoLoginFinish(data);
            }
        }
    }


    protected void loginFail(int code, String msg) {
        if (mView != null) {
            mView.autoLoginFail(code, msg);
        }
    }

    public void autoLoginFinish(Map<String, String> data) {
        String accountType = data.get(LoginTractionManager.TRACK_ACCOUNT_TYPE);
        LoginTractionManager.trackInvoke(accountType, LoginTractionManager.login_way_auto);
        LoginTractionManager.track(LoginTractionManager.login_way_auto, data);
    }

}
