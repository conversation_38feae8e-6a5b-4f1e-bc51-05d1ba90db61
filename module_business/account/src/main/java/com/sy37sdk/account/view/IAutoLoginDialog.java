package com.sy37sdk.account.view;


import com.sqwan.common.mvp.IView;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/20
 */
public interface IAutoLoginDialog extends IView {


    void autoLoginSuccess(Map<String, String> data);

    void autoLoginFail(int code, String msg);

    void setChangeAccountListener(IChangeAccountListener listener);

    interface IChangeAccountListener {

        /**
         * 切换账号
         */
        void changeAccount();
    }

    void closeDialog();

}
