package com.sy37sdk.account.scanCode;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraDevice.StateCallback;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraMetadata;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.CaptureResult;
import android.hardware.camera2.TotalCaptureResult;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.Image;
import android.media.ImageReader;
import android.media.ImageReader.OnImageAvailableListener;
import android.support.annotation.NonNull;
import android.util.Size;
import android.view.Surface;
import android.view.TextureView;
import com.sqwan.common.util.LogUtil;
import java.nio.ByteBuffer;
import java.util.Arrays;

/**
 * @author: gsp
 * @date: 2024/12/17
 * @desc: 基于Camera2 api封装
 * 流程：打开指定id的相机，传入需要接收帧数据的surface，开启预览的session即可
 */
public class Camera2 implements ICameraOperation {

    private final CameraManager mCameraManager;
    private CameraDevice mCameraDevice;
    private FrameCallback mFrameCallback;
    private OpenFailCallback mFailCallback;
    private Size mPreviewSize;
    private ImageReader mImageReader;
    private CaptureRequest.Builder mPreviewRequestBuilder;
    //预览抓捕进程
    private CameraCaptureSession mCaptureSession;


    public Camera2(Context context) {
        mCameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
    }

    private final OnImageAvailableListener imageAvailableListener = reader -> {
        Image image = reader.acquireLatestImage();
        if (image == null) {
            return;
        }
        ByteBuffer buffer = image.getPlanes()[0].getBuffer();
        int imageWidth = image.getWidth();
        int imageHeight = image.getHeight();
        byte[] data = new byte[buffer.remaining()];
        buffer.get(data);
        image.close();
        onFrameAvailable(data, imageWidth, imageHeight);
    };

    @SuppressLint("MissingPermission")
    private void openCamera(Surface surface)
        throws CameraAccessException {
        mCameraManager.openCamera(getBackCameraId(), new StateCallback() {
            @Override
            public void onOpened(@NonNull CameraDevice camera) {
                LogUtil.i(buildPrefixLog("相机打开"));
                mCameraDevice = camera;
                try {
                    startPreview(surface);
                } catch (CameraAccessException e) {
                    mFailCallback.onFail("相机预览失败");
                    LogUtil.e(buildPrefixLog("打开相机报错  " + e.getMessage()));
                    e.printStackTrace();
                }
            }

            @Override
            public void onDisconnected(@NonNull CameraDevice camera) {
                LogUtil.e(buildPrefixLog("相机断开链接"));
                camera.close();
                mCameraDevice = null;
            }

            @Override
            public void onError(@NonNull CameraDevice camera, int error) {
                LogUtil.e(buildPrefixLog("相机链接报错 errCode：" + error));
            }
        }, null);
    }

    /**
     * 设置相机输出尺寸
     */
    private void setUpCameraOutputs(TextureView textureView, int viewWidth, int viewHeight)
        throws CameraAccessException {
        CameraCharacteristics characteristics = mCameraManager.getCameraCharacteristics(getBackCameraId());
        StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        //当前相机设备支持的预览尺寸
        Size[] supportedSizes = map.getOutputSizes(SurfaceTexture.class);

        int windowRotation = ((Activity) textureView.getContext()).getWindowManager().getDefaultDisplay().getRotation();
        //相机方向
        int cameraOrientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);
        //是否需要根据屏幕方向调整相机旋转方向（当屏幕方向和相机方向不一致时需要）
        boolean needSwappedDimensions = false;
        switch (windowRotation) {
            case Surface.ROTATION_0:
            case Surface.ROTATION_180:
                if (cameraOrientation == 90 || cameraOrientation == 270) {
                    needSwappedDimensions = true;
                }
                break;
            case Surface.ROTATION_90:
            case Surface.ROTATION_270:
                if (cameraOrientation == 0 || cameraOrientation == 180) {
                    needSwappedDimensions = true;
                }
                break;
        }
        int rotatedPreviewWidth = viewWidth;
        int rotatedPreviewHeight = viewHeight;
        if (needSwappedDimensions) {
            rotatedPreviewWidth = viewHeight;
            rotatedPreviewHeight = viewWidth;
        }
        mPreviewSize = new Size(rotatedPreviewWidth, rotatedPreviewHeight);
        float ration = (float) rotatedPreviewWidth / rotatedPreviewHeight;
        for (Size option : supportedSizes) {
            if ((float) option.getWidth() / option.getHeight() == ration
                && option.getWidth() <= rotatedPreviewWidth
                && option.getHeight() <= rotatedPreviewHeight) {
                mPreviewSize = option;
                break;
            }
        }
        //预览输出到ImageReader中
        mImageReader = ImageReader.newInstance(mPreviewSize.getWidth(), mPreviewSize.getHeight(),
            ImageFormat.YUV_420_888, /*maxImages*/2);
        //监听预览输出
        mImageReader.setOnImageAvailableListener(imageAvailableListener, null);
    }


    private void onFrameAvailable(byte[] imageData, int imageWidth, int imageHeight) {
        boolean continuePreview = true;
        if (mFrameCallback != null) {
            continuePreview = mFrameCallback.onFrameAvailable(imageData, imageWidth, imageHeight, mPreviewSize);
        }
        if (continuePreview) {
            try {
                mCaptureSession.setRepeatingRequest(mPreviewRequestBuilder.build(), sessionCaptureCallback, null);
            } catch (CameraAccessException e) {
                e.printStackTrace();
            }
        }


    }


    //预览捕获状态
    private final CameraCaptureSession.CaptureCallback sessionCaptureCallback = new CameraCaptureSession.CaptureCallback() {
        @Override
        public void onCaptureCompleted(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request,
            @NonNull TotalCaptureResult result) {
            mCaptureSession = session;
            Integer afState = result.get(CaptureResult.CONTROL_AF_STATE);
            Integer aeState = result.get(CaptureResult.CONTROL_AE_STATE);
            if (afState == null || afState == CaptureRequest.CONTROL_AF_STATE_PASSIVE_FOCUSED
                || (afState == CaptureRequest.CONTROL_AF_STATE_INACTIVE
                && (aeState != null && aeState == CaptureRequest.CONTROL_AE_STATE_CONVERGED))) {
                capturePicture();
            }
        }
    };

    //预览配置状态
    private final CameraCaptureSession.StateCallback sessionStateCallback = new CameraCaptureSession.StateCallback() {
        @Override
        public void onConfigured(@NonNull CameraCaptureSession session) {
            mCaptureSession = session;
            mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
            mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);
            try {
                //重复请求预览
                session.setRepeatingRequest(mPreviewRequestBuilder.build(), sessionCaptureCallback, null);
            } catch (CameraAccessException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onConfigureFailed(@NonNull CameraCaptureSession session) {

        }
    };


    /**
     * 抓捕预览画面
     */
    private void capturePicture() {
        if (mCaptureSession == null) {
            return;
        }
        if (mImageReader == null) {
            LogUtil.e(buildPrefixLog("capturePicture ImageReader为空，return"));
            return;
        }
        try {
            mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER, CaptureRequest.CONTROL_AF_TRIGGER_START);
            mPreviewRequestBuilder.addTarget(mImageReader.getSurface());
            mCaptureSession.stopRepeating();
            mCaptureSession.capture(mPreviewRequestBuilder.build(), new CameraCaptureSession.CaptureCallback() {
                @Override
                public void onCaptureCompleted(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request,
                    @NonNull TotalCaptureResult result) {
                    unlockFocus();
                }
            }, null);
        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 停止预览
     */
    private void unlockFocus() {
        if (mCaptureSession == null) {
            return;
        }
        if (mImageReader == null) {
            LogUtil.e(buildPrefixLog("capturePicture ImageReader为空，return"));
            return;
        }
        mPreviewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER, CameraMetadata.CONTROL_AF_TRIGGER_CANCEL);
        mPreviewRequestBuilder.removeTarget(mImageReader.getSurface());
    }


    private void startPreview(Surface surface) throws CameraAccessException {
        mPreviewRequestBuilder = mCameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
        mPreviewRequestBuilder.addTarget(surface);
        mCameraDevice.createCaptureSession(Arrays.asList(surface, mImageReader.getSurface()), sessionStateCallback,
            null);
    }


    /**
     * 获取后置镜头的id
     */
    private String getBackCameraId() throws CameraAccessException {
        String[] ids = mCameraManager.getCameraIdList();
        for (String id : ids) {
            CameraCharacteristics characteristics = mCameraManager.getCameraCharacteristics(id);
            Integer orientation = characteristics.get(CameraCharacteristics.LENS_FACING);
            if (orientation != null && orientation == CameraCharacteristics.LENS_FACING_BACK) {
                return id;
            }
        }
        return "";
    }

    @Override
    public void openCamera(TextureView textureView, FrameCallback frameCallback, OpenFailCallback failCallback) {
        this.mFrameCallback = frameCallback;
        this.mFailCallback = failCallback;
        try {
            SurfaceTexture texture = textureView.getSurfaceTexture();
            if (texture == null) {
                failCallback.onFail("相机打开失败");
                LogUtil.e(buildPrefixLog("相机打开失败，getSurfaceTexture为空"));
                return;
            }
            Surface surface = new Surface(texture);
            setUpCameraOutputs(textureView, textureView.getWidth(), textureView.getHeight());
            texture.setDefaultBufferSize(mPreviewSize.getWidth(), mPreviewSize.getHeight());
            openCamera(surface);
        } catch (CameraAccessException e) {
            failCallback.onFail("相机打开失败");
            LogUtil.e(buildPrefixLog("相机打开失败" + e.getMessage()));
            e.printStackTrace();
        }
    }

    @Override
    public void closeCamera() {
        LogUtil.d(buildPrefixLog("关闭相机"));
        if (mCameraDevice != null) {
            mCameraDevice.close();
            mCameraDevice = null;
        }
        if (mImageReader != null) {
            mImageReader.close();
            mImageReader = null;
        }
        if (mCaptureSession != null) {
            mCaptureSession.close();
            mCaptureSession = null;
        }
        mFailCallback = null;
        mFrameCallback = null;
    }

    private String buildPrefixLog(String msg) {
        return "【Camera】" + msg;
    }
}
