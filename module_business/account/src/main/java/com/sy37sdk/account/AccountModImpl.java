package com.sy37sdk.account;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import com.social.sdk.SocialApi;
import com.sq.eventbus.annotation.Subscribe;
import com.sq.eventbus.core.EventBus;
import com.sq.tool.logger.SQLog;
import com.sq.websocket_engine.WebSocketEngine;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.eventbus.OnActivityResultEvent;
import com.sqwan.common.eventbus.SActiveEvent;
import com.sqwan.common.mod.account.IAccountChangeListener;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.account.IAuthResultListener;
import com.sqwan.common.mod.account.IBackToGameLoginListener;
import com.sqwan.common.mod.account.IBindWxListener;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.mod.account.IScreenshotListener;
import com.sqwan.common.mod.liveshow.LiveRadioEngine;
import com.sqwan.common.mod.liveshow.LiveshowEngine;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.UrlUtils;
import com.sqwan.common.util.ZipString;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.age.AppropriateAgeManager;
import com.sy37sdk.account.auth.AuthManager;
import com.sy37sdk.account.auth.floatview.AuthCountDownManager;
import com.sy37sdk.account.binding.GameBindingManager;
import com.sy37sdk.account.controller.AbstractLoginController;
import com.sy37sdk.account.controller.UIVersionManager;
import com.sy37sdk.account.eventbus.EventBusIndex;
import com.sy37sdk.account.face.FaceVerifyManager;
import com.sy37sdk.account.floatview.FloatViewDataManager;
import com.sy37sdk.account.floatview.LoginInfoUtil;
import com.sy37sdk.account.floatview.MenuConfig;
import com.sy37sdk.account.floatview.SqBaseFloatView;
import com.sy37sdk.account.floatview.SqFloatViewManager;
import com.sy37sdk.account.floatview.data.RedDot;
import com.sy37sdk.account.floatview.redpacket.RedPacketManager;
import com.sy37sdk.account.pop.LoginPopupDialogManager;
import com.sy37sdk.account.screenshot.ScreenshotManager;
import com.sy37sdk.account.uagree.UAgreeManager;
import com.sy37sdk.account.util.SocialAccountUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
public class AccountModImpl implements ILoginListener, IAccountMod {

    private static final String TAG = "【Login Mod】";
    private ILoginListener mListener;
    private IBackToGameLoginListener backToGameLoginListener;
    private IAccountChangeListener accountChangeListener;
    public IAuthResultListener iAuthResultListener;
    public static boolean isSQLoginSuccess = false;
    //是否进服
    private boolean isSQSubmitRole = false;
    private Context mContext;

    public AccountModImpl(Context context) {
        this.mContext = context;
        EventBus.getDefault().addIndex(new EventBusIndex());
        EventBus.getDefault().register(this);
        SocialApi.init(context);
    }

    @Override
    public void login(final ILoginListener loginListener) {
        this.mListener = loginListener;
        SqFloatViewManager.getInstance().dismissFloatView();
        LiveshowEngine.getInstance().leaveLiveshowRoom(null, null);
        LiveRadioEngine.getInstance().leaveLiveRadioRoom(null, null);
//        WebSocketEngine.getInstance().close();
        AuthCountDownManager.getInstance().stopAuthCdTask();
        AbstractLoginController controller = UIVersionManager.getInstance(mContext).getLoginController();
        SQLog.d(TAG + "login: " + controller.getClass().getSimpleName());
        controller.login(this);
    }

    @Override
    public void logout() {
        isSQLoginSuccess = false;
    }

    @Override
    public void changeAccount(ILoginListener loginListener) {
        this.mListener = loginListener;
        SqFloatViewManager.getInstance().dismissFloatView();
        LiveshowEngine.getInstance().leaveLiveshowRoom(null, null);
        LiveRadioEngine.getInstance().leaveLiveRadioRoom(null, null);
//        WebSocketEngine.getInstance().close();
        AuthCountDownManager.getInstance().stopAuthCdTask();
        UIVersionManager.getInstance(mContext).getLoginController().showLoginDialog(this);
    }

    @Override
    public void showLoginView(ILoginListener loginListener) {
        this.mListener = loginListener;
        UIVersionManager.getInstance(mContext).getLoginController().showLoginDialog(this);
    }

    @Override
    public String getToken() {
        return AccountCache.getToken(mContext);
    }

    @Override
    public String getUid() {
        return AccountCache.getUserid(mContext);
    }

    @Override
    public String getUname() {
        return AccountCache.getUsername(mContext);
    }

    /**
     * 登录成功之后解析其他非账户业务的配置，比如悬浮窗，实名认证
     */
    private void handOtherConfig() {
        String content = AccountLogic.getInstance(mContext).getLoginData();
        try {
            JSONObject data = new JSONObject(content);
            if (data.has("oauthinfo")) {
                String oauthinfo = data.getString("oauthinfo");
                LoginInfoUtil.setOauthNickName(oauthinfo, mContext);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("解析登录配置失败");
            BuglessAction.reportCatchException(e, content, BuglessAction.PARSE_CONFIG_ERROR);
        }
    }


    @Subscribe()
    public void onSActiveEvent(SActiveEvent event) {
        //在激活的时候触发获取用户协议配置
        AppropriateAgeManager.getInstance().refreshConfig();
        String data = event.getData();
        SQLog.v(TAG + "onSActiveEvent data=" + data);
        try {
            JSONObject dataJson = new JSONObject(data);

            if (dataJson.has("c")) {
                JSONObject configJson = dataJson.getJSONObject("c");
                if (configJson.has("version")) {
                    UIVersionManager.getInstance(mContext).initVersion(configJson.getString("version"));
                }
            }

//            if(dataJson.has("u")) {
//                String urlJson = dataJson.getString("u");
//                UrlConstant.refreshUrls(urlJson);
//            }

            AccountConfig.praseConfig(data);

        } catch (JSONException e) {
            LogUtil.e("account mod parse s active data error!");
            e.printStackTrace();
        }
        LoginTriggerManager.getInstance().startQueryLoginTrigger();
    }

    @Subscribe()
    public void onActivityResult(OnActivityResultEvent event) {
        int requestCode = event.getRequestCode();
        int resultCode = event.getResultCode();
        Intent intent = event.getIntent();
        SQLog.v(TAG + "onActivityResult code=" + requestCode);
        SocialApi.getInstance().onActivityResult(requestCode, resultCode, intent);
        FaceVerifyManager.getInstance(mContext).onActivityResult(requestCode, resultCode, intent);
    }


    @Override
    public void onSuccess(final Map<String, String> data) {
        SQLog.d(TAG + "登录成功, 触发实名认证检查");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.request_pcheck);
        SqTrackActionManager2.getInstance().flush();
        AuthManager.getInstance(mContext).requestAuthConfig(new SQResultListener() {
            @Override
            public void onSuccess(Bundle bundle) {
                SQLog.i(TAG + "实名认证检查成功, " + bundle);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.request_pcheck_succ);
                SqTrackActionManager2.getInstance().flush();
                setSubmitRole(false);
                handOtherConfig();
                AuthCountDownManager.getInstance().release();
                FloatViewDataManager.getInstance().clearRedDot();
                SqFloatViewManager.getInstance().requestFloatWindowConfig(mContext);
                FloatViewDataManager.getInstance().requestAvatarList();
                isSQLoginSuccess = true;
                if (mListener != null) {
                    mListener.onSuccess(data);
                }
                LoginPopupDialogManager.getInstance().handlePopup(mContext);
                WebSocketEngine.getInstance().close();
                WebSocketEngine.getInstance().open();
                SqFloatViewManager.getInstance().bindRedDot();
            }

            @Override
            public void onFailture(int code, String msg) {
                SQLog.e(TAG + "实名认证检查失败, code=" + code + ", msg=" + msg);
                HashMap<String, String> map = new HashMap<>();
                map.put(SqTrackKey.fail_code, code + "");
                map.put(SqTrackKey.reason_fail, msg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.request_pcheck_fail,map);
            }
        });
    }

    @Override
    public void onFailure(int code, String msg) {
        if (mListener != null) {
            mListener.onFailure(code, msg);
        }
    }


    @Override
    public void saveAccount(String name, String pwd) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUname(name);
        userInfo.setUpwd(ZipString.json2ZipString(pwd));
        AccountTools.setAccountToFile(mContext, userInfo);
    }

    @Override
    public void modifyPassword() {
//        AccountCache.setAutoPassword(mContext, ZipString.json2ZipString(""));
//        UserInfo userInfo = new UserInfo();
//        userInfo.setUname(AccountCache.getUsername(mContext));
//        userInfo.setUpwd("");
//        AccountTools.setAccountToFile(mContext, userInfo);
    }

    void accountChanged(Map<String, String> data) {
        accountChangeListener.accountChanged(data);
        AuthCountDownManager.getInstance().release();
    }

    @Override
    public void backToGameLogin() {
        backToGameLoginListener.backToGameLogin();
        AuthCountDownManager.getInstance().release();
        SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.BACK_TO_GAME_LOGIN);
    }

    @Override
    public void webEnLogin(boolean skipBackToGameLogin) {
        GameBindingManager.getInstance().setIsLogin(false);
        SqFloatViewManager.getInstance().dismissFloatView();
        LiveshowEngine.getInstance().leaveLiveshowRoom(null, null);
        LiveRadioEngine.getInstance().leaveLiveRadioRoom(null, null);
        WebSocketEngine.getInstance().close();
        if (backToGameLoginListener != null && !skipBackToGameLogin) {
            LogUtil.w("回到游戏登录界面的监听|不为空，现在回到游戏的登录界面");
            backToGameLogin();
        } else {
            if (accountChangeListener == null) {
                ToastUtil.showToast(mContext, "切换账号错误，请联系客服【10002】");
                return;
            }
            if (isSkipSQChangeAccountLogin(mContext)) {
                accountChanged(null);
            } else {
                if (isSQLoginSuccess) {
                    showLoginView(new ILoginListener() {
                        @Override
                        public void onSuccess(Map<String, String> data) {
                            accountChanged(data);
                        }

                        @Override
                        public void onFailure(int code, String msg) {
                            // 切换账号没有成功，不回调
                            if (SqFloatViewManager.getInstance().isShowFloat()) {
                                SqFloatViewManager.getInstance().showFloatView((Activity) mContext);
                            }
                        }
                    });
                } else {
                    ToastUtil.showToast(mContext, "您还未登录！");
                }
            }
        }
    }

    @Override
    public void setAccountChangeListener(IAccountChangeListener accountChangeListener) {
        this.accountChangeListener = accountChangeListener;
    }

    @Override
    public void setBackToGameLoginListener(IBackToGameLoginListener backToGameLoginListener) {
        this.backToGameLoginListener = backToGameLoginListener;
    }

    @Override
    public void setScreenshotListener(IScreenshotListener listener) {
        ScreenshotManager.getInstance(mContext).setScreenshotListener(listener);
    }

    @Override
    public void wxBind(IBindWxListener listener) {
        SocialAccountUtil.socialBindAuthorize((Activity) mContext, listener);
    }


    public static boolean isSkipSQChangeAccountLogin(Context context) {
        boolean isSkipLogin = false;
        ApplicationInfo appInfo;
        String result;
        try {
            String pname = context.getPackageName();
            appInfo = context.getPackageManager().getApplicationInfo(pname, PackageManager.GET_META_DATA);
            if (appInfo.metaData != null) {
                result = appInfo.metaData.getString("SQwanSkipSwitchLogin");
                System.out.println("是否跳过切换账号登录框：" + result);
                if (!TextUtils.isEmpty(result) && "yes".equals(result)) {
                    isSkipLogin = true;
                }
            }

        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return isSkipLogin;
    }

    @Override
    public void showUAgreement() {
        UAgreeManager.getInstance().showUAgree();
    }

    @Override
    public void submitRoleInfo(Map<String, String> roleInfo) {
        setSubmitRole(true);
        RedPacketManager.getInstance().submitRoleInfos(mContext, roleInfo);
        AuthCountDownManager.getInstance().startReportAuth();
    }

    @Override
    public void onResume() {
        SqFloatViewManager.getInstance().onResume();
    }

    @Override
    public void onPause() {
        SqFloatViewManager.getInstance().onPause();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
    }

    @Override
    public void setAuthResultListener(IAuthResultListener authResultListener) {
        this.iAuthResultListener = authResultListener;
    }

    @Override
    public IAuthResultListener getAuthResultListener() {
        return iAuthResultListener;
    }


    @Override
    public void showAgeAppropriate() {
        AppropriateAgeManager.getInstance().showAppropriateAgeDialog(mContext);
    }

    @Override
    public boolean hasSubmitRole() {
        return isSQSubmitRole;
    }

    @Override
    public void setSubmitRole(boolean isSubmitRole) {
        this.isSQSubmitRole = isSubmitRole;
    }

    @Override
    public void showFloatMenu() {
        SqBaseFloatView floatView = SqFloatViewManager.getInstance().floatView;
        if (floatView != null ) {
            floatView.setWindowVisibility(View.VISIBLE);
            floatView.resetView(false);
        }
        SqFloatViewManager.getInstance().showFloatMenu();
    }

    @Override
    public void redDotCalled(String title) {
        final RedDot redDot = FloatViewDataManager.getInstance().getRedDotByKey(title);
        if (SqFloatViewManager.getInstance().config == null) {
            return;
        }
        List<MenuConfig> menuConfigs = SqFloatViewManager.getInstance().config.getMenuConfigs();
        if (menuConfigs == null) {
            return;
        }
        MenuConfig config = null;
        for (MenuConfig menuConfig : menuConfigs) {
            if (TextUtils.equals(title, menuConfig.title)) {
                config = menuConfig;
                break;
            }
        }
        if (config != null && config.needRedDot()) {
            String pageUuid = UrlUtils.readValueFromUrlStrByParamName(config.openUrl, "page_uuid");
            if (redDot != null && redDot.getNum() > 0) {
                //通知服务端红点已读
                SqFloatViewManager.getInstance().redDotCalled(pageUuid, title, mContext);
                //将对应的红点数量置0
                FloatViewDataManager.getInstance().clearRedDotByKey(title);
                //刷新红点
                SqFloatViewManager.getInstance().showFloatItemRedDot();
                //刷新悬浮球红点
                if (SqFloatViewManager.getInstance().floatView != null) {
                    SqFloatViewManager.getInstance().floatView.showRedDot(FloatViewDataManager.getInstance().hasRedDot());
                }
            }
        }

    }

}
