package com.sy37sdk.account.floatview.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.sq.tools.Logger;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.account.floatview.FloatViewDataManager;
import com.sy37sdk.account.floatview.request.bean.FloatUserInfo;

import java.util.ArrayList;
import java.util.List;

public class AvatarAdapter extends RecyclerView.Adapter<AvatarAdapter.ViewHolder> {

    private Context context;

    private List<String> dataList = new ArrayList<>();

    private int selected = -1;

    public AvatarAdapter(Context context) {
        this.context = context;
    }

    public void setDataList(List<String> list,String data) {
        dataList.clear();
        dataList.addAll(list);
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
                if (data.equals(dataList.get(i))) {
                    selected = i;
                    break;
                }
            }
        }
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        View view = LayoutInflater.from(context).inflate(SqResUtils.getLayoutId(context, "sysq_item_float_avatar"), viewGroup, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        String avatar = dataList.get(position);
        AsyncImageLoader loader = new AsyncImageLoader(context);
        loader.loadDrawable(avatar, holder.ivAvatar, new AsyncImageLoader.ImageCallback() {

            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                holder.ivAvatar.setImageBitmap(imageDrawable);
            }
        });
        if (selected == position) {
            holder.ivSelectStatus.setImageResource(SqResUtils.getIdByName("sysq_ic_avatar_selected", "drawable", context));
        } else {
            holder.ivSelectStatus.setImageResource(SqResUtils.getIdByName("sysq_ic_avatar_unselect", "drawable", context));
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selected = position;
                notifyDataSetChanged();
                if (itemClickListener != null) {
                    itemClickListener.ItemOnClick(position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList == null ? 0 : dataList.size();
    }


    class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivAvatar, ivSelectStatus;


        ViewHolder(View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(SqResUtils.getId(context, "iv_avatar"));
            ivSelectStatus = itemView.findViewById(SqResUtils.getId(context, "iv_status"));
        }
    }

    public void setItemClickListener(ItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    private ItemClickListener itemClickListener;

    public interface ItemClickListener {
        void ItemOnClick(int item);
    }

    public List<String> getDatas() {
        return dataList;
    }
}
