package com.sy37sdk.account.device;

import com.sq.tool.network.SqHttpClient;
import com.sq.tool.sqtools.net.DHttpClient;
import com.sq.tool.sqtools.net.DevicesHttpCallback;
import com.sqnetwork.voly.DefaultRetryPolicy;
import com.sqnetwork.voly.Request;
import com.sqnetwork.voly.Response.Listener;
import com.sqnetwork.voly.toolbox.StringRequest;
import java.util.Collections;
import java.util.Map;


public class DevicesHttpClient implements DHttpClient {

    @Override
    public void postString(String url, String body, Map<String, String> headers, DevicesHttpCallback callback) {
        if (body == null) {
            return;
        }
        final StringRequest request = new StringRequest(Request.Method.POST, url,
            (Listener<String>) (raw, response) -> callback.onSuccess(response),
            error -> callback.onFail(-1, error.getMessage())) {

            @Override
            public Map<String, String> getHeaders() {
                return headers == null ? Collections.emptyMap() : headers;
            }

            @Override
            public byte[] getBody() {
                return body.getBytes();
            }

            @Override
            public String getBodyContentType() {
                return "application/json; charset=" + this.getParamsEncoding();
            }
        };
        //设置超时2秒
        request.setRetryPolicy(new DefaultRetryPolicy(2000,1,1));
        SqHttpClient.getInstance().enqueue(request);
    }
}
