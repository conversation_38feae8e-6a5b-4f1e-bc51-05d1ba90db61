package com.sy37sdk.account;


import android.app.Activity;
import android.content.Context;
import android.support.v4.app.NotificationManagerCompat;
import android.text.TextUtils;
import com.sqwan.common.mod.track.TrackModManager2;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.SQContextWrapper;
import com.sy37sdk.account.binding.GameBindingManager;
import com.sy37sdk.account.device.DevicesInfo;
import com.sy37sdk.account.trackaction.TrackNoticeLog;
import com.sy37sdk.account.util.AccountLoginType;
import java.util.HashMap;
import java.util.Map;

public class LoginTractionManager {

    public static final String login_way_account = "1";
    public static final String login_way_phone_code = "2";
    public static final String login_way_ali_fast = "3";
    public static final String login_way_auto = "4";
    public static final String login_way_phone_pwd = "5";
    public static final String login_way_history = "6";
    public static final String login_way_quick_start = "7";
    public static final String login_way_wechat = "8";

    public static final String register_way_account = "1";
    public static final String register_way_phone_code = "2";
    public static final String register_way_ali_fast = "3";
    public static final String register_way_quick = "4";
    public static final String register_way_wechat = "5";

    //账号类型 37账号or手机号
    public static final String TRACK_ACCOUNT_TYPE = "account_type";

    //登录类型，注册or登录
    public static final String TRACK_LOGIN_TYPE = "login_type";

    public static final String TRACK_ACCOUNT_TYPE_PHONE = "phone";
    public static final String TRACK_ACCOUNT_TYPE_ACCOUNT = "account";

    //37账号
    public static final String TRACK_ACCOUNT_TYPE_ACCOUNT_NUM = "1";
    //手机号
    public static final String TRACK_ACCOUNT_TYPE_PHONE_NUM = "2";
    //微信号
    public static final String TRACK_ACCOUNT_TYPE_WECHAT_NUM = "3";

    public static final String TRACK_LOGIN_TYPE_LOGIN = "login";
    public static final String TRACK_LOGIN_TYPE_REGISTER = "register";

    //调用登录
    public static void trackInvoke(String loginType, String loginWay) {
        //上报调用登录埋点
        HashMap<String, String> map = new HashMap<>();
        map.put(SqTrackKey.login_type, loginType);
        map.put(SqTrackKey.login_way, loginWay);
        map.put(SqTrackKey.is_game_binding, GameBindingManager.getInstance().isGameBinding() ? "1" : "0");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.login_invoke, map);
        SqTrackActionManager2.getInstance().flush();
    }


    //登录成功
    public static void track(String loginWay, Map<String, String> account) {
        Activity activity = SQContextWrapper.getActivity();
        //按压上报
        TrackNoticeLog.noticeLog(activity);

        if (account == null || account.isEmpty()) {
            return;
        }
        //设置数数的账号id
        TrackModManager2.setUserId(account.get("userid"));
        String accountType = account.get(TRACK_ACCOUNT_TYPE);
        String loginType = account.get(TRACK_LOGIN_TYPE);
        if (!TextUtils.isEmpty(loginType) && TRACK_LOGIN_TYPE_REGISTER.equals(loginType)) {
            String regWay = "";
            //上报注册埋点
            switch (loginWay) {
                case login_way_ali_fast:
                    regWay = register_way_ali_fast;
                    break;
                case login_way_phone_code:
                    regWay = register_way_phone_code;
                    break;
                case login_way_wechat:
                    regWay = register_way_wechat;
                    break;
                case login_way_quick_start:
                    regWay = register_way_quick;
                    break;
                default:
                    regWay = register_way_account;
                    break;
            }
            HashMap<String, String> map = new HashMap<>();
            map.put("uid", account.get("userid"));
            map.put("account_type", accountType);
            map.put("register_way", regWay);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.register_succ, map);

        }
        //上报登录新埋点
        HashMap<String, String> loginSuccMap = new HashMap<>();
        loginSuccMap.put(SqTrackKey.uid, account.get("userid"));
        loginSuccMap.put(SqTrackKey.login_type, accountType);
        loginSuccMap.put(SqTrackKey.login_way, loginWay);
        // 通知状态
        loginSuccMap.put("notification_status", areNotificationsEnabled() ? "1" : "0");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.login_succ, loginSuccMap);

        //设备评分上报
        DevicesInfo.doDeviceCollect(DevicesInfo.DEVICE_LOGIN_SUCC);
    }

    //调用登录失败
    public static void trackFail(String loginType, String loginWay, String code, String msg) {
        //上报调用登录埋点
        HashMap<String, String> map = new HashMap<>();
        map.put(SqTrackKey.login_type, loginType);
        map.put(SqTrackKey.login_way, loginWay);
        map.put(SqTrackKey.fail_code, code);
        map.put(SqTrackKey.reason_fail, msg);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.login_fail, map);

        //设备评分上报
        DevicesInfo.doDeviceCollect(DevicesInfo.DEVICE_LOGIN_FAIL);
    }

    /**
     * 微信返回登录成功
     */
    public static void trackWechatSuccess(String auth_code) {
        HashMap<String, String> map = new HashMap<>();
        map.put("wx_auth_code", auth_code);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.login_wechat_succ, map);
    }

    /**
     * 微信登录失败
     */
    public static void trackWechatFail(String msg) {
        HashMap<String, String> map = new HashMap<>();
        map.put("msg", msg);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.login_wechat_fail, map);
    }

    public static String parseAccountType(String accountType) {
        if (TextUtils.isEmpty(accountType)) {
            return TRACK_ACCOUNT_TYPE_ACCOUNT_NUM;
        }
        switch (accountType) {
            case AccountLoginType.LoginType.ACCOUNT_TYPE_PHONE:
                return TRACK_ACCOUNT_TYPE_PHONE_NUM;
            case AccountLoginType.LoginType.ACCOUNT_TYPE_WECHAT:
                return TRACK_ACCOUNT_TYPE_WECHAT_NUM;
            default:
                return TRACK_ACCOUNT_TYPE_ACCOUNT_NUM;
        }

    }

    private static boolean areNotificationsEnabled() {
        Context context = SQContextWrapper.getApplicationContext();
        if (context == null) {
            return false;
        }
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    public static void trackCheckAccountListSucc(String[] uid, String okUid) {
        HashMap<String, String> map = new HashMap<>();
        for (int i = 0; i < uid.length; i++) {
            map.put("uid" + (i + 1), uid[i]);
        }
        map.put("okUid", okUid);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.CHECK_ACCOUNT_LIST_SUCC, map);
    }

    public static void trackCheckAccountListFail(String failReason) {
        HashMap<String, String> map = new HashMap<>();
        map.put("reason_fail", failReason);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.CHECK_ACCOUNT_LIST_FAIL, map);
    }
}
