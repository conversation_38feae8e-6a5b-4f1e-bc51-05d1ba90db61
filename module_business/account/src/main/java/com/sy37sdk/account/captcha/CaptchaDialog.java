package com.sy37sdk.account.captcha;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;

import com.sqwan.common.webview.SQWebViewDialog;

/**
 *    author : lidaisheng
 *    time   : 2020/10/13
 *    desc   : 滑块验证弹窗
 */
public class CaptchaDialog extends SQWebViewDialog {

    private static final String SUCCESS_TAG = "1";

    private VerifyListener mListener;


    public CaptchaDialog(Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setOnCancelListener(new OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if(mListener != null) {
                    mListener.result(false, "取消验证");
                }
            }
        });
    }

    @Override
    protected void jsClose(String tag, String data) {
        super.jsClose(tag, data);
        dismiss();
        boolean isSuccess = !TextUtils.isEmpty(tag) && tag.equals(SUCCESS_TAG);
        if(mListener != null) {
            mListener.result(isSuccess, isSuccess ? "验证成功" : "验证失败");
        }
    }

    public void setVerifyListener(VerifyListener listener) {
        this.mListener = listener;
    }


    public interface VerifyListener {

        /**
         *
         * @param success  是否验证成功
         * @param msg
         */
        void result(boolean success, String msg);
    }

}
