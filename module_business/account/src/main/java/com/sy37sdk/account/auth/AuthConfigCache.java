package com.sy37sdk.account.auth;

import android.text.TextUtils;

import com.sqwan.common.util.TimeTools;

import org.json.JSONException;
import org.json.JSONObject;


public class AuthConfigCache {

    public static AuthBean authBean;


    public static String getAuthUrl() {
        if (authBean != null) {
            return authBean.getUrl();
        }
        return "";
    }

    public static String getPersonalDurl() {
        if (authBean != null) {
            return authBean.getDurl();
        }
        return "";
    }


    /**
     * 是否需要显示实名认证
     *
     * @return
     */
    public static boolean needAuth() {
        return authBean != null && authBean.needAuth() && !TextUtils.isEmpty(authBean.getUrl());
    }

    /**
     * 是否强制
     *
     * @return
     */
    public static boolean isAuthFocus() {
        return authBean != null && authBean.isFocus();
    }

    /**
     * 是否实名认证上报
     *
     * @return
     */
    public static boolean needAccumulateDuration() {
        return authBean != null && authBean.getNeedAccumulateDuration();
    }

    public static int getInterval() {
        if (authBean != null) {
            return authBean.getInterval();
        }
        return AuthBean.DEFAULT_INTERVAL;
    }

    /**
     * 是否防沉迷上报
     *
     * @return
     */
    public static boolean needAddiction() {
        return authBean != null && authBean.getNeedAddiction();
    }

    public static boolean allowRecharge() {
        if (authBean != null) {
            return authBean.getAllowRecharge();
        }
        return true;
    }

    /**
     * 获取实名制的信息
     */
    public static void saveAuthConfig(String authData) {
        try {
            JSONObject authJson = new JSONObject(authData);
            authBean = AuthBean.parseFromJson(authJson);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    public static void clearAuthConfig() {
        authBean = null;
    }

    public static void setIsAuth(boolean isAuth) {
        if (authBean != null) {
            authBean.setIsAuth(isAuth ? 1 : 0);
        }
    }

    public static boolean getIsAuth() {
        if (authBean != null) {
            return authBean.getIsAuth();
        }
        return false;
    }

    public static int getAuthLimitTime() {
        if (authBean != null) {
            return 1000 * authBean.getRemainingTime();
        } else {
            return 0;
        }
    }

    public static void autoCalAuthLimitTime() {
        if (authBean != null) {
            int interval = authBean.getInterval() * 60;
            int calRemainingTime = authBean.getRemainingTime() - interval;
            authBean.setRemainingTime(calRemainingTime);
        }
    }

    public static boolean isNeddReportOnline() {
        if (authBean != null) {
            return authBean.isNeddReportOnline();
        }
        return false;
    }

    public static void setNeedReportOnline(int needReportOnline) {
        if (authBean != null) {
            authBean.setNeedReportOnline(needReportOnline);
        }
    }

    /**
     * 获取年龄
     *
     * @return
     */
    public static int getAge() {
        return authBean != null ? authBean.getAge() : -1;
    }

    /**
     * 当前时间戳
     *
     * @return
     */

    //是否可玩日期
    public static boolean isPlayDate() {
        return authBean != null && authBean.isPlayableDate();
    }

    //timeStamp是否在可玩时间段
    public static boolean isPlayTimeRange(long timeStamp) {
        String playTimeRange = authBean != null ? authBean.getPlayableTimerange() : "";
        try {
            String[] timeSplit = playTimeRange.split("-");
            if (timeSplit == null || timeSplit.length != 2) {
                return false;
            }
            String startTime = timeSplit[0];
            String endTime = timeSplit[1];
            String[] startTimeSplit = startTime.split(":");
            if (startTimeSplit == null || startTimeSplit.length != 2) {
                return false;
            }
            String[] endTimeSplit = endTime.split(":");
            if (endTimeSplit == null || endTimeSplit.length != 2) {
                return false;
            }
            return TimeTools.isCurrentInTimeScope(timeStamp, Integer.parseInt(startTimeSplit[0]), Integer.parseInt(startTimeSplit[1]), Integer.parseInt(endTimeSplit[0]), Integer.parseInt(endTimeSplit[1]));
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取年龄限制弹窗url
     *
     * @return
     */
    public static String getTimeRangeLimitUrl() {
        return authBean != null ? authBean.getTimerangeLimtUrl() : "";
    }

    /**
     * 获取当前时间
     */
    public static long getTimeStamp() {
        return authBean != null ? authBean.getTimestamp() : 0;
    }

    /**
     * 是否版署认证了
     */
    public static boolean isBsAuth() {
        return authBean != null && authBean.getBanshuAuthState() == 1;
    }

    //是否成年人
    public static boolean isAdult() {
        return authBean != null && (18 - authBean.getAge() <= 0);
    }
}
