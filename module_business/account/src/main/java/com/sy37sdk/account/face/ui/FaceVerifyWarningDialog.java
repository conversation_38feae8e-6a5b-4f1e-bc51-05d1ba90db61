package com.sy37sdk.account.face.ui;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.msdk.api.SQResultListener;
import com.sy37sdk.account.UrlConstant;
import com.sy37sdk.account.face.FaceVerifyManager;
import com.sy37sdk.account.face.data.FaceVerifyData;
import com.sy37sdk.account.trackaction.PageExposureTrackManager;

/**
 * 人脸认证提醒弹窗
 */
public class FaceVerifyWarningDialog extends BaseDialog {
    private Context mContext;
    private TextView tvCancel, tvSure;
    private ImageView ivHelp;
    private final SQResultListener authResultListener;

    public FaceVerifyWarningDialog(Context context, SQResultListener authResultListener) {
        super(context);
        this.mContext = context;
        this.authResultListener = authResultListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(mContext, "sysq_face_warning_dialog"));
        PageExposureTrackManager.track(SqTrackPage.SqTrackViewId.face, SqTrackPage.SqTrackViewName.face);
        initView();
        initEvent();
    }

    private void initView() {
        ivHelp = findViewById(getIdByName("iv_help", "id"));
        tvCancel = findViewById(getIdByName("tv_cancel", "id"));
        tvSure = findViewById(getIdByName("tv_sure", "id"));
    }

    private void initEvent() {
        ivHelp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtils.toSQWebUrl(mContext, UrlConstant.FACE_VERIFY_HELP_PAGE, "帮助");
            }
        });
        tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FaceVerifyData faceVerifyData = FaceVerifyManager.getInstance(mContext).getFaceVerifyData();
                if (faceVerifyData == null) {
                    return;
                }
                if (mContext instanceof Activity) {
                    Intent intent = new Intent(mContext, FaceVerifyConfirmActivity.class);
                    intent.putExtra(FaceVerifyConfirmActivity.BUNDLE_USER_NAME, faceVerifyData.getName());
                    intent.putExtra(FaceVerifyConfirmActivity.BUNDLE_USER_CARD, faceVerifyData.getIdCardName());
                    intent.putExtra(FaceVerifyConfirmActivity.BUNDLE_VERIFY_TIP, faceVerifyData.getVerifyTip());
                    ((Activity) mContext).startActivityForResult(intent, FaceVerifyConfirmActivity.REQUEST_CODE);
                }
                dismiss();
            }
        });

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.changeAccount, SqTrackBtn.SqTrackBtnExt.CHANGE_ACCOUNT_FACE_TIP);
                ModHelper.get(IAccountMod.class).webEnLogin(false);
                dismiss();

                if (authResultListener != null) {
                    authResultListener.onFailture(203, "取消人脸识别，登录失败");
                }

            }
        });
    }
}
