package com.sy37sdk.account.trackaction;

import android.text.TextUtils;
import android.util.Log;

import com.sqwan.bugless.core.Bugless;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.util.LogUtil;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-03-03 15:54
 */
public class UserNameEmptyTrackAction {
    public enum ActionType {
        saveAccout,
        active,
        login,
        modifyPass
    }
    public static void report(ActionType type,String uname ,String businessData) {
        if (TextUtils.isEmpty(uname)) {
            LogUtil.e("UserNameEmptyTrackAction report");
            BuglessAction.reportCatchException(new Exception(type.toString()), type.toString(), businessData, BuglessAction.LOGIN_USERNAME_EMPTY);
        }
    }

}
