package com.sy37sdk.account.activebefore;

import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 描述:
 * 作者：znb
 * 时间：2021-04-20 11:17
 */
public class UserProtocolInfo extends ActiveBeforeBaseInfo{
    public String userprotol = "";
    public String privacyprotol = "";
    @Override
    public void parse(String jsonData){
        try {
            JSONObject jsonObject = new JSONObject(jsonData);
            String urlProtocol = jsonObject.optString("url_protocol");
            String urlPolicy = jsonObject.optString("url_policy");
            if (!TextUtils.isEmpty(urlProtocol)) {
                this.userprotol = urlProtocol+"&isAgree=true";
            }
            if (!TextUtils.isEmpty(urlPolicy)) {
                this.privacyprotol = urlPolicy+"&isAgree=true";
            }
        } catch (JSONException e) {
            e.printStackTrace();

        }
    }

    @Override
    public String toString() {
        return "UserProtocolInfo{" +
                "userprotol='" + userprotol + '\'' +
                ", privacyprotol='" + privacyprotol + '\'' +
                '}';
    }
}
