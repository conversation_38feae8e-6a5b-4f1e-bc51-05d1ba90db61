apply plugin: 'com.android.library'

def isPluginMode = get_isPluginMode()

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {

        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"

        //2、android标签内defaultConfig标签处理
        defaultConfig {
            //...
            //增加如下配置,其中com.sq.sqinjectdemo换成对应包名，如果是lib工程，对应成lib工程R文件的包名
            javaCompileOptions {
                annotationProcessorOptions {
                    arguments = [packageName: 'com.sy37sdk.account.eventbus', className: 'EventBusIndex']
                }
            }
        }

        // 支持 Java JDK 8
        compileOptions {
            targetCompatibility JavaVersion.VERSION_1_8
            sourceCompatibility JavaVersion.VERSION_1_8
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':37sdkcommon')
    annotationProcessor 'com.37sy.android:sqeventbus-compile:1.0.0'
    api 'com.37sy.android:sqeventbus-annotation:1.0.0'
    api 'com.37sy.android:sqeventbus:1.0.0'
    // 第三方登录和分享
    api 'com.37sy.android:social_sdk:2.0.8'
    // 通过transform移除jar
    api project(':module_third:social_sdk')
    api ('com.android.support:recyclerview-v7:26.0.0')
    api project(':window')
    if (!isPluginMode) {
        // 阿里闪验
        implementation project(':module_third:ali_auth:ali_auth_sdk')
        // 阿里人脸验证
        implementation project(':module_third:ali_face:ali_face_sdk')
        // 阿里支付
        implementation project(':module_third:ali_pay:ali_pay_sdk')
    } else {
        // 插件下阿里闪验在插件宿主中, 所以不打进插件apk
        compileOnly project(':module_third:ali_auth:ali_auth_sdk')
        // 插件下阿里人脸认证在插件宿主中, 所以不打进插件apk
        compileOnly project(':module_third:ali_face:ali_face_sdk')
        // 插件下阿里支付在插件宿主中, 所以不打进插件apk
        compileOnly project(':module_third:ali_pay:ali_pay_sdk')
    }
    implementation project(path: ':websocket:websocket_engine')

    // zxing 扫码
    api project(':module_third:zxing_scan:zxing_sdk')

    // taptap跳转
    api project(':module_third:taptap:taptap_sdk')
}
