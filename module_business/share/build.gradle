apply plugin: 'com.android.library'

def isPluginMode = get_isPluginMode()

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    //2、android标签内defaultConfig标签处理
    defaultConfig {
        //...
        //增加如下配置,其中com.sq.sqinjectdemo换成对应包名，如果是lib工程，对应成lib工程R文件的包名
        javaCompileOptions {
            annotationProcessorOptions {
                includeCompileClasspath = true
                arguments = [packageName: 'com.sy37sdk.plugin.eventbus', className: 'EventBusIndex']
            }
        }
    }

    buildTypes {

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':37sdkcommon')
    // 第三方登录和分享
    api 'com.37sy.android:social_sdk:2.0.8'
    // 通过transform移除jar
    api project(':module_third:social_sdk')
}