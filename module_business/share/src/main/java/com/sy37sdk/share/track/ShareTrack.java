package com.sy37sdk.share.track;

public interface ShareTrack {

    interface ShareTrackKey {
        //分享平台：qq/微信
        String platform = "platform";
        //分享种类：37默认分享/图片/h5/文本
        String classify = "classify";
        //是否跳过分享预览界面
        String skipPreview = "skipPreview";

        String message = "message";
    }


    interface ShareTrackMsg {
        String share_message_null = "cp传入分享内容空";
        String share_info_null = "分享内容空";
        String share_not_classify = "不支持的分享种类";
        String share_not_install = "未安装客户端";
        String share_cancel = "分享取消";
        String share_close = "关闭分享";
        String share_param_error = "没有配置分享参数";
    }

}
