package com.sy37sdk.share.bean;

import android.graphics.Bitmap;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ShareBean {
    private int type = ShareType.SHARE_IMG;
    private String img;
    private String title;
    private String desc;
    private String landingPageUrl;
    private String text;
    private Bitmap bitmap;

    private List<Integer> sharePlatforms;

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLandingPageUrl() {
        return landingPageUrl;
    }

    public void setLandingPageUrl(String landingPageUrl) {
        this.landingPageUrl = landingPageUrl;
    }

    public List<Integer> getShareWays() {
        return sharePlatforms;
    }

    public void setShareWays(List<Integer> shareWays) {
        this.sharePlatforms = shareWays;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public void parse(String content) {
        try {
            JSONObject jsonObject = new JSONObject(content);
            String img = jsonObject.optString("img");
            String title = jsonObject.optString("title");
            String desc = jsonObject.optString("desc");
            String landingPageUrl = jsonObject.optString("landingPageUrl");
            setDesc(desc);
            setImg(img);
            setTitle(title);
            setLandingPageUrl(landingPageUrl);
            parseShareType(jsonObject);
            parseShareWay(jsonObject);

        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    //解析分享类型
    private void parseShareType(JSONObject jsonObject) {
        String typeStr = jsonObject.optString("type");
        try {
            int type = Integer.parseInt(typeStr);
            if (type == 2) {
                setType(ShareType.SHARE_WEB);
            } else {
                setType(ShareType.SHARE_IMG);
            }
        } catch (Exception e) {
            e.printStackTrace();
            setType(ShareType.SHARE_IMG);
        }
    }

    //解析分享方式
    private void parseShareWay(JSONObject jsonObject) {
        try {
            List<Integer> shareWays = new ArrayList<>();
            JSONArray wayArray = jsonObject.optJSONArray("way");
            for (int i = 0; i < wayArray.length(); i++) {
                String way = (String) wayArray.get(i);
                int platformType = ShareWay.SHARE_WECHAT;
                switch (way) {
                    case "wechat":
                        platformType = ShareWay.SHARE_WECHAT;
                        break;
                    case "moments":
                        platformType = ShareWay.SHARE_MOMENTS;
                        break;
                    case "qq":
                        platformType = ShareWay.SHARE_QQ;
                        break;
                }
                shareWays.add(platformType);
            }
            setShareWays(shareWays);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
