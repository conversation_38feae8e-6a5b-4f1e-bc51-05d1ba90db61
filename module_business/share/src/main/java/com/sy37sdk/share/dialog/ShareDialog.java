package com.sy37sdk.share.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.social.sdk.SocialApi;
import com.social.sdk.common.listener.ShareListener;
import com.social.sdk.platform.PlatformType;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.CutoutUtil;
import com.sqwan.common.util.ImageUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.MD5Util;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.share.ShareImageHandler;
import com.sy37sdk.share.ShareManager;
import com.sy37sdk.share.bean.ShareBean;
import com.sy37sdk.share.bean.ShareMediaParser;
import com.sy37sdk.share.bean.ShareWay;
import com.sy37sdk.share.track.ShareTrack;

import java.io.File;
import java.util.HashMap;
import java.util.List;

public class ShareDialog extends BaseDialog {

    private Context mContext;

    private ShareBean shareBean;

    private IShareResultListener mListener;

    //是否忽略判断分享方式是否存在
    private boolean ignoreShareWay;

    public ShareDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        RelativeLayout rootView;
        Configuration configuration = mContext.getResources().getConfiguration();
        int orientation = configuration.orientation;
        int imgWidth, imgHeight;
        int screenHeight = DisplayUtil.getScreenHeight(mContext);
        int[] landscapeLimit = new int[2];
        int[] portraitLimit = new int[2];
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            imgHeight = screenHeight - DisplayUtil.dip2px(mContext, 120);
            imgWidth = 16 * imgHeight / 9;

            rootView = (RelativeLayout) getLayoutInflater().inflate(getIdByName("sy37_share_dialog_landscape", "layout"), null, false);
            landscapeLimit[0] = imgWidth;
            landscapeLimit[1] = imgHeight;
        } else {
            imgHeight = screenHeight - DisplayUtil.dip2px(mContext, 180);
            imgWidth = imgHeight * 9 / 16;
            portraitLimit[0] = imgWidth;
            portraitLimit[1] = imgHeight;
            rootView = (RelativeLayout) getLayoutInflater().inflate(getIdByName("sy37_share_dialog_portrait", "layout"), null, false);
        }
        setContentView(rootView);
        HashMap<String, String> extra = new HashMap<>();
        extra.put(SqTrackKey.view_id, SqTrackPage.SqTrackViewId.share_preview);
        extra.put(SqTrackKey.view_name, SqTrackPage.SqTrackViewName.share_preview);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_view_show, extra);

        //按照比例动态计算图片实际展示的宽高
        int[] actualSize = ShareImageHandler.handleSize(mContext, shareBean.getBitmap(), landscapeLimit, portraitLimit);

        RelativeLayout rlImg = rootView.findViewById(getIdByName("rl_img", "id"));
        RelativeLayout.LayoutParams rlImgLayoutParams = (RelativeLayout.LayoutParams) rlImg.getLayoutParams();
        rlImgLayoutParams.width = imgWidth + 2 * DisplayUtil.dip2px(mContext, 5);
        rlImgLayoutParams.height = imgHeight + 2 * DisplayUtil.dip2px(mContext, 5);
        rlImg.setLayoutParams(rlImgLayoutParams);

        int statusBarHeight = StatusBarUtil.getStatusBarHeight(mContext);
        ImageView img = rootView.findViewById(getIdByName("imageView", "id"));
        img.setImageBitmap(shareBean.getBitmap());
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) img.getLayoutParams();
        layoutParams.width = actualSize[0];
        layoutParams.height = actualSize[1];
        //对刘海屏做适配
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            boolean hasCutout = CutoutUtil.hasNotchScreen((Activity) mContext);
            if (hasCutout) {
                rlImgLayoutParams.topMargin = statusBarHeight;
                rlImg.setLayoutParams(rlImgLayoutParams);
            }
        }
        img.setLayoutParams(layoutParams);
        View close = rootView.findViewById(getIdByName("close", "id"));
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, String> shareFailMap = new HashMap<>();
                shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_close);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                if (mListener != null) {
                    mListener.onFailture(203, "分享取消");
                }
                dismiss();
            }
        });
        initView(rootView);
    }


    private void initView(View rootView) {
        View shareRootView = rootView.findViewById(SqResUtils.getIdByName("ll_share_way", "id", mContext));
        List<Integer> shareWays = shareBean.getShareWays();
        if (!ignoreShareWay) {
            if (shareWays == null || shareWays.isEmpty()) {
                shareRootView.setVisibility(View.GONE);
                return;
            }
        }

        View shareWechatView = rootView.findViewById(SqResUtils.getIdByName("share_wx", "id", mContext));

        View shareMomentView = rootView.findViewById(SqResUtils.getIdByName("share_wx_circle", "id", mContext));

        View shareQQView = rootView.findViewById(SqResUtils.getIdByName("share_qq", "id", mContext));

        View shareMoreView = rootView.findViewById(SqResUtils.getIdByName("share_more", "id", mContext));

        //不需要判断分享分时，则分享按钮全部展示
        if (ignoreShareWay) {
            shareWechatView.setVisibility(View.VISIBLE);
            shareMomentView.setVisibility(View.VISIBLE);
            shareQQView.setVisibility(View.VISIBLE);
            shareMoreView.setVisibility(View.VISIBLE);
        } else {
            //需要根据分享方式来决定展示哪种分享途径
            for (int way : shareWays) {
                switch (way) {
                    case ShareWay.SHARE_WECHAT:
                        shareWechatView.setVisibility(View.VISIBLE);
                        break;
                    case ShareWay.SHARE_MOMENTS:
                        shareMomentView.setVisibility(View.VISIBLE);
                        break;
                    case ShareWay.SHARE_QQ:
                        shareQQView.setVisibility(View.VISIBLE);
                        break;
                    case ShareWay.SHARE_SYSTEM:
                        shareMoreView.setVisibility(View.VISIBLE);
                        break;
                }
            }
        }

        //点击微信分享
        shareWechatView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.share_wechat, SqTrackBtn.SqTrackBtnExt.share_wechat);
                if (!checkInstallWX()) {
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_not_install + "微信");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                    return;
                }
                if (supportWx()) {
                    share(PlatformType.WECHAT);
                    dismiss();
                } else {
                    shareOriginal(ShareWay.SHARE_WECHAT);
                }

            }
        });
        //点击朋友圈分享
        shareMomentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.share_moment, SqTrackBtn.SqTrackBtnExt.share_moment);
                if (!checkInstallWX()) {
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_not_install + "微信");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                    return;
                }
                if (supportWx()) {
                    share(PlatformType.WECHAT_CIRCLE);
                    dismiss();
                } else {
                    shareOriginal(ShareWay.SHARE_MOMENTS);
                }
            }
        });
        //点击qq分享
        shareQQView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.share_qq, SqTrackBtn.SqTrackBtnExt.share_qq);
                if (!checkInstallQQ()) {
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_not_install + "qq");
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                    return;
                }
                if (supportQQ()) {
                    share(PlatformType.QQ);
                    dismiss();
                } else {
                    shareOriginal(ShareWay.SHARE_QQ);
                }
            }
        });

        // 点击系统分享
        shareMoreView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.share_system, SqTrackBtn.SqTrackBtnExt.share_system);
                // 通过系统分享
                ShareManager.getInstance(getContext()).shareToSystem(shareBean, mListener);
                dismiss();
            }
        });
    }

    /**
     * 打开第三方客户端
     */
    private void shareOriginal(int shareWay) {
        String fileName;
        if (TextUtils.isEmpty(shareBean.getImg())) {
            fileName = MD5Util.Md5(ShareImageHandler.bitmapToBase64(shareBean.getBitmap())) + ".jpg";
            LogUtil.i("存储路径：" + fileName);
        } else {
            fileName = MD5Util.Md5(shareBean.getImg());
        }
        Uri uri = ImageUtils.save(mContext, shareBean.getBitmap(), fileName + ".jpg");
        if (uri == null) {
            showTipDailog();
            return;
        }
        ShareConfirmDialog confirmDialog = new ShareConfirmDialog(mContext).setShareBean(shareBean).setShareWay(shareWay).setListener(mListener);
        confirmDialog.show();
    }

    /**
     * 保存图片失败时，弹提示框
     */
    private void showTipDailog() {
        ShareTipDialog shareTipDialog = new ShareTipDialog(mContext);
        shareTipDialog.show();
    }

    /**
     * 判断是否安装了微信
     *
     * @return
     */
    private boolean checkInstallWX() {
        boolean isInstall = checkInstall("com.tencent.mm");
        if (!isInstall) {
            ViewUtils.showToast(mContext, "请先安装微信客户端");
        }
        return isInstall;
    }

    /**
     * 判断是否申请了微信参数
     *
     * @return
     */
    private boolean supportWx() {
        try {
            ApplicationInfo info = mContext.getPackageManager().getApplicationInfo(mContext.getPackageName(), PackageManager.GET_META_DATA);
            String wx_appid = info.metaData.getString("wx_appid");
            return !TextUtils.isEmpty(wx_appid);
        } catch (Exception exception) {
            exception.printStackTrace();
            LogUtil.e("未找到微信参数");
            return false;
        }
    }

    /**
     * 判断是否安装了微信
     *
     * @return
     */
    private boolean checkInstallQQ() {
        boolean isInstall = checkInstall("com.tencent.mobileqq");
        if (!isInstall) {
            ViewUtils.showToast(mContext, "请先安装qq客户端");
        }
        return isInstall;
    }

    /**
     * 检测应用是否安装
     *
     * @param packageName
     * @return
     */
    private boolean checkInstall(String packageName) {
        try {
            mContext.getPackageManager().getApplicationInfo(packageName, PackageManager.GET_UNINSTALLED_PACKAGES);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 判断是否申请了微信参数
     *
     * @return
     */
    private boolean supportQQ() {
        try {
            ApplicationInfo info = mContext.getPackageManager().getApplicationInfo(mContext.getPackageName(), PackageManager.GET_META_DATA);
            String wx_appid = info.metaData.getString("qq_appid");
            return !TextUtils.isEmpty(wx_appid);
        } catch (Exception exception) {
            exception.printStackTrace();
            LogUtil.e("未找到qq参数");
            return false;
        }
    }

    private void share(PlatformType platform) {
        SocialApi.getInstance().share((Activity) mContext, platform, ShareMediaParser.parse(shareBean), mShareListener);
    }

    public ShareDialog setShareBean(ShareBean shareBean) {
        this.shareBean = shareBean;
        return this;
    }

    public ShareDialog ignoreShareWay(boolean ignoreShareWay) {
        this.ignoreShareWay = ignoreShareWay;
        return this;
    }

    public ShareDialog setListener(IShareResultListener listener) {
        this.mListener = listener;
        return this;
    }

    private ShareListener mShareListener = new ShareListener() {

        @Override
        public void onSuccess(PlatformType platform) {
            LogUtil.i(" share onSuccess");
            HashMap<String, String> shareMap = new HashMap<>();
            shareMap.put(ShareTrack.ShareTrackKey.platform, platform.name());
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);
            if (mListener != null) {
                mListener.onSuccess(new Bundle());
            }
        }

        @Override
        public void onCancel(PlatformType platform) {
            LogUtil.i(" share onCancel");
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, platform.name());
            shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_cancel);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            if (mListener != null) {
                mListener.onFailture(203, "分享取消");
            }
        }

        @Override
        public void onFailure(PlatformType platform, String msg) {
            LogUtil.i(" share onFailure platform: " + platform.name() + " message: " + msg);
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, platform.name());
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            ViewUtils.showToast(mContext, "分享失败");
            if (mListener != null) {
                mListener.onFailture(203, "分享失败：" + msg);
            }
        }
    };
}
