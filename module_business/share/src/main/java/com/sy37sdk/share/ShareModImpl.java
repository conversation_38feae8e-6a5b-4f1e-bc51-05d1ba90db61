package com.sy37sdk.share;

import android.content.Context;
import com.parameters.share.ShareMessage;
import com.sqwan.common.mod.share.IShareMod;
import com.sqwan.common.mod.share.IShareResultListener;

public class ShareModImpl implements IShareMod {

    private final Context mContext;

    public ShareModImpl(Context context) {
        mContext = context;
    }

    @Override
    public void share(ShareMessage message, IShareResultListener listener) {
        ShareCoreManager.getInstance(mContext).share(message,listener);
    }
}
