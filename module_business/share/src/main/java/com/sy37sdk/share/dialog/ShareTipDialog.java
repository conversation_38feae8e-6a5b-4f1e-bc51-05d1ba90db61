package com.sy37sdk.share.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;

import com.sqwan.common.util.SqResUtils;

public class ShareTipDialog extends Dialog {
    public ShareTipDialog(Context context) {
        super(context, SqResUtils.getStyleId(context,"CustomDialog"));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(SqResUtils.getLayoutId(getContext(), "sy37_dialog_share_fail"));
        setCanceledOnTouchOutside(false);
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        final Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = display.getWidth();
        getWindow().setAttributes(params);
        getWindow().setWindowAnimations(SqResUtils.getStyleId(getContext(), "dialogWindowAnim"));
        findViewById(SqResUtils.getId(getContext(), "tv_tips_ok")).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }
}
