package com.sy37sdk.share;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.util.Base64;

import com.sqwan.common.util.LogUtil;

import java.io.ByteArrayOutputStream;

public class ShareImageHandler {

    /**
     * 计算图片实际需要展示的宽高
     *
     * @param context   上下文
     * @param bitmap    图片
     * @param landscape 横屏时最大展示的图片宽、高
     * @param portrait  竖屏时最大展示的图片宽、高
     */
    public static int[] handleSize(Context context, Bitmap bitmap, int[] landscape, int[] portrait) {
        if (bitmap == null) return new int[]{0, 0};
        Configuration configuration = context.getResources().getConfiguration();
        int orientation = configuration.orientation;
        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();
        LogUtil.i("原始图片宽：" + bitmapWidth + " 高" + bitmapHeight);
        LogUtil.i("横屏时最大宽高：" + landscape[0]+"  "+landscape[1] + " 竖屏时最大宽高：" + portrait[0]+" "+portrait[1]);
        float ratio = 16.0f / 9;
        LogUtil.i("宽高比：" + ratio);
        float ratioLandscape = ((float) bitmapWidth) / bitmapHeight;
        float ratioPortrait = ((float) bitmapHeight) / bitmapWidth;
        int widthActual, heightActual;
        //横屏
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (ratioLandscape >= ratio) {
                //以宽为基准
                widthActual = landscape[0];
                heightActual = bitmapHeight * widthActual / bitmapWidth;
            } else {
                //以高为基准
                heightActual = landscape[1];
                widthActual = bitmapWidth * heightActual / bitmapHeight;
            }
        } else {
            if (ratioPortrait >= ratio) {
                heightActual = portrait[1];
                widthActual = bitmapWidth * heightActual / bitmapHeight;
            } else {
                widthActual = portrait[0];
                heightActual = bitmapHeight * widthActual / bitmapWidth;
            }
        }
        LogUtil.i("计算出的实际宽高 width = " + widthActual + " height = " + heightActual);
        return new int[]{widthActual, heightActual};
    }

    /**
     * bitmap转base64
     *
     * @param bitmap
     * @return
     */
    public static String bitmapToBase64(Bitmap bitmap) {
        String result = "";
        ByteArrayOutputStream baos = null;
        try {
            if (bitmap != null) {
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos);
                byte[] bytes = baos.toByteArray();
                result = Base64.encodeToString(bytes, Base64.DEFAULT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.flush();
                    baos.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return result;
    }

}
