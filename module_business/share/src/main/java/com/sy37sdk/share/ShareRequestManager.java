package com.sy37sdk.share;

import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sqwan.common.request.CommonParamsV2;


public class ShareRequestManager {

    /**
     * 获取分享物料
     * type:分享类型 1: 图片 2:H5
     */
    public void getShareSources(String inviteCode, String img_id, SqHttpCallback<String> callback) {
        SqRequest.of(UrlConstant.GET_SHARE_SOURCE)
            .signV3()
            .addParam("invitecode", inviteCode)
            .addParam("os", "android")
            .addParam("img_id", img_id)
            .addParamsTransformer(new CommonParamsV2())
            .post(callback, String.class);
    }
}
