package com.sy37sdk.share.dialog;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.social.sdk.platform.PlatformType;
import com.sq.sdk.tool.util.DisplayUtil;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.mvp.BaseDialog;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackBtn;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackPage;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.share.ShareImageHandler;
import com.sy37sdk.share.bean.ShareBean;
import com.sy37sdk.share.bean.ShareWay;
import com.sy37sdk.share.track.ShareTrack;

import java.util.HashMap;

/**
 * 提示用户保存图片成功，手动打开第三方应用
 */
public class ShareConfirmDialog extends BaseDialog {

    private Context mContext;

    private ShareBean shareBean;

    private int shareWay = ShareWay.SHARE_WECHAT;

    private IShareResultListener mListener;

    public ShareConfirmDialog(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        View rootView;
        Configuration configuration = mContext.getResources().getConfiguration();
        int orientation = configuration.orientation;
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            rootView = getLayoutInflater().inflate(getIdByName("share_confirm_dialog_portrait", "layout"), null, false);
        } else {
            rootView = getLayoutInflater().inflate(getIdByName("share_confirm_dialog_landscape", "layout"), null, false);
        }
        setContentView(rootView);
        HashMap<String, String> extra = new HashMap<>();
        extra.put(SqTrackKey.view_id, SqTrackPage.SqTrackViewId.share_confirm);
        extra.put(SqTrackKey.view_name, SqTrackPage.SqTrackViewName.share_confirm);
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_view_show, extra);
        ImageView imgPreview = rootView.findViewById(getIdByName("img_preview", "id"));
        imgPreview.setImageBitmap(shareBean.getBitmap());
        //竖屏时最大展示的宽高
        int[] portraitLimit = {DisplayUtil.dip2px(mContext, 225f), DisplayUtil.dip2px(mContext, 400f)};
        //横屏时最大展示的宽高
        int[] landscapeLimit = {DisplayUtil.dip2px(mContext, 240f), DisplayUtil.dip2px(mContext, 135f)};
        //重新计算后的图片宽高
        int[] actualSize = ShareImageHandler.handleSize(mContext, shareBean.getBitmap(), landscapeLimit, portraitLimit);
        //重新设置imagview宽高
        ViewGroup.LayoutParams layoutParams = imgPreview.getLayoutParams();
        layoutParams.width = actualSize[0];
        layoutParams.height = actualSize[1];
        imgPreview.setLayoutParams(layoutParams);
        initView(rootView);
    }

    public ShareConfirmDialog setShareBean(ShareBean shareBean) {
        this.shareBean = shareBean;
        return this;
    }

    public ShareConfirmDialog setShareWay(int shareWay) {
        this.shareWay = shareWay;
        return this;
    }

    public ShareConfirmDialog setListener(IShareResultListener listener) {
        this.mListener = listener;
        return this;
    }

    private void initView(View rootView) {
        View close = rootView.findViewById(getIdByName("close", "id"));
        close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
        View openPlatform = rootView.findViewById(getIdByName("open_platform", "id"));
        openPlatform.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SqTrackActionManager2.getInstance().trackBtn(SqTrackBtn.SqTrackBtnId.share_open_platform, SqTrackBtn.SqTrackBtnExt.share_open_platform);
                PlatformType platformType = PlatformType.WECHAT;
                switch (shareWay) {
                    case ShareWay.SHARE_WECHAT:
                        startPlatform("com.tencent.mm", platformType);
                        break;
                    case ShareWay.SHARE_MOMENTS:
                        platformType = PlatformType.WECHAT_CIRCLE;
                        startPlatform("com.tencent.mm", platformType);
                        break;
                    case ShareWay.SHARE_QQ:
                        platformType = PlatformType.QQ;
                        startPlatform("com.tencent.mobileqq", platformType);
                        break;
                    default:
                        HashMap<String, String> shareFailMap = new HashMap<>();
                        shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                        shareFailMap.put(SqTrackKey.reason_fail, "不支持的分享平台" + shareWay);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                        break;
                }
                dismiss();
            }
        });

        TextView tvShareWay = rootView.findViewById(SqResUtils.getIdByName("tv_share_way", "id", mContext));
        switch (shareWay) {
            case ShareWay.SHARE_WECHAT:
            case ShareWay.SHARE_MOMENTS:
                tvShareWay.setText("去微信分享给好友");
                openPlatform.setBackgroundResource(SqResUtils.getIdByName("sy37_open_wx_bg", "drawable", mContext));
                break;
            case ShareWay.SHARE_QQ:
                tvShareWay.setText("去QQ分享给好友");
                openPlatform.setBackgroundResource(SqResUtils.getIdByName("sy37_open_qq_bg", "drawable", mContext));
                break;
        }
    }

    /**
     * 启动客户端
     */
    private void startPlatform(String packageName, PlatformType platformType) {
        try {
            Intent component = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            Intent intent = new Intent(Intent.ACTION_MAIN);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setComponent(component.getComponent());
            mContext.startActivity(intent);
            HashMap<String, String> shareMap = new HashMap<>();
            shareMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
            shareMap.put(ShareTrack.ShareTrackKey.message, "直接启动客户端");
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);
            if (mListener != null) {
                mListener.onSuccess(new Bundle());
            }
        } catch (Exception e) {
            e.printStackTrace();
            ViewUtils.showToast(mContext, "打开客户端失败");
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
            shareFailMap.put(SqTrackKey.reason_fail, e.toString());
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
        }
    }
}
