package com.sy37sdk.share;

import android.accounts.AccountManager;
import android.app.Activity;
import android.content.Context;
import com.parameters.share.DefaultShareInfo;
import com.parameters.share.IShareInfo;
import com.parameters.share.ShareImageInfo;
import com.parameters.share.ShareMessage;
import com.parameters.share.SharePlatform;
import com.parameters.share.ShareTextInfo;
import com.parameters.share.ShareWebInfo;
import com.social.sdk.platform.PlatformType;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.share.bean.ShareBean;
import com.sy37sdk.share.bean.ShareType;
import com.sy37sdk.share.track.ShareTrack;
import java.util.HashMap;

public class ShareCoreManager {
    private static volatile ShareCoreManager instance;

    private Context context;

    private Activity mActivity;

    private ShareCoreManager(Context context) {
        if (context instanceof Activity) {
            mActivity = (Activity) context;
        }
        this.context = context.getApplicationContext();
    }

    public static ShareCoreManager getInstance(Context context) {
        if (instance == null) {
            synchronized (AccountManager.class) {
                if (instance == null) {
                    instance = new ShareCoreManager(context);
                }
            }
        }
        return instance;
    }

    //统一的分享入口
    public void share(ShareMessage message, final IShareResultListener listener) {
        HashMap<String, String> shareInvokeMap = new HashMap<>();
        shareInvokeMap.put(ShareTrack.ShareTrackKey.platform, getPlatformName(message));
        shareInvokeMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(message));
        shareInvokeMap.put(ShareTrack.ShareTrackKey.skipPreview, message == null ? "" : message.isSkipPreview() + "");
        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_invoke, shareInvokeMap);

        if (message == null) {
            String msg = "分享失败，ShareMessage 为空";
            LogUtil.e(msg);
            if (listener != null) {
                listener.onFailture(203, msg);
            }
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, getPlatformName(message));
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(message));
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            return;
        }
        IShareInfo shareInfo = message.getShareMessage();
        if (shareInfo == null) {
            String msg = "分享失败，ShareInfo 为空";
            LogUtil.i(msg);
            if (listener != null) {
                listener.onFailture(203, msg);
            }
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, getPlatformName(message));
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(message));
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            return;
        }
        LogUtil.i("classify=" + getClassifyType(message));
        switch (shareInfo.classify()) {
            //37平台的分享，邀请码必传,走37平台的分享
            case IShareInfo.ShareClassify
                    .SHARE_DEFAULT:
                DefaultShareInfo defaultShareInfo = (DefaultShareInfo) shareInfo;
                ShareManager.getInstance(mActivity).getShareSources(defaultShareInfo.getInviteCode(), defaultShareInfo.getImgId(), listener);
                break;
            case IShareInfo.ShareClassify.SHARE_IMG:
                //图片分享,直接传图
                ShareImageInfo shareImageInfo = (ShareImageInfo) shareInfo;
                ShareBean shareImage = new ShareBean();
                shareImage.setBitmap(shareImageInfo.getBitmap());
                shareBranch(message, shareImage, listener);
                break;
            case IShareInfo.ShareClassify.SHARE_WEB:
                //h5分享,直接传h5
                ShareWebInfo shareWebInfo = (ShareWebInfo) shareInfo;
                ShareBean shareWeb = new ShareBean();
                shareWeb.setType(ShareType.SHARE_WEB);
                shareWeb.setBitmap(shareWebInfo.getThumbBmp());
                shareWeb.setDesc(shareWebInfo.getDesc());
                shareWeb.setTitle(shareWebInfo.getTitle());
                shareWeb.setLandingPageUrl(shareWebInfo.getPageUrl());
                shareBranch(message, shareWeb, listener);
                break;
            case IShareInfo.ShareClassify.SHARE_TEXT:
                // 文本分享
                ShareTextInfo shareTextInfo = (ShareTextInfo) shareInfo;
                ShareBean shareText = new ShareBean();
                shareText.setType(ShareType.SHARE_TEXT);
                shareText.setText(shareTextInfo.getText());
                shareBranch(message, shareText, listener);
                break;
            default:
                String msg = "分享失败，不支持的分享内容类型";
                LogUtil.e(msg);
                if (listener != null) {
                    listener.onFailture(203, msg);
                }
                HashMap<String, String> shareFailMap = new HashMap<>();
                shareFailMap.put(ShareTrack.ShareTrackKey.platform, getPlatformName(message));
                shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(message));
                shareFailMap.put(SqTrackKey.reason_fail, msg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                break;
        }
    }

    //分享流程
    private void shareBranch(ShareMessage message, ShareBean shareBean, final IShareResultListener listener) {
        //是否跳过分享预览页
        if (message.isSkipPreview()) {
            //直接分享至第三方平台
            ShareManager.getInstance(mActivity).shareDirectly(message.getPlatform(), shareBean, listener);
        } else {
            //通过分享预览页再分享至第三方平台
            ShareManager.getInstance(mActivity).share(shareBean, true, listener);
        }
    }

    private String getPlatformName(ShareMessage message) {
        if (message == null) {
            return "";
        }
        int platformType = message.getPlatform();
        switch (platformType) {
            case SharePlatform.WECHAT:
                return PlatformType.WECHAT.name();
            case SharePlatform.WECHAT_MOMENT:
                return PlatformType.WECHAT_CIRCLE.name();
            case SharePlatform.QQ:
                return PlatformType.QQ.name();
            case SharePlatform.SYSTEM:
                return "SYSTEM";
            default:
                return "";
        }
    }

    private String getClassifyType(ShareMessage message) {
        if (message == null) {
            return "";
        }
        IShareInfo shareMessage = message.getShareMessage();
        if (shareMessage == null) {
            return "";
        }
        return String.valueOf(shareMessage.classify());
    }
}
