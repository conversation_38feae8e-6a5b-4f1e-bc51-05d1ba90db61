package com.sy37sdk.share.bean;

import com.social.sdk.share.media.IShareMedia;
import com.social.sdk.share.media.ShareImageMedia;
import com.social.sdk.share.media.ShareWebMedia;

import static com.sy37sdk.share.bean.ShareType.SHARE_IMG;
import static com.sy37sdk.share.bean.ShareType.SHARE_WEB;


public class ShareMediaParser {

    public static IShareMedia parse(ShareBean shareBean) {
        switch (shareBean.getType()) {
            case SHARE_IMG:
                ShareImageMedia imgMedia = new ShareImageMedia();
                imgMedia.setImage(shareBean.getBitmap());
                return imgMedia;
            case SHARE_WEB:
                ShareWebMedia webMedia = new ShareWebMedia();
                webMedia.setTitle(shareBean.getTitle());
                webMedia.setDescription(shareBean.getDesc());
                webMedia.setWebPageUrl(shareBean.getLandingPageUrl());
                webMedia.setThumb(shareBean.getBitmap());
                return webMedia;
            default:
                ShareImageMedia media = new ShareImageMedia();
                media.setImage(shareBean.getBitmap());
                return media;
        }
    }
}
