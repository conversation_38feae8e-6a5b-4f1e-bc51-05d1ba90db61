package com.sy37sdk.share;

import android.text.TextUtils;

import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.msdk.config.MultiSdkManager;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;

public class UrlConstant {
    public static final String KEY_SHARE = "share";

    /**
     * 获取分享物料
     */
    @UrlUpdate(value = KEY_SHARE, xValue = "x_share")
    public static String GET_SHARE_SOURCE= "https://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/share";

}
