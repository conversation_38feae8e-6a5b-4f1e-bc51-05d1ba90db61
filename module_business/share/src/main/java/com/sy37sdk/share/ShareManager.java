package com.sy37sdk.share;

import android.accounts.AccountManager;
import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.support.v4.content.FileProvider;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.widget.ImageView;
import com.parameters.share.IShareInfo;
import com.parameters.share.SharePlatform;
import com.social.sdk.SocialApi;
import com.social.sdk.common.listener.ShareListener;
import com.social.sdk.platform.PlatformType;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.dialog.LoadingDialog;
import com.sqwan.common.mod.share.IShareResultListener;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.ImageUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.ToastUtil;
import com.sqwan.common.util.ViewUtils;
import com.sy37sdk.share.bean.ShareBean;
import com.sy37sdk.share.bean.ShareMediaParser;
import com.sy37sdk.share.bean.ShareType;
import com.sy37sdk.share.dialog.ShareDialog;
import com.sy37sdk.share.track.ShareTrack;
import java.io.File;
import java.util.HashMap;
import java.util.UUID;


public class ShareManager {

    private static final long DELAY_MILLIS = 10000;

    private static volatile ShareManager instance;

    private Context context;

    private Activity mActivity;

    private ShareRequestManager requestManager;

    private LoadingDialog waitDialog;

    private boolean isBreak = false;

    private ShareManager(Context context) {
        if (context instanceof Activity) {
            mActivity = (Activity) context;
        }
        this.context = context.getApplicationContext();
        requestManager = new ShareRequestManager();
    }

    public static ShareManager getInstance(Context context) {
        if (instance == null) {
            synchronized (AccountManager.class) {
                if (instance == null) {
                    instance = new ShareManager(context);
                }
            }
        }
        return instance;
    }

    private void showLoading() {
        if (waitDialog == null) {
            waitDialog = new LoadingDialog(mActivity);
            waitDialog.setCancelable(false);
        }
        waitDialog.show();
    }

    private void hideLoading() {
        if (waitDialog != null) {
            waitDialog.dismiss();
        }
    }

    /**
     * 获取分享物料
     *
     * @param inviteCode 邀请码
     */
    public void getShareSources(String inviteCode, String img_id, final IShareResultListener listener) {
        showLoading();
        isBreak = false;
        final Handler mTimeoutHandler = new Handler();
        Runnable mTimeoutRunnable = new Runnable() {
            @Override
            public void run() {
                isBreak = true;
                ViewUtils.showToast(context, "网络异常，请稍后重试");
                handleRequestErro("分享失败", listener);
            }
        };
        mTimeoutHandler.postDelayed(mTimeoutRunnable, DELAY_MILLIS);

        requestManager.getShareSources(inviteCode, img_id, new SqHttpCallback<String>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg, @Nullable String data) {
                if (isBreak) {
                    return;
                }
                HashMap<String, String> shareFailMap = new HashMap<>();
                shareFailMap.put(SqTrackKey.reason_fail, msg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                mTimeoutHandler.removeCallbacksAndMessages(null);
                ViewUtils.showToast(context, "分享失败");
                LogUtil.e("请求分享物料失败,msg:  " + msg);
                handleRequestErro("分享失败:  " + msg, listener);
            }

            @Override
            public void onSuccess(String data) {
                if (isBreak) {
                    return;
                }
                handleRequestShareSuccess(data, mTimeoutHandler, listener);
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                if (isBreak) {
                    return;
                }
                HashMap<String, String> shareFailMap = new HashMap<>();
                shareFailMap.put(SqTrackKey.reason_fail, errorMsg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                mTimeoutHandler.removeCallbacksAndMessages(null);
                ViewUtils.showToast(context, "网络异常，请稍后重试");
                handleRequestErro("分享失败:" + errorMsg, listener);
            }
        });
    }


    private void handleRequestShareSuccess(String data, final Handler handler, final IShareResultListener listener) {
        final ShareBean shareBean = handleShareData(data);
        if (shareBean.getShareWays() == null || shareBean.getShareWays().isEmpty()) {
            if (isBreak) {
                return;
            }
            if (handler != null)
                handler.removeCallbacksAndMessages(null);
            hideLoading();
            LogUtil.i("没有配置分享平台");
            ToastUtil.showToast(mActivity, "未配置分享平台");
            return;
        }
        AsyncImageLoader imageLoader = new AsyncImageLoader(context);
        imageLoader.loadDrawable(shareBean.getImg(), null, new AsyncImageLoader.ImageCallback() {
            @Override
            public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                if (isBreak) {
                    return;
                }
                if (handler != null)
                    handler.removeCallbacksAndMessages(null);
                hideLoading();
                shareBean.setBitmap(imageDrawable);
                share(shareBean, listener);
            }
        });
    }

    public void share(ShareBean shareBean, IShareResultListener listener) {
        share(shareBean, false, listener);
    }

    public void share(ShareBean shareBean, boolean ignoreShareWay, IShareResultListener listener) {
        ShareDialog shareDialog = new ShareDialog(mActivity);
        shareDialog.setShareBean(shareBean).setListener(listener).ignoreShareWay(ignoreShareWay);
        shareDialog.show();
    }

    private void handleRequestErro(String msg, IShareResultListener listener) {
        hideLoading();
        if (listener != null)
            listener.onFailture(203, msg);
    }

    private ShareBean handleShareData(String data) {
        ShareBean shareBean = new ShareBean();
        shareBean.parse(data);
        return shareBean;
    }


    public void shareDirectly(int platform, ShareBean shareBean, final IShareResultListener listener) {
        switch (platform) {
            case SharePlatform.WECHAT:
                share("com.tencent.mm", "wx_appid", PlatformType.WECHAT, shareBean, listener);
                break;
            case SharePlatform.WECHAT_MOMENT:
                share("com.tencent.mm", "wx_appid", PlatformType.WECHAT_CIRCLE, shareBean, listener);
                break;
            case SharePlatform.QQ:
                share("com.tencent.mobileqq", "qq_appid", PlatformType.QQ, shareBean, listener);
                break;
            case SharePlatform.SYSTEM:
                shareToSystem(shareBean, listener);
                break;
            default:
                String msg = "未设置分享平台或设置了不支持的分享平台";
                LogUtil.i(msg);
                if (listener != null) {
                    listener.onFailture(203, msg);
                }
                HashMap<String, String> shareFailMap = new HashMap<>();
                shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                shareFailMap.put(SqTrackKey.reason_fail, msg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                break;
        }
    }

    private void share(String packageName, String key, final PlatformType platformType, final ShareBean shareBean, final IShareResultListener listener) {
        if (!SqTrackUtil.checkAppInstalled(context, packageName)) {

            String msg;
            if ("com.tencent.mm".equals(packageName)) {
                msg = "分享失败，请先安装微信客户端";
            } else if ("com.tencent.mobileqq".equals(packageName)) {
                msg = "分享失败，请先安装 QQ 客户端";
            } else {
                msg = "分享失败，请先安装对应客户端";
            }

            LogUtil.i(msg);

            if (listener != null) {
                listener.onFailture(404, msg);
            }

            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
            shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_not_install);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            return;
        }

        // 判断有没有设置社交软件的 key
        if (supportSocialApp(key)) {
            SocialApi.getInstance().share(mActivity, platformType, ShareMediaParser.parse(shareBean), new ShareListener() {
                @Override
                public void onSuccess(PlatformType platform) {
                    LogUtil.i("分享成功");
                    if (listener != null) {
                        listener.onSuccess(new Bundle());
                    }
                    HashMap<String, String> shareMap = new HashMap<>();
                    shareMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);
                }

                @Override
                public void onCancel(PlatformType platform) {
                    String msg = "取消分享";
                    LogUtil.i(msg);
                    if (listener != null) {
                        listener.onFailture(203, msg);
                    }
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareFailMap.put(SqTrackKey.reason_fail, ShareTrack.ShareTrackMsg.share_cancel);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                }

                @Override
                public void onFailure(PlatformType platform, String msg) {
                    LogUtil.i(" 分享失败: " + msg);
                    if (listener != null) {
                        listener.onFailture(203, msg);
                    }
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareFailMap.put(SqTrackKey.reason_fail, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                }
            });

        } else {
            if (shareBean.getType() == ShareType.SHARE_WEB) {

                String url = shareBean.getLandingPageUrl();
                try {
                    ClipboardManager clipboardManager = (ClipboardManager) context
                            .getSystemService(Context.CLIPBOARD_SERVICE);
                    if (clipboardManager != null) {
                        clipboardManager.setPrimaryClip(ClipData.newPlainText("url", url.trim()));
                    }
                    String msg = "分享链接已复制到粘贴板";
                    ToastUtil.showToast(context, msg);
                    if (listener != null) {
                        listener.onSuccess(new Bundle());
                    }
                    HashMap<String, String> shareMap = new HashMap<>();
                    shareMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareMap.put(ShareTrack.ShareTrackKey.message, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);
                } catch (Exception e) {
                    String msg = "分享失败，无法复制链接到粘贴板";
                    LogUtil.e(msg, e);
                    if (listener != null) {
                        listener.onFailture(203, msg);
                    }
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareFailMap.put(SqTrackKey.reason_fail, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                }
            } else {
                if (shareBean.getBitmap() != null) {
                    ImageUtils.save(context, shareBean.getBitmap(), String.format("share_image_%s", UUID.randomUUID().toString()));
                }

                // 启动第三方平台
                try {
                    Intent component = context.getPackageManager().getLaunchIntentForPackage(packageName);
                    Intent intent = new Intent(Intent.ACTION_MAIN);
                    intent.addCategory(Intent.CATEGORY_LAUNCHER);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setComponent(component.getComponent());
                    // 注意需要添加 Intent.FLAG_ACTIVITY_NEW_TASK，否则在某些机型会出现下面的报错，目前出现的机型信息为：华为荣耀畅玩5A 移动4G、Android 6.0、EMUI 4.1.3
                    // Calling startActivity() from outside of an Activity  context requires the FLAG_ACTIVITY_NEW_TASK flag. Is this really what you want?
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);

                    String msg = "直接启动客户端";
                    LogUtil.i(msg);

                    if (listener != null) {
                        listener.onSuccess(new Bundle());
                    }

                    HashMap<String, String> shareMap = new HashMap<>();
                    shareMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareMap.put(ShareTrack.ShareTrackKey.message, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);

                } catch (Exception e) {
                    e.printStackTrace();

                    String msg = "打开第三方平台失败";
                    LogUtil.e(msg, e);
                    if (listener != null) {
                        listener.onFailture(505, msg);
                    }

                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(ShareTrack.ShareTrackKey.platform, platformType.name());
                    shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareFailMap.put(SqTrackKey.reason_fail, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                }
            }
        }
    }

    public void shareToSystem(ShareBean shareBean, final IShareResultListener listener) {
        Intent intent = null;
        switch (shareBean.getType()) {
            case IShareInfo.ShareClassify.SHARE_IMG:
                Uri uri = ImageUtils.save(context, shareBean.getBitmap(),
                        String.format("share_image_%s", System.currentTimeMillis()) + ".png");
                if (uri == null) {
                    String msg = "分享失败，图片文件保存失败";
                    if (listener != null) {
                        listener.onFailture(203, msg);
                    }
                    HashMap<String, String> shareFailMap = new HashMap<>();
                    shareFailMap.put(ShareTrack.ShareTrackKey.platform, "SYSTEM");
                    shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
                    shareFailMap.put(SqTrackKey.reason_fail, msg);
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
                    return;
                }
                intent = new Intent(Intent.ACTION_SEND);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
                intent.putExtra(Intent.EXTRA_STREAM, uri);
                intent.setType("image/*");
                break;
            case IShareInfo.ShareClassify.SHARE_WEB:
                // 分享链接
                intent = new Intent(Intent.ACTION_SEND);
                intent.setType("text/plain");
                intent.putExtra(Intent.EXTRA_TEXT, shareBean.getTitle() + ": " + shareBean.getLandingPageUrl());
                break;
            case IShareInfo.ShareClassify.SHARE_TEXT:
                // 分享文本
                intent = new Intent(Intent.ACTION_SEND);
                intent.setType("text/plain");
                intent.putExtra(Intent.EXTRA_TEXT, shareBean.getText());
                break;
            default:
                break;
        }

        if (intent == null) {
            String msg = "分享失败，分享类型不被支持";
            LogUtil.i(msg);
            if (listener != null) {
                listener.onFailture(203, msg);
            }
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, "SYSTEM");
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            return;
        }

        if (context.getPackageManager().queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY).isEmpty()) {
            String msg = "分享失败，找不到对应的系统程序";
            LogUtil.i(msg);
            if (listener != null) {
                listener.onFailture(203, msg);
            }
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, "SYSTEM");
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
            return;
        }

        Intent chooserIntent = Intent.createChooser(intent, "");
        // 注意需要添加 Intent.FLAG_ACTIVITY_NEW_TASK，否则在某些机型会出现下面的报错，目前出现的机型信息为：华为荣耀畅玩5A 移动4G、Android 6.0、EMUI 4.1.3
        // Calling startActivity() from outside of an Activity  context requires the FLAG_ACTIVITY_NEW_TASK flag. Is this really what you want?
        chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            context.startActivity(chooserIntent);
            if (listener != null) {
                listener.onSuccess(new Bundle());
            }

            LogUtil.i("调起系统分享成功");

            HashMap<String, String> shareMap = new HashMap<>();
            shareMap.put(ShareTrack.ShareTrackKey.platform, "SYSTEM");
            shareMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_success, shareMap);

        } catch (Exception e) {
            e.printStackTrace();

            String msg = "分享失败，调起系统分享失败";
            LogUtil.e(msg, e);

            if (listener != null) {
                listener.onFailture(203, msg);
            }

            // 分享失败埋点上报
            HashMap<String, String> shareFailMap = new HashMap<>();
            shareFailMap.put(ShareTrack.ShareTrackKey.platform, "SYSTEM");
            shareFailMap.put(ShareTrack.ShareTrackKey.classify, getClassifyType(shareBean));
            shareFailMap.put(SqTrackKey.reason_fail, msg);
            SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.share_fail, shareFailMap);
        }
    }

    private boolean supportSocialApp(String key) {
        try {
            ApplicationInfo info = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            String wx_appid = info.metaData.getString(key);
            return !TextUtils.isEmpty(wx_appid);
        } catch (Exception exception) {
            exception.printStackTrace();
            LogUtil.e("未找到第三方的配置参数");
            return false;
        }
    }

    private String getClassifyType(ShareBean shareBean) {
        if (shareBean == null) {
            return "";
        }
        return String.valueOf(shareBean.getType());
    }
}