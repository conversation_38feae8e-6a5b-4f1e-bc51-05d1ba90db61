# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#artifactory_user=admin
#artifactory_password=502829ningmeng
#artifactory_contextUrl=http://localhost:8081/artifactory
#artifactory_repositories_id=http://localhost:8081/artifactory/sqsdk-multi-libs
#artifactory_repoKey=sqsdk-multi-libs
android.disableResourceValidation=true
# sonarqube host
systemProp.sonar.host.url=http://************:9123
#----- Token generated from an account with 'publish analysis' permission
systemProp.sonar.login=****************************************

#
sonarqubeSdkType=sq

android.injected.testOnly=false
org.gradle.daemon=false





#-----------------------start æå»ºç¸å³çåæ°-------------------
#cmd_psdk_gd
#cmd_psdk_sq
#cmd_psdk_jc
#cmd_msdk_sq
#cmd_pdemo_sq
#cmd_pdemo_gd
#cmd_pdemo_jc
#cmd_mdemo_sq
#cmd_mdemo_gd
#cmd_mdemo_jc
#cmd_plugin_sq
#cmd_plugin_gd
#cmd_plugin_jc
#cmd_host_gd
#cmd_host_jc

#none æåæºè·çè¯è®¾ç½®ä¸ºnone

cmb_build_sdk = cmd_mdemo_sq
#-----------------------end æå»ºç¸å³çåæ°-------------------






