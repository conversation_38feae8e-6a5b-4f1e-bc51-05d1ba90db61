apply plugin: 'com.android.library'
apply plugin: 'com.kezong.fat-aar'

def isPluginHostAar = get_isPluginHostAar()
android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

afterEvaluate {
    generateReleaseBuildConfig.enabled = false
    generateDebugBuildConfig.enabled = false
    generateReleaseResValues.enabled = false
    generateDebugResValues.enabled = false
}

dependencies {
    api ('com.taptap.sdk:tap-core:4.5.7')
    api ('com.taptap.sdk:tap-review:4.5.7')
}
