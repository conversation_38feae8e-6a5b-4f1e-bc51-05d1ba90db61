<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.social.sdk">

  <uses-permission android:name="android.permission.INTERNET" />

  <application>
    <!--  qq start  -->
    <activity
      android:name="com.tencent.tauth.AuthActivity"
      android:launchMode="singleTask"
      android:noHistory="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data android:scheme="tencent101495075" />
      </intent-filter>
    </activity>
    <activity
      android:name="com.tencent.connect.common.AssistActivity"
      android:configChanges="orientation|keyboardHidden"
      android:screenOrientation="behind"
      android:theme="@android:style/Theme.Translucent.NoTitleBar" />
    <!--  qq end  -->
  </application>
</manifest>