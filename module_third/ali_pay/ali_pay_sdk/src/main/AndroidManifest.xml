<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.alipay.sdk">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application>
        <uses-library
          android:name="org.apache.http.legacy"
          android:required="false" />

        <activity
          android:name="com.alipay.sdk.app.H5PayActivity"
          android:configChanges="orientation|keyboardHidden|navigation"
          android:exported="false"
          android:screenOrientation="behind" >
        </activity>
        <activity
          android:name="com.alipay.sdk.app.H5AuthActivity"
          android:configChanges="orientation|keyboardHidden|navigation"
          android:exported="false"
          android:screenOrientation="behind" >
        </activity>
        <activity
          android:name="com.alipay.sdk.app.PayResultActivity"
          android:configChanges="orientation|keyboardHidden|navigation"
          android:exported="true"
          android:launchMode="singleInstance"
          android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
          android:name="com.alipay.sdk.app.AlipayResultActivity"
          android:exported="true"
          android:launchMode="singleTask"
          android:theme="@android:style/Theme.Translucent.NoTitleBar" >
        </activity>
    </application>
</manifest>