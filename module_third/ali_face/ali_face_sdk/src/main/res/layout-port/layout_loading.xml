<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toyger_face_eye_loading_page"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="194dip"
    android:background="#88000000">

    <com.aliyun.aliyunface.ui.widget.iOSLoadingView
        android:layout_gravity="center_horizontal"
        android:layout_width="30dip"
        android:layout_height="30dip" />

    <TextView
        android:id="@+id/simple_process_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dip"
        android:gravity="center_horizontal"
        android:text="@string/zface_processing"
        android:textColor="#E4E4E4"
        android:textSize="14dp" />

</LinearLayout>