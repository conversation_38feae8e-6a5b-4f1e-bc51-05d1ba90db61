<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CircleHoleView">
        <attr format="dimension" name="holeLeft"/>
        <attr format="boolean" name="holeHCenter"/>
        <attr format="boolean" name="holeVCenter"/>
        <attr format="dimension" name="holeTop"/>
        <attr format="dimension" name="holeWidth"/>
        <attr format="dimension" name="holeHeight"/>
    </declare-styleable>
    <declare-styleable name="RectMaskView">
        <attr format="dimension" name="rectLeft"/>
        <attr format="dimension" name="rectTop"/>
        <attr format="dimension" name="rectWidth"/>
        <attr format="dimension" name="rectHeight"/>

        <attr format="boolean" name="rectHCenter"/>
        <attr format="boolean" name="rectVCenter"/>
    </declare-styleable>
    <declare-styleable name="zface_round_progressBar">
        <attr format="color" name="zface_round_color"/>
        <attr format="color" name="zface_round_progress_color"/>
        <attr format="dimension" name="zface_round_width"/>
        <attr format="color" name="zface_text_color"/>
        <attr format="dimension" name="zface_text_size"/>
        <attr format="integer" name="zface_max"/>
        <attr format="integer" name="zface_start_angle"/>
        <attr format="integer" name="zface_background_color"/>
        <attr format="integer" name="zface_end_angle"/>
        <attr format="dimension" name="zface_color_bg_width"/>
        <attr format="boolean" name="zface_text_is_displayable"/>
        <attr format="boolean" name="zface_progress_shader"/>
        <attr format="color" name="zface_gradient_color_start"/>
        <attr format="color" name="zface_gradient_color_end"/>
        <attr name="zface_style">
            <enum name="ZFACE_STROKE" value="0"/>
            <enum name="ZFACE_FILL" value="1"/>
        </attr>
    </declare-styleable>
</resources>