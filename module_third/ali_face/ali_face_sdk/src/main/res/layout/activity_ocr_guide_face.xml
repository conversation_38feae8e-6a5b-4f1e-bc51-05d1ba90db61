<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ocr_white"
    tools:context=".ui.OcrGuideBaseActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!--引入自定义ActionBar-->
        <include layout="@layout/ocr_section_layout_action_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dip"
            android:background="@color/ocr_background_gray" />

        <!--引入进度指示器布局-->
        <com.aliyun.aliyunface.ui.widget.OcrGuideStageView
            android:id="@+id/ocr_guide_stage_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:layout_width="match_parent"
            android:layout_height="10dip"
            android:background="@color/ocr_background_gray" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_margin="@dimen/comm_margin_size_20"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:text="@string/aliyun_face_name"
                android:textSize="@dimen/comm_normal_mid_font_size"
                android:textColor="@color/ocr_black_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:text="@string/ocr_take_photo_tips"
                android:textSize="@dimen/comm_normal_small_font_size"
                android:textColor="#555555"
                android:layout_marginTop="@dimen/comm_margin_size_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <ImageView
                android:src="@mipmap/ocr_guide_face"
                android:layout_marginTop="@dimen/comm_margin_size_40"
                android:layout_marginLeft="@dimen/comm_margin_size_80"
                android:layout_marginRight="@dimen/comm_margin_size_80"
                android:scaleType="fitStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>
    </LinearLayout>

    <com.aliyun.aliyunface.ui.overlay.OcrLoadingOverlay
        android:id="@+id/ocr_loading_overlay"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</FrameLayout>