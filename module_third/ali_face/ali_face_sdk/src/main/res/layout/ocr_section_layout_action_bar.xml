<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70dip"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="20dip"
        android:layout_height="20dip"
        android:layout_margin="@dimen/comm_margin_size_20"
        android:layout_gravity="end"
        android:scaleType="fitStart"
        android:src="@mipmap/ocr_black_close" />

    <View
        android:id="@+id/ocr_comm_back_button"
        android:layout_gravity="end"
        android:layout_width="70dip"
        android:layout_height="70dip" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text=""
        android:textColor="@color/ocr_black_text"
        android:textSize="@dimen/comm_title_font_size" />

</FrameLayout>
