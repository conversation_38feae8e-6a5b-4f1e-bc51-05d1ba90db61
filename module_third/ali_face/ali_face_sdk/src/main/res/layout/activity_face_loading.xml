<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:context=".ui.FaceLoadingActivity">

    <com.aliyun.aliyunface.ui.widget.iOSLoadingView
        android:id="@+id/iOSLoadingView"
        android:layout_width="30dip"
        android:layout_height="30dip" />

</LinearLayout>