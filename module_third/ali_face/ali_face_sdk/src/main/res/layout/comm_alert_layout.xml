<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ocr_take_photo_require_page"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.25"
        android:background="#000000" />

    <LinearLayout
        android:background="@drawable/alert_round_shape"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:layout_margin="30dip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/comm_alert_title_text"
            android:text="@string/message_box_title_exit_tip"
            android:textColor="#000000"
            android:textSize="22dp"
            android:textStyle="bold"
            android:layout_marginLeft="20dip"
            android:layout_marginTop="20dip"
            android:layout_marginRight="20dip"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_marginLeft="20dip"
            android:layout_marginRight="20dip"
            android:id="@+id/comm_alert_message_text"
            android:lineSpacingExtra="5dip"
            android:text="@string/message_box_message_exit_tip"
            android:textColor="#000000"
            android:textSize="20dp"
            android:gravity="center_horizontal"
            android:layout_marginTop="5dip"
            android:layout_marginBottom="7dip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:background="#F6F6F6"
            android:layout_width="match_parent"
            android:layout_height="1dip" />

        <FrameLayout
            android:layout_marginTop="5dip"
            android:layout_marginBottom="10dip"
            android:layout_width="match_parent"
            android:layout_height="30dip">

            <LinearLayout
                android:id="@+id/comm_alert_button_2"
                android:orientation="horizontal"
                android:layout_marginLeft="20dip"
                android:layout_marginRight="20dip"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/comm_alert_cancel"
                    android:layout_weight="1"
                    android:text="@string/message_box_btn_cancel_tip"
                    android:textColor="#0E83E6"
                    android:textSize="20dp"
                    android:gravity="center"
                    android:layout_width="0dip"
                    android:layout_height="match_parent"/>

                <View
                    android:background="#F6F6F6"
                    android:layout_width="1dip"
                    android:layout_height="match_parent" />

                <TextView
                    android:id="@+id/comm_alert_confirm"
                    android:layout_weight="1"
                    android:text="@string/message_box_btn_ok_tip"
                    android:textColor="#0E83E6"
                    android:textSize="20dp"
                    android:gravity="center"
                    android:layout_width="0dip"
                    android:layout_height="match_parent"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/comm_alert_button_1"
                android:visibility="invisible"
                android:orientation="horizontal"
                android:layout_marginLeft="20dip"
                android:layout_marginRight="20dip"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/comm_alert_confirm1"
                    android:layout_weight="1"
                    android:text="@string/message_box_btn_retry"
                    android:textColor="#0E83E6"
                    android:textSize="20dp"
                    android:gravity="center"
                    android:layout_width="0dip"
                    android:layout_height="match_parent"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</FrameLayout>
