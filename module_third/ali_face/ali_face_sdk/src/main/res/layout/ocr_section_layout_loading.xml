<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ocr_take_photo_require_page"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:background="@color/ocr_black_text"
        android:alpha="0.25"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:background="@color/ocr_black_text"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:paddingLeft="@dimen/comm_margin_size_20"
        android:paddingRight="@dimen/comm_margin_size_20"
        android:layout_width="wrap_content"
        android:layout_height="140dip">

        <ImageView
            android:id="@+id/img_ocr_loading"
            android:src="@mipmap/comm_ocr_loading"
            android:scaleType="fitStart"
            android:layout_gravity="center_horizontal"
            android:layout_margin="@dimen/comm_margin_size_20"
            android:layout_width="@dimen/comm_ocr_button_large_size"
            android:layout_height="@dimen/comm_ocr_button_large_size" />

        <TextView
            android:id="@+id/ocr_loading_tips"
            android:text="@string/ocr_idcard_identity_loading"
            android:textColor="@color/ocr_white"
            android:textSize="@dimen/comm_normal_small_font_size"
            android:gravity="center_horizontal"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_margin="@dimen/comm_margin_size_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </FrameLayout>

</FrameLayout>
