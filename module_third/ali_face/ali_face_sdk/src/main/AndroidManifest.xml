<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.aliyun.aliyunface">

  <uses-permission android:name="android.permission.CAMERA" />

  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

  <application>
    <!--  阿里人脸识别 start -->
    <activity
      android:name="com.aliyun.aliyunface.ui.FaceLoadingActivity"
      android:exported="false"
      android:screenOrientation="portrait"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.ToygerActivity"
      android:exported="false"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.ToygerLandActivity"
      android:exported="false"
      android:screenOrientation="landscape"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.ToygerPortActivity"
      android:exported="false"
      android:screenOrientation="portrait"
      android:theme="@style/ToygerAppTheme" />

    <activity
      android:name="com.aliyun.aliyunface.ui.OcrGuideFaceActivity"
      android:exported="false"
      android:screenOrientation="portrait"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.OcrGuideBackActivity"
      android:exported="false"
      android:screenOrientation="portrait"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.OcrGuideFrontActivity"
      android:exported="false"
      android:screenOrientation="portrait"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.OcrTakePhotoActivity"
      android:exported="false"
      android:theme="@style/ToygerAppTheme" />
    <activity
      android:name="com.aliyun.aliyunface.ui.OcrGuideBaseActivity"
      android:exported="false"
      android:theme="@style/ToygerAppTheme" />

    <!--  阿里人脸识别 end -->
  </application>
</manifest>