apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        debug {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

afterEvaluate {
    generateReleaseBuildConfig.enabled = false
    generateDebugBuildConfig.enabled = false
    generateReleaseResValues.enabled = false
    generateDebugResValues.enabled = false
}

dependencies {
    api files('libs/android-aliyunbasicstl-sdk-release-1.5.2-20211230100840.jar')
    api files('libs/android-aliyuncomm-sdk-release-1.5.2-20211230100840 .jar')
    api files('libs/Android-AliyunDevice-FG-10022.2.jar')
    api files('libs/android-aliyunface-sdk-release-1.5.2-20211230100840.jar')
    api files('libs/android-aliyunocr-sdk-release-1.5.2-20211230100840.jar')
    api files('libs/deviceid-release-6.0.7.20211109.jar')
    api files('libs/oss-android-sdk-2.9.11.jar')
    api files('libs/photinus-1.0.1.211130153658.jar')
    api files('libs/tygerservice-1.0.0.210619103852.jar')
}
