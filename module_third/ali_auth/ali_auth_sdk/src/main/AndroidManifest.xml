<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.mobile.auth"
    android:versionCode="21321"
    android:versionName="2.13.2.1" >

    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络访问 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 检查wifi网络状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 检查网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <application>
        <!--重点关注！！！！！！！-->
        <!--如果不需要使用窗口模式，不要使用authsdk_activity_dialog主题，会出现异常动画-->
        <!--如果需要使用authsdk_activity_dialog主题，则screenOrientation一定不能指定明确的方向，
        比如portrait、sensorPortrait，在8.0的系统上不允许窗口模式指定orientation，会发生crash，需要指定为behind，
        然后在授权页的前一个页面指定具体的orientation-->
        <activity
          android:name="com.mobile.auth.gatewayauth.LoginAuthActivity"
          android:configChanges="orientation|keyboardHidden|screenSize"
          android:exported="false"
          android:launchMode="singleTop"
          android:screenOrientation="behind"
          android:theme="@style/authsdk_activity_dialog" />

        <activity
          android:name="com.mobile.auth.gatewayauth.activity.AuthWebVeiwActivity"
          android:configChanges="orientation|keyboardHidden|screenSize"
          android:exported="false"
          android:launchMode="singleTop"
          android:screenOrientation="behind" />

        <activity
          android:name="com.mobile.auth.gatewayauth.PrivacyDialogActivity"
          android:configChanges="orientation|keyboardHidden|screenSize"
          android:exported="false"
          android:launchMode="singleTop"
          android:screenOrientation="behind"
          android:theme="@style/authsdk_activity_dialog" />
    </application>
</manifest>