#### 基本流程

![image-20200311231250389](./image/image-20200311231250389.png)

#### 点击登录界面的 ？号

1. 如果是正版、判断帮助页面链接是否为空，如果不为空跳转至帮助页面，如果为空跳转至公众号引导界面；

2. 如果是简版，直接弹出公众号引导界面；

3. 帮助页面链接配置和悬浮窗帮助链接相同；

4. 公众号引导界面文案以及复制文案有后端控制返回（见s层激活接口）。

   ![image-20200312110422436](./image/image-20200312110422436.png)

####

#### 登录界面填充账号

1. 优先显示上次登录的账号，其次显示账号列表中最近登录的账号，没有历史账号则跳转注册页面。

   ![image-20200312103702303](./image/image-20200312103702303.png)

#### 

#### 自动生成注册账号

1. 账号注册页面会自动生成注册账号，如果之前生成过但是没有被注册，则使用之前生成的， 再次打开不会请求服务端重新生成；

2. 如果是自动生成的账号，注册成功会跳转至注册成功界面；

3. 注册成功的行为由后台控制，详情请见接口文档：<https://37wiki.37wan.com/pages/viewpage.action?pageId=12232731#SDK%E5%9F%BA%E7%A1%80%E6%9C%8D%E5%8A%A1%E6%96%87%E6%A1%A3-22.%E8%87%AA%E5%8A%A8%E5%88%86%E9%85%8D%E8%B4%A6%E5%8F%B7%E6%8E%A5%E5%8F%A3>

4. 注册接口会返回二维码相关的配置，来决定在注册成功界面是否显示二维码。

   ![image-20200312105300465](./image/image-20200312105300465.png)

   ![image-20200312105322788](./image/image-20200312105322788.png)

#### 

#### UI版本控制

1. UI版本配置（登录/账号注册/手机注册的UI）：1、2、3、4...等版本；
2. 一键注册功能配置（一键注册功能页面）：0为关闭，1为旧版，2为新版；
3.  一键注册功能版本：新版本， UI版本：新版本。



#### 登录接口返回数据说明

1. 后端接口文档地址：https://37wiki.37wan.com/pages/viewpage.action?pageId=12232733#SDK%E7%94%A8%E6%88%B7%E7%9B%B8%E5%85%B3%E6%96%87%E6%A1%A3-1%E3%80%81SDK%E7%99%BB%E5%BD%95%E6%8E%A5%E5%8F%A3>



#### 跑马灯相关

1. 跑马灯配置由s层激活接口返回

   ![image-20200312111440078](./image/image-20200312111440078.png)

2. n表示跑马灯文案，文案为空则不显示跑马灯。

   ![image-20200312163909185](./image/image-20200312163909185.png)

#### 历史账号列表的存储

1. 37体系下的账号保存在SD卡中，应用间可以共用，卸载应用不会丢失；（路径：37shouyou/37file.37）
2. 账号列表按照最近登录时间排序，最近登录排在最前面。
3. 有别名优先显示别名。

#### 找回密码/账号

#### 隐私协议