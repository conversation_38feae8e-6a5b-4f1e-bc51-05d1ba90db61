#### 总体流程

![企业微信截图_5d7036c5-016c-4336-bc10-535dd4e77239](./image/原生支付页面总体流程.png)

#### 跳转

客户端保留H5支付和原生支付两种支付方式，有m层下单接口返回控制使用H5支付或者原生支付。

下单接口：http://mpay.api.m.37.com/sdk/order/

| 字段   | 类型    | 说明                   |
| ------ | ------- | ---------------------- |
| appPay | integer | appPay=1表示走原生支付 |

#### 支付方式

原生支付的方式一共有如下

1. 支付宝支付
2. 花呗支付
3. 微信支付
4. 钱包支付

支付方式由后端接口控制，默认情况下显示支付宝和微信支付

支付方式接口：http://spay.api.m.37.com/coupon/get

支付方式默认选中上一次支付使用的支付方式，如果没有支付记录， 则默认选中支付宝。

#### 钱包

如果支付方式中打开了钱包支付，则请求钱包余额，如果钱包余额小于当前订单金额，也不显示钱包支付方式

钱包余额接口：http://s.api.m.37.com/sdk/piwt/

选择钱包支付的情况下，不能使用代金券

#### 代金券

除钱包支付的其它支付方式可以使用代金券，实际付款金额 = 订单价格 - 代金券面值，如果代金券面值大于或者等于订单金额，则支付0.01元

用户可以选择可使用的代金券或者不使用代金券

代金券接口：http://spay.api.m.37.com/coupon/get

#### 人工充值

跳转到人工充值说明界面，文案由支付方式接口返回

简版状态下，不显示人工充值入口

#### 支付流程

- 钱包支付，直接调用钱包支付接口
- 支付宝、花呗
  1. s层下单，返回跳转支付宝的链接
  2. 跳转支付宝
  3. 返回支付结果
- 微信
  1. s层下单，返回跳转微信的链接
  2. 跳转微信
  3. 通过PayDialog生命周期onWindowFocusChanged 判断微信支付返回
  4. 查询支付结果

#### 微信支付结果上报

支付页面上报支付结果，但是在微信支付时，查询到的结果可能不准确， 在查询到支付失败时，一分钟之后会再次查询支付结果，此次查询结果如果成功，则补充上报；再次查询的结果不回调。



