package com.sq.tools.network.httpdns.constant;

public class HttpDnsRequestConstant {
    //dns默认服务开关（业务使用开关）
    public static boolean switchEnable = true;

    //dns服务默认是否可用
    public static boolean isEnable = false;

    //正式环境
    //请求dns服务的ip地址
    public static String[] REQUEST_IPS = new String[]{"************", "************", "**************", "***********"};

    //appId
    public static String APP_ID = "100002";

    //appKey
    public static String APP_KEY = "4ce9fae67d9c";


    //测试环境
    //请求dns服务的ip地址
//    public static String REQUEST_IP = "**********";
//
//    //appId
//    public static String APP_ID = "123458";
//
//    //appKey
//    public static String APP_KEY = "db522fb49bbb";

    //dns接口的主域
    public static final String DNS_HOST = "httpdns.tgcome.com";

}
