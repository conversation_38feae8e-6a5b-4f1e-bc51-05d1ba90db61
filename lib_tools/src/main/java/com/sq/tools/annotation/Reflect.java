package com.sq.tools.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 以此注解修饰的类，或者方法，表示其存在反射调用的情况。
 * <br>对此类型方法修改名称或者包名时，需要额外注意，一并修改反射引用的地方
 *
 * @caller 调用者所在的类与方法，通过类似 "com.xxx.xxx.class#method" 的方式指定，多个调用者均需指定
 * @rule 此被反射类（方法）的命名规则（如果有）
 */
@Retention(RetentionPolicy.SOURCE) @Target({ElementType.METHOD, ElementType.TYPE})
public @interface Reflect {
    String[] caller();
    String rule() default "";
}
