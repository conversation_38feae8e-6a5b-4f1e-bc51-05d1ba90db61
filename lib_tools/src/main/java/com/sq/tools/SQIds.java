package com.sq.tools;

import android.content.Context;
import android.content.res.AssetManager;
import android.support.annotation.NonNull;
import android.util.Xml;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

public enum SQIds {
    ID;

    protected final String CONFIG_FILE_NAME = "37wan_config.xml";
    protected final String CONFIG_NODE_GAMEID = "gameid";
    protected final String CONFIG_NODE_PARTNER = "partner";
    protected final String CONFIG_NODE_REFER = "referer";
    protected final String CONFIG_NODE_KEY = "appkey";

    public String gameId = "1000001";
    public String partner = "1";
    public String refer = "";
    public String appkey = "";

    // pgid, cid, tgid, 目前只能通过后台获取，故此处直接留空
    public String cid = "";
    public String pgid = "";
    public String tgid = "";

    private volatile boolean isInit;

    public void init(@NonNull Context context) {
        if(isInit) return;
        InputStreamReader reader = null;
        try {
            reader = new InputStreamReader(context.getAssets().open(CONFIG_FILE_NAME, AssetManager.ACCESS_BUFFER), StandardCharsets.UTF_8);
            XmlPullParser parser = Xml.newPullParser();
            parser.setInput(reader);
            int eventType = parser.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                if(eventType != XmlPullParser.START_TAG) {
                    eventType = parser.next();
                    continue;
                }
                if(CONFIG_NODE_GAMEID.equals(parser.getName())) gameId = parser.nextText().trim();
                if(CONFIG_NODE_PARTNER.equals(parser.getName())) partner = parser.nextText().trim();
                if(CONFIG_NODE_REFER.equals(parser.getName())) refer = parser.nextText().trim();
                if(CONFIG_NODE_KEY.equals(parser.getName())) appkey = parser.nextText().trim();
                eventType = parser.next();
            }
            isInit = true;
        } catch (IOException e) {
            Logger.error("Init failed, miss 37wan_config.xml or 37wan_config.xml was damaged", e);
        } catch (XmlPullParserException e) {
            Logger.error("Init failed, parse 37wan_config exception", e);
        } finally {
            if(reader != null) {
                try{
                    reader.close();
                }catch (IOException e){
                    Logger.warning("Exception happen when trying to close %s, this may cause a memory leak", "IdKeeper InputStreamReader", e);
                }
            }
        }
    }
}
