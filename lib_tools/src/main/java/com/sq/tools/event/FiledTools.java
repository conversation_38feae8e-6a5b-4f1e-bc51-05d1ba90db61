package com.sq.tools.event;

import android.support.annotation.NonNull;
import android.support.annotation.RestrictTo;
import android.text.TextUtils;
import com.sq.tools.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * 事件基础类，各个不同的事件类型，例如普通事件，异常事件等，均需继承此类
 * <br>通常，调用者无需直接继承此类，而可以直接使用 {@link EventFiled}
 */
@SuppressWarnings("WeakerAccess")
public abstract class FiledTools implements Serializable {

    protected int maxLength = 512;
    protected String data;


    /**
     * 该方法将带有 {@link EventField} 标记的所有<b>非null</b>成员变量
     * 按照其注解值组装成 {@link JSONObject}
     */
    @NonNull public JSONObject toJsonObject() {
        List<Field> fields = new ArrayList<>();
        fields.addAll(Arrays.asList(this.getClass().getFields()));
        fields.addAll(Arrays.asList(this.getClass().getDeclaredFields()));
        fields.addAll(Arrays.asList(this.getClass().getInterfaces().getClass().getDeclaredFields()));
        JSONObject json = new JSONObject();
        try {
            json = new JSONObject(data != null ? data : "{}");
        } catch (JSONException ignore) {}

        for (Field field : fields) {
            EventField eventField = field.getAnnotation(EventField.class);
            if (eventField == null) continue;
            String tag = eventField.value();
            if (TextUtils.isEmpty(tag)) continue;
            field.setAccessible(true);
            try {
                json.put(tag, cropFiled(field.get(this)));
            } catch (IllegalAccessException e) {
                Logger.error(Logger.tag(EventsTracker.TAG), "Get Field value from Event with refection has Exception", e);
            } catch (JSONException e) {
                Logger.error(Logger.tag(EventsTracker.TAG), "Put Event value to Json has Exception", e);
            }
        }
        return json;
    }

    /**
     * 针对每个字段进行裁剪，防止其长度过长导致后端存储错误。
     * @param field 需要裁剪的字段
     * @return 裁剪后的 object
     */
    protected Object cropFiled(Object field) {
        if (field == null) return null;
        int length = field.toString().length();
        if (length <= maxLength) return field;
        JSONObject data = castToJsonObject(field);
        if(data != null) {
            try {
                JSONObject cut = new JSONObject();
                Iterator<String> iterator = data.keys();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Object value = data.get(key);
                    Object cropValue = !(value instanceof String) || value.toString().length() < maxLength/data.length() ? value
                            : "Cut:" + data.optString(key).substring(0, maxLength/data.length() - 5);
                    cut.put(key, cropValue);
                }
                return cut;
            } catch (JSONException e) {
                Logger.error(Logger.tag(EventsTracker.TAG), "Put cut data to json exception", e);
                return field;
            }
        } else if(field instanceof String) {
            return ((String)field).substring(0, maxLength);
        } else {
            return field;
        }
    }

    /**
     * @return A instance of JSONObject if Object could cast to JSONObject, otherwise return null
     */
    private JSONObject castToJsonObject(Object field) {
        if (field instanceof JSONObject) return (JSONObject)field;
        try {
            if (field instanceof String) {
                return new JSONObject((String) field);
            }
        } catch (Exception ignored) {}
        return null;
    }


    @NonNull @Override
    public String toString() {
        return toJsonObject().toString();
    }

    @Target(ElementType.FIELD) @Retention(RetentionPolicy.RUNTIME) @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public @interface EventField {
        /**
         * @return 组装 Event 事件 JSON 格式的 Key
         */
        String value();
    }

}
