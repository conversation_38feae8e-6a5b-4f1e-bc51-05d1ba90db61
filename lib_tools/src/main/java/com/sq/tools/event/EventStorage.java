package com.sq.tools.event;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.RestrictTo;
import android.text.TextUtils;
import com.sq.tools.Assert;
import com.sq.tools.Logger;
import java.io.EOFException;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

/**
 * 事件存储类，通过 {@link ObjectInputStream} & {@link ObjectOutputStream} 的方式进行事件的存储与读取.
 * <br><b>注意:</b> 此类中的方法均不能运行在 UI 线程，切勿在 UI 线程调用此类相关方法
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
final class EventStorage {

    private String EVENT_STORAGE_FILE_NAME = "SQ_EVENT_LOG.log";

    void setEventStorageFileName(String name) {
        if(!TextUtils.isEmpty(name)) EVENT_STORAGE_FILE_NAME = name;
    }

    synchronized EventCollection readAndClearStore(Context context) {
        Assert.shouldNotInUIThread();
        Logger.info(Logger.tag(EventsTracker.TAG), "Trying to read events from log file, waiting....");

        if(!isEventFileExist(context)) {
            Logger.info(Logger.tag(EventsTracker.TAG), "Event file does not exist, return directly");
            return new EventCollection();
        }

        EventCollection collection = readStorage(context);
        Logger.info(Logger.tag(EventsTracker.TAG), "Read Events should success, read Events count: %d", collection.size());
        try {
            boolean isDelete = context.getApplicationContext().getFileStreamPath(EVENT_STORAGE_FILE_NAME).delete();
            if (!isDelete) Logger.warning(Logger.tag(EventsTracker.TAG), "Events file delete fail somehow");
        } catch (Exception e) {
            Logger.warning(Logger.tag(EventsTracker.TAG), "Delete File has Exception, this may cause repeat event post", e);
        }
        return collection;
    }


    synchronized boolean saveEventToDisk(Context context, @NonNull EventCollection collection) {
        if (collection.size() == 0) return true;
        Assert.shouldNotInUIThread();
        Logger.info(Logger.tag(EventsTracker.TAG), "Trying to storage Events to file, waiting.....");
        FileOutputStream stream = null;
        ObjectOutputStream oo = null;

        EventCollection saved = new EventCollection();
        saved.add(collection);
        if(isEventFileExist(context)) {
            saved.add(readStorage(context));
        }

        try {
            stream = context.openFileOutput(EVENT_STORAGE_FILE_NAME, Context.MODE_PRIVATE);
            oo = new ObjectOutputStream(stream);
            oo.writeObject(saved);
            oo.flush();
            Logger.info(Logger.tag(EventsTracker.TAG), "Storage Events should success, storage %d events", collection.size());
            return true;
        } catch (Exception e) {
            Logger.error(Logger.tag(EventsTracker.TAG), "Unexpected Exception while write events to file", e);
            return false;
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    Logger.warning(Logger.tag(EventsTracker.TAG), "Events File OutputStream close exception", e);
                }
            }
            if (oo != null) {
                try {
                    oo.close();
                } catch (IOException e) {
                    Logger.warning(Logger.tag(EventsTracker.TAG), "Events Object OutputStream close exception", e);
                }
            }
        }
    }

    private synchronized EventCollection readStorage(Context context) {
        EventCollection collection = new EventCollection();
        ObjectInputStream oi = null;
        FileInputStream stream = null;
        try {
            stream = context.getApplicationContext().openFileInput(EVENT_STORAGE_FILE_NAME);
            oi = new ObjectInputStream(stream);
            EventCollection c = null;
            try {
                c = (EventCollection) oi.readObject();
            }catch (EOFException e){
                //EOFException happen when reach the end of file, since we don't have any way to detect
                //if it's already the end of file, so we deal it like this.
            }
            if(c != null) collection.add(c);
        } catch (FileNotFoundException e) {
            Logger.warning(Logger.tag(EventsTracker.TAG), "Event File Not found when read, this is expected since we might never storage any event to file");
        } catch (Exception e) {
            Logger.error(Logger.tag(EventsTracker.TAG), "Unexpected Exception while read events from file", e);
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    Logger.warning(Logger.tag(EventsTracker.TAG), "Events File InputStream close IOException", e);
                }
            }
            if (oi != null) {
                try {
                    oi.close();
                } catch (IOException e) {
                    Logger.warning(Logger.tag(EventsTracker.TAG), "Event Object InputStream close Exception", e);
                }
            }
        }
        return collection;
    }

    private synchronized boolean isEventFileExist(Context context) {
        boolean isEventFileExist = false;
        String[] fileList = context.getApplicationContext().fileList();
        for (String s : fileList) {
            if (s.contains(EVENT_STORAGE_FILE_NAME)) {
                isEventFileExist = true;
                break;
            }
        }
        return isEventFileExist;
    }


}
