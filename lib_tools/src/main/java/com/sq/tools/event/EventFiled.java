package com.sq.tools.event;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sq.tools.proxy.Pair;
import com.sq.tools.utils.TimeUtils;

import org.json.JSONObject;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.UUID;

/**
 * <p>事件基础字段类，组装单个事件，用于保存事件信息，以进行存储文件或者发送到服务器等操作</p>
 * <br>原则上，外部调用不需要修改或者继承此类，事件特殊字段只需通过{@link EventsTracker#log(String, Pair...)} 中的 data 传递即可
 * <br><b>注意:</b> 若需修改增加此处的成员变量时，需要额外注意 {@link Serializable} 序列化问题
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class EventFiled extends FiledTools implements Serializable {

    @EventField("event") public String event;
    @EventField("msg_id") public String id;
    @EventField("cdate") public String timeDate;


    public EventFiled(@Nullable Context context, String event) {
        this(context, event, new JSONObject());
    }

    public EventFiled(@Nullable Context context, String event, JSONObject data) {
        this.event = event;
        this.id = UUID.randomUUID().toString();
        this.timeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA).format(new Date(TimeUtils.getLongServerTimeInMills()));
        this.data = data.toString();
    }


}
