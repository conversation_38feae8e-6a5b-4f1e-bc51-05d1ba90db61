package com.sq.tools.event;


import android.content.Context;

import com.sq.tools.network.ContentType;
import com.sq.tools.network.request.RequestTools;


/**
 * 组装事件请求的基本类型，供外部继承.
 * <br>此类以及其子类中只存储公共的字段，因性能以及代码抽象的考虑，这些字段会在且仅在将事件发送到服务器时被带上。
 * <br><b>注意：</b> 请勿在该类以及其子类中添加某条事件特有的字段，会影响其他事件的上报。
 * <br>外部需继承此类，以组装定制化的请求格式. 或者使用预组装好的默认通用请求参数 {@link DefaultCommonFiled}
 * <br>外部继承请在 {@link #fresh(Context)} 中初始化非固定值的字段参数
 */
public abstract class EventRequest extends RequestTools {


    protected EventRequest(Context context) {
        contentType = ContentType.JSON;
        fresh(context);
    }

    /**
     * This method will be called again just before send events to server.
     * So it could update some value if the value have not been set in init.
     */
    protected abstract void fresh(Context context);

}
