package com.sq.tools.event;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sq.tools.SQIds;
import com.sq.tools.network.request.RequestParam;
import com.sq.tools.utils.DeviceUtils;
import com.sq.tools.utils.HardwareUtils;
import com.sq.tools.utils.SoftwareUtils;

/**
 * 此默认字段是业务侧通用字段，参照如下文档整理，若对字段存疑，可参考文档:
 * https://vulenhv772.feishu.cn/sheets/shtcnnZvwcCf0rrIGcnoy1vq8iX?sheet=sa6ufz
 */
public class DefaultCommonFiled extends EventRequest{

    @RequestParam("appid") public String appid;
    @RequestParam("os") public String os;
    @RequestParam("os_version") public String osVersion;
    @RequestParam("country") public String country;
    @RequestParam("country_code") public String countryCode;
    @RequestParam("province") public String province;
    @RequestParam("city") public String city;
    @RequestParam("network_type") public String networkType;
    @RequestParam("ip") public String ip;
    @RequestParam("phone_brand") public String brand;
    @RequestParam("phone_model") public String model;
    @RequestParam("densit") public String density;
    @RequestParam("screen_height") public String screenHeight;
    @RequestParam("screen_width") public String screenWidth;
    @RequestParam("ram") public String ram;
    @RequestParam("sd_memory") public String memory;
    @RequestParam("cpu_hardware") public String cpu;
    @RequestParam("cpu_Ghz") public String cpuGhz;
    @RequestParam("cpu_core") public String cpuCore;
    @RequestParam("cpu_is_x86") public String cpuArch; // CPU architecture
    @RequestParam("imei") public String imei;
    @RequestParam("dev") public String devId;
    @RequestParam("imsi") public String imsi;
    @RequestParam("idfa") public String idfa;
    @RequestParam("oaid") public String oaid;
    @RequestParam("vaid") public String vaid;
    @RequestParam("aaid") public String aaid;
    @RequestParam("uuid") public String uuid;
    @RequestParam("android_id") public String androidId;
    @RequestParam("sim") public String simNum;
    @RequestParam("gid") public String gid;
    @RequestParam("refer") public String refer;
    @RequestParam("pid") public String pid;
    @RequestParam("cid") public String cid;
    @RequestParam("pgid") public String pGid;
    @RequestParam("tgid") public String tGid;
    @RequestParam("sversion") public String sVersion;
    @RequestParam("adversion") public String adVersion;
    @RequestParam("gwversion") public String gwVersion;
    @RequestParam("last_open_phone_ts") public String elapsedTime;
    @RequestParam("last_os_update_ts") public String elapsedOsTime;
    @RequestParam("apk_name") public String apkName;
    @RequestParam("targetVersion") public String targetVersion;
    @RequestParam("version_name") public String versionName;
    @RequestParam("version_code") public String versionCode;


    public DefaultCommonFiled(@NonNull Context context) {
        super(context);
        os = "1";
        idfa = "";
        province = "";
        city = "";
    }

    @Override
    protected void fresh(@NonNull Context context) {
        appid = "sdk";
        osVersion = SoftwareUtils.getOsVersion();
        country = SoftwareUtils.getCountry(context);
        countryCode = SoftwareUtils.getCountry(context);
        networkType = SoftwareUtils.getNetworkType(context);
        ip = SoftwareUtils.getIpAddress(context);
        brand = HardwareUtils.getBrand();
        model = HardwareUtils.getModel();
        density = String.valueOf(HardwareUtils.getDensity(context));
        screenHeight = Integer.toString(HardwareUtils.getScreenHeightPx(context));
        screenWidth = Integer.toString(HardwareUtils.getScreenWidthPx(context));
        ram = Long.toString(HardwareUtils.getTotalRam(context));
        memory = Long.toString(HardwareUtils.getExternalTotalMemory());
        cpu = HardwareUtils.getCpuModel();
        cpuGhz = HardwareUtils.getMaxCpuFreq();
        cpuCore = Integer.toString(HardwareUtils.getCpuCores());
        cpuArch = HardwareUtils.getCpuArch();
        imei = DeviceUtils.getIMEI(context);
        imsi = DeviceUtils.getIMSI(context);
        devId = DeviceUtils.getDevId(context);
        oaid = DeviceUtils.getOAID(context);
        vaid = DeviceUtils.getVAID(context);
        aaid = DeviceUtils.getAAID(context);
        uuid = DeviceUtils.getUUID(context);
        androidId = DeviceUtils.getAndroidId(context);
        simNum = Integer.toString(HardwareUtils.getCurrentSimNum(context));
        sVersion = SoftwareUtils.getSQSdkVersion();
        adVersion = SoftwareUtils.getADSdkVersion();
        gwVersion = SoftwareUtils.getGWVersion();
        elapsedTime = Long.toString(SoftwareUtils.getBootTime());
        elapsedOsTime = Long.toString(SoftwareUtils.getBuildTime());
        apkName = SoftwareUtils.getApkName(context);
        targetVersion = Integer.toString(SoftwareUtils.getTargetSdkVersion(context));
        versionCode = Integer.toString(SoftwareUtils.getVersionCode(context));
        versionName = SoftwareUtils.getVersionName(context);


        SQIds.ID.init(context);
        gid = SQIds.ID.gameId;
        refer = SQIds.ID.refer;
        pid = SQIds.ID.partner;
        cid = SQIds.ID.cid;
        pGid = SQIds.ID.pgid;
        tGid = SQIds.ID.tgid;

    }
}
