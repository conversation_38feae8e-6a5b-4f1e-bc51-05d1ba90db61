package com.sq.tools.event;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.RestrictTo;
import com.sq.tools.Logger;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <p>主要打点逻辑管理类, 向上暴露了添加事件，存储事件的方法</p>
 * <p> {@link EventQueue} 内部维护了一个 {@link EventCollection} 与 {@link ScheduledExecutorService},
 * 对于所有添加，发送，存储事件的操作，都在其维护的 {@link ScheduledExecutorService} 中进行处理, 以避免对主线程造成任何影响。</p>
 * <p>满足一定条件后，{@link EventQueue} 会将事件文件以及其维护的 {@link EventCollection} 中存储的事件发送到服务器
 * <ol>若发送成功，清空 {@link EventCollection}</ol>
 * <ol>若失败，将所有事件存储在文件中，等待下次发送服务器条件触发。并清空当前 {@link EventCollection}</ol></p>
 * <p>发送事件到服务器的条件:
 * <ol>添加事件时，{@link EventCollection} 中存储的事件数目超过了定义的 {@link EventQueue#maxFlushNum}</ol>
 * <ol>轮值调度达到发送时间，每隔一段时间会发送一次事件。第一次在 {@link EventQueue#INIT_DELAY} 分钟后，
 * 之后每 {@link EventQueue#EVENT_POST_PERIOD} 发送一次。</ol></p>
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
final class EventQueue {

    private final long INIT_DELAY = 1; // INIT_DELAY can not be 0, otherwise data will not ready in first call
    private final long EVENT_POST_PERIOD = 5;
    private final ScheduledExecutorService singleThreadExecutor = Executors.newSingleThreadScheduledExecutor();
    private final EventCollection collection = new EventCollection();

    int maxFlushNum = 100;
    final EventStorage storage;
    EventRequest common; // common filed for all events
    EventPost poster;
    String url;
    boolean isPostAvailable;

    EventQueue() {
        storage = new EventStorage();
        poster = new EventPost();
    }


    void schedulePost(@NonNull final Context context) {
        singleThreadExecutor.scheduleAtFixedRate(() -> {
            Logger.info(Logger.tag(EventsTracker.TAG), "Scheduled flush Event start, post events to service if we have, otherwise do nothing");
            flushEvent(context);
        }, INIT_DELAY, EVENT_POST_PERIOD, TimeUnit.MINUTES);
    }

    void add(final Context context, final FiledTools event) {
        if (event == null) return;
        singleThreadExecutor.execute(() -> {
            Logger.info(Logger.tag(EventsTracker.TAG), "Add one event, current events size: %d, event content: %s", collection.size(), event.toString());
            collection.add(event);
            if (context != null && collection.size() > maxFlushNum) {
                flushEvent(context);
            }
        });
    }

    void save(final Context context) {
        if(collection.isEmpty()) return;
        singleThreadExecutor.execute(() -> {
            if (storage.saveEventToDisk(context, collection)) {
                collection.clear();
            }
        });
    }

    void post(final Context context) {
        singleThreadExecutor.execute(() -> flushEvent(context));
    }


    /**
     * 仅当 {@link #isPostAvailable} 为 {@code true} 时才会执行操作
     * 若当前事件列表不为空，该方法会将所有事件发送到服务器，包括普通事件，异常等不同类型的事件。
     *  1. 若发送成功，清除当前所有事件（包括事件列表与文件中存储的事件)
     *  2. 若发送失败，将所有事件保存在本地
     * 若当前事件列表为空，且未存储事件，不做任何操作
     */
    private void flushEvent(Context context) {
        if(!isPostAvailable) return;
        EventCollection c = storage.readAndClearStore(context);
        collection.add(c);
        if (collection.isEmpty()) return;
        Logger.info(Logger.tag(EventsTracker.TAG), "start flush events...");

        if (!poster.sendToServer(context, common, collection, url)) {
            storage.saveEventToDisk(context, collection);
        }
        collection.clear();
    }

}
