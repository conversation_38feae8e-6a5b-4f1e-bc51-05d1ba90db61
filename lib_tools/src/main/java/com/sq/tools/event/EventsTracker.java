package com.sq.tools.event;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import com.sq.tools.Logger;
import com.sq.tools.annotation.Api;
import com.sq.tools.proxy.DefaultLifecycleCallbacks;
import com.sq.tools.proxy.Pair;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;


/**
 * 事件 API 类，此类主要提供事件相关 API, 也是外部使用事件 API 主要需要的类
 * <br>若需使用此类，可直接创建此类对象，每个对象内部均维护独立的一套事件调度，事件缓存，事件请求机制。
 * <br>对于单个项目而言，可以自己创建一个单例对象，供项目使用。
 */
@SuppressWarnings({"unused", "RedundantSuppression"})
public class EventsTracker {


    protected static String TAG = "sq_events";
    private final WeakReference<Context> context;
    private volatile boolean isEnable = true;
    private final EventQueue queue;

    /**
     * 事件 API 构造方法
     * @param c 上下文参数，建议直接传递 Application 级别的上下文
     * @param request 网络事件上报的参数类，可以是继承 {@link EventRequest} 的定制化类
     */
    public EventsTracker(@NonNull Context c, @NonNull EventRequest request, @NonNull String url) {
        this.context = new WeakReference<>(c);
        queue = new EventQueue();
        setEventCommonRequest(request);
        setEventUrl(url);
        queue.schedulePost(context.get());

        if(c instanceof Application) {
            registerActivityLife((Application) c);
        } else if(c instanceof Activity) {
            registerActivityLife(((Activity) c).getApplication());
        }
    }

    /**
     * 必须: 调用者需在合适时机(通常是获得网络请求允许后), 调用并设置 {@code true}，调用此方法后，上报服务器的网络请求才会被打开.
     * <br>默认: {@code false}  不进行服务器上报，而只是缓存并存储事件
     * <br>注意: 调用此方法后，并不表示会马上进行事件上报，事件上报仍然会按照其既定调度时机进行
     * @param isAvailable 是否允许上报
     */
    @Api public void setPostAvailable(boolean isAvailable) {
        queue.isPostAvailable = isAvailable;
    }

    /**
     * 构造方法中已调用: 设置事件对象请求服务器的 url
     * @param url 事件请求 url
     */
    @Api public void setEventUrl(String url) {
        if(!TextUtils.isEmpty(url)) queue.url = url;
    }

    /**
     * 构造方法中已调用: 设置事件请求的公共字段
     * @param common 事件请求的公共字段
     */
    @Api public void setEventCommonRequest(EventRequest common) {
        if(common != null) queue.common = common;
        else queue.common = new EventRequest(context.get()) {
            @Override
            protected void fresh(Context context) {}
        };
    }

    /**
     * 可选: 设置事件请求服务器的对象，可通过此方法重写服务器请求实现
     * <br>默认通用请求方法: {@link EventPost}
     * @param poster 事件请求服务器的对象
     */
    @Api public void setEventPoster(EventPost poster) {
        if(poster != null) queue.poster = poster;
    }

    /**
     * 可选: 设置事件存储文件名, 建议设置项目特有事件存储文件
     * <br>默认 'SQ_EVENT_LOG.log'
     * @param name 存储文件名, 只需文件名即可，例如: 'SQ_EVENT_LOG.log'
     */
    @Api public void setStorageFileName(String name) {
        if(!TextUtils.isEmpty(name)) queue.storage.setEventStorageFileName(name);
    }

    /**
     * 可选: 最大保存事件数
     * <br>默认: {@link EventQueue#maxFlushNum}
     * @param num 最大保存事件数，若事件在内存中缓存超过此数量，会尝试进行一次上报
     */
    @Api public void setFlushNum(int num) {
        if(num > 0) queue.maxFlushNum = num;
    }


    /**
     * 可选: 若项目有关闭事件打点需求，可调用此接口关闭事件收集, 此接口会直接关闭事件收集机制，但调度线程依旧会检查调度已经收集的事件
     * 默认: {@code true} 默认会进行事件收集
     * @param enable 是否进行事件收集的开关
     */
    @Api public void setEnable(boolean enable) {
        isEnable = enable;
    }

    /**
     * 事件收集 API, 在特定的事件触发时机调用此方法进行事件收集
     * @param event 事件名称
     * @param data key-value 形式，事件特有字段
     */
    @Api public void log(String event, Pair... data) {
        doLog(event, data);
    }

    private void doLog(String event, Pair... data) {
        if(!isEnable || TextUtils.isEmpty(event)) return;
        if(context == null || context.get() == null) {
            Logger.error(Logger.tag(EventsTracker.TAG), "EventsApi can not track event, since context is null, did you init EventsApi?");
            return;
        }

        JSONObject json = new JSONObject();
        if(data != null) {
            for (Pair datum : data) {
                try {
                    json.put(datum.name, datum.value);
                } catch (JSONException e) {
                    Logger.error(Logger.tag(EventsTracker.TAG), "EventLog do log put Json Exception", e);
                }
            }
        }

        EventFiled filed = new EventFiled(context.get(), event, json);
        queue.add(context.get(), filed);
    }

    @Api
    public void save(Context context) {
        queue.save(context);
    }

    @Api
    public void post(Context context) {
        queue.post(context);
    }

    private void registerActivityLife(Application application) {
        application.registerActivityLifecycleCallbacks(new DefaultLifecycleCallbacks() {
            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
                super.onActivitySaveInstanceState(activity, outState);
                save(application);
            }
        });
    }
}
