package com.sq.tools.event;

import android.content.Context;
import android.support.annotation.NonNull;
import com.sq.tools.Logger;

/**
 * 此类根据如下文档: https://37wiki.37wan.com/pages/viewpage.action?pageId=16091690 实现，
 * <br>根据联调, 其中的压缩算法需要使用 UTF-8 编码格式，而 Base64 算法使用 Java API 默认的 ISO_8859_1 格式。 文档本身描述错误，特此记录
 *
 * <br><br>若项目上传埋点使用非上述文档中的特殊方法，请继承该类，并在子类中重写 {@link #sendToServer(Context, EventRequest, EventCollection, String)}
 * 方法，再使用 {@link EventsTracker#setEventPoster(EventPost)} 设置请求对象
 * <br>若总体迁移，请更新此类实现
 */
public class EventPost {

    /**
     * 此方法供且仅供 {@link EventQueue} 调用，使用埋点功能，无需主动调用此方法。
     * 若需自定义上传埋点，可继承 {@link EventPost} 并重载此方法，再调用 {@link EventsTracker#setEventPoster(EventPost)} 设置即可
     * @return 上传是否成功
     */
    protected boolean sendToServer(@NonNull Context context, @NonNull EventRequest request, @NonNull EventCollection co, final String url) {
        Logger.error(Logger.tag(EventsTracker.TAG), "默认不支持sendToServer, 请自己实现请求方法");
        // TODO: 2023/8/16 当前场景用不到这个方法, 所以忽略
        return true;
    }
}
