package com.sq.tools.event;


import org.json.JSONArray;
import org.json.JSONObject;
import android.support.annotation.NonNull;
import android.support.annotation.RestrictTo;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>事件集合类，其中维护了一个事件列表，且因存储需要，该类需要可序列化</p>
 * <p>对于该类对象的操作，线程安全</p>
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP) @SuppressWarnings({"WeakerAccess", "unused", "RedundantSuppression"})
public final class EventCollection implements Serializable {

    // 序列化ID, 将该类进行存储时需要
    private static final long serialVersionUID = 2021_06_16_021L;

    final List<FiledTools> eventsList;

    EventCollection() {
        eventsList = new ArrayList<>();
    }

    synchronized void add(@NonNull FiledTools event) {
        eventsList.add(event);
    }

    synchronized void add(@NonNull List<FiledTools> events) {
        if (!events.isEmpty()) eventsList.addAll(events);
    }

    synchronized void add(@NonNull EventCollection collection) {
        if (!collection.eventsList.isEmpty()) eventsList.addAll(collection.eventsList);
    }

    synchronized void clear() {
        eventsList.clear();
    }

    public synchronized FiledTools get(int i) {
        return eventsList.get(i);
    }

    public synchronized int size() {
        return eventsList.size();
    }

    public synchronized boolean isEmpty() {
        return eventsList.size() == 0;
    }

    /**
     * 将事件列表集合中的每个事件组成一个 json 数组。
     * @return 新的事件集合 Json 数组。
     */
    public synchronized JSONArray toJsonArray() {
        JSONArray array = new JSONArray();
        for (int i = 0; i < eventsList.size(); i++) {
            JSONObject object = eventsList.get(i).toJsonObject();
            array.put(object);
        }
        return array;
    }

}
