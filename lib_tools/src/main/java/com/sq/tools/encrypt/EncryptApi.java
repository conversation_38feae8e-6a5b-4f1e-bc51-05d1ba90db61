package com.sq.tools.encrypt;

import android.content.Context;
import android.content.res.AssetManager;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import com.sq.tools.Logger;
import com.sq.tools.utils.DecodeUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;

public final class EncryptApi {

    /**
     * We don't wanna any other class extend or create a instance for {@link EncryptApi}
     */
    private EncryptApi() {
    }

    /**
     * Des encrypt data with default key
     */
    public static byte[] desEncrypt(Context context, byte[] source) {
        return desEncrypt(desKey(context), source);
    }

    /**
     * Des decrypt with default key
     */
    public static byte[] desDecrypt(Context context, byte[] source) {
        return desDecrypt(desKey(context), source);
    }

    /**
     * DES encrypt with give path key
     * @param key DES encrypt key
     * @param source the source need encrypt
     * @return encrypted data
     */
    public static byte[] desEncrypt(String key, byte[] source) {
        return DES.encrypt(source, key);
    }

    /**
     * DES decrypt with give path key
     * @param key DES decrypt key
     * @param source the source need decrypt
     * @return decrypted data
     */
    public static byte[] desDecrypt(String key, byte[] source) {
        return DES.decrypt(source, key);
    }

    @NonNull
    public static String sign(Context context, JSONObject json) {
        return sign(signKey(context), json);
    }

    @NonNull
    public static String sign(String key, JSONObject json) {
        StringBuilder sb = new StringBuilder();
        ArrayList<String> keys = new ArrayList<>();
        Iterator<String> j_keys = json.keys();
        while (j_keys.hasNext()) {
            keys.add(j_keys.next());
        }
        Collections.sort(keys);

        for (String ke : keys) {
            try {
                if (!TextUtils.isEmpty(json.getString(ke)))
                    sb.append(ke).append("=").append(json.getString(ke));
            } catch (JSONException e) {
                Logger.warning("Sign json object has Exception", e);
            }
        }
        return sign(key, sb.toString());
    }

    @NonNull
    public static String sign(Context context, String dataToEncrypt) {
        return sign(signKey(context), dataToEncrypt);
    }

    @NonNull
    public static String sign(String key, String dataToEncrypt) {
        return DecodeUtils.MD5(dataToEncrypt + key).toLowerCase();
    }

    protected static String desKey(Context context) {
        return readKey(context, "httpdns/dk");
    }

    protected static String signKey(Context context) {
        return readKey(context, "sq_games/sk");
    }

    private static String readKey(Context context, String path) {
        AssetManager asset = context.getAssets();
        String[] files = null;
        try {
            files = asset.list(path);
        } catch (IOException e) {
            Logger.error("Read Key exception since list failed", e);
            return "";
        }

        if (files == null || files.length == 0) {
            Logger.error("fail, there is no files in path %s", path);
            return "";
        }

        char[] chars = new char[files.length];
        for (String file : files) {
            if (TextUtils.isEmpty(file) || !file.contains("#")) continue;
            try {
                int i = Integer.parseInt((file.split("#"))[0]);
                if (i >= chars.length) continue;

                StringBuilder hide = new StringBuilder();
                for (int j = file.indexOf('#') + 1; j < file.length(); j++) {
                    if (Character.isDigit(file.charAt(j))) {
                        hide.append(file.charAt(j));
                    } else {
                        break;
                    }
                }
                chars[i] = (char) Integer.parseInt(hide.toString());
            } catch (NumberFormatException e) {
                Logger.error("Read Key parse int exception", e);
            }
        }
        return new String(chars).trim();
    }
}
