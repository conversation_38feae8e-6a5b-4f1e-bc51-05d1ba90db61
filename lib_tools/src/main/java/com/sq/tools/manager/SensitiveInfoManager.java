package com.sq.tools.manager;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.hardware.Sensor;
import android.hardware.SensorManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.Locale;

/**
 * author : 黄锦群
 * time   : 2022/08/12
 * desc   : 敏感信息调用管理类
 */
public final class SensitiveInfoManager {

    private static volatile SensitiveInfoManager sInstance;

    public static SensitiveInfoManager getInstance() {
        if (sInstance == null) {
            synchronized (SensitiveInfoManager.class) {
                if (sInstance == null) {
                    sInstance = new SensitiveInfoManager();
                }
            }
        }
        return sInstance;
    }

    private SensitiveInfoManager() {
    }

    public boolean mAuthCheck = false;

    /**
     * 是否同意了隐私
     */
    public boolean isAuthCheck() {
        return mAuthCheck;
    }

    /**
     * 设置是否同意隐私
     */
    public void setAuthCheck(boolean authCheck) {
        mAuthCheck = authCheck;
    }

    private String mIMEI;

    /**
     * 获取 IMEI 码
     */
    public String getIMEI(Context context) {
//        if (!isAuthCheck()) {
//            return "";
//        }
//
//        if (mIMEI != null) {
//            return mIMEI;
//        }
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
//                context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
//            return "";
//        }
//
//        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//        try {
//            mIMEI = telephonyManager.getDeviceId();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        if (mIMEI == null) {
//            mIMEI = "";
//        }
//        return mIMEI;

        // 新政策要求不能获取IMEI
        return "";
    }

    private String mIMSI;

    /**
     * 获取 IMSI 码
     */
    public String getIMSI(Context context) {
//        if (!isAuthCheck()) {
//            return "";
//        }
//
//        if (mIMSI != null) {
//            return mIMSI;
//        }
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
//                context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
//            return "";
//        }
//
//        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//        try {
//            mIMSI = telephonyManager.getSubscriberId();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        if (mIMSI == null) {
//            mIMSI = "";
//        }
//        return mIMSI;

        // 新政策要求不能获取IMSI
        return "";
    }

    private String mPhoneNumber;

    /**
     * 获取电话号码
     */
    public String getPhoneNumber(Context context) {
        if (!isAuthCheck()) {
            return "";
        }

        if (mPhoneNumber != null) {
            return mPhoneNumber;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            return "";
        }

        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        try {
            mPhoneNumber = telephonyManager.getLine1Number();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (mPhoneNumber == null) {
            mPhoneNumber = "";
        }
        return mPhoneNumber;
    }

    private String mMacAddress;

    /**
     * 获取网卡地址
     */
    public String getMacAddress(Context context) {
//        if (!isAuthCheck()) {
//            return "";
//        }
//
//        if (mMacAddress != null) {
//            return mMacAddress;
//        }
//
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
//            mMacAddress = getMacDefault(context);
//        } else if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
//            mMacAddress = getMacFromFile();
//        } else {
//            mMacAddress = getMacFromHardware();
//        }
//        return mMacAddress;

        // 新政策要求不能获取MAC地址
        return "";
    }

    /**
     * Android  6.0 之前（不包括6.0）
     * 必须的权限  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
     */
    private static String getMacDefault(Context context) {
        String mac = "";
        if (context == null) {
            return mac;
        }
        WifiManager wifi = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifi == null) {
            return mac;
        }
        WifiInfo info = null;
        try {
            info = wifi.getConnectionInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (info == null) {
            return mac;
        }
        mac = info.getMacAddress();
        if (!TextUtils.isEmpty(mac)) {
            mac = mac.toUpperCase(Locale.ENGLISH);
        }
        return mac;
    }

    /**
     * Android 6.0（包括） - Android 7.0（不包括）
     */
    private static String getMacFromFile() {
        String mac = "";
        String addressPath = "/sys/class/net/wlan0/address";
        try {
            mac = new BufferedReader(new FileReader(new File(addressPath))).readLine();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return mac;
    }

    private static String getMacFromHardware() {
        try {
            List<NetworkInterface> all = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface nif : all) {
                if (!"wlan0".equalsIgnoreCase(nif.getName())) {
                    continue;
                }
                byte[] macBytes = nif.getHardwareAddress();
                if (macBytes == null) {
                    return "";
                }

                StringBuilder res1 = new StringBuilder();
                for (byte b : macBytes) {
                    res1.append(String.format("%02X:", b));
                }

                if (res1.length() > 0) {
                    res1.deleteCharAt(res1.length() - 1);
                }
                return res1.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private String mAndroidId;

    /**
     * 获取 AndroidId
     */
    @SuppressLint({"HardwareIds"})
    public String getAndroidId(Context context) {
        if (!isAuthCheck()) {
            return "";
        }

        if (mAndroidId != null) {
            return mAndroidId;
        }

        try {
            mAndroidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        } catch (Exception e) {
            e.printStackTrace();
            // In some device, IllegalStateException happen: Failed to lookup info for package happen
        }

        if (mAndroidId == null) {
            mAndroidId = "";
        }
        return mAndroidId;
    }

    private String mIpAddress;

    /**
     * 获取 ip 地址
     */
    public String getIpAddress(Context context) {
        if (!isAuthCheck()) {
            return "";
        }

        if (mIpAddress != null) {
            return mIpAddress;
        }

        try {
            NetworkInfo info = ((ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();

            if (info == null || !info.isConnected()) {
                return "";
            }

            switch (info.getType()) {
                // 移动数据网络
                case ConnectivityManager.TYPE_MOBILE:
                    for (Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces(); networkInterfaces.hasMoreElements(); ) {
                        NetworkInterface networkInterface = networkInterfaces.nextElement();
                        for (Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses(); inetAddresses.hasMoreElements(); ) {
                            InetAddress inetAddress = inetAddresses.nextElement();

                            if (inetAddress.isLoopbackAddress()) {
                                continue;
                            }

                            if (!(inetAddress instanceof Inet4Address)) {
                                continue;
                            }

                            mIpAddress = inetAddress.getHostAddress();
                            break;
                        }
                    }
                    break;
                // WIFI 网络
                case ConnectivityManager.TYPE_WIFI:
                    WifiManager wifiManager = (WifiManager) context.
                            getApplicationContext().getSystemService(Context.WIFI_SERVICE);
                    WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                    if (wifiInfo != null) {
                        mIpAddress = intIpToStringIP(wifiInfo.getIpAddress());
                    }
                    break;
                // 有限网络
                case ConnectivityManager.TYPE_ETHERNET:
                    for (Enumeration<NetworkInterface> networkInterfaces = NetworkInterface
                            .getNetworkInterfaces(); networkInterfaces.hasMoreElements(); ) {
                        NetworkInterface networkInterface = networkInterfaces.nextElement();
                        for (Enumeration<InetAddress> inetAddresses = networkInterface
                                .getInetAddresses(); inetAddresses.hasMoreElements(); ) {
                            InetAddress inetAddress = inetAddresses.nextElement();
                            if (!inetAddress.isLoopbackAddress()
                                    && inetAddress instanceof Inet4Address) {
                                mIpAddress = inetAddress.getHostAddress();
                            }
                        }
                    }
                    if (mIpAddress == null) {
                        mIpAddress = "0.0.0.0";
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (mIpAddress == null) {
            mIpAddress = "";
        }
        return mIpAddress;
    }

    private static String intIpToStringIP(int ip) {
        return (ip & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                (ip >> 24 & 0xFF);
    }

    private String mIpV6Address;

    public String getIpV6Address(Context context) {
        if (!isAuthCheck()) {
            return "";
        }

        if (mIpV6Address != null) {
            return mIpV6Address;
        }

        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            return "";
        }
        try {
            NetworkInfo info = cm.getActiveNetworkInfo();
            if (info == null || !info.isConnected()) {
                return "";
            }

            for (Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                networkInterfaces.hasMoreElements(); ) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                for (Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                    inetAddresses.hasMoreElements(); ) {
                    InetAddress inetAddress = inetAddresses.nextElement();

                    if (!inetAddress.isLoopbackAddress() && isIpV6Address(inetAddress)) {
                        mIpV6Address = inetAddress.getHostAddress();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return mIpV6Address == null ? "" : mIpV6Address;
    }

    private static boolean isIpV6Address(InetAddress inetAddress) {
        if (inetAddress instanceof Inet6Address) {
            String ip = inetAddress.getHostAddress();
            // 2xxx开头的地址才是对外的ipv6
            return ip != null && ip.startsWith("2");
        }
        return false;
    }

    private List<String> mSensorList;

    /**
     * 获取所有传感器列表
     */
    public List<String> getSensorList(Context context) {
        if (!isAuthCheck()) {
            return new ArrayList<>();
        }

        if (mSensorList != null) {
            return mSensorList;
        }

        mSensorList = new ArrayList<>();

        SensorManager sensorManager = (SensorManager) context.getSystemService(Context.SENSOR_SERVICE);
        if (sensorManager != null) {
            List<Sensor> sensors = sensorManager.getSensorList(Sensor.TYPE_ALL);
            for (Sensor sensor : sensors) {
                String name = sensor.getName();
                mSensorList.add(name);
            }
        }
        return mSensorList;
    }
}