package com.sq.tools.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.support.annotation.NonNull;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import com.sq.tools.manager.SensitiveInfoManager;

/**
 * TODO annotation
 */
public final class DeviceUtils {


    /**
     * In Android 10 and all the google devices, imei is no longer support in future.
     * Let's slowly remove imei from our project.
     * @return IMEI if we get, otherwise empty
     */
    @SuppressLint({"MissingPermission", "HardwareIds"}) @NonNull
    public static String getIMEI(@NonNull Context context) {
        return SensitiveInfoManager.getInstance().getIMEI(context);
    }

    /**
     * @return IMSI if we get, otherwise empty
     */
    @SuppressLint({"MissingPermission", "HardwareIds"}) @NonNull
    public static String getIMSI(@NonNull Context context) {
        return SensitiveInfoManager.getInstance().getIMSI(context);
    }


    @SuppressLint({"MissingPermission", "HardwareIds"}) @NonNull
    public static String getSimSerialNumber(Context context){
        try {
            if (!PermissionUtils.hasAndroidPermission(context, Manifest.permission.READ_PHONE_STATE)) {
                return "";
            }
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (tm == null) return "";
            String serial = tm.getSimSerialNumber();
            return TextUtils.isEmpty(serial) ? "" : serial;
        } catch (Exception ignored){
            return "";
        }
    }

    /**
     * 三七客户端自定义设备 ID, 用来区分单个设备
     * <br>注意：客户端生成的设备 ID, 无法 100% 做到设备唯一，此值只做参考，不能作为设备唯一依旧
     * @return device ID
     */
    @NonNull public static String getDevId(@NonNull Context context) {
        return ""; //TODO implement
    }

    /**
     * @return 通用唯一识别码，使用随机数 + 时间戳生成
     */
    @NonNull public static String getUUID(@NonNull Context context) {
        return ""; //TODO implement
    }

    /**
     * @return 中国信通院发布的移动安全联盟 OAID
     */
    @NonNull public static String getOAID(@NonNull Context context) {
        return ""; //TODO implement
    }

    /**
     * @return 中国信通院发布的移动安全联盟 VAID
     */
    @NonNull public static String getVAID(@NonNull Context context) {
        return ""; //TODO implement
    }

    /**
     * @return 中国信通院发布的移动安全联盟 AAID
     */
    @NonNull public static String getAAID(@NonNull Context context) {
        return ""; //TODO implement
    }

    /**
     * 设备 Android ID, Android ID 在  Android 8 以及以上版本，会根据当前设备用户，APK安卓签名，设备，确定作用域，
     * <br>任一不同均会获取到不同的Android ID. 即 Android ID 不再能标识唯一设备了
     * @return Android ID
     */
    @SuppressLint({"HardwareIds"})
    @NonNull public static String getAndroidId(@NonNull Context context) {
        return SensitiveInfoManager.getInstance().getAndroidId(context);
    }


    /**
     * Trying to get Mac address in different way
     * @param context context
     * @return Mac address or empty if can not get
     * @deprecated please note, Mac address will be reset in every network connection after Android 11
     */
    @SuppressLint({"HardwareIds"}) @NonNull
    public static String getMacAddress(Context context) {
        return SensitiveInfoManager.getInstance().getMacAddress(context);
    }


    @SuppressLint({"MissingPermission", "HardwareIds"})
    @NonNull
    public static String getLine1Number(Context context){
        return SensitiveInfoManager.getInstance().getPhoneNumber(context);
    }

    private DeviceUtils(){}
}
