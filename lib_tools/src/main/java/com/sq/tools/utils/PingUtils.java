package com.sq.tools.utils;

import android.text.TextUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.regex.Pattern;

public class PingUtils {
    //ip地址正则表达式
    private static final String ipRegex =
            "((?:(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d))))";

    //默认超时时间
    private static final int TIME_OUT = 5;
    //默认ping的次数
    private static final int DEFAULT_COUNT = 3;

    /**
     * ping指令格式文本
     *
     * @param count   调用次数
     * @param timeout 超时时间
     * @param domain  地址
     * @return 返回完整的ping命令
     */
    private static String createSimplePingCommand(int count, int timeout, String domain) {
        return "/system/bin/ping -c " + count + " -w " + timeout + " " + domain;
    }

    /**
     * ping方法，调用ping指令
     *
     * @param command ping指令文本
     * @return 返回ping命令后的内容
     */
    private static String ping(String command) {
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(command);       //执行ping指令
            InputStream is = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(is));
            StringBuilder sb = new StringBuilder();
            String line;
            while (null != (line = reader.readLine())) {
                sb.append(line);
                sb.append("\n");
            }
            reader.close();
            is.close();
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != process) {
                process.destroy();
            }
        }
        return "";
    }

    /**
     * 获取ping结果
     *
     * @param url 域名
     * @return 返回ping结果
     */
    public static PingResult getPingResult(String url) {
        return getPingResult(url, DEFAULT_COUNT, TIME_OUT);
    }

    /**
     * 获取ping结果
     *
     * @param url     域名
     * @param count   ping次数，默认三次
     * @param timeout 超时时长，单位s，默认5s
     * @return 返回ping结果
     */
    public static PingResult getPingResult(String url, int count, int timeout) {
        String domain = getDomain(url);
        if (null == domain) {
            return null;
        }
        PingResult pingResult = new PingResult();
        pingResult.setDomain(domain);
        String pingString = ping(createSimplePingCommand(count, timeout, domain));
        if (TextUtils.isEmpty(pingString)) {
            return pingResult;
        }
        pingResult.setPingDesc(pingString);
        try {
            //获取以"min/avg/max/mdev"为头的文本，分别获取此次的ping参数
            if (!pingString.contains("min/avg/max/mdev")) {
                return pingResult;
            }
            String tempInfo = pingString.substring(pingString.indexOf("min/avg/max/mdev") + 19);
            String[] temps = tempInfo.split("/");
            float min = Float.parseFloat(temps[0]);
            float avg = Float.parseFloat(temps[1]);
            float max = Float.parseFloat(temps[2]);
            String tempMdev = temps[3];
            String mdevStr = tempMdev.substring(0, tempMdev.length() - 4);
            float mdev = Float.parseFloat(mdevStr);
            pingResult.setMin(min);
            pingResult.setAvg(avg);
            pingResult.setMax(max);
            pingResult.setMdev(mdev);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pingResult;
    }

    // ********************以下是一些辅助方法，设为private********************//

    /**
     * 域名获取
     *
     * @param url 网址
     * @return 获取域名
     */
    private static String getDomain(String url) {
        String domain = null;
        try {
            domain = URI.create(url).getHost();
            if (null == domain && isMatch(ipRegex, url)) {
                domain = url;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return domain;
    }

    private static boolean isMatch(String regex, String string) {
        return Pattern.matches(regex, string);
    }


}
