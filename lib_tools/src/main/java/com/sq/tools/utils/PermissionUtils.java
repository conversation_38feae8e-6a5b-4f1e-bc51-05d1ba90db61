package com.sq.tools.utils;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import com.sq.tools.Logger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings({"unused", "RedundantSuppression"})
public final class PermissionUtils {

    private static final List<PermissionBundle> permissionsList = new ArrayList<>();

    /**
     * This flag is locked after {@link #requestSinglePermission(Activity)} called,
     * and only unlocked after {@link #onRequestPermissionsResult(Activity, int, String[], int[])} processed
     */
    private static volatile boolean isRequesting;

    /**
     * Check request permission is granted or not.
     *
     * @param context   context
     * @param permission the permission you would like to check, e.g: Manifest.permission.WRITE_EXTERNAL_STORAGE
     * @return In API 23 and above
     * return Permission granted or not
     * <br>Otherwise always return {@code true}
     */
    @TargetApi(Build.VERSION_CODES.M)
    public static boolean hasAndroidPermission(@NonNull Context context, @NonNull String permission) {
        if (SoftwareUtils.getAndroidVersion() >= Build.VERSION_CODES.M) {
            return context.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
        } else {
            return true;
        }
    }

    /**
     * Check if Permission define in AndroidManifest.xml or not
     * @return true if defined in AndroidManifest, otherwise false
     */
    public static boolean isPermissionDefine(Activity activity, String permission) {
        try {
            PackageManager manager = activity.getApplication().getPackageManager();
            PackageInfo packageInfo = manager.getPackageInfo(activity.getApplication().getPackageName(), PackageManager.GET_PERMISSIONS);
            if(packageInfo != null) {
                String[] permissions = packageInfo.requestedPermissions;
                for(String p: permissions) {
                    if(p.equals(permission)) {
                        return true;
                    }
                }
            } else {
                Logger.error("PackageInfo is null, can not figure out permission define or not");
            }
            return false;
        } catch (PackageManager.NameNotFoundException e) {
            Logger.error("Can not figure out permission define or not since can not get PackageInfo from application", e);
            return false;
        }
    }

    /**
     * Jump to App setting to guide user grant permission
     * @param activity context
     */
    public static void jumpAppSetting(Activity activity) {
        activity.startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                Uri.parse("package:" + activity.getPackageName())));
    }

    /**
     * Request for one single Android Permission, the result will be return in {@link PermissionCallback}.
     * <br>{@link PermissionCallback#onGranted(String)} will be automatic call if device is under Android M or permission already granted
     * <br>{@link PermissionCallback#onDenied(String)} will be automatic call if permission is not defined in AndroidManifest
     * @param activity Context
     * @param permission permission need request, should be string like  android.Manifest.permission.READ_PHONE_STATE
     * @param callback result callback
     */
    public static void requestAndroidPermission(@NonNull Activity activity, @NonNull String permission, PermissionCallback callback) {
        requestAndroidPermission(activity, new String[]{permission}, callback);
    }

    /**
     * Request for one or more Android permission, permissions will be request one by one.
     * <br>Every time a permission request, the result will be passed in {@link PermissionCallback}
     * <br>In this method, all the permission will use same {@link PermissionCallback}.
     * <br>Check {@link #requestAndroidPermission(Activity, String[], PermissionCallback[])} to use different callback for different permission
     * @param activity Context
     * @param permissions a array of permission need request
     * @param callback result callback for all permissions
     */
    public static void requestAndroidPermission(@NonNull Activity activity, @NonNull String[] permissions, PermissionCallback callback) {
        PermissionCallback[] callbacks = new PermissionCallback[permissions.length];
        for(int i = 0; i < permissions.length; i++) {
            callbacks[i] = callback;
        }
        requestAndroidPermission(activity, permissions, callbacks);
    }

    /**
     * All the {@link #requestAndroidPermission} methods will eventually call this method
     * Request one or more Android permission and return the result in every specific callback
     * <br>Every permission should has one callback, therefore the length of permission array should same as callback array,
     * otherwise a error will be logged and will not process the request
     * @param activity context
     * @param permissions a array of permission need request
     * @param callbacks a array of callback for different permission
     */
    public static void requestAndroidPermission(@NonNull final Activity activity, @NonNull final String[] permissions, @NonNull final PermissionCallback[] callbacks) {
        activity.runOnUiThread(() -> requestPermissions(activity, permissions, callbacks));
    }

    private static void requestPermissions(@NonNull Activity activity, @NonNull String[] permissions, @NonNull PermissionCallback[] callbacks) {
        if(callbacks.length != permissions.length) {
            Logger.error("Something went wrong in Permission utils, the length of permissions is not same as callbacks, do nothing now");
            return;
        }
        if (SoftwareUtils.getAndroidVersion() < Build.VERSION_CODES.M) {
            Logger.info("Request Permission on Device with Android version under M, grant permission without request");
            onResult(callbacks, true, permissions);
            return;
        }

        for(int i = 0; i < permissions.length; i++) {
            if(TextUtils.isEmpty(permissions[i])) continue;
            if(hasAndroidPermission(activity.getApplicationContext(), permissions[i])) {
                onResult(callbacks[i], true, permissions[i]);
                continue;
            }
            if(!isPermissionDefine(activity, permissions[i])) {
                onResult(callbacks[i], false, permissions[i]);
                Logger.error("please define permission %s in AndroidManifest before request, will not process to request permission", permissions[i]);
                continue;
            }
            PermissionBundle bundle = new PermissionBundle(permissions[i], permissionsList.size() + 100, callbacks[i]);
            permissionsList.add(bundle);
        }
        requestSinglePermission(activity);
    }

    private static void requestSinglePermission(Activity activity) {
        if(permissionsList.size() == 0) return;
        final PermissionBundle bundle = permissionsList.get(0);
        // We check again the permission has grant here, since now we are not filter repeat permission in list
        // So it may happen the permission granted after add into list
        if(hasAndroidPermission(activity.getApplicationContext(), bundle.permission)) {
            onResult(bundle.callback, true, bundle.permission);
            permissionsList.remove(bundle);
            return;
        }
        if(isRequesting) return;
        isRequesting = true;
        Logger.debug("Start request permission %s, request code: %d", bundle.permission, bundle.requestCode);
        Intent intent = new Intent(activity, PermissionActivity.class);
        intent.putExtra("permission_request", bundle.permission);
        intent.putExtra("permission_code", bundle.requestCode);
        activity.startActivity(intent);
    }

    protected static void onRequestPermissionsResult(Activity activity, int code, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if(permissionsList.size() == 0) return;
        Logger.debug("PermissionUtil onRequestPermissionsResult, code: %d, permissions: %s, grant result: %s", code, Arrays.toString(permissions), Arrays.toString(grantResults));
        for(int i = 0; i < permissionsList.size(); i++) {
            PermissionBundle bundle = permissionsList.get(i);
            if(bundle.requestCode == code) {
                permissionsList.remove(bundle);
                onResult(bundle.callback, grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED, bundle.permission);
            }
        }
        isRequesting = false;
        if(!permissionsList.isEmpty()) requestSinglePermission(activity);
    }


    private static void onResult(PermissionCallback callback, boolean isGrant, String permission) {
        PermissionCallback[] callbacks = new PermissionCallback[1];
        callbacks[0] = callback;
        onResult(callbacks, isGrant, new String[]{permission});
    }

    private static void onResult(PermissionCallback[] callbacks, boolean isGrant, String[] permissions) {
        for(int i = 0; i < callbacks.length; i++) {
            if(callbacks[i] != null) {
                if (isGrant) {
                    callbacks[i].onGranted(permissions[i]);
                } else {
                    callbacks[i].onDenied(permissions[i]);
                }
            }
        }
    }

    /**
     * When Request multi permissions, every permission should have one unique {@link PermissionCallback}
     * <br>{@link #onGranted(String)} will ba called when the matched permission granted
     * <br>{@link #onDenied(String)} will be called when the matched permission denied
     * <br>The passed String is the permission granted or denied
     */
    public interface PermissionCallback{
        void onGranted(String grantPermission);
        void onDenied(String deniedPermissions);
    }

    static class PermissionBundle {
        String permission;
        int requestCode;
        PermissionCallback callback;

        PermissionBundle(@NonNull String permission, int requestCode, PermissionCallback callback) {
            this.permission = permission;
            this.requestCode = requestCode;
            this.callback = callback;
        }
    }
}
