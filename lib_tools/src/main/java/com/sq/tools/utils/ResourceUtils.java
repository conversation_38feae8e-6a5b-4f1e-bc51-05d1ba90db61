package com.sq.tools.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Drawable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.RestrictTo;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;


/**
 * <p>
 * Android 不同 Resource 的获取方法，库开发中不可以直接引用 {@code R} 文件，
 * 故使用动态获取 {@code id} 的形式获取对应资源。
 * </p>
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressWarnings({"WeakerAccess", "unused"})
public abstract class ResourceUtils {
    /**
     * This method will be removed when we support all resource types
     * Get Identifier for named resource
     *
     * @param context context
     * @param name     resource name
     * @param defType  resource name type, check here for available types
     *                 https://developer.android.com/guide/topics/resources/available-resources.html?hl=es
     * @return resource id in real APK R.java,return 0 if id not found or params invalid
     */
    public static int getId(Context context, String name, String defType) {
        if (context == null) return 0;
        else return context.getResources().getIdentifier(name, defType, context.getPackageName());
    }

    public static int getColor(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "color", context.getPackageName());
            return (id == 0) ? 0 : context.getResources().getColor(id);
        } else {
            return 0;
        }
    }

    public static float getDimension(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "dimen", context.getPackageName());
            return (id == 0) ? 0 : context.getResources().getDimension(id);
        } else {
            return 0;
        }
    }

    @NonNull
    public static String getString(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "string", context.getPackageName());
            return (id == 0) ? "" : context.getResources().getString(id);
        } else {
            return "";
        }
    }

    @NonNull
    public static int[] getIntArray(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "array", context.getPackageName());
            return (id == 0) ? new int[0] : context.getResources().getIntArray(id);
        } else {
            return new int[0];
        }
    }

    @NonNull
    public static String[] getStringArray(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "array", context.getPackageName());
            return (id == 0) ? new String[0] : context.getResources().getStringArray(id);
        } else {
            return new String[0];
        }
    }

    @Nullable
    public static Boolean getBoolean(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "bool", context.getPackageName());
            return (id == 0) ? null : context.getResources().getBoolean(id);
        } else {
            return null;
        }
    }

    @Nullable
    public static Integer getInteger(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "integer", context.getPackageName());
            return (id == 0) ? null : context.getResources().getInteger(id);
        } else {
            return null;
        }
    }

    @Nullable
    public static AssetManager getAsset(Context context) {
        if (context != null) {
            return context.getResources().getAssets();
        } else {
            return null;
        }
    }

    @Nullable
    public static XmlResourceParser getAnim(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "anim", context.getPackageName());
            return (id == 0) ? null : context.getResources().getAnimation(id);
        } else {
            return null;
        }
    }

    @Nullable
    public static Drawable getDrawable(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "drawable", context.getPackageName());
            return (id == 0) ? null : context.getResources().getDrawable(id);
        } else {
            return null;
        }
    }

    @Nullable
    public static XmlResourceParser getLayout(Context context, String name) {
        if (context != null) {
            int id = context.getResources().getIdentifier(name, "layout", context.getPackageName());
            return (id == 0) ? null : context.getResources().getLayout(id);
        } else {
            return null;
        }
    }

    public static int getLayoutIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "layout");
    }

    public static int getColorIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "color");
    }

    public static int getArrayIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "array");
    }

    public static int getStringIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "string");
    }

    public static String getStringByName(Context context, String resourcesName) {
        return context.getResources().getString(getId(context, resourcesName, "string"));
    }

    public static int getViewIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "id");
    }

    public static int getDrawableIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "drawable");
    }

    public static int getMipmapIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "mipmap");
    }

    public static int getAnimIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "anim");
    }

    public static int getStyleIdByName(Context context, String resourcesName) {
        return getId(context, resourcesName, "style");
    }

    @SuppressWarnings("unchecked")
    public static <T> T getAttributeSetValue(Context context, AttributeSet attrs, String name, Class<T> cls, T defaultValue) {
        T obj = defaultValue;
        if (TextUtils.isEmpty(name)) return obj;
        if (cls == null) return obj;
        int attributeCount = attrs.getAttributeCount();
        for (int i = 0; i < attributeCount; i++) {
            String attributeName = attrs.getAttributeName(i);
            if (name.equals(attributeName)) {
                String attributeValue = attrs.getAttributeValue(i);
                if (cls.equals(String.class)) {
                    obj = (T) attributeValue;
                } else if (cls.equals(Boolean.class)) {
                    obj = (T) Boolean.valueOf(attributeValue);
                } else if (cls.equals(Drawable.class)) {
                    int id = Integer.parseInt(attributeValue.replace("@", ""));
                    obj = (T) context.getResources().getDrawable(id);
                } else if (cls.equals(View.class)) {
                    int id = Integer.parseInt(attributeValue.replace("@", ""));
                    obj = (T) LayoutInflater.from(context).inflate(id, null);
                }
                break;
            }
        }
        return obj;
    }
}
