package com.sq.tools.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import com.sq.tools.Logger;
import com.sq.tools.manager.SensitiveInfoManager;
import com.sq.tools.network.Network;
import java.net.NetworkInterface;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class SoftwareUtils {

    private static final Pattern PATTERN_IS_IP_ADDRESS = Pattern.compile("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}");


    /**
     * @return 当前设备 Android API 版本，每个 API 版本代表一个 Android 版本.
     * Android 版本与 API 对应关系可以参见:
     * https://source.android.com/setup/start/build-numbers
     */
    public static int getAndroidVersion() {
        return Build.VERSION.SDK_INT;
    }

    /**
     * @return 用户可见的设备系统版本号
     */
    @NonNull public static String getOsVersion() {
        return TextUtils.isEmpty(Build.VERSION.RELEASE) ? "" : Build.VERSION.RELEASE;
    }

    /**
     * @return 返回国家英文代码，例如中国 -> 'CN'
     */
    @NonNull public static String getCountry(@NonNull Context context) {
        TelephonyManager manager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if(manager == null || TextUtils.isEmpty(manager.getNetworkCountryIso())) {
            return context.getResources().getConfiguration().locale.getCountry().toUpperCase();
        }
        return manager.getNetworkCountryIso().toUpperCase();
    }


    /**
     * @return 客户端 IP, 通过蜂窝网络{@link NetworkInterface} 或者 {@link WifiManager} 获取。 无法获取则返回 {@code ""}
     */
    @NonNull
    public static String getIpAddress(Context context) {
        return SensitiveInfoManager.getInstance().getIpAddress(context);
    }

    /**
     *
     * 返回给定 {@link Throwable} 排重后的堆栈信息
     * <br>因直接调用 {@link Throwable#printStackTrace()} 打印出的堆栈，因堆栈信息过长可能造成 {@link OutOfMemoryError} 错误,
     * <br>故此处我们直接自己处理堆栈打印
     * <br>默认限制最高堆栈大小: 2M
     * @param throwable 需要获取堆栈信息的异常对象
     *
     * @return 给定 {@link Throwable} 排重后的堆栈信息，即如果堆栈信息中有任何一行重复的信息，则其会被压缩记录，如:
     *         e.g: "at java.util.Arrays.copyOf(Arrays.java:3260) *10" 替代 10 行 "at java.util.Arrays.copyOf(Arrays.java:3260)"
     */
    public static String getStackTrace(Throwable throwable) {
        return getStackTrace(throwable, 2048 * 1024, "");
    }

    private static String getStackTrace(Throwable throwable, int limit, String duplicates) {
        if (limit <= 0) return "";
        if(throwable == null) return "";
        if (throwable instanceof UnknownHostException) {
            return deduplicate(throwable.toString());
        }
        StackTraceElement[] elements = throwable.getStackTrace();
        StringBuilder stack = new StringBuilder();

        String cleans = deduplicate(throwable.toString());
        if (cleans.length() < limit) {
            stack.append(cleans);
        }

        for (StackTraceElement element: elements) {
            if(stack.length() >= limit) break;
            if(element == null) continue;
            if(stack.toString().contains(element.toString())) {
                int start = stack.toString().indexOf(element.toString());
                int end = start + element.toString().length();
                String bridge = " *";
                if(stack.toString().contains(element.toString() + bridge)) {
                    int i = end + bridge.length();
                    while (Character.isDigit(stack.charAt(i))) {
                        i++;
                    }
                    if (i != end + bridge.length()) {
                        int num = Integer.parseInt(stack.substring(end + bridge.length(), i));
                        stack.replace(start, i, element.toString() + bridge + (num + 1));
                    }
                } else {
                    stack.replace(start, end, element.toString() + bridge + "2");
                }
                continue;
            }
            if(duplicates.contains(element.toString())) continue;
            stack.append("\tat ").append(element.toString()).append("\n");
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            for (Throwable se : throwable.getSuppressed()) {
                stack.append(getStackTrace(se, limit - stack.length(), stack.toString() + duplicates));
            }
        }

        Throwable ourCause = throwable.getCause();
        if (ourCause != null)
            stack.append("Caused by: ").append(getStackTrace(ourCause, limit - stack.length(), stack.toString() + duplicates));
        return stack.toString();
    }


    /**
     * 移除给定字符串的重复行
     * @param s 给定字符串
     * @return 移除重复行后的字符串, 返回 {@code ""} 如果给定字符串为 {@code null} 或 {@code ""}
     */
    @NonNull public static String deduplicate(String s) {
        if (TextUtils.isEmpty(s)) return "";
        StringBuilder sb = new StringBuilder();
        boolean flag = false;
        for (String line: s.split("\n")) {
            if(!sb.toString().contains(line)) {
                sb.append(line).append("\n");
            } else if(!flag) {
                sb.append("CLEAN DUPLICATE INFO").append("\n");
                flag = true;
            }
        }
        return sb.toString();
    }


    /**
     * <p>
     * This method return current Network type with a {@link Network} enum.
     * </p>
     * <p>
     * In Android 28 and above, NetworkInfo is @deprecated. And we may need switch to {@link android.net.NetworkCapabilities}
     * <ol>
     * see <a href="https://developer.android.com/reference/android/net/NetworkCapabilities">NetworkCapabilities</a>
     * </ol>
     * </p>
     * <p>
     * In order to get {@link android.net.NetworkCapabilities} you can use following code:
     * </p>
     * <pre>
     *     {@code
     *          ConnectivityManager manager = (ConnectivityManager)activity.getSystemService(Context.CONNECTIVITY_SERVICE);
     *          Network network = manager.getActiveNetwork();
     *          manager.getNetworkCapabilities(network);
     *     }
     * </pre>
     *
     * @param context  context
     * @return {@link Network.NetworkType} Network type enum
     *
     * Sometimes this method will throw {@link SecurityException} in Android 10 and above even permission configured,
     * This method need be refactor some day, check
     * https://stackoverflow.com/questions/23329590/android-crash-on-getactivenetworkinfo
     */
    @NonNull public static @Network.NetworkType String getNetworkType(@NonNull Context context) {
        try {
            ConnectivityManager connectivityManager = ((ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE));
            if (connectivityManager == null) return Network.UNKNOWN;
            @SuppressLint("MissingPermission") NetworkInfo info = connectivityManager.getActiveNetworkInfo();
            if (info == null) return Network.UNKNOWN;
            if (info.getType() == 1) return Network.WIFI;
            if (info.getType() == 0) {
                TelephonyManager telephonyManager = ((TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE));
                if (telephonyManager == null) return Network.UNKNOWN;
                int type = telephonyManager.getNetworkType();
                switch (type) {
                    case TelephonyManager.NETWORK_TYPE_GPRS:
                    case TelephonyManager.NETWORK_TYPE_EDGE:
                    case TelephonyManager.NETWORK_TYPE_CDMA:
                    case TelephonyManager.NETWORK_TYPE_1xRTT:
                    case TelephonyManager.NETWORK_TYPE_IDEN:
                    case TelephonyManager.NETWORK_TYPE_GSM:
                        return Network.MOBILE_2G;

                    case TelephonyManager.NETWORK_TYPE_TD_SCDMA:
                    case TelephonyManager.NETWORK_TYPE_UMTS:
                    case TelephonyManager.NETWORK_TYPE_EVDO_0:
                    case TelephonyManager.NETWORK_TYPE_EVDO_A:
                    case TelephonyManager.NETWORK_TYPE_HSDPA:
                    case TelephonyManager.NETWORK_TYPE_HSUPA:
                    case TelephonyManager.NETWORK_TYPE_HSPA:
                    case TelephonyManager.NETWORK_TYPE_EVDO_B:
                    case TelephonyManager.NETWORK_TYPE_EHRPD:
                    case TelephonyManager.NETWORK_TYPE_HSPAP:
                        return Network.MOBILE_3G;

                    case TelephonyManager.NETWORK_TYPE_LTE:
                        return Network.MOBILE_5G;

                    case TelephonyManager.NETWORK_TYPE_IWLAN:
                        return Network.WIFI;

                    case TelephonyManager.NETWORK_TYPE_UNKNOWN:
                        return Network.UNKNOWN;
                }
                String subName = info.getSubtypeName();
                if (subName.equalsIgnoreCase("TD-SCDMA") || subName.equalsIgnoreCase("WCDMA") || subName.equalsIgnoreCase("CDMA2000")) {
                    return Network.MOBILE_3G;
                }

            }
        } catch (SecurityException e) {
            Logger.warning("ACCESS_NETWORK_STATE permission may miss or it's some weird device", e);
        } catch (Exception e) {
            // As for research，in {@code getActiveNetworkInfo}, To got {@code NetworkInfo},
            // it called {@code State.valueOf(in.readString())};, and the method {@code Parcel#readString} was marked as Nullable.
            // But {@code Enum.valueOf(String)} require {@code Nonull} object as argument, in this case, NullPointerException happen.
            // Since it inside Android source code, What we can do is surround it with try catch.
            Logger.warning("Get network info in weird device", e);
        }

        return Network.UNKNOWN;
    }

    /**
     * @return SQ SDK 版本
     */
    @NonNull public static String getSQSdkVersion() {
        // TODO implement
        return "";
    }

    /**
     * @return 广告 SDK 版本
     */
    @NonNull public static String getADSdkVersion() {
        // TODO implement
        return "";
    }

    /**
     * @return SQ SDK 业务版本
     */
    @NonNull public static String getGWVersion() {
        // TODO implement
        return "";
    }

    /**
     * @return 手机上次开机时间的 unix 时间戳, 以毫秒作为最低位单位
     */
    public static long getBootTime() {
        return System.currentTimeMillis() - SystemClock.elapsedRealtime();
    }

    /**
     * @return 手机系统上次更新的时间
     */
    public static long getBuildTime() {
        return Build.TIME;
    }


    /**
     * 监测到此方法可发生:
     * java.lang.RuntimeException: android.os.DeadSystemException may happen in {@link PackageManager#getPackageInfo}
     * <br>此异常发生在系统层级，并且我们尚未定位到具体原因
     * <br>目前，我们简单粗暴在此方法中捕获所有 {@link Exception} 异常
     * <br>{@link #getVersionName(Context)} 也存在相同情况
     */
    public static int getVersionCode(@NonNull Context context) {
        int versionCode = -1;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                versionCode = (int) context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_CONFIGURATIONS).getLongVersionCode();
            } else {
                versionCode = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_CONFIGURATIONS).versionCode;
            }
        } catch (Throwable e) {
            Logger.warning("Exception happen when trying to get version code", e);
        }
        return versionCode;
    }

    /**
     * 监测到此方法可发生:
     * java.lang.RuntimeException: android.os.DeadSystemException may happen in {@link PackageManager#getPackageInfo}
     * <br>此异常发生在系统层级，并且我们尚未定位到具体原因
     * <br>目前，我们简单粗暴在此方法中捕获所有 {@link Exception} 异常
     * <br>{@link #getVersionCode(Context)} 也存在相同情况
     */
    public static String getVersionName(@NonNull Context context) {
        String versionName = "-1";
        try {
            versionName = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_CONFIGURATIONS).versionName;
        } catch (Throwable e) {
            Logger.warning("Exception happen when trying to get version name", e);
        }
        return versionName;
    }

    /**
     * @return APP 名称
     */
    public static String getApkName(@NonNull Context context) {
        ApplicationInfo applicationInfo = context.getApplicationInfo();
        int stringId = applicationInfo.labelRes;
        return stringId == 0 ? applicationInfo.nonLocalizedLabel.toString() : context.getString(stringId);
    }

    /**
     * @return APP target SDK version
     */
    public static int getTargetSdkVersion(@NonNull Context context) {
        return context.getApplicationContext().getApplicationInfo().targetSdkVersion;
    }

    /**
     * @return The APK is debug build or release build
     */
    public static boolean isDebugBuild(@NonNull Context context) {
        return 0 != (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE );
    }

    /**
     * 返回给定 url 中的域名
     * @param url 请求的 API url. 如 http://yapi.39on.com/mock/75/go/cfg/v2/float_window
     * @return API url 的域名, 如 yapi.39on.com
     */
    public static String getHost(String url) {
        if(TextUtils.isEmpty(url)) return url;
        try {
            URI uri = new URI(url);
            return uri.getHost();
        } catch (URISyntaxException e) {
            Logger.error("Trying to get host from Illegal url %s", url, e);
            return "";
        }
    }

    /**
     * 返回给定 url 中的协议
     * @param url 请求的 API url. 如 http://yapi.39on.com/mock/75/go/cfg/v2/float_window
     * @return API url 的协议, 如 http/https
     */
    public static String getProtocol(String url) {
        if(TextUtils.isEmpty(url)) return "";
        try {
            URL uri = new URL(url);
            return uri.getProtocol();
        } catch (Exception e) {
            Logger.error("Trying to get host from Illegal url %s", url, e);
            return "";
        }
    }

    /**
     * 检查给定 url 是否是IP形式的请求
     * @param url 给定 url
     * @return true -> IP 形式的请求 false -> 非 IP 形式的请求
     */
    public static boolean isIpv4Url(String url) {
        if(TextUtils.isEmpty(url)) return false;

        String ip = getHost(url);
        if(TextUtils.isEmpty(ip)) ip = url;

        String[] parts = ip.split("\\.", -1);
        if(parts.length != 4) return false;
        try {
            for (String s : parts) {
                int i = Integer.parseInt(s);
                if(i < 0 || i > 255) return false;
                if(!s.equals(String.valueOf(i))) return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isIpAddress(String address) {
        if (TextUtils.isEmpty(address)) {
            return false;
        } else {
            Matcher matcher = PATTERN_IS_IP_ADDRESS.matcher(address);
            return matcher.matches();
        }
    }

    private SoftwareUtils(){}
}
