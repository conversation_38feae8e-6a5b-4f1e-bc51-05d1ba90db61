package com.sq.tools.utils;

public class PingResult {

    //ping命令获取的内容
    private String pingDesc;

    //最小值
    private float min;

    //平均值
    private float avg;

    //最大值
    private float max;

    //ICMP 包的 RTT 偏离平均值的程度，这个值越大说明你的网速越不稳定。
    private float mdev;

    //域名
    private String domain;


    @Override
    public String toString() {
        return "PingResult{" +
                "pingDesc='" + pingDesc + '\'' +
                ", min=" + min +
                ", avg=" + avg +
                ", max=" + max +
                ", mdev=" + mdev +
                ", domain='" + domain + '\'' +
                '}';
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getPingDesc() {
        return pingDesc;
    }

    public void setPingDesc(String pingDesc) {
        this.pingDesc = pingDesc;
    }

    public float getMin() {
        return min;
    }

    public void setMin(float min) {
        this.min = min;
    }

    public float getAvg() {
        return avg;
    }

    public void setAvg(float avg) {
        this.avg = avg;
    }

    public float getMax() {
        return max;
    }

    public void setMax(float max) {
        this.max = max;
    }

    public float getMdev() {
        return mdev;
    }

    public void setMdev(float mdev) {
        this.mdev = mdev;
    }


}
