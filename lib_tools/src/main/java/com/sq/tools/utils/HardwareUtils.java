package com.sq.tools.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.support.annotation.NonNull;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.WindowManager;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;

import static android.content.Context.TELEPHONY_SERVICE;

public abstract class HardwareUtils {

    /**
     * @return 设备硬件屏幕高度尺寸，单位：像素, 若需要 dp 尺寸，请参见 {@link #getScreenHeightDp(Context)}
     */
    public static int getScreenHeightPx(@NonNull Context context) {
        DisplayMetrics metrics = new DisplayMetrics();
        WindowManager manager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if(manager == null) return 0;
        manager.getDefaultDisplay().getMetrics(metrics);
        return metrics.heightPixels;
    }

    /**
     * @return 设备硬件屏幕宽度尺寸，单位：像素, 若需要 dp 尺寸，请参见 {@link #getScreenWidthDp(Context)}
     */
    public static int getScreenWidthPx(Context context) {
        DisplayMetrics metrics = new DisplayMetrics();
        WindowManager manager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if(manager == null) return 0;
        manager.getDefaultDisplay().getMetrics(metrics);
        return metrics.widthPixels;
    }

    /**
     * @return 设备硬件屏幕高度尺寸，单位：DP
     */
    public static int getScreenHeightDp(Context context) {
        float density = getDensity(context);
        return (int) (getScreenHeightPx(context) / density);
    }

    /**
     * @return 设备硬件屏幕宽度尺寸，单位：DP
     */
    public static int getScreenWidthDp(Context context) {
        float density = getDensity(context);
        return (int) (getScreenWidthPx(context) / density);
    }

    /**
     * @return 设备硬件屏幕密度，关于屏幕尺寸与像素关系的详细介绍，
     * 请参照: https://developer.android.com/guide/practices/screens_support
     */
    public static float getDensity(Context context) {
        return context.getResources().getDisplayMetrics().density;
    }

    /**
     * @return 手机品牌
     */
    @NonNull public static String getBrand() {
        return TextUtils.isEmpty(Build.BRAND) ? "" : Build.BRAND;
    }

    /**
     * @return 手机型号
     */
    @NonNull public static String getModel() {
        return TextUtils.isEmpty(Build.MODEL) ? "" : Build.MODEL;
    }

    /**
     * @return 当前设备 ram, 单位: KB
     */
    public static long getTotalRam(@NonNull Context context) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if(manager == null) return 0;
        ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
        manager.getMemoryInfo(memInfo);
        return memInfo.totalMem;
    }

    /**
     * @return 当前设备外部存储空间， 单位 KB
     */
    public static long getExternalTotalMemory() {
        if(!isSDCardEnable()) return 0;
        StatFs stat = new StatFs(Environment.getExternalStorageDirectory().getPath());
        return stat.getBlockSizeLong() * stat.getAvailableBlocksLong() / 1024;
    }

    /**
     * @return 正常: 当前设备 cpu 核心数 无法获取: 0
     */
    public static int getCpuCores() {
        try {
            return Runtime.getRuntime().availableProcessors();
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * @return 正常: 当前设备 CPU 型号。 无法获取: {@code ""}
     */
    @NonNull public static String getCpuModel() {
        FileReader fileReader = null;
        BufferedReader bufferedReader = null;

        try {
            fileReader = new FileReader("/proc/cpuinfo");
            bufferedReader = new BufferedReader(fileReader);
            String s;
            while (!TextUtils.isEmpty(s = bufferedReader.readLine())) {
                if (s.contains("Processor")) {
                    return s.split(":")[1].trim();
                }
            }
        } catch (IOException ignore) {}

        if(fileReader != null) try { fileReader.close();} catch (IOException ignored) {}
        if(bufferedReader != null) try { bufferedReader.close();} catch (IOException ignored) {}

        return "";
    }


    /**
     * @return 正常: CPU最大频率（单位KHZ） 无法获取: N/A
     */
    @NonNull public static String getMaxCpuFreq() {
        StringBuilder result = new StringBuilder();
        InputStream in = null;
        try {
            String[] args = {"/system/bin/cat", "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq"};
            ProcessBuilder cmd = new ProcessBuilder(args);
            Process process = cmd.start();
            in = process.getInputStream();
            byte[] re = new byte[24];
            while (in.read(re) != -1) {
                result.append(new String(re));
            }
            in.close();
        } catch (Exception ex) {
            result = new StringBuilder("N/A");
        }

        if(in != null) try { in.close();} catch (IOException ignored) {}
        return result.toString().trim();
    }

    /**
     * @return CPU 支持的架构
     */
    @NonNull public static String getCpuArch() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && Build.SUPPORTED_ABIS.length > 0) {
            return TextUtils.isEmpty(Build.SUPPORTED_ABIS[0]) ? "" : Build.SUPPORTED_ABIS[0];
        }
        return TextUtils.isEmpty(Build.CPU_ABI) ? "" : Build.CPU_ABI;
    }

    /**
     * 判断SD卡是否可用
     *
     * @return true : 可用<br>false : 不可用
     */
    public static boolean isSDCardEnable() {
        return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState());
    }


    /**
     * @return 设备当前 SIM 卡插入的数量, 此方法需要在已经获取 {@code READ_PHONE_STATE} 权限的情况下调用，否则返回 0
     */
    @SuppressLint("MissingPermission")
    public static int getCurrentSimNum(@NonNull Context context) {
        if (!PermissionUtils.hasAndroidPermission(context, Manifest.permission.READ_PHONE_STATE)) {
            return 0;
        }
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP_MR1) {
            return 0;
        }
        try {
            SubscriptionManager manager = (SubscriptionManager) context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE);
            if(manager != null) return manager.getActiveSubscriptionInfoCount();
        } catch (Exception ignored) {}
        return 0;
    }

    /**
     *
     * @param context Android Context
     * @return 设备当前 SIM 卡槽数量
     */
    public static int getSimSlotNum(@NonNull Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return 0;
        }
        TelephonyManager tm = (TelephonyManager) (context).getSystemService(TELEPHONY_SERVICE);
        return tm != null ? tm.getPhoneCount() : 0;
    }



    private HardwareUtils(){}
}
