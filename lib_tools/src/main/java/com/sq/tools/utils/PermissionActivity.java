package com.sq.tools.utils;

import android.annotation.TargetApi;
import android.app.Activity;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import com.sq.tools.Logger;

import static android.content.pm.PackageManager.PERMISSION_DENIED;
import static android.content.pm.PackageManager.PERMISSION_GRANTED;

/**
 * 权限申请 Activity, 调用权限申请工具 {@link PermissionUtils} 中申请权限方法时会被创建
 */
public class PermissionActivity extends Activity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = this.getIntent().getExtras();
        if(bundle == null) {
            // This should not happen but in case
            Logger.error("Internal error, request permission but no permission bundle passed");
            this.finish();
            return;
        }
        bundle.setClassLoader(PermissionActivity.class.getClassLoader());
        dealPermission(bundle);
    }

    @TargetApi(Build.VERSION_CODES.M)
    private void dealPermission(@NonNull Bundle bundle) {
        String permission = bundle.getString("permission_request");
        int requestCode = bundle.getInt("permission_code");
        if(TextUtils.isEmpty(permission) || requestCode == 0 || permission == null) {
            Logger.error("Internal error, request permission with illegal request code, or empty permission transferred");
            PermissionUtils.onRequestPermissionsResult(this, requestCode, new String[]{}, new int[]{PERMISSION_DENIED});
            this.finish();
            return;
        }
        if(PermissionUtils.hasAndroidPermission(this, permission)) {
            Logger.warning("Request a granted permission %s, grant by default", permission);
            PermissionUtils.onRequestPermissionsResult(this, requestCode, new String[]{}, new int[]{PERMISSION_GRANTED});
            this.finish();
            return;
        }
        this.requestPermissions(new String[]{permission}, requestCode);
    }

    @Override
    @SuppressWarnings("ConstantConditions")
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        // In API 30, onRequestPermissionsResult permissions & grantResults are marked as NonNull,
        // But in other API, we are not sure about this, so let's do a condition in case
        PermissionUtils.onRequestPermissionsResult(this, requestCode,
                permissions == null ? new String[]{} : permissions, grantResults == null ? new int[]{} : grantResults);
        this.finish();
    }
}
