package com.sq.tools.utils;

import android.os.SystemClock;
import android.support.annotation.NonNull;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <p>
 * Time manager utils. Check Time APi document for more details.
 * </p>
 * <p>
 * In this class, methods usually return timestamp in seconds level if no specific in method name
 * </p>
 */
@SuppressWarnings({"WeakerAccess", "unused", "RedundantSuppression"})
public abstract class TimeUtils {

    private static long baseClientTime;
    private static long baseServerTime;
    private static volatile boolean isFetched;

    private static Long getLongSystemElapsedTime() {
        return SystemClock.elapsedRealtime() / 1000;
    }

    private static String getSystemElapsedTime() {
        return Long.toString(getLongSystemElapsedTime());
    }

    public static String getServerTimeInSeconds() {
        return Long.toString(getLongServerTimeInSeconds());
    }

    public static long getLongServerTimeInSeconds() {
        if (isFetched) return baseServerTime + getLongSystemElapsedTime() - baseClientTime;
        else return Calendar.getInstance().getTimeInMillis() / 1000;
    }

    public static String getServerTimeInMills() {
        return Long.toString(getLongServerTimeInMills());
    }

    public static long getLongServerTimeInMills() {
        if (isFetched) return (baseServerTime + getLongSystemElapsedTime() - baseClientTime) * 1000;
        else return Calendar.getInstance().getTimeInMillis();
    }

    public static void fetchServerTime(@NonNull String url) {
        // TODO: 2023/8/16 无发现调用, 移除网络请求
    }

    //获得给定时间戳那天0点的时间戳
    public static long getStartDayTime(long timestamp) {
        return getSpecificHourTime(timestamp, 0);
    }

    //获得给定时间戳那天24点的时间戳
    public static long getEndDayTime(long timestamp) {
        return getStartDayTime(timestamp) + 86400;
    }

    public static long getSpecificHourTime(long timestamp, int hour) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestampInMills(timestamp));
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis() / 1000;
    }

    /**
     * @param timestamp Unix 时间戳
     * @return 小时时间， 24 小时制
     */
    public static int getHour(long timestamp) {
        return Integer.parseInt(format(timestamp, "HH"));
    }

    //获得给定时间戳那个月最后一天的时间戳
    public static long getMonthEndTime(long timestamp) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(timestampInMills(timestamp));
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.getActualMaximum(Calendar.DAY_OF_MONTH), 24, 0, 0);
        return cal.getTimeInMillis() / 1000;
    }

    public static String format(long timestamp) {
        return format(timestamp, "yyyy-MM-dd HH:mm:ss");
    }

    public static String format(long timestamp, String format) {
        timestamp = timestampInMills(timestamp);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format, Locale.getDefault());
        Date date = new Date(timestamp);
        return simpleDateFormat.format(date);
    }

    private static long timestampInMills(long timestamp) {
        int timeLength = String.valueOf(timestamp).length();
        if (timeLength < 13) {
            timestamp = timestamp * (long) Math.pow(10, 13 - timeLength);
        }
        return timestamp;
    }

}
