package com.sq.tools.utils;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Base64;
import com.sq.tools.Logger;
import com.sq.tools.constant.ToolsConsent;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.zip.DataFormatException;
import java.util.zip.GZIPOutputStream;
import java.util.zip.Inflater;

/**
 * 此类中存储了各种<b>加解码</b>算法以供使用
 * <br>以下算法中，实现均优先调用 Android API, 若未方法未指定编码格式，均默认使用 {@link StandardCharsets#UTF_8}
 * <br>若异常情况，则均返回 {@code ""} 或者新建默认对象
 */
@SuppressWarnings("WeakerAccess")
public abstract class DecodeUtils {

    @NonNull public static String MD5(@NonNull String s) {
       return MD5(s, StandardCharsets.UTF_8);
    }

    @NonNull public static String MD5(@NonNull String s, Charset charset) {
        MessageDigest m;
        try {
            m = MessageDigest.getInstance(ToolsConsent.DECODE_MD5);
        } catch (NoSuchAlgorithmException e) {
            Logger.error("MD5 calculate exception", e);
            return "";
        }
        m.update(s.getBytes(charset), 0, s.length());
        return String.format("%032x", new BigInteger(1, m.digest()));
    }

    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();
    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static byte[] hexToBytes(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }


    @NonNull public static String base64Encoding(@NonNull String s, Charset charset) {
        return new String(Base64.encode(s.getBytes(charset), Base64.DEFAULT | Base64.NO_WRAP), charset);
    }

    @NonNull public static byte[] base64Encoding(@NonNull byte[] bytes) {
        return Base64.encode(bytes, Base64.DEFAULT | Base64.NO_WRAP);
    }

    @NonNull public static String base64Decoding(@NonNull String s, Charset charset) {
        return new String(Base64.decode(s.getBytes(charset), Base64.DEFAULT | Base64.NO_WRAP), charset);
    }

    @NonNull public static byte[] base64Decoding(@NonNull byte[] bytes) {
        return Base64.decode(bytes, Base64.DEFAULT | Base64.NO_WRAP);
    }

    @NonNull public static String urlEncode(@NonNull String s, Charset charset) {
        try {
            return URLEncoder.encode(s, charset.name());
        } catch (UnsupportedEncodingException e) {
            Logger.warning("URL encode exception", e);
            return "";
        }
    }

    @NonNull public static String urlDecode(@NonNull String s, Charset charset) {
        try {
            return URLDecoder.decode(s, charset.name());
        } catch (UnsupportedEncodingException e) {
            Logger.warning("URL decode exception", e);
            return "";
        }
    }

    @NonNull public static byte[] gzip(@NonNull String data, Charset charset) {
        if(TextUtils.isEmpty(data)) return new byte[0];
        GZIPOutputStream gzip = null;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            gzip = new GZIPOutputStream(outputStream);
            gzip.write(data.getBytes(charset));
            gzip.flush();
        } catch (IOException e) {
            Logger.error("GZIP, compress data failed with exception, data: %s", data, e);
        } finally {
            if(gzip != null) {
                try {
                    gzip.close();
                } catch (IOException ignore) {}
            }
        }
        return outputStream.toByteArray();
    }

    @NonNull public static byte[] unzip(@NonNull byte[] data) {
        ByteArrayOutputStream outputStream = null;
        try {
            Inflater inflater = new Inflater();
            inflater.setInput(data, 0, data.length);
            outputStream = new ByteArrayOutputStream(data.length);
            byte[] buffer = new byte[data.length];
            while (!inflater.finished()) {
                int count = inflater.inflate(buffer);
                outputStream.write(buffer, 0, count);
            }
            return outputStream.toByteArray();
        } catch (DataFormatException e) {
            Logger.warning("Uncompress data Exception", e);
            return new byte[0];
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    Logger.warning("Exception happen when trying to close %s, this may cause a memory leak", "ByteArrayOutputStream", e);
                }
            }
        }
    }

    /**
     * Merge two Json Object to one, the content for both Json object will be merged.
     * If passed json objects have same field, this method will return {@code null}
     *
     * @param json1 the json to be merged
     * @param json2 another json to be merged
     * @return return the merged Json Object or {@code null} if those two json have same data or
     * unexpected Exception happen
     */
    @Nullable public static JSONObject mergeTwoJson(JSONObject json1, JSONObject json2) {
        if (json1 == null) {
            return json2;
        }
        if (json2 == null) {
            return json1;
        }
        String json1Str = json1.toString();
        String merge = json1Str.substring(0, json1Str.length() - 1) + "," + json2.toString().substring(1);
        try {
            return new JSONObject(merge);
        } catch (JSONException e) {
            Logger.warning("Merge two Json Object Exception", e);
            return null;
        }
    }

}
