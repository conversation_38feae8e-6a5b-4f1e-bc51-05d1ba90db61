package com.sq.tools.proxy;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import java.util.UUID;

/**
 * Key-Value 键值对的对象
 * <br>其中 {@link #name} 若为空或者 {@code null}, 会自动生成一个 8 位随机数串作为其值
 */
public class Pair {
    public String name;
    public String value;

    public Pair(String name, String value) {
        this.name = TextUtils.isEmpty(name) ? UUID.randomUUID().toString().substring(0, 8) : name;
        this.value = TextUtils.isEmpty(value) ? "" : value;
    }

    @Override
    @NonNull
    public String toString() {
        return "Pair{" + "name='" + name + '\'' + ", value='" + value + '\'' + '}';
    }
}
