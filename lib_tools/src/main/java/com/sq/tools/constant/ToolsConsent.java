package com.sq.tools.constant;

import android.support.annotation.RestrictTo;

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
public abstract class ToolsConsent {


    public static final int READ_TIMEOUT = 15 * 1000;
    public static final int CONN_TIMEOUT = 15 * 1000;


    public static final String HTTP_POST = "POST";
    public static final String HTTP_GET = "GET";
    public static final String HTTP_PUT = "PUT";
    public static final String HTTP_DELETE = "DELETE";

    public static final String DECODE_MD5 = "MD5";

}
