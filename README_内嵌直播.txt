直播相关接口
1、开启直播列表 (初始化成功设置)

    /**
     *
     * @param data 目前可传空，拓展用
     * @param listener
     */
	SQwanCore.getInstance().joinLiveshowRoom(null,new SQResultListener() {
			@Override
			public void onSuccess(Bundle bundle) {
				//开播成功
				ToastUtil.showToast(MainActivity.this,"joinRoom onSuccess " + bundle.toString());
			}

			@Override
			public void onFailture(int code, String msg) {
				ToastUtil.showToast(MainActivity.this,"joinRoom onFailture code " + code + " msg " + msg);
			}
		});


2、监听直播关闭 (初始化成功设置)
	SQwanCore.getInstance().setLiveshowDestroyCallback(new SQResultListener() {
			@Override
			public void onSuccess(Bundle bundle) {
				//直播关闭
				Log.i(TAG,"setLiveshowDestroyCallback onSuccess bundle " + bundle);
			}

			@Override
			public void onFailture(int code, String msg) {

			}
		});

3、直播过程中监听直播声音变化 (初始化成功设置)
	/**
     * 监听直播关闭
     * @param listener onSuccess
     */
	SQwanCore.getInstance().setLiveshowVoiceChangeCallback(new SQResultListener() {
			@Override
			public void onSuccess(Bundle bundle) {
				//是否有播放声音
				boolean isResume = bundle.getBoolean("isResume");
				Log.i(TAG,"setLiveshowVoiceChangeCallback bundle " + bundle);
			}

			@Override
			public void onFailture(int code, String msg) {

			}
		});
4、扩展接口
//关闭直播间声音
public void closeLiveshowVoice() {
		Map<String,String> map = new HashMap<>();
		map.put("switchVoice","false");
		SQwanCore.getInstance().performLiveshowFeature(map, new SQResultListener() {
			@Override
			public void onSuccess(Bundle bundle) {
				LogUtil.i(TAG,"closeLiveshowVoice");
			}

			@Override
			public void onFailture(int code, String msg) {

			}
		});
	}
//开启直播间声音
	public void openLiveshowVoice() {
		Map<String,String> map = new HashMap<>();
		map.put("switchVoice","true");
		SQwanCore.getInstance().performLiveshowFeature(map, new SQResultListener() {
			@Override
			public void onSuccess(Bundle bundle) {
				LogUtil.i(TAG,"openLiveshowVoice");
			}

			@Override
			public void onFailture(int code, String msg) {

			}
		});
	}