apply plugin: 'com.android.library'
apply plugin: 'com.kezong.fat-aar'
apply from: '../common.gradle'

//s层是否打aar模式，需要将依赖库使用fat-aar打到一起
def isSModuleAar = false

android {

    compileSdkVersion rootProject.ext.androidCompileSdkVersion
    buildToolsVersion rootProject.ext.androidBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.androidMinSdkVersion
        targetSdkVersion rootProject.ext.androidTargetSdkVersion

        //android 6.0(api 23) SDK以及以上，不再提供org.apache.http.*(只保留几个类).
        useLibrary 'org.apache.http.legacy'

        //sdk版本设定
        versionCode 22
        versionName get_libraryVersion()

    }

    // 配置结构
    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java']
            resources.srcDirs = ['src/main/resources']
            aidl.srcDirs = ['src/main/aidl']
            renderscript.srcDirs = ['src/maom']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDir 'src/main/jniLibs'
        }
    }

    //这个是解决lint报错的代码
    lintOptions {
        quiet true
        abortOnError false
        ignoreWarnings true
        checkReleaseBuilds false//方法过时警告的开关
        disable 'InvalidPackage' //Some libraries have issues with this.
        disable 'OldTargetApi' //Lint gives this warning but SDK 20 would be Android L Beta.
        disable 'IconDensities' //For testing purpose. This is safe to remove.
        disable 'IconMissingDensityFolder' //For testing purpose. This is safe to remove.
    }

    compileOptions {
        encoding "UTF-8"
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    /**
     * 混淆设置
     */
    buildTypes {
        release {
            //执行proguard混淆
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled false
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_s.pro'
        }
        debug {
            //不执行proguard
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled false
            //前一部分代表系统默认的android程序的混淆文件，该文件已经包含了基本的混淆声明
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules_37sdk_s.pro'
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    if(!isSModuleAar) {
        api project(':module_business:account')
        api project(':module_business:order')
        api project(':module_business:share')
        api project(':module_business:sdk_plugin')
        api project(':37sdkcommon')
        api project(':37SDKLibrarys')
        api project(':lib_tools')
    } else {
        embed project(path: ':module_business:order', configuration: 'default')
        embed project(path: ':module_business:account', configuration: 'default')
        embed project(path: ':module_business:share', configuration: 'default')
        embed project(path: ':37sdkcommon', configuration: 'default')
        embed project(path: ':37SDKLibrarys', configuration: 'default')
        embed project(path: ':notchtools', configuration: 'default')
        embed project(path: ':module_business:sdk_plugin', configuration: 'default')
        embed project(path: ':module_ui:sq', configuration: 'default')
        embed project(path: ':module_ui:base_new', configuration: 'default')
        embed project(path: ':module_plugin:plugin_standard', configuration: 'default')
        embed(rootProject.ext.sqsdk.volly) {
            exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        }
        embed project(path: ':gson', configuration: 'default')
        embed rootProject.ext.sqsdk.bugless
        embed 'com.37sy.android:tool:2.0.5'
        embed 'com.37sy.android:sqinject:1.0.0'
        embed 'com.37sy.android:sqeventbus:1.0.0'
        embed 'com.37sy.android:sqeventbus-annotation:1.0.0'
        embed 'com.37sy.android:social_sdk:2.0.8'
        // 第三方登录和分享
        embed project(path: ':module_third:social_sdk', configuration: 'default')
        embed project(path: ':module_third:ali_face:ali_face_sdk', configuration: 'default')
    }

}

task deleteOldJar(type: Delete) {
    delete "build/libs/37coreSDK_v${get_libraryVersion()}.jar"
}

// task to export contents as jar
task exportJar(type: Copy) {
    from('build/intermediates/packaged-classes/release/')
    into('build/libs/')
    include('classes.jar')
    rename('classes.jar', "37coreSDK_v${get_libraryVersion()}.jar")
}
exportJar.dependsOn(deleteOldJar, assemble)
