# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\ADT\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

#37sy_sdk_core_s混淆配置

-ignorewarnings

# 代码混淆压缩比，在0~7之间，默认为5,一般不下需要修改
-optimizationpasses 5

# 混淆时不使用大小写混合，混淆后的类名为小写
# windows下的同学还是加入这个选项吧(windows大小写不敏感)
-dontusemixedcaseclassnames

# 指定不去忽略非公共的库的类
# 默认跳过，有些情况下编写的代码与类库中的类在同一个包下，并且持有包中内容的引用，此时就需要加入此条声明
-dontskipnonpubliclibraryclasses

# 指定不去忽略非公共的库的类的成员
-dontskipnonpubliclibraryclassmembers

# 不做预检验，preverify是proguard的四个步骤之一
# Android不需要preverify，去掉这一步可以加快混淆速度
-dontpreverify

# 有了verbose这句话，混淆后就会生成映射文件
# 包含有类名->混淆后类名的映射关系
# 然后使用printmapping指定映射文件的名称
-verbose
-printmapping priguardMapping.txt

# 指定混淆时采用的算法，后面的参数是一个过滤器
# 这个过滤器是谷歌推荐的算法，一般不改变
-optimizations !code/simplification/artithmetic,!field/*,!class/merging/*

# 保护代码中的Annotation不被混淆
# 这在JSON实体映射时非常重要，比如fastJson
-keepattributes *Annotation*

# 避免混淆泛型
# 这在JSON实体映射时非常重要，比如fastJson
-keepattributes Signature

# 抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable

#混淆时不抛出警告
-dontwarn sun.misc.Unsafe,com.google.common.collect.MinMaxPriorityQueue,android.support.v4.**

# 保留所有的本地native方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

#不混淆gson
-keep class com.google.gson.** {
    <fields>;
    <methods>;
}

-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# for 37shouyou start--------
-keep public class com.sy37sdk.core.SQwan {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.IConfig {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.SQResultListener {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.AntiManager {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.account.auth.AuthManager {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.floatView.SyFloatItemView {
    <fields>;
    <methods>;
}


-keep public interface  com.sy37sdk.core.SQScreenshotListener {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.RequestManager {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.RequestCallBack {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.account.download.DownloaderUtil {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.account.AccountTools {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.account.UserInfo {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.utils.DeviceTools {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.utils.Util {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.utils.DisplayUtil {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.account.auth.AuthConfigCache {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.utils.AppUtils {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.utils.RomUtils {
    <fields>;
    <methods>;
}

-keep class com.sqwan.common.util.PermissionHelper{*;}

-keep public interface com.sqwan.common.util.PermissionHelper$PermissionCallback{*;}

-keep public interface  com.sy37sdk.core.IError {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.floatView.FloatWebView$* {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.views.NetErrorView.** {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.views.PayWebDialog$* {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.views.SY37web$* {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.views.webview.** {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.widget.** {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.otherlogin.** {
    <fields>;
    <methods>;
}

-keep public class com.sy37sdk.core.SQwan {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.bean.** {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.http.** {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.receiver.** {
    <fields>;
    <methods>;
}

# for 37shouyou end
# for android-support-v4 start
-keep class android.support.** {
    <fields>;
    <methods>;
}

-keep class android.support.v4.** {
    <fields>;
    <methods>;
}

-keep public class android.support.v4.view.ViewPager {
    <fields>;
    <methods>;
}

-keep interface  android.support.v4.app.** {
    <fields>;
    <methods>;
}

-keep class com.sy37sdk.data.** { *; }

-keep class com.sy37sdk.views.SqLoginFailWebViewDialog {
    <fields>;
    <methods>;
}

-keep public class * extends android.support.v4.**

-keep public class * extends android.app.Fragment

# for android-support-v4 end
# for ali pay start---------------
-keep class com.alipay.** {
    <fields>;
    <methods>;
}

-keep class com.ta.utdid2.** {
    <fields>;
    <methods>;
}

-keep class com.json.alipay.** {
    <fields>;
    <methods>;
}

-keep class com.ut.*

-keep class com.sy37sdk.alipay.** {
    <fields>;
    <methods>;
}

-keep class android.util.** {
    <fields>;
    <methods>;
}

# for ali pay end-----------------
-keep public class * extends android.app.Activity

-keep public class * extends android.app.Application

-keep public class * extends android.app.Service

-keep public class * extends android.content.BroadcastReceiver

-keep public class * extends android.content.ContentProvider

-keep public class * extends android.app.backup.BackupAgentHelper

-keep public class * extends android.preference.Preference

-keep public class com.android.vending.licensing.ILicensingService

-keep class * extends android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

-keepclasseswithmembers,allowshrinking class * {
    public <init>(android.content.Context,android.util.AttributeSet);
}

-keepclasseswithmembers,allowshrinking class * {
    public <init>(android.content.Context,android.util.AttributeSet,int);
}

# Also keep - Enumerations. Keep the special static methods that are required in
# enumeration classes.
-keepclassmembers enum  * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep names - Native method names. Keep all native class/method names.
-keepclasseswithmembers,allowshrinking class * {
    native <methods>;
}

-keep class com.sdk.sq.net.** { *; }
-keep interface com.sdk.sq.net.** { *; }
-keep class com.sqnetwork.** { *; }
-keep interface com.sqnetwork.** { *; }
-keep class com.sy37sdk.utils.** { *; }
-keep class com.sy37sdk.account.auth.** { *; }
-keep class com.sy37sdk.plugin.** { *; }