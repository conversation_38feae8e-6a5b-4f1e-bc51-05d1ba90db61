<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.sy37sdk">

    <application>

        <activity
            android:name="com.sqwan.common.web.SY37web"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="stateVisible|adjustResize">
<!--            <intent-filter>-->
<!--                <action android:name="com.sq.mytel.protocolWeb" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
        </activity>

        <activity
            android:name="com.sqwan.common.web.SY37BehindWebPage"
            android:configChanges="keyboardHidden|screenSize"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.sqwan.common.web.SY37PortraitWebPage"
            android:configChanges="keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name="com.sqwan.common.web.SY37LandscapeWebPage"
            android:configChanges="keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustResize" />

        <!-- 悬浮窗的切换账号和主动切换账号，是否跳过登录界面  -->
        <!-- yes：不弹出登录框，直接返回成功，没有用户信息；no：弹出登录框，成功后返回用户信息 -->
        <meta-data
            android:name="SQwanSkipSwitchLogin"
            android:value="no" />

    </application>

</manifest>