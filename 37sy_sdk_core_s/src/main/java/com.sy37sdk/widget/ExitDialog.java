package com.sy37sdk.widget;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.Button;
import android.widget.ImageView;

import com.sqwan.common.util.LogUtil;
import com.sy37sdk.utils.AppUtils;
import com.sqwan.common.util.AsyncImageLoader;
import com.sqwan.common.util.AsyncImageLoader.ImageCallback;
import com.sy37sdk.utils.Util;

public class ExitDialog extends Dialog {

    private String mPackageName;
    private String mUrl;
    private String mImgPath;
    private Drawable mImg;
    private Context context;
    private ExitCallBack mCallback;
    private AsyncImageLoader imageLoader;


    public ExitDialog(Context context,ExitCallBack callback) {
        this(context, Util.getIdByName("Dialog", "style", context.getPackageName(), context),callback);
        this.context = context;
        this.mCallback = callback;
    }

    ExitDialog(Context context, int theme,ExitCallBack callback) {
        super(context, theme);
        imageLoader = new AsyncImageLoader(context);
        this.mCallback = callback;
        this.context = context;
    }

    ExitDialog(Context context, boolean cancelable,
               OnCancelListener cancelListener,ExitCallBack callback) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(Util.getIdByName("sy37_dialog_exit", "layout", context.getPackageName(),context), null);

        ImageView exitResImg = (ImageView) view.findViewById(Util.getIdByName("sy37_img_exit_res", "id", context.getPackageName(),context));
        Button exitBtn = (Button) view.findViewById(Util.getIdByName("sy37_btn_exit_game", "id", context.getPackageName(),context));
        Button backGameBtn = (Button) view.findViewById(Util.getIdByName("sy37_btn_back_game", "id", context.getPackageName(), context));

        if (mImg != null) {

            exitResImg.setImageDrawable(mImg);
        }else{

            imageLoader.loadDrawable(mImgPath, exitResImg, new ImageCallback() {

                @Override
                public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {

                    imageView.setImageBitmap(imageDrawable);
                }
            });

        }

        exitResImg.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                //默认做显示用，点击不做处理
                if (mPackageName != null
                        && !"".equals(mPackageName)
                        && Util.checkAppInstalled(context, mPackageName)) {  //打开应用

                    AppUtils.startAppFromPackage(context, mPackageName);
                    ExitDialog.this.dismiss();

                }else if(mUrl != null && !"".equals(mUrl)){   //打开外部链接

                    AppUtils.toUri(context, mUrl);
                    ExitDialog.this.dismiss();
                }


            }
        });

        exitBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                ExitDialog.this.dismiss();
                mCallback.exit();
            }
        });

        backGameBtn.setOnClickListener(new View.OnClickListener(){
            @Override
            public void onClick(View v) {
                LogUtil.i("取消退出，返回游戏");
                dismiss();
            }
        });

        setContentView(view, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));
    }


    /**
     * 设置退弹框的图片资源点击url
     * @param url
     */
    public void setUrl(String url) {
        mUrl = url;
    }


    /**
     * 设置包名
     * @param pname
     */
    public void setPkn(String pname){

        this.mPackageName = pname;
    }

    /**
     * 设置图片资源
     * @param img
     */
    public void setImageDrawable(Drawable img){

        this.mImg = img;
    }


    /**
     * 设置图片资源路径
     * @param img
     */
    public void setImagePath(String exitImgPath) {

        this.mImgPath = exitImgPath;
    }


    /**
     *
     *  退出框callback
     */
    public interface ExitCallBack {

        public void exit();

    }


}
