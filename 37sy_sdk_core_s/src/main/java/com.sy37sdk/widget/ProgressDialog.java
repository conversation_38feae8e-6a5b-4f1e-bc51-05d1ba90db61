package com.sy37sdk.widget;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.TextView;

import com.sqwan.common.dialog.FullScreenDialog;
import com.sy37sdk.utils.Util;


/**
 * 使用 com.sqwan.common.dialog.LoadingDialog
 */
@Deprecated
public class ProgressDialog extends FullScreenDialog {
    private CharSequence mMessage;
    private Context context;
    
    TextView textView;
    
    public ProgressDialog(Context context) {
        this(context, Util.getIdByName("progressDialog", "style", context.getPackageName(), context));
        this.context = context;
    }

    ProgressDialog(Context context, int theme) {
        super(context, theme);
        this.context = context;
    }

    ProgressDialog(Context context, boolean cancelable,
            OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view;
        if (Util.getIsSpecialSDK(context)) {
			//����dialog
        	view = inflater.inflate(Util.getIdByName("sy37_progress_dialog_simple", "layout", context.getPackageName(),context), null);
        	textView = (TextView) view.findViewById(Util.getIdByName("msg", "id", context.getPackageName(),context));
		} else {
			//37ԭ��Dialog
			 view = inflater.inflate(Util.getIdByName("sy37_progress_dialog", "layout", context.getPackageName(),context), null);
			textView = (TextView) view.findViewById(Util.getIdByName("msg", "id", context.getPackageName(),context));
		}
        
        setContentView(view, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }
    
    public static ProgressDialog show(Context context, CharSequence msg) {
        return show(context, msg, false);
    }
    
    public static ProgressDialog show(Context context, int id) {
        return show(context, context.getResources().getString(id), false);
    }
    
    public static ProgressDialog show(Context context, CharSequence msg, boolean cancelable) {
        ProgressDialog dialog = new ProgressDialog(context);
        dialog.setMessage(msg);
        dialog.setCancelable(cancelable);
        dialog.show();
        return dialog;
    }

    public void setMessage(CharSequence msg) {
    	
    	mMessage = msg;
        if (!TextUtils.isEmpty(mMessage) && textView != null) {
            textView.setText(mMessage);
        }
        
    }
}
