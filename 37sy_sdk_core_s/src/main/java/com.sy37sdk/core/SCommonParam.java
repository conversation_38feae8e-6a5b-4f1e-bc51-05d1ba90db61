package com.sy37sdk.core;

import android.content.Context;
import android.support.annotation.Nullable;
import com.sdk.sq.net.RequestBuilder.ParamsTransformer;
import com.sqwan.common.constants.SqConstants;
import com.sqwan.common.data.cache.DevManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.RootLogic;
import com.sqwan.common.dev.SimulatorLogic;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.SQReportCore;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/8/16
 */
class SCommonParam implements ParamsTransformer {

    @Nullable
    @Override
    public Map<String, Object> transform(@Nullable Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        Context context = SQContextWrapper.getApplicationContext();
        SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
        params.put("pid", config.getPartner());
        params.put("gid", config.getGameid());
        params.put("refer", config.getRefer());
        params.put("dev", DevLogic.getInstance(context).getValue());
        params.put("sversion", VersionUtil.sdkVersion);
        params.put("version", AppUtils.getVersionName(context));
        params.put("gwversion", VersionUtil.gwversion);
        String mDev = SQReportCore.getInstance().getMDev();
        params.put("mdev", mDev == null ? "" : mDev);
        String androidId = DevManager.getAndroidId(DevLogic.getInstance(context).isAuthCheck());
        params.put("android_id", androidId == null ? "" : androidId);
        params.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("scut", getCodeOfLogin(context));
        // 宿主版本号
        params.put("host_sdk_version", VersionUtil.getOriginalVersion());
        params.put(SqConstants.IS_ROOT, RootLogic.getInstance(context).getValue());
        params.put(SqConstants.IS_SIMULATOR, SimulatorLogic.getInstance(context).getValue());
        return params;
    }

    protected String getCodeOfLogin(Context context) {
        return ConfigManager.getInstance(context).getLoginCode() + "";
    }
}
