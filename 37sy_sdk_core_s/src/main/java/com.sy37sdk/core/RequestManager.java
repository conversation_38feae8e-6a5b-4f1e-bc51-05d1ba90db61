package com.sy37sdk.core;

import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import com.sq.tool.network.SignInterceptor.SignVersion;
import com.sq.tool.network.SignV2Interceptor.SignExt;
import com.sq.tool.network.SqHttpCallback;
import com.sq.tool.network.SqRequest;
import com.sq.tools.Logger;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.FakeActive;
import com.sqwan.common.data.cache.DevManager;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.request.CommonParamsV2;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.AppUtils;
import com.sqwan.msdk.SQReportCore;
import com.sy37sdk.utils.Util;
import com.sy37sdk.widget.ProgressDialog;
import java.util.HashMap;
import org.json.JSONObject;


/**
 * 网络请求细节封装类
 *
 * <AUTHOR>
 * @since 2014年09月28日15:43:19
 */
public class RequestManager {

    private Context mContext;
    private ProgressDialog waitDialog;

    public RequestManager(Context context) {

        this.mContext = context;
    }


    /**
     * 初始化
     */
    public void initRequst(final SqHttpCallback<JSONObject> callback) {
        String mDev = SQReportCore.getInstance().getMDev();
        boolean isLand = mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
        //url 新增时间戳，避免缓存
        String url = INewUrl.INIT + "?t=" + System.currentTimeMillis() + "";
        final SqRequest request = SqRequest.of(url)
            .signV2(null)
            // 3.5.5 新增横竖屏参数 type 1：横版、2：竖屏
            .addParam("type", isLand ? "1" : "2")
            .addParam("mdev", mDev == null ? "" : mDev)
            .addParam("display_name", AppUtils.getAppName(mContext))
            .addParam("android_id", DevManager.getAndroidId(DevLogic.getInstance(mContext).isAuthCheck()))
            .addParamsTransformer(new CommonParamsV2());
        request.post(new SqHttpCallback<JSONObject>() {
            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                if (callback != null) {
                    callback.onResponseStateError(httpStatus, state, msg, data);
                }
            }

            @Override
            public void onSuccess(JSONObject jsonObject) {
                FakeActive.INSTANCE.saveContent(mContext, getResponseStr(), FakeActive.INSTANCE.S_FAKE_ACTIVE_KEY);
                if (callback != null) {
                    callback.onSuccess(jsonObject);
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                HashMap<String, String> extraMap = new HashMap<>();
                extraMap.put(SqTrackKey.fail_code, code + "");
                extraMap.put(SqTrackKey.reason_fail, errorMsg);
                SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_fail, extraMap);

                if (callback == null) {
                    return;
                }
                String content = FakeActive.INSTANCE.requireContent(mContext,
                    FakeActive.INSTANCE.S_FAKE_ACTIVE_KEY, url, request.getRequestParams(), SignVersion.V2);
                JSONObject dataJson = null;
                try {
                    JSONObject contentJson = new JSONObject(content);
                    dataJson = contentJson.getJSONObject("data");
                } catch (Exception e) {
                    /* no-op */
                }
                if (dataJson != null) {
                    Logger.info("本地s层激活有缓存, 本地激活成功");
                    callback.onSuccess(dataJson);
                } else {
                    try {
                        Logger.warning("本地s层激活未读到缓存, 返回默认结果");
                        callback.onSuccess(buildFakeData());
                    } catch (Exception e) {
                        Logger.error("默认结果构建失败, s层激活失败");
                        callback.onFailure(code, "网络异常，请稍候再试", error);
                    }
                }
            }

            private JSONObject buildFakeData() throws Exception {
                JSONObject data = new JSONObject();
                data.put("c", new JSONObject());
                return data;
            }
        });
    }


    /**
     * 登录请求
     *
     * @param loginName 账号
     * @param loginPwd 密码
     * @param callback 登录网络请求回调
     * @param isShowDialog 是否显示网络加载框
     */
    public void loginRequest(String loginName, String loginPwd, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> logparams = new HashMap<>();
        logparams.put("uname", loginName);
        logparams.put("upwd", loginPwd);
        SignExt signExt = new SignExt(loginName).append(loginPwd);
        post(INewUrl.LOGIN, logparams, SignVersion.V2, signExt, callback, isShowDialog);
    }


    /**
     * 注册请求
     *
     * @param regName 注册的账号
     * @param regPw 注册的密码
     * @param callback 注册网络请求回调
     * @param isShowDialog 是否显示网络加载框
     */
    public void registRequest(String regName, String regPw, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> regparams = new HashMap<>();
        regparams.put("uname", regName);
        regparams.put("upwd", regPw);
        SignExt signExt = new SignExt(regName).append(regPw);
        post(INewUrl.REG, regparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 一键注册接口，
     *
     * @param callback 一键注册网络请求回调
     * @param isShowDialog 是否显示网络加载框
     * @description 当前客户端没有账号记录时，服务器端自动生成账号密码
     */
    public void fastRegistRequest(final RequestCallBack callback, boolean isShowDialog) {
        post(INewUrl.REG_FAST, null, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 修改密码接口
     *
     * @param oldPw 旧密码
     * @param newPw 新密码
     */
    public void updatePassWordRequest(String oldPw, String newPw, final RequestCallBack callback,
        boolean isShowDialog) {
        HashMap<String, String> updatePWparams = new HashMap<>();
        updatePWparams.put("token", Util.getToken(mContext));
        updatePWparams.put("oupwd", oldPw);
        updatePWparams.put("nupwd", newPw);
        post(INewUrl.CPWD, updatePWparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 手机找回密码
     *
     * @param uname 账号
     * @param phone 手机
     */
    public void findAccountByPhoneRequest(String uname, String phone, final RequestCallBack callback,
        boolean isShowDialog) {
        HashMap<String, String> findpwByPhoneparams = new HashMap<>();
        findpwByPhoneparams.put("uname", uname);
        findpwByPhoneparams.put("phone", phone);
        SignExt signExt = new SignExt(uname).append(phone);
        post(INewUrl.PFP, findpwByPhoneparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 邮箱找回密码
     *
     * @param uname 账号
     * @param email 邮箱
     */
    public void findAccountByEmailRequest(String uname, String email, final RequestCallBack callback,
        boolean isShowDialog) {
        HashMap<String, String> findpwByEmailparams = new HashMap<>();
        findpwByEmailparams.put("uname", uname);
        findpwByEmailparams.put("email", email);
        SignExt signExt = new SignExt(uname).append(email);
        post(INewUrl.MFP, findpwByEmailparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 获取短信验证码（用于绑定手机）
     */
    public void getVerfyCodeRequest(String phone, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getVcodeparams = new HashMap<>();
        getVcodeparams.put("token", Util.getToken(mContext));
        getVcodeparams.put("phone", phone);
        SignExt signExt = new SignExt(phone);
        post(INewUrl.SPV, getVcodeparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 账户手机绑定
     *
     * @param phone 手机号
     * @param vcode 手机验证码
     */
    public void bindByPhoneRequest(String phone, String vcode, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> bindByPhoneparams = new HashMap<>();
        bindByPhoneparams.put("token", Util.getToken(mContext));
        bindByPhoneparams.put("phone", phone);
        bindByPhoneparams.put("vcode", vcode);
        SignExt signExt = new SignExt(phone).append(vcode);
        post(INewUrl.BP, bindByPhoneparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 邮箱绑定账户
     *
     * @param email 邮箱
     */
    public void bindByEmailRequest(String email, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> bindByEmailparams = new HashMap<>();
        bindByEmailparams.put("token", Util.getToken(mContext));
        bindByEmailparams.put("email", email);
        SignExt signExt = new SignExt(email);
        post(INewUrl.BM, bindByEmailparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 修改用户信息
     *
     * @param sex 性别
     * @param nick 昵称
     * @param birth 生日
     * @param phone 手机
     */
    public void modifyUserinfoRequest(String sex, String nick, String birth, String phone,
        final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> mUserinfoparams = new HashMap<>();
        mUserinfoparams.put("token", Util.getToken(mContext));
        mUserinfoparams.put("sex", sex);
        mUserinfoparams.put("nick", nick);
        mUserinfoparams.put("birth", birth);
        mUserinfoparams.put("phone", phone);
        post(INewUrl.SPRO, mUserinfoparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取钱包信息接口
     */
    public void getMyWalletInfoRequest(final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getWalletInfoparams = new HashMap<>();
        getWalletInfoparams.put("token", Util.getToken(mContext));
        post(INewUrl.IWT, getWalletInfoparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取我的礼包
     */
    public void getMyGiftsRequest(int pno, int psize, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getMygiftInfoparams = new HashMap<>();
        getMygiftInfoparams.put("token", Util.getToken(mContext));
        getMygiftInfoparams.put("pno", "" + pno);
        getMygiftInfoparams.put("psize", "" + psize);
        post(INewUrl.ICARD, getMygiftInfoparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取开服列表
     */
    public void getOpenServersRequest(int pno, int psize, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getWalletInfoparams = new HashMap<>();
        getWalletInfoparams.put("token", Util.getToken(mContext));
        getWalletInfoparams.put("pno", "" + pno);
        getWalletInfoparams.put("psize", "" + psize);
        post(INewUrl.OSL, getWalletInfoparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取官网信息（包括：攻略、新闻、礼包、其他信息）
     */
    public void getServiceInfoRequest(final RequestCallBack callback, boolean isShowDialog) {
        post(INewUrl.GWI, null, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 领取礼包接口
     */
    public void getGiftCardRequest(String rid, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getGiftCardparams = new HashMap<>();
        getGiftCardparams.put("token", Util.getToken(mContext));
        getGiftCardparams.put("rid", rid);
        SignExt signExt = new SignExt(rid);
        post(INewUrl.GCARD, getGiftCardparams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 获取礼包列表
     */
    public void getListOfGiftsRequest(int pno, int psize, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getGiftsListparams = new HashMap<>();
        getGiftsListparams.put("pno", "" + pno);
        getGiftsListparams.put("psize", "" + psize);
        post(INewUrl.CARD, getGiftsListparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取文章列表（新闻、攻略、其他）
     *
     * @param key 标识（新闻[xw]、攻略[gl]、其他[qt]）
     * @param pno 页码
     * @param psize 记录数
     */
    public void getListOfArtsRequest(String key, int pno, int psize, final RequestCallBack callback,
        boolean isShowDialog) {
        HashMap<String, String> getArtsListparams = new HashMap<>();
        getArtsListparams.put("key", "" + key);
        getArtsListparams.put("pno", "" + pno);
        getArtsListparams.put("psize", "" + psize);
        post(INewUrl.ART, getArtsListparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 推送请求
     */
    public void pushRequest(final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getPushInfoparams = new HashMap<>();
        getPushInfoparams.put("token", Util.getToken(mContext));
        post(INewUrl.PUSH, getPushInfoparams, SignVersion.V2, null, callback, isShowDialog);
    }

    /**
     * 获取手机注册的时候的验证码
     *
     * @param phoneNum 手机号码
     */
    public void getVerifyCodeRequest(String phoneNum, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> getVerifyCodeParams = new HashMap<>();
        getVerifyCodeParams.put("uname", phoneNum);
        SignExt signExt = new SignExt(phoneNum);
        post(INewUrl.MSCODE, getVerifyCodeParams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    /**
     * 手机号码注册接口
     *
     * @param phoneNum 手机号
     * @param verifyCode 验证码
     */
    public void phoneNumRegRequest(String phoneNum, String verifyCode, final RequestCallBack reqestCallback,
        boolean isShowDialog) {
        HashMap<String, String> phoneNumRegParams = new HashMap<>();
        phoneNumRegParams.put("uname", phoneNum);
        phoneNumRegParams.put("scode", verifyCode);
        SignExt signExt = new SignExt(phoneNum).append(verifyCode);
        post(INewUrl.MREG, phoneNumRegParams, SignVersion.V2, signExt, reqestCallback, isShowDialog);
    }

    /**
     * 短信注册结果查询接口
     *
     * @param vsign 短信注册验证串
     */
    public void queryMsgRegResultRequest(String vsign, final RequestCallBack callback, boolean isShowDialog) {
        HashMap<String, String> phoneNumRegResultQueryParams = new HashMap<>();
        phoneNumRegResultQueryParams.put("vsign", vsign);
        SignExt signExt = new SignExt(vsign);
        post(INewUrl.MREG_RES, phoneNumRegResultQueryParams, SignVersion.V2, signExt, callback, isShowDialog);
    }

    private void post(final String requestUrl,
        HashMap<String, String> params,
        @Nullable SignVersion signVersion,
        @Nullable SignExt signExt,
        final RequestCallBack callBack,
        boolean isShowDialog) {
        if (isShowDialog) {
            if (waitDialog == null) {
                waitDialog = new ProgressDialog(mContext);
                waitDialog.setCancelable(false);
            }
            if (waitDialog != null && !waitDialog.isShowing()) {
                waitDialog.show();
            }
        }

        SqRequest.of(requestUrl)
            .sign(signVersion, signExt)
            .formParams(params)
            .addParamsTransformer(new SCommonParam())
            .post(new SqHttpCallback<String>() {
                @Override
                public void onSuccess(String data) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestSuccess(getResponseStr());
                    }
                }

                @Override
                public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                    @Nullable String data) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestSuccess(getResponseStr());
                    }
                }

                @Override
                public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                    if (waitDialog != null && waitDialog.isShowing()) {
                        waitDialog.dismiss();
                    }
                    if (callBack != null) {
                        callBack.onRequestError(code, "网络异常，请稍候再试");
                    }
                }
            }, String.class);
    }
}
