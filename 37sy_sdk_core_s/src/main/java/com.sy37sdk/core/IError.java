package com.sy37sdk.core;

public interface IError {

    /*  未登录  */
    public static final int NO_LOGINED = 201;

    /*  连接超时  */
    public static final int TIME_OUT = 202;

    /*  网络错误  */
    public static final int NET_ERROR = 203;

    /*  参数错误  */
    public static final int PARAMS_ERROR = 204;

    /*  取消登录  */
    public static final int CANCEL_LOGIN = 205;
    /*  取消支付  */
    public static final int CANCEL_PAY = 205;


    public static final int ERROR_BAD_PARAMES = 204; //缺少必要参数或者参数不正确
    public static final int ERROR_NO_CONFIG_FILE = 101; //缺少37wan_config.xml配置文件
    public static final int ERROR_BAD_CONFIG = 102; //37wan_config.xml文件配置错误
    public static final int ERROR_GET_DATA_FAILD = 203; //服务器返回错误



}
