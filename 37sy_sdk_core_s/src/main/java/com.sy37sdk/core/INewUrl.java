package com.sy37sdk.core;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.util.Log;

import com.sqwan.common.annotation.UrlUpdate;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.config.MultiSdkManager;
import com.sy37sdk.account.update.UpdateUrlManager;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

public class INewUrl {

    private static final String SQ_URL_PREFS = "sq_url_prefs";
    private static final String SQ_URL_POP_HELP_URL = "pop_help_url";

    public static HashMap<String, String> urls = new HashMap<String, String>();


    public static final String KEY_S_ACTIVE = "s_activate";
    public static final String KEY_S_LOGIN = "login";
    public static final String KEY_S_REG = "reg";
    public static final String KEY_S_FREG = "freg";
    public static final String KEY_S_IMSG = "imsg";
    public static final String KEY_S_GWI = "gwi";
    public static final String KEY_S_ART = "art";
    public static final String KEY_S_CARD = "card";
    public static final String KEY_S_GCARD = "gcard";
    public static final String KEY_S_PUSH = "push";
    public static final String KEY_S_GWA = "gwa";
    public static final String KEY_S_KF = "kf";
    public static final String KEY_S_OSL = "osl";
    public static final String KEY_S_ICARD = "icard";
    public static final String KEY_S_BBS = "bbs";
    public static final String KEY_S_SHOP = "shop";
    public static final String KEY_S_IWT = "iwt";
    public static final String KEY_S_SPRO = "spro";
    public static final String KEY_S_CPWD = "cpwd";
    public static final String KEY_S_PFP = "pfp";
    public static final String KEY_S_MFP = "mfp";
    public static final String KEY_S_SPV = "spv";
    public static final String KEY_S_BP = "bp";
    public static final String KEY_S_BM = "bm";
    public static final String KEY_S_MSCODE = "mscode";
    public static final String KEY_S_MREG = "mreg";
    public static final String KEY_S_MREG_RES = "mreg_res";
    public static final String KEY_S_UAGREE = "uagree";
    public static final String KEY_S_REST_PWD = "resetPwd";
    public static final String KEY_S_AUTO_ASSIGN = "auto_assign";
    public static final String KEY_TRACK = "track";
    public static final String KEY_S_OTHER_LOGIN = "other_login";
    public static final String KEY_S_REPORT_DEV = "report_dev";
    public static final String KEY_S_REPORT_USER = "report_user";
    public static final String KEY_S_PAY = "pay";
    public static final String KEY_S_ORDER_STATUS = "order_status";

    @UrlUpdate(value = KEY_S_ACTIVE, xValue = "x_activate_s")
    public static String INIT = "https://s-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/active/";   //初始化接口  √-
    @UrlUpdate(value = KEY_S_LOGIN, xValue = "x_login")
    public static String LOGIN = "http://s-api"+ MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/login/";   //登录接口 √
    @UrlUpdate(value = KEY_S_REG, xValue = "x_register")
    public static String REG = "http://s-api"+ MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/sdk/reg/";        //注册接口 √
    @UrlUpdate(KEY_S_FREG)
    public static String REG_FAST = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/freg/"; //一键注册接口 √
    @UrlUpdate(KEY_S_IMSG)
    public static String IMSG = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/imsg/";  //个人消息   √暂时忽略
    @UrlUpdate(KEY_S_GWI)
    public static String GWI = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/gwi/";    //游戏官网   √
    @UrlUpdate(KEY_S_ART)
    public static String ART = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/art/";    //文章分类   √
    @UrlUpdate(KEY_S_CARD)
    public static String CARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/card/";    //礼包列表 √
    @UrlUpdate(KEY_S_GCARD)
    public static String GCARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/gcard/";  //礼包领取 √
    @UrlUpdate(KEY_S_PUSH)
    public static String PUSH = "http://push-api." + MultiSdkManager.APP_HOST + "/push/";    //推送接口  √
    @UrlUpdate(KEY_S_PAY)
    public static String PAY = "http://" + MultiSdkManager.APP_HOST +  "/sdk/pay/";          //支付接口   √-
    @UrlUpdate(KEY_S_ORDER_STATUS)
    public static String PAY_CHECK = "http://spay-api." + MultiSdkManager.APP_HOST + "/sdk/ordersearch/";   //支付查询接口   √-
    @UrlUpdate(KEY_S_GWA)
    public static String GWA = "http://" + MultiSdkManager.APP_HOST + "/";                  //官网 √
    @UrlUpdate(KEY_S_KF)
    public static String KEFU = "http://" + MultiSdkManager.APP_HOST + "/service/";              //客服  √
    @UrlUpdate(KEY_S_OSL)
    public static String OSL = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/osl/";    //开服列表   √
    @UrlUpdate(KEY_S_ICARD)
    public static String ICARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/icard/";  //我的礼包 √
    @UrlUpdate(KEY_S_BBS)
    public static String BBS = "http://bbs." + MultiSdkManager.APP_HOST + "/";             //BBS  √
    @UrlUpdate(KEY_S_SHOP)
    public static String SHOP = "http://" + MultiSdkManager.APP_HOST + "/shop/";             //萌点商城  √
    @UrlUpdate(KEY_S_IWT)
    public static String IWT = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/iwt/";    //我的钱包 √
    @UrlUpdate(KEY_S_SPRO)
    public static String SPRO = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/spro/";   //个人信息修改  √
    @UrlUpdate(KEY_S_CPWD)
    public static String CPWD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/cpwd/";  //修改密码 √
    @UrlUpdate(KEY_S_PFP)
    public static String PFP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/pfp/";    //手机找密码接口 √
    @UrlUpdate(KEY_S_MFP)
    public static String MFP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/mfp/";    //邮箱找回密码接口 √
    @UrlUpdate(KEY_S_SPV)
    public static String SPV = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/spv/";    //获取手机验证码接口 √
    @UrlUpdate(KEY_S_BP)
    public static String BP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/bp/";     //手机绑定接口 √
    @UrlUpdate(KEY_S_BM)
    public static String BM = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/bm/";    //邮箱绑定接口 √
    @UrlUpdate(KEY_S_MSCODE)
    public static String MSCODE = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/scode/";    //手机注册验证码接口
    @UrlUpdate(KEY_S_MREG)
    public static String MREG = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg/";    //手机注册接口
    @UrlUpdate(KEY_S_MREG_RES)
    public static String MREG_RES = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg_res/";    //短信注册结果查询
    @UrlUpdate(KEY_S_UAGREE)
    public static String USER_AGREE = "http://" + MultiSdkManager.APP_HOST + "/sdk-wrap/iframe.html?sversion=3.1.0&isrc=http://" + MultiSdkManager.APP_HOST + "/uagree/37.html";    //用户注册协议
    @UrlUpdate(value = KEY_S_REST_PWD, xValue = "x_forget_password")
    public static String FORGET_PWD = "http://" + MultiSdkManager.APP_HOST + "/sdkv1/service/retrieval/psw/index";//忘记密码页面
    @UrlUpdate(KEY_TRACK)
    public static String URL_DATA_TRACK = "https://track." + MultiSdkManager.APP_HOST + "/api/event/";  //数据采集监控接口
    @UrlUpdate(value = KEY_S_AUTO_ASSIGN, xValue = "x_auto_assign")
    public static String AUTO_SET_ACCOUNT = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/autoassign"; //自动获取注册账号密码
    @UrlUpdate(KEY_S_OTHER_LOGIN)
    public static String OTHER_LOGIN = "http://s-api." + MultiSdkManager.APP_HOST + "/oauth/login/"; // 第三方登录

    /**
     * 实名认证，上报在线时长接口
     */
    @UrlUpdate(KEY_S_REPORT_DEV)
    public static String URL_REPORT_DEV_DURATION = "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportDevDuration";

    /**
     * sdk 防沉迷上报用户在线时长接口
     */
    @UrlUpdate(KEY_S_REPORT_USER)
    public static String URL_REPORT_USER_DURATION = "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportUserDuration";

    /**
    * 支付前弹窗接口
    */
    @UrlUpdate(value = "pop_ups_order_api", xValue = "x_popups_order")
    public static String URL_POPUPS_ORDER = "http://m-api" + MultiSdkManager.SECURE_SUFFIX + MultiSdkManager.APP_HOST + "/go/sdk/popups/order";


    /**
     * 账户
     */
    public static String POP_USER_URL = "";
    /**
     * 礼包
     */
    public static String POP_CARD_URL = "";
    /**
     * 论坛
     */
    public static String POP_BBS_URL = "";
    /**
     * 攻略
     */
    public static String POP_GL_URL = "";
    /**
     * 帮助
     */
    public static String POP_HELP_URL = "";
    /**
     * 切换账号
     */
    public static String POP_CHANGEACCOUNT = "";


    public static void refreshUrls() {
        INIT = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/active/";
        LOGIN = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/login/";
        REG = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/reg/";
        REG_FAST = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/freg/";
        PAY = "http://" + MultiSdkManager.APP_HOST + "/sdk/pay/";
        PAY_CHECK = "http://spay-api." + MultiSdkManager.APP_HOST + "/sdk/ordersearch/";
        IMSG = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/imsg/";
        GWI = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/gwi/";
        ART = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/art/";
        CARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/card/";
        GCARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/gcard/";
        PUSH = "http://push-api." + MultiSdkManager.APP_HOST + "/push/";
        GWA = "http://" + MultiSdkManager.APP_HOST + "/";
        KEFU = "http://" + MultiSdkManager.APP_HOST + "/service/";
        OSL = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/osl/";
        ICARD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/icard/";
        BBS = "http://bbs." + MultiSdkManager.APP_HOST + "/";
        SHOP = "http://" + MultiSdkManager.APP_HOST + "/shop/";
        IWT = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/iwt/";
        SPRO = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/spro/";
        CPWD = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/cpwd/";
        PFP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/pfp/";
        MFP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/mfp/";
        SPV = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/spv/";
        BP = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/bp/";
        BM = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/bm/";
        MSCODE = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/scode/";
        MREG = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg/";
        MREG_RES = "http://s-api." + MultiSdkManager.APP_HOST + "/mobile/reg_res/";
        USER_AGREE = "http://" + MultiSdkManager.APP_HOST + "/sdk-wrap/iframe.html?sversion=3.1.0&isrc=http://" + MultiSdkManager.APP_HOST + "/uagree/37.html";
        URL_DATA_TRACK = "https://track." + MultiSdkManager.APP_HOST + "/api/event/";
        FORGET_PWD = "http://" + MultiSdkManager.APP_HOST + "/mt/user/account/forgetpassword/index";
        AUTO_SET_ACCOUNT = "http://s-api." + MultiSdkManager.APP_HOST + "/sdk/autoassign";
        OTHER_LOGIN = "http://s-api." + MultiSdkManager.APP_HOST + "/oauth/login/";
        URL_REPORT_DEV_DURATION = "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportDevDuration";
        URL_REPORT_USER_DURATION = "http://s-api." + MultiSdkManager.APP_HOST + "/go/sdk/reportUserDuration";
    }


    static {
        urls.put("login", LOGIN);
        urls.put("reg", REG);
        urls.put("freg", REG_FAST);
        urls.put("pay", PAY);
        urls.put("imsg", IMSG);
        urls.put("gwi", GWI);
        urls.put("art", ART);
        urls.put("card", CARD);
        urls.put("gcard", GCARD);
        urls.put("push", PUSH);
        urls.put("gwa", GWA);
        urls.put("kf", KEFU);
        urls.put("osl", OSL);
        urls.put("icard", ICARD);
        urls.put("bbs", BBS);
        urls.put("shop", SHOP);
        urls.put("iwt", IWT);
        urls.put("spro", SPRO);
        urls.put("cpwd", CPWD);
        urls.put("pfp", PFP);
        urls.put("mfp", MFP);
        urls.put("spv", SPV);
        urls.put("bp", BP);
        urls.put("bm", BM);
        urls.put("mscode", MSCODE);
        urls.put("mreg", MREG);
        urls.put("mreg_res", MREG_RES);
        //********新增
        urls.put("pay_check", PAY_CHECK);
        urls.put("uagree", USER_AGREE);

        //********新增优化版sdk
        urls.put("uurl", POP_USER_URL);
        urls.put("curl", POP_CARD_URL);
        urls.put("burl", POP_BBS_URL);
        urls.put("gurl", POP_GL_URL);
        urls.put("surl", POP_HELP_URL);
        urls.put("switch", POP_CHANGEACCOUNT);
        urls.put("resetPwd", FORGET_PWD);
    }



    public static void setUrl_POP_HELP_URL(Context context, String url) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_URL_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(SQ_URL_POP_HELP_URL, url);
        editor.commit();
    }

    public static String getUrl_POP_HELP_URL(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_URL_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(SQ_URL_POP_HELP_URL, "");
    }
}
