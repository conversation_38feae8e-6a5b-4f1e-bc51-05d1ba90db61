package com.sy37sdk.core;


import static com.sy37sdk.account.LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountChangeListener;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.mod.account.IAuthResultListener;
import com.sqwan.common.mod.account.IBackToGameLoginListener;
import com.sqwan.common.mod.account.ILoginListener;
import com.sqwan.common.mod.account.IScreenshotListener;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.SDKError;
import com.sy37sdk.account.AccountCache;
import com.sy37sdk.account.LoginTractionManager;
import com.sy37sdk.utils.Util;
import java.util.HashMap;
import java.util.Map;

public class SQwanManager implements IError {

    private static final String TAG = "【SQManager】";
    public static Context sqContext;

    public static boolean mmInit = false;    // 移动支付是否初始化了
    public static SQResultListener switchAccountListener;    //切换账号回调
    public SQResultListener payListener;    //pay回调
    public static SQResultListener back2GameLoginListener;    //回到游戏的登录界面
    public static SQScreenshotListener mScreenshotListener;//截图回调
    public SQResultListener mAuthResultListener;

    public SQwanManager(Context context, String appkey, SQResultListener initCallback) {
        sqContext = context;
        Util.setAppKey(context, appkey);
    }

    public void setAuthResultListener(final SQResultListener listener){
        this.mAuthResultListener = listener;
        ModHelper.get(IAccountMod.class).setAuthResultListener(new IAuthResultListener() {
            @Override
            public void onAuthResult(boolean success) {
                if (mAuthResultListener!=null) {
                    if (success) {
                        mAuthResultListener.onSuccess(new Bundle());
                    }else {
                        HashMap<String,String> logoutMap=new HashMap<>();
                        logoutMap.put(SqTrackKey.logout_type, "防沉迷踢下线退出");
                        String loginType = AccountCache.getLoginType(sqContext);
                        logoutMap.put(SqTrackKey.login_type, TRACK_ACCOUNT_TYPE_PHONE.equals(loginType) ? LoginTractionManager.TRACK_ACCOUNT_TYPE_PHONE_NUM : LoginTractionManager.TRACK_ACCOUNT_TYPE_ACCOUNT_NUM);
                        SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.LOGOUT_SUCC,logoutMap);
                        mAuthResultListener.onFailture(0,"");
                    }
                }
            }
        });
    }

    public void setSwitchAccountListener(final SQResultListener listener) {
        SQwanManager.switchAccountListener = listener;
        ModHelper.get(IAccountMod.class).setAccountChangeListener(new IAccountChangeListener() {
            @Override
            public void accountChanged(Map<String, String> data) {
                if(listener != null) {
                    listener.onSuccess(convertMapToBundle(data));
                    SqTrackActionManager2.getInstance().trackActionCPTest(SqTrackAction2.SWITCH_ACCOUNT_SUCCESS);
                }
            }
        });
    }

    public void setBackToGameLoginListener(final SQResultListener listener) {
        SQwanManager.back2GameLoginListener = listener;
        ModHelper.get(IAccountMod.class).setBackToGameLoginListener(new IBackToGameLoginListener() {
            @Override
            public void backToGameLogin() {
                if(listener != null) {
                    listener.onSuccess(new Bundle());
                }
            }
        });
    }

    public void setScreenshotListener(final SQScreenshotListener listener) {
        mScreenshotListener = listener;
        ModHelper.get(IAccountMod.class).setScreenshotListener(new IScreenshotListener() {
            @Override
            public Bitmap createScreenshot() {
                if(listener != null) {
                    return listener.createScreenshot();
                }
                return null;
            }
        });
    }

    public void setCurrentContext(Context context) {
        sqContext = context;
    }

    /**
     * 切换账号
     *
     * @param listener
     */
    public void changeAccount(final SQResultListener listener) {
        if (Util.isSkipSQChangeAccountLogin(sqContext)) {
            //跳过登录界面，返回空的信息
            LogUtil.v("SQWanManager changeAccount SkipSQChange");
            listener.onSuccess(new Bundle());
        } else {
            //显示登录界面
            Util.setLoginType(sqContext, "sq");
            ModHelper.get(IAccountMod.class).changeAccount(new ILoginListener() {
                @Override
                public void onSuccess(Map<String, String> data) {
                    listener.onSuccess(convertMapToBundle(data));
                }

                @Override
                public void onFailure(int code, String msg) {
                    listener.onFailture(code, msg);
                }
            });
        }
    }

    /**
     * 显示账号登录界面
     *
     * @param listener
     */
    public void showSQLoginView(final SQResultListener listener) {
        ModHelper.get(IAccountMod.class).showLoginView(new ILoginListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                listener.onSuccess(convertMapToBundle(data));
            }

            @Override
            public void onFailure(int code, String msg) {
                listener.onFailture(code, msg);
            }
        });
    }

    /**
     * 登录接口
     *
     * @modify 2015年05月30日11:22:47  -- 增加切换账号逻辑
     */
    public void login(final SQResultListener loginCallback) {
        if(loginCallback == null) {
            SQLog.w(TAG + "未设置callback, 忽略");
            return;
        }
        if (TextUtils.isEmpty(Util.getAppKey(sqContext))) {
            SQLog.e(TAG + "未设置app key, 登录失败");
            loginCallback.onFailture(SDKError.PARAMS_ERROR_NULL.code, SDKError.PARAMS_ERROR_NULL.message);
            return;
        }
        ModHelper.get(IAccountMod.class).login(new ILoginListener() {
            @Override
            public void onSuccess(Map<String, String> data) {
                loginCallback.onSuccess(convertMapToBundle(data));
            }

            @Override
            public void onFailure(int code, String msg) {
                loginCallback.onFailture(code, msg);
            }
        });
    }

    private Bundle convertMapToBundle(Map<String, String> data) {
        Bundle bundle = new Bundle();
        if(data != null && !data.isEmpty()) {
            for(Map.Entry<String, String> entry : data.entrySet()) {
                bundle.putString(entry.getKey(), entry.getValue());
            }
        }
        return bundle;
    }

    /**
     * 读取当前用户信息
     *
     * @param listener
     */
    public void read(SQResultListener listener, Bundle bundle) {
        String userid = Util.getUserid(sqContext);//Util.getFgValue("userid", context);
        String username = Util.getUsername(sqContext);//Util.getFgValue("username", context);
        String token = Util.getToken(sqContext);//Util.getFgValue("token", context);

        if (token.equals("")) {

            if (listener != null) {

                listener.onFailture(SQwanManager.NO_LOGINED, "No User Logined");
            }

        } else {

            if (listener != null && bundle != null) {

                bundle.putString("userid", userid);
                bundle.putString("username", username);
                bundle.putString("token", token);
                listener.onSuccess(bundle);
            }

        }

    }

    /**
     * 退出接口方法
     */
    public void logout() {
        try {
            //清理本地数据
//	        Util.setToken(context, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 支付接口(供CP调用)
     *
     * @param doid    CP订单ID
     * @param dpt     CP商品名
     * @param dcn     CP货币名称
     * @param dsid    CP游戏服ID( 标识 )
     * @param dext    CP扩展回调参数
     * @param drid    CP角色ID
     * @param drname  CP角色名
     * @param drlevel CP角色等级
     * @param dmoney  CP金额(定额)
     * @param dradio  CP兑换比率(1元兑换率默认1:10)
     * @param moid    SDK[M]订单ID
     */
    public void pay(Context context, String doid, String dpt, String dcn, String dsid, String dext, String drid, String drname,
                    int drlevel, float dmoney, int dradio, String moid, SQResultListener payListener) {
        this.payListener = payListener;
        outPay(context, doid, dpt, dcn, dsid, dext, drid, drname, drlevel, dmoney, dradio, moid, 1, 0, payListener);

    }


    /*
     * 仅供外部点击充值调用调用 (private ,need proguid)
     * web支付用的都是此参数
     * type = 0 其他支付
     * type = 1 洋葱头支付
     */
    public void outPay(Context context, String doid, String dpt, String dcn, String dsid, String dext, String drid, String drname,
                       int drlevel, float dmoney, int dradio, String moid, int ig, int type, final SQResultListener payListener) {
        if (payListener != null && "".equals(dsid)) {
            LogUtil.w("outPay 区服为空");
            payListener.onFailture(PARAMS_ERROR, "区服ID不能为空");
            return;
        }

        String token = Util.getToken(context);
        if ("".equals(token) && payListener != null) {
            LogUtil.w("outPay 尚未登录");
            payListener.onFailture(IError.NO_LOGINED, "尚未登录，请登录");
            return;
        }

        if (payListener != null) {
            SQLog.e("不支持outPay");
            payListener.onFailture(-1, "不支持(-2)");
        }
    }


}
