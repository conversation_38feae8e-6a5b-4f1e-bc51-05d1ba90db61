package com.sy37sdk.core;

import java.util.HashMap;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;

public interface SQSdkInterface {

    //初始化接口
    public void init(Context context,String appkey,SQResultListener initCallback);

    //登录接口
    public void login(Context context,SQResultListener listener);

    //主动调用切换账号接口
    public void changeAccount(Context context,SQResultListener listener);

    //从悬浮窗中切换账号接口
    public void setSwitchAccountListener(SQResultListener listener);

    public void onResume();

    public void onPause();

    void onConfigurationChanged(Configuration newConfig);


    public void onStop();

    //退出接口
    public void logout(Context context,SQResultListener listener);


    //支付接口
    public void pay (Context context,String doid,String dsid,String dext, float dmoney ,String moid,SQResultListener payListener);

    //提交角色信息接口（可以提交指定格式数据）
    public void submitRoleInfo(HashMap<String, String> infos);

    void showUAgreement(Context context);

    void setAuthResultListener(SQResultListener listener);
    void showAgeAppropriate(Context context);
}
