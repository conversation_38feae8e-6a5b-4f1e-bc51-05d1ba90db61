package com.sy37sdk.core;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import com.social.sdk.SocialApi;
import com.sq.eventbus.core.EventBus;
import com.sq.tool.network.SqHttpCallback;
import com.sqnetwork.voly.VolleyError;
import com.sqwan.common.BuglessAction;
import com.sqwan.common.dialog.CommonAlertDialog;
import com.sqwan.common.eventbus.SActiveEvent;
import com.sqwan.common.mod.ModHelper;
import com.sqwan.common.mod.account.IAccountMod;
import com.sqwan.common.track.SqTrackAction2;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.track.SqTrackKey;
import com.sqwan.common.util.AssetsUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.msdk.config.ConfigManager;
import com.sy37sdk.utils.Util;
import com.sy37sdk.utils.ViewController;
import com.sy37sdk.views.SplashDialog;
import com.sy37sdk.widget.ExitDialog;
import com.sy37sdk.widget.ExitDialog.ExitCallBack;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;
import org.json.JSONException;
import org.json.JSONObject;


public class SQwan implements SQSdkInterface,IError {

    private static SQwan instance;

    private SQwanManager wan;

    private RequestManager rManager;

    private static byte[] lock = new byte[0];

    private Context context;

    private boolean isInit = false;
    private boolean isInitResponse = false;

    public static boolean isLoad = true;//是否加载闪屏
    @Deprecated
    public static boolean isSQLoginSuccess = false;//登录成功


    /**
     * 废弃， 请使用VersionUtil中的sdkVersion
     */
    @Deprecated
    public static final String sdkVersion = "3.6.0";

    /**
     * loading界面
     */
    private ViewGroup mDecorView;
    private View mView;
    private SQwan(){
    }


    /**
     * 获取单例
     *
     * @return
     */
    public static SQwan getInstance() {

        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new SQwan();
                }
            }
        }
        return instance;
    }



    /*
     * 2014年12月24日15:50:19 添加
     */
    public void setContext(Context cxt){

        this.context = cxt;

        System.out.println("--SQ setContext--");
        if (wan != null) {
            SQwanManager.sqContext = cxt;
        }
    }


    public void init(final Context cxt,String appkey,final SQResultListener initCallback){

        this.context = cxt;
        wan = new SQwanManager(context,appkey,initCallback);
        rManager = new RequestManager(context);

        //初始化socialsdk
        SocialApi.init(context);

        //解析配置文件 √
        //加载logo背景图
        //更新检测
        //init请求
        rManager.initRequst(new SqHttpCallback<JSONObject>() {

            @Override
            public void onResponseStateError(int httpStatus, int state, @NonNull String msg,
                @Nullable String data) {
                isInitResponse = true;
            }

            @Override
            public void onSuccess(JSONObject dataJson) {
                isInitResponse = true;

                try {
                    isInit = true;

                    //拆解数据
                    String configs = dataJson.getString("c");

                    JSONObject jsonObj_configs = new JSONObject(configs);

                    // 从后端读取配置的闪屏配置
                    String splashConfig = dataJson.optString("e");
                    readSplashConfig(splashConfig);

                    if (jsonObj_configs.has("tt")) {

                        boolean is_img = jsonObj_configs.getJSONObject("tt").has("img");
                        boolean is_u = jsonObj_configs.getJSONObject("tt").has("u");
                        boolean is_dpgn = jsonObj_configs.getJSONObject("tt").has("dpgn");
                        String exit_img = ""; //退弹img
                        String exit_url = ""; //退弹url
                        String exit_dpgn = ""; //退弹dpgn
                        if (is_img && is_u && is_dpgn) {//判断tt数组里是否有内容

                            //广告弹窗内容
                            exit_img = jsonObj_configs.getJSONObject("tt").getString("img");
                            exit_url = jsonObj_configs.getJSONObject("tt").getString("u");
                            exit_dpgn = jsonObj_configs.getJSONObject("tt").getString("dpgn");

                        } else {
                            System.out.println("初始化传入的tt参数为空");
                        }

                        IConfig.exitImgPath = exit_img;
                        IConfig.exitUrl = exit_url;
                        IConfig.exitDpgn = exit_dpgn;

                    }

                    if (jsonObj_configs.has("scode")) {
                        int scode = jsonObj_configs.optInt("scode", 0);
                        ConfigManager.getInstance(context).setLessFunctionCode(scode);
                    } else {
                        ConfigManager.getInstance(context).setLessFunctionCode(0);
                    }

                    EventBus.getDefault().post(new SActiveEvent(dataJson.toString()));
                    Bundle bundle = new Bundle();
                    initCallback.onSuccess(bundle);
                } catch (Exception e) {
                    e.printStackTrace();
                    HashMap<String, String> extraMap = new HashMap<>();
                    extraMap.put(SqTrackKey.fail_code, "204");
                    extraMap.put(SqTrackKey.reason_fail, "s层解析错误 " + e.getMessage());
                    SqTrackActionManager2.getInstance().trackAction(SqTrackAction2.sdk_init_fail, extraMap);
                    BuglessAction.reportCatchException(e, dataJson.toString(), BuglessAction.S_INIT);
                    initCallback.onFailture(IError.ERROR_GET_DATA_FAILD, "数据异常，初始化失败");
                    isInit = false;
                }
            }

            @Override
            public void onFailure(int code, String errorMsg, @NonNull VolleyError error) {
                initCallback.onFailture(IError.ERROR_GET_DATA_FAILD, errorMsg);
            }
        });



    }

    /**
     * 读取后端返回的闪屏配置
     * @param splashConfig
     */
    private void readSplashConfig(String splashConfig){
        String flashScreenImgUrl = null;
        if (!TextUtils.isEmpty(splashConfig)){
            try {
                JSONObject json = new JSONObject(splashConfig);
                flashScreenImgUrl = json.getString("flash_screen_img");
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        final String imgUrl = flashScreenImgUrl;
        ((Activity)context).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showSplash(imgUrl);
            }
        });
    }

    /**
     * 显示闪屏
     * 先进行判断是否要显示闪屏，如果要的话，显示的时候，内部判断，显示的闪屏是本地还是imgUrl，当imgUrl为空的时候，就显示本地闪屏
     */
    private void showSplash(String imgUrl){
        boolean show = false;
        if (!TextUtils.isEmpty(imgUrl)){
            show = true;
        }else {
            // 走老的逻辑
            if (Util.getIsSpecialSDK(context)) {
                LogUtil.d("是简版");
                //读取multiconfig中的配置文件，判断是否显示截图测试按钮
                Properties sdkInfo = AssetsUtils.readProperties(context, AssetsUtils.SQ_MULTI_CONFIG);
                if (sdkInfo != null) {
                    String showSplashWhenScut = sdkInfo.getProperty(AssetsUtils.PRO_SHOW_SPLASH_WHEN_SCUT);
                    if (!TextUtils.isEmpty(showSplashWhenScut) && TextUtils.equals("1", showSplashWhenScut)) {
                        show = true;
                        LogUtil.d("开启了简版显示闪屏");
                    }else {
                        LogUtil.d("未设置简版显示闪屏或不显示闪屏");
                    }
                }
            }
        }
        if (show){
            SplashDialog splash = new SplashDialog(context,3);
            splash.setImgUrl(imgUrl);
            splash.show();
        }
    }


    /**
     * 更新本地url组为服务器所传数据
     * @param urlsData
     * @throws
     */
    private void checkUrlNeedUpdate(String urlsData) throws Exception {
        LogUtil.e("checkUrlNeedUpdate");
        LogUtil.e(urlsData);
        JSONObject jurls = new JSONObject(urlsData);
        Set<String> urls_keys = INewUrl.urls.keySet();
        Iterator<String> it = urls_keys.iterator();
        while (it.hasNext()) {
            String key = it.next();

            if (!jurls.isNull(key)) {
                String urlValue = jurls.getString(key);

                if ("login".equals(key)) { INewUrl.LOGIN = urlValue; }
                if ("reg".equals(key)) { INewUrl.REG = urlValue; }
                if ("freg".equals(key)) { INewUrl.REG_FAST = urlValue; }
                if ("imsg".equals(key)) { INewUrl.IMSG = urlValue; }
                if ("gwi".equals(key)) { INewUrl.GWI = urlValue; }
                if ("art".equals(key)) { INewUrl.ART = urlValue; }
                if ("card".equals(key)) { INewUrl.CARD = urlValue; }
                if ("gcard".equals(key)) { INewUrl.GCARD = urlValue; }
                if ("push".equals(key)) { INewUrl.PUSH = urlValue; }
                if ("gwa".equals(key)) { INewUrl.GWA = urlValue; }
                if ("kf".equals(key)) { INewUrl.KEFU = urlValue; }
                if ("osl".equals(key)) { INewUrl.OSL = urlValue; }
                if ("icard".equals(key)) { INewUrl.ICARD = urlValue; }
                if ("bbs".equals(key)) { INewUrl.BBS = urlValue; }
                if ("shop".equals(key)) { INewUrl.SHOP = urlValue; }
                if ("iwt".equals(key)) { INewUrl.IWT = urlValue; }
                if ("spro".equals(key)) { INewUrl.SPRO = urlValue; }
                if ("cpwd".equals(key)) { INewUrl.CPWD = urlValue; }
                if ("pfp".equals(key)) { INewUrl.PFP = urlValue; }
                if ("mfp".equals(key)) { INewUrl.MFP = urlValue; }
                if ("spv".equals(key)) { INewUrl.SPV = urlValue; }
                if ("bp".equals(key)) { INewUrl.BP = urlValue; }
                if ("icard".equals(key)) { INewUrl.ICARD = urlValue; }
                if ("bm".equals(key)) { INewUrl.BM = urlValue; }
                if ("mscode".equals(key)) { INewUrl.MSCODE = urlValue; }
                if ("mreg".equals(key)) { INewUrl.MREG = urlValue; }
                if ("mreg_res".equals(key)) { INewUrl.MREG_RES = urlValue; }
                if ("uagree".equals(key)) { INewUrl.USER_AGREE = urlValue; }
                if ("resetPwd".equals(key)) { INewUrl.FORGET_PWD = urlValue; }
            }
        }
    }

    /**
     * 切换账号
     * @param listener
     */
    public void changeAccount(final Context context,final SQResultListener listener){
        wan.changeAccount(new SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                listener.onSuccess(bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                listener.onFailture(code, msg);
            }
        });
    }

    /**显示登录界面
     * @param listener
     */
    public void showLoginView(SQResultListener listener){
        wan.showSQLoginView(listener);
    }


    /**
     * 监听账号切换
     **/
    public void setSwitchAccountListener(SQResultListener listener){
        wan.setSwitchAccountListener(listener);
    }
    /**
     *回到游戏登录界面
     **/
    public void setBackToGameLoginListener(SQResultListener listener){
        wan.setBackToGameLoginListener(listener);
    }

    /**
     * 设置悬浮窗截图监听
     * @param listener
     */
    public void setScreenshotListener(SQScreenshotListener listener){
        wan.setScreenshotListener(listener);
    }

    /**
     * 账号注册/登录接口
     * @param listener
     */
    public void login(final Context context,final SQResultListener listener) {
        //登录接口，增加一个登录成功的判断
        wan.login(new SQResultListener() {

            @Override
            public void onSuccess(Bundle bundle) {
                listener.onSuccess(bundle);
            }

            @Override
            public void onFailture(int code, String msg) {
                listener.onFailture(code, msg);
            }
        });
    }


    @Override
    public void onStop(){

    }

    @Override
    public void onResume(){
        ModHelper.get(IAccountMod.class).onResume();
    }


    @Override
    public void onPause(){
        ModHelper.get(IAccountMod.class).onPause();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        ModHelper.get(IAccountMod.class).onConfigurationChanged(newConfig);
    }

    /**
     * 退出接口方法
     * @param context
     */
    public void logout(final Context context, final SQResultListener listener) {

        if (ConfigManager.getInstance(context).isLessFunction() || ConfigManager.getInstance(context).isSplashSDK()) {
            //显示原生退弹
            LogUtil.i("show original exit dialog");
            showOriginalExit(context, listener);
        } else {
            LogUtil.i("show sq exit dialog");
            //弹出广告退弹
            showExitDialog(new ExitCallBack() {

                @Override
                public void exit() {
                    ModHelper.get(IAccountMod.class).logout();
                    listener.onSuccess(new Bundle());

                }
            });
        }

    }

    private void showOriginalExit(final Context context, final SQResultListener listener){
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                new CommonAlertDialog.Builder(context)
                        .setTitle("您确定退出游戏吗？")
                        .setPositiveButton("确定", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ModHelper.get(IAccountMod.class).logout();
                                wan.logout();
                                listener.onSuccess(new Bundle());
                            }
                        })
                        .setNegativeButton("取消", null).show();
            }
        });

    }

    /**
     * 显示退弹框
     */
    private void showExitDialog(final ExitCallBack callback) {

        new Handler(Looper.getMainLooper()).post(new Runnable() {

            @Override
            public void run() {

                ExitDialog exitDialog = new ExitDialog(context, callback);
                exitDialog.setUrl(IConfig.exitUrl); //解析
                exitDialog.setPkn(IConfig.exitDpgn); //包名
                exitDialog.setImagePath(IConfig.exitImgPath); //初始化完成时开异步线程预加载
                exitDialog.setCanceledOnTouchOutside(true);
                exitDialog.setCancelable(true);
                exitDialog.show();

            }
        });


    }



    /**
     * 支付接口(供CP调用)
     * @param doid  CP订单ID
     * @param dpt   CP商品名
     * @param dcn   CP货币名称
     * @param dsid  CP游戏服ID( 标识 )
     * @param dext  CP扩展回调参数
     * @param drid  CP角色ID
     * @param drname   CP角色名
     * @param drlevel  CP角色等级
     * @param dmoney   CP金额(定额)
     * @param dradio   CP兑换比率(1元兑换率默认1:10)
     * @param moid     SDK[M]订单ID
     */
    public void pay (Context context,String doid,String dpt,String dcn ,String dsid,String dext,String drid,String drname,
                     int drlevel ,float dmoney ,int dradio ,String moid,SQResultListener payListener){

        wan.pay(context, doid, dpt, dcn, dsid, dext, drid, drname, drlevel, dmoney, dradio, moid, payListener);

    }

    /**网页支付
     * @param context
     */
    public void payWeb(Context context){
        if(Util.getToken(context) == null || "".equals(Util.getToken(context))){    //用户没登录，调登录接口

            ViewController.showToast(context, "您的登录状态已过期，请重新登录【20001】");

        }else{
            wan.outPay(context,"", "", "", "", "", "", "", 0, 0, 10, "", 1, 1, null);
        }
    }


    /**
     *
     * @param context
     * @param doid  CP订单ID
     * @param dsid  CP游戏服ID( 标识 )
     * @param dext  CP扩展回调参数
     * @param dmoney  CP金额(定额)
     * @param moid  SDK[M]订单ID
     * @param payListener
     */
    @Override
    public void pay (Context context,String doid,String dsid,String dext, float dmoney ,String moid,SQResultListener payListener){

        pay(context, doid, "", "", dsid, dext, "", "", 0, dmoney, 0, moid, payListener);

    }


    /**
     * //用于类似小米平台需要提交角色信息的时候获取游戏数据
     */
    @Override
    public void submitRoleInfo(HashMap<String, String> infos) {
        ModHelper.get(IAccountMod.class).submitRoleInfo(infos);
    }


    @Override
    public void showUAgreement(Context context) {
        ModHelper.get(IAccountMod.class).showUAgreement();
    }

    @Override
    public void setAuthResultListener(SQResultListener listener) {
        wan.setAuthResultListener(listener);
    }

    @Override
    public void showAgeAppropriate(Context context) {
        ModHelper.get(IAccountMod.class).showAgeAppropriate();
    }
}
