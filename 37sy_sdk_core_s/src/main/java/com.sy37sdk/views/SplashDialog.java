package com.sy37sdk.views;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.sqwan.common.util.StatusBarUtil;
import com.sqwan.common.util.AsyncImageLoader;
import com.sy37sdk.utils.BitmapUtils;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.utils.Util;

import java.lang.reflect.Method;


public class SplashDialog extends Dialog {

    private Context mContext;
    private int splashTime = 3;//默认3秒
    private Bitmap bitmap;
    private String mSplashImgUrl;
    private SplashListener splashCallback;

    public SplashDialog(Context context, int time) {
        super(context, Util.getIdByName("Mdialog", "style", context.getPackageName(), context));
        this.mContext = context;
        this.splashTime = time;
    }

    public SplashDialog(Context context) {
        super(context, Util.getIdByName("Mdialog", "style", context.getPackageName(), context));
        this.mContext = context;
    }

    public void setSplashTime(int time) {
        this.splashTime = time;
    }

    public void setImgUrl(String url){
        mSplashImgUrl = url;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LogUtil.i("splash dialog onCreate!");
        ImageView imageView = new ImageView(mContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        imageView.setLayoutParams(layoutParams);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        if (TextUtils.isEmpty(mSplashImgUrl)){
            LogUtil.i("使用老的逻辑显示本地闪图");
            if (Util.isScreenOriatationPortrait(mContext)) {
                bitmap = BitmapUtils.decodeResource(getContext(), "sy37_splash_port");
            } else {
                bitmap = BitmapUtils.decodeResource(getContext(), "sy37_splash_land");
            }
            LogUtil.e("show splash dialog");
            imageView.setImageBitmap(bitmap);
            imageView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    dismiss();
                    bitmap.recycle();
                    bitmap = null;
                    //如果闪屏结束有需要做的事情
                    if (splashCallback != null) {
                        splashCallback.afterSplash();
                    }
                }
            }, splashTime * 1000);
        }else {
            LogUtil.i("加载后端配置的闪图：" + mSplashImgUrl);
            AsyncImageLoader asyncImageLoader = new AsyncImageLoader(mContext);
            asyncImageLoader.loadDrawable(mSplashImgUrl, imageView, new AsyncImageLoader.ImageCallback() {
                @Override
                public void imageLoaded(Bitmap imageDrawable, ImageView imageView, String imageUrl) {
                    bitmap = imageDrawable;
                    imageView.setImageBitmap(imageDrawable);
                    imageView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            dismiss();
                            if (bitmap != null) {
                                bitmap.recycle();
                                bitmap = null;
                            }
                            //如果闪屏结束有需要做的事情
                            if (splashCallback != null) {
                                splashCallback.afterSplash();
                            }
                        }
                    }, splashTime * 1000);
                }
            });
        }

        setContentView(imageView);
        hideSystemView(getWindow());
    }

    private void hideSystemView(Window window) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            window.setAttributes(layoutParams);
        } else {
            hideMiuiNotch();
        }
    }

    private void hideMiuiNotch() {
        int flag = 0x00000100 | 0x00000200 | 0x00000400;
        try {
            Method method = Window.class.getMethod("addExtraFlags",
                    int.class);
            method.invoke(getWindow(), flag);
        } catch (Exception e) {
            LogUtil.i("addExtraFlags not found.");
        }
    }


    @Override
    public void show() {
        if(mContext != null && !((Activity)mContext).isFinishing()) {
            super.show();
            StatusBarUtil.hideSystemUI(getWindow());
        }
    }

    public interface SplashListener {
        void afterSplash();
    }

    public void setSplashListener(SplashListener listener) {
        this.splashCallback = listener;
    }

}
