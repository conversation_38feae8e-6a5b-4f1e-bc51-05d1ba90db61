package com.sy37sdk.views;


import android.app.Activity;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.sy37sdk.utils.Util;

public class WelcomePage extends LinearLayout {
    private ImageView iv_logo;
    private CustomProgressBar mBar;

    private Activity activity;

    public WelcomePage(Activity activity) {
        super(activity);
        this.activity = activity;
        init();

        iv_logo = new ImageView(activity);
        mBar = new CustomProgressBar(activity);

        iv_logo.setImageResource(Util.getIdByName("sy37_logo_mobile", "drawable", activity.getPackageName(), activity));
        iv_logo.setPadding(0, 0, 0, (int) dpTopx(10));

        addView(iv_logo);
        addView(mBar);

    }

    private void init() {
        setBackgroundResource(Util.getIdByName("sy37_welcom_main_bg", "drawable", activity.getPackageName(), activity));
        setOrientation(LinearLayout.VERTICAL);
        setGravity(Gravity.CENTER);
        setFocusable(true);
        setFocusableInTouchMode(true);

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        
        return true;
    }

    private float dpTopx(float dp) {
        float size = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, getResources().getDisplayMetrics());
        return size;

    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return true;
    }

}
