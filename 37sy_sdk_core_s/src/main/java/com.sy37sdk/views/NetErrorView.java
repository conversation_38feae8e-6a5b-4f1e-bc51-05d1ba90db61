package com.sy37sdk.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.sqwan.common.util.SqResUtils;
import com.sy37sdk.utils.Util;

public class NetErrorView extends RelativeLayout {

    public NetErrorView(Context context) {
        super(context);
    }

    public NetErrorView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NetErrorView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        View rootView = LayoutInflater.from(getContext()).inflate(Util.getIdByName("sy37_net_error_view_port", "layout", getContext().getPackageName(), getContext()), this);
        ImageView errorView = rootView.findViewById(SqResUtils.getId(getContext(), "iv_error_view"));
        if(errorView != null) {
            errorView.setImageResource(SqResUtils.getDrawableId(getContext(), "sy37_net_wifi"));
        }
    }
}
