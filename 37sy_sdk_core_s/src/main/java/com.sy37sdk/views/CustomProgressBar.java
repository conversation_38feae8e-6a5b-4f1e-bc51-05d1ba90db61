package com.sy37sdk.views;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.sy37sdk.utils.Util;


public class CustomProgressBar extends ImageView {
    private Activity activity;

    private AnimationDrawable loadingAnimationDrawable;
    private Bitmap loadingBitmap;
    private final int duration = 100;

    public CustomProgressBar(Activity activity) {
        super(activity);
        // TODO Auto-generated constructor stub
        this.activity = activity;
        setFocusable(true);
        setFocusableInTouchMode(true);

        loadingBitmap = BitmapFactory.decodeResource(getResources(), Util.getIdByName("sy37_loading", "drawable", activity.getPackageName(), activity));

        loadingAnimationDrawable = new AnimationDrawable();
        int orientationDegree = 0;
        while (orientationDegree <= 360) {
            loadingAnimationDrawable.addFrame(adjustDrawableRotation(orientationDegree), duration);
            orientationDegree += 30;
        }

        setImageDrawable(loadingAnimationDrawable);
        loadingAnimationDrawable.setOneShot(false);
        loadingAnimationDrawable.start();
    }
    
    
    private Drawable adjustDrawableRotation(int orientationDegree) {
        PaintFlagsDrawFilter pfd = new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
        Matrix matrix = new Matrix();
        matrix.setRotate(orientationDegree, (float) loadingBitmap.getWidth() / 2, (float) loadingBitmap.getHeight() / 2);
        Bitmap bitmap = Bitmap.createBitmap(loadingBitmap.getHeight(), loadingBitmap.getWidth(), Bitmap.Config.ARGB_8888);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(pfd);
        canvas.drawBitmap(loadingBitmap, matrix, paint);

        Drawable drawable = new BitmapDrawable(getResources(), bitmap);
        return drawable;
    }

}
