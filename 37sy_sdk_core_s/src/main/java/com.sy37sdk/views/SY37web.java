package com.sy37sdk.views;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnFocusChangeListener;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.webkit.DownloadListener;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.plugin.standard.RealBaseActivity;
import com.sq.tool.logger.SQLog;
import com.sqwan.common.track.SqTrackActionManager2;
import com.sqwan.common.util.LogUtil;
import com.sy37sdk.account.AccountTools;
import com.sy37sdk.account.UserInfo;
import com.sy37sdk.account.trackaction.UserNameEmptyTrackAction;
import com.sy37sdk.core.SQResultListener;
import com.sy37sdk.core.SQwan;
import com.sy37sdk.core.SQwanManager;
import com.sy37sdk.utils.AppUtils;
import com.sy37sdk.utils.Util;
import com.sy37sdk.utils.ViewController;
import com.sy37sdk.utils.ZipString;
import com.sy37sdk.views.webview.AsyncHandler;
import com.sy37sdk.views.webview.WebviewUtils;
import com.sy37sdk.widget.ProgressDialog;
import java.lang.reflect.Field;

/**
 * 在旧版本(3.6.0), 该类还在AndroidManifest.xml中有声明, 不确定直接删除该类是否会对旧版本热更有影响, 因此保留
 */
@Deprecated
public class SY37web extends RealBaseActivity {

    public static final String WEB_URL = "url";
    public static final String WEB_TITLE = "title";
    public static final String WEB_SHOW_TITLE = "showTitle";

    private RelativeLayout header;
    private WebView webView;
    private View errorNetView;
    private View mIvErrorViewBack;

    private static final String TEL = "tel:";
    private static final String MAILTO = "mailto:";
    private static final String QQ = "qq:";
    private static final String WEIXIN = "weixin:";
    private static final int MOBILEQQ_TYPE = 2;
    private static final int WEIXIN_TYPE = 3;


    private ProgressDialog waitDialog;
    private ValueCallback<Uri[]> uploadMessageAboveL;
    private final static int FILE_CHOOSER_RESULT_CODE = 10000;

    private String currentUrl;
    private String title;
    private boolean showTitle;
    private TextView tvTitle;
    private ImageButton ibClose;

    //加载超时
    private long LOAD_TIMEOUT = 5000L;
    private final int RETRY_LOAD_COUNTS = 3;//重试次数
    private ValueCallback<Uri> uploadMessage;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(Util.getIdByName("sy37_web", "layout", getContext().getPackageName(), getContext()));

        currentUrl = getIntent().getExtras().getString(WEB_URL);
        title = getIntent().getExtras().getString(WEB_TITLE);
        showTitle = getIntent().getExtras().getBoolean(WEB_SHOW_TITLE);

        // 进入即隐藏软键盘
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN
                        | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        //初始化view
        header = (RelativeLayout) findViewById(Util.getIdByName("header", "id", getContext().getPackageName(), getContext()));
        tvTitle = (TextView) findViewById(Util.getIdByName("title", "id", getPackageName(), getContext()));
        ibClose = (ImageButton) findViewById(Util.getIdByName("togame", "id", getPackageName(), getContext()));

        errorNetView = findViewById(Util.getIdByName("sy37_m_net_error_view", "id", getPackageName(), getContext()));
        mIvErrorViewBack = findViewById(Util.getIdByName("sy37_iv_back", "id", getPackageName(), getContext()));

        if (showTitle) {
            mIvErrorViewBack.setVisibility(View.GONE);
            header.setVisibility(View.VISIBLE);
            tvTitle.setText(title);
            ibClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });
        } else {
            mIvErrorViewBack.setVisibility(View.VISIBLE);
            mIvErrorViewBack.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
            header.setVisibility(View.GONE);
        }



        //浏览器加载部分
        webView = findViewById(Util.getIdByName("webView", "id", this.getPackageName(), getContext()));

        //各种监听
        webView.setOnFocusChangeListener(new myOnFocusChangeListener());  //防止输入框点击后页面放大
        webView.setDownloadListener(new MyWebViewDownLoadListener());//下载
        webView.setWebViewClient(new myWebViewClient());
        webView.setWebChromeClient(new myWebChromeClient());
        //属性设置
        webView.addJavascriptInterface(new JsObj(getContext()), "fee");
        //延迟加载
        showWebHandler.sendEmptyMessageDelayed(0, 200);

        // 调用过时类埋点
        SqTrackActionManager2.getInstance().trackDeprecatedCall(this);
    }


    Handler showWebHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);

            if (currentUrl == null || TextUtils.isEmpty(currentUrl)) {
                ViewController.showToast(getContext(), "主人，网址是空的，即将为您关闭..");
                //关闭wap页
                closeHandler.sendEmptyMessageDelayed(0, 1000);
                return;
            }
            // 正常加载
            if (Util.isNetworkConnected(getApplicationContext())) {
                if(webView != null){
                    webView.loadUrl(currentUrl);
                }
            } else {
                loadErrorHtml();
            }

        }
    };

    Handler closeHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);
            finish();
        }
    };

    Handler showErrorWebHandler = new Handler() {

        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);
            loadErrorHtml();
        }
    };


    private void showWaitDialog(Context context) {
        if (context != null) {
            //如果上下文被杀掉了，则不执行
            if (waitDialog == null) {
                waitDialog = new ProgressDialog(context);
                waitDialog.setCanceledOnTouchOutside(false);
            }

            if (waitDialog != null && !waitDialog.isShowing() && !isFinishing()) {
                waitDialog.show();
            }
        }

    }

    private void hideWaitDialog() {
        if (waitDialog != null && waitDialog.isShowing() && !isFinishing()) {
            waitDialog.dismiss();
        }
    }

    private void updateWaitDialog(String msg) {
        if (waitDialog != null && waitDialog.isShowing() && !isFinishing()) {
            waitDialog.setMessage(msg);
        }
    }


    private void loadErrorHtml() {
        errorNetView.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) { // 表示按返回键时的操作

            System.out.println("--onBackPressed,canGoBack:" + webView.canGoBack());

            if (webView.canGoBack()) {
                webView.goBack(); // 后退 //webview.goForward();//前进
            } else {
                finish();
            }
        }
        return true;
    }


    public class JsObj {

        private Context context;

        public JsObj(Context context) {
            super();
            this.context = context;
        }

        @JavascriptInterface
        public void enRefresh() {
            LogUtil.i("wap 调用enRefresh");
            ((Activity) context).runOnUiThread(new Runnable() {

                @Override
                public void run() {

                    showWebHandler.sendEmptyMessageDelayed(0, 200);
                }
            });
        }

        @JavascriptInterface
        public void enClose() {
            LogUtil.i("wap 调用enClose");
            ((Activity) context).runOnUiThread(new Runnable() {

                @Override
                public void run() {
                    ((Activity) context).finish();
                }
            });
        }

        @JavascriptInterface
        public void modifyPass() {
            LogUtil.i("wap 调用 modifyPass");
            // 清除当前用户的密码
            Util.setPassword(context, ZipString.json2ZipString(""));
            // 组建用户
            UserInfo info = new UserInfo();
            info.setUname(Util.getUsername(context));
            info.setUpwd(Util.getPassword(context));
            UserNameEmptyTrackAction.report(UserNameEmptyTrackAction.ActionType.modifyPass,info.getUname(),info.toString());
            AccountTools.setAccountToFile(context, info);
            // 弹出登录页面
            enLogin();
        }


        @JavascriptInterface
        public void enLogin() {
            LogUtil.i("wap 调用 enLogin");
            final Context logincontext = SQwanManager.sqContext;
            ((Activity) logincontext).runOnUiThread(new Runnable() {

                @Override
                public void run() {

                    if (SQwanManager.back2GameLoginListener != null) {
                        //如果有回到游戏登录界面的监听，优先使用此
                        LogUtil.w("回到游戏登录界面的监听|不为空，现在回到游戏的登录界面");
                        SQwanManager.back2GameLoginListener.onSuccess(new Bundle());
                    } else {

                        LogUtil.w("回到游戏登录界面的监听|为空，使用切换账号的逻辑");
                        if (SQwanManager.switchAccountListener == null) {
                            ViewController.showToast(logincontext, "切换账号错误，请联系客服【10003】");
                            return;
                        }

                        if (Util.isSkipSQChangeAccountLogin(logincontext)) {
                            //跳过login，直接返回OK
                            SQwanManager.switchAccountListener.onSuccess(new Bundle());
                        } else {
                            //显示登录界面
                            if (SQwan.isSQLoginSuccess) {

                                SQwan.getInstance().showLoginView(new SQResultListener() {

                                    @Override
                                    public void onSuccess(Bundle bundle) {
                                        bundle.putString("gid", Util.getGameID(logincontext));
                                        bundle.putString("pid", Util.getPaternerID(logincontext));
                                        SQwanManager.switchAccountListener.onSuccess(bundle);
                                    }

                                    @Override
                                    public void onFailture(int code, String msg) {
                                        SQwanManager.switchAccountListener.onFailture(code, msg);
                                    }
                                });

                            } else {
                                ViewController.showToast(logincontext, "您还未登录!");
                            }
                        }

                    }

                }
            });
        }

        @JavascriptInterface
        public void enPay() {
            SQLog.w("已经废弃, 无法调起支付");

        }
    }

    //----------------工具类---------------------------------
    private void shareTo(int type, String content) {

        Intent intent = new Intent(Intent.ACTION_SEND);
        String toastContent = "";
        switch (type) {
            case MOBILEQQ_TYPE:
                toastContent = "已复制QQ号码到剪贴版";
                Util.copyString2System(getContext(), content, toastContent);
                intent.setAction(Intent.ACTION_SENDTO);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse("imto://qq"));
                break;
            case WEIXIN_TYPE:
                toastContent = "已复制微信号码到剪贴版";
                intent.setAction(Intent.ACTION_SENDTO);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse(content));
                break;
            default:
                break;
        }

        try {
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
            if (WEIXIN_TYPE == type) {

                ViewController.showToast(getContext(), "请安装微信，" + toastContent);
            } else {

                ViewController.showToast(getContext(), "请安装移动QQ" + toastContent);
            }
        }
    }

    //--------------------------------WebView相关------------------------------------------

    private class MyWebViewDownLoadListener implements DownloadListener {

        @Override
        public void onDownloadStart(String url, String userAgent,
                                    String contentDisposition, String mimetype, long contentLength) {
            AppUtils.toUri(getContext(), url);
        }

    }

    public class myOnFocusChangeListener implements OnFocusChangeListener {

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                try {
                    Field defaultScale = WebView.class
                            .getDeclaredField("mDefaultScale");
                    defaultScale.setAccessible(true);
                    // WebViewSettingUtil.getInitScaleValue(VideoNavigationActivity.this,
                    // false )/100.0f 是我的程序的一个方法，可以用float 的scale替代
                    defaultScale.setFloat(webView, 1);
                } catch (Exception e) {
                }
            }
        }
    }

    ;


    public class myWebViewClient extends WebViewClient {

        @Override
        public void onPageStarted(final WebView view, final String url, Bitmap favicon) {
            System.out.println("37web onPageStarted");
            //记录当前地址
            currentUrl = url;
            // 清除线程
            removeTimeoutCheckingRunnable();
            runnable = new Runnable() {
                public void run() {
                    System.out.println("37web timeout..");
                    onReceivedError(view, -8, "网络超时，请稍后再试.", url);
                }
            };
            AsyncHandler.postDelayed(runnable, LOAD_TIMEOUT);
            //记录当前URL的加载时间
            WebviewUtils.setUrlLoadTime(getApplicationContext(), url, System.currentTimeMillis());
            //进度框
            showWaitDialog(getContext());
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            System.out.println("37web onPageFinished");
            //清楚线程
            removeTimeoutCheckingRunnable();
            //加载次数复原
            if (WebviewUtils.isLoadOneTime(getApplicationContext(), url, LOAD_TIMEOUT)) {
                LogUtil.i("当前页面:超时前加载完成");
                WebviewUtils.setUrlLoadCount(getApplicationContext(), url, 1);
            } else {
                LogUtil.i("当前页面:超时后加载完成");
            }
            //隐藏进度框
            hideWaitDialog();
            String title = view.getTitle();
            if (!TextUtils.isEmpty(title) && tvTitle != null) {
                tvTitle.setText(title);
            }
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failedUrl) {
            System.out.println("37web onReceivedError");
            //清楚线程
            removeTimeoutCheckingRunnable();
            if (-8 == errorCode) {
                // 超时返回的,特殊处理
                int count = WebviewUtils.getUrlLoadCount(getApplicationContext(), failedUrl);
                if (count < RETRY_LOAD_COUNTS) {
                    System.out.println("超时处理，准备加载第" + (count + 1) + "次");
                    WebviewUtils.setUrlLoadCount(getApplicationContext(), failedUrl, count + 1);
                    showWebHandler.sendEmptyMessageAtTime(1, 500);
                } else {
                    showErrorWebHandler.sendEmptyMessageDelayed(1, 200);
                }
            } else {
                // 其他情况，直接加载错误信息
                showErrorWebHandler.sendEmptyMessageDelayed(1, 200);
            }

        }

        @TargetApi(8)
        public void onReceivedSslError(WebView paramWebView,
                                       SslErrorHandler paramSslErrorHandler, SslError paramSslError) {
            System.out.println("37web onReceivedSslError");
            paramSslErrorHandler.proceed();
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            System.out.println("37web shouldOverrideUrlLoading");
            if (url.startsWith(TEL)) {

                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_DIAL);
                intent.setData(Uri.parse(url));
                startActivity(intent);

            } else if (url.startsWith(QQ)) {

                String qq = url.substring(3);
                shareTo(MOBILEQQ_TYPE, qq);

            } else if (url.startsWith(MAILTO)) {

                Uri uri = Uri.parse(url);
                Intent it = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(it);

            } else if (url.startsWith(WEIXIN)) {

                shareTo(WEIXIN_TYPE, url);

            } else {
                /* 当开启新的页面的时候用webview来进行处理而不是用系统自带的浏览器处理 */
                view.loadUrl(url);
            }
            return true;
        }

    }

    private Runnable runnable;

    private void removeTimeoutCheckingRunnable() {
        if (this.runnable != null) {
            AsyncHandler.removeCallbacks(this.runnable);
            this.runnable = null;
        }
    }

    private void openImageChooserActivity() {
        Intent i = new Intent(Intent.ACTION_GET_CONTENT);
        i.addCategory(Intent.CATEGORY_OPENABLE);
        i.setType("image/*");
        startActivityForResult(Intent.createChooser(i, "Image Chooser"), FILE_CHOOSER_RESULT_CODE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == FILE_CHOOSER_RESULT_CODE) {
            if (null == uploadMessage && null == uploadMessageAboveL) return;
            Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();
            if (uploadMessageAboveL != null) {
                onActivityResultAboveL(requestCode, resultCode, data);
            } else if (uploadMessage != null) {
                uploadMessage.onReceiveValue(result);
                uploadMessage = null;
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private void onActivityResultAboveL(int requestCode, int resultCode, Intent intent) {
        if (requestCode != FILE_CHOOSER_RESULT_CODE || uploadMessageAboveL == null) {
            return;
        }
        Uri[] results = null;
        if (resultCode == RESULT_OK && intent != null) {
            String dataString = intent.getDataString();
            ClipData clipData = intent.getClipData();
            if (clipData != null) {
                results = new Uri[clipData.getItemCount()];
                for (int i = 0; i < clipData.getItemCount(); i++) {
                    ClipData.Item item = clipData.getItemAt(i);
                    results[i] = item.getUri();
                }
            }
            if (dataString != null)
                results = new Uri[]{Uri.parse(dataString)};
        }
        uploadMessageAboveL.onReceiveValue(results);
        uploadMessageAboveL = null;
    }


    @Override
    public void onDestroy() {
        //在Activity销毁前，先关闭dialog，防止窗体泄露。
        LogUtil.i("37Web被销毁，先关闭加载进度框");
        hideWaitDialog();
        //清除
        WebviewUtils.clearWebviewPrefs(getApplicationContext());
        if (webView != null) {
            webView.clearHistory();
            ((ViewGroup) webView.getParent()).removeView(webView);
            webView.destroy();
            webView = null;
        }
        AsyncHandler.removeCallbacks(runnable);
        showWebHandler.removeCallbacksAndMessages(null);
        showErrorWebHandler.removeCallbacksAndMessages(null);
        closeHandler.removeCallbacksAndMessages(null);
        super.onDestroy();
    }

    public class myWebChromeClient extends WebChromeClient {
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            updateWaitDialog("加载中.." + newProgress + "%");
            super.onProgressChanged(view, newProgress);
        }

        // For Android < 3.0
        public void openFileChooser(ValueCallback<Uri> valueCallback) {
            uploadMessage = valueCallback;
            openImageChooserActivity();
        }

        // For Android  >= 3.0
        public void openFileChooser(ValueCallback valueCallback, String acceptType) {
            uploadMessage = valueCallback;
            openImageChooserActivity();
        }

        //For Android  >= 4.1
        public void openFileChooser(ValueCallback<Uri> valueCallback, String acceptType, String capture) {
            uploadMessage = valueCallback;
            openImageChooserActivity();
        }

        // For Android >= 5.0
        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
            uploadMessageAboveL = filePathCallback;
            openImageChooserActivity();
            return true;
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            if (tvTitle != null) {
                tvTitle.setText(title);
            }
        }
    }

}
