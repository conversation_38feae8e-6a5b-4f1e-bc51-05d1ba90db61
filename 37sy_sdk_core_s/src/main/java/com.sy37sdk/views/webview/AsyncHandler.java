package com.sy37sdk.views.webview;

import android.os.Handler;
import android.os.HandlerThread;

@Deprecated
public class AsyncHandler {
	private static Handler handler;
	private static HandlerThread handlerThread = new HandlerThread("HandlerThread");

	static {
		init();
	}

	public static void destroy()
  {
    try
    {
      if (handlerThread != null)
      {
        handlerThread.quit();
        handlerThread = null;
        handler = null;
      }
      return;
    }
    finally
    {
    }
  }

	public static void init() {
		
		synchronized (handlerThread) {
			handlerThread.start();
			try {
				handlerThread.wait();
				handler = new Handler(handlerThread.getLooper());
				return;
			} catch (InterruptedException localInterruptedException) {
			}
		}
	}

	public static void post(Runnable paramRunnable) {
		if (handler != null)
			handler.post(paramRunnable);
	}

	public static void postDelayed(Runnable paramRunnable, long paramLong) {
		if (handler != null)
			handler.postDelayed(paramRunnable, paramLong);
	}

	public static void removeCallbacks(Runnable paramRunnable) {
		if (handler != null)
			handler.removeCallbacks(paramRunnable);
	}
}