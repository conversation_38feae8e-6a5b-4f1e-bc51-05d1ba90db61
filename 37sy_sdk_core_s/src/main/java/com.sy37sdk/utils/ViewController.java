package com.sy37sdk.utils;

import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;
import com.sqwan.common.util.ToastUtil;


@Deprecated
public class ViewController {
	
    
    
	public static View inflate(Activity activity,int layout){
		LayoutInflater inflater = activity.getLayoutInflater();
        View view = inflater.inflate(layout, null);
        return view;
	}
	
	public static View inflate(Context context,int layout){
		LayoutInflater inflater = ((Activity) context).getLayoutInflater();
        View view = inflater.inflate(layout, null);
        return view;
	}

	/**
	 *  使用 ToastUtil
	 */
	@Deprecated
	public static void showToast(Context context,String words){
		try {
			ToastUtil.showToast(context, words);
		}catch (Exception e){
			e.printStackTrace();
		}
	}
	
	
}
