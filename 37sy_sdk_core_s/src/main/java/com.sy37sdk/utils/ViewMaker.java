package com.sy37sdk.utils;

import java.util.Calendar;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TimePicker;

public class ViewMaker {
	static int year ;
	static int month ;
	static int day;
	static DatePickerDialog datedialog;

	public static void showDateSetDialog(final Context context,final EditText input){

		Calendar calendar = Calendar.getInstance();

		String brithday = input.getText().toString();
		if(!brithday.equals("")){
			inputBrithday(context, brithday);
			month = month -1;

		}else{
			year = Util.getYear(context);
			month =Util.getMonth(context)-1;
			day = Util.getMonthOfday(context);
		}

		datedialog = new DatePickerDialog(context,
				new DatePickerDialog.OnDateSetListener() {
					@Override
					public void onDateSet(DatePicker view, int newYear, int monthOfYear, int dayOfMonth) {
						String temp_dayOfMonth = "";
						String temp_monthOfYear = "";
						temp_dayOfMonth =  toTime(dayOfMonth);
						int newMonthOfYear = monthOfYear+1;
						temp_monthOfYear =  toTime(newMonthOfYear);
						input.setText(newYear+"-"+ temp_monthOfYear +"-"+temp_dayOfMonth);
						year = newYear;
						month = newMonthOfYear-1;
						day = dayOfMonth;
					}
				},
				year,
				month,
				day);
		datedialog.setCancelable(false);
		datedialog.setCanceledOnTouchOutside(false);
		datedialog.show();


	}

	public static void showTimeSetDialog(Context context,final EditText input){
		Calendar calendar = Calendar.getInstance();
		int hour = calendar.get(Calendar.HOUR_OF_DAY);
		int minute = calendar.get(Calendar.MINUTE);

		new TimePickerDialog(context,new TimePickerDialog.OnTimeSetListener(){
			@Override
			public void onTimeSet(TimePicker view, int hourOfDay, int minute) {

				input.setText(hourOfDay+":"+minute);
			}
		},hour,minute,true)
				.show();

	}

	private static  String toTime(int time1){
		String time="";
		if (0 < time1 && time1 < 10) {

			time = "0"+time1;

		}else{
			time = ""+time1;
		}
		return time;
	}


	/**
	 * 存储生日到本地
	 * @param context
	 * @param brithday
	 */
	public static void inputBrithday(Context context,String brithday){

		String[] split = brithday.split("-");
		if(split != null && split.length >= 3 ){
			year = Integer.parseInt(split[0]);
			month = Integer.parseInt(split[1]);
			day = Integer.parseInt(split[2]);
			Util.putYear(context, year);
			Util.putMonth(context, month);
			Util.putMonthOfday(context, day);
		}
	}

}
