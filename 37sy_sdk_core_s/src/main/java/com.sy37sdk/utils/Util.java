package com.sy37sdk.utils;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.ResolveInfo;
import android.graphics.Point;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import com.sq.tools.manager.SensitiveInfoManager;
import com.sqwan.common.util.SQContextWrapper;
import com.sqwan.common.util.SqResUtils;
import com.sqwan.common.util.TelephonyInfoUtils;
import com.sy37sdk.core.INewUrl;
import com.sy37sdk.widget.AbstractView;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SuppressLint("NewApi")
public class Util {

    private static final String SQ_PREFS = "sq_prefs";

    private static final String GID = "gid";  //GID 就是 APPID
    private static final String PID = "pid";
    private static final String REFER = "refer";
    private static final String DEV = "dev";
    private static final String APPKEY = "appkey";

    //37用户相关的
    private static final String USERID = "userid";
    private static final String USERNAME = "username";
    private static final String USERNICK = "usernick";
    private static final String USER_BIND_PHONE = "bphone";    // 用户绑定的手机号
    private static final String USER_BIND_EMAIL = "bemail";    //用户绑定的邮箱
    private static final String PASSWORD = "pd";
    private static final String TOKEN = "token";

    //自动注册
    private static final String AUTONAME = "auto_name";
    private static final String AUTOPASSWORD = "auto_pwd";
    private static final String AUTOSTATE = "auto_state";
    private static final String AUTOISSAVE = "auto_Issave";

    //其他
    private static final String BBS_URL = "bbsurl";		//游戏论坛地址
    private static final String CHANGE_ID = "ci";		//订单号
    private static final String CODE_LOGIN = "login_cut";//ms通用
    private static final String CODE_PAY = "pay_cut";      //新增截流code
    private static final String PAY_NEW ="pay_url_new";    //新增截流url

    private static final String GET_VERIFY_CODE_LAST_TIME = "time_last_get_verify_code"; //最后点击请求短信验证码时间

    private static final String SQ_LESS_FUNC_SDK = "scode";//功能精简版SDK（依旧37）

    //新增缓存IMEI 和 MAC
    private static final String DEV_IMEI = "dev_imei";
    private static final String DEV_MAC = "dev_mac";

    private static final String PAY_WAY = "pay_way";//2016年8月29增加威富通微信支付字段
    private static final String LOGIN_NURL = "login_nurl";//2016年10月20登录之后的公告nurl

    //201709新增玩家游戏信息
    private static HashMap<String, String> roleInfos = null;

    //20171107新增第三方登录
    private static final String LOGIN_TYPE = "login_type";
    private static final String OAUTH_AccessToken = "oauth_accesstoken";
    private static final String OAUTH_RefreshToken = "oauth_refreshtoken";
    private static final String OAUTH_OpenID = "oauth_openid";
    private static final String OAUTH_NickName = "oauth_nickname";

    private static final String HARMONY = "harmony";
    private static final String ANDROID = "android";

    //20180330新增账户别名
    private static String USERALIAS = "useralias";

    /**
     * 悬浮球开启权限的提示次数
     */
    private static String CODE_FLOAT_VIEW_PERMISSION_TIPS = "float_view_permission_tips";

    public static void setFloatViewPermissionTipsNum(Context context, int num){
        SharedPreferences preferences = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = preferences.edit();
        editor.putInt(CODE_FLOAT_VIEW_PERMISSION_TIPS, num);
        editor.commit();
    }

    public static int getFloatViewPermissionTipsNum(Context context){
        SharedPreferences preferences = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return preferences.getInt(CODE_FLOAT_VIEW_PERMISSION_TIPS, -1);
    }

    @Deprecated
    public static void setCodeOfLogin(Context context,String loginCode){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(CODE_LOGIN, loginCode);
        editor.commit();
    }

    /**
     * 使用 ConfigManager
     */
    @Deprecated
    public static String getCodeOfLogin(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(CODE_LOGIN, "0");
    }

    /**
     * 请使用 ConfigManager
     */
    @Deprecated
    public static void setIsLessFunctionSDK(Context context, boolean isLess) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putBoolean(SQ_LESS_FUNC_SDK, isLess);
        editor.commit();
    }

    /**功能精简版SDK，依旧是37界面
     * @param context
     * @return
     *
     * 请使用 ConfigManager
     */
    @Deprecated
    public static boolean getIsLessFunctionSDK(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getBoolean(SQ_LESS_FUNC_SDK, false);
    }

    /**是否是简版SDK，和功能精简版有区别。
     * @param context
     * @return
     *
     * 请使用 ConfigManager
     *
     */
    @Deprecated
    public static boolean getIsSpecialSDK(Context context) {
        return !TextUtils.isEmpty(getCodeOfLogin(context)) && "1".equals(getCodeOfLogin(context));
    }

    public static void setVerifyCodeLastTime(Context context, long time) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putLong(GET_VERIFY_CODE_LAST_TIME, time);
        editor.commit();
    }

    public static long getVerifyCodeLastTime(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getLong(GET_VERIFY_CODE_LAST_TIME, 0L);
    }




    public static String getNewPayUrl(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(PAY_NEW, INewUrl.PAY);
    }

    public static void setCodeOfPay(Context context, String payCode) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(CODE_PAY, payCode);
        editor.commit();
    }

    public static String getCodeOfPay(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(CODE_PAY, "0");
    }


    public static void setUserBindEmail(Context context,String bm){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USER_BIND_EMAIL, bm);
        editor.commit();
    }
    public static String getUserBindEmail(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USER_BIND_EMAIL, "");
    }

    public static void setUserBindPhone(Context context,String bp){

        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USER_BIND_PHONE, bp);
        editor.commit();
    }
    public static String getUserBindPhone(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USER_BIND_PHONE, "");
    }

    @Deprecated
    public static String getDevid(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(DEV, getLocalDev(context));
    }

    public static void setGameID(Context context,String gid){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(GID, gid);
        editor.commit();
    }
    public static String getGameID(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(GID, "");
    }


    public static void setPaternerID(Context context,String pid){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(PID, pid);
        editor.commit();
    }
    public static String getPaternerID(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(PID, "");
    }


    public static void setRefer(Context context,String refer){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(REFER, refer);
        editor.commit();
    }
    public static String getRefer(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(REFER, "");
    }



    //	public static void setAppid(Context context,String appid){
//		SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
//        Editor editor = uiState.edit();
//        editor.putString(APPID, appid);
//        editor.commit();
//	}
//
//	public static String getAppid(Context context){
//		SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
//		return uiState.getString(APPID, "");
//	}
//
    public static void setAppKey(Context context,String appkey){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(APPKEY, appkey);
        editor.commit();
    }

    public static String getAppKey(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(APPKEY, "");
    }

    public static void setUserid(Context context,String userid){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERID, userid);
        editor.commit();
    }

    public static String getUserid(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USERID, "");
    }

    public static void setUsername(Context context,String username){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERNAME, username);
        editor.commit();
    }

    public static String getUsername(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USERNAME, "");
    }

    public static void setUsernick(Context context,String usernick){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERNICK, usernick);
        editor.commit();
    }

    public static String getUsernick(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USERNICK, "");
    }
    public static void setPassword(Context context,String password){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(PASSWORD, password);
        editor.commit();
    }

    public static String getPassword(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(PASSWORD, "");
    }

    public static void setToken(Context context,String token){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(TOKEN, token);
        editor.commit();
    }

    public static String getToken(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(TOKEN, "");
    }

    public static void setBBS(Context context,String bbs){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(BBS_URL, bbs);
        editor.commit();
    }

    public static void setAutoName(Context context,String token){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(AUTONAME, token);
        editor.commit();
    }

    public static String getAutoName(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(AUTONAME, "");
    }

    public static void setAutoPassword(Context context,String token){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(AUTOPASSWORD, token);
        editor.commit();
    }

    public static String getAutoPassword(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(AUTOPASSWORD, "");
    }

    public static void setAutoState(Context context,String token){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(AUTOSTATE, token);
        editor.commit();
    }

    public static String getAutoState(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(AUTOSTATE, "0");
    }

    public static void setAutoIssave(Context context,String token){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(AUTOISSAVE, token);
        editor.commit();
    }

    public static String getAutoIssave(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(AUTOISSAVE, "0");
    }

    /**
     * 第三方登录
     * 登录方式
     * @param context
     * @param type sq，qq，wx，wb
     */
    public static void setLoginType(Context context,String type){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(LOGIN_TYPE, type);
        editor.commit();
    }

    public static String getLoginType(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(LOGIN_TYPE, "sq");
    }

    public static void setOauthAccessToken(Context context,String type){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(OAUTH_AccessToken, type);
        editor.commit();
    }

    public static String getOauthAccessToken(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(OAUTH_AccessToken, "");
    }

    public static void setOauthRefreshToken(Context context,String type){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(OAUTH_RefreshToken, type);
        editor.commit();
    }

    public static String getOauthRefreshToken(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(OAUTH_RefreshToken, "");
    }

    public static void setOauthOpenID(Context context,String type){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(OAUTH_OpenID, type);
        editor.commit();
    }

    public static String getOauthOpenID(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(OAUTH_OpenID, "");
    }

    public static void setOauthNickName(Context context,String type){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(OAUTH_NickName, type);
        editor.commit();
    }

    public static String getOauthNickName(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(OAUTH_NickName, "");
    }

    /**
     * 存储玩家游戏信息
     * @param infos
     */
    @Deprecated
    public static void setRoleInfos(HashMap<String, String> infos) {
        roleInfos = infos;
    }

    @Deprecated
    public static HashMap<String, String> getRoleInfos() {
        return roleInfos;
    }

    public static void setAccountAlias(Context context, String alias) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(USERALIAS, alias);
        editor.commit();
    }

    public static String getAccountAlias(Context context) {
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(USERALIAS, "");
    }

    /**
     * 设置年月日
     *
     */

    public static void putYear(Context context,int year){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        share.edit().putInt("year", year).commit();
    }

    public static int getYear(Context context){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return share.getInt("year", 1990);
    }
    public static void putMonth(Context context,int month){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        share.edit().putInt("month", month).commit();
    }

    public static int getMonth(Context context){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return share.getInt("month", 03);
    }
    public static void putMonthOfday(Context context,int monthOfday){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        share.edit().putInt("monthOfday", monthOfday).commit();
    }

    public static int getMonthOfday(Context context){
        SharedPreferences share = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return share.getInt("monthOfday", 07);
    }


    /**
     * 查询BBS
     * @param context
     * @return
     */
    public static String getBBS(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(BBS_URL, "http://bbs.m.37.com");
    }
    /**
     * 设置订单号
     * @param context
     * @param changeId
     */
    public static void setChangeId(Context context,String changeId){
        if(!"".equals(changeId)){
            changeId = ZipString.json2ZipString(changeId);
        }
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(CHANGE_ID, changeId);
        editor.commit();
    }

    public static String getChangeId(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        String changeId = uiState.getString(CHANGE_ID, "");
        if(!"".equals(changeId)){
            changeId = ZipString.zipString2Json(changeId);
        }
        return changeId;
    }
    /**
     public static void saveFgValue(Context context,Bundle bundle) {
     SharedPreferences uiState=context.getSharedPreferences("SQ_PREFS", Context.MODE_PRIVATE);
     Editor editor=uiState.edit();
     Set<String> keys=bundle.keySet();

     for(String key: keys) {
     editor.putString(key, bundle.getString(key));
     }
     editor.commit();
     }

     public static String getFgValue(String key, Context context) {
     SharedPreferences uiState=context.getSharedPreferences("SQ_PREFS", Context.MODE_PRIVATE);
     return uiState.getString(key, "");
     }
     */

    public static String encodeUrl(Bundle parameters) {
        if(parameters == null) {
            return "";
        }
        StringBuilder sb=new StringBuilder();
        boolean first=true;
        for(String key: parameters.keySet()) {
            Object parameter=parameters.get(key);
            if(!(parameter instanceof String)) {
                continue;
            }
            if(first)
                first=false;
            else
                sb.append("&");
            sb.append(URLEncoder.encode(key) + "=" + URLEncoder.encode(parameters.getString(key)));
        }
        return sb.toString();
    }

    public static Bundle decodeUrl(String s) {
        Bundle params=new Bundle();
        if(s != null) {
            String array[]=s.split("&");
            for(String parameter: array) {
                String v[]=parameter.split("=");
                if(v.length == 2) {
                    params.putString(URLDecoder.decode(v[0]), URLDecoder.decode(v[1]));
                }
            }
        }
        return params;
    }

    public static Bundle parseUrl(String url) {
        url=url.replace("fgwanhttp", "http");
        try {
            URL u=new URL(url);
            Bundle b = decodeUrl(u.getQuery());
            b.putAll(decodeUrl(u.getRef()));
            return b;
        } catch(MalformedURLException e) {
            return new Bundle();
        }
    }
    /**
     * 检测网络是否可用
     * @return
     */
    @Deprecated
    public static boolean isNetworkConnected(Context context) {

        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        @SuppressLint("MissingPermission") NetworkInfo ni = cm.getActiveNetworkInfo();
        return ni != null && ni.isConnectedOrConnecting();
    }

    public static String Md5(String string) {
        byte[] hash;
        try {
            hash = MessageDigest.getInstance("MD5").digest(string.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Huh, MD5 should be supported?", e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Huh, UTF-8 should be supported?", e);
        }

        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) hex.append("0");
            hex.append(Integer.toHexString(b & 0xFF));
        }
        return hex.toString();
    }

    public static String openUrl(String url, String method, Bundle params) throws MalformedURLException, IOException {
        String strBoundary="ArYfORhtTP3i2ndDfv2rTHiSisAbouNdEefj3q2f";
        String endLine="\r\n";
        OutputStream os;
        if(method.equalsIgnoreCase("GET")) {
            url=url + "?" + encodeUrl(params);
        }

        HttpURLConnection conn=(HttpURLConnection)new URL(url).openConnection();
        conn.setRequestProperty("User-Agent", System.getProperties().getProperty("http.agent") + " FGWANAndroidSDK");
        if(!method.equalsIgnoreCase("GET")) {
            Bundle dataparams=new Bundle();
            for(String key: params.keySet()) {
                Object parameter=params.get(key);
                if(parameter instanceof byte[]) {
                    dataparams.putByteArray(key, (byte[])parameter);
                }
            }
            // use method override
            if(!params.containsKey("method")) {
                params.putString("method", method);
            }

            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=" + strBoundary);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.connect();
            os=new BufferedOutputStream(conn.getOutputStream());
            os.write(("--" + strBoundary + endLine).getBytes());
            os.write((encodePostBody(params, strBoundary)).getBytes());
            os.write((endLine + "--" + strBoundary + endLine).getBytes());
            if(!dataparams.isEmpty()) {
                for(String key: dataparams.keySet()) {
                    os.write(("Content-Disposition: form-data; filename=\"" + key + "\"" + endLine).getBytes());
                    os.write(("Content-Type: content/unknown" + endLine + endLine).getBytes());
                    os.write(dataparams.getByteArray(key));
                    os.write((endLine + "--" + strBoundary + endLine).getBytes());
                }
            }
            os.flush();
        }
        String response="";
        try {
            response=read(conn.getInputStream());
        } catch(FileNotFoundException e) {
            response=read(conn.getErrorStream());
        }
        return response;
    }

    private static String read(InputStream in) throws IOException {
        StringBuilder sb=new StringBuilder();
        BufferedReader r=new BufferedReader(new InputStreamReader(in), 1000);
        for(String line=r.readLine(); line != null; line=r.readLine()) {
            sb.append(line);
        }
        in.close();
        return sb.toString();
    }

    public static String encodePostBody(Bundle parameters, String boundary) {
        if(parameters == null)
            return "";
        StringBuilder sb=new StringBuilder();
        for(String key: parameters.keySet()) {
            Object parameter=parameters.get(key);
            if(!(parameter instanceof String)) {
                continue;
            }
            sb.append("Content-Disposition: form-data; name=\"" + key + "\"\r\n\r\n" + (String)parameter);
            sb.append("\r\n" + "--" + boundary + "\r\n");
        }
        return sb.toString();
    }

    public static String getModel(){
        return android.os.Build.MODEL;
    }

    public static String getWH(Activity context){
        WindowManager windowManager = context.getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        Point point = new Point();
        display.getSize(point);
        return point.x+"x"+point.y;
    }

    public static String getIMEI(Context context){
        return TelephonyInfoUtils.getDeviceId(context);

    }

    public static String getSIM(Context context){
        return TelephonyInfoUtils.getSimSerialNumber(context);
    }

    public static String getIMSI(Context context){
        return TelephonyInfoUtils.getSubscriberId(context);
    }

    public static String getMac(Context context){
        String macAddress = SensitiveInfoManager.getInstance().getMacAddress(context);
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……& amp;*（）——+|{}【】‘；：”“’。，、？]";
        Pattern   p   =   Pattern.compile(regEx);
        Matcher   m   =   p.matcher(macAddress != null ? macAddress :"");
        return m!=null?m.replaceAll("").trim():"";
    }

    /**
     * sim卡存在的情况下，imei+imsi
     * sim卡不存在的情况下，imei+mac
     * @param context
     * @return
     */
    public static String getSerial(Context context){

        String imei = getIMEI(context);
        String imsi = getIMSI(context);

        if(!imsi.equals("")){
            return imei+imsi;
        }

        String mac = getMac(context);
        if(!mac.equals(""))
            return imei+mac;

        String sim = getSIM(context);
        if(!sim.equals(""))
            return imei+sim;

        return "";

    }

    /**
     * MAC可能获取不到的情况，则获取IMEI、IMSI
     * @param context
     * @return
     */
    public static String getMAC(Context context){
        String macAddress = SensitiveInfoManager.getInstance().getMacAddress(context);
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……& amp;*（）——+|{}【】‘；：”“’。，、？]";
        Pattern   p   =   Pattern.compile(regEx);
        Matcher   m   =   p.matcher(macAddress != null ? macAddress : "");

        if(!getIMEI(context).equals(""))
            return getIMEI(context);
        else if(!getIMSI(context).equals(""))
            return getIMSI(context);
        else if(!getSIM(context).equals(""))
            return getSIM(context);
        else if(!m.replaceAll("").equals(""))
            return m.replaceAll("").trim();
        else
            return "";
    }

    public static String getOsVersion(){
        return android.os.Build.VERSION.SDK;
    }


    /**
     * 获取软件版本号
     *
     * @param context
     * @return
     */
    public static int getVersionCode(Context context)
    {
        int versionCode = 0;
        try
        {
            // 获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionCode = context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionCode;
        } catch (NameNotFoundException e)
        {
            e.printStackTrace();
        }
        return versionCode;
    }


    /**
     * 获取软件版本名称
     * 用于更新
     *
     * @param context
     * @return
     */
    public static String getVersionName(Context context)
    {
        String versionName = "1.0.0.0";

        try
        {
            // 获取软件版本号，对应AndroidManifest.xml下android:versionName
            versionName = context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionName;
        } catch (NameNotFoundException e)
        {
            e.printStackTrace();
        }
        return versionName;
    }

    public static String getNetworkType(Context context) {
        String netType = "";
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        @SuppressLint("MissingPermission") NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if (networkInfo == null) {
            return "";
        }
        int nType = networkInfo.getType();
        if (nType == ConnectivityManager.TYPE_MOBILE) {
            String extraInfo = networkInfo.getExtraInfo();
            if (extraInfo!=null&&!extraInfo.equals("")) {
                netType = extraInfo.toLowerCase();
            }
        } else if (nType == ConnectivityManager.TYPE_WIFI) {
            netType = "wifi";
        }
        return netType;
    }

    public static boolean isfirst(Context context){
        SharedPreferences sp = context.getSharedPreferences("first_launch", Context.MODE_PRIVATE);
        boolean first = sp.getBoolean("first", true);

        if(first){
            Editor editor = sp.edit();
            editor.putBoolean("first", false);
            editor.commit();
        }

        return first;
    }


    /**
     * 实现文本复制功能
     * @param content
     * API 11之前： android.text.ClipboardManager
     * API 11之后： android.content.ClipboardManager
     */
    public static boolean copyString2System(Context context, String content,String tips) {

        if (content == null || "".equals(content)) {

            return false;
        }

        boolean isCopySuccess = true;
        try {
            // 得到剪贴板管理器
            if (android.os.Build.VERSION.SDK_INT > 11) {
                android.content.ClipboardManager c = (android.content.ClipboardManager) context
                        .getSystemService(Context.CLIPBOARD_SERVICE);
                c.setPrimaryClip(ClipData.newPlainText("code", content.trim()));

            } else {
                android.text.ClipboardManager c = (android.text.ClipboardManager) context
                        .getSystemService(Context.CLIPBOARD_SERVICE);
                c.setText(content);
            }

            ViewController.showToast(context, tips);
        } catch (Exception e) {
            e.printStackTrace();
            isCopySuccess = false;
        }
        return isCopySuccess;
    }


//    /**
//    * 实现粘贴功能
//    * @param context
//    * @return
//    */
//    public static String paste(Context context) {
//        // 得到剪贴板管理器
//        ClipboardManager cmb = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
//        return cmb.getText().toString().trim();
//    }

    public static int getIdByName(String name, String type, String packageName, Context context) {
        int id = 0x0;
        try {
            id = SqResUtils.getIdByName(name, type, context);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    public static int getIdByNameHostFirst(String name, String type, String packageName, Context context) {
        int id = 0x0;
        try {
            id = SqResUtils.getIdByNameHostFirst(name, type, context);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(" TYPE:" + type + ",RES:" + name + " NOT FOUND!");
        }
        return id;
    }

    public static View getViewByIdName(AbstractView view, String name) {

        View tagView = null;
        Context context = view.getActivity();
        try {
            int id = SqResUtils.getId(context, name);
            tagView = view.findViewById(id);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("找不到" + name + "资源");
        }
        return tagView;
    }

    public static View getViewByName(View view, String name) {
        int id = getIdByName(name, "id", view.getContext());
        return view.findViewById(id);
    }


    public static int getIdByName(String name,String type,Context context){
        return  getIdByName(name, type, context.getPackageName(), context);
    }


    /**
     * 隐藏软键盘
     *
     * @param context
     * @param v
     */
    public static void hideSystemKeyBoard(Context context, View v) {
        InputMethodManager imm = (InputMethodManager) context
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
    }



    /**
     * 汉字转Unicode
     * @param s
     * @return
     */
    public static String gbEncoding(final String s){
        String str = "";
        for (int i = 0; i < s.length(); i++) {
            int ch = (int) s.charAt(i);
            str += "\\u" + Integer.toHexString(ch);
        }
        return str;
    }
    /**
     * Unicode转汉字
     * @param str
     * @return
     */
    public static String encodingtoStr(String str){
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        return str;
    }


    /**
     * 检测是否安装有指定应用
     * @param context
     * @param packageName
     * @return
     */
    public static boolean checkAppInstalled( Context context, String packageName ) {
        try {
            final PackageManager packageManager = context.getPackageManager();
            Intent launchIntentForPackage = packageManager.getLaunchIntentForPackage(packageName);
            return !packageManager.queryIntentActivities(launchIntentForPackage, PackageManager.MATCH_DEFAULT_ONLY).isEmpty();
        } catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }



    /**
     * @param context
     * @return true 为竖屏，反之横屏
     */
    public static boolean isScreenOriatationPortrait(Context context) {
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.setPackage(context.getPackageName());

        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            for (ResolveInfo info : context.getPackageManager().queryIntentActivities(intent, PackageManager.MATCH_ALL)) {
                com.sqwan.common.util.LogUtil.d("sqsdk", "[activtiy]" + info.activityInfo.name + " screenOrientation: " + info.activityInfo.screenOrientation);
                return (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_PORTRAIT) || (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_SENSOR_PORTRAIT) || (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_REVERSE_PORTRAIT);
            }
        } else {
            for (ResolveInfo info : context.getPackageManager().queryIntentActivities(intent, 0)) {
                com.sqwan.common.util.LogUtil.d("sqsdk", "[activtiy]" + info.activityInfo.name + " screenOrientation: " + info.activityInfo.screenOrientation);
                return (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_PORTRAIT) || (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_SENSOR_PORTRAIT) || (info.activityInfo.screenOrientation == SCREEN_ORIENTATION_REVERSE_PORTRAIT);
            }
        }

        return false;
    }


    public static boolean isSkipSQChangeAccountLogin(Context context){
        boolean isSkipLogin = false;
        ApplicationInfo appInfo;
        String result;
        try {
            String pname = context.getPackageName();
            appInfo = context.getPackageManager().getApplicationInfo(pname,PackageManager.GET_META_DATA);
            if (appInfo.metaData!=null) {
                result=appInfo.metaData.getString("SQwanSkipSwitchLogin");
                System.out.println("是否跳过切换账号登录框："+result);
                if (!TextUtils.isEmpty(result) && "yes".equals(result)) {
                    isSkipLogin = true;
                }
            }

        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }

        return isSkipLogin;
    }


    public static String getLocalDev(Context context) {
        String key = "-:&d4@zXqm-pLgW";
        return Md5(getDevMac(context) + getDevImei(context) + key)
                .toLowerCase();
    }

    //保存mac到本地
    public static void setDevMac(Context context,String mac){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(DEV_MAC, mac);
        editor.commit();
    }

    @Deprecated
    public static String getDevMac(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(DEV_MAC, "");
    }
    //保存imei到本地
    public static void setDevImei(Context context,String imei){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(DEV_IMEI, imei);
        editor.commit();
    }

    @Deprecated
    public static String getDevImei(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(DEV_IMEI, "");
    }

    //保存payway到本地
    @Deprecated
    public static void setPayWay(Context context,String payway){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(PAY_WAY, payway);
        editor.commit();
    }

    @Deprecated
    public static String getPayWay(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(PAY_WAY, "");
    }

    //保存登录之后的公告url
    public static void setNurl(Context context, String nurl){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS,Context.MODE_PRIVATE);
        Editor editor = uiState.edit();
        editor.putString(LOGIN_NURL, nurl);
        editor.commit();
    }

    public static String getNurl(Context context){
        SharedPreferences uiState = context.getSharedPreferences(SQ_PREFS, Context.MODE_PRIVATE);
        return uiState.getString(LOGIN_NURL, "");
    }

    private static boolean sLogPrintEnable;

    /**
     * 设置 log 打印开关
     */
    public static void setLogPrintEnable(boolean enable){
        sLogPrintEnable = enable;
    }


    public static boolean LOG_DEBUG = Log.isLoggable("sysdk.debug.log", Log.DEBUG);

    /**
     * 获取 log 打印开关
     */
    public static boolean isLogPrintEnable() {
        Context context = SQContextWrapper.getApplicationContext();
        boolean isDebuggable;
        if (context != null) {
            isDebuggable = (0 != (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE));
        } else {
            isDebuggable = false;
        }
        return sLogPrintEnable || LOG_DEBUG || isDebuggable;
    }

    /**
     *
     * @Title: getWpixels(横屏的像素)
     * @param: @param mContext
     * @param: @return
     * @return int 返回类型
     */
    public static int getWpixels(Context mContext) {

        String screenResolution = getDisplayScreenResolution(mContext);
        String wpixels = screenResolution.substring(0,
                screenResolution.indexOf("*"));
        return Integer.valueOf(wpixels);
    }

    /**
     *
     * @Title: getHpixels(竖屏的像素)
     * @param: @param mContext
     * @param: @return
     * @return int 返回类型
     */
    public static int getHpixels(Context mContext) {
        String screenResolution = getDisplayScreenResolution(mContext);
        String hpixels = screenResolution.substring(screenResolution
                .indexOf("*") + 1);
        return Integer.valueOf(hpixels);
    }

    /**
     *
     * @Title: getDisplayScreenResolution(手机的分辨率)
     * <AUTHOR>
     * @data 2013-8-10 下午3:41:29
     * @param: @param mContext
     * @param: @return
     * @return String 返?乩嘈?
     */
    private static String getDisplayScreenResolution(Context mContext) {
        int screen_w = 0;
        int screen_h = 0;
        try {
            screen_h = mContext.getResources().getDisplayMetrics().heightPixels;
            screen_w = mContext.getResources().getDisplayMetrics().widthPixels;
        } catch (Exception e) {
            return screen_w + "*" + screen_h;
        }

        com.sqwan.common.util.LogUtil.d( "Run2 Calibration  resolution:" + screen_w + "*"
                + screen_h);

        return screen_w + "*" + screen_h;
    }

    public static String getAppName(Context context){
        PackageManager packageManager = null;
        ApplicationInfo applicationInfo = null;
        try {
            packageManager = context.getApplicationContext().getPackageManager();
            applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            applicationInfo = null;
        }
        return packageManager.getApplicationLabel(applicationInfo).toString();
    }

    public static String getLocaleLanguage() {
        Locale locale = Locale.getDefault();
        return locale.getLanguage() + "-" + locale.getCountry();
    }


    public static String getOs() {
        return isHarmony() ? HARMONY : ANDROID;
    }


    public static boolean isHarmony() {
        try {
            Class.forName("ohos.app.Application");
            return true;
        } catch (Throwable ignored) {}

        try {
            Class.forName("ohos.system.version.SystemVersion");
            return true;
        } catch (Throwable ignored) {}

        try {
            Class<?> clz = Class.forName("com.huawei.system.BuildEx");
            Method method = clz.getMethod("getOsBrand");
            ClassLoader classLoader = clz.getClassLoader();
            if (classLoader != null && classLoader.getParent() != null) {
                return HARMONY.equals(method.invoke(clz));
            }
        } catch (Throwable ignore) {}

        return false;
    }


}
