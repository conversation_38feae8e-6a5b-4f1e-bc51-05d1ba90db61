package com.sy37sdk.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.util.SparseArray;
import com.sqwan.common.util.LogUtil;

/**
 * <AUTHOR>
 * @date 2019/5/8
 */
public class SettingHelper {

    private Activity mContext;
    private static SettingHelper mInstance;

    private SparseArray<SettingCallback> mSettingCallbacks;

    public static final int SETTING_REQUEST_CODE_FLOAT = 2313;


    private SettingHelper(Activity context) {
        this.mContext = context;
        mSettingCallbacks = new SparseArray<>();
    }

    public static synchronized SettingHelper getInstance(Activity context) {
        if (mInstance == null) {
            mInstance = new SettingHelper(context);
        }
        return mInstance;
    }

    public void requestFloatWindowSetting(SettingCallback callback) {
        mSettingCallbacks.put(SETTING_REQUEST_CODE_FLOAT, callback);
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
        intent.setData(Uri.parse("package:" + mContext.getPackageName()));
        mContext.startActivityForResult(intent, SETTING_REQUEST_CODE_FLOAT);
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        LogUtil.d("onActivityResult: requestCode = " + requestCode + ", resultCode = " + resultCode);
        SettingCallback callback = mSettingCallbacks.get(requestCode);
        if (callback != null){
            callback.onSettingResult(resultCode, data);
        }
    }

    public interface SettingCallback {
        void onSettingResult(int resultCoe, Intent data);
    }
}
