package com.sy37sdk.utils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import android.app.ActivityManager;
import android.app.ActivityManager.RunningServiceInfo;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.text.TextUtils;

import com.sqwan.common.util.LogUtil;
import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.common.web.SY37web;
import com.sqwan.msdk.config.MultiSdkManager;

@Deprecated
public class AppUtils {

	private final static SimpleDateFormat dateFormater = new SimpleDateFormat("yyyy-MM-dd");

	/**
	 * 获取App安装包信息
	 *
	 * @return
	 */
	public static PackageInfo getPackageInfo(Context context, String pack) {
		PackageInfo info = null;
		try {
			info = context.getPackageManager().getPackageInfo(pack, 0);
		} catch (NameNotFoundException e) {
			e.printStackTrace(System.err);
		}
		if (info == null)
			info = new PackageInfo();
		return info;
	}

	public static Drawable getPackIcon(Context context, String pack) {
		Drawable icon = null;
		PackageInfo packInfo = getPackageInfo(context, pack);
		icon = packInfo.applicationInfo.loadIcon(context.getPackageManager());
		return icon;
	}

	public static boolean checkPackInstalled(Context context,String pack){
		PackageInfo packageInfo;
		try {
			packageInfo = context.getPackageManager().getPackageInfo(pack, 0);
		} catch (NameNotFoundException e) {
			packageInfo = null;
		}

		if(packageInfo==null)
			return false;
		else
			return true;
	}

	/**
	 * 检测网络是否可用
	 * @return
	 */
	public static boolean isNetworkConnected(Context context) {
		ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		NetworkInfo ni = cm.getActiveNetworkInfo();
		return ni != null && ni.isConnectedOrConnecting();
	}

	/**
	 * 检测Service是否运行中
	 * @param context
	 * @param pack
	 * @return
	 */
	public static boolean isWorked(Context context, String pack) {
		ActivityManager myManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
		ArrayList<RunningServiceInfo> runningService = (ArrayList<RunningServiceInfo>) myManager.getRunningServices(45);
		for (int i = 0; i < runningService.size(); i++) {
			if (runningService.get(i).service.getClassName().toString().equals(pack)){
				return true;
			}
		}
		return false;
	}

	/**
	 * 将字符串转位日期类型
	 * @param sdate
	 * @return
	 */
	public static String toDate(long sdate) {
		return dateFormater.format(sdate*1000);
	}


	public static void startAppFromPackage(Context context,String packageName){
		PackageInfo pi = null;
		try {
			pi = context.getPackageManager().getPackageInfo(packageName, 0);
		} catch (NameNotFoundException e) {
			e.printStackTrace();
		}

		if(pi == null) {
			return;
		}

		Intent resolveIntent = new Intent(Intent.ACTION_MAIN, null);
		resolveIntent.addCategory(Intent.CATEGORY_LAUNCHER);
		resolveIntent.setPackage(pi.packageName);

		List<ResolveInfo> apps = context.getPackageManager().queryIntentActivities(resolveIntent, 0);

		ResolveInfo ri = apps.iterator().next();
		if (ri != null ) {
			String packageName1 = ri.activityInfo.packageName;
			String className = ri.activityInfo.name;

			Intent intent = new Intent(Intent.ACTION_MAIN);
			intent.addCategory(Intent.CATEGORY_LAUNCHER);

			ComponentName cn = new ComponentName(packageName1, className);

			intent.setComponent(cn);
			intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			context.startActivity(intent);
		}
	}





	/**
	 * 获取手机浏览器列表的首项
	 * @param context
	 * @return 浏览器列表首项，无则返回null
	 */
	private static ComponentName getDefaultBrowserIntent(Context context){

		PackageManager packageManager = context.getPackageManager();

		Intent defaultBrowserIntent = new Intent("android.intent.action.VIEW");
		defaultBrowserIntent.addCategory("android.intent.category.BROWSABLE");
		defaultBrowserIntent.addCategory("android.intent.category.DEFAULT");
		Uri uri = Uri.parse("http://");
		defaultBrowserIntent.setDataAndType(uri, null);

		// 找出手机当前安装的所有浏览器程序
		List<ResolveInfo> resolveInfoList = packageManager
				.queryIntentActivities(defaultBrowserIntent,
						PackageManager.GET_INTENT_FILTERS);


		int size = resolveInfoList.size();
		ComponentName[] arrayOfComponentName = new ComponentName[size];
		ComponentName componentName = null;
		boolean hasUcBrowser = false;
		String defaultBrowserClassName = "com.UCMobile"; //"com.android.chrome"


		for (int i = 0; i < size; i++) {
			ActivityInfo activityInfo = resolveInfoList.get(i).activityInfo;
			String packageName = activityInfo.packageName;
			String className = activityInfo.name;

			if (packageName.contains(defaultBrowserClassName)) { //默认UC打开
				hasUcBrowser = true;
				componentName = new ComponentName(packageName, className);
			}else{

				hasUcBrowser = false;
			}

			arrayOfComponentName[i] =  new ComponentName(packageName, className);
		}

		if (arrayOfComponentName != null && arrayOfComponentName.length > 0 && !hasUcBrowser) {
			return arrayOfComponentName[0];
		}

		return componentName;
	}




	/**
	 * 浏览器打开url
	 * @param context
	 * @param url
	 */
	public static void toUri(Context context,String url){

		Intent intent = new Intent();
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		intent.setAction(Intent.ACTION_VIEW);
		intent.setComponent(getDefaultBrowserIntent(context));
		intent.setData(Uri.parse(url));
		context.startActivity(intent);
	}

	/**
	 * 跳转到内置浏览器打开带参数的url
	 * @param context
	 * @param sdkUrl
	 * @param title
	 */
	@Deprecated
	public static void toSQWebUrl(Context context,String sdkUrl,String title) {
		if (sdkUrl == null) { return; }
		Intent intent = new Intent(context,SY37web.class);
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		intent.putExtra(SY37web.WEB_URL, constructWebUrlParam(context, sdkUrl));
		intent.putExtra(SY37web.WEB_TITLE, title);
		context.startActivity(intent);
	}

	/**
	 * 跳转到内置浏览器打开带参数的url  浏览器带title
	 * @param context
	 * @param url
	 * @param title
	 */
	@Deprecated
	public static void toSQWebUrlWithTitle(Context context, String url, String title){
		if (url == null) { return; }
		Intent intent = new Intent(context,SY37web.class);
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		intent.putExtra(SY37web.WEB_URL, constructWebUrlParam(context, url));
		intent.putExtra(SY37web.WEB_TITLE, title);
		intent.putExtra(SY37web.WEB_SHOW_TITLE,true);
		context.startActivity(intent);
	}


	/**
	 * 跳转到外部浏览器打开带参数的url
	 * @param context
	 * @param sdkUrl
	 */
	@Deprecated
	public static void toSdkUrl(Context context,String sdkUrl){

		if (sdkUrl == null) { return; }

		AppUtils.toUri(context, constructWebUrlParam(context, sdkUrl));
	}


	public static String constructWebUrlParam(Context context,String sdkUrl){

		String url_fixed = sdkUrl;

		//2015年07月31日14:47:19 对符合"X.m.37.com"的url追加参数
		Uri uri =  Uri.parse(sdkUrl);
		String url_host = uri.getHost();
		if (url_host != null) {
			//以m.37.com结尾的host才处理追加参数
			//改为包含37.com 和 37.com.cn 的都传递

			String appendUrl =
					"?gid="+Util.getGameID(context)+
							"&pid="+Util.getPaternerID(context)+
							"&dev="+ DevLogic.getInstance(context).getValue() +
							"&token="+Util.getToken(context)+
							"&sversion="+ VersionUtil.sdkVersion+
							"&refer="+Util.getRefer(context)+
							"&scut="+Util.getCodeOfLogin(context)
							// 宿主的版本号
							+ "&host_sdk_version=" + VersionUtil.getOriginalVersion();

			// 特殊SDK增加scut3这个参数
			if(!TextUtils.isEmpty(MultiSdkManager.getInstance().getScut3())) {
				appendUrl = appendUrl + "&scut3=" + MultiSdkManager.getInstance().getScut3();
			}

			//201709添加 在线客服
			String dsid = Util.getRoleInfos() != null ? Util.getRoleInfos().get("serverId") : "0";
			appendUrl += "&dsid=" + dsid + "&os=1";

			if (sdkUrl.contains("?")) {

				appendUrl = appendUrl.replace("?", "&");
			}

			url_fixed += appendUrl;
		}

		LogUtil.i("SQ weburl："+url_fixed);

		return url_fixed;
	}


	/**
	 * 用于获取状态栏的高度。
	 *
	 * @return 返回状态栏高度的像素值。
	 */
	public static  int getStatusBarHeight(Context context) {

		int statusBarHeight = 0;
		if (statusBarHeight == 0) {
			try {
				Class<?> c = Class.forName("com.android.internal.R$dimen");
				Object o = c.newInstance();
				Field field = c.getField("status_bar_height");
				int x = (Integer) field.get(o);
				statusBarHeight = context.getResources().getDimensionPixelSize(x);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return statusBarHeight;
	}

}
