package com.sy37sdk.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;

import android.content.Context;
import android.os.Environment;

import com.sqwan.common.util.EnvironmentUtils;
import com.sy37sdk.bean.DeviceInfo;

import org.json.JSONArray;
import org.json.JSONObject;

public class DeviceTools {

	private static final String DEVICEDIR 	= "systemgameinfo";			//设备信息存储在sd的位置，命名该名称防止用户手动删除
	private static final String DEVICEFILE = "devinfo.txt";		//存储设备信息的文件

	/**
	 * get device's path on sdcard
	 * @param context
	 * @return
	 */
	private static String getDir(Context context){
		File file = new File(getSDPath(context)+"/"+DEVICEDIR);
		if(!file.exists()){
			file.mkdir();
		}
		return getSDPath(context)+"/"+DEVICEDIR+"/";
	}

	/**
	 *
	 * @param context
	 * @return
	 */
	private static File getDeviceFile(Context context){
		//验证md5或加密
		try{
			File file = new File(getDir(context)+"/"+DEVICEFILE);
			if(!file.exists()){
				file.createNewFile();
			}
			return file;
		}catch(Exception e){

			System.err.println("无SDCard，获取AF失败");
			e.printStackTrace();
			return new File("");
		}

	}


	/**
	 *
	 * @param context
	 * @return
	 */
	public static List<DeviceInfo> getDeviceFromFile(Context context){
		File file = getDeviceFile(context);
		String json = "";
		BufferedReader reader = null;
		try {
			reader = new BufferedReader(new FileReader(file));
			String tempString = null;
			int line = 1;
			// 一次读入一行，直到读入null为文件结束
			while ((tempString = reader.readLine()) != null) {
				// 显示行号
				json += tempString;
				line++;
			}
			reader.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (Exception e1) {
				}
			}
		}
		if(json.equals("")){
			return null;
		}else{
			List<DeviceInfo> deviceList = new ArrayList<DeviceInfo>();
			try {
				String jsonDecode = ZipString.zipString2Json(json);
				JSONArray jsonArray = new JSONArray(jsonDecode);
				if(jsonArray.length() > 0) {
					for(int index = 0; index < jsonArray.length(); index++) {
						JSONObject deviceJson = jsonArray.getJSONObject(index);
						DeviceInfo deviceInfo = new DeviceInfo();
						deviceInfo.setDevMac(deviceJson.optString("dev_mac"));
						deviceInfo.setDevImei(deviceJson.optString("dev_imei"));
						deviceList.add(deviceInfo);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return deviceList;
		}
	}

	/**
	 * 设置设备信息
	 */
	public static void setDeviceToFile(Context context, DeviceInfo devInfo) {

		List<DeviceInfo> deviceList = getDeviceFromFile(context);
		File file = getDeviceFile(context);

		if (deviceList == null) { // 如果sd卡没有设备信息就保存一份，否则不做处理
			deviceList = new ArrayList<DeviceInfo>();
			deviceList.add(devInfo);
			try {
				JSONArray jsonArray = new JSONArray();
				for (DeviceInfo deviceInfo : deviceList) {
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("dev_mac", deviceInfo.getDevMac());
					jsonObject.put("dev_imei", deviceInfo.getDevImei());
					jsonArray.put(jsonObject);
				}
				String json = jsonArray.toString();
				// 打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
				FileWriter writer = new FileWriter(file.getAbsolutePath(),
						false);
				writer.write(ZipString.json2ZipString(json)); // 压缩
				writer.close();
			} catch (Exception e) {
				e.printStackTrace();
			}

		}
	}


	/**
	 * 获取sd卡路径 返回路径带"／"
	 * @param context
	 * @return
	 */
	public static String getSDPath(Context context) {
		return EnvironmentUtils.getCommonDirPathEndWithSprit(context);

	}

}
