package com.sy37sdk.utils;

import java.lang.reflect.Method;

public class ReflectionUtils {

	 private static Class<?>[] getTypeClass(Object[] arrayObject)
	  {
	    Class[] arrayOfClass = null;
	    if (arrayObject != null)
	    {
	      int i = arrayObject.length;
	      arrayOfClass = new Class[i];
	      if (i > 0)
	      {
	        for (int j = 0; j < i; j++)
	          arrayOfClass[j] = rawType(arrayObject[j].getClass());
	      }
	    }
	    return arrayOfClass;
	  }
	 
	 
	 public static Class<?> rawType(Class<?> paramClass)
 {
		if (paramClass.equals(Boolean.class))paramClass = Boolean.TYPE;
		if (paramClass.equals(Integer.class))return Integer.TYPE;
		if (paramClass.equals(Float.class))return Float.TYPE;
		if (paramClass.equals(Double.class))return Double.TYPE;
		if (paramClass.equals(Short.class))return Short.TYPE;
		if (paramClass.equals(Long.class))return Long.TYPE;
		if (paramClass.equals(Byte.class))return Byte.TYPE;
		if (paramClass.equals(Character.class)) return Character.TYPE;
		return paramClass;

	}
	 
	 
	public static Object invoke(Object paramObject, Class<?> paramClass,String paramString, Class<?>[] paramArrayOfClass,Object[] paramArrayOfObject) {
		
		try {
			Method localMethod = paramClass.getDeclaredMethod(paramString,paramArrayOfClass);
			localMethod.setAccessible(true);
			Object localObject = localMethod.invoke(paramObject,
					paramArrayOfObject);
			return localObject;
		} catch (Throwable localThrowable) {
		}
		return null;
	}

	public static Object invoke(Object paramObject, String paramString,Object[] paramArrayOfObject) {
		return invoke(paramObject, paramObject.getClass(), paramString,getTypeClass(paramArrayOfObject), paramArrayOfObject);
	}


}