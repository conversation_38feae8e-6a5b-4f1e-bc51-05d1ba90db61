ext{// 是否为m层调试模式
isMModuleDebug=true
// m层是否打插件，打开后运行assembleRelease
isPluginMode=false
// 是否是打37插件,和插件保留类配置有关
isSQSkin=true
//定义是否是发布状态(true)／开发状态(false)
isMModuleRelease=false
// 是否是打aar供channel工程使用
isMultiAAR=false
// 打aar时的版本号
libraryVersion='*******'
// plugin-host是否打aar,和isMultiAAR配合可打供channel使用的aar,打开后运行assembleRelease
isPluginHostAar=false
// plugin-host打多包jar包,打开后运行assembleRelease
isPluginHostApk=false
//编译SDK类型三七sq
sdkType='sq'
}