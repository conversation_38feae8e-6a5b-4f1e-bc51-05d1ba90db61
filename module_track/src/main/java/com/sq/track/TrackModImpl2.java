package com.sq.track;

import android.content.Context;
import com.sqwan.common.mod.track.ITrackMod2;
import java.util.Map;

public class TrackModImpl2 implements ITrackMod2 {

    private final Context mContext;

    public TrackModImpl2(Context context) {
        mContext = context;
    }

    @Override
    public void init(Context context) {
        ThinkingDataManager2.getInstance().init(context);
    }

    @Override
    public void track(String event, Map<String, String> params) {
        ThinkingDataManager2.getInstance().track(event, params);
    }

    @Override
    public void userSet(String properties, String value) {
        ThinkingDataManager2.getInstance().userSet(properties, value);
    }

    @Override
    public void userSetOnce(String properties, String value) {
        ThinkingDataManager2.getInstance().userSetOnce(properties, value);
    }

    @Override
    public void setUserId(String id) {
        ThinkingDataManager2.getInstance().setUserId(id);
    }

    @Override
    public void flush() {
        ThinkingDataManager2.getInstance().flush();
    }


}
