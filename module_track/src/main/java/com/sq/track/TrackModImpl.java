package com.sq.track;

import android.content.Context;
import com.sqwan.common.mod.track.ITrackMod;
import java.util.HashMap;

public class TrackModImpl implements ITrackMod {

    private final Context mContext;

    public TrackModImpl(Context context) {
        mContext = context;
    }

    @Override
    public void init(Context context) {
        ThinkingDataManager.getInstance().init(context);
    }

    @Override
    public void track(String event, HashMap<String, String> params) {
        ThinkingDataManager.getInstance().track(event,params);
    }

    @Override
    public void userSet(String properties, String value) {
        ThinkingDataManager.getInstance().userSet(properties,value);
    }

    @Override
    public void userSetOnce(String properties, String value) {
        ThinkingDataManager.getInstance().userSetOnce(properties,value);
    }

    @Override
    public void setUserId(String id) {
        ThinkingDataManager.getInstance().setUserId(id);
    }


}
