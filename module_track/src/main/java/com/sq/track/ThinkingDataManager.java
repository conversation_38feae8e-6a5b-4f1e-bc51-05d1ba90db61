package com.sq.track;

import android.content.Context;
import android.text.TextUtils;

import com.sqwan.common.dev.DevLogic;
import com.sqwan.common.dev.ImeiLogic;
import com.sqwan.common.dev.MacLogic;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.VersionUtil;
import com.sqwan.msdk.api.SQAppConfig;
import com.sqwan.msdk.config.ConfigManager;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.thinkingdata.android.ThinkingAnalyticsSDK;

/**
 * 集成第三方采集SDK, 数数 SDK 的主要实现类、数数相关操作均放在此类中实现。
 * <br>数数官方接入文档:
 * https://doc.thinkingdata.cn/ta-manual/latest/installation/installation_menu/client_sdk/android_sdk_installation/android_sdk_installation.html#_4-6-user-append
 */
public enum ThinkingDataManager {
    INSTANCE;
    private static final String TAG = "ThinkingDataManager";

    private ThinkingAnalyticsSDK sdkInstance;

    public static ThinkingDataManager getInstance() {
        return INSTANCE;
    }

    private Context context;

    /**
     *  数数初始化方法，调用其他方法前需要调用此方法, 初始化后会开启数数自动采集
     *  <br>注意此方法需要在确认用户隐私协议同意后调用，以保证合规
     */
    public void init(Context context) {
        LogUtil.i(TAG,"数数进行初始化");
        String appId = "8de79ea48b924c6eabbfae0ae868f1a9";
        String serverUrl = "https://ta.shan-yu-tech.com";
        this.context=context;
        sdkInstance = ThinkingAnalyticsSDK.sharedInstance(context, appId, serverUrl);
        sdkInstance.setDynamicSuperPropertiesTracker(new ThinkingAnalyticsSDK.DynamicSuperPropertiesTracker() {
            @Override
            public JSONObject getDynamicSuperProperties() {
                return getDynamicSuperPropertiesTracker();
            }
        });
        
        // ThinkData debug code
//        TDConfig config = TDConfig.getInstance(context, appId, serverUrl);
//        config.setMode(TDConfig.ModeEnum.DEBUG);
//        sdkInstance = ThinkingAnalyticsSDK.sharedInstance(config);
//        LogUtil.i(TAG,"数数设备ID: " + sdkInstance.getDeviceId());

        // 开启数数自动采集事件
        List<ThinkingAnalyticsSDK.AutoTrackEventType> eventTypeList = new ArrayList<>();
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_INSTALL);  //APP安装事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_START);  //APP启动事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_END);  //APP关闭事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_VIEW_SCREEN); //APP浏览页面事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_CLICK); //APP点击控件事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_CRASH); //APP崩溃事件
        sdkInstance.enableAutoTrack(eventTypeList);  //开启自动采集事件

        pushCacheEvents();
    }

    /**
     * 数数采集埋点主要方法,需初始化后调用
     * @param event  事件名称
     * @param params 事件所需参数
     */
    public void track(String event, HashMap<String, String> params) {
        if(TextUtils.isEmpty(event)) return;
        if(sdkInstance == null) {
            cacheEvent(event, params);
            return;
        }
        LogUtil.i(TAG,"数数埋点, 事件名:" + event);
        if (params == null) {
            sdkInstance.track(event);
            return;
        }
        try {
            JSONObject properties = new JSONObject();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if(TextUtils.isEmpty(entry.getKey())) continue;
                properties.put(entry.getKey(), entry.getValue());
            }
            sdkInstance.track(event, properties);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     *  设置用户ID，设置后所有上报会使用该用户ID进行. 可参见文档 "用户ID"
     */
    public void setUserId(String id) {
        if(sdkInstance == null || TextUtils.isEmpty(id)) return;
        LogUtil.i(TAG,"设置数数账户ID: " + id);
        sdkInstance.login(id);
    }

    /**
     * 设置用户属性，可重复设置，每次设置后会更新该用户原有值. 可参见文档"用户属性"
     */
    //last_login_time
    public void userSet(String properties, String value) {
        if(sdkInstance == null || TextUtils.isEmpty(properties)) return;
        try {
            JSONObject json = new JSONObject();
            json.put(properties, value);
            LogUtil.i(TAG,"设置数数普通用户属性 " + properties + " : " + value);
            sdkInstance.user_set(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置用户属性，不可重复设置，已设置过则会忽略后续调用. 可参见文档"用户属性"
     */
    //first_login_time
    public void userSetOnce(String properties, String value) {
        if(sdkInstance == null || TextUtils.isEmpty(properties)) return;
        try {
            JSONObject json = new JSONObject();
            json.put(properties, value);
            LogUtil.i(TAG,"设置数数永久用户属性 " + properties + " : " + value);
            sdkInstance.user_setOnce(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** 因合规问题, SDK 需要在同意用户隐私协议后触发，而部分埋点事件发生在同意隐私协议前，
     * 故有如下简易缓存实现，以保证用户同意隐私协议后相关事件能上报*/
    private final HashMap<String, HashMap<String, String>> cache = new HashMap<>();

    private void cacheEvent(String event, HashMap<String, String> params) {
        if(TextUtils.isEmpty(event)) return;
        LogUtil.i(TAG,"数数初始化前,缓存事件: " + event);
        cache.put(event, params);
    }

    private void pushCacheEvents() {
        if(cache.isEmpty()) return;
        if(sdkInstance == null) return;
        LogUtil.i(TAG,"上报数数缓存事件");
        for (Map.Entry<String, HashMap<String, String>> entry : cache.entrySet()) {
            if(TextUtils.isEmpty(entry.getKey())) continue;
            track(entry.getKey(), entry.getValue());
        }
        cache.clear();
    }

    /**
     * 动态公共属性
     */
    private JSONObject getDynamicSuperPropertiesTracker(){
        JSONObject dynamicProperties=new JSONObject();
        try {
            SQAppConfig config = ConfigManager.getInstance(context).getSQAppConfig();
            dynamicProperties.put("gid",config.getGameid());
            dynamicProperties.put("pid",config.getPartner());
            dynamicProperties.put("refer",config.getRefer());
            dynamicProperties.put("sversion", VersionUtil.getSdkVersion());
            String dev = DevLogic.getInstance(context).getFromCache() == null ? "" : DevLogic.getInstance(context).getFromCache().getValue();
            String imei = ImeiLogic.getInstance(context).getFromCache() == null ? "" : ImeiLogic.getInstance(context).getFromCache().getValue();
            String mac = MacLogic.getInstance(context).getFromCache() == null ? "" : MacLogic.getInstance(context).getFromCache().getValue();
            dynamicProperties.put("dev", dev);
            dynamicProperties.put("imei", imei);
            dynamicProperties.put("mac", mac);
            dynamicProperties.put("oaid",DeviceUtils.getOaid(context));
            dynamicProperties.put("gwversion", VersionUtil.gwversion);
            dynamicProperties.put("version", SqTrackUtil.getVersionCode(context) + "");
            if(SqTrackUtil.getLogined(context)){
                dynamicProperties.put("uid", SqTrackUtil.getUserid(context));
                dynamicProperties.put("uname", SqTrackUtil.getUsername(context));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return dynamicProperties;
    }


}
