package com.sq.track;

import android.content.Context;
import android.text.TextUtils;

import com.sq.tools.utils.HardwareUtils;
import com.sqwan.common.data.cache.SpRequestInfo;
import com.sqwan.common.net.base.RequestUtil;
import com.sqwan.common.track.SqTrackCommonKey;
import com.sqwan.common.track.SqTrackUtil;
import com.sqwan.common.util.AppUtils;
import com.sqwan.common.util.DeviceUtils;
import com.sqwan.common.util.EnvironmentUtils;
import com.sqwan.common.util.LogUtil;
import com.sqwan.common.util.NetWorkUtils;
import com.sqwan.common.util.VersionUtil;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.thinkingdata.android.ThinkingAnalyticsSDK;

/**
 * 新埋点
 * 集成第三方采集SDK, 数数 SDK 的主要实现类、数数相关操作均放在此类中实现。
 * <br>数数官方接入文档:
 * https://doc.thinkingdata.cn/ta-manual/latest/installation/installation_menu/client_sdk/android_sdk_installation/android_sdk_installation.html#_4-6-user-append
 */
public enum ThinkingDataManager2 {
    INSTANCE;
    private static final String TAG = "ThinkingDataManager2";

    //测试
    private static final String TA_APP_ID_DEBUG = "debug-appid";

    //正式
    private static final String TA_APP_ID = "f271c0ab7bcf4481b35fa1916850ab19";

    private static final String TA_SERVER_URL = "https://ta.shan-yu-tech.com";

    private ThinkingAnalyticsSDK sdkInstance;

    public static ThinkingDataManager2 getInstance() {
        return INSTANCE;
    }

    private Context mContext;

    /**
     * 数数初始化方法，调用其他方法前需要调用此方法, 初始化后会开启数数自动采集
     * <br>注意此方法需要在确认用户隐私协议同意后调用，以保证合规
     */
    public void init(Context context) {
        LogUtil.i(TAG, "数数进行初始化");
        this.mContext = context;
        ThinkingAnalyticsSDK.enableTrackLog(isDebugModeByFile(context));
        sdkInstance = ThinkingAnalyticsSDK.sharedInstance(context, TA_APP_ID, TA_SERVER_URL);
        sdkInstance.setSuperProperties(getSuperProperties());
        sdkInstance.setDynamicSuperPropertiesTracker(new ThinkingAnalyticsSDK.DynamicSuperPropertiesTracker() {
            @Override
            public JSONObject getDynamicSuperProperties() {
                return getDynamicSuperPropertiesTracker();
            }
        });

        // ThinkData debug code
//        TDConfig config = TDConfig.getInstance(context, appId, serverUrl);
//        config.setMode(TDConfig.ModeEnum.DEBUG);
//        sdkInstance = ThinkingAnalyticsSDK.sharedInstance(config);
//        LogUtil.i(TAG,"数数设备ID: " + sdkInstance.getDeviceId());

        // 开启数数自动采集事件
        List<ThinkingAnalyticsSDK.AutoTrackEventType> eventTypeList = new ArrayList<>();
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_INSTALL);  //APP安装事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_START);  //APP启动事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_END);  //APP关闭事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_VIEW_SCREEN); //APP浏览页面事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_CLICK); //APP点击控件事件
        eventTypeList.add(ThinkingAnalyticsSDK.AutoTrackEventType.APP_CRASH); //APP崩溃事件
        sdkInstance.enableAutoTrack(eventTypeList);  //开启自动采集事件

        pushCacheEvents();
    }

    /**
     * 数数采集埋点主要方法,需初始化后调用
     *
     * @param event  事件名称
     * @param params 事件所需参数
     */
    public void track(String event, Map<String, String> params) {
        if (TextUtils.isEmpty(event)) return;
        if (sdkInstance == null) {
            cacheEvent(event, params);
            return;
        }
        if (params == null) {
            LogUtil.i(TAG, "数数埋点, 事件名:" + event + " 无参数");
            sdkInstance.track(event);
            return;
        }
        try {
            JSONObject properties = new JSONObject();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (TextUtils.isEmpty(entry.getKey())) continue;
                properties.put(entry.getKey(), entry.getValue());
            }
            LogUtil.i(TAG, "数数埋点, 事件名:" + event + " 参数 " + properties.toString());
            sdkInstance.track(event, properties);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置用户ID，设置后所有上报会使用该用户ID进行. 可参见文档 "用户ID"
     */
    public void setUserId(String id) {
        if (sdkInstance == null || TextUtils.isEmpty(id)) return;
        LogUtil.i(TAG, "设置数数账户ID: " + id);
        sdkInstance.login(id);
    }

    /**
     * 设置用户属性，可重复设置，每次设置后会更新该用户原有值. 可参见文档"用户属性"
     */
    //last_login_time
    public void userSet(String properties, String value) {
        if (sdkInstance == null || TextUtils.isEmpty(properties)) return;
        try {
            JSONObject json = new JSONObject();
            json.put(properties, value);
            LogUtil.i(TAG, "设置数数普通用户属性 " + properties + " : " + value);
            sdkInstance.user_set(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置用户属性，不可重复设置，已设置过则会忽略后续调用. 可参见文档"用户属性"
     */
    //first_login_time
    public void userSetOnce(String properties, String value) {
        if (sdkInstance == null || TextUtils.isEmpty(properties)) return;
        try {
            JSONObject json = new JSONObject();
            json.put(properties, value);
            LogUtil.i(TAG, "设置数数永久用户属性 " + properties + " : " + value);
            sdkInstance.user_setOnce(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 因合规问题, SDK 需要在同意用户隐私协议后触发，而部分埋点事件发生在同意隐私协议前，
     * 故有如下简易缓存实现，以保证用户同意隐私协议后相关事件能上报
     */
    private final List<CacheEvent> cacheEvents = new ArrayList<>();

    private void cacheEvent(String event, Map<String, String> params) {
        if (TextUtils.isEmpty(event)) return;
        LogUtil.i(TAG, "数数初始化前,缓存事件: " + event);
        cacheEvents.add(new CacheEvent(event, params));
    }

    private void pushCacheEvents() {
        if (cacheEvents.isEmpty()) return;
        if (sdkInstance == null) return;
        LogUtil.i(TAG, "上报数数缓存事件");
        for (CacheEvent cacheEvent : cacheEvents) {
            if (cacheEvent == null || TextUtils.isEmpty(cacheEvent.getEvent())) continue;
            track(cacheEvent.getEvent(), cacheEvent.getEventParams());
        }
        cacheEvents.clear();
    }

    public void flush() {
        if (sdkInstance == null) {
            return;
        }
        sdkInstance.flush();
    }

    /**
     * 动态公共属性
     */
    private JSONObject getDynamicSuperPropertiesTracker() {
        JSONObject dynamicProperties = new JSONObject();
        try {
            dynamicProperties.put(SqTrackCommonKey.network_type, DeviceUtils.getNetWorkType(mContext));
            dynamicProperties.put(SqTrackCommonKey.carrier, DeviceUtils.getCarrier(mContext));
            dynamicProperties.put(SqTrackCommonKey.ip, DeviceUtils.getIpAddress(mContext));
            dynamicProperties.put(SqTrackCommonKey.event_time, System.currentTimeMillis() + "");
            dynamicProperties.put(SqTrackCommonKey.mac, DeviceUtils.getMac(mContext));
            dynamicProperties.put(SqTrackCommonKey.imei, DeviceUtils.getIMEI(mContext));
            dynamicProperties.put(SqTrackCommonKey.dev, DeviceUtils.getDev(mContext));
            dynamicProperties.put(SqTrackCommonKey.android_id, DeviceUtils.getAndroidId(mContext));
            dynamicProperties.put(SqTrackCommonKey.oaid, DeviceUtils.getOaid(mContext));
            dynamicProperties.put(SqTrackCommonKey.gid, SqTrackUtil.getGameID(mContext));
            dynamicProperties.put(SqTrackCommonKey.refer, SqTrackUtil.getRefer(mContext));
            dynamicProperties.put(SqTrackCommonKey.cid, SqTrackUtil.getChannelId(mContext));
            dynamicProperties.put(SqTrackCommonKey.pid, SqTrackUtil.getPaternerID(mContext));
            dynamicProperties.put(SqTrackCommonKey.sversion, VersionUtil.getSdkVersion());
            dynamicProperties.put(SqTrackCommonKey.gwversion, VersionUtil.gwversion);
            dynamicProperties.put(SqTrackCommonKey.original_sversion, VersionUtil.getOriginalVersion());
            dynamicProperties.put(SqTrackCommonKey.pluginVersion, VersionUtil.getPluginVersion(mContext));
            dynamicProperties.put(SqTrackCommonKey.request_liveid, SpRequestInfo.getRequestLiveId(mContext));
            dynamicProperties.put(SqTrackCommonKey.request_id, RequestUtil.generateRequestId());
            dynamicProperties.put(SqTrackCommonKey.scut, SqTrackUtil.getScut(mContext));
            dynamicProperties.put(SqTrackCommonKey.isProxy, NetWorkUtils.isWifiProxy());
            if (SqTrackUtil.getLogined(mContext)) {
                dynamicProperties.put(SqTrackCommonKey.uid, SqTrackUtil.getUserid(mContext));
                dynamicProperties.put(SqTrackCommonKey.uname, SqTrackUtil.getUsername(mContext));
                dynamicProperties.put(SqTrackCommonKey.role_id, SqTrackUtil.getRoleid(mContext));
                dynamicProperties.put(SqTrackCommonKey.role_name, SqTrackUtil.getRolename(mContext));
                dynamicProperties.put(SqTrackCommonKey.role_level, SqTrackUtil.getRolelevel(mContext));
                dynamicProperties.put(SqTrackCommonKey.vip_level, SqTrackUtil.getVipLevel(mContext));
                dynamicProperties.put(SqTrackCommonKey.server_id, SqTrackUtil.getServerid(mContext));
                dynamicProperties.put(SqTrackCommonKey.server_name, SqTrackUtil.getServerName(mContext));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dynamicProperties;
    }

    /**
     * 公共属性
     */
    public JSONObject getSuperProperties() {
        JSONObject superProperties = new JSONObject();
        try {
            superProperties.put(SqTrackCommonKey.os, DeviceUtils.isHarmony() ? "4" : "1");
            superProperties.put(SqTrackCommonKey.os_version, DeviceUtils.getOSVersion());
            superProperties.put(SqTrackCommonKey.country, DeviceUtils.getCountry());
            superProperties.put(SqTrackCommonKey.country_code, DeviceUtils.getCountryCode());
            superProperties.put(SqTrackCommonKey.province, "");
            superProperties.put(SqTrackCommonKey.city, "");
            superProperties.put(SqTrackCommonKey.phone_brand, DeviceUtils.getBrand());
            superProperties.put(SqTrackCommonKey.phone_model, DeviceUtils.getModel());
            Map<String, String> displayMetrics = DeviceUtils.getDisplayMetrics(mContext);
            superProperties.put(SqTrackCommonKey.pixel, displayMetrics.get("dpi"));
            superProperties.put(SqTrackCommonKey.screen_height, displayMetrics.get("height"));
            superProperties.put(SqTrackCommonKey.screen_width, displayMetrics.get("width"));
            superProperties.put(SqTrackCommonKey.ram, DeviceUtils.getTotalRam() + "");
            superProperties.put(SqTrackCommonKey.sd_memory, DeviceUtils.getTotalExternalMemorySize() + "");
            superProperties.put(SqTrackCommonKey.cpu_hardware, DeviceUtils.getCpuHardware());
            superProperties.put(SqTrackCommonKey.cpu_Ghz, DeviceUtils.getMaxCpuFreq());
            superProperties.put(SqTrackCommonKey.cpu_core, DeviceUtils.getCpuCore() + "");
            superProperties.put(SqTrackCommonKey.cpu_is_x86, DeviceUtils.getCpuName());
            superProperties.put(SqTrackCommonKey.sim, DeviceUtils.getSIM(mContext) + "");
            superProperties.put(SqTrackCommonKey.last_os_update_ts, DeviceUtils.getBootTime() + "");
            superProperties.put(SqTrackCommonKey.apk_name, AppUtils.getPackageName(mContext));
            superProperties.put(SqTrackCommonKey.game_name, AppUtils.getAppName(mContext));
            superProperties.put(SqTrackCommonKey.install_time, AppUtils.getAppInstallTime(mContext));
            superProperties.put(SqTrackCommonKey.last_update_time, AppUtils.getAppUpdateTime(mContext));
            superProperties.put(SqTrackCommonKey.isSimulator, DeviceUtils.isSimulator(mContext) + "");
            superProperties.put(SqTrackCommonKey.version, SqTrackUtil.getVersionCode(mContext) + "");
            superProperties.put(SqTrackCommonKey.versionName, SqTrackUtil.getVersionName(mContext));
            superProperties.put(SqTrackCommonKey.targetVersion, DeviceUtils.getTargetVersion(mContext));
            superProperties.put(SqTrackCommonKey.cpu_abi, HardwareUtils.getCpuArch());
            superProperties.put(SqTrackCommonKey.isRoot, DeviceUtils.isRootDevice());
            superProperties.put(SqTrackCommonKey.cpu_info, DeviceUtils.readCpuInfo());
            superProperties.put(SqTrackCommonKey.channel_sdk_version, SqTrackUtil.getChannelSdkVersion(mContext));
            superProperties.put(SqTrackCommonKey.channel_name, SqTrackUtil.getChannelName(mContext));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return superProperties;
    }

    /***
     * 开启调试文件
     *
     */
    private static final String DEBUG_FILE = "track-test";

    /**
     * 读取测试机的文件，如果存在则开启调试模式。
     */
    private static boolean isDebugModeByFile(Context context) {
        String filePath = EnvironmentUtils.getCommonDirPath(context) + File.separator + DEBUG_FILE;
        LogUtil.i(filePath);
        File file = new File(filePath);
        if (file.exists() && file.isDirectory()) {
            return true;
        }
        return false;
    }

}
