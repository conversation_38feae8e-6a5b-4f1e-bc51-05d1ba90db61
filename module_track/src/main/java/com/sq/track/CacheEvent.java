package com.sq.track;

import java.util.HashMap;
import java.util.Map;

public class CacheEvent {
    private String event;
    private Map<String,String> eventParams;

    public CacheEvent(String event, Map<String, String> eventParams) {
        this.event = event;
        this.eventParams = eventParams;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Map<String, String> getEventParams() {
        return eventParams;
    }

    public void setEventParams(HashMap<String, String> eventParams) {
        this.eventParams = eventParams;
    }
}
