#!/usr/bin/env python
# coding:utf-8


import os
import shutil
import sys
import argparse

path = os.path.dirname(sys.argv[0])
os.chdir(path)
print(path)
os.chdir('../')
path = os.getcwd()
print(path)

cmd_psdk_sq = "./gradlew clean :module_plugin:plugin_host:buildSdk"
cmd_msdk_sq = "./gradlew clean :37sy_sdk_core_m:buildSdk"
cmd_pdemo_sq = "./gradlew clean :plugin_demo:buildDemo"
cmd_mdemo_sq = "./gradlew clean :37sy_sdk_core_m:buildDemo"
cmd_plugin_sq = "./gradlew clean :37sy_sdk_core_m:buildPlugin"

cmds = {
    'cmd_psdk_sq': cmd_psdk_sq,
    'cmd_msdk_sq': cmd_msdk_sq,
    'cmd_pdemo_sq': cmd_pdemo_sq,
    'cmd_mdemo_sq': cmd_mdemo_sq,
    'cmd_plugin_sq': cmd_plugin_sq,
}

argCmd = sys.argv[1]
cmd = cmds[argCmd] + " -Pcmb_build_sdk=" + argCmd

print(cmd)

print('stop first')
os.system("./gradlew --stop")
print('start')
os.system(cmd)
print('end')

